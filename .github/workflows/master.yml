name: Production
on:
  push:
    branches:
      - master
concurrency:
  group: "master"
  cancel-in-progress: true
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
          tools: composer:v2
          coverage: none

      - uses: actions/checkout@master

      - name: Copy .env
        run: |
          php -r "file_exists('.env') || copy('.env.example', '.env');"
          sed -i -r 's/^(CIPHERSWEET_KEY=).*/\${{ secrets.CIPHERSWEET_KEY }}/' .env

      - name: Install Dependencies
        run: |
          composer config http-basic.nova.laravel.com ${{ secrets.NOVA_USERNAME }} ${{ secrets.NOVA_PASSWORD }}
          composer install --no-ansi --no-interaction --prefer-dist --no-scripts

      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache

      - name: Require Vapor CLI
        run: composer global require laravel/vapor-cli

      - name: Deploy Environment
        run: |
          vapor team:switch --id=32708
          vapor deploy production --commit="${{ github.sha }}"
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}
          VITE_BROADCAST_DRIVER: "pusher"
          VITE_PUSHER_APP_KEY: ${{ secrets.PUSHER_APP_KEY }}
          VITE_STRIPE_PUBLISH_KEY: ${{ secrets.STRIPE_PUBLISH_KEY }}
          VITE_PUSHER_APP_CLUSTER: "ap2"
