name: Label Merged PR as Done
on:
  pull_request:
    types: [closed]
jobs:
  label-merged-pr:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true
    steps:
      - name: Add Done label
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.addLabels({
              issue_number: context.payload.pull_request.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['Done']
            })