@font-face {
  font-family: 'x-cloud-icon';
  src:  url('/fonts/x-cloud-icon.eot?2ptqcv');
  src:  url('/fonts/x-cloud-icon.eot?2ptqcv#iefix') format('embedded-opentype'),
    url('/fonts/x-cloud-icon.ttf?2ptqcv') format('truetype'),
    url('/fonts/x-cloud-icon.woff?2ptqcv') format('woff'),
    url('/fonts/x-cloud-icon.svg?2ptqcv#x-cloud-icon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.xcloud {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'x-cloud-icon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.xc-download:before {
  content: "\e95c";
}
.xc-stop:before {
  content: "\e9a7";
}
.xc-cache-clean:before {
  content: "\e9a8";
}
.xc-bug_3:before {
  content: "\e9a6";
}
.xc-Document_3:before {
  content: "\e9a5";
}
.xc-folder:before {
  content: "\e9a4";
}
.xc-laravel_1:before {
  content: "\e9a3";
}
.xc-output:before {
  content: "\e99f";
}
.xc-pause:before {
  content: "\e9a0";
}
.xc-Restart:before {
  content: "\e9a1";
}
.xc-refresh:before {
  content: "\e9a2";
}
.xc-play1:before {
  content: "\e99d";
}
.xc-elephant-php:before {
  content: "\e99e";
}
.xc-add_ons:before {
  content: "\e99c";
}
.xc-shield_3:before {
  content: "\e996";
}
.xc-crossX:before {
  content: "\e999";
}
.xc-privacy_bolt:before {
  content: "\e99a";
}
.xc-bug_2:before {
  content: "\e99b";
}
.xc-upper-arrow:before {
  content: "\e994";
}
.xc-bottom-arrow:before {
  content: "\e995";
}
.xc-warning1:before {
  content: "\e993";
}
.xc-laravel:before {
  content: "\e998";
}
.xc-script:before {
  content: "\e997";
}
.xc-dot:before {
  content: "\e992";
}
.xc-icon-wordpress-white:before {
  content: "\e98e";
}
.xc-icon-wordpress-black:before {
  content: "\e991";
}
.xc-icon-tools-white:before {
  content: "\e98f";
}
.xc-icon-tools-black:before {
  content: "\e990";
}
.xc-backup-restore:before {
  content: "\e98d";
}
.xc-box-minimalistic:before {
  content: "\e987";
}
.xc-document-2:before {
  content: "\e988";
}
.xc-shield-3:before {
  content: "\e989";
}
.xc-user-block:before {
  content: "\e98a";
}
.xc-user-check:before {
  content: "\e98b";
}
.xc-widget:before {
  content: "\e98c";
}
.xc-mariadb:before {
  content: "\e985";
}
.xc-mysql:before {
  content: "\e986";
}
.xc-caret_up:before {
  content: "\e968";
}
.xc-briefcase:before {
  content: "\e969";
}
.xc-bug:before {
  content: "\e96a";
}
.xc-caret_left:before {
  content: "\e96b";
}
.xc-caret_right:before {
  content: "\e96c";
}
.xc-cashing:before {
  content: "\e96d";
}
.xc-checkbox:before {
  content: "\e96e";
}
.xc-clock_2:before {
  content: "\e96f";
}
.xc-delete_2:before {
  content: "\e970";
}
.xc-duplicate:before {
  content: "\e971";
}
.xc-growth_down:before {
  content: "\e972";
}
.xc-growth_up:before {
  content: "\e973";
}
.xc-help:before {
  content: "\e974";
}
.xc-history:before {
  content: "\e975";
}
.xc-idea_2:before {
  content: "\e976";
}
.xc-info_3:before {
  content: "\e977";
}
.xc-money:before {
  content: "\e978";
}
.xc-pencil:before {
  content: "\e979";
}
.xc-products:before {
  content: "\e97a";
}
.xc-role:before {
  content: "\e97b";
}
.xc-server_2:before {
  content: "\e97c";
}
.xc-settings_2:before {
  content: "\e97d";
}
.xc-shield_2:before {
  content: "\e97e";
}
.xc-site:before {
  content: "\e97f";
}
.xc-upload_3:before {
  content: "\e980";
}
.xc-user_2:before {
  content: "\e981";
}
.xc-users_2:before {
  content: "\e982";
}
.xc-users:before {
  content: "\e983";
}
.xc-wallet:before {
  content: "\e984";
}
.xc-dashboard-2:before {
  content: "\e964";
}
.xc-playground:before {
  content: "\e965";
}
.xc-server:before {
  content: "\e966";
}
.xc-whitelabel:before {
  content: "\e967";
}
.xc-Bolt:before {
  content: "\e95e";
}
.xc-blueprint:before {
  content: "\e95d";
}
.xc-close-2:before {
  content: "\e95f";
}
.xc-right-arrow:before {
  content: "\e960";
}
.xc-star-full:before {
  content: "\e961";
}
.xc-star-half:before {
  content: "\e962";
}
.xc-star-line:before {
  content: "\e963";
}
.xc-git:before {
  content: "\e958";
}
.xc-crown:before {
  content: "\e951";
}
.xc-info:before {
  content: "\e952";
}
.xc-php:before {
  content: "\e948";
}
.xc-cloud:before {
  content: "\e949";
}
.xc-data-cleaning:before {
  content: "\e94a";
}
.xc-data-transfer:before {
  content: "\e94b";
}
.xc-invoice:before {
  content: "\e94c";
}
.xc-shield:before {
  content: "\e94d";
}
.xc-update:before {
  content: "\e94f";
}
.xc-web-settings:before {
  content: "\e950";
}
.xc-hexa-warning:before {
  content: "\e947";
}
.xc-idea:before {
  content: "\e944";
}
.xc-warning-o:before {
  content: "\e945";
}
.xc-web:before {
  content: "\e946";
}
.xc-grid:before {
  content: "\e93f";
}
.xc-list:before {
  content: "\e940";
}
.xc-menu-vertical:before {
  content: "\e93e";
}
.xc-logout:before {
  content: "\e936";
}
.xc-chat:before {
  content: "\e937";
}
.xc-document:before {
  content: "\e938";
}
.xc-account-1:before {
  content: "\e939";
}
.xc-dashboard:before {
  content: "\e93a";
}
.xc-data-network:before {
  content: "\e93b";
}
.xc-analytics-1:before {
  content: "\e93c";
}
.xc-indent:before {
  content: "\e93d";
}
.xc-close1:before {
  content: "\e934";
}
.xc-toggle:before {
  content: "\e935";
}
.xc-file:before {
  content: "\e932";
}
.xc-confirm:before {
  content: "\e92d";
}
.xc-database:before {
  content: "\e92e";
}
.xc-destination:before {
  content: "\e92f";
}
.xc-domains:before {
  content: "\e930";
}
.xc-done:before {
  content: "\e931";
}
.xc-plugin:before {
  content: "\e933";
}
.xc-tick-o:before {
  content: "\e92b";
}
.xc-steped_arrow_down:before {
  content: "\e92c";
}
.xc-close-o:before {
  content: "\e92a";
}
.xc-close-sq:before {
  content: "\e929";
}
.xc-show:before {
  content: "\e90f";
}
.xc-hide:before {
  content: "\e928";
}
.xc-right:before {
  content: "\e927";
}
.xc-web_view:before {
  content: "\e900";
}
.xc-wordpress_upload:before {
  content: "\e901";
}
.xc-wordpress:before {
  content: "\e902";
}
.xc-warning:before {
  content: "\e903";
}
.xc-verify_dns:before {
  content: "\e904";
}
.xc-user:before {
  content: "\e905";
}
.xc-upload_2:before {
  content: "\e906";
}
.xc-upload:before {
  content: "\e907";
}
.xc-team:before {
  content: "\e908";
}
.xc-settings:before {
  content: "\e909";
}
.xc-server_provider:before {
  content: "\e90a";
}
.xc-search:before {
  content: "\e90b";
}
.xc-rocket:before {
  content: "\e90c";
}
.xc-play:before {
  content: "\e90d";
}
.xc-password:before {
  content: "\e90e";
}
.xc-pass_lock:before {
  content: "\e910";
}
.xc-other_providers:before {
  content: "\e911";
}
.xc-notifications:before {
  content: "\e912";
}
.xc-note:before {
  content: "\e913";
}
.xc-maximize:before {
  content: "\e914";
}
.xc-lock:before {
  content: "\e915";
}
.xc-link:before {
  content: "\e916";
}
.xc-keys:before {
  content: "\e917";
}
.xc-https:before {
  content: "\e918";
}
.xc-email:before {
  content: "\e919";
}
.xc-edit:before {
  content: "\e91a";
}
.xc-double_right:before {
  content: "\e91b";
}
.xc-delete:before {
  content: "\e91c";
}
.xc-copy:before {
  content: "\e91d";
}
.xc-caret_down:before {
  content: "\e91e";
}
.xc-browser:before {
  content: "\e91f";
}
.xc-bar:before {
  content: "\e920";
}
.xc-angle_up:before {
  content: "\e921";
}
.xc-angle_right:before {
  content: "\e922";
}
.xc-angle_left:before {
  content: "\e923";
}
.xc-angle_down:before {
  content: "\e924";
}
.xc-analytics:before {
  content: "\e925";
}
.xc-add:before {
  content: "\e926";
}
.xc-tick-sq:before {
  content: "\ea52";
}
.xc-spinner:before {
  content: "\e941";
}
.xc-price-tags:before {
  content: "\e942";
}
.xc-price-tag:before {
  content: "\e943";
}
.xc-clock:before {
  content: "\e94e";
}
.xc-earth:before {
  content: "\e9ca";
}
.xc-info-2:before {
  content: "\e953";
}
.xc-mail:before {
  content: "\e954";
}
.xc-synchronization:before {
  content: "\e955";
}
.xc-data-recovery:before {
  content: "\e956";
}
.xc-pdf:before {
  content: "\e957";
}
.xc-cron-job:before {
  content: "\e959";
}
.xc-download-db:before {
  content: "\e95a";
}
.xc-download-file:before {
  content: "\e95b";
}
