{"custom_stats": {}, "global_custom_stats": {}, "plays": [{"play": {"duration": {"end": "2022-08-17T08:08:11.135646Z", "start": "2022-08-17T08:08:02.375240Z"}, "id": "baef7c61-fc27-cd4d-ec16-000000000006", "name": "all"}, "tasks": [{"hosts": {"**************": {"_ansible_no_log": null, "_ansible_verbose_override": true, "action": "gather_facts", "ansible_facts": {"ansible_all_ipv4_addresses": ["**************", "***************"], "ansible_all_ipv6_addresses": ["2400:8904::f03c:93ff:fe56:3cb6", "fe80::f03c:93ff:fe56:3cb6"], "ansible_apparmor": {"status": "enabled"}, "ansible_architecture": "x86_64", "ansible_bios_date": "04/01/2014", "ansible_bios_vendor": "SeaBIOS", "ansible_bios_version": "rel-1.12.1-0-ga5cab58e9a3f-prebuilt.qemu.org", "ansible_board_asset_tag": "NA", "ansible_board_name": "NA", "ansible_board_serial": "NA", "ansible_board_vendor": "NA", "ansible_board_version": "NA", "ansible_chassis_asset_tag": "NA", "ansible_chassis_serial": "NA", "ansible_chassis_vendor": "QEMU", "ansible_chassis_version": "pc-q35-4.1", "ansible_cmdline": {"BOOT_IMAGE": "/boot/vmlinuz-5.13.0-30-generic", "console": "ttyS0,19200n8", "net.ifnames": "0", "ro": true, "root": "/dev/sda"}, "ansible_date_time": {"date": "2022-08-17", "day": "17", "epoch": "1660723686", "epoch_int": "1660723686", "hour": "08", "iso8601": "2022-08-17T08:08:06Z", "iso8601_basic": "20220817T080806663001", "iso8601_basic_short": "20220817T080806", "iso8601_micro": "2022-08-17T08:08:06.663001Z", "minute": "08", "month": "08", "second": "06", "time": "08:08:06", "tz": "UTC", "tz_dst": "UTC", "tz_offset": "+0000", "weekday": "Wednesday", "weekday_number": "3", "weeknumber": "33", "year": "2022"}, "ansible_default_ipv4": {"address": "**************", "alias": "eth0", "broadcast": "**************", "gateway": "************", "interface": "eth0", "macaddress": "f2:3c:93:56:3c:b6", "mtu": 1500, "netmask": "*************", "network": "************", "prefix": "24", "type": "ether"}, "ansible_default_ipv6": {"address": "2400:8904::f03c:93ff:fe56:3cb6", "gateway": "fe80::1", "interface": "eth0", "macaddress": "f2:3c:93:56:3c:b6", "mtu": 1500, "prefix": "64", "scope": "global", "type": "ether"}, "ansible_device_links": {"ids": {"sda": ["scsi-0QEMU_QEMU_HARDDISK_drive-scsi-disk-0"], "sdb": ["scsi-0QEMU_QEMU_HARDDISK_drive-scsi-disk-1"]}, "labels": {"sda": ["linode-root"]}, "masters": {}, "uuids": {"sda": ["180151c6-a2b2-5734-6d64-ca1d7a3fafd4"], "sdb": ["3acea225-dab7-4fd8-97ef-ced952c1740e"]}}, "ansible_devices": {"loop0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "4096", "vendor": null, "virtual": 1}, "loop1": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop2": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop3": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop4": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop5": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop6": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop7": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "sda": {"holders": [], "host": "SCSI storage controller: Red Hat, Inc. Virtio SCSI", "links": {"ids": ["scsi-0QEMU_QEMU_HARDDISK_drive-scsi-disk-0"], "labels": ["linode-root"], "masters": [], "uuids": ["180151c6-a2b2-5734-6d64-ca1d7a3fafd4"]}, "model": "QEMU HARDDISK", "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "51380224", "sectorsize": "512", "size": "24.50 GB", "support_discard": "4096", "vendor": "QEMU", "virtual": 1}, "sdb": {"holders": [], "host": "SCSI storage controller: Red Hat, Inc. Virtio SCSI", "links": {"ids": ["scsi-0QEMU_QEMU_HARDDISK_drive-scsi-disk-1"], "labels": [], "masters": [], "uuids": ["3acea225-dab7-4fd8-97ef-ced952c1740e"]}, "model": "QEMU HARDDISK", "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "1048576", "sectorsize": "512", "size": "512.00 MB", "support_discard": "4096", "vendor": "QEMU", "virtual": 1}}, "ansible_distribution": "Ubuntu", "ansible_distribution_file_parsed": true, "ansible_distribution_file_path": "/etc/os-release", "ansible_distribution_file_variety": "Debian", "ansible_distribution_major_version": "21", "ansible_distribution_release": "impish", "ansible_distribution_version": "21.10", "ansible_dns": {"nameservers": ["**********"], "options": {"edns0": true, "trust-ad": true}, "search": ["members.linode.com"]}, "ansible_domain": "localdomain", "ansible_effective_group_id": 1000, "ansible_effective_user_id": 1000, "ansible_env": {"DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1000/bus", "HOME": "/home/<USER>", "LANG": "en_US.UTF-8", "LC_ALL": "en_US.UTF-8", "LOGNAME": "xcloud", "MOTD_SHOWN": "pam", "PATH": "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin", "PWD": "/home/<USER>", "SHELL": "/bin/bash", "SHLVL": "0", "SSH_CLIENT": "************** 61789 22", "SSH_CONNECTION": "************** 61789 ************** 22", "SSH_TTY": "/dev/pts/0", "TERM": "xterm-256color", "USER": "xcloud", "XDG_RUNTIME_DIR": "/run/user/1000", "XDG_SESSION_CLASS": "user", "XDG_SESSION_ID": "277242", "XDG_SESSION_TYPE": "tty", "_": "/bin/sh"}, "ansible_eth0": {"active": true, "device": "eth0", "features": {"esp_hw_offload": "off [fixed]", "esp_tx_csum_hw_offload": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on [fixed]", "hsr_dup_offload": "off [fixed]", "hsr_fwd_offload": "off [fixed]", "hsr_tag_ins_offload": "off [fixed]", "hsr_tag_rm_offload": "off [fixed]", "hw_tc_offload": "off [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "macsec_hw_offload": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on [fixed]", "rx_fcs": "off [fixed]", "rx_gro_hw": "on", "rx_gro_list": "off", "rx_udp_gro_forwarding": "off", "rx_udp_tunnel_port_offload": "off [fixed]", "rx_vlan_filter": "on [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tls_hw_record": "off [fixed]", "tls_hw_rx_offload": "off [fixed]", "tls_hw_tx_offload": "off [fixed]", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_esp_segmentation": "off [fixed]", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_csum_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_list": "off [fixed]", "tx_gso_partial": "off [fixed]", "tx_gso_robust": "on [fixed]", "tx_ipxip4_segmentation": "off [fixed]", "tx_ipxip6_segmentation": "off [fixed]", "tx_lockless": "off [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "off [fixed]", "tx_sctp_segmentation": "off [fixed]", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_mangleid_segmentation": "off", "tx_tcp_segmentation": "on", "tx_tunnel_remcsum_segmentation": "off [fixed]", "tx_udp_segmentation": "off [fixed]", "tx_udp_tnl_csum_segmentation": "off [fixed]", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "off [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "**************", "broadcast": "**************", "netmask": "*************", "network": "************", "prefix": "24"}, "ipv4_secondaries": [{"address": "***************", "broadcast": "***************", "netmask": "*************", "network": "*************", "prefix": "17"}], "ipv6": [{"address": "2400:8904::f03c:93ff:fe56:3cb6", "prefix": "64", "scope": "global"}, {"address": "fe80::f03c:93ff:fe56:3cb6", "prefix": "64", "scope": "link"}], "macaddress": "f2:3c:93:56:3c:b6", "module": "virtio_net", "mtu": 1500, "pciid": "virtio2", "promisc": false, "speed": -1, "timestamping": [], "type": "ether"}, "ansible_fibre_channel_wwn": [], "ansible_fips": false, "ansible_form_factor": "Other", "ansible_fqdn": "xcloud-staging.localdomain", "ansible_hostname": "xcloud-staging", "ansible_hostnqn": "", "ansible_interfaces": ["eth0", "lo"], "ansible_is_chroot": false, "ansible_iscsi_iqn": "", "ansible_kernel": "5.13.0-30-generic", "ansible_kernel_version": "#33-Ubuntu SMP Fri Feb 4 17:03:31 UTC 2022", "ansible_lo": {"active": true, "device": "lo", "features": {"esp_hw_offload": "off [fixed]", "esp_tx_csum_hw_offload": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on [fixed]", "hsr_dup_offload": "off [fixed]", "hsr_fwd_offload": "off [fixed]", "hsr_tag_ins_offload": "off [fixed]", "hsr_tag_rm_offload": "off [fixed]", "hw_tc_offload": "off [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "on [fixed]", "macsec_hw_offload": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on [fixed]", "rx_fcs": "off [fixed]", "rx_gro_hw": "off [fixed]", "rx_gro_list": "off", "rx_udp_gro_forwarding": "off", "rx_udp_tunnel_port_offload": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tls_hw_record": "off [fixed]", "tls_hw_rx_offload": "off [fixed]", "tls_hw_tx_offload": "off [fixed]", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on [fixed]", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "on [fixed]", "tx_checksumming": "on", "tx_esp_segmentation": "off [fixed]", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_csum_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_list": "on", "tx_gso_partial": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipxip4_segmentation": "off [fixed]", "tx_ipxip6_segmentation": "off [fixed]", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off [fixed]", "tx_scatter_gather": "on [fixed]", "tx_scatter_gather_fraglist": "on [fixed]", "tx_sctp_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_mangleid_segmentation": "on", "tx_tcp_segmentation": "on", "tx_tunnel_remcsum_segmentation": "off [fixed]", "tx_udp_segmentation": "on", "tx_udp_tnl_csum_segmentation": "off [fixed]", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "off [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "vlan_challenged": "on [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "127.0.0.1", "broadcast": "", "netmask": "*********", "network": "*********", "prefix": "8"}, "ipv6": [{"address": "::1", "prefix": "128", "scope": "host"}], "mtu": 65536, "promisc": false, "timestamping": [], "type": "loopback"}, "ansible_local": {}, "ansible_lsb": {"codename": "impish", "description": "Ubuntu 21.10", "id": "Ubuntu", "major_release": "21", "release": "21.10"}, "ansible_machine": "x86_64", "ansible_machine_id": "5622566f4206414da9719a8e39771ef1", "ansible_memfree_mb": 149, "ansible_memory_mb": {"nocache": {"free": 758, "used": 213}, "real": {"free": 149, "total": 971, "used": 822}, "swap": {"cached": 8, "free": 1105, "total": 1535, "used": 430}}, "ansible_memtotal_mb": 971, "ansible_mounts": [{"block_available": 3761074, "block_size": 4096, "block_total": 6305367, "block_used": 2544293, "device": "/dev/sda", "fstype": "ext4", "inode_available": 1424827, "inode_total": 1605632, "inode_used": 180805, "mount": "/", "options": "rw,relatime,errors=remount-ro", "size_available": 15405359104, "size_total": 25826783232, "uuid": "180151c6-a2b2-5734-6d64-ca1d7a3fafd4"}], "ansible_nodename": "xcloud-staging", "ansible_os_family": "Debian", "ansible_pkg_mgr": "apt", "ansible_proc_cmdline": {"BOOT_IMAGE": "/boot/vmlinuz-5.13.0-30-generic", "console": "ttyS0,19200n8", "net.ifnames": "0", "ro": true, "root": "/dev/sda"}, "ansible_processor": ["0", "AuthenticAMD", "AMD EPYC 7642 48-Core Processor"], "ansible_processor_cores": 1, "ansible_processor_count": 1, "ansible_processor_nproc": 1, "ansible_processor_threads_per_core": 1, "ansible_processor_vcpus": 1, "ansible_product_name": "Standard PC (Q35 + ICH9, 2009)", "ansible_product_serial": "NA", "ansible_product_uuid": "NA", "ansible_product_version": "pc-q35-4.1", "ansible_python": {"executable": "/usr/bin/python3", "has_sslcontext": true, "type": "cpython", "version": {"major": 3, "micro": 7, "minor": 9, "releaselevel": "final", "serial": 0}, "version_info": [3, 9, 7, "final", 0]}, "ansible_python_version": "3.9.7", "ansible_real_group_id": 1000, "ansible_real_user_id": 1000, "ansible_selinux": {"status": "disabled"}, "ansible_selinux_python_present": true, "ansible_service_mgr": "systemd", "ansible_ssh_host_key_dsa_public": "AAAAB3NzaC1kc3MAAACBAOrjfRqpK9n2H3yhAsL9KKBIBJQ1SiqM9iH5p/UAwiVoFECpnmeC38UIzHiHEJrjv6jBo0QHGS1C4fBQzwU6tvR3iUguO9BnspOw97P4peXHJuWZ6J6wr8YTqDA+bnuM5JetYM6ePDEKNY8h7fXjsAQM4Z0fyjNW+tx94CnbHgnrAAAAFQDdaiR7eM6Xr8eu77B6ZNV10ApO6QAAAIBko8g6miMO1dRr2aJPc9BOj4n0nF40pgEfIEZLajCmlC80XAFV/KtYJewOpWnuxnzkCaYqcSY9t+wpNB6oqqy1b0SzRjpuwdjkpD1ciyqj1oairPhqbcMz8Y9Yln3114DmzycSLiI84PXi9u/KFKHtVyaG+uR/5x0GdQLMcNCvWAAAAIAdK+jO1xCTxIF4iM+yxe9cHN+nUybCYnR6CT8XcTONBRoa/lgr3dVaWIl+HhGo3OqJuA0TlciAOLx1Pyqjq0qUG6RjWVXZkEBTnJhntjLXGemZbDszsZxsojKyrXKaAgyqKF16XHm7Zk/+zRa5ZEK9uXT0yQazpwPB8bmGcebwGw==", "ansible_ssh_host_key_dsa_public_keytype": "ssh-dss", "ansible_ssh_host_key_ecdsa_public": "AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBLQT1n6dTAOaFgUqTC6F3S9aS+Bt+rkUSlGgQ/c2wHAPgxksBT8j4HWyNjTQWWT8XSpMQxa0KTTVtUjTaAupXWc=", "ansible_ssh_host_key_ecdsa_public_keytype": "ecdsa-sha2-nistp256", "ansible_ssh_host_key_ed25519_public": "AAAAC3NzaC1lZDI1NTE5AAAAIAu9Lz6ViaCqaRVYQSeh/LuECkbA+WR9w9EQxsnX2/nc", "ansible_ssh_host_key_ed25519_public_keytype": "ssh-ed25519", "ansible_ssh_host_key_rsa_public": "AAAAB3NzaC1yc2EAAAADAQABAAABgQCvUcz0dvRV6cVGGNnnDp6dOQ9X862Xk7/hwBvtvIfpPtsHXx+0/jaV7vBWLtCtoneT2T27CAf/ca1e1zyKi4XHn1j4dyd/5GNkme8dy2JdTXRtFp8m/Rk95BAht2V5Z3JLpvi/HsUEJh09myrmF1qmXESKB5N0vh6W/XAkvzfZwLIZht8oFDAlGRyNKyGDpNteNmgB3ve9tasR0j6uiT2t6evAIBARj2B5ZXJoiJJpqo04Hc/OD/9JRaCrU/7314EQaFgPl8GKRELyAbuoIbo7qKbMFJ+3BS4uua0cXlgONvl1TlAZEN8XUzIDFXwPPROQxXMUWiczRnzkUV/QATaGCqS3wl6Bh7rKbKCzbrTBQ2KzQdAcF9wu6IUTpoiBtSAwli1ObaoTDFJ0wblSGxZXM20zf7aNvX+3y5Ws41pp2EbMWY5hMk8/puhM2/MeWfvk1hJJv/h9dJT/508VZQRwMIcsdD79/ytiryVmVbxGS517VSn3eR8YPgnAqxqDPgk=", "ansible_ssh_host_key_rsa_public_keytype": "ssh-rsa", "ansible_swapfree_mb": 1105, "ansible_swaptotal_mb": 1535, "ansible_system": "Linux", "ansible_system_capabilities": [""], "ansible_system_capabilities_enforced": "True", "ansible_system_vendor": "QEMU", "ansible_uptime_seconds": 13824469, "ansible_user_dir": "/home/<USER>", "ansible_user_gecos": "", "ansible_user_gid": 1000, "ansible_user_id": "xcloud", "ansible_user_shell": "/bin/bash", "ansible_user_uid": 1000, "ansible_userspace_architecture": "x86_64", "ansible_userspace_bits": "64", "ansible_virtualization_role": "guest", "ansible_virtualization_tech_guest": ["kvm"], "ansible_virtualization_tech_host": [], "ansible_virtualization_type": "kvm", "discovered_interpreter_python": "/usr/bin/python3", "gather_subset": ["all"], "module_setup": true}, "changed": false, "deprecations": [], "warnings": []}}, "task": {"duration": {"end": "2022-08-17T08:08:07.536783Z", "start": "2022-08-17T08:08:02.381022Z"}, "id": "baef7c61-fc27-cd4d-ec16-00000000000e", "name": "Gathering Facts"}}, {"hosts": {"**************": {"_ansible_no_log": null, "action": "command", "changed": true, "cmd": ["pwd"], "delta": "0:00:00.003437", "end": "2022-08-17 08:08:10.770341", "invocation": {"module_args": {"_raw_params": "pwd", "_uses_shell": false, "argv": null, "chdir": null, "creates": null, "executable": null, "removes": null, "stdin": null, "stdin_add_newline": true, "strip_empty_ends": true, "warn": false}}, "msg": "", "rc": 0, "start": "2022-08-17 08:08:10.766904", "stderr": "", "stderr_lines": [], "stdout": "/home/<USER>", "stdout_lines": ["/home/<USER>"]}}, "task": {"duration": {"end": "2022-08-17T08:08:11.062930Z", "start": "2022-08-17T08:08:07.557906Z"}, "id": "baef7c61-fc27-cd4d-ec16-000000000008", "name": "print to stdout"}}, {"hosts": {"**************": {"_ansible_no_log": null, "_ansible_verbose_always": true, "action": "debug", "changed": false, "msg": "/home/<USER>"}}, "task": {"duration": {"end": "2022-08-17T08:08:11.113161Z", "start": "2022-08-17T08:08:11.082604Z"}, "id": "baef7c61-fc27-cd4d-ec16-000000000009", "name": "stdout"}}, {"hosts": {"**************": {"_ansible_no_log": null, "_ansible_verbose_always": true, "action": "debug", "changed": false, "msg": ""}}, "task": {"duration": {"end": "2022-08-17T08:08:11.135646Z", "start": "2022-08-17T08:08:11.120267Z"}, "id": "baef7c61-fc27-cd4d-ec16-00000000000a", "name": "stderr"}}]}], "stats": {"**************": {"changed": 1, "failures": 0, "ignored": 0, "ok": 4, "rescued": 0, "skipped": 0, "unreachable": 0}}}