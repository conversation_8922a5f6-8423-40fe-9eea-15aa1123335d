import {computed} from "vue";
import {asset} from "laravel-vapor";
import {useNavigationStore} from "@/stores/NavigationStore";
import { usePage } from "@inertiajs/inertia-vue3";

export function useCloudProviderIcon(cloud_provider) {
    const nightMode = useNavigationStore().nightMode;
    const pageProps = usePage().props.value;

    const cloudProviderIcon = computed(() => {
        switch (cloud_provider?.toLowerCase()?.replace(/\s/g, '')) {
            case 'digitalocean':
            case 'digital_ocean':
            case 'do':
                return asset('img/digital_ocean.svg')
            case 'hetzner':
                return asset('img/hetzner-logo-h.png')
            case 'linode':
                return asset('img/akamai.png')
            case 'vultr':
                return asset('img/vultr.svg')
            case 'aws':
                if(nightMode) {
                    return asset('img/aws_dark_mode.svg')
                } else {
                    return asset('img/aws.svg')
                }
            case 'googlecloud':
            case 'gcp':
                return asset('img/google-cloud-platform.svg')
            case 'azure':
                return asset('img/azure.svg')
            case 'xcloud':
                return pageProps?.current_white_label ? pageProps.current_white_label.brand_photo_url : asset('img/x_cloud.svg')
            case 'xcloud_vultr':
                return pageProps?.current_white_label ? pageProps.current_white_label.brand_photo_url : asset('img/x_cloud.svg');
            case 'xcloud_provider':
                return pageProps?.current_white_label ? pageProps.current_white_label.brand_photo_url : asset('img/x_cloud.svg')
            case 'backblaze_b2':
                return asset('img/integration/backblaze_b2.svg')
            case 'otherprovider':
                return asset('img/other_providers.svg')
            case 'google_drive':
                return asset('img/integration/google_drive.svg')
            case 'google_cloud_storage':
                return asset('img/google-cloud-platform.svg')
            case 'cloudflare_r2':
            case 'cloudflare':
                return asset('img/integration/cloudflare.svg')
        }

        return asset('img/other_providers.svg')
    })


    return {cloudProviderIcon}
}
