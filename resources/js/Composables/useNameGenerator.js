export function useNameGenerator(string, limit = 16) {
    string = string.split('.').reduce((carry, part) => {
        return carry.length > 4 ? carry : carry + '_' + part;
    }, '');

    string = string.toLowerCase()
        .replace(/(\.|-|\s)/g, '_')
        .replace(/[^\w\s-]/g, '_')
        .replace(/[\s_-]+/g, '_')
        .replace(/^-+|-+$/g, '_')
        .replace(/_+/g, '_')
        .substring(0, limit)
        .replace(/^(_)?(.*?)(_)?$/g, '$2')
        .trim();

    return string;
}
