export function useCapitalizeString(string) {

    const capitalize = () => {
        let capitalized = []
        string.split(' ').forEach(word => {
            capitalized.push(
                word.charAt(0).toUpperCase() +
                word.slice(1).toLowerCase()
            )
        })
        return capitalized.join(' ');
    }

    const textFromLink = () => {
        const regex = /<a[^>]*>([^<]+)<\/a>/;
        // Executing the regular expression pattern on the author link
        const match = regex.exec(string);
        // Check if a match is found
        if (match && match.length > 1) {
            return  match[1]; // Extracted author name
        } else {
            return string;
        }
    }


    const sliceString = (length=20) => {
        return string.length > length ? string.slice(0, length) + '...' : string;
    }

    const sanitizeXSSPayload = () => {
        return String(string)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    const formatProviderName = () => {
        return string.replaceAll('_', ' ');
    }

    const truncateText = (length = 65) => {
        if (!string) return '';
        return string.length > length ? string.slice(0, length) + '...' : string;
    }

    return {capitalize,textFromLink,sliceString,sanitizeXSSPayload,formatProviderName,truncateText}
}
