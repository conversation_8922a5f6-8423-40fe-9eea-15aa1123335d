import {computed} from "vue";
import {asset} from "laravel-vapor";

export function useIntegrationIcon(provider) {
    const integrationIcon = computed(() => {
        switch (provider?.toLowerCase()?.replace(/\s/g, '')) {
            case 'digitalocean':
            case 'digital_ocean':
            case 'do':
                return asset('img/integration/digitalocean.svg')
            case 'sendgrid':
                return asset('img/integration/sendgrid.svg')
            case 'xcloudmanagedemailservice':
            case 'xcloud':
                return asset('img/integration/xcloud.svg')
            case 'slack':
                return asset('img/integration/slack.svg')
            case 'whatsapp':
                return asset('img/integration/whatsapp.svg')
            case 'telegram':
                return asset('img/integration/telegram.svg')
            case 'vultr':
                return asset('img/integration/vultr.svg')
            case 'cloudflare_r2':
            case 'cloudflare':
                return asset('img/integration/cloudflare.svg')
            case 'backblaze_b2':
                return asset('img/integration/backblaze_b2.svg')
            case 'mailgunapi':
            case 'mailgun':
                return asset('img/integration/mailgun.svg')
            case 'email':
                return asset('img/integration/email.svg')
            case 'hetzner':
                return asset('img/hetzner-logo-h.png')
            case 'google_drive':
                return asset('img/integration/google_drive.svg')
            case 'google_cloud_storage':
                return asset('img/google-cloud-platform.svg')
            case 'other':
                return asset('img/other_providers.svg')
        }

        return asset('img/other_providers.svg')
    })


    return {integrationIcon}
}
