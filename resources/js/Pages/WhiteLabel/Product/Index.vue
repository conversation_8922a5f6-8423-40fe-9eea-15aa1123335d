<template>
    <single-white-label active="Products">
        <Head title="Products"/>
        <div class="flex flex-col p-6 gap-3 bg-white dark:bg-mode-light w-full rounded-lg">
            <div class="inline-flex items-end gap-2 text-secondary-full dark:text-white">
                <h4 class="text-2xl font-medium leading-none font-manrope">{{ $t('Products') }}</h4>
                <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2">
                    <SelectorTitleDropdown position="right" :selected-item="sortByStatus" :custom-class="'!w-52'">
                        <SelectItem
                            title="Active"
                            :is-active="sortByStatus === 'Active'"
                            @onItemSelected="onSortByChanged($event)"
                        />
                        <SelectItem
                            title="Inactive"
                            :is-active="sortByStatus === 'Inactive'"
                            @onItemSelected="onSortByChanged($event)"
                        />
                    </SelectorTitleDropdown>
                    <button @click.prevent="addNewProduct" class="inline-flex items-center justify-center rounded gap-2 border-primary-light shadow-none min-h-10 px-4 bg-primary-light text-sm font-medium text-white focus:outline-0">
                        <span class="inline-flex items-center text-xs">
                            <i class="xcloud xc-add"></i>
                        </span>
                        {{ $t('Add Product') }}
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto flex flex-col">
                <div class="inline-block min-w-full">
                    <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                        <thead>
                        <tr>
                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Plan Name') }}</th>
                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Type') }}</th>
                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Renewal Type') }}</th>
                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('SKU') }}</th>
                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Buying Price') }}</th>
                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Selling Price') }}</th>
                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Status') }}</th>
                            <th scope="col" class="py-3 px-6 text-right text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Actions') }}</th>
                        </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-mode-base">
                            <tr v-for="product in products.data" :key="product.id"
                                class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                                    <Link :href="route('white-label.products.details', product.id)">
                                        {{ product?.title }}
                                    </Link>
                                </td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                                    {{ product?.type }}
                                </td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                                    {{ product?.renewal_type }}
                                </td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                    {{ product?.sku }}
                                </td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                    ${{ product?.source?.price }}
                                </td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                    ${{ product?.price }}
                                </td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                    <span v-if="product?.is_active" class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-success-full/10 text-xs leading-none text-success-full focus:outline-0">
                                        <span>Active</span>
                                    </span>
                                    <span v-else class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-danger text-xs leading-none text-white focus:outline-0">
                                        <span>Inactive</span>
                                    </span>
                                </td>
                                <td class="whitespace-nowrap w-14 px-30px py-20px text-right text-sm font-normal text-dark dark:text-white">
                                    <span class="flex gap-30px">
                                        <button @click.prevent="openDuplicateProduct(product)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-copy"></i>
                                            </span>
                                        </button>
                                        <Link :href="route('white-label.products.details', product.id)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-maximize"></i>
                                            </span>
                                         </Link>
                                        <button @click.prevent="editProduct(product)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-edit"></i>
                                            </span>
                                        </button>
                                        <button @click.prevent="destroyProduct(product)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-delete"></i>
                                            </span>
                                        </button>
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <Pagination :links="products.links"/>
        </div>
    </single-white-label>

    <Modal
        @close="closeModalOne"
        :show="addProductModal"
        :footerButton="true"
        :title="isEditMode ? $t('Edit Product') : $t('Create New Product')"
        :widthClass="'max-w-244'"
    >

        <div class="flex flex-col gap-5">
            <div class="flex flex-col gap-3">
                <h3 class="text-lg font-normal text-dark dark:text-white leading-none">
                    {{ $t('Select Server Plan at xCloud') }}
                </h3>
                <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-4">
                    <label class="w-full">
                        <input type="radio" v-model="serverType" :disabled="isEditMode" name="product-type" id="general" value="general" class="peer hidden">
                        <span
                            class="bg-light dark:bg-mode-base flex gap-6 items-start w-full rounded-lg border-1 border-solid border-transparent peer-checked:border-primary-light p-6 cursor-pointer relative after:absolute after:top-3 after:right-3 after:h-4 after:aspect-square after:shrink-0 after:rounded-full after:inline-flex after:justify-center after:items-center after:font-xc after:content-['\e927'] after:text-xxxs after:text-transparent after:border-2 after:border-secondary-light dark:after:border-dark peer-checked:after:bg-primary-light peer-checked:after:border-primary-light peer-checked:after:text-white dark:peer-checked:after:text-mode-base"
                        >
                            <span class="w-16 aspect-square shrink-0 rounded-lg bg-white dark:bg-mode-light inline-flex items-center justify-center">
                                <img
                                    :src="asset('img/png/cloud-storage 1.png')"
                                    alt="xcloud_logo"
                                    class="w-12 h-auto"
                                />
                            </span>
                            <span class="flex flex-col gap-2">
                                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                    General
                                </h5>
                                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                    {{ $t('Cost-effective servers powered by Intel CPUs and regular SSDs.') }}
                                </p>
                            </span>
                        </span>
                    </label>
                    <label class="w-full">
                        <input type="radio" v-model="serverType" :disabled="isEditMode" name="product-type" id="premium" value="premium" class="peer hidden">
                        <span
                            class="bg-light dark:bg-mode-base flex gap-6 items-start w-full rounded-lg border-1 border-solid border-transparent peer-checked:border-primary-light p-6 cursor-pointer relative after:absolute after:top-3 after:right-3 after:h-4 after:aspect-square after:shrink-0 after:rounded-full after:inline-flex after:justify-center after:items-center after:font-xc after:content-['\e927'] after:text-xxxs after:text-transparent after:border-2 after:border-secondary-light dark:after:border-dark peer-checked:after:bg-primary-light peer-checked:after:border-primary-light peer-checked:after:text-white dark:peer-checked:after:text-mode-base"
                        >
                            <span class="w-16 aspect-square shrink-0 rounded-lg bg-white dark:bg-mode-light inline-flex items-center justify-center">
                                <img
                                    :src="asset('img/png/cloud-server 2.png')"
                                    alt="xcloud_logo"
                                    class="w-12 h-auto"
                                />
                            </span>
                            <span class="flex flex-col gap-2">
                                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                    {{ $t('Premium') }}
                                </h5>
                                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                    {{ $t('Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.') }}
                                </p>
                            </span>
                        </span>
                    </label>
                </div>
            </div>

            <div class="flex flex-col gap-3">
                <suggestion
                    v-if="isSuggestion"
                    class="mb-0 pl-2"
                    message="We recommend choosing the 2 GB server type based on your requirements."
                    :light-mode="false"
                />

                <h3 class="text-lg font-normal text-dark dark:text-white leading-none">
                    {{ $t('Select Server Size') }}:
                </h3>
                <div class="grid grid-cols-auto-56 gap-4">
                    <label
                        v-for="(product, key) in whiteLabelAllProducts"
                        :key="key"
                        class="w-full"
                    >
                        <input
                            type="radio"
                            v-model="selectedPlan"
                            :id="'basic' + key"
                            class="hidden peer"
                            :value="product"
                            :disabled="isEditMode"
                        />
                        <div
                            class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light peer-checked:!border-primary-light"
                        >
                            <div class="flex items-center gap-2 px-4 py-3 justify-start bg-light dark:bg-dark">
                                <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ product?.title }}</h4>
                                <tooltip v-if="product?.tooltip" :title="product?.tooltip" align="bottom">
                                    <button
                                        title="Info Here"
                                        class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                                    >
                                        <i class="xcloud xc-info"></i>
                                    </button>
                                </tooltip>
                                <span class="ml-auto text-base text-primary-light" v-if="selectedPlan?.id === product.id">
                                    <i class="xcloud xc-checkbox"></i>
                                </span>
                            </div>
                            <div class="px-4 py-3">
                                <ul class="list-none marker:text-light flex flex-col gap-2 text-sm">
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> {{ $t('RAM') }} -
                                        <span class="text-dark dark:text-white">
                                            {{ product?.memory }}
                                        </span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> {{ $t('SSD') }} -
                                        <span class="text-dark dark:text-white">{{ product?.disk }} </span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> {{ $t('vCPU') }} -
                                        <span class="text-dark dark:text-white">{{ product?.cpu }}</span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> {{ $t('Bandwidth') }} -
                                        <span class="text-dark dark:text-white">{{ product?.bandwidth }}</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="flex items-center gap-3 px-4 py-3 justify-start">
                                <h4 class="text-base leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ product?.price }}/ <small class="text-secondary-full dark:text-mode-secondary-light">{{ $t('month') }}</small></h4>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex justify-end">
                <button @click.prevent="addNewProductStepTwo" class="inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 min-h-10 px-6 bg-primary-light text-base font-medium text-white focus:outline-0">
                    {{ $t('Next') }}
                </button>
            </div>
        </template>
    </Modal>

    <Modal
        @close="closeModalTwo"
        :show="addStepTwoProductModal"
        :footerButton="true"
        :title="isEditMode ? $t('Edit Product') : $t('Create New Product')"
        :widthClass="'max-w-244'"
    >
        <div class="flex flex-col gap-6">
            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Source Product') }}
                    </h3>
                    <button
                        v-if="!isEditMode"
                        @click.prevent="addNewProduct"
                        class="ml-auto text-xs leading-tight text-primary-light hover:underline underline-offset-2"
                    >
                        {{ $t('Change Plan') }}
                    </button>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">
                          {{ isEditMode ? selectedPlan?.source?.title : selectedPlan?.title }}
                        </h4>
                        <tooltip :title="isEditMode ? selectedPlan?.source?.title : selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                        <span class="ml-auto text-sm text-success-light inline-flex items-center">
                            <i class="xcloud xc-tick-o"></i>
                        </span>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white">
                              ${{ isEditMode ? selectedPlan?.source?.price : selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">{{ $t('month') }}</small>
                            </h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ isEditMode ? selectedPlan?.source?.memory : selectedPlan?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ isEditMode ? selectedPlan?.source?.disk : selectedPlan?.disk }}</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ isEditMode ? selectedPlan?.source?.cpu : selectedPlan?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ isEditMode ? selectedPlan?.source?.bandwidth : selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>

            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Customize Package') }}
                    </h3>
                </div>
                <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
                    <text-input
                        v-model="form.plan_name"
                        id="plan_name"
                        :placeholder="$t('My Server')"
                        :label="$t('Plan Name')"/>
                    <text-input
                        type="number"
                        v-model="form.custom_price"
                        id="custom_price"
                        :placeholder="minPrice.toFixed(2)"
                        :error="form.errors.custom_price"
                        :label="$t('Price/Month')"/>
                    <text-input
                        v-model="form.sku"
                        id="sku"
                        :placeholder="$t('XCPU110')"
                        :error="form.errors.sku"
                        :label="$t('SKU')"
                        @input="changeSku"/>
                </div>
            </div>

            <div>
                <suggestion
                    type="slot"
                    :light-mode="false"
                    class="mb-0 pl-2"
                >
                    <a href="https://stripe.com/pricing" target="_blank" class="underline cursor-pointer">{{ $t('Stripe') }}</a>
                    {{ $t('will deduct a 3%-7% fee per sale. Your approximate profit for this sale is') }} ${{ calculateProfit(basePrice, form.custom_price, 3) }}-${{ calculateProfit(basePrice, form.custom_price, 7) }}
                </suggestion>
            </div>

            <div
                class="flex gap-6 items-center justify-start"
            >
                <div class="flex gap-1 items-center">
                    <h5 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Active Package') }}
                    </h5>
                    <tooltip title="Toggle on to active the package">
                        <button
                            title="Info Here"
                            class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-dark dark:text-white text-sm"
                        >
                            <i class="xcloud xc-info"></i>
                        </button>
                    </tooltip>
                </div>
                <label class="inline-flex outline-none">
                    <input
                        v-model="form.is_active"
                        :checked="form.is_active"
                        class="hidden peer"
                        type="checkbox"
                        :disabled="false"
                    />
                    <span
                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0
                        before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none
                        before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1
                        before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white
                        cursor-pointer"
                    >
                    </span>
                </label>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Preview Plan') }}
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ form.plan_name || selectedPlan?.title }}</h4>
                        <tooltip :title="form.plan_name || selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                        <!--<span class="ml-auto text-sm text-success-light inline-flex items-center">
                            <i class="xcloud xc-tick-o"></i>
                        </span>-->
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ form.custom_price || selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}g</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex items-center gap-4">
                <button v-if="!isEditMode" @click.prevent="backToStepOne" class="inline-flex items-center justify-center rounded-lg border-1 border-primary-light shadow-none gap-2 min-h-10 px-6 bg-transparent text-base font-medium text-primary-light hover:bg-primary-light hover:text-white transition duration-75 ease-in-out focus:outline-0">
                    {{ $t('Back') }}
                </button>
                <div class="flex items-center gap-4 ml-auto">
                    <!--<button class="inline-flex items-center justify-center rounded-lg border-1 border-success-light shadow-none gap-2 min-h-10 px-6 bg-transparent text-base font-medium text-success-light hover:bg-success-light hover:text-white transition duration-75 ease-in-out focus:outline-0">
                        Save
                    </button>-->
                    <button @click.prevent="saveProduct" :disabled="isPublishRunning" class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2 min-h-10 px-6 bg-success-light text-base font-medium text-white focus:outline-0">
                        <i v-if="isPublishRunning"
                           class="xcloud xc-verify_dns text-white animate-spin mr-1">
                        </i>{{ $t('Save and Publish') }}
                    </button>
                </div>
            </div>
        </template>
    </Modal>

    <Modal
        @close="closeDuplicateModal"
        :show="openDuplicateProductModal"
        :footerButton="true"
        :title="$t('Duplicate Product')"
        :widthClass="'max-w-244'"
    >
        <div class="flex flex-col gap-6">
            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Source Product') }}
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">
                            {{ selectedPlan?.source?.title }}
                        </h4>
                        <tooltip :title="selectedPlan?.source?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                        <span class="ml-auto text-sm text-success-light inline-flex items-center">
                            <i class="xcloud xc-tick-o"></i>
                        </span>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white">
                                ${{ selectedPlan?.source?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">{{ $t('month') }}</small>
                            </h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.source?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.source?.disk }}</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.source?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.source?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>

            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Customize Package') }}
                    </h3>
                </div>
                <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
                    <text-input
                        v-model="form.plan_name"
                        id="plan_name"
                        :placeholder="$t('My Server')"
                        :label="$t('Plan Name')"/>
                    <text-input
                        type="number"
                        v-model="form.custom_price"
                        id="custom_price"
                        :placeholder="minPrice.toFixed(2)"
                        :error="form.errors.custom_price"
                        :label="$t('Price/Month')"/>
                    <text-input
                        v-model="form.sku"
                        id="sku"
                        :placeholder="$t('XCPU110')"
                        :error="form.errors.sku"
                        :label="$t('SKU')"
                        @input="changeSku"/>
                </div>
            </div>

            <div>
                <suggestion
                    type="slot"
                    :light-mode="false"
                    class="mb-0 pl-2"
                >
                    <a href="https://stripe.com/pricing" target="_blank" class="underline cursor-pointer">{{ $t('Stripe') }}</a>
                    {{ $t('will deduct a 3%-7% fee per sale. Your approximate profit for this sale is') }} ${{ calculateProfit(basePrice, form.custom_price, 3) }}-${{ calculateProfit(basePrice, form.custom_price, 7) }}
                </suggestion>
            </div>

            <div
                class="flex gap-6 items-center justify-start"
            >
                <div class="flex gap-1 items-center">
                    <h5 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Active Package') }}
                    </h5>
                    <tooltip title="Toggle on to active the package">
                        <button
                            title="Info Here"
                            class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-dark dark:text-white text-sm"
                        >
                            <i class="xcloud xc-info"></i>
                        </button>
                    </tooltip>
                </div>
                <label class="inline-flex outline-none">
                    <input
                        v-model="form.is_active"
                        :checked="form.is_active"
                        class="hidden peer"
                        type="checkbox"
                        :disabled="false"
                    />
                    <span
                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0
                        before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none
                        before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1
                        before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white
                        cursor-pointer"
                    >
                    </span>
                </label>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Preview Plan') }}
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ form.plan_name || selectedPlan?.title }}</h4>
                        <tooltip :title="form.plan_name || selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ form.custom_price || selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-4 ml-auto">
                    <button @click.prevent="duplicateProduct" :disabled="isPublishRunning" class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2 min-h-10 px-6 bg-success-light text-base font-medium text-white focus:outline-0">
                        <i v-if="isPublishRunning"
                           class="xcloud xc-verify_dns text-white animate-spin mr-1">
                        </i>{{ $t('Duplicate') }}
                    </button>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import Modal from "@/Shared/Modal.vue";
import {computed, onMounted, ref, watch} from "vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import SelectorTitleDropdown from "../Components/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import Button from "@/Jetstream/Button.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const addProductModal = ref(false);
const addStepTwoProductModal = ref(false);
const openDuplicateProductModal = ref(false);
const selectedPlan = ref(null);
const isEditMode = ref(false)
const productId = ref(null)
const isSuggestion = ref(false);
const priceError = ref(false);
const isPublishRunning = ref(false);
const basePrice = ref(0);

const props = defineProps({
    products: Object,
    whiteLabelProducts: Object,
    skuPrefix: String,
    sort_by_status: String
});

const sortByStatus = ref(props.sort_by_status ?? "Sort By Status")
let serverType = ref("general");
const whiteLabelAllProducts = computed(() => {
    return props.whiteLabelProducts[serverType.value];
});

const product_id = computed(() => selectedPlan.value?.id || null);
const minPrice = ref(0);

let form = useForm({
    plan_name: "",
    custom_price: null,
    sku: "",
    server_type: serverType.value,
    product_id: product_id.value,
    is_active: true
});

const generateSku = computed(() => {
    if (!selectedPlan.value) {
        return '';
    }

    const parts = selectedPlan.value?.sku.split('-');
    parts[0] = props.skuPrefix;
    const baseSku = parts.join('-');

    // Check if this SKU already exists in the products list
    const existingSkus = props.products.data.map(product => product.sku);

    if (!existingSkus.includes(baseSku)) {
        return baseSku;
    }

    // If SKU exists, find the highest number suffix and increment it
    const skuPattern = new RegExp(`^${baseSku}-(\d+)$`);
    let highestNumber = 0;

    existingSkus.forEach(sku => {
        const match = sku.match(skuPattern);
        if (match) {
            const num = parseInt(match[1], 10);
            if (num > highestNumber) {
                highestNumber = num;
            }
        }
    });

    // If no numbered suffix exists yet, start with 1, otherwise increment the highest
    return `${baseSku}-${highestNumber + 1}`;
});

const addNewProduct = () => {
    serverType.value = 'general';
    form.plan_name = '';
    form.custom_price = '';
    isEditMode.value = false;
    addStepTwoProductModal.value = false;
    addProductModal.value = true;
}

const backToStepOne = () => {
    form.custom_price = null;
    addStepTwoProductModal.value = false;
    addProductModal.value = true;
}

const addNewProductStepTwo = () => {
    if (selectedPlan.value) {
        let minimumPrice = selectedPlan.value?.price * 1.1;
        minPrice.value = parseFloat(minimumPrice.toFixed(2));
        basePrice.value = selectedPlan.value?.price;
        addProductModal.value = false;
        addStepTwoProductModal.value = true;
    } else {
        useFlash().error('Please select a Hosting Plan');
    }
}

const saveProduct = () => {
    if (priceError.value) {
        return;
    }

    isPublishRunning.value = true;

    if (isEditMode.value) {
        form.post(route('white-label.products.update', {product: productId.value}), {
            preserveScroll: true,
            onSuccess: () => {
                addStepTwoProductModal.value = false;
                serverType.value = 'general';
                isEditMode.value = false;
                isPublishRunning.value = false;
                resetForm();
                Inertia.reload();
            },
            onError: (errors) => {
                isPublishRunning.value = false;
            }
        });
    } else {
        form.post('/white-label/products/store', {
            preserveScroll: true,
            onSuccess: () => {
                form.plan_name = "";
                addStepTwoProductModal.value = false;
                serverType.value = 'general';
                isPublishRunning.value = false;
                resetForm();
                Inertia.reload();
            },
            onError: (errors) => {
                isPublishRunning.value = false;
            }
        });
    }
}

const editProduct = (product) => {
    if (product.description){
        const descriptions = product.description.trim().split('\n');
        descriptions.forEach(description => {
            if (description.includes('RAM')) {
                product.memory = description.replace('RAM -', '').trim();
            } else if (description.includes('SSD')) {
                product.disk = description.replace('SSD -', '').trim();
            } else if (description.includes('vCPU')) {
                product.cpu = description.replace('vCPU -', '').trim();
            } else if (description.includes('Bandwidth')) {
                product.bandwidth = description.replace('Bandwidth -', '').trim();
            }
        });
    }
    isEditMode.value = true;
    productId.value = product.id;
    selectedPlan.value = product;
    serverType.value = product.type
    form.plan_name = product.title;
    form.custom_price = product.price;
    form.is_active = product.is_active;
    form.sku = product.sku;
    basePrice.value = product?.source?.price;
    let minimumPrice = basePrice.value * 1.1;
    minPrice.value = parseFloat(minimumPrice.toFixed(2));
    addStepTwoProductModal.value = true;
    //addProductModal.value = true;
}

const openDuplicateProduct = (product) => {
    // if the product contains depended id can't be duplicated
    if (product.depends_on_product_id) {
        useFlash().error('A Depended product can not be duplicated. It will be automatically duplicate while duplicating the main product.');
        return;
    }

    if (product.description){
        const descriptions = product.description.trim().split('\n');
        descriptions.forEach(description => {
            if (description.includes('RAM')) {
                product.memory = description.replace('RAM -', '').trim();
            } else if (description.includes('SSD')) {
                product.disk = description.replace('SSD -', '').trim();
            } else if (description.includes('vCPU')) {
                product.cpu = description.replace('vCPU -', '').trim();
            } else if (description.includes('Bandwidth')) {
                product.bandwidth = description.replace('Bandwidth -', '').trim();
            }
        });
    }

    productId.value = product.id;
    selectedPlan.value = product;
    serverType.value = product.type
    form.plan_name = product.title;
    form.custom_price = product.price;
    form.is_active = product.is_active;
    form.sku = product.sku;
    basePrice.value = product?.source?.price;
    let minimumPrice = basePrice.value * 1.1;
    minPrice.value = parseFloat(minimumPrice.toFixed(2));
    openDuplicateProductModal.value = true;
}

const duplicateProduct = () => {
    form.post(route('white-label.products.duplicate', {product: productId.value}), {
        preserveScroll: true,
        onSuccess: () => {
            openDuplicateProductModal.value = false;
            serverType.value = 'general';
            isPublishRunning.value = false;
            resetForm();
            Inertia.reload();
        },
        onError: (errors) => {
            isPublishRunning.value = false;
        }
    });
}

const destroyProduct = (product) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure?'),
        text: t("You won't be able to revert this!"),
        btn_text: t('Yes, Remove!'),
    }, () => {
        Inertia.delete(route('white-label.products.delete', [product.id]), {
            preserveScroll: true,
            onSuccess: (page) => {
                if (page?.props?.jetstream?.flash?.type !== 'error') {
                    // Use the flash message from the backend response
                    const flashMessage = page?.props?.jetstream?.flash?.message || 'Product Removed Successfully';
                    useFlash().success(flashMessage);
                } else {
                    // Use the default success message
                    useFlash().error(page?.props?.jetstream?.flash?.message);
                }
            }
        })
    });
}

const onSortByChanged = (status) => {
    sortByStatus.value = status;
    Inertia.get(route("white-label.products.index"), {
        sort_by_status: sortByStatus.value
    });
}

onMounted(() => {
    const selectedProduct = whiteLabelAllProducts.value && whiteLabelAllProducts.value.find(
        (product) => product.memory === '2 GB'
    );

    if (selectedProduct) {
        selectedPlan.value = selectedProduct;
    }
});

function calculateProfit(previousPrice, newPrice, feePercentage) {
    const currentPrice = newPrice * (1 - feePercentage / 100);
    return (currentPrice - previousPrice).toFixed(2);
}

const resetForm = () => {
    form.plan_name = "";
    form.custom_price = null;
    form.sku = generateSku.value;
}

const changeSku = () => {
    form.errors.sku = ""
}

const closeModalOne = () => {
    addProductModal.value = false;
    resetForm();
}

const closeModalTwo = () => {
    addStepTwoProductModal.value = false;
    resetForm();
}

const closeDuplicateModal = () => {
    openDuplicateProductModal.value = false;
    resetForm();
}

watch(selectedPlan, (newVal) => {
    isSuggestion.value = newVal && newVal?.memory === '1 GB';

    form.product_id = newVal?.id || null;
});

watch(serverType, (newVal) => {
    form.server_type = newVal || 'general';
});

watch(generateSku, (newVal) => {
    if (!isEditMode.value) {
        form.sku = newVal || null;
    }
});

watch(() => form.custom_price, (newPrice) => {
    if (newPrice && parseFloat(newPrice) < minPrice.value) {
        priceError.value = true;
        form.errors.custom_price = `Price must be at least $${minPrice.value}`;
    } else {
        priceError.value = false;
        form.errors.custom_price = '';
    }
});
</script>

<style scoped>

</style>

