<template>
    <single-white-label active="Products">
        <Head title="Products"/>
        <div class="flex flex-col p-6 gap-3 bg-white dark:bg-mode-light w-full rounded-lg">
            <div class="inline-flex items-end gap-2 text-secondary-full dark:text-white">
                <h4 class="text-2xl font-medium leading-none font-manrope">{{ $t('Products') }}</h4>
                <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2">
                    <SelectorTitleDropdown position="right" :selected-item="sortByStatus" :custom-class="'!w-52'">
                        <SelectItem
                            title="Active"
                            :is-active="sortByStatus === 'Active'"
                            @onItemSelected="onSortByChanged($event)"
                        />
                        <SelectItem
                            title="Inactive"
                            :is-active="sortByStatus === 'Inactive'"
                            @onItemSelected="onSortByChanged($event)"
                        />
                    </SelectorTitleDropdown>
                    <button @click.prevent="addNewProduct" class="inline-flex items-center justify-center rounded gap-2 border-primary-light shadow-none min-h-10 px-4 bg-primary-light text-sm font-medium text-white focus:outline-0">
                        <span class="inline-flex items-center text-xs">
                            <i class="xcloud xc-add"></i>
                        </span>
                        {{ $t('Add Product') }}
                    </button>
                </div>
            </div>

            <Lists
                :products="products.data"
                @edit-product="editProduct"
                @duplicate-product="openDuplicateProduct"
                @delete-product="destroyProduct"
            />

            <Pagination :links="products.links"/>
        </div>
    </single-white-label>

    <CreateModal
        ref="productCreationComponent"
        :products="whiteLabelProducts"
        :sku-prefix="skuPrefix"
        :save-button-text="'Save and Publish'"
        @saved="onProductSaved"
        @close="onModalClosed"
    />

    <EditModal
        ref="productEditComponent"
        :product="selectedPlan"
        :show="showEditModal"
        @saved="onProductSaved"
        @close="onModalClosed"
    />
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import {ref} from "vue";
import SelectorTitleDropdown from "../Components/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import { useI18n } from "vue-i18n";
import CreateModal from "@/Pages/WhiteLabel/Product/Components/CreateModal.vue";
import EditModal from "@/Pages/WhiteLabel/Product/Components/EditModal.vue";
import Lists from "@/Pages/WhiteLabel/Product/Components/Lists.vue";

const { t } = useI18n();
const selectedPlan = ref(null);
const isEditMode = ref(false);
const productId = ref(null);
const showEditModal = ref(false);

const props = defineProps({
    products: Object,
    whiteLabelProducts: Object,
    skuPrefix: String,
    sort_by_status: String
});

const sortByStatus = ref(props.sort_by_status ?? "Sort By Status")
let serverType = ref("general");

const productCreationComponent = ref(null);
const productEditComponent = ref(null);

const addNewProduct = () => {
    serverType.value = 'general';
    isEditMode.value = false;
    selectedPlan.value = null;
    productId.value = null;
    productCreationComponent.value.showFirstModal();
}

const editProduct = (product) => {
    if (product.description){
        const descriptions = product.description.trim().split('\n');
        descriptions.forEach(description => {
            if (description.includes('RAM')) {
                product.memory = description.replace('RAM -', '').trim();
            } else if (description.includes('SSD')) {
                product.disk = description.replace('SSD -', '').trim();
            } else if (description.includes('vCPU')) {
                product.cpu = description.replace('vCPU -', '').trim();
            } else if (description.includes('Bandwidth')) {
                product.bandwidth = description.replace('Bandwidth -', '').trim();
            }
        });
    }

    // Set up for edit modal
    selectedPlan.value = product;
    showEditModal.value = true;
}

const openDuplicateProduct = (product) => {
    // if the product contains depended id can't be duplicated
    if (product.depends_on_product_id) {
        useFlash().error('A Depended product can not be duplicated. It will be automatically duplicate while duplicating the main product.');
        return;
    }

    // Process the product description
    if (product.description){
        const descriptions = product.description.trim().split('\n');
        descriptions.forEach(description => {
            if (description.includes('RAM')) {
                product.memory = description.replace('RAM -', '').trim();
            } else if (description.includes('SSD')) {
                product.disk = description.replace('SSD -', '').trim();
            } else if (description.includes('vCPU')) {
                product.cpu = description.replace('vCPU -', '').trim();
            } else if (description.includes('Bandwidth')) {
                product.bandwidth = description.replace('Bandwidth -', '').trim();
            }
        });
    }

    // Duplicate the product directly
    Inertia.post(route('white-label.products.duplicate', {product: product.id}), {}, {
        preserveScroll: true,
        onSuccess: () => {
            useFlash().success('Product duplicated successfully');
            Inertia.reload();
        },
        onError: (errors) => {
            useFlash().error('Failed to duplicate product');
        }
    });
}

const destroyProduct = (product) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure?'),
        text: t("You won't be able to revert this!"),
        btn_text: t('Yes, Remove!'),
    }, () => {
        Inertia.delete(route('white-label.products.delete', [product.id]), {
            preserveScroll: true,
            onSuccess: (page) => {
                if (page?.props?.jetstream?.flash?.type !== 'error') {
                    // Use the flash message from the backend response
                    const flashMessage = page?.props?.jetstream?.flash?.message || 'Product Removed Successfully';
                    useFlash().success(flashMessage);
                } else {
                    // Use the default success message
                    useFlash().error(page?.props?.jetstream?.flash?.message);
                }
            }
        })
    });
}

const onSortByChanged = (status) => {
    sortByStatus.value = status;
    Inertia.get(route("white-label.products.index"), {
        sort_by_status: sortByStatus.value
    });
}



const onProductSaved = () => {
    isEditMode.value = false;
    productId.value = null;
    selectedPlan.value = null;
    Inertia.reload();
}

const onModalClosed = () => {
    isEditMode.value = false;
    productId.value = null;
    selectedPlan.value = null;
    showEditModal.value = false;
}


</script>

<style scoped>

</style>

