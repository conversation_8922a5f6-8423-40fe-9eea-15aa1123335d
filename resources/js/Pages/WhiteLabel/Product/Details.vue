<template>
    <single-white-label active="Products">
        <Head title="Product Details"/>
        <div class="h-full flex flex-col w-full divide-y-1 divide-light dark:divide-mode-base">
            <div class="flex items-center px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light rounded-t-lg gap-x-2 wide-mobile:gap-x-1">
                <Link :href="route('white-label.products.index')" class="flex items-center gap-x-2">
                    <span class="h-7 aspect-square shrink-0 rounded inline-flex items-center justify-center bg-light dark:bg-mode-base text-xxs text-dark dark:text-white">
                        <i class="xcloud xc-angle_left"></i>
                    </span>
                    <h4 class="text-2xl wide-mobile:text-xl font-medium leading-tight text-secondary-full dark:text-white">
                        {{ $t('Product Details') }}
                    </h4>
                </Link>
            </div>
            <div class="flex items-center p-8 wide-mobile:p-5 bg-white dark:bg-mode-light rounded-b-lg">
                <div class="flex flex-col text-dark dark:text-white gap-8 wide-mobile:gap-5 w-full">
                    <div class="flex flex-col gap-4 wide-mobile:gap-2 w-full">
                        <div class="flex items-center flex-wrap gap-2 wide-mobile:gap-1">
                            <div class="flex flex-col gap-2 wide-mobile:gap-1">
                                <h4 class="text-lg font-medium leading-none">{{ $t('Product Information') }}</h4>
                                <p class="text-xs leading-none text-secondary-full dark:text-mode-secondary-dark">{{ $t('Some basic information is shared over here') }}</p>
                            </div>
                            <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
                                <button @click.prevent="editProduct(product)" class="inline-flex items-center justify-center rounded gap-2 border border-primary-light shadow-none min-h-10 px-4 bg-transparent text-sm font-medium text-primary-light focus:outline-0">
                                    <span class="inline-flex items-center text-xs">
                                        <i class="xcloud xc-edit"></i>
                                    </span>
                                    {{ $t('Edit Product') }}
                                </button>
                            </div>
                        </div>
                        <div class="flex flex-col bg-light dark:bg-mode-base">
                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Plan Name') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.title }}</h4>
                            </div>
                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Type') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.type }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Renewal Type') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.renewal_type }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('SKU') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.sku }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Price') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">${{ product?.price }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Site Limit') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.limit_model_quantity }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Checkout URL') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">
                                    <a :href="product?.checkout_url" target="_blank" class="underline">
                                        {{ product?.checkout_url }}
                                    </a>
                                </h4>
                                <div class="ml-3">
                                    <CopyButton position-class="left" :content="product?.checkout_url"></CopyButton>
                                </div>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Status') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">
                                    <span v-if="product?.is_active" class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-success-full/10 text-xs leading-none text-success-full focus:outline-0">
                                        <span>Active</span>
                                    </span>
                                    <span v-else class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-danger text-xs leading-none text-white focus:outline-0">
                                        <span>Inactive</span>
                                    </span>
                                </h4>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col gap-4 wide-mobile:gap-2 w-full">
                        <div class="flex items-center flex-wrap gap-2 wide-mobile:gap-1">
                            <div class="flex flex-col gap-2 wide-mobile:gap-1">
                                <h4 class="text-lg font-medium leading-none">{{ $t('Invoices of Product') }}</h4>
                                <p class="text-xs leading-none text-secondary-full dark:text-mode-secondary-dark">{{ $t('Here you can check all your previous invoices') }}</p>
                            </div>
                            <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
                                <SelectorTitleDropdown :selected-item="invoiceDateFilter">
                                    <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                                        <div class="p-2px">
                                            <SelectItem
                                                title="All Invoices"
                                                :is-active="invoiceDateFilter === 'All Invoices'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="This Month"
                                                :is-active="invoiceDateFilter === 'This Month'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Last Month"
                                                :is-active="invoiceDateFilter === 'Last Month'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Last 6 Months"
                                                :is-active="invoiceDateFilter === 'Last 6 Months'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Last 1 Year"
                                                :is-active="invoiceDateFilter === 'Last 1 Year'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                        </div>
                                    </div>
                                </SelectorTitleDropdown>

                                <SelectorTitleDropdown :selected-item="invoiceStatusFilter" custom-class="!w-52">
                                    <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                                        <div class="p-2px">
                                            <SelectItem
                                                title="Paid Invoices"
                                                :is-active="invoiceStatusFilter === 'Paid Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Unpaid Invoices"
                                                :is-active="invoiceStatusFilter === 'Unpaid Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Failed Invoices"
                                                :is-active="invoiceStatusFilter === 'Failed Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="All Invoices"
                                                :is-active="invoiceStatusFilter === 'All Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                        </div>
                                    </div>
                                </SelectorTitleDropdown>
                            </div>
                        </div>
                        <div class="flex flex-col">
                            <div class="inline-block min-w-full">
                                <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                                    <thead>
                                    <tr>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Date') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Invoice No') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Amount') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Title') }}</th>
                                        <!--<th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">Server</th>-->
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Status') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Actions') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-mode-base">
                                    <tr v-for="invoice in invoices?.data" :key="invoice?.id" class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">{{ invoice?.date }}</td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                                            {{ invoice?.invoice_number ?? invoice?.reference_no }}
                                            <CopyButton
                                                position-class=""
                                                :content="invoice?.invoice_number ?? invoice?.reference_number"
                                                align="top"
                                                color="primary"
                                                :hideCopyText="true"
                                            />
                                        </td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">${{ invoice?.amount }}</td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">{{ invoice?.title }}</td>
                                        <!--<td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark"></td>-->
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                            <span v-if="invoice?.status === 'refunded' || invoice?.status === 'paid'"
                                                  class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full">
                                              {{ invoice?.status_readable }}
                                            </span>
                                            <span
                                                v-else-if="invoice?.status === 'cancelled' || invoice?.status === 'failed' || invoice?.status === 'payment_failed'"
                                                class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10 group-hover:bg-danger">
                                                  {{ invoice?.status_readable }}
                                            </span>
                                            <span v-else
                                                  class="text-warning group-hover:text-white px-6 py-1.5 rounded-3xl bg-warning/10 group-hover:bg-warning">
                                                  {{ invoice?.status_readable }}
                                            </span>
                                        </td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                            <div class="flex items-center justify-center">
                                                <InvoiceActions
                                                    :invoice="invoice"
                                                    :isStaging="isStaging"
                                                    :isAdmin="isAdmin"
                                                    :isImpersonating="isImpersonating"
                                                    position="right"
                                                    :show_border="true"
                                                    @onChangeDropDown="() => {}"
                                                />
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <Pagination :links="invoices?.links"/>
                    </div>
                </div>
            </div>
        </div>
    </single-white-label>

    <EditModal
        :product="product"
        :show="showEditModal"
        @close="closeEditModal"
        @saved="onProductSaved"
    />
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {Link} from "@inertiajs/inertia-vue3";
import {ref, onMounted} from "vue";
import Pagination from "@/Shared/Pagination.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import SelectorTitleDropdown from "@/Pages/WhiteLabel/Components/SelectorTitleDropdown.vue";
import InvoiceActions from "@/Pages/WhiteLabel/Components/InvoiceActions.vue";
import EditModal from "@/Pages/WhiteLabel/Product/Components/EditModal.vue";
import {Inertia} from "@inertiajs/inertia";

const props = defineProps({
    product: Object,
    invoices: Object,
    invoiceDateFilter:{
        type: String,
        default: 'All Invoices'
    },
    invoiceStatusFilter:{
        type: String,
        default: 'Paid Invoices'
    },
    isStaging: {
        type: Boolean,
        default: false
    },
    isAdmin: {
        type: Boolean,
        default: false
    },
    isImpersonating: {
        type: Boolean,
        default: false
    },
});

const showEditModal = ref(false);
const invoiceDateFilter = ref(props?.invoiceDateFilter);
const invoiceStatusFilter = ref(props?.invoiceStatusFilter);

// Initialize product data when component mounts
onMounted(() => {
    console.log('Details component mounted with product:', props.product);
});

const editProduct = (product) => {
    console.log('Edit product called with:', product);

    // Parse product description to extract specs for display in the modal
    if (product.description) {
        const descriptions = product.description.trim().split('\n');
        descriptions.forEach(description => {
            if (description.includes('RAM')) {
                product.memory = description.replace('RAM -', '').trim();
            } else if (description.includes('SSD')) {
                product.disk = description.replace('SSD -', '').trim();
            } else if (description.includes('vCPU')) {
                product.cpu = description.replace('vCPU -', '').trim();
            } else if (description.includes('Bandwidth')) {
                product.bandwidth = description.replace('Bandwidth -', '').trim();
            }
        });
    }

    // Make sure we're using the full product object
    console.log('Opening edit modal with product:', props.product);
    showEditModal.value = true;
}

const closeEditModal = () => {
    showEditModal.value = false;
}

const onProductSaved = () => {
    showEditModal.value = false;
    Inertia.reload();
}

const onInvoiceDateFilterChanged = (filter) => {
    invoiceDateFilter.value = filter;
    loadFilteredData();
}

const onInvoiceStatusFilterChanged = (filter) => {
    invoiceStatusFilter.value = filter;
    loadFilteredData();
}

function loadFilteredData(type = 'created_at'){
    Inertia.get(route("white-label.products.details", props?.product?.id), {
        invoiceDateFilter: invoiceDateFilter.value,
        invoiceStatusFilter: invoiceStatusFilter.value,
        filterType: type,
    });
}


</script>

<style scoped>

</style>
