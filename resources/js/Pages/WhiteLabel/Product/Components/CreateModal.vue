<template>
    <Modal
        @close="closeModalOne"
        :show="addProductModal"
        :footerButton="true"
        :title="isEditMode ? $t('Edit Product') : $t('Create New Product')"
        :widthClass="'max-w-244'"
    >
        <div class="flex flex-col gap-5">
            <div class="flex flex-col gap-3">
                <h3 class="text-lg font-normal text-dark dark:text-white leading-none">
                    Select Server Plan at xCloud
                </h3>
                <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-4">
                    <label class="w-full">
                        <input type="radio" v-model="serverType" name="product-type" id="general" value="general" class="peer hidden">
                        <span
                            class="bg-light dark:bg-mode-base flex gap-6 items-start w-full rounded-lg border-1 border-solid border-transparent peer-checked:border-primary-light p-6 cursor-pointer relative after:absolute after:top-3 after:right-3 after:h-4 after:aspect-square after:shrink-0 after:rounded-full after:inline-flex after:justify-center after:items-center after:font-xc after:content-['\e927'] after:text-xxxs after:text-transparent after:border-2 after:border-secondary-light dark:after:border-dark peer-checked:after:bg-primary-light peer-checked:after:border-primary-light peer-checked:after:text-white dark:peer-checked:after:text-mode-base"
                        >
                            <span class="w-16 aspect-square shrink-0 rounded-lg bg-white dark:bg-mode-light inline-flex items-center justify-center">
                                <img
                                    :src="asset('img/png/cloud-storage 1.png')"
                                    alt="xcloud_logo"
                                    class="w-12 h-auto"
                                />
                            </span>
                            <span class="flex flex-col gap-2">
                                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                    General
                                </h5>
                                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                    {{ $t('Cost-effective servers powered by Intel CPUs and regular SSDs.') }}
                                </p>
                            </span>
                        </span>
                    </label>
                    <label class="w-full">
                        <input type="radio" v-model="serverType" name="product-type" id="premium" value="premium" class="peer hidden">
                        <span
                            class="bg-light dark:bg-mode-base flex gap-6 items-start w-full rounded-lg border-1 border-solid border-transparent peer-checked:border-primary-light p-6 cursor-pointer relative after:absolute after:top-3 after:right-3 after:h-4 after:aspect-square after:shrink-0 after:rounded-full after:inline-flex after:justify-center after:items-center after:font-xc after:content-['\e927'] after:text-xxxs after:text-transparent after:border-2 after:border-secondary-light dark:after:border-dark peer-checked:after:bg-primary-light peer-checked:after:border-primary-light peer-checked:after:text-white dark:peer-checked:after:text-mode-base"
                        >
                            <span class="w-16 aspect-square shrink-0 rounded-lg bg-white dark:bg-mode-light inline-flex items-center justify-center">
                                <img
                                    :src="asset('img/png/cloud-server 2.png')"
                                    alt="xcloud_logo"
                                    class="w-12 h-auto"
                                />
                            </span>
                            <span class="flex flex-col gap-2">
                                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                    Premium
                                </h5>
                                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                    {{ $t('Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.') }}
                                </p>
                            </span>
                        </span>
                    </label>
                </div>
            </div>
            <div class="flex flex-col gap-3">
                <suggestion
                    v-if="isSuggestion"
                    class="mb-0 pl-2"
                    message="We recommend choosing the 2 GB server type based on your requirements."
                    :light-mode="false"
                />

                <h3 class="text-lg font-normal text-dark dark:text-white leading-none">
                    Select Server Size:
                </h3>

                <div class="grid grid-cols-auto-56 gap-4">
                    <label
                        v-for="(product, key) in products"
                        :key="key"
                        class="w-full"
                    >
                        <input
                            type="radio"
                            v-model="selectedPlan"
                            :id="'basic' + key"
                            class="hidden peer"
                            :value="product"
                            :name="'server-plan'"
                        />
                        <div
                            class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light peer-checked:!border-primary-light"
                        >
                            <div class="flex items-center gap-2 px-4 py-3 justify-start bg-light dark:bg-dark">
                                <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ product?.title }}</h4>
                                <tooltip v-if="product?.tooltip" :title="product?.tooltip" align="bottom">
                                    <button
                                        title="Info Here"
                                        class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                                    >
                                        <i class="xcloud xc-info"></i>
                                    </button>
                                </tooltip>
                                <span class="ml-auto text-base text-primary-light" v-if="selectedPlan?.id === product?.id">
                                    <i class="xcloud xc-checkbox"></i>
                                </span>
                            </div>
                            <div class="px-4 py-3">
                                <ul class="list-none marker:text-light flex flex-col gap-2 text-sm">
                                    <li class="text-secondary-full dark:text-mode-secondary-light" v-if="product?.description.memory"> RAM -
                                        <span class="text-dark dark:text-white">
                                            {{ product?.description.memory }}
                                        </span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light" v-if="product?.description.disk"> SSD -
                                        <span class="text-dark dark:text-white">{{ product?.description.disk }} GB </span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light" v-if="product?.description.cpu"> vCPU -
                                        <span class="text-dark dark:text-white">{{ product?.description.cpu }}</span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light" v-if="product?.description.bandwidth"> Bandwidth -
                                        <span class="text-dark dark:text-white">{{ product?.description.bandwidth }}</span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light" v-if="product?.description.raw"> Details -
                                        <span class="text-dark dark:text-white">{{ product?.description.raw }}</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="flex items-center gap-3 px-4 py-3 justify-start">
                                <h4 class="text-base leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ product?.price }}/ <small class="text-secondary-full dark:text-mode-secondary-light">month</small></h4>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex justify-end">
                <button @click.prevent="addNewProductStepTwo" class="inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 min-h-10 px-6 bg-primary-light text-base font-medium text-white focus:outline-0">
                    Next
                </button>
            </div>
        </template>
    </Modal>

    <!-- Second Modal for customizing product -->
    <Modal
        @close="closeModalTwo"
        :show="addStepTwoProductModal"
        :footerButton="true"
        :title="isEditMode ? $t('Edit Product') : $t('Create New Product')"
        :widthClass="'max-w-244'"
    >
        <div class="flex flex-col gap-6">
            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Selected Plan
                    </h3>
                    <button
                        @click.prevent="backToStepOne"
                        class="ml-auto text-xs leading-tight text-primary-light hover:underline underline-offset-2"
                    >
                        Change Plan
                    </button>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ selectedPlan?.title }}</h4>
                        <tooltip :title="selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                Includes Up to RAM - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                SSD - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                vCPU - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                Bandwidth - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Customize Package
                    </h3>
                </div>

                <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
                    <text-input
                        v-model="form.plan_name"
                        id="plan_name"
                        placeholder="My Server"
                        label="Plan Name"/>
                    <text-input
                        type="number"
                        v-model="form.custom_price"
                        id="custom_price"
                        :placeholder="minPrice.toFixed(2)"
                        :error="form.errors.custom_price"
                        label="Price/Month"/>
                    <text-input
                        v-model="form.sku"
                        id="sku"
                        placeholder=""
                        :error="form.errors.sku"
                        label="SKU"
                        @input="changeSku"/>
                    <text-input
                        type="number"
                        v-model="form.limit_model_quantity"
                        id="limit_model_quantity"
                        placeholder="Enter site limit (leave empty for unlimited)"
                        :error="form.errors.limit_model_quantity"
                        label="Site Limit"/>
                </div>
            </div>

            <div>
                <suggestion
                    type="slot"
                    :light-mode="false"
                    class="mb-0 pl-2"
                >
                    <a href="https://stripe.com/pricing" target="_blank" class="underline cursor-pointer">Stripe</a>
                    will deduct a 3%-7% fee per sale. Your approximate profit for this sale is ${{ calculateProfit(selectedPlan.price, form.custom_price, 3) }}-${{ calculateProfit(selectedPlan.price, form.custom_price, 7) }}
                </suggestion>
            </div>

            <div
                class="flex gap-6 items-center justify-start"
            >
                <div class="flex gap-1 items-center">
                    <h5 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Active Package
                    </h5>
                    <tooltip title="Toggle on to active the package">
                        <button
                            title="Info Here"
                            class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-dark dark:text-white text-sm"
                        >
                            <i class="xcloud xc-info"></i>
                        </button>
                    </tooltip>
                </div>

                <label class="inline-flex outline-none">
                    <input
                        v-model="form.is_active"
                        :checked="form.is_active"
                        class="hidden peer"
                        type="checkbox"
                        :disabled="false"
                    />
                    <span
                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0
                        before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none
                        before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1
                        before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white
                        cursor-pointer"
                    >
                    </span>
                </label>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Preview Plan
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ form.plan_name || selectedPlan?.title }}</h4>
                        <tooltip :title="form.plan_name || selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ form.custom_price || selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                Includes Up to RAM - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                SSD - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                vCPU - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                Bandwidth - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex items-center gap-4">
                <button @click.prevent="backToStepOne" class="inline-flex items-center justify-center rounded-lg border-1 border-primary-light shadow-none gap-2 min-h-10 px-6 bg-transparent text-base font-medium text-primary-light hover:bg-primary-light hover:text-white transition duration-75 ease-in-out focus:outline-0">
                    Back
                </button>
                <div class="flex items-center gap-4 ml-auto">
                    <button @click.prevent="saveProduct" :disabled="isPublishRunning" class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2 min-h-10 px-6 bg-success-light text-base font-medium text-white focus:outline-0">
                        <i v-if="isPublishRunning"
                           class="xcloud xc-verify_dns text-white animate-spin mr-1">
                        </i>{{ saveButtonText }}
                    </button>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import {computed, nextTick, onMounted, ref, watch} from "vue";
import {useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";
import Modal from "@/Shared/Modal.vue";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import {Inertia} from "@inertiajs/inertia";

const props = defineProps({
    // products: {
    //     type: Object,
    //     default: null
    // },
    skuPrefix: {
        type: String,
        default: 'whl-'
    },
    productToEdit: {
        type: Object,
        default: null
    },
    saveEndpoint: {
        type: String,
        default: '/white-label/products/store'
    },
    saveButtonText: {
        type: String,
        default: 'Save and Publish'
    },
    onSuccess: {
        type: Function,
        default: () => {}
    }
});

const products = ref(null); // props.products

onMounted(() => {
    fetchProducts();
});

const fetchProducts = async () => {
    try {
        const { data } = await axios.get(route('api.white-label.products.index', {
            per_page: 100,
            type: serverType.value
        }));
        products.value = data.data;
    } catch (error) {
        useFlash().error('Failed to fetch products');
    }
};

const emit = defineEmits(['close', 'saved']);

// State variables
const addProductModal = ref(false);
const addStepTwoProductModal = ref(false);
const selectedPlan = ref(null);
const isSuggestion = ref(false);
const priceError = ref(false);
const isPublishRunning = ref(false);
const isEditMode = computed(() => !!props.productToEdit);

// Server type selection
let serverType = ref("general");
const whiteLabelAllProducts = computed(() => {
    return props.products[serverType.value];
});

// Product ID and minimum price
const product_id = computed(() => selectedPlan.value?.id || null);
const minPrice = ref(0);

// Form setup
let form = useForm({
    plan_name: props.productToEdit?.title || "",
    custom_price: props.productToEdit?.price || null,
    sku: props.productToEdit?.sku || "",
    server_type: serverType.value,
    product_id: product_id.value,
    is_active: props.productToEdit?.is_active ?? true,
    limit_model_quantity: props.productToEdit?.limit_model_quantity || null
});

// Generate SKU based on selected plan
const generateSku = computed(() => {
    if (!selectedPlan.value) {
        return '';
    }

    const parts = selectedPlan.value?.sku?.split('-');

    if (parts) {
        parts[0] = props.skuPrefix;
        return parts.join('-');
    }

    return '';
});

// Methods for modal navigation
const showFirstModal = () => {
    addProductModal.value = true;
};

const backToStepOne = () => {
    form.custom_price = null;
    addStepTwoProductModal.value = false;
    addProductModal.value = true;
};

const addNewProductStepTwo = () => {
    if (selectedPlan.value) {
        let minimumPrice = selectedPlan.value?.price * 1.1;
        minPrice.value = parseFloat(minimumPrice.toFixed(2));
        addProductModal.value = false;
        addStepTwoProductModal.value = true;
    } else {
        useFlash().error('Please select a Hosting Plan');
    }
};

// Save product
const saveProduct = () => {
    if (priceError.value) {
        return;
    }

    isPublishRunning.value = true;

    const endpoint = isEditMode.value && props.productToEdit?.id
        ? `${props.saveEndpoint}/${props.productToEdit.id}`
        : props.saveEndpoint;

    form.post(endpoint, {
        preserveScroll: true,
        onSuccess: () => {
            form.plan_name = '';
            addProductModal.value = false;
            addStepTwoProductModal.value = false;
            isPublishRunning.value = false;
            resetForm();
            emit('saved');
            props.onSuccess();
        },
        onError: (errors) => {
            isPublishRunning.value = false;
        },
        onFinish: () => {
            isPublishRunning.value = false;
        }
    });
};

// Calculate profit
function calculateProfit(previousPrice, newPrice, feePercentage) {
    if (!newPrice) return '0.00';
    const currentPrice = newPrice * (1 - feePercentage / 100);
    return (currentPrice - previousPrice).toFixed(2);
}

// Reset form fields
const resetForm = () => {
    form.plan_name = "";
    form.custom_price = null;
    form.sku = generateSku.value;
    form.limit_model_quantity = null;
};

// Clear SKU error
const changeSku = () => {
    form.errors.sku = "";
};

// Close modals
const closeModalOne = () => {
    addProductModal.value = false;
    resetForm();
    emit('close');
};

const closeModalTwo = () => {
    addStepTwoProductModal.value = false;
    resetForm();
    emit('close');
};

// Watch for changes
watch(selectedPlan, (newVal) => {

    console.log(selectedPlan);

    isSuggestion.value = newVal && newVal?.memory === '1 GB';
    form.product_id = newVal?.id || null;
});

watch(serverType, (newVal) => {
    // Reset selected plan when server type changes
    selectedPlan.value = null;

    // Update form server_type
    form.server_type = newVal;

    fetchProducts();
});

watch(generateSku, (newVal) => {
    form.sku = newVal || null;
});

watch(() => form.custom_price, (newPrice) => {
    if (newPrice && parseFloat(newPrice) < minPrice.value) {
        priceError.value = true;
        form.errors.custom_price = `Price must be at least $${minPrice.value}`;
    } else {
        priceError.value = false;
        form.errors.custom_price = '';
    }
});

// Expose methods to parent component
defineExpose({
    showFirstModal
});
</script>
