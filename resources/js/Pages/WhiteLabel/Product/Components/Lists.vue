<template>
    <div class="overflow-x-auto flex flex-col">
        <div class="inline-block min-w-full">
            <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                <thead>
                <tr>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Plan Name') }}</th>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Type') }}</th>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Renewal Type') }}</th>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('SKU') }}</th>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Buying Price') }}</th>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Selling Price') }}</th>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Site Limit') }}</th>
                    <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Status') }}</th>
                    <th scope="col" class="py-3 px-6 text-right text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Actions') }}</th>
                </tr>
                </thead>
                <tbody class="bg-white dark:bg-mode-base">
                <tr v-for="product in products" :key="product.id"
                    class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                        <Link :href="route('white-label.products.details', product.id)">
                            {{ product?.title }}
                        </Link>
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                        {{ product?.type }}
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                        {{ product?.renewal_type }}
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                        {{ product?.sku }}
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                        ${{ product?.source?.price }}
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                        ${{ product?.price }}
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                        {{ product?.limit_model_quantity }}
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                    <span v-if="product?.is_active" class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-success-full/10 text-xs leading-none text-success-full focus:outline-0">
                                        <span>Active</span>
                                    </span>
                        <span v-else class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-danger text-xs leading-none text-white focus:outline-0">
                                        <span>Inactive</span>
                                    </span>
                    </td>
                    <td class="whitespace-nowrap w-14 px-30px py-20px text-right text-sm font-normal text-dark dark:text-white">
                                    <span class="flex gap-30px">
                                        <button @click.prevent="emit('duplicate-product', product)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-copy"></i>
                                            </span>
                                        </button>
                                        <Link :href="route('white-label.products.details', product.id)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-maximize"></i>
                                            </span>
                                         </Link>
                                        <button @click.prevent="emit('edit-product', product)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-edit"></i>
                                            </span>
                                        </button>
                                        <button @click.prevent="emit('delete-product', product)" class="relative group cursor-pointer mt-1.5">
                                            <span class="text-sm flex text-secondary-light dark:group-hover:text-white group-hover:text-dark">
                                                <i class="xcloud xc-delete"></i>
                                            </span>
                                        </button>
                                    </span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>
<script setup>

defineProps({
    products: Object
})

const emit = defineEmits([
    'edit-product',
    'duplicate-product',
    'delete-product'
])

</script>
