<template>
    <Modal
        @close="closeModal"
        :show="show"
        :footerButton="true"
        :title="$t('Edit Product')"
        :widthClass="'max-w-244'"
    >
        <div class="flex flex-col gap-6">
            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Source Product') }}
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ selectedPlan?.source?.title }}</h4>
                        <tooltip :title="selectedPlan?.source?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                        <span class="ml-auto text-sm text-success-light inline-flex items-center">
                            <i class="xcloud xc-tick-o"></i>
                        </span>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ selectedPlan?.source?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">{{ $t('month') }}</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>

            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Customize Package') }}
                    </h3>
                </div>
                <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
                    <text-input
                        v-model="form.plan_name"
                        id="plan_name"
                        :placeholder="$t('My Server')"
                        :label="$t('Plan Name')"/>
                    <text-input
                        type="number"
                        v-model="form.custom_price"
                        id="custom_price"
                        :placeholder="minPrice.toFixed(2)"
                        :error="form.errors.custom_price"
                        :label="$t('Price/Month')"/>
                    <text-input
                        v-model="form.sku"
                        id="sku"
                        :placeholder="$t('XCPU110')"
                        :error="form.errors.sku"
                        :label="$t('SKU')"
                        @input="changeSku"/>
                    <text-input
                        type="number"
                        v-model="form.limit_model_quantity"
                        id="limit_model_quantity"
                        :placeholder="$t('Enter site limit (leave empty for unlimited)')"
                        :error="form.errors.limit_model_quantity"
                        :label="$t('Site Limit')"/>
                </div>
            </div>

            <div>
                <suggestion
                    type="slot"
                    :light-mode="false"
                    class="mb-0 pl-2"
                >
                    <a href="https://stripe.com/pricing" target="_blank" class="underline cursor-pointer">{{ $t('Stripe') }}</a>
                    {{ $t('will deduct a 3%-7% fee per sale. Your approximate profit for this sale is') }} ${{ calculateProfit(basePrice, form.custom_price, 3) }}-${{ calculateProfit(basePrice, form.custom_price, 7) }}
                </suggestion>
            </div>

            <div
                class="flex gap-6 items-center justify-start"
            >
                <div class="flex gap-1 items-center">
                    <h5 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Active Package') }}
                    </h5>
                    <tooltip title="Toggle on to active the package">
                        <button
                            title="Info Here"
                            class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-dark dark:text-white text-sm"
                        >
                            <i class="xcloud xc-info"></i>
                        </button>
                    </tooltip>
                </div>
                <label class="inline-flex outline-none">
                    <input
                        v-model="form.is_active"
                        :checked="form.is_active"
                        class="hidden peer"
                        type="checkbox"
                        :disabled="false"
                    />
                    <span
                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0
                        before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none
                        before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1
                        before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white
                        cursor-pointer"
                    >
                    </span>
                </label>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Preview Plan') }}
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ form.plan_name || selectedPlan?.title }}</h4>
                        <tooltip :title="form.plan_name || selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ form.custom_price || selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-4 ml-auto">
                    <button @click.prevent="saveProduct" :disabled="isPublishRunning" class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2 min-h-10 px-6 bg-success-light text-base font-medium text-white focus:outline-0">
                        <i v-if="isPublishRunning"
                           class="xcloud xc-verify_dns text-white animate-spin mr-1">
                        </i>{{ $t('Save and Publish') }}
                    </button>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import Modal from "@/Shared/Modal.vue";
import {computed, ref, watch} from "vue";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import {Inertia} from "@inertiajs/inertia";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";

const props = defineProps({
    product: Object,
    show: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close', 'saved']);

const selectedPlan = ref(null);
const productId = ref(null);
const priceError = ref(false);
const isPublishRunning = ref(false);
const basePrice = ref(0);
const serverType = ref('general');
const minPrice = ref(0);

let form = useForm({
    plan_name: "",
    custom_price: null,
    sku: "",
    server_type: 'general',
    product_id: null,
    is_active: true,
    limit_model_quantity: null
});

const saveProduct = () => {
    if (priceError.value) {
        return;
    }

    isPublishRunning.value = true;

    // Make sure all required fields are included
    const formData = {
        plan_name: form.plan_name,
        custom_price: form.custom_price,
        sku: form.sku,
        is_active: form.is_active,
        limit_model_quantity: form.limit_model_quantity,
        product_id: productId.value
    };

    console.log('Submitting form with data:', formData);

    form.post(route('white-label.products.update', {product: productId.value}), {
        preserveScroll: true,
        onSuccess: (response) => {
            console.log('Success response:', response);
            emit('saved');
            emit('close');
            serverType.value = 'general';
            isPublishRunning.value = false;
            resetForm();
            useFlash().success('Product updated successfully');
            Inertia.reload();
        },
        onError: (errors) => {
            console.error('Error response:', errors);
            isPublishRunning.value = false;
            useFlash().error('Failed to update product: ' + (errors.message || 'Unknown error'));
        }
    });
}

const closeModal = () => {
    emit('close');
    resetForm();
}

const resetForm = () => {
    form.plan_name = "";
    form.custom_price = null;
    form.sku = "";
    form.limit_model_quantity = null;
}

const changeSku = () => {
    form.errors.sku = ""
}

function calculateProfit(previousPrice, newPrice, feePercentage) {
    if (!newPrice) return '0.00';
    const currentPrice = newPrice * (1 - feePercentage / 100);
    return (currentPrice - previousPrice).toFixed(2);
}

// Define the initializeProduct function before using it in the watch
const initializeProduct = (product) => {
    console.log('Initializing product:', product);
    if (!product) {
        console.warn('Product is null or undefined');
        return;
    }

    // Parse product description to extract specs
    if (product.description) {
        const descriptions = product.description.trim().split('\n');
        descriptions.forEach(description => {
            if (description.includes('RAM')) {
                product.memory = description.replace('RAM -', '').trim();
            } else if (description.includes('SSD')) {
                product.disk = description.replace('SSD -', '').trim();
            } else if (description.includes('vCPU')) {
                product.cpu = description.replace('vCPU -', '').trim();
            } else if (description.includes('Bandwidth')) {
                product.bandwidth = description.replace('Bandwidth -', '').trim();
            }
        });
    }

    productId.value = product.id;
    selectedPlan.value = product;
    serverType.value = product.type || 'general';
    form.plan_name = product.title || '';
    form.custom_price = product.price || 0;
    form.is_active = product.is_active !== undefined ? product.is_active : true;
    form.sku = product.sku || '';
    form.server_type = product.type || 'general';
    form.product_id = product.id;
    form.limit_model_quantity = product.limit_model_quantity;

    basePrice.value = product?.source?.price || 0;
    let minimumPrice = basePrice.value * 1.1;
    minPrice.value = parseFloat(minimumPrice.toFixed(2));

    console.log('Form initialized:', {
        productId: productId.value,
        selectedPlan: selectedPlan.value,
        serverType: serverType.value,
        form: {
            plan_name: form.plan_name,
            custom_price: form.custom_price,
            is_active: form.is_active,
            sku: form.sku,
            server_type: form.server_type,
            product_id: form.product_id,
            limit_model_quantity: form.limit_model_quantity
        },
        basePrice: basePrice.value,
        minPrice: minPrice.value
    });
};

watch(() => form.custom_price, (newPrice) => {
    if (newPrice && parseFloat(newPrice) < minPrice.value) {
        priceError.value = true;
        form.errors.custom_price = `Price must be at least $${minPrice.value}`;
    } else {
        priceError.value = false;
        form.errors.custom_price = '';
    }
});

// Watch for changes to the product prop
watch(() => props.product, (newProduct) => {
    console.log('Product prop changed:', newProduct);
    if (newProduct) {
        initializeProduct(newProduct);
    }
}, { immediate: true });

// Watch for changes to the show prop
watch(() => props.show, (isVisible) => {
    console.log('Show prop changed:', isVisible, 'product:', props.product);
    if (isVisible && props.product) {
        // Re-initialize when modal becomes visible
        initializeProduct(props.product);
    }
});
</script>
