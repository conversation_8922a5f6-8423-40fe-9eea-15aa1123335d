<template>
  <single-white-label active="Privacy Policy">
    <Head title="Privacy Policy"/>
    <div class="flex flex-col w-full divide-y-1 divide-light dark:divide-mode-base h-full">
      <div class="flex items-center px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light rounded-t-lg gap-x-2 wide-mobile:gap-x-1">
        <h4 class="text-2xl wide-mobile:text-xl font-medium leading-tight text-secondary-full dark:text-white">
          {{ $t('Privacy Policy Settings') }}
        </h4>
      </div>

      <div class="flex flex-col px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light rounded-b-lg gap-x-2 wide-mobile:gap-x-1 gap-y-6">
          <div>
              <span class="inline-flex w-full mt-5 gap-1 p-1 bg-white dark:bg-mode-base border-1 border-secondary-light/50
                      dark:border-mode-base rounded-md wide-mobile:flex-col mb-30px tablet:mb-25px wide-mobile:mb-20px">
                  <button
                      type="button"
                      @click="form.privacy_policy_type = 'default'; form.privacy_policy_url = null"
                      :class="form.privacy_policy_type === 'default' ? 'dark:bg-dark text-white bg-primary-dark' : 'text-dark dark:text-white bg-transparent'"
                      class="inline-flex grow items-center min-h-50px px-6 mobile:p-4 py-1 rounded-md
                          text-base font-normal hover:bg-primary-dark dark:hover:bg-dark
                          hover:text-white focus:outline-none text-center justify-center wide-mobile:justify-start
                          wide-mobile:shadow wide-mobile:shadow-dark/10 hover:shadow-none"
                  >
                    {{ $t('Default Privacy Policy') }}
                  </button>

                  <button
                      @click="form.privacy_policy_type = 'custom'"
                      type="button"
                      :class="{
                           'dark:bg-dark text-white bg-primary-dark' : form.privacy_policy_type === 'custom',
                           'text-dark dark:text-white bg-transparent': form.privacy_policy_type !== 'custom',
                      }"
                      class="inline-flex grow items-center min-h-50px px-6 mobile:p-4 py-1 rounded-md
                              text-base font-normal hover:bg-primary-dark dark:hover:bg-dark
                              hover:text-white focus:outline-none text-center justify-center wide-mobile:justify-start
                              wide-mobile:shadow wide-mobile:shadow-dark/10 hover:shadow-none"
                  >
                    {{ $t('Use your own Privacy Policy') }}
                  </button>
              </span>
          </div>
          <div v-if="form.privacy_policy_type === 'default'">
              <v-ace-editor
                  v-model:value="form.privacy_policy"
                  @init="editorInit"
                  lang="html"
                  :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
                  style="height: 380px"/>
          </div>

          <div v-else>
            <text-input
                class="w-[50%]"
                v-model="form.privacy_policy_url"
                :error="form.errors.privacy_policy_url"
                :label="$t('Use your own Privacy Policy URL')"
                :placeholder="$t('https://example.com/privacy-policy')"
                :note="$t('Enter the URL of your own Privacy Policy page and make sure to add https:// in url')"
            />
          </div>

          <div class="flex items-center gap-3 ml-auto">
              <button v-if="form.privacy_policy_type === 'default'" @click.prevent="previewPrivacyPolicy" class="inline-flex items-center justify-center rounded-10px focus:outline-0 bg-transparent hover:bg-primary-dark shadow-none min-h-50px
                    mobile:min-h-40px px-25px py-2px border-1 border-primary-light hover:border-primary-dark text-base text-primary-light hover:text-white">
                  {{ $t('Preview') }}
              </button>

              <button class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2 min-h-12
                  px-6 bg-success-light text-base font-medium text-white focus:outline-0"
                      @click.prevent="submitPrivacyPolicyPageSettings"
                      :disabled="form.processing"
                      :class="{ 'opacity-50 cursor-not-allowed': form.processing }"
                      v-text="form.processing ? $t('Saving...') : $t('Save')"
              >
              </button>
          </div>
      </div>
    </div>
  </single-white-label>
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import Label from "@/Jetstream/Label.vue";
import {VAceEditor} from "vue3-ace-editor";
let navigation = useNavigationStore();
import 'ace-builds/src-noconflict/mode-powershell';
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/theme-tomorrow_night_blue';
import {useNavigationStore} from "@/stores/NavigationStore";
import {useForm} from "@inertiajs/inertia-vue3";
import {Inertia} from "@inertiajs/inertia";
import {watch} from "vue";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";

const props = defineProps({
  whiteLabel: Object
})

const getDefaultPrivacyPolicy = () => {
  return `
    **Privacy Policy for ` + props.whiteLabel.branding?.brand_name + `**

    At ` + props.whiteLabel.branding?.brand_name + `, your privacy is a priority, and we are dedicated to protecting your personal information. This Privacy Policy explains our practices for collecting, using, and safeguarding your data.

    ### About ` + props.whiteLabel.branding?.brand_name + `
    ` + props.whiteLabel.branding?.brand_name + ` provides seamless hosting and management services for WordPress and other web platforms. We integrate with various cloud and VPS providers to ensure a streamlined hosting experience for developers, agencies, and businesses.

    ### Information Collection
    We collect certain personal information ("Personal Data") from you to provide and improve our services. This may include:
    - Contact details such as your name and email address.
    - Payment information.
    - Server and account details related to your use of our hosting services.

    ### Use of Your Information
    Your personal data is used for the following purposes:
    - To manage your account and provide requested services.
    - To handle support inquiries and communications.
    - To notify you of important updates and security alerts.
    - To analyze user interactions to improve and personalize our services.
    - To comply with legal requirements.

    ### Data Retention
    We retain personal data only as long as necessary to fulfill its purpose, such as for contract performance or legal compliance. After the retention period, we delete or anonymize your data as appropriate.

    ### GDPR Compliance
    ` + props.whiteLabel.branding?.brand_name + ` follows the principles of "privacy by design" and only collects the information necessary to provide our services. For clients operating in GDPR-compliant regions, you are responsible for ensuring your own compliance with GDPR requirements. ` + props.whiteLabel.branding?.brand_name + ` does not assume liability for individual GDPR compliance obligations.

    ### Cookies and Tracking
    We use cookies to optimize your experience on our website. These may include analytics and ad cookies to understand website usage and preferences. You can control cookie usage through your browser settings.

    ### Information Security
    We implement robust security measures to protect your personal information. Access to your data is restricted to authorized personnel and protected by industry-standard safeguards.

    ### Your Rights
    Under applicable privacy laws, you have certain rights regarding your data:
    - **Access:** You may request access to the personal data we hold about you.
    - **Correction/Update:** You can update or correct any information you have provided.
    - **Deletion:** You may request deletion of your data, subject to any legal obligations we may have.
    - **Data Portability:** You can request a digital copy of your data.
    - **Withdrawal of Consent:** You can change your consent preferences by contacting us at **` + props.whiteLabel.branding?.email + `**.

    ### Content Ownership
    ` + props.whiteLabel.branding?.brand_name + ` does not claim ownership of any content you create or host with us, nor are we liable for the compliance of that content.

    ### Contact Us
    For any questions or concerns about this Privacy Policy, please reach out to us at **` + props.whiteLabel.branding?.email + `**.
  `;
}
const form = useForm({
    privacy_policy: props.whiteLabel?.privacy_policy ?? getDefaultPrivacyPolicy(),
    privacy_policy_type: props.whiteLabel?.settings?.privacy_policy?.privacy_policy_type ?? 'default',
    privacy_policy_url: props.whiteLabel?.settings?.privacy_policy?.privacy_policy_url ?? '',
});

const editorInit = (editor) => {
    const mode = 'html';
    editor.getSession().setMode(`ace/mode/${mode}`);
}

const submitPrivacyPolicyPageSettings = () => {
    form.post(route('white-label.privacy-policy.settings.update'), {
        onSuccess: (response) => {
            if(response.props.jetstream.flash?.success){
                Inertia.reload();
            }
        },
        onFinish: () => {
        }
    });
}

const previewPrivacyPolicy = () => {
    const url = form.privacy_policy_type === 'default' ? props.whiteLabel.url + '/privacy-policy' : form.privacy_policy_url;
    window.open(url, '_blank');
}
</script>
