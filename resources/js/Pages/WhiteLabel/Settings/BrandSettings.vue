<template>
    <single-white-label active="Brand">
        <Head title="Brand Settings"/>
        <div class="flex flex-col w-full divide-y-1 divide-light dark:divide-mode-base h-full">
            <div class="flex items-center px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light rounded-t-lg gap-x-2 wide-mobile:gap-x-1">
                <h4 class="text-2xl wide-mobile:text-xl font-medium leading-tight text-secondary-full dark:text-white">
                    {{ $t('Brand Setup') }}
                </h4>
            </div>
            <div class="flex flex-col p-8 wide-mobile:p-5 gap-8 wide-mobile:gap-5 bg-white dark:bg-mode-light rounded-b-lg h-full">
                <div class="flex flex-col text-dark dark:text-white gap-8 wide-mobile:gap-5 w-full">
                    <div class="flex flex-col lg:flex-row gap-6">
                        <div class="flex flex-col gap-3">
                            <div class="flex items-center gap-2">
                                <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('Brand Profile') }}</h3>
                                <!--<Link
                                    href=""
                                    as="a"
                                    class="ml-auto text-xs font-medium leading-tight text-primary-light hover:underline underline-offset-2"
                                >
                                    See Preview
                                </Link>-->
                            </div>
                            <div class="flex items-start gap-4">
                                <div class="shrink-0 h-24 min-w-96 p-5 bg-white dark:bg-mode-light border-2 border-light dark:border-dark relative z-1 before:bg-white dark:before:bg-mode-light before:absolute before:-inset-x-0.5 before:inset-y-5 before:-z-1 after:bg-white dark:after:bg-mode-light after:absolute after:-inset-y-0.5 after:inset-x-5 after:-z-1">
                                    <img :src="brand_photo_path" alt="logo" class="h-full w-auto" />
                                </div>
                                <div class="flex flex-col items-start gap-1">
                                    <h4 class="text-dark dark:text-white text-lg font-medium leading-none">{{ $t('Add Your Logo') }}</h4>
                                    <p class="text-sm leading-tight text-secondary-full dark:text-mode-secondary-dark">{{ $t('Upload a logo that represent your brand profile.') }}
                                        <br> <span class="italic text-xs">{{ $t('Please upload a logo with a minimum size of 212x40 pixels in PNG, JPEG, or JPG format (max 5MB) for best quality.') }}</span></p>
                                    <input
                                        type="file"
                                        ref="file"
                                        accept="image/jpeg,image/png,image/jpg"
                                        class="hidden"
                                        @input="openFile($event)"
                                    />
                                    <button
                                        @click="$refs.file.click()"
                                        type="button"
                                        class="mt-3 inline-flex items-center border-1 border-primary-light justify-center min-h-10 p-2 px-5 pl-4 rounded-md shadow-none text-sm text-center text-white font-normal bg-primary-light focus:outline-none hover:bg-primary-dark hover:border-primary-dark hover:text-white ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30"
                                    >
                                    <span class="inline-flex justify-center items-center text-base mr-2">
                                        <i class="xcloud xc-upload_3"></i>
                                    </span>
                                        <span class="drop-shadow-button">{{ $t('Upload Logo') }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="flex flex-col gap-3">
                            <div class="flex items-center gap-2">
                                <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('Favicon') }}</h3>
                            </div>
                            <div class="flex items-start gap-4">
                                <div class="shrink-0 h-24 min-w-96 p-5 bg-white dark:bg-mode-light border-2 border-light dark:border-dark relative z-1 before:bg-white dark:before:bg-mode-light before:absolute before:-inset-x-0.5 before:inset-y-5 before:-z-1 after:bg-white dark:after:bg-mode-light after:absolute after:-inset-y-0.5 after:inset-x-5 after:-z-1">
                                    <img :src="favicon_photo_path" alt="favicon" class="h-full w-auto" />
                                </div>
                                <div class="flex flex-col items-start gap-1">
                                    <h4 class="text-dark dark:text-white text-lg font-medium leading-none">{{ $t('Add Your Favicon') }}</h4>
                                    <p class="text-sm leading-tight text-secondary-full dark:text-mode-secondary-dark">
                                        <span class="italic text-xs">{{ $t('Please upload a favicon with a minimum size of 16x16 pixels in PNG or ICO format (max 1MB) for best quality.') }}</span>
                                    </p>
                                    <input
                                        type="file"
                                        ref="faviconFile"
                                        accept="image/png,image/x-icon"
                                        class="hidden"
                                        @input="openFaviconFile($event)"
                                    />
                                    <button
                                        @click="$refs.faviconFile.click()"
                                        type="button"
                                        class="mt-3 inline-flex items-center border-1 border-primary-light justify-center min-h-10 p-2 px-5 pl-4 rounded-md shadow-none text-sm text-center text-white font-normal bg-primary-light focus:outline-none hover:bg-primary-dark hover:border-primary-dark hover:text-white ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30"
                                    >
                                    <span class="inline-flex justify-center items-center text-base mr-2">
                                        <i class="xcloud xc-upload_3"></i>
                                    </span>
                                        <span class="drop-shadow-button">{{ $t('Upload Favicon') }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 tablet:grid-cols-1 gap-4">
                        <text-input
                            v-model="brandSetupForm.brand_name"
                            :error="brandSetupForm.errors.brand_name"
                            id="brand_name"
                            :placeholder="$t('Cloud Hosting')"
                            :label="$t('Brand Name')"
                            :required-field="true"
                        />
                        <text-input
                            v-model="brandSetupForm.contact_number"
                            :error="brandSetupForm.errors.contact_number"
                            id="contact_number"
                            placeholder="+1(XXX) XXX-XXXX"
                            :label="$t('Contact Number')"
                            :required-field="true"
                        />
                        <text-input
                            v-model="brandSetupForm.email"
                            :error="brandSetupForm.errors.email"
                            id="email"
                            placeholder="<EMAIL>"
                            :label="$t('Email Address')"
                            :required-field="true"
                        />
                        <text-input
                            v-model="brandSetupForm.support_email"
                            :error="brandSetupForm.errors.support_email"
                            id="support_email"
                            placeholder="<EMAIL>"
                            :label="$t('Support Email')"
                            :required-field="true"
                        />
                        <textarea-input
                            :rows="2"
                            v-model="brandSetupForm.address"
                            :error="brandSetupForm.errors.address"
                            id="address"
                            :placeholder="$t('579 Spruce Court, Dallas, TX 75201')"
                            :label="$t('Address')"
                            :required-field="true"
                        />
                        <textarea-input
                            :rows="2"
                            v-model="brandSetupForm.copyright_name"
                            :error="brandSetupForm.errors.copyright_name"
                            id="copyright_name"
                            :placeholder="$t('CloudHosting')"
                            :label="$t('Copyright Name')"
                        />
                        <div>
                     
                            <label for="language" class="block text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none mb-2.5">{{ $t('Language Settings') }}</label>
                            <Multiselect
                                class="!ml-0 w-full !min-w-full"
                                v-model="brandSetupForm.language"
                                @select="changeLanguage"
                                placeholder="Choose Language"
                                label="Language"
                                :canClear="false"
                                :canDeselect="false"
                                :options="countries"
                            >
                                <template v-slot:singlelabel="{ value }">
                                    <div
                                        class="multiselect-single-label rounded-md"
                                    >
                                        <RegionFlag
                                            v-if="value?.region"
                                            :region="value?.region"
                                            classList="w-[20px] h-[20px] rounded-[10px] p-3 object-cover mr-2"
                                            :showRegion="false"
                                        />
                                        {{ value?.name }}
                                    </div>
                                </template>

                                <template v-slot:option="{ option }">
                                    <RegionFlag
                                        v-if="option?.region"
                                        :region="option?.region"
                                        classList="w-[20px] h-[20px] rounded-[10px] p-3 object-cover mr-2"
                                        :showRegion="false"
                                    />
                                    {{ option?.name }}
                                </template>
                            </Multiselect>
                        </div>
                    
                    </div>
                </div>
                <div class="flex items-center gap-4 mt-auto">
                    <div class="flex items-center gap-4 ml-auto">
                        <!--<button class="inline-flex items-center justify-center rounded-lg border-1 border-transparent shadow-none gap-2 min-h-12 px-6 bg-transparent text-base font-medium text-danger hover:bg-danger hover:border-danger hover:text-white transition duration-75 ease-in-out focus:outline-0">
                            Cancel
                        </button>-->
                        <button
                            @click.prevent="updateBrandSetup"
                            class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2
                                      min-h-12 px-6 bg-success-light text-base font-medium text-white focus:outline-0"
                            :disabled="brandSetupForm.processing || loading"
                            :class="{'opacity-50 cursor-not-allowed': brandSetupForm.processing || loading}"
                        >
                            <span v-text="brandSetupForm.processing || loading ? $t('Processing...') : $t('Save')"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </single-white-label>
</template>

<script setup>
import {useForm} from "@inertiajs/inertia-vue3";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import TextareaInput from "@/Shared/TextareaInput.vue";
import {Inertia} from "@inertiajs/inertia";
import {ref} from "vue";
import Vapor, {asset} from "laravel-vapor";
import {useFlash} from "@/Composables/useFlash";
import Multiselect from "@vueform/multiselect";
import { languagesData } from "@/Composables/LanguagesWithCountryName";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";

const file = ref(null);
const faviconFile = ref(null);
const props = defineProps({
    whiteLabel: Object,
});

const brand_photo_path = ref(props.whiteLabel.brand_photo_url);
const favicon_photo_path = ref(props.whiteLabel.favicon_photo_url);
const loading = ref(false);
const countries = ref(languagesData);

const brandSetupForm = useForm({
    brand_name: props.whiteLabel?.branding?.brand_name ?? '',
    email: props.whiteLabel?.branding?.email ?? '',
    support_email: props.whiteLabel?.branding?.support_email ?? '',
    address: props.whiteLabel?.branding?.address ?? '',
    contact_number: props.whiteLabel?.branding?.contact_number ?? '',
    copyright_name: props.whiteLabel?.branding?.copyright_name ?? '',
    language: props.whiteLabel?.branding?.language ?? '',
    photo: null,
    favicon: null,
});

const openFile = (event) => {
    if (event.target.files && event.target.files[0]) {
        const file = event.target.files[0];
        if (file.size > (5 * 1024 * 1024)) {
            useFlash().error('The logo size must not exceed 5MB.');
            return;
        }

        let reader = new FileReader();
        reader.onload = (e) => {
            brand_photo_path.value = e.target.result;
        };
        reader.readAsDataURL(event.target.files[0]);
    }
};

const openFaviconFile = (event) => {
    if (event.target.files && event.target.files[0]) {
        const faviconFile = event.target.files[0];
        if (faviconFile.size > (5 * 1024 * 1024)) {
            useFlash().error('The favicon size must not exceed 1MB.');
            return;
        }

        let reader = new FileReader();
        reader.onload = (e) => {
            favicon_photo_path.value = e.target.result;
        };
        reader.readAsDataURL(event.target.files[0]);
    }
};

const updateBrandSetup = async () => {
    loading.value = true;
    if (file.value.files.length > 0) {
        brandSetupForm.photo = await Vapor.store(file.value.files[0], {
            visibility: 'public-read',
            progress: progress => {
                //console.log(Math.round(progress * 100));
            }
        });
    }

    if (faviconFile.value.files.length > 0) {
        brandSetupForm.favicon = await Vapor.store(faviconFile.value.files[0], {
            visibility: 'public-read',
            progress: progress => {
                //console.log(Math.round(progress * 100));
            }
        });
    }

    file.value = null;
    faviconFile.value = null;
    submitBrandSetup();
}

const submitBrandSetup = () => {
    brandSetupForm.post('/white-label/brand/settings/update', {
        onSuccess: () => {
            Inertia.reload();
        },
        onFinish: () => {
            loading.value = false;
        }
    });
}

const changeLanguage = () => {
    // console.log('white label lang: ', brandSetupForm.language)
}
</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
@import "flag-icons/css/flag-icons.min.css";
</style>
