<template>
    <div class="xc-container">
        <div class="max-w-300 w-full mx-auto flex flex-col">
            <div class="bg-white dark:bg-mode-light text-dark dark:text-white w-full p-12 small-laptop:p-8 wide-mobile:p-5 flex flex-col items-center text-center gap-10 small-laptop:gap-6 wide-mobile:gap-4 rounded-lg">
                <div class="inline-flex items-center justify-center relative">
                    <img v-if="navigations.nightMode" :src="asset('img/white-label/onboarding/Illustration.svg')" alt="Illustration" class="w-full max-w-72 tablet:max-w-52 aspect-square rounded-full"/>
                    <img v-else :src="asset('img/white-label/Illustration-white.svg')" alt="Illustration" class="w-full max-w-72 tablet:max-w-52 aspect-square rounded-full"/>
                    <img v-if="navigations.nightMode" :src="asset('img/white-label/onboarding/circle.svg')" alt="Circular Image" class="w-32 tablet:w-20 aspect-square rounded-full inline-flex items-center justify-center absolute -right-2 bottom-0 custom-animate-spin"/>
                    <img v-else :src="asset('img/white-label/circle-white.svg')" alt="Circular Image" class="w-32 tablet:w-20 aspect-square rounded-full inline-flex items-center justify-center absolute -right-2 bottom-0 custom-animate-spin"/>
                </div>
                <div class="flex flex-col items-center gap-4 wide-mobile:gap-3">
                    <h3 class="text-3xl tablet:text-2xl mobile:text-lg font-semibold leading-tight max-w-[40ch]">{{ $t('Start Your Hosting Business: Resell & Earn Revenue') }}</h3>
                    <p class="text-base wide-tablet:text-sm leading-normal text-secondary-full dark:text-mode-secondary-light max-w-[70ch]">{{ $t('Launch a cloud hosting business with xCloud Managed Servers. Resell our fully managed web hosting under your own brand or domain to maximize your revenue ensuring a reliable performance.') }}</p>
                </div>
                <div class="grid grid-cols-2 tablet:grid-cols-1 gap-x-6 gap-y-0">
                    <p class="flex items-center text-left text-dark dark:text-white text-base wide-mobile:text-sm before:font-xc before:content-['\e92d'] before:text-primary-light before:mr-3">{{ $t('Complete control for your personal branding') }}</p>
                    <p class="flex items-center text-left text-dark dark:text-white text-base wide-mobile:text-sm before:font-xc before:content-['\e92d'] before:text-primary-light before:mr-3">{{ $t('Manage your client billings with Stripe Connect') }}</p>
                    <p class="flex items-center text-left text-dark dark:text-white text-base wide-mobile:text-sm before:font-xc before:content-['\e92d'] before:text-primary-light before:mr-3">{{ $t('Customize hosting packages & sell at your own price') }}</p>
                    <p class="flex items-center text-left text-dark dark:text-white text-base wide-mobile:text-sm before:font-xc before:content-['\e92d'] before:text-primary-light before:mr-3">{{ $t('Get access to powerful features of xCloud') }}</p>
                </div>
                <div>
                    <label class="mt-10px inline-flex pb-0">
                        <input type="checkbox" v-model="enableTermsAndServices" class="hidden peer"/>
                        <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent
                                        before:border-1 before:border-secondary-light dark:border-mode-secondary-dark border-radius
                                        before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center
                                        before:text-transparent before:outline-none before:transition before:duration-200
                                        peer-checked:before:border-success-full peer-checked:before:bg-success-full
                                        peer-checked:before:text-white">
                              <span class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark">
                                  {{ $t('By ticking this box, you are confirming that you have read, understood, and agree to our') }}
                                  <a target="_blank" href="https://xcloud.host/xcloud-white-label-terms-and-conditions/" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                        dark:hover:text-primary-light">
                                        {{ $t('Terms') }}
                                      </a>
                                        {{ $t('and') }}
                                      <a target="_blank" href="https://xcloud.host/privacy-policy/" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                              dark:hover:text-primary-light">
                                          {{ $t('Privacy Policy') }}
                                      </a>
                                  <span>*</span>
                              </span>
                        </span>
                    </label>
                    <br>
                    <span v-if="termsAndServicesError" class="text-danger">{{ $t('Please check this box to confirm that you accept the terms and conditions and want to proceed.') }}</span>

                    <label class="mt-10px inline-flex pb-0">
                        <input type="checkbox" v-model="enableStripeConnect" class="hidden peer"/>
                        <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent
                                        before:border-1 before:border-secondary-light dark:border-mode-secondary-dark border-radius
                                        before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center
                                        before:text-transparent before:outline-none before:transition before:duration-200
                                        peer-checked:before:border-success-full peer-checked:before:bg-success-full
                                        peer-checked:before:text-white">
                              <span class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark">
                                  {{ $t('Before proceeding, ensure you have an active Stripe account, as all transactions are managed via') }}
                                  <a target="_blank" href="https://stripe.com/connect" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                        dark:hover:text-primary-light">
                                        {{ $t('Stripe Connect') }}
                                      </a>
                                  <span>*</span>
                              </span>
                        </span>
                    </label>
                    <br>
                    <span v-if="stripeConnectError" class="text-danger">{{ $t('Please check this box to confirm that you have a Stripe account and want to proceed.') }}</span>
                </div>
                <button @click.prevent="submit" class="inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 min-h-12 px-8 py-1 bg-primary-light text-base leading-none font-medium text-white focus:outline-0">
                    {{ $t('Start Your Hosting Business Now') }}
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import {Inertia} from "@inertiajs/inertia";
import {useForm} from "@inertiajs/inertia-vue3";
import {ref, watch} from "vue";
import {useNavigationStore} from "@/stores/NavigationStore";

const navigations = useNavigationStore();

const props = defineProps({
    whiteLabel: Object,
});

const enableTermsAndServices = ref(false)
const enableStripeConnect = ref(false)
const termsAndServicesError = ref(false)
const stripeConnectError = ref(false)

const startupForm = useForm({
    status: 'active',
});

const submit = () => {
    if (!enableTermsAndServices.value || !enableStripeConnect.value) {
        if (!enableTermsAndServices.value) {
            termsAndServicesError.value = true;
        }

        if (!enableStripeConnect.value) {
            stripeConnectError.value = true;
        }
        return;
    }

    termsAndServicesError.value = false;
    stripeConnectError.value = false;

    if (props.whiteLabel) {
        Inertia.visit('/white-label/onboarding/checkout')
    } else {
        startupForm.post('/white-label/onboarding/signup', {
            onSuccess: () => {
                Inertia.visit('/white-label/onboarding/checkout')
            }
        });
    }
}

watch(enableTermsAndServices, (newValue) => {
    if (newValue) {
        termsAndServicesError.value = false;
    }
});

watch(enableStripeConnect, (newValue) => {
    if (newValue) {
        stripeConnectError.value = false;
    }
});
</script>

<style scoped>
@keyframes custom-animate-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.custom-animate-spin {
    animation: custom-animate-spin 5s linear infinite;
}
</style>
