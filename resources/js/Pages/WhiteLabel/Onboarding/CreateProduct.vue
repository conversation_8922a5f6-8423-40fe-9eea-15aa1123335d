<template>
    <div class="xc-container">
        <div class="max-w-300 w-full mx-auto flex flex-col grow">
            <div class="flex tablet:flex-col gap-2 grow">
                <Sidebar :step="3" :current-step="currentStep" />
                <div class="grow max-w-[calc(theme(width.full)-theme(spacing.96)-theme(spacing.2))] wide-tablet:max-w-[calc(theme(width.full)-theme(spacing.60)-theme(spacing.2))] tablet:max-w-full flex flex-col gap-2">
                    <div class="flex flex-col w-full divide-y-1 divide-light dark:divide-mode-base h-full">
                        <div class="flex items-center px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light rounded-t-lg gap-x-2 wide-mobile:gap-x-1">
                            <h4 class="text-2xl wide-mobile:text-xl font-medium leading-tight text-secondary-full dark:text-white">
                                {{ $t('Create Product') }} <span class="text-xs">({{ $t('Optional') }})</span>
                            </h4>
                            <button @click.prevent="skipCreateProduct" class="ml-auto text-base text-secondary-full dark:text-mode-secondary-dark">{{ $t('Skip') }}</button>
                        </div>

                        <div class="flex flex-col p-8 wide-mobile:p-5 gap-8 wide-mobile:gap-5 bg-white dark:bg-mode-light rounded-b-lg h-full">
                            <div v-if="createdProducts && createdProducts?.length > 0" class="flex justify-end">
                                <button @click.prevent="addNewProduct" class="w-60 inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 h-12 px-8 bg-primary-light text-base font-medium text-white focus:outline-0 btn-sm">
                                    {{ $t('Create Hosting Plan') }}
                                </button>
                            </div>
                            <div v-if="createdProducts && createdProducts?.length > 0" class="overflow-x-auto flex flex-col">
                                <div class="inline-block min-w-full">
                                    <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Plan Name') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Type') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Renewal Type') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('SKU') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Price') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Status') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody class="bg-white dark:bg-mode-base">
                                        <tr v-for="product in createdProducts" :key="product.id"
                                            class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                                                {{ product?.title }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                                                {{ product?.type }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                                                {{ product?.renewal_type }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                                {{ product?.sku }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                                ${{ product?.price }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                                <span v-if="product?.is_active" class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-success-full/10 text-xs leading-none text-success-full focus:outline-0">
                                                    <span>Active</span>
                                                </span>
                                                <span v-else class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-danger text-xs leading-none text-white focus:outline-0">
                                                    <span>Inactive</span>
                                                </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div v-else class="flex flex-col text-dark dark:text-white items-center text-center gap-3 wide-mobile:gap-2 mobile:gap-1">
                                <div class="inline-flex items-center justify-center">
                                    <img v-if="navigations.nightMode" :src="asset('img/white-label/onboarding/Empty.svg')" alt="Empty" class="w-56 wide-mobile:w-44 mobile:w-36 h-auto"/>
                                    <img v-else :src="asset('img/white-label/empty-product-white.svg')" alt="Empty" class="w-56 wide-mobile:w-44 mobile:w-36 h-auto"/>
                                </div>
                                <div class="flex flex-col items-center gap-4">
                                    <h3 class="text-2xl wide-mobile:text-xl mobile:text-lg font-semibold leading-tight">{{ $t('Setup Products & Start Selling') }}</h3>
                                </div>
                                <button @click.prevent="addNewProduct" class="mt-1 inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 h-12 px-8 bg-primary-light text-base font-medium text-white focus:outline-0">
                                    {{ $t('Create Hosting Plan') }}
                                </button>
                            </div>

                            <div class="flex items-center gap-4 mt-auto">
                                <Link href="/white-label/onboarding/payment-setup" class="inline-flex items-center justify-center rounded-lg border-1 border-primary-light shadow-none gap-2 min-h-12 px-6 bg-transparent text-base font-semibold text-primary-light hover:bg-primary-light hover:border-primary-light hover:text-white transition duration-75 ease-in-out focus:outline-0">
                                    {{ $t('Back') }}
                                </Link>
                                <div class="flex items-center gap-4 ml-auto">
                                    <button @click.prevent="submit" class="inline-flex items-center justify-center rounded-lg border-primary-light shadow-none min-h-12 px-6 bg-primary-light text-base font-semibold text-white focus:outline-0 gap-1.5">
                                        <span>{{ $t('Next') }}</span>
                                        <span class="text-xs inline-flex items-center">
                                            <i class="xcloud xc-angle_right"></i>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <CreateModal
        ref="productCreationComponent"
        :products="products"
        :sku-prefix="skuPrefix"
        :save-endpoint="'/white-label/products/store'"
        :save-button-text="'Save and Publish'"
        @saved="onProductSaved"
        @close="onModalClosed"
    />
</template>

<script setup>
import {Inertia} from "@inertiajs/inertia";
import Sidebar from "@/Pages/WhiteLabel/Onboarding/Sidebar.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import Modal from "@/Shared/Modal.vue";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import {computed, onMounted, ref, watch} from "vue";
import {useFlash} from "@/Composables/useFlash";
import Button from "@/Jetstream/Button.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import SelectorTitleDropdown from "@/Pages/WhiteLabel/Components/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import {useNavigationStore} from "@/stores/NavigationStore";
import CreateModal from "@/Pages/WhiteLabel/Product/Components/CreateModal.vue";
const currentStep = ref(3);

const navigations = useNavigationStore();

const props = defineProps({
    products: Object,
    createdProducts: Object,
    skuPrefix: String
});

// These variables are now handled by the CreateModal component

const submit = () => {
    Inertia.visit('/white-label/onboarding/domain-setup')
}

const skipCreateProduct = () => {
    Inertia.visit('/white-label/onboarding/domain-setup')
}

const productCreationComponent = ref(null);

const addNewProduct = () => {
    productCreationComponent.value.showFirstModal();
}

// addNewProductStepTwo is now handled by the CreateModal component

// These methods are now handled by the CreateModal component
const onProductSaved = () => {
    Inertia.reload();
}

const onModalClosed = () => {
    // Handle any cleanup needed when the modal is closed
}

// Initial product selection is now handled by the CreateModal component

// These methods and watchers are now handled by the CreateModal component
</script>
