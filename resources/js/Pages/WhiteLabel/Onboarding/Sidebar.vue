<template>
    <div class="basis-96 wide-tablet:basis-60 tablet:w-full shrink-0 bg-white dark:bg-mode-light text-dark dark:text-white px-6 wide-tablet:px-5 py-8 wide-tablet:py-5 rounded-lg flex flex-col gap-8 wide-tablet:gap-6 wide-mobile:gap-4">
        <div class="flex flex-col gap-10 wide-mobile:gap-5">
            <template v-for="stepItem in steps" :key="stepItem.id">
                <div class="flex items-start gap-4 relative before:absolute before:w-0.25 before:top-10 before:left-5 before:-translate-x-1/2 before:-bottom-10 wide-mobile:before:-bottom-5 before:bg-secondary-light dark:before:bg-mode-secondary-dark last:before:hidden" :class="{'before:!bg-dark dark:before:!bg-white' : currentStep > stepItem.id}">
                    <SidebarStateIcon
                        :read-state="currentStep > stepItem.id"
                        :active-state="currentStep === stepItem.id"
                        :unread-state="currentStep < stepItem.id"
                        :icon="stepItem.icon"
                    />
                    <div class="flex flex-col text-secondary-full dark:text-mode-secondary-light"
                        :class="{'!text-dark dark:!text-white' : currentStep === stepItem.id}"
                    >
                        <h4 class="text-lg font-medium leading-snug">{{ stepItem.title }} <span class="text-sm" v-if="stepItem.optional_text">{{ stepItem.optional_text }}</span></h4>
                        <p class="text-sm leading-tight">{{ stepItem.description }}</p>
                    </div>
                </div>
            </template>
        </div>
        <div class="mt-auto flex flex-col items-start gap-2 wide-tablet:gap-1 wide-mobile:gap-0.5">
            <div class="inline-flex items-center justify-center text-4xl wide-mobile:text-3xl mb-2 wide-tablet:mb-1 wide-mobile:mb-0">
                <i class="xcloud xc-help"></i>
            </div>
            <h4 class="text-lg font-medium leading-snug text-dark dark:text-white">{{ $t('Having Trouble?') }}</h4>
            <p class="text-sm leading-tight text-secondary-full dark:text-mode-secondary-light">{{ $t('Read our comprehensive documentation & learn to manage your hosting easily.') }}</p>
            <a href="https://xcloud.host/docs/setup-reseller-account-with-xcloud-white-label" target="_blank" class="text-sm font-medium leading-tight text-primary-light">{{ $t('Read Documentation') }}</a>
        </div>
    </div>
</template>

<script setup>
import SidebarStateIcon from "@/Pages/WhiteLabel/Onboarding/SidebarStateIcon.vue";
import { computed } from 'vue';
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
    step: {
        type: Number,
        required: true,
    },
    currentStep: {
        type: Number,
        required: true,
    }
});

const steps = computed(() => [
    { id: 1, title: t('Brand Setup'), description: t('Setup your brand to get started'), icon: 'xc-settings_2' },
    { id: 2, title: t('Payment Setup'), description: t('Setup your payment method'), icon: 'xc-money' },
    { id: 3, title: t('Create Products'), description: t('Create customized plans'), optional_text: '('+t('Optional')+')', icon: 'xc-briefcase' },
    { id: 4, title: t('Domain Setup'), description: t('Setup your domain easily'), optional_text: '('+t('Optional')+')', icon: 'xc-domains' },
]);
</script>
