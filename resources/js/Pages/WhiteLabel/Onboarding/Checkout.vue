<template>
    <div class="xc-container">
        <div class="max-w-300 w-full mx-auto flex flex-col">
            <div class="bg-white dark:bg-mode-light text-dark dark:text-white w-full p-12 small-laptop:p-8 wide-mobile:p-5 flex flex-col items-center text-center gap-10 small-laptop:gap-6 wide-mobile:gap-4 rounded-lg">
                <h3 class="text-center text-3xl tablet:text-2xl mobile:text-lg font-semibold leading-tight">
                  {{ $t('Proceed to Checkout') }}
                </h3>
                <div class="w-full grid grid-cols-9 wide-tablet:grid-cols-1 gap-8 wide-tablet:gap-6">
                    <div class="col-span-5 wide-tablet:col-span-1">
                        <div class="grid grid-cols-1 wide-tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-6 wide-tablet:gap-4 wide-mobile:gap-3 wide-tablet:max-h-max"> <!-- max-h-[29rem] overflow-y-auto no-scrollbar-->
                            <label
                                v-for="packages in whiteLabelPackages"
                            >
                                <input type="radio" name="plan-check" @click="price = (packages.can_avail_free ? 0 : packages.price)" :value="packages.uuid" v-model="form.subscription_product" class="hidden peer"/>
                                <div
                                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-focused dark:divide-mode-focus-light overflow-hidden border-focused dark:border-mode-focus-light peer-checked:border-primary-light relative before:absolute before:left-6 wide-mobile:before:left-4 before:top-[1.125rem] before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:aspect-square before:bg-transparent before:border-2 before:border-secondary-light dark:before:border-[#313A6C] before:rounded-full before:mt-0 before:text-xxxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-primary-light peer-checked:before:bg-primary-light peer-checked:before:text-white"
                                >
                                    <div class="flex items-center gap-2 px-6 h-14 wide-mobile:px-4 justify-start bg-light dark:bg-mode-focus-middle">
                                        <h4 class="ml-8 wide-mobile:ml-7 text-lg wide-mobile:text-base leading-tight font-medium text-dark dark:text-white">
                                            {{ packages.name }}
                                        </h4>
                                        <span
                                            v-if="packages.can_avail_free"
                                            class="px-10px py-1 wide-mobile:p-1 rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5 text-success-full bg-success-light/10 text-sm">
                                            <span>{{ $t('Claim Free with LTD') }}</span>
                                        </span>
                                        <div class="ml-auto">
                                            <p class="text-base wide-mobile:text-sm leading-tight font-normal text-secondary-full dark:text-mode-secondary-light">
                                              {{ $t('Sell up to') }} {{ packages.unit }} {{ $t('Servers') }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-1.5 wide-mobile:gap-1 px-6 py-4 wide-mobile:px-4 wide-mobile:py-2 justify-between">
                                        <div class="flex flex-col gap-1.5 wide-mobile:gap-0.5 text-left">
                                            <div class="flex gap-2 items-end">
                                                <h4 class="text-[2rem] wide-mobile:text-2xl leading-none font-bold tracking-tighter text-dark dark:text-white">
                                                    {{ packages.can_avail_free ? '$0' : packages.currency_symbol + packages.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-2xl wide-mobile:text-sm leading-none">mo</small>
                                                </h4>
                                                <h6 v-if="packages.actual_price && !packages.can_avail_free" class="text-sm leading-none font-bold tracking-tighter text-secondary-full dark:text-mode-secondary-light mb-0.5 wide-mobile:mb-1.5 line-through">
                                                    {{ packages.currency_symbol + packages.actual_price }}/<small class="text-xs leading-none">{{ packages.short_renewal_period }}</small>
                                                </h6>
                                                <h6 v-if="packages.can_avail_free" class="text-sm leading-none font-bold tracking-tighter text-secondary-full dark:text-mode-secondary-light mb-0.5 wide-mobile:mb-1.5 line-through">
                                                    {{ packages.currency_symbol + packages.actual_price }}/<small class="text-xs leading-none">{{ packages.short_renewal_period }}</small>
                                                </h6>
                                            </div>
                                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                                <span v-if="packages.can_avail_free">
                                                    {{ $t('As an existing LTD customer, you are eligible to claim this plan for free.') }}
                                                </span>
                                                <span v-else>
                                                    {{ packages.description }}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="col-span-4 wide-tablet:col-span-1">
                        <div class=" flex flex-col p-6 wide-mobile:p-4 bg-light dark:bg-mode-focus-middle rounded-lg text-left">
                            <div class="pb-5 wide-mobile:pb-3 flex flex-col gap-2 wide-mobile:gap-1 border-b border-focused dark:border-mode-focus-light">
                                <h4 class="text-xl font-semibold leading-tight">{{ $t('Checkout') }}</h4>
                                <p class="text-secondary-full dark:text-mode-secondary-light text-sm leading-tight">{{ $t('Your Order Summary') }}</p>
                            </div>
                            <div class="py-5 wide-mobile:py-3 flex flex-col gap-2 wide-mobile:gap-1 border-b border-focused dark:border-mode-focus-light">
                                <div class="flex items-center gap-2 wide-mobile:gap-1 text-secondary-full dark:text-mode-secondary-light text-base leading-tight">
                                    <p class="text-left">{{ $t('Sub Total') }}</p>
                                    <p class="ml-auto">${{ price }}</p>
                                </div>
<!--                                <div class="flex items-center gap-2 wide-mobile:gap-1 text-secondary-full dark:text-mode-secondary-light text-base leading-tight">-->
<!--                                    <p class="text-left">VAT (15%)</p>-->
<!--                                    <p class="ml-auto">$29.99</p>-->
<!--                                </div>-->
                            </div>
                            <div class="pt-4 wide-mobile:pt-3 pb-6 wide-mobile:pb-4 flex flex-col">
                                <div class="flex items-center gap-2 text-dark dark:text-white text-xl font-medium leading-tight">
                                    <p class="text-left">{{ $t('Total') }}</p>
                                    <p class="ml-auto">${{ price }}</p>
                                </div>
                            </div>
<!--                            <div class="flex flex-col gap-4 wide-mobile:gap-2.5">-->
<!--                                <h4 class="flex items-center gap-2 text-dark dark:text-white text-lg font-medium leading-none">Payment Method</h4>-->
<!--                                <div class="w-full p-4 wide-mobile:p-2.5 rounded-lg flex items-center gap-1.5 border border-solid border-focused dark:border-mode-focus-light">-->
<!--                                    <div class="grow flex items-center gap-2 wide-mobile:gap-1.5">-->
<!--                                        <span class="inline-flex items-center justify-center w-15 wide-mobile:w-12 shrink-0 rounded bg-white dark:bg-mode-light">-->
<!--                                            <img :src="asset('img/visa.svg')" alt="avatar" class="w-3/4 h-auto" />-->
<!--                                        </span>-->
<!--                                        <div class="flex flex-col gap-1 wide-mobile:gap-0.5">-->
<!--                                            <h4 class="text-dark dark:text-white text-base wide-mobile:text-sm leading-tight wide-mobile:leading-tight">Visa ending in 1234</h4>-->
<!--                                            <p class="text-secondary-full dark:text-mode-secondary-light text-xs leading-normal">Expiry 06/2024</p>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <button class="inline-flex items-center text-sm gap-2">-->
<!--                                        <span class="whitespace-nowrap text-secondary-full dark:text-mode-secondary-light">Change</span>-->
<!--                                    </button>-->
<!--                                </div>-->
<!--                            </div>-->
                            <button v-if="price" @click.prevent="payNow"
                                    :disabled="form.processing"
                                    class="mt-4 w-full inline-flex items-center justify-center rounded-lg border-primary-light
                                        shadow-none gap-2 h-12 px-8 bg-primary-light text-base font-medium text-white focus:outline-0"
                                    :class="{ 'opacity-50 cursor-not-allowed': form.processing }"
                                    v-text="form.processing ? $t('Processing Payment...') : $t('Pay Now')"
                            >
                            </button>

                            <button v-show="form.subscription_product" v-else @click.prevent="claim"
                                    :disabled="form.processing"
                                    class="mt-4 w-full inline-flex items-center justify-center rounded-lg border-primary-light
                                        shadow-none gap-2 h-12 px-8 bg-primary-light text-base font-medium text-white focus:outline-0"
                                    :class="{ 'opacity-50 cursor-not-allowed': form.processing }"
                                    v-text="form.processing ? $t('Processing Offer...') : $t('Claim Free')"
                            >
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {Inertia} from "@inertiajs/inertia";
import {useForm} from "@inertiajs/inertia-vue3";
import {ref} from "vue";

const props = defineProps({
    whiteLabel: Object,
    whiteLabelPackages: Array,
    claimableProduct: Object
})

const form = useForm({
    subscription_product: props.claimableProduct ? props.claimableProduct?.uuid : null,
})

const price = ref(0);

const payNow = () => {
    form.post(
        route("api.whitelabel-billing.process-payment", {
            whiteLabel: props.whiteLabel.id
        }),
        {
            preserveScroll: true,
            onSuccess(response) {
                if(response?.props?.jetstream?.flash?.type === 'success'){
                    Inertia.visit('/white-label/onboarding/brand-setup')
                }
            },
            onError(error) {
                window.scrollTo(0, 0);
            },
        },
    );
}

const claim = () => {
    form.post(
        route("api.whitelabel-billing.claim-offer", {
            whiteLabel: props.whiteLabel.id
        }),
        {
            preserveScroll: true,
            onSuccess(response) {
                if(response?.props?.jetstream?.flash?.type === 'success'){
                    Inertia.visit('/white-label/onboarding/brand-setup')
                }
            },
            onError(error) {
                window.scrollTo(0, 0);
            },
        },
    );
}

</script>

