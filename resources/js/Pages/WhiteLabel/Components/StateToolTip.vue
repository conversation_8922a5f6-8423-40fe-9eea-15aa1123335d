<template>
    <Tooltip :align="align" :title="updateMessage ?? title">
        <span v-if="state === 'Initial'"
            class="w-1.5 aspect-square shrink-0 rounded-full relative after:absolute after:-inset-0.5 after:rounded-full after:bg-primary-light/20 z-1 after:-z-1 bg-primary-dark after:animate-ping">
        </span>
        <span v-if="state === 'InQueue'"
            class="w-1.5 aspect-square shrink-0 rounded-full relative after:absolute after:-inset-0.5 after:rounded-full after:bg-primary-light/20 z-1 after:-z-1 bg-primary-dark after:animate-ping">
        </span>
        <span v-if="state === 'Processing'"
            class="w-1.5 aspect-square shrink-0 rounded-full relative after:absolute after:-inset-0.5 after:rounded-full after:bg-primary-light/20 z-1 after:-z-1 bg-primary-dark after:animate-ping">
        </span>
        <span v-if="state === 'Disabled'"
            class="w-1.5 aspect-square shrink-0 rounded-full relative after:absolute after:-inset-0.5 after:rounded-full after:bg-warning/20 z-1 after:-z-1 bg-warning after:animate-ping">
        </span>
        <span v-if="state === 'Disconnected'"
            class="w-1.5 aspect-square shrink-0 rounded-full relative after:absolute after:-inset-0.5 after:rounded-full after:bg-primary-light/20 z-1 after:-z-1 bg-danger after:animate-ping">
        </span>
        <span v-if="state === 'Success'"
            class="w-1.5 aspect-square shrink-0 rounded-full relative after:absolute after:-inset-0.5 after:rounded-full after:bg-success-full/20 z-1 after:-z-1 bg-success-full after:animate-ping">
        </span>
        <span v-if="isLow"
            class="w-1.5 aspect-square shrink-0 rounded-full relative after:absolute after:-inset-0.5 after:rounded-full after:bg-warning/20 z-1 after:-z-1 bg-warning after:animate-ping">
        </span>
    </Tooltip>
</template>

<script setup>

import Tooltip from "@/Shared/Tooltip.vue";

const { title, state, updates, isLow } = defineProps({
    title: String,
    state: {
        type: String,
        default: null
    },
    isLow: {
        type: Boolean,
        default: false
    },
    updates: {
        type: Object,
        default: {
            plugins: 0,
            themes: 0,
            wp_version: 0,
        }
    },
    align: {
        type: String,
        default: 'top'
    },
});

const totalCount = Object.values(updates).reduce((acc, cur) => acc + cur);

let updateMessage = undefined;
if (state === "Success" && totalCount !== 0) {
    updateMessage = '';
   if (updates?.plugins > 0){
       updateMessage = `${updates?.plugins} plugins `;
   }
    if (updates?.themes > 0){
         updateMessage += `${updates?.themes} themes `;
    }
    if (updates?.wp_version > 0){
         updateMessage += `WordPress `;
    }
    updateMessage += `updates available!`;
}
if(state==='Disabled'){
    updateMessage = 'Disabled';
}
if (isLow){
    updateMessage = 'Low disk space';
}
</script>
