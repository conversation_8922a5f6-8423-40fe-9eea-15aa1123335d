<template>
  <single-white-label active="Billing">
    <Head title="Billing Details"/>
    <div class="h-full flex flex-col w-full divide-y-1 divide-light dark:divide-mode-base">
      <!--<div class="flex items-center px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light
            rounded-t-lg gap-x-2 wide-mobile:gap-x-1"
      >
          <h4 class="text-2xl wide-mobile:text-xl font-medium leading-tight text-secondary-full dark:text-white">
              Billing Details
          </h4>
      </div>-->
      <div class="flex items-center p-8 wide-mobile:p-5 bg-white dark:bg-mode-light rounded-b-lg">
        <div class="flex flex-col text-dark dark:text-white gap-8 wide-mobile:gap-5 w-full">
          <!--<div class="flex flex-col gap-4 wide-mobile:gap-2 w-full">
            <div class="flex items-center flex-wrap gap-2 wide-mobile:gap-1">
              <div class="flex flex-col gap-2 wide-mobile:gap-1">
                <h4 class="text-lg font-medium leading-none">Bill State</h4>
                <p class="text-xs leading-none text-secondary-full dark:text-mode-secondary-dark">Last Updated on 27
                  June, 2024 12:20</p>
              </div>
              <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
                <SelectorTitleDropdown position="right" selected-item="Sort By">
                  <template v-if="false">
                    <SelectItem
                        v-if="true"
                        title="View Sites"
                        @onItemSelected="() => {}"
                    />
                  </template>
                  <template v-else>
                    <SelectItem
                        title="View Server"
                        @onItemSelected="() => {}"
                    />
                  </template>
                </SelectorTitleDropdown>
              </div>
            </div>
            <div class="grid grid-cols-auto-50 gap-4 wide-mobile:gap-2">
              <div class="flex flex-col gap-4 wide-mobile:gap-2 bg-light dark:bg-mode-base p-6 rounded-sm">
                <div class="flex gap-4 wide-mobile:gap-2 items-center">
                  <span
                      class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                      <span class="text-base font-semibold leading-none font-manrope">MRR</span>
                      <tooltip title="MRR">
                          <button
                              title="Info Here"
                              class="inline-flex items-center justify-center w-3.5 aspect-square shrink-0 text-current text-xs"
                          >
                              <i class="xcloud xc-info"></i>
                          </button>
                      </tooltip>
                  </span>
                </div>
                <h3 class="text-2xl leading-none font-semibold font-manrope text-dark dark:text-white">$12805</h3>
              </div>

              <div class="flex flex-col gap-4 wide-mobile:gap-2 bg-light dark:bg-mode-base p-6 rounded-sm">
                <div class="flex gap-4 wide-mobile:gap-2 items-center">
                  <span
                      class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                      <span
                          class="text-base font-semibold leading-none font-manrope">Subscriptions</span>
                      <tooltip title="Subscriptions">
                          <button
                              title="Info Here"
                              class="inline-flex items-center justify-center w-3.5 aspect-square shrink-0 text-current text-xs"
                          >
                              <i class="xcloud xc-info"></i>
                          </button>
                      </tooltip>
                  </span>
                </div>
                <h3 class="text-2xl leading-none font-semibold font-manrope text-dark dark:text-white">2</h3>
              </div>

              <div class="flex flex-col gap-4 wide-mobile:gap-2 bg-light dark:bg-mode-base p-6 rounded-sm">
                <div class="flex gap-4 wide-mobile:gap-2 items-center">
                  <span class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <span class="text-base font-semibold leading-none font-manrope">Next Billing</span>
                  </span>
                </div>
                <h3 class="text-2xl leading-none font-semibold font-manrope text-dark dark:text-white">$12805</h3>
              </div>

              <div class="flex flex-col gap-4 wide-mobile:gap-2 bg-light dark:bg-mode-base p-6 rounded-sm">
                <div class="flex gap-4 wide-mobile:gap-2 items-center">
                  <span class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <span class="text-base font-semibold leading-none font-manrope">Websites</span>
                  </span>
                </div>
                <h3 class="text-2xl leading-none font-semibold font-manrope text-dark dark:text-white">3</h3>
              </div>

              <div class="flex flex-col gap-4 wide-mobile:gap-2 bg-light dark:bg-mode-base p-6 rounded-sm">
                <div class="flex gap-4 wide-mobile:gap-2 items-center">
                  <span class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                      <span class="text-base font-semibold leading-none font-manrope">Avg. Billing Per Site</span>
                  </span>
                </div>
                <h3 class="text-2xl leading-none font-semibold font-manrope text-dark dark:text-white">$12805</h3>
              </div>
            </div>
          </div>-->

          <!--<div class="flex flex-col gap-4 wide-mobile:gap-2 w-full">
            <div class="flex items-center flex-wrap gap-2 wide-mobile:gap-1">
              <div class="flex flex-col gap-2 wide-mobile:gap-1">
                <h4 class="text-lg font-medium leading-none">Packages</h4>
                <p class="text-xs leading-none text-secondary-full dark:text-mode-secondary-dark">
                  Here you can find all package information
                </p>
              </div>
              <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
                <SelectorTitleDropdown position="right" selected-item="Sort By">
                  <template v-if="false">
                    <SelectItem
                        v-if="true"
                        title="View Sites"
                        @onItemSelected="() => {}"
                    />
                  </template>
                  <template v-else>
                    <SelectItem
                        title="View Server"
                        @onItemSelected="() => {}"
                    />
                  </template>
                </SelectorTitleDropdown>
                <SelectorTitleDropdown position="right" selected-item="Sort By">
                  <template v-if="false">
                    <SelectItem
                        v-if="true"
                        title="View Sites"
                        @onItemSelected="() => {}"
                    />
                  </template>
                  <template v-else>
                    <SelectItem
                        title="View Server"
                        @onItemSelected="() => {}"
                    />
                  </template>
                </SelectorTitleDropdown>
              </div>
            </div>
            <div class="overflow-x-auto flex flex-col">
              <div class="inline-block min-w-full">
                <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                  <thead>
                  <tr>
                    <th scope="col"
                        class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">
                      Plan Name
                    </th>
                    <th scope="col"
                        class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">
                      Billing Type
                    </th>
                    <th scope="col"
                        class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">
                      SKU
                    </th>
                    <th scope="col"
                        class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">
                      Price
                    </th>
                    <th scope="col"
                        class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">
                      End Date
                    </th>
                    <th scope="col"
                        class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">
                      Status
                    </th>
                    <th scope="col"
                        class="py-3 px-6 text-center text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">
                      Actions
                    </th>
                  </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-mode-base">
                  <tr v-for="i in 10" :key="i"
                      class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                      Elite Premium
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                      Yearly
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                      XC025
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                      $3615
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                      18-07-2024
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                                <span
                                                    class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-success-full/10 text-xs leading-none text-success-full focus:outline-0">
                                                    <span>Active</span>
                                                </span>
                    </td>
                    <td class="whitespace-nowrap py-3 px-6 text-center text-sm font-normal text-dark dark:text-white">
                      <ServerActions
                          server="server"
                          position="right"
                          :show_border="false"
                          :can_add_site="true"
                          :can_archive_server="true"
                          :can_restart_server="true"
                          :can_restart_nginx_server="true"
                          :can_restart_mysql_server="true"
                          :can-delete-server="true"
                          @onChangeDropDown="() => {}"
                      />
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="ml-auto flex items-center gap-2">
              <button disabled
                      class="flex items-center justify-center h-10 aspect-square shrink-0 text-xs text-center text-primary-light uppercase align-middle transition-all rounded-lg select-none hover:bg-primary-light hover:text-white active:bg-primary-light active:text-white disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                      type="button">
                <i class="xcloud xc-angle_left"></i>
              </button>
              <div class="flex items-center gap-2">
                <button
                    class="relative h-10 aspect-square shrink-0 select-none rounded-lg bg-primary-light text-center align-middle text-base font-medium uppercase text-white shadow-md shadow-gray-900/10 transition-all hover:shadow-lg hover:shadow-gray-900/20 focus:opacity-[0.85] focus:shadow-none active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                    type="button">
                                    <span class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                                        1
                                    </span>
                </button>
                <button
                    class="relative h-10 aspect-square shrink-0 select-none rounded-lg text-center align-middle font-sans text-base font-medium uppercase text-primary-light transition-all hover:bg-primary-light/10 active:bg-primary-light/10 disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                    type="button">
                                    <span class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                                        2
                                    </span>
                </button>
                <button
                    class="relative h-10 aspect-square shrink-0 select-none rounded-lg text-center align-middle font-sans text-base font-medium uppercase text-primary-light transition-all hover:bg-primary-light/10 active:bg-primary-light/10 disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                    type="button">
                                    <span class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                                        3
                                    </span>
                </button>
                <button
                    class="relative h-10 aspect-square shrink-0 select-none rounded-lg text-center align-middle font-sans text-base font-medium uppercase text-primary-light transition-all hover:bg-primary-light/10 active:bg-primary-light/10 disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                    type="button">
                                    <span class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                                        4
                                    </span>
                </button>
                <button
                    class="relative h-10 aspect-square shrink-0 select-none rounded-lg text-center align-middle font-sans text-base font-medium uppercase text-primary-light transition-all hover:bg-primary-light/10 active:bg-primary-light/10 disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                    type="button">
                                    <span class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2">
                                        5
                                    </span>
                </button>
              </div>
              <button
                  class="flex items-center justify-center h-10 aspect-square shrink-0 text-xs text-center text-primary-light uppercase align-middle transition-all rounded-lg select-none hover:bg-primary-light hover:text-white active:bg-primary-light active:text-white disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
                  type="button">
                <i class="xcloud xc-angle_right"></i>
              </button>
            </div>
          </div>-->

            <div class="flex flex-col gap-4 wide-mobile:gap-2 w-full">
                <div class="flex items-center flex-wrap gap-2 wide-mobile:gap-1">
                    <div class="flex flex-col gap-2 wide-mobile:gap-1">
                        <h4 class="text-lg font-medium leading-none">{{ $t('Invoices of Clients') }}</h4>
                        <p class="text-xs leading-none text-secondary-full dark:text-mode-secondary-dark">{{ $t('Here you can check all your previous invoices') }}</p>
                    </div>
                    <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
                        <SelectorTitleDropdown :selected-item="invoiceDateFilter">
                            <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                                <div class="p-2px">
                                    <SelectItem
                                        title="All Invoices"
                                        :is-active="invoiceDateFilter === 'All Invoices'"
                                        @onItemSelected="onInvoiceDateFilterChanged($event)"
                                    />
                                    <SelectItem
                                        title="This Month"
                                        :is-active="invoiceDateFilter === 'This Month'"
                                        @onItemSelected="onInvoiceDateFilterChanged($event)"
                                    />
                                    <SelectItem
                                        title="Last Month"
                                        :is-active="invoiceDateFilter === 'Last Month'"
                                        @onItemSelected="onInvoiceDateFilterChanged($event)"
                                    />
                                    <SelectItem
                                        title="Last 6 Months"
                                        :is-active="invoiceDateFilter === 'Last 6 Months'"
                                        @onItemSelected="onInvoiceDateFilterChanged($event)"
                                    />
                                    <SelectItem
                                        title="Last 1 Year"
                                        :is-active="invoiceDateFilter === 'Last 1 Year'"
                                        @onItemSelected="onInvoiceDateFilterChanged($event)"
                                    />
                                </div>
                            </div>
                        </SelectorTitleDropdown>

                        <SelectorTitleDropdown :selected-item="invoiceStatusFilter" custom-class="!w-52">
                            <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                                <div class="p-2px">
                                    <SelectItem
                                        title="Paid Invoices"
                                        :is-active="invoiceStatusFilter === 'Paid Invoices'"
                                        @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                    />
                                    <SelectItem
                                        title="Unpaid Invoices"
                                        :is-active="invoiceStatusFilter === 'Unpaid Invoices'"
                                        @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                    />
                                    <SelectItem
                                        title="Failed Invoices"
                                        :is-active="invoiceStatusFilter === 'Failed Invoices'"
                                        @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                    />
                                    <SelectItem
                                        title="All Invoices"
                                        :is-active="invoiceStatusFilter === 'All Invoices'"
                                        @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                    />
                                </div>
                            </div>
                        </SelectorTitleDropdown>
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="inline-block min-w-full">
                        <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                            <thead>
                            <tr>
                                <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Date') }}</th>
                                <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Invoice No') }}</th>
                                <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Amount') }}</th>
                                <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Title') }}</th>
                                <!--<th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">Server</th>-->
                                <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Status') }}</th>
                                <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Actions') }}</th>
                            </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-mode-base">
                            <tr v-for="invoice in invoices?.data" :key="invoice?.id" class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">{{ invoice?.date }}</td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                                    {{ invoice?.invoice_number ?? invoice?.reference_no }}
                                    <CopyButton
                                        position-class=""
                                        :content="invoice?.invoice_number ?? invoice?.reference_number"
                                        align="top"
                                        color="primary"
                                        :hideCopyText="true"
                                    />
                                </td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">{{ invoice?.currency_symbol + invoice?.amount }}</td>
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">{{ invoice?.title }}</td>
                                <!--<td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark"></td>-->
                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                    <span v-if="invoice?.status === 'refunded' || invoice?.status === 'paid'"
                                          class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full">
                                      {{ invoice?.status_readable }}
                                    </span>
                                    <span
                                        v-else-if="invoice?.status === 'cancelled' || invoice?.status === 'failed' || invoice?.status === 'payment_failed'"
                                        class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10 group-hover:bg-danger">
                                                  {{ invoice?.status_readable }}
                                    </span>
                                    <span v-else
                                          class="text-warning group-hover:text-white px-6 py-1.5 rounded-3xl bg-warning/10 group-hover:bg-warning">
                                                  {{ invoice?.status_readable }}
                                    </span>
                                </td>

                                <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                    <div class="flex items-center justify-center">
                                        <InvoiceActions
                                            :invoice="invoice"
                                            :isStaging="isStaging"
                                            :isAdmin="isAdmin"
                                            :isImpersonating="isImpersonating"
                                            position="right"
                                            :show_border="true"
                                            @onChangeDropDown="() => {}"
                                        />
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <Pagination :links="invoices?.links"/>
            </div>
        </div>
      </div>
    </div>
  </single-white-label>
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import ServerActions from "@/Pages/WhiteLabel/Components/ServerActions.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import SelectorTitleDropdown from "@/Pages/WhiteLabel/Components/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import Modal from "@/Shared/Modal.vue";
import {ref} from "vue";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import Pagination from "@/Shared/Pagination.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import {Inertia} from "@inertiajs/inertia";
import Button from "@/Jetstream/Button.vue";
import InvoiceActions from "@/Pages/WhiteLabel/Components/InvoiceActions.vue";

const props = defineProps({
    invoices: Object,
    invoiceDateFilter:{
        type: String,
        default: 'All Invoices'
    },
    invoiceStatusFilter:{
        type: String,
        default: 'Paid Invoices'
    },
    isStaging: {
        type: Boolean,
        default: false
    },
    isAdmin: {
        type: Boolean,
        default: false
    },
    isImpersonating: {
        type: Boolean,
        default: false
    },
});

const invoiceDateFilter = ref(props?.invoiceDateFilter);
const invoiceStatusFilter = ref(props?.invoiceStatusFilter);

const onInvoiceDateFilterChanged = (filter) => {
    invoiceDateFilter.value = filter;
    loadFilteredData();
}

const onInvoiceStatusFilterChanged = (filter) => {
    invoiceStatusFilter.value = filter;
    loadFilteredData();
}

function loadFilteredData(type = 'created_at'){
    Inertia.get(route("white-label.billing.index"), {
        invoiceDateFilter: invoiceDateFilter.value,
        invoiceStatusFilter: invoiceStatusFilter.value,
        filterType: type,
    });
}
</script>

<style scoped>

</style>
