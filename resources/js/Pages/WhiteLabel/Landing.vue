<template>
    <LandingLayout :whiteLabel="whiteLabel" :landing_page_navbar_photo_url="landing_page_navbar_photo_url" :products="products">
        <!-- Heading Section -->
        <header class="flex flex-col">
            <div class="py-6 px-5">
                <div class="landing-container">
                    <div class="grid grid-cols-2 tablet:grid-cols-1 gap-x-10 gap-y-5">
                        <div class="flex self-center flex-col gap-5 wide-tablet:gap-3.5">
                            <h1 class="text-[4rem] wide-tablet:text-[2.25rem] font-semibold leading-[1.2em] text-[#26242E]" v-text="whiteLabel.settings?.landing_page?.hero_section_heading ?? 'Fast, Secure and Reliable Cloud Hosting!' "></h1>
                            <p class="text-lg wide-tablet:text-sm font-normal leading-normal text-[#5A5862]" v-text="whiteLabel.settings?.landing_page?.hero_section_subheading ??
                                       'Launch your dream WordPress website with hassle-free setup, lightning-fast speeds, \n'+
                                       'and advanced round-the-clock security.'"></p>
                            <a :href="whiteLabel.settings?.landing_page?.hero_section_button_url ? whiteLabel.settings?.landing_page?.hero_section_button_url : route('register')" class="mt-4 self-start inline-flex items-center justify-center gap-2 wide-tablet:gap-1.5 h-[3.25rem]
                                      wide-tablet:h-10 px-6 wide-tablet:px-4 py-1 rounded-lg bg-[#8C6CFB] text-white text-base
                                      wide-tablet:text-sm font-medium leading-tight hover:cursor-pointer">
                                <span>{{ whiteLabel.settings?.landing_page?.hero_section_button_text ?? 'Get Started' }}</span>
                                <i class="xcloud xc-right-arrow text-sm wide-tablet:text-xs"></i>
                            </a>
                        </div>
                        <div class="flex self-center flex-col gap-3">
                            <img :src="asset('white-label/white-label-landing-promo.png')" alt="promo" class="w-full max-w-full">
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <main class="pt-10 wide-tablet:pt-7 flex flex-col">
            <div class="py-10 wide-tablet:py-7 px-5">
                <div class="landing-container">
                    <div class="flex flex-col gap-y-16 wide-tablet:gap-y-12">
                        <div class="flex flex-col items-center text-center gap-4">
                            <h3 class="text-5xl wide-tablet:text-[1.875rem] font-semibold leading-[1.2em] text-[#26242E] max-w-[22ch]">{{ $t('Experience Effortless Hosting With Powerful Features') }}</h3>
                            <p class="text-lg wide-tablet:text-base font-normal leading-normal text-[#5A5862] max-w-[60ch]">{{ $t('Enjoy the lightning performance backed by powerful features and experience hassle-free hosting') }}</p>
                        </div>
                        <div class="grid grid-cols-4 wide-tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-6">
                            <div class="flex flex-col gap-4 shrink-0">
                                <img :src="asset('white-label/white-label-landing-icon-1.png')" alt="promo" class="w-16 wide-tablet:w-14 aspect-square">
                                <h4 class="text-2xl wide-tablet:text-xl font-semibold leading-[1.4em] text-[#26242E]">{{ $t('Effortless Server Operations') }}</h4>
                                <p class="text-base wide-tablet:text-sm font-normal leading-normal text-[#5A5862]">{{ $t('Manage your server effortlessly with our intuitive dashboard. Enjoy high performance and reliability without worrying about downtime, regardless of your technical skill level.') }}</p>
                            </div>
                            <div class="flex flex-col gap-4 shrink-0">
                                <img :src="asset('white-label/white-label-landing-icon-2.png')" alt="promo" class="w-16 wide-tablet:w-14 aspect-square">
                                <h4 class="text-2xl wide-tablet:text-xl font-semibold leading-[1.4em] text-[#26242E]">{{ $t('Easy Website Management') }}</h4>
                                <p class="text-base wide-tablet:text-sm font-normal leading-normal text-[#5A5862]">{{ $t('We offer a comprehensive platform for managing WordPress sites with ease. With our intuitive dashboard and advanced features, you can manage your sites without any hassle.') }}</p>
                            </div>
                            <div class="flex flex-col gap-4 shrink-0">
                                <img :src="asset('white-label/white-label-landing-icon-3.png')" alt="promo" class="w-16 wide-tablet:w-14 aspect-square">
                                <h4 class="text-2xl wide-tablet:text-xl font-semibold leading-[1.4em] text-[#26242E]">{{ $t('Powerful Security Measures') }}</h4>
                                <p class="text-base wide-tablet:text-sm font-normal leading-normal text-[#5A5862]">{{ $t('Our advanced security features ensure the safety of your data through advanced protective measures. Also, automated regular updates keep everything safe from threats and remains protected.') }}</p>
                            </div>
                            <div class="flex flex-col gap-4 shrink-0">
                                <img :src="asset('white-label/white-label-landing-icon-4.png')" alt="promo" class="w-16 wide-tablet:w-14 aspect-square">
                                <h4 class="text-2xl wide-tablet:text-xl font-semibold leading-[1.4em] text-[#26242E]">{{ $t('Real-time Resources Monitoring') }}</h4>
                                <p class="text-base wide-tablet:text-sm font-normal leading-normal text-[#5A5862]">{{ $t('Monitoring your site and server is essential for ensuring optimal performance and reliability. With real-time measures, you can quickly identify issues provide a seamless experience to your customers.') }} </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="py-20 wide-tablet:py-14 px-5">
                <div class="landing-container">
                    <div class="flex flex-col gap-y-16 wide-tablet:gap-y-12">
                        <div class="flex flex-col items-center text-center gap-4">
                            <h3 class="text-5xl wide-tablet:text-[1.875rem] font-semibold leading-[1.2em] text-[#26242E] max-w-[22ch]">
                                {{ $t('Transparent & Flexible Pricing for Everyone') }} </h3>
                            <p class="text-lg wide-tablet:text-base font-normal leading-normal text-[#5A5862] max-w-[60ch]">
                                {{ $t('Explore our range of plans designed to meet every needs of every web creator') }} </p>
                        </div>
                        <div class="grid grid-cols-4 wide-tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-6">
                            <div v-for="product in products" :key="product.id" class="flex flex-col p-6 gap-6 shrink-0 border border-solid border-[#E4DFF8]
                                      hover:border-[#8C6CFB] transition-colors ease-in-out duration-150 rounded-lg">
                                <div class="flex flex-col gap-4">
                                    <p class="text-lg wide-tablet:text-base font-medium leading-[1.4] text-[#5A5862]">
                                        {{ product.title }} </p>
                                    <h4 class="text-5xl wide-tablet:text-[2.5rem] font-semibold leading-[1.2em] text-[#26242E]">
                                        ${{ product.price }}<small class="text-2xl wide-tablet:text-xl font-medium leading-[1.2em] text-[#5A5862]">/mo</small>
                                    </h4>
                                </div>
                                <a :href="product.checkout_url" target="_blank" class="inline-flex items-center justify-center gap-2 wide-tablet:gap-1.5 h-[2.625rem] wide-tablet:h-9 px-4 wide-tablet:px-3 py-1 rounded-lg bg-[#8C6CFB] text-white text-base wide-tablet:text-sm font-medium leading-tight">
                                    <span>{{ $t('Select Plan') }}</span>
                                </a>
                                <div class="flex flex-col gap-2" v-if="product.description">
                                    <p class="text-base wide-tablet:text-sm font-medium leading-[1.4em] text-[#92909A]">{{ $t('Includes') }}:</p>
                                    <ul class="flex flex-col gap-1">
                                        <li v-for="description in product.description?.split('\n')" :key="description" class="py-1 flex items-center gap-2">
                                            <i class="xcloud xc-confirm text-xl wide-tablet:text-lg text-[#AF97FF]"></i>
                                            <span class="text-lg wide-tablet:text-base leading-[1.4] font-medium text-[#26242E]">
                                              {{ description }}
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- CTA Section -->
            <div class="py-2 px-5">
                <div class="landing-container">
                    <div class="flex flex-col items-center justify-center text-center gap-6 p-[5.5rem] wide-tablet:p-12
                                rounded-[2rem] bg-[#8C6CFB] bg-cover bg-no-repeat bg-bottom" :style="{ backgroundImage: `url(${asset('white-label/white-label-landing-bg.png')})` }">
                        <div class="flex flex-col items-center text-center gap-4 text-white" style="font-family: 'Roboto', sans-serif">
                            <h3 class="text-5xl wide-tablet:text-[1.875rem] font-bold leading-[1.2em]" v-text="whiteLabel.settings?.landing_page?.cta_section_heading ?? 'Start Building Today & Enjoy a Reliable Cloud Hosting'"></h3>
                            <p class="text-lg wide-tablet:text-base font-normal leading-normal max-w-[60ch]" v-text="whiteLabel.settings?.landing_page?.cta_section_subheading ?? 'Create your server, deploy unlimited sites, and enjoy a lightning-fast performance effortlessly'"></p>
                        </div>
                        <a :href="whiteLabel.settings?.landing_page?.cta_section_button_url ? whiteLabel.settings?.landing_page?.cta_section_button_url : route('register')" class="inline-flex items-center justify-center gap-2 wide-tablet:gap-1.5 h-[3.25rem]
                                      wide-tablet:h-11 px-6 wide-tablet:px-4 py-1 rounded-lg text-[#8C6CFB] bg-white text-base
                                      wide-tablet:text-sm font-medium leading-tight hover:cursor-pointer">
                            <span>{{ whiteLabel.settings?.landing_page?.cta_section_button_text ?? 'Create Now' }}</span>
                            <i class="xcloud xc-right-arrow text-sm wide-tablet:text-xs"></i>
                        </a>
                    </div>
                </div>
            </div>
        </main>
        <!-- Footer -->

    </LandingLayout>
</template>

<script setup>
import {Link} from "@inertiajs/inertia-vue3";
import 'primeicons/primeicons.css';
import {computed} from "vue";
import LandingLayout from "@/Pages/WhiteLabel/LandingLayout.vue";
import {asset} from "laravel-vapor";

const props = defineProps({
    whiteLabel: Object,
    landing_page_navbar_photo_url: String,
    products: Object
});

// get current year using js
const currentYear = new Date().getFullYear();

const showCtaSection = () => {
    return props.whiteLabel.settings?.landing_page?.cta_section_heading ||
        props.whiteLabel.settings?.landing_page?.cta_section_subheading;
}
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

html {
    font-size: 16px!important;
}
.landing-container {
    max-width: 100%;
    width: 1320px;
    margin-left: auto;
    margin-right: auto;
}
@media only screen and (max-width: 1023.98px) {
    .landing-container {
        width: 730px;
    }
}
</style>
