<template>
    <single-server :server="server" active="Cron Jobs">
        <Head title="Create Cron Job"/>
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">

            <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center wide-mobile:px-15px gap-20px mobile:gap-1 justify-between">

                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                        {{ $t('Add Cron Job') }}
                    </h4>
                </div>

                <div
                    class="p-30px wide-mobile:p-25px mobile:p-4 h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                    <form @submit.prevent="submit" class="h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                        <input type="submit" class="hidden">
                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.command"
                                        :error="form.errors.command"
                                        type="text"
                                        :label="$t('Command')+ '*'"
                                        :note="$t('You can add wget and curl commands here. But if you want to add wp-cli commands then you need to specify the site location with cd /var/www/sitename && wp command.')"
                                        autocomplete="off"
                                        placeholder="wget https://example.com/backup.sh"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.user"
                                        :error="form.errors.user"
                                        type="text"
                                        :label="$t('User')+ '*'"
                                        autocomplete="off"
                                        placeholder="root"
                                        :note="$t('By default you can use')+` <strong>`+$t('root')+`</strong> `+$t('user. But if you want to do it for specific site enter the specific site user name here.')"
                            />
                        </div>

                        <div class="grid grid-cols-1 wide-tablet:grid-cols-1">
                            <label for="frequency" class="block text-base w-1/2 font-medium mb-2.5 text-secondary-full dark:text-light leading-none">{{ $t('Frequency') }} *</label>
                            <template v-for="(frequency_list, index) in frequency_lists" :key="index">
                                <radio-input v-model="form.frequency"
                                             :error="form.errors.frequency"
                                             autocomplete="off"
                                             class="w-1/2"
                                             :value="index">
                                    {{ frequency_list }}
                                </radio-input>
                            </template>
                        </div>

                        <div v-if="form.frequency === 'custom'" class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.pattern"
                                        :error="form.errors.pattern"
                                        type="text"
                                        :label="$t('Custom Schedule')+ '*'"
                                        autocomplete="off"
                                        placeholder="* * * * *"
                            />
                        </div>

                        <div class="flex">
                            <Btn @click.prevent="submit" type="submit" :loading="form.processing">
                                {{ $t('Save') }}
                            </Btn>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </single-server>
</template>

<script setup>
import SingleServer from "@/Pages/Server/SingleServer.vue";
import TextInput from '@/Shared/TextInput.vue';
import {useForm} from '@inertiajs/inertia-vue3';
import Box from "@/Shared/Box.vue";
import Btn from "@/Shared/Btn.vue";
import RadioInput from "@/Shared/RadioInput.vue";

let props = defineProps({
    server: Object,
    frequency_lists: [Array, Object],
})

const form = useForm({
    command: '',
    user: '',
    frequency: Object.keys(props.frequency_lists)[0],
    pattern: ''
});

const submit = () => {
    form.post(route('api.server.cron.job.store', props.server.id));
}
</script>
