<template>
    <single-server
        :can-delete-server="can_delete_server"
        :can_add_site="can_add_site"
        :can_restart_mysql_server="can_restart_mysql_server"
        :can_restart_nginx_server="can_restart_nginx_server"
        :can_restart_server="can_restart_server"
        :server="server"
        active="PHP Configuration"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">

            <div class="grid grid-cols-1 gap-30px mobile:gap-20px">
                <Card
                    :disable-use-button="form.processing"
                    :loading="form.processing"
                    :use-button="php_settings != null"
                    button-style="success"
                    :title="$t('Update PHP Configuration')"
                    @onSubmit="updatePhpSettings"
                >
                    <skeleton v-if="php_settings == null || form.processing"
                              :columns="1"
                              :has-header="false"
                              :rows="7"
                              column-classes="mb-5 mt-5"
                              row-div-classes="h-5"
                    >

                    </skeleton>
                    <template v-else>
                        <select-input
                            id="php_version"
                            v-model="form.php_version"
                            :error="form.errors.php_version"
                            :label="$t('PHP Version')"
                            :note="$t('Select your site\'s PHP version to update php settings on the sites.')"
                            @change="changePhpVersion">
                            <option v-for="(php_setting) in Object.keys(php_settings)" :value="php_setting">
                                {{ php_setting }}
                            </option>
                        </select-input>

                        <TextInput id="max_execution_time" v-model="form.max_execution_time"
                                   :error="form.errors.max_execution_time"
                                   :label="$t('Max Execution Time')" text-on-right="SECONDS" type="text"
                                   :note="$t('The maximum time in seconds a script is allowed to run before it is terminated by the parser. We recommend 60 seconds.')"
                        />

                        <TextInput id="max_input_time" v-model="form.max_input_time"
                                   :error="form.errors.max_input_time"
                                   :label="$t('Max Input Time')" text-on-right="SECONDS" type="text"
                                   :note="$t('The maximum time in seconds a script is allowed to parse input data, like POST and GET. We recommend 60 seconds.')"
                        />

                        <TextInput id="max_input_vars" v-model="form.max_input_vars"
                                   :error="form.errors.max_input_vars"
                                   :label="$t('Max Input Vars')" text-on-right="VARS" type="text"
                                   :note="$t('The maximum number of input variables allowed per request. We recommend 1000 vars.')"
                        />

                        <TextInput id="memory_limit"
                                   v-model="form.memory_limit"
                                   :error="form.errors.memory_limit"
                                   :label="$t('Memory Limit')" text-on-right="MB" type="number"
                                   :note="$t('The maximum amount of memory a script may consume. We recommend 256MB.')"
                        />

                        <TextInput id="post_max_size" v-model="form.post_max_size"
                                   :error="form.errors.post_max_size"
                                   :label="$t('Post Max Size')" text-on-right="MB" type="text"
                                   :note="$t('The maximum size of POST data that PHP will accept. We recommend 128MB.')"
                        />

                        <TextInput id="max_file_upload_size" v-model="form.upload_max_filesize"
                                   :error="form.errors.upload_max_filesize"
                                   :label="$t('Max File Upload Size')" text-on-right="MB" type="text"
                                   :note="$t('The maximum size of an uploaded file. We recommend 128MB.')"
                        />

                        <TextInput id="session_gc_maxlifetime"
                                   v-model="form.session_gc_maxlifetime"
                                   :error="form.errors.session_gc_maxlifetime"
                                   :label="$t('Session GC Maxlifetime')" text-on-right="SECONDS" type="text"
                                   :note="$t('The maximum time in seconds a session is allowed to be idle before it is terminated by the session garbage collector. We recommend 1440 seconds.')"
                        />

                        <div>
                            <div>
                                <Switch :checked="form.opcache_enabled" @click.prevent="form.opcache_enabled = !form.opcache_enabled" no>
                                    {{ form.opcache_enabled ? $t('PHP OPCache Enabled') : $t('PHP OPCache Disabled') }}
                                </Switch>
                            </div>

                            <p class="text-sm text-secondary-full dark:text-mode-secondary-light mt-1">
                                {{ $t('Enabling PHP OPCache will highly improve performance by storing precompiled scripts (PHP Code) in shared memory.') }}
                            </p>
                        </div>
                    </template>
                </Card>
            </div>

            <div class="grid grid-cols-1 gap-30px mobile:gap-20px">
                <Card
                    :disable-use-button="defaultPhpFrom.processing"
                    :loading="defaultPhpFrom.processing"
                    button-style="success"
                    :title="$t('Default PHP Version')"
                    @onSubmit="updateDefaultPhpSettings"
                >
                    <select-input
                        id="default_php_version"
                        v-model="defaultPhpFrom.php_version"
                        :error="defaultPhpFrom.errors.php_version"
                        :label="$t('Default Server PHP Version')"
                        :note="$t('This will set the default PHP version for the CLI and for new site installations. However, it won\'t affect the PHP versions of any existing sites, and the default version for new sites can still be changed during installation.')"
                    >
                        <option v-for="(php_setting) in available_php_versions" :value="php_setting">
                            {{ php_setting }}
                        </option>
                    </select-input>
                </Card>

                <Card
                    :disable-use-button="defaultNodeFrom.processing"
                    :loading="defaultNodeFrom.processing"
                    button-style="success"
                    :title="$t('Default Node.js Version')"
                    @onSubmit="updateDefaultNodeSettings"
                >
                        <select-input
                            id="default_node_version"
                            v-model="defaultNodeFrom.node_version"
                            :error="defaultNodeFrom.errors.node_version"
                            :label="$t('Default Server Node.js Version')"
                            :note="$t('This will set the default Node.js version for the server. Node.js is managed using nvm (Node Version Manager).')"
                        >
                            <option v-for="(node_version) in available_node_versions" :value="node_version">
                                {{ node_version }}
                            </option>
                        </select-input>
                </Card>
            </div>
        </div>
    </single-server>
</template>

<!-- script -->
<script setup>
import Card from "@/Shared/Card.vue";
import TextInput from "@/Shared/TextInput.vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import {Inertia} from "@inertiajs/inertia";
import {useFlash} from "@/Composables/useFlash";
import {onMounted, ref, watch} from "vue";
import SelectInput from "@/Shared/SelectInput.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Switch from "@/Shared/Switch.vue";

const {server, availablePhpVersions, server_php_version, available_node_versions, server_node_version} = defineProps({
    server: Object,
    can_add_server: Boolean,
    can_delete_server: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
    available_php_versions: Array,
    server_php_version: String,
    available_node_versions: Array,
    server_node_version: String,
});

const php_settings = ref(null);

const form = useForm({
    php_version: null,
    upload_max_filesize: null,
    max_execution_time: null,
    max_input_time: null,
    memory_limit: null,
    max_input_vars: null,
    post_max_size: null,
    session_gc_maxlifetime: null,
    opcache_enabled: false,
});

const defaultPhpFrom = useForm({
    php_version: server_php_version,
});

const defaultNodeFrom = useForm({
    node_version: server_node_version,
});

// watch(() => form.post_max_size, (newValue) => {
//     if (form.post_max_size < 0) {
//         return;
//     }
//
//     if(form.upload_max_filesize >= form.post_max_size) {
//         form.upload_max_filesize = form.post_max_size / 0.8;
//     }
// });

onMounted(() => {
    axios.get(route('api.server.php-settings', server.id)).then((response) => {
        php_settings.value = response.data;
        form.php_version = server_php_version ? server_php_version : Object.keys(php_settings.value)[0];
        form.upload_max_filesize = parseInt(php_settings.value[form.php_version].upload_max_filesize);
        form.max_execution_time = php_settings.value[form.php_version].max_execution_time;
        form.max_input_time = php_settings.value[form.php_version].max_input_time;
        form.memory_limit = parseInt(php_settings.value[form.php_version].memory_limit);
        form.max_input_vars = parseInt(php_settings.value[form.php_version].max_input_vars);
        form.post_max_size = parseInt(php_settings.value[form.php_version].post_max_size);
        form.session_gc_maxlifetime = parseInt(php_settings.value[form.php_version].session_gc_maxlifetime);
        form.opcache_enabled = php_settings.value[form.php_version].opcache_enabled === true;
    });
});

const updatePhpSettings = () => {
    form.put(route('server.php.settings.update', server.id), {
        onSuccess: () => {
            //update php settings
            php_settings.value[form.php_version].upload_max_filesize = form.upload_max_filesize;
            php_settings.value[form.php_version].max_execution_time = form.max_execution_time;
            php_settings.value[form.php_version].max_input_time = form.max_input_time;
            php_settings.value[form.php_version].memory_limit = form.memory_limit;
            php_settings.value[form.php_version].post_max_size = form.post_max_size;
            php_settings.value[form.php_version].max_input_vars = form.max_input_vars;
            php_settings.value[form.php_version].session_gc_maxlifetime = form.session_gc_maxlifetime;
            php_settings.value[form.php_version].opcache_enabled = form.opcache_enabled;
            useFlash().success('PHP Settings updated successfully');
        }
    });
}
const updateDefaultPhpSettings = () => {
    defaultPhpFrom.post(route('api.server.php.default', server.id));
}

const updateDefaultNodeSettings = () => {
    defaultNodeFrom.post(route('api.server.node.default', server.id));
}

const changePhpVersion = (event) => {
    form.php_version = event.target.value;
    form.upload_max_filesize = parseInt(php_settings.value[event.target.value]?.upload_max_filesize);
    form.max_execution_time = php_settings.value[event.target.value]?.max_execution_time;
    form.max_input_time = php_settings.value[event.target.value]?.max_input_time;
    form.max_input_vars = php_settings.value[event.target.value]?.max_input_vars;
    form.memory_limit = parseInt(php_settings.value[event.target.value]?.memory_limit);
    form.post_max_size = parseInt(php_settings.value[event.target.value]?.post_max_size);
    form.session_gc_maxlifetime = parseInt(php_settings.value[event.target.value]?.session_gc_maxlifetime);
    form.opcache_enabled = php_settings.value[event.target.value]?.opcache_enabled === true;
}
</script>

<!-- style -->
<style scoped>

</style>
