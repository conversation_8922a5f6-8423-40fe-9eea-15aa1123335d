<template>
    <single-server :server="server" active="Cron Jobs">
        <Head :title="`Update Cron Job ${cron_job.user}`"/>
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">

            <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center wide-mobile:px-15px gap-20px mobile:gap-1 justify-between">

                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                        {{ $t('Update Cron Job') }} {{ cron_job.user }}
                    </h4>
                </div>

                <div
                    class="p-30px wide-mobile:p-25px mobile:p-4 h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                    <form @submit.prevent="submit" class="h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                        <input type="submit" class="hidden">
                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.command"
                                        :error="form.errors.command"
                                        type="text"
                                        placeholder=""
                                        :label="$t('Command') + '*'"
                                        autocomplete="off"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.user"
                                        :error="form.errors.user"
                                        type="text"
                                        :label="$t('User')+ '*'"
                                        autocomplete="off"
                            />
                        </div>

                        <div class="grid grid-cols-1 wide-tablet:grid-cols-1">
                            <label for="frequency" class="block text-base w-1/2 font-medium mb-2.5 text-secondary-full dark:text-light leading-none">Frequency *</label>
                            <template v-for="(frequency_list, index) in frequency_lists" :key="index">
                                <radio-input v-model="form.frequency"
                                             :error="form.errors.frequency"
                                             autocomplete="off"
                                             class="w-1/2"
                                             :value="index">
                                    {{ frequency_list }}
                                </radio-input>
                            </template>
                        </div>

                        <div v-if="form.frequency === 'custom'" class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.pattern"
                                        :error="form.errors.pattern"
                                        type="text"
                                        :label="$t('Custom Schedule')+ '*'"
                                        autocomplete="off"
                            />
                        </div>

                        <div class="flex">
                            <Btn @click.prevent="submit" type="submit" :loading="form.processing">
                                {{ $t('Update') }}
                            </Btn>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </single-server>
</template>

<script setup>
import {ref} from "vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import TextInput from '@/Shared/TextInput.vue';
import {useForm} from '@inertiajs/inertia-vue3';
import Box from "@/Shared/Box.vue";
import Btn from "@/Shared/Btn.vue";
import RadioInput from "@/Shared/RadioInput.vue";

let props = defineProps({
    server: Object,
    frequency_lists: Array,
    cron_job: Object,
})

const form = useForm({
    command: props.cron_job.command,
    user: props.cron_job.user,
    frequency: props.cron_job.frequency,
    pattern: props.cron_job.pattern
});

const submit = () => {
    form.put(route('api.server.cron.job.update', [props.server.id, props.cron_job.id]));
}
</script>
