<template>
    <div class="xc-container">
        <div class="flex-1 flex items-center"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'">
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div
                    class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark dark:text-white
                          mb-20px wide-mobile:text-3xl mobile:text-28px tablet:mb-15px wide-mobile:mb-2.5">
                        {{ $t('Set Up Your Server With') }}
                        <img :src="asset('img/logo_digitalOcean.svg')" alt="digitalOcean_logo"
                             class="h-50px tablet:h-40px wide-mobile:h-30px inline-flex -mt-1 ml-2"/>
                    </h2>
                    <p class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center mb-50px mobile:mb-25px">
                        {{ $t('Connect xCloud with your Digital Ocean account') }}
                    </p>
                    <form>
                        <div class="grid grid-cols-3 tablet:grid-cols-2 mobile:grid-cols-2 gap-30px tablet:gap-y-25px">
                            <text-input
                                id="label"
                                type="text"
                                autocomplete="label"
                                :label="$t('Provider Label')"
                                :placeholder="$t('Label')"
                                v-model="provider.name"
                                readonly
                            >
                            </text-input>

                            <div class="mt-auto">
                                <button class="flex-1 flex justify-center items-center text-xl font-normal text-white
                                        min-h-60px p-2 px-25px w-full rounded-md bg-success-full" disabled>
                                    <i class="xcloud mr-15px flex xc-confirm"></i>
                                    {{ $t('Connected') }}
                                </button>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px mt-30px">
                            <text-input
                                id="server_name"
                                v-model="form.name"
                                :error="form.errors.name"
                                :type="'text'"
                                autocomplete="name"
                                :label="$t('Server Name')"
                                :placeholder="$t('Server Name')">
                            </text-input>
                            <select-input
                                id="server_size"
                                v-model="currentServer"
                                :error="form.errors.size"
                                :label="$t('Server Size')"
                                :placeholder="$t('Server Size')">
                                <option selected disabled :value="Object">{{ $t('Choose Server') }}</option>
                                <optgroup v-bind:label="description"
                                          v-for="(dropletList, description) in serverDBDetailsDO.droplets">
                                    <option v-for="droplet in dropletList" :value="droplet"
                                            v-text="droplet.title"></option>
                                </optgroup>
                            </select-input>
                            <select-input
                                id="region"
                                v-model="form.region"
                                :error="form.errors.region"
                                :label="$t('Region')"
                                :placeholder="$t('Region')">
                                <option selected disabled value="">{{ $t('Choose Region') }}</option>
                                <option v-for="(region, slug) in currentServer.region" :value="slug"
                                        v-text="region"></option>
                            </select-input>


                            <select-input
                                id="database_type"
                                v-model="form.database_type"
                                :error="form.errors.database_type"
                                :label="$t('Select Database Server')"
                                :placeholder="$t('Select Database Server')">
                                <option
                                    :value="_database_type.value"
                                    v-for="_database_type in database_type_list"
                                    v-text="_database_type.label"
                                    :disabled="_database_type?.disabled">
                                </option>
                            </select-input>

                            <div>
                                <label
                                       class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none">
                                    {{ $t('Tags') }}
                                </label>
                                <multiselect
                                    :class="{ 'ring-1 ring-danger-light': tagsErrors()}"
                                    id="tags"
                                    v-model="form.tags"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    :placeholder="$t('Select or create tags')"
                                    :options="tagList"
                                    regex="^[a-zA-Z0-9_ -]+$"
                                />
                                <Error :error="tagsErrors()"/>
                            </div>

                        </div>
                            <StackInput v-model="form.stack" :error="form.errors.stack"/>


                      <div class="mt-5 flex flex-col gap-y-1">
                          <div v-if="currentServer.backupCost">
                              <label>
                                  <input type="checkbox" class="hidden peer" v-model="form.backups" :checked="form.backups"/>
                                  <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                                                 before:border-1 before:border-secondary-light before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                                                 before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                                 before:transition before:duration-200 peer-checked:before:border-success-full
                                                 peer-checked:before:bg-success-full peer-checked:before:text-white">
                                      <span class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                                          {{ $t('Enable Digital Ocean') }}
                                          <a href="https://docs.digitalocean.com/products/images/backups/" target="_blank"
                                             class="underline dark:decoration-mode-secondary-dark ml-1">{{ $t('Auto Backups') }}</a>&nbsp;
                                          (+${{ currentServer.backupCost }}/month)
                                      </span>
                                  </span>
                              </label>
                          </div>
                        <label>
                          <input type="checkbox" class="hidden peer" v-model="form.consent"/>
                          <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                                             before:border-1 before:border-secondary-light  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                                             before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                             before:transition before:duration-200 peer-checked:before:border-success-full
                                             peer-checked:before:bg-success-full peer-checked:before:text-white"
                                :class="{'before:border-danger' : form.errors.consent}"
                          >
                                  <span class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                                      {{ $t('I have understood that the billing of this server will be handled on my server provider account.') }}
                                  </span>
                              </span>
                        </label>
                        <div class="mt-2 text-sm dark:text-light text-red-600 flex gap-2 items-center" v-if="form.errors.consent">
                            <img :src="asset('img/eeror.svg')" alt="warning_img" class="h-4"/>
                            <span class="mt-1 text-red-600">
                              {{ form.errors.consent }}
                            </span>
                        </div>
                          <label v-if="can_create_demo">
                              <input type="checkbox" class="hidden peer" v-model="form.demo"/>
                              <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                                             before:border-1 before:border-secondary-light  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                                             before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                             before:transition before:duration-200 peer-checked:before:border-success-full
                                             peer-checked:before:bg-success-full peer-checked:before:text-white">
                              <span
                                  class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                                 {{ $t('Demo Server for Billing Plan') }}
                              </span>
                          </span>
                          </label>
                      </div>
                    </form>
                </div>

                <WizardProgress :processing="form.processing" :back="route('server.choose.credential', 'digitalocean')" @next="submit"
                                :progress-width="progressWidth(2,3)" progress-text="Step 2 of 3">
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                        <span>{{ $t('Verifying') }}</span>
                    </template>
                    <template v-else>
                        <span>{{ $t('Next') }}</span>
                    </template>
                </WizardProgress>
            </div>
        </div>
    </div>
</template>

<script setup>
import {computed, ref} from "vue";
import TextInput from '@/Shared/TextInput.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import PasswordInput from '@/Shared/PasswordInput.vue'
import WizardProgress from '@/Shared/WizardProgress.vue'
import {useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";
import {useHelpStore} from "@/stores/HelpStore";
import {useNavigationStore} from "@/stores/NavigationStore";
import Multiselect from '@vueform/multiselect';
import Label from "@/Jetstream/Label.vue";
import Switch from "@/Shared/Switch.vue";
import Error from "@/Shared/Error.vue";
import StackInput from "@/Shared/StackInput.vue";



const {provider, database_type, tags, database_type_list} = defineProps({
    provider: Object,
    helpers: {
        type: Object,
        default: {}
    },
    tags: {
        type: Array,
        required: true,
    },
    serverDBDetailsDO: Object,
    database_type_list: Object,
    database_type: String,
    can_create_demo:{
        type: Boolean,
        default: false
    }
});

let navigation = useNavigationStore();
let helper = useHelpStore();

let currentServer = ref(Object);
let databaseSizeNode = ref(Object);

const progressWidth = (start, end) => (start * 100) / (end ?? 1);

let form = useForm({
    name: '',
    size: currentServer.slug,
    database_type: database_type,
    database_size: databaseSizeNode.size,
    database_node: databaseSizeNode.node,
    stack: 'nginx',
    region: '',
    tags: [],
    provider : provider.id,
    backups: false,
    consent: false,
    demo: false,
});

const tagList = computed(() => {
    return tags.map((tag) =>  ({
            label: (tag?.counter>1) && !form.tags.includes(tag.value) ?  tag.label+' ('+tag?.counter+')' : tag.label,
            value: tag.value,
        })
    )
})


let submit = () => {
    form.size = currentServer.value.slug;
    form.post(route('api.server.store.do', {cloudProvider: provider.id}), {
        preserveScroll: true,
    });
}
const tagsErrors = function() {
    const errors = [];

    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
};
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
