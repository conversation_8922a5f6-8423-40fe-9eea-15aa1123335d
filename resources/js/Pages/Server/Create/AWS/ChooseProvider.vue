<template>
    <div class="xc-container">
        <div class="flex-1 flex items-center"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'">
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div
                    class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2
                        class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark h-20
                               dark:text-white mb-20px wide-mobile:text-3xl mobile:text-28px tablet:mb-15px wide-mobile:mb-2.5">
                        {{ $t('Set Up Your Server With') }}
                        <img v-if="nightMode" :src="asset('img/aws_dark_mode.svg')" alt="AWS logo"
                             class="h-60px tablet:h-40px wide-mobile:h-30px inline-flex -mt-1 ml-2"/>
                        <img v-else :src="asset('img/aws.svg')" alt="AWS logo"
                             class="h-60px tablet:h-40px wide-mobile:h-30px inline-flex -mt-1 ml-2"/>
                    </h2>
                    <p class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center mb-50px mobile:mb-25px">
                        {{ $t('Connect xCloud with your AWS Account') }}
                    </p>

                    <div
                        class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px mb-10">

                        <select-input
                            @change="chooseAccessKey()"
                            v-model="provider"
                            :label="$t('Select Existing or Connect New')">
                            <option selected value="">{{ $t('Choose Account') }}</option>
                            <option v-for="(prov, key) in providers" :value="key">
                                {{ prov.name }}
                            </option>
                            <option v-if="can_add_provider" value="new">{{ $t('Connect New Account') }}</option>
                        </select-input>
                    </div>

                    <AWSServices v-model="form.service_names" :error="form.errors.service_names"/>

                    <div
                        class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px mb-10">

                        <text-input
                            v-bind:disabled="provider !== 'new' || !can_add_provider"
                            type="text"
                            :label="$t('Label for AWS Credential')"
                            :placeholder="$t('Label')"
                            :error="form.errors.label"
                            v-model="form.label">
                        </text-input>


                        <password-input
                            icon=""
                            v-bind:disabled="provider !== 'new' || !can_add_provider"
                            type="text"
                            :label="$t('AWS Access Key')"
                            :placeholder="$t('Access Key')"
                            :error="form.errors.aws_access_key"
                            v-model="form.aws_access_key">
                        </password-input>

                        <password-input
                            icon=""
                            v-bind:disabled="provider !== 'new' || !can_add_provider"
                            type="text"
                            :label="$t('AWS Secret Key')"
                            :placeholder="$t('Secret Key')"
                            :error="form.errors.aws_secret_key"
                            v-model="form.aws_secret_key">
                        </password-input>

                    </div>
                    <div
                        class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px my-10">
                        <button
                            v-if="!awsVerified"
                            @click.prevent="verifyAws"
                            class="flex-1 flex justify-center items-center text-xl font-normal min-h-60px p-2
                                       px-25px w-full rounded-md border-1 cursor-pointer hover:bg-success-full
                                       hover:text-white disabled:bg-success-full disabled:text-white bg-white
                                       border-green-500 text-success-full transition-all">
                            <i class="xcloud mr-15px flex xc-verify_dns"
                               v-bind:class="form.processing ? 'animate-spin' : ''"></i>
                            {{ form.processing ? $t('Verifying ...') : $t('Verify') }}
                        </button>
                        <button v-else class="flex-1 flex justify-center items-center text-xl font-normal text-white
                                    min-h-60px p-2 px-25px w-full rounded-md bg-success-full">
                            <i class="xcloud mr-15px flex xc-confirm"></i>
                            {{ $t('Verified') }}
                        </button>
                    </div>
                  <div class="flex justify-center">
                      <suggestion v-if="!can_add_provider" :message="$t('You do not have permission to add new provider')"/>
                  </div>

                </div>

                <WizardProgress :processing="form.processing" :back="route('server.create')" @next="submit"
                                :hasNextButton="awsVerified"
                                :progress-width="progressWidth(2,3)" progress-text="Step 2 of 3">
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                        <span>{{ $t('Verifying') }}</span>
                    </template>
                    <template v-else>
                        <span>{{ $t('Next') }}</span>
                    </template>
                </WizardProgress>
            </div>
        </div>
    </div>
</template>

<script setup>
import {ref, watch} from "vue";
import TextInput from '@/Shared/TextInput.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import WizardProgress from '@/Shared/WizardProgress.vue'
import {useForm} from "@inertiajs/inertia-vue3";
import {useHelpStore} from "@/stores/HelpStore";
import {useNavigationStore} from "@/stores/NavigationStore";
import {Inertia} from "@inertiajs/inertia";
import Suggestion from "@/Pages/Clone/Components/Suggestion.vue";
import PasswordInput from "@/Shared/PasswordInput.vue";
import StackInput from "@/Shared/StackInput.vue";
import AWSServices from "@/Pages/Server/Create/AWS/AWSServices.vue";
import {useFlash} from "@/Composables/useFlash";

const props = defineProps({
    cloud_provider_name: String,
    provider: String,
    providers: Object,
    helpers: {
        type: Object,
        default: {}
    },
    can_add_provider: Boolean
});

let navigation = useNavigationStore();
let helper = useHelpStore();

const nightMode = navigation.nightMode

let awsVerified = ref(false);
let providers = ref(props.providers);
let provider = ref(props.provider ? props.provider : '');

const progressWidth = (start, end) => (start * 100) / (end ?? 1);

let form = useForm({
    label: '',
    service_names: [],
    aws_access_key: '',
    aws_secret_key: '',
    provider_id: '',
    cloud_provider_name: props.cloud_provider_name
});

watch(() => provider.value, (provider) => {
    if (provider === 'new') {
        form.clearErrors();
        awsVerified.value = false;
    }
});

let chooseAccessKey = () => {
    if (provider.value === 'new') {
        form.label = '';
        form.service_names = [];
        form.aws_access_key = '';
        form.aws_secret_key = '';
        form.provider_id = '';
    } else {
        form.label = providers.value[provider.value].name;
        form.service_names = providers.value[provider.value].service_names;
        form.aws_access_key = '************************';
        form.aws_secret_key = '************************';
        form.provider_id = providers.value[provider.value].id;
        verifyAws();
    }
}

let verifyAws = () => {
    form.post(route('api.cloudProvider.validate.credential'), {
        preserveScroll: true,
        onSuccess: (response) => {
        },
        onError: (error) => {
            awsVerified.value = error?.verified;
        }
    });
}

const submit = () => {
    Inertia.get(route('server.create.provider', {
        cloudProvider: form.provider_id
    }));
}

</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
