<template>
    <div class="my-30px tablet:my-30px">
        <div>
            <label for="services" class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none">{{ label }}</label>

            <p class="text-sm text-secondary-full dark:text-mode-secondary-light mb-4">
                {{ description }}
            </p>

            <div id="services" class="grid grid-cols-[repeat(auto-fill,minmax(24rem,1fr))] gap-6 md:gap-5">
                <label class="w-full flex">
                    <input class="hidden peer" :type="inputType" :value="'ec2'" v-model="selectedServices" :disabled="!isEnabled('ec2')">
                    <div :class="['bg-light dark:bg-mode-base rounded-lg border border-solid w-full h-full', {'cursor-not-allowed opacity-50': !isEnabled('ec2')}]" class="peer-checked:border-primary-light relative before:absolute before:h-4 before:aspect-square before:rounded-full before:top-6 md:before:top-5 before:right-6 md:before:right-5 before:border-[0.09375rem] before:border-solid before:border-secondary-light dark:before:border-mode-focus-light peer-checked:before:border-primary-light before:bg-transparent before:content-['\e927'] before:font-xc before:inline-flex before:items-center before:justify-center before:text-[6px] before:text-transparent peer-checked:before:text-primary-light flex flex-col cursor-pointer">
                        <div class="p-6 md:p-5 flex items-start gap-4 md:gap-3">
                            <img :src="asset('img/aws_ec2.svg')" alt="AWS EC2 Logo" class="w-12 md:w-10 aspect-square shrink-0 rounded-lg inline-flex object-cover">
                            <div class="flex flex-col gap-2.5 mr-5">
                                <h4 class="text-xl wide-mobile:text-lg leading-tight font-medium text-dark dark:text-white">EC2</h4>
                                <p class="text-sm leading-tight text-secondary-full dark:text-mode-secondary-light mb-2">
                                    {{ $t('Flexible, scalable virtual servers for all workloads.') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </label>
                <label class="w-full flex">
                    <input class="hidden peer" :type="inputType"  :value="'lightsail'" v-model="selectedServices" :disabled="!isEnabled('lightsail')">
                    <div :class="['bg-light dark:bg-mode-base rounded-lg border border-solid w-full h-full', {'cursor-not-allowed opacity-50': !isEnabled('lightsail')}]" class="peer-checked:border-primary-light relative before:absolute before:h-4 before:aspect-square before:rounded-full before:top-6 md:before:top-5 before:right-6 md:before:right-5 before:border-[0.09375rem] before:border-solid before:border-secondary-light dark:before:border-mode-focus-light peer-checked:before:border-primary-light before:bg-transparent before:content-['\e927'] before:font-xc before:inline-flex before:items-center before:justify-center before:text-[6px] before:text-transparent peer-checked:before:text-primary-light flex flex-col cursor-pointer">
                        <div class="p-6 md:p-5 flex items-start gap-4 md:gap-3">
                            <img :src="asset('img/aws_lightsail.svg')" alt="AWS Lightsail Logo" class="w-12 md:w-10 aspect-square shrink-0 rounded-lg inline-flex object-cover">
                            <div class="flex flex-col gap-2.5 mr-5">
                                <h4 class="text-xl wide-mobile:text-lg leading-tight font-medium text-dark dark:text-white">Lightsail</h4>
                                <p class="text-sm leading-tight text-secondary-full dark:text-mode-secondary-light mb-2">
                                    {{ $t('Easy, budget-friendly servers for small projects.') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </label>
            </div>
        </div>

        <div class="text-sm text-red-600 block" v-if="error">{{ error }}</div>
    </div>
</template>

<script setup>
import { asset } from "laravel-vapor";
import { ref, watch } from "vue";

const emit = defineEmits(['update:modelValue']);

let props = defineProps({
    'modelValue': [Array, String],
    'error': String,
    'inputType': {
        type: String,
        default: 'checkbox',
    },
    'enabledServices': {
        type: Array,
        default: () => ['ec2', 'lightsail'],
    },
    'label': {
        type: String,
        default: 'Choose AWS Services',
    },
    'description': {
        type: String,
        default: 'Select one or more AWS services for which you want to use this credentials.',
    },
});

let selectedServices = ref(props.inputType === 'radio' ? props.modelValue : (props.modelValue || []));
let updating = false;

watch(selectedServices, (newVal, oldVal) => {
    if (!updating && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        updating = true;
        emit('update:modelValue', props.inputType === 'radio' ? selectedServices.value : [...selectedServices.value]);
        updating = false;
    }
}, { immediate: true });

watch(() => props.modelValue, (newVal) => {
    if (!updating && JSON.stringify(newVal) !== JSON.stringify(selectedServices.value)) {
        updating = true;
        selectedServices.value = newVal;
        updating = false;
    }
}, { immediate: true });

const isEnabled = (service) => {
    return props.enabledServices.includes(service);
};
</script>
