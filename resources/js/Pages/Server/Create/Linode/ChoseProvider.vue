<template>
    <div class="xc-container">
        <div class="flex-1 flex items-center"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'">

            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div
                    class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark
                            dark:text-white mb-20px wide-mobile:text-3xl mobile:text-28px tablet:mb-15px wide-mobile:mb-2.5">
                        {{ $t('Set Up Your Server With') }}
                        <img :src="asset('img/akamai.png')" alt="Linode (Akamai) Logo"
                             class="h-50px tablet:h-40px wide-mobile:h-30px inline-flex -mt-1 ml-2"/>
                    </h2>
                    <p class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center mb-50px mobile:mb-25px">
                        {{ $t('Connect xCloud with your Linode (Akamai) account') }}
                    </p>
                    <form>
                        <div>
                            <div class="flex justify-center flex-wrap items-center tablet:grid tablet:grid-cols-1 gap-30px wide-tablet:gap-20px mb-10">
                                <select-input
                                    v-if="Object.keys(providers).length > 0"
                                    :label="$t('Existing Linode Credential')"
                                    class="min-w-[250px]"
                                    v-model="selectedProvider">
                                    <option value="">Choose Credential</option>
                                    <option :value="provider.id" v-for="provider in providers">{{ provider.name }}</option>
                                </select-input>

                                <div class="flex justify-center items-center"
                                     v-if="Object.keys(providers).length > 0 && can_add_provider"
                                >
                                    <p class="text-center pt-20px tablet:pt-0 dark:text-white">Or</p>
                                </div>
                                <div v-if="can_add_provider">
                                    <label v-if="Object.keys(providers).length > 0"
                                           class="block text-sm font-medium mb-2.5 text-secondary-full leading-none">
                                        &nbsp;{{ $t('Add new credential') }}
                                    </label>
                                    <a :href="route('oauth.redirect', 'linode')"
                                       class="flex-1 bg-primary-light flex justify-center items-center text-xl wide-tablet:text-base font-medium
                                            text-white min-h-14 px-30px py-15px w-full rounded-md">
                                        <i class="xcloud mr-15px wide-tablet:mr-10px flex xc-link"></i>
                                        {{ $t('Authorize on Linode (Akamai)') }}
                                    </a>
                                </div>
                            </div>
                            <div class="flex justify-center">
                                <suggestion v-if="!can_add_provider" :message="$t('You do not have permission to add new provider')"/>
                                <suggestion v-else :message="formattedMessage"/>
                            </div>
                        </div>
                    </form>
                </div>

                <WizardProgress :processing="form.processing" :back="route('server.create')" @next="submit"
                                :has-next-button="Object.keys(providers).length > 0"
                                :progress-width="progressWidth(2,3)" progress-text="Step 2 of 3">
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                        <span>{{ $t('Verifying') }}</span>
                    </template>
                    <template v-else>
                        <span>{{ $t('Next') }}</span>
                    </template>
                </WizardProgress>
            </div>
        </div>
    </div>
</template>

<script setup>
import {ref} from "vue";
import SelectInput from '@/Shared/SelectInput.vue'
import WizardProgress from '@/Shared/WizardProgress.vue'
import {Link, useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";
import {useHelpStore} from "@/stores/HelpStore";
import {useNavigationStore} from "@/stores/NavigationStore";
import {Inertia} from "@inertiajs/inertia";
import Suggestion from "@/Pages/Clone/Components/Suggestion.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();


const {providers} = defineProps({
    providers: Object,
    helpers: {
        type: Object,
        default: {}
    },
    can_add_provider: Boolean,
});

let navigation = useNavigationStore();
let helper = useHelpStore();

const formattedMessage = '<a href="https://www.linode.com/lp/refer/?r=581217e373089abfc2c263e16b9da9ecc2077bd4" target="_blank" class="underline">'+t('Click here')+'</a> '+t('to claim')+' <span class="font-bold">'+t('$100 free credits')+'</span> '+t('if you are a new user on Linode.');

let selectedProvider = ref('');

const progressWidth = (start, end) => (start * 100) / (end ?? 1);


let form = useForm({
    label: '',
    api_token: '',
});

let submit = () => {
    if (selectedProvider.value) {
        Inertia.get(route('server.create.provider', {cloudProvider: selectedProvider.value}));
    } else {
        useFlash().error('Please Select or Create a Linode credential.');
    }
}
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
