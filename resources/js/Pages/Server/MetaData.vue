<template>
    <single-server
        active="Metadata"
        :server="server"
        :can-delete-server="can_delete_server"
        :can_add_site="can_add_site"
        :can_restart_server="can_restart_server"
        :can_restart_nginx_server="can_restart_nginx_server"
        :can_restart_mysql_server="can_restart_mysql_server"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">

            <Card
                @onSubmit="updateInformation"
                :title="$t('Information')"
                buttonStyle="success"
            >
                <TextInput :label="$t('Server Name')" v-model="informationForm.name" :error="informationForm.errors.name"
                           :type="'text'"/>

                <TextInput
                    v-if="!$page?.props?.current_white_label"
                    :label="$t('Provider Name')" v-model="informationForm.provider_name"
                           :error="informationForm.errors.provider_name" disabled
                           :type="'text'"/>
                <TextInput :label="$t('Plan')"  disabled :model-value="server?.plan_title"/>

                <TextInput :label="$t('Server Size')" v-model="informationForm.server_size"
                           :error="informationForm.errors.server_size" disabled
                           :type="'text'"/>

                <TextInput :label="$t('Ubuntu Version')"  disabled :model-value="server?.ubuntu_version"/>

                <TextInput :label="$t('PHP Version')"  disabled :model-value="server?.php_version"/>
                <TextInput :label="$t('MySQL Version')"  disabled :model-value="8"/>

                <TextInput :label="$t('Region')" :model-value="`${server?.location} (${server?.region})`" disabled/>

                <div>
                    <label
                        class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none">
                        {{ $t('Tags') }}
                    </label>

                    <multiselect
                        class="mt-3 !ml-0 flex !min-w-full"
                        v-model="informationForm.tags"
                        :class="{'ring-1 ring-danger-light': tagsErrors()}"
                        mode="tags"
                        :close-on-select="false"
                        :searchable="true"
                        :create-option="true"
                        @select="onTagSelect"
                        :placeholder="$t('Select or create tags')"
                        :options="tagList"
                        regex="^[a-zA-Z0-9_ -]+$"
                    />
                    <Error :error="tagsErrors()"/>
                </div>
            </Card>

            <Card
                @onSubmit="updateNotes"
                :title="$t('Notes')"
            >
                <TextareaInput :label="$t('Note')" v-model="notesForm.notes" :error="notesForm.errors.notes"/>
            </Card>
        </div>
    </single-server>

</template>
<script setup>
import Card from "@/Shared/Card.vue";
import TextInput from "@/Shared/TextInput.vue";
import TextareaInput from "@/Shared/TextareaInput.vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import Multiselect from '@vueform/multiselect';
import {useForm} from "@inertiajs/inertia-vue3";
import {computed} from "vue";
import Error from "@/Shared/Error.vue";

const {server, tags,selected_tags} = defineProps({
    server: Object,
    tags: Array,
    selected_tags: Array,
    can_add_server: Boolean,
    can_delete_server: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean
})

let notesForm = useForm({
    notes: server.notes,

});

let informationForm = useForm({
    name: server.name,
    provider_name: server.provider_readable,
    server_size: server.size,
    region: server.region,
    tags: server.tags.map(tag => tag.name)
});

const tagList = computed(() => {
    return tags.map((tag) =>  ({
            label: (tag?.counter>1) && !informationForm.tags.includes(tag.value) ?  tag.label+' ('+tag?.counter+')' : tag.label,
            value: tag.value,
        })
    )
})


function updateInformation() {
    informationForm.post(route('api.server.information', server.id));
}

function updateNotes() {
    notesForm.post(route('api.server.notes', server.id));
}

const onTagSelect = (tag) => {
    //check does tag has contained any special character like #, @, $, %, ^, /, \, |, {, }, [, ], ~
    if (tag.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
        //remove from tag list
        informationForm.tags = informationForm.tags.filter((item) => item !== tag);
    }
}
const tagsErrors = function() {
    const errors = [];

    informationForm.tags.forEach((tag, index) => {
        const error = informationForm.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
};
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
