<template>
    <SingleServer
        active="Database"
        :server="server"
        :can-delete-server="can_delete_server"
        :can_add_site="can_add_site"
        :can_restart_server="can_restart_server"
        :can_restart_nginx_server="can_restart_nginx_server"
        :can_restart_mysql_server="can_restart_mysql_server"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
          <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col wide-tablet:col-span-1">
            <div class="flex items-center gap-20px px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px  wide-mobile:px-15px">
              <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                {{ $t('Database Info') }}
              </h4>
              <button type="button"
                      @click.prevent="getServerDatabasesAndUsers"
                      :disabled="fetchingDBInfo"
                      class="inline-flex items-center rounded-3xl
                                shadow-none px-1 py-1 pr-10px bg-success-light/10 text-sm text-success-light focus:outline-0 gap-1"
                      :class="{'cursor-not-allowed opacity-50' : fetchingDBInfo}"
                      aria-expanded="true" aria-haspopup="true">
                      <span class="rounded-full bg-success-light h-25px w-25px shrink-0 flex items-center justify-center">
                          <i class="xcloud xc-verify_dns text-white"
                             :class="{'animate-spin': fetchingDBInfo}">
                          </i>
                      </span>
                <span>{{ fetchingDBInfo ? $t('Refreshing...') : $t('Refresh') }}</span>
              </button>
              <div class="ml-auto mobile:ml-0">
                  <span class="text-base text-dark dark:text-mode-secondary-light">
                      {{ $t('Last checked') }}: {{ dbInfoLastPulledAt }}
                  </span>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <div class="flex flex-col gap-30px mobile:gap-20px">
                <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                    <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px
                              flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
                        <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                            {{ $t('Database Name') }}
                        </h4>
                        <div v-if="can_create_db" class="flex gap-20px mobile:flex-wrap mobile:gap-10px">
                            <btn
                                :loading="fetchingDBInfo"
                                :disabled="fetchingDBInfo"
                                icon="xcloud xc-add"
                                @click.prevent="openAddDatabaseModal=true"
                                :class="{'cursor-not-allowed opacity-50' : fetchingDBInfo}"
                            >
                              {{ !fetchingDBInfo ? $t('Add Database') : $t('Loading...') }}
                            </btn>
                        </div>
                    </div>
                    <div class="overflow-x-auto w-full" v-if="!fetchingDBInfo">
                        <table class="w-full">
                            <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                            <tr v-if="serverData.databases"
                                class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                v-for="database in serverData.databases">
                                <td class="px-30px flex flex-col py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                  <div class="flex justify-between">
                                    <span>{{ getDatabaseName(database) }}</span>
                                    <span v-if="getDatabaseSize(database)">{{ getDatabaseSize(database) }} MB</span>
                                  </div>
                                  <div :class="['text-sm', getSiteName(database) ? 'text-green-500' : 'text-gray-500']">
                                    <Tooltip
                                        v-if="getSiteID(database)"
                                        title="Go to Site"
                                    >
                                      <Link
                                          :href="getSiteID(database) ? route('site.show', [server.id, getSiteID(database) ?? 0]) : '#'"
                                      >
                                        {{ getSiteName(database) || 'No associated site' }}
                                      </Link>
                                    </Tooltip>

                                    <span v-else class="text-gray-500 hover:text-white">{{ $t('No associated site') }}</span>
                                  </div>
                                </td>
                                <td class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                                  <div class="flex gap-2">
                                    <tooltip
                                        v-if="getSiteID(database)"
                                        class="cursor-pointer ml-1"
                                        :title="$t('Manage Database')"
                                        align="left"
                                    >
                                      <a
                                          :href="route('site.adminer-login', [getSiteID(database)])"
                                          target="_blank"
                                          class="relative group cursor-pointer mt-1.5"
                                      >
                                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                                            <i class="xcloud xc-logout rotate-180"></i>
                                        </span>
                                      </a>
                                    </tooltip>

                                    <Tooltip v-if="database.split('||').shift().trim() != 'xcloud'" align="top"
                                             :title="$t('Delete')" :color="'danger'">
                                      <a href="#"
                                         @click.prevent="selectedDatabase = database.split('||').shift().trim();openDeleteDatabaseModal=true;"
                                         class="relative group cursor-pointer mt-1.5">
                                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                                            <i class="xcloud xc-delete"></i>
                                        </span>
                                      </a>
                                    </Tooltip>
                                  </div>

                                </td>
                            </tr>
                            <tr v-else>
                              <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ $t('No database found!') }}
                              </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <Skeleton v-else :columns="2" :rows="3" />
                </div>
            </div>
            <div class="flex flex-col gap-30px mobile:gap-20px">
                <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                    <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center
                        gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
                        <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                            {{ $t('Database Users') }}
                        </h4>
                        <div v-if="can_create_db" class="flex gap-20px mobile:flex-wrap mobile:gap-10px">
                          <btn
                              icon="xcloud xc-add"
                              :loading="fetchingDBInfo"
                              @click.prevent="openAddUserModal=true"
                              :disabled="fetchingDBInfo"
                              :class="{'cursor-not-allowed opacity-50' : fetchingDBInfo}"
                          >
                            {{ !fetchingDBInfo ? $t('Add Database User') : $t('Loading...') }}
                          </btn>
                        </div>
                    </div>
                    <div class="overflow-x-auto w-full" v-if="!fetchingDBInfo">
                        <table class="w-full">
                            <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                            <tr v-if="serverData.databaseUsers"
                                class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                v-for="(user, index) in serverData.databaseUsers">
                                <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                  {{ user }}
                                </td>
                                <td class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                                    <div class="flex gap-15px">
                                        <Tooltip align="left" :title="$t('Edit')" :color="'success'">
                                          <a href="#"
                                             @click.prevent="getUserDatabases(user, index)"
                                             class="relative group cursor-pointer mt-1.5">
                                                  <span class="text-sm flex text-secondary-light group-hover:text-white">
                                                     <i class="xcloud xc-spinner mr-2.5 animate-spin" v-if="loading[index]"></i>
                                                     <i class="xcloud xc-edit" v-else></i>
                                                  </span>
                                          </a>
                                        </Tooltip>
                                        <Tooltip v-if="user != 'xcloud'" align="left" :title="$t('Delete')" :color="'danger'">
                                          <a href="#"
                                             @click.prevent="selectedDatabaseUser = user;openDeleteDatabaseUserModal=true;"
                                             class="relative group cursor-pointer mt-1.5">
                                                <span class="text-sm flex text-secondary-light group-hover:text-white">
                                                     <i class="xcloud xc-delete"></i>
                                                </span>
                                          </a>
                                        </Tooltip>
                                    </div>
                                </td>
                            </tr>
                            <tr v-else>
                              <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ $t('No database user found!') }}
                              </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <Skeleton v-else :columns="2" :rows="3" />
                </div>
            </div>
        </div>
    </SingleServer>

  <!-- Create database modal -->
  <Modal
      @footerClick="createNewDatabase()"
      @close="openAddDatabaseModal= false"
      :footer-button-title="$t('Add Database')"
        :footer-button="true"
      :loading="dbForm.processing"
      :closeable="true"
      :show="openAddDatabaseModal"
      :widthClass="'max-w-590px'"
      :title="$t('Add a new database to your server')">
      <div class="flex flex-col gap-30px">
          <text-input v-model="dbForm.database_name"
              :error="dbForm.errors.database_name"
              :placeholder="$t('xcloud_db')"
              type="text"
              :label="$t('Database Name')"/>

          <text-input v-model="dbForm.database_user"
                :error="dbForm.errors.database_user"
                :placeholder="$t('Database User Name')"
                type="text"
                :label="$t('User (Optional)')"/>

          <password-input
                :show-password="true"
                v-model="dbForm.database_user_password"
                :generate-random-value="true"
                :error="dbForm.errors.database_user_password"
                placeholder="********************"
                :label="$t('Password (Required with user)')"
          />
      </div>
  </Modal>

  <!-- create user modal -->
  <Modal
      @footerClick="createNewDbUser()"
      @close="openAddUserModal= false; dbUserForm.clearErrors();"
      :footer-button-title="$t('Add Database User')"
        :footerButton="true"
      :loading="dbUserForm.processing"
      :closeable="true"
      :show="openAddUserModal"
      :widthClass="'max-w-590px'"
      :title="$t('Add a new user to your database')">
    <div class="flex flex-col gap-30px">
      <text-input v-model="dbUserForm.database_username"
          :error="dbUserForm.errors.database_username"
          :placeholder="$t('Database User Name')"
          type="text"
          :label="$t('Name')"/>

      <password-input v-model="dbUserForm.database_user_password"
          :generate-random-value="true"
          :error="dbUserForm.errors.database_user_password"
          placeholder="********************"
          :label="$t('Password')"
      />



      <div class="flex flex-col" v-if="serverData.databases">
        <label class="block text-base mb-2.5 text-dark dark:text-white leading-none">
          {{ $t('Can Access') }}
        </label>
        <div class="block gap-4">
          <label class="inline-flex mr-5" v-for="database in serverData.databases">
            <input type="checkbox" class="hidden peer" :value="Array.isArray(database.split('||')) ? database.split('||').shift() : database" v-model="dbUserForm.selected_databases" />
            <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                  before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded
                  before:mt-0.5 before:mr-2.5 before:text-xxxs before:inline-flex before:items-center before:justify-center
                  before:text-transparent before:outline-none before:transition before:duration-200
                  peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white">
                  <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">{{ Array.isArray(database.split('||')) ? database.split('||').shift() : database }}</span>
            </span>
          </label>

          <div class="mt-2 text-sm dark:text-light text-red-600 flex gap-2 items-center" v-if="dbUserForm.errors.selected_databases">
            <img :src="asset('img/eeror.svg')" alt="warning_img" class="h-4"/><span class="mt-1">{{ dbUserForm.errors.selected_databases }}</span>
          </div>
        </div>
      </div>
    </div>
  </Modal>

  <!-- edit user modal -->
  <Modal
      @footerClick="editDBUser()"
      @close="openEditDatabaseUserModal= false;editDbUserForm.clearErrors();"
      :loading="editDbUserForm.processing"
      :footer-button-title="$t('Update')"
        :footerButton="true"
      :closeable="true"
      :show="openEditDatabaseUserModal"
      :widthClass="'max-w-590px'"
      :title="$t('Edit database user:')+' ' + selectedDatabaseUser + '?'"
  >
    <div class="flex flex-col gap-30px">
      <password-input v-model="editDbUserForm.database_user_password"
            :error="editDbUserForm.errors.database_user_password"
            :placeholder="$t('Keep empty to keep the same password')"
            :label="$t('Password')"
      />

      <div class="flex flex-col" v-if="serverData.databases">
        <label class="block text-base mb-2.5 text-dark dark:text-white leading-none">
          {{ $t('Can Access') }}
        </label>
          <div v-if="editDbUserForm.hasErrors && editDbUserForm.errors['selected_databases.0']" class="text-red-600"> {{ editDbUserForm.errors["selected_databases.0"] }}</div>

        <div class="block gap-4">
          <label class="inline-flex mr-5" v-for="(database,index) in serverData.databases">
            <input type="checkbox" class="hidden peer" :value="Array.isArray(database.split('||')) ? database.split('||').shift().trim() : database" v-model="editDbUserForm.selected_databases" />
            <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                  before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded
                  before:mt-0.5 before:mr-2.5 before:text-xxxs before:inline-flex before:items-center before:justify-center
                  before:text-transparent before:outline-none before:transition before:duration-200
                  peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white">
                  <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">{{ Array.isArray(database.split('||'))? database.split('||').shift() : database }}</span>
            </span>
          </label>

          <div class="mt-2 text-sm dark:text-light text-red-600 flex gap-2 items-center" v-if="dbUserForm.errors.selected_databases">
            <img :src="asset('img/eeror.svg')" alt="warning_img" class="h-4"/><span class="mt-1">{{ dbUserForm.errors.selected_databases }}</span>
          </div>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Delete database modal -->
  <Modal
      @close="openDeleteDatabaseModal = false"
      :footer-button-title="$t('Delete')"
        :footerButton="true"
      :show="openDeleteDatabaseModal"
      :loading="deleteDatabaseForm.processing"
      :title="$t('Are You Sure You Want To Delete this database:')+' ' + selectedDatabase + '?'"
      :widthClass="'max-w-850px'">
    <div class="flex flex-col gap-30px">
      <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
        <img :src="asset('img/warning.svg')" alt="warning_img"/>
        <p class="text-sm text-dark dark:text-white leading-7 pl-4">
          <b>{{ $t('Warning') }}:</b> {{ $t('If you proceed, this is permanently removed and you will not be able to access or retrieve it again.') }}
        </p>
      </div>

      <div class="flex flex-col gap-30px">
        <text-input v-model="deleteDatabaseForm.delete_confirmation"
            :error="deleteDatabaseForm.errors.delete_confirmation"
            type="text"
            :label="$t('Type')+` ${selectedDatabase} `+$t('to confirm')"
        />
      </div>
    </div>
    <template #footer>
        <button
            :disabled="!checkDeleteDatabaseInputMatch"
            @click.prevent="deleteDatabase()"
            class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
            aria-expanded="true" aria-haspopup="true">
          <span>{{ $t('Delete') }}</span>
        </button>
    </template>
  </Modal>

  <!-- Delete database user modal -->
  <Modal
      @close="openDeleteDatabaseUserModal = false"
      :footer-button-title="$t('Delete')"
        :footerButton="true"
      :loading="deleteDatabaseUserForm.processing"
      :show="openDeleteDatabaseUserModal"
      :title="$t('Are You Sure You Want To Delete this database user:')+' ' + selectedDatabaseUser + '?'"
      :widthClass="'max-w-850px'">
    <div class="flex flex-col gap-30px">
      <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
        <img :src="asset('img/warning.svg')" alt="warning_img"/>
        <p class="text-sm text-dark dark:text-white leading-7 pl-4">
          <b>{{ $t('Warning') }}:</b> {{ $t('If you proceed, this is permanently removed and you will not be able to access or retrieve it again.') }}
        </p>
      </div>

      <div class="flex flex-col gap-30px">
        <text-input v-model="deleteDatabaseUserForm.delete_confirmation"
              :error="deleteDatabaseUserForm.errors.delete_confirmation"
              type="text"
              :label="$t('Type')+` ${selectedDatabaseUser} `+$t('to confirm')"
        />
      </div>
    </div>
    <template #footer>
        <button
            :disabled="!checkDeleteDatabaseUserInputMatch"
            @click.prevent="deleteDatabaseUser()"
            class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px
                  text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
            aria-expanded="true" aria-haspopup="true">
          <span>{{ $t('Delete') }}</span>
        </button>
    </template>
  </Modal>

</template>

<script setup>
import SingleServer from "@/Pages/Server/SingleServer.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Modal from "@/Shared/Modal.vue";
import TextInput from "@/Shared/TextInput.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import {computed, onMounted, reactive, ref} from "vue";
import PasswordInput from "@/Shared/PasswordInput.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import axios from "axios";
import Btn from "@/Shared/Btn.vue";
import Error from "@/Pages/Error.vue";
import Checkbox from "@/Jetstream/Checkbox.vue";
import {useFlash} from "@/Composables/useFlash";

const props = defineProps({
  server: Object,
  databases: {
    type: Object,

  },
  databaseUsers: {
    type: Object,
  },
  dbInfoLastPulledAt: String,
  can_create_db: Boolean,
  can_add_server: Boolean,
  can_delete_server: Boolean,
  can_add_site: Boolean,
  can_restart_server: Boolean,
  can_restart_nginx_server: Boolean,
  can_restart_mysql_server: Boolean
})

const serverData = reactive({
  databases: [],
  users: []
})

let openAddDatabaseModal = ref(false);
let openAddUserModal = ref(false);
let openDeleteDatabaseModal = ref(false);
let openDeleteDatabaseUserModal = ref(false);
let openEditDatabaseUserModal = ref(false);

let selectedDatabase = ref('');
let selectedDatabaseUser = ref('');

let loading = ref([]);

let fetchingDBInfo = ref(false);

const getDatabaseName = (database) => {
  return database.split('||')[0]?.trim();
}

const getSiteID = (database) => {
  return database.split('||')[1]?.trim();
}

const getSiteName = (database) => {
  return database.split('||')[2]?.trim();
}

const getSiteUrl = (database) => {
  return database.split('||')[3]?.trim();
}

const getDatabaseSize = (database) => {
  return database.split('||')[4]?.trim();
}

const dbForm = useForm({
  database_name: '',
  database_user: '',
  database_user_password: '',
});

const dbUserForm = useForm({
  database_username: '',
  database_user_password: '',
  selected_databases: [],
});

const editDbUserForm = useForm({
  database_user_password: '',
  selected_databases: [],
});

const deleteDatabaseForm = useForm({
  delete_confirmation: ''
});

const deleteDatabaseUserForm = useForm({
  delete_confirmation: ''
});

onMounted(async () => {
  if(props.databases){
    serverData.databases = props.databases;
    serverData.databaseUsers = props.databaseUsers;
  }else{
    await getServerDatabasesAndUsers();
  }

  // loader
  if(props.databaseUsers){
    props.databaseUsers.forEach(() => {
      loading.value.push(false);
    });
  }
})

const getServerDatabasesAndUsers = async () => {
  fetchingDBInfo.value = true;

  try {
    let response = await axios.get(`${route('api.server.databases', {server: props.server.id})}`).then(res => res.data);
    if(response.databases){
      serverData.databases = response.databases;
      serverData.databaseUsers = response.databaseUsers;

      // loader
      if(serverData.databaseUsers){
        serverData.databaseUsers.forEach(() => {
          loading.value.push(false);
        });
      }
    }
    fetchingDBInfo.value = false;
    useFlash().success('Database information updated.')
  }catch (e) {
    console.log(e)
    fetchingDBInfo.value = false;
    useFlash().error('Failed to fetch database information.')
  }
}

const checkDeleteDatabaseInputMatch = computed(() => {
  return deleteDatabaseForm.delete_confirmation === selectedDatabase.value;
})

const checkDeleteDatabaseUserInputMatch = computed(() => {
  return deleteDatabaseUserForm.delete_confirmation === selectedDatabaseUser.value;
})

const createNewDatabase = () => {
  fetchingDBInfo.value = true;
  dbForm.post(route('api.server.database.add', props.server.id), {
    onSuccess: () => {
      dbForm.reset();
      serverData.databases = props.databases;
      serverData.databaseUsers = props.databaseUsers;
      openAddDatabaseModal.value = false;
      fetchingDBInfo.value = false;
    },
    onFinish: () => {
      // when request process is finished (whether succeed or failed)
      fetchingDBInfo.value = false;
    }
  });
}

const createNewDbUser = () => {
  fetchingDBInfo.value = true;
  dbUserForm.post(route('api.server.database.user.add', props.server.id), {
    onSuccess: () => {
      dbUserForm.reset();
      serverData.databases = props.databases;
      serverData.databaseUsers = props.databaseUsers;
      openAddUserModal.value = false;
      fetchingDBInfo.value = false;
    },
    onFinish: () => {
      // when request process is finished (whether succeed or failed)
      fetchingDBInfo.value = false;
    }
  });
}

const getUserDatabases = async (user, index) => {
  selectedDatabaseUser.value = user;
  loading.value[index] = true;

  try {
    let response = await axios.get(`${route('api.server.database.user.get', {
      server: props.server.id,
      user: selectedDatabaseUser.value
    })}`)

    if(response.status === 200){
      loading.value[index] = false;
      openEditDatabaseUserModal.value = true;
      editDbUserForm.selected_databases = response.data.databases;
    }
  } catch (error) {
    console.log(error)
    // Handle errors
    editDbUserForm.reset();
    loading.value[index] = false;
    selectedDatabaseUser.value = null;
    openEditDatabaseUserModal = false;
  }
}

const editDBUser = () => {
  editDbUserForm.post(route('api.server.database.user.edit', [
    props.server.id,
    selectedDatabaseUser.value
  ]), {
    onSuccess: () => {
      editDbUserForm.reset();
      openEditDatabaseUserModal.value = false;
    },
    onFinish: () => {
      // when request process is finished (whether succeed or failed)
    }
  });
}


function deleteDatabase() {
  fetchingDBInfo.value = true;
  deleteDatabaseForm.delete(route('api.server.database.destroy', [props.server.id, selectedDatabase.value]), {
    preserveScroll: true,
    onSuccess: () => {
      deleteDatabaseForm.reset( 'delete_confirmation');
      serverData.databases = props.databases;
      serverData.databaseUsers = props.databaseUsers;

      openDeleteDatabaseModal.value = false;
      fetchingDBInfo.value = false;
    },
    onFinish: () => {
      // when request process is finished (whether succeed or failed)
      fetchingDBInfo.value = false;
    }
  })
}

function deleteDatabaseUser() {
  fetchingDBInfo.value = true;
  deleteDatabaseForm.delete(route('api.server.database.user.destroy', [props.server.id, selectedDatabaseUser.value]), {
    preserveScroll: true,
    onSuccess: () => {
      deleteDatabaseUserForm.reset( 'delete_confirmation');
      serverData.databases = props.databases;
      serverData.databaseUsers = props.databaseUsers;

      openDeleteDatabaseUserModal.value = false;
      fetchingDBInfo.value = false;
    },
    onFinish: () => {
      // when request process is finished (whether succeed or failed)
      fetchingDBInfo.value = false;
    }
  })
}

</script>
