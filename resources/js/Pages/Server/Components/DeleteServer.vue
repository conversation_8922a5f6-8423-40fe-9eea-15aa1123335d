<template>
    <Modal
        @close="openDeleteModal = false"
        :footer-button-title="$t('Delete')"
        :footer-button="true"
        :show="openDeleteModal"
        :title="$t('Are You Sure You Want To Delete Server')+' '+ serverName +'?'"
        :widthClass="'max-w-850px'">
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img"/>
                <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b>{{ $t('Warning') }}:</b> {{ $t('Proceeding will permanently delete all data stored on this server and all WordPress sites under it, which can’t be recovered later. Full Server Backups created for this server will also be deleted.') }}
                </p>
            </div>

            <div class="flex flex-col gap-30px">
                <Switch v-if="!is_xcloud_or_white_label && !is_any_provider"
                        @click.prevent="form.delete_from_provider=!form.delete_from_provider"
                        :checked="form.delete_from_provider">
                    {{ form.delete_from_provider ? $t('Delete From Provider') : $t('Enable this to delete this server from the provider also.') }}
                </Switch>
                <text-input v-model="form.delete_confirmation"
                            :placeholder="$t('Type server name to confirm')"
                            :error="form.errors.delete_confirmation"
                            type="text"
                            :label="$t('Type')+` ${serverName} `+$t('to confirm')"/>
            </div>

            <div v-if="hasCloudflareIntegration">
              <label class="inline-flex mr-5">
                <input type="checkbox" class="hidden peer"
                       :disabled="!hasCloudflareIntegration"
                       v-model="form.delete_dns_records"
                />
                <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                              before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded
                              before:mt-0.5 before:mr-2.5 before:text-xxxs before:inline-flex before:items-center before:justify-center
                              before:text-transparent before:outline-none before:transition before:duration-200
                              peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white"
                >
                  <span class="font-normal text-secondary-full dark:text-white text-sm">
                      <span class="flex" >
                        <span>{{ $t('Delete DNS records from your Cloudflare account') }}</span>
                        <img :src="asset('img/CF_logomark.png')" alt="cloudflare logo" class="ml-1 w-8 h-4">
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                          {{ $t('Your DNS records for the sites on this server will be deleted from your Cloudflare account.') }}
                      </span>
                  </span>
                </span>
              </label>
            </div>
        </div>
        <template #footer>
                <button
                    :disabled="!checkDeleteInputMatch || isDeleting"
                    @click.prevent="deleteServer()"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px
                            px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed
                            disabled:opacity-50"
                    aria-expanded="true" aria-haspopup="true">
                    <span>{{ $t('Delete') }}</span>
                </button>
        </template>
    </Modal>
</template>

<script setup>
import TextInput from '@/Shared/TextInput.vue';
import Modal from "@/Shared/Modal.vue";
import {computed, ref } from "vue";
import {useForm} from "@inertiajs/inertia-vue3";
import Switch from "@/Shared/Switch.vue";
import {asset} from "laravel-vapor";

const {serverName, serverId,provider, hasCloudflareIntegration} = defineProps({
    serverName: String,
    serverId: Number,
    openDeleteModal: Boolean,
    is_xcloud: Boolean,
    is_xcloud_or_white_label: Boolean,
    is_any_provider: Boolean,
    provider: String,
    hasCloudflareIntegration: {
        type: Boolean,
        default: false
    },
});

const emit = defineEmits(['closeModal']);

const isDeleting = ref(false);

const form = useForm({
    delete_from_provider: false,
    delete_confirmation: '',
    delete_dns_records: false
});

//these providers don't need the delete from provider option
const providers = ['xcloud', 'Other Provider', undefined];
// const providers = ['Other Provider', 'Managed Hosting', 'white_label_vultr'];

const checkDeleteInputMatch = computed(() => {
    return form.delete_confirmation === serverName;
})

function deleteServer() {
    isDeleting.value = true;

    form.post(route('api.server.delete', serverId), {
        preserveScroll: true,
        onFinish: () => {
            form.reset('delete_confirmation');
            emit('closeModal');
            isDeleting.value = false;
        },
        onError: () => {
            isDeleting.value = false;
        }
    });
}

</script>
