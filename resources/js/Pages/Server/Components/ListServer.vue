<template>
    <div
        class="group relative grid grid-cols-12 gap-x-20px gap-y-10px bg-white dark:bg-mode-light border-2 border-white dark:border-mode-light hover:drop-shadow-list-box dark:hover:bg-dark px-30px py-20px rounded-10px duration-75 ease-in-out"
        :class="{ 'z-[1]': showDropDown }"
    >
        <div
            class="flex items-center grow col-span-5 wide-tablet:col-span-8 tablet:col-span-12 tablet:pr-30px"
        >
            <div class="flex items-center gap-20px wide-mobile:gap-10px">
                <span class="w-50px wide-mobile:w-40px shrink-0">
                     <Tooltip class="ml-3" :title="server.provider_readable">
                        <img class="w-full h-full" :src="cloudProviderIcon" alt="h-full" />
                     </Tooltip>
                </span>
                <div class="flex flex-col">
                    <h2 class="inline-flex items-center">
                        <Link
                            class="text-3xl wide-tablet:text-2xl wide-mobile:text-xl mobile:text-lg text-dark hover:text-primary-dark dark:text-light dark:hover:text-white leading-none cursor-pointer"
                            :href="
                                server.status !== 'deleting'
                                    ? '/server/' + server.id + '/sites'
                                    : '#'
                            "
                        >
                            {{ $filters.textLimit(server.name) }}
                        </Link>
                        <StateToolTip
                            class="ml-3"
                            :state="server.state"
                            :title="server.status_readable"
                        />
                    </h2>
                    <span
                        class="text-sm text-secondary-full dark:text-mode-secondary-light mt-1.5"
                        >{{ $t('Created') }}: {{ server.created_at_readable }}</span
                    >
                    <div class="flex items-center flex-wrap gap-x-15px gap-y-2">
                        <h6
                            v-if="server?.public_ip"
                            class="flex items-center flex-wrap text-base text-secondary-full dark:text-mode-secondary-light"
                        >
                            <CopyAbleText
                                align="bottom"
                                :text="server?.public_ip"
                            />
                        </h6>
                        <span
                            v-if="server.region"
                            class="flex items-center text-base px-10px py-1 text-secondary-full dark:text-mode-secondary-light rounded-md"
                        >
                            <RegionFlag :region="server.region_flag" />
                        </span>
                        <!-- Server Stack -->
                        <div v-if="server.stack"
                             class="flex px-2 items-center text-base text-secondary-full dark:text-mode-secondary-light  divide-x divide-light group-hover:divide-primary-dark rounded-md">
                            <Tooltip
                                    :title="`${server.stack_readable} Web Server`"
                                    align="top"
                                >
                                    <div class="flex flex-row items-center gap-5px">
                                        <img
                                            :src="{ 'openlitespeed': asset('img/openlitespeed.svg'),'nginx': asset('img/nginx-logo.svg')}[server.stack]"
                                            :alt="server.stack"
                                            class="w-25px"
                                        />
                                        <h6 class="text-secondary-full dark:text-mode-secondary-light leading-none text-sm">
                                            {{ server.stack_readable }}
                                        </h6>
                                    </div>
                                </Tooltip>
                        </div>

                        <div v-if="server.database_type_readable"
                             class="flex px-2 items-center text-base text-secondary-full dark:text-mode-secondary-light
                                    divide-x divide-light group-hover:divide-primary-dark rounded-md"
                        >
                          <Tooltip
                              :title="server.database_type_readable + ' Database Server'"
                              align="top"
                          >
                            <div class="flex flex-row items-center gap-5px">
                              <i class="xcloud"
                                 :class="server.database_type_readable === 'MySQL' ? 'xc-mysql' : 'xc-mariadb'"
                              ></i>
                              <h6 class="text-secondary-full dark:text-mode-secondary-light leading-none text-sm">
                                {{ server.database_type_readable }}
                              </h6>
                            </div>
                          </Tooltip>
                        </div>

                        <span v-if="server.plan_title"
                              class="flex items-center text-base py-1 text-secondary-full dark:text-mode-secondary-light
                                divide-x divide-light
                                group-hover:divide-primary-dark rounded-md">
                           <tooltip :title="server?.plan_title">
                            {{ server?.plan_title?.slice(0, 7) + "..." }}
                            </tooltip>
                        </span>

                        <h6
                            v-if="server.status !== 'deleting'"
                            class="flex items-center text-base text-secondary-full dark:text-mode-secondary-light"
                        >
                            <span
                                class="px-10px py-1 dark:group-hover:bg-mode-focus-dark group-hover:bg-white/10 divide-x divide-light dark:divide-mode-base group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md"
                            >
                                <span v-if="server.sites_count > 0">
                                    {{
                                        server.sites_count > 1
                                            ? server.sites_count + " "+$t('sites')
                                            : server.sites_count + " "+$t('site')
                                    }}
                                </span>
                                <span v-else>{{ $t('No sites') }}</span>
                            </span>
                        </h6>

                        <div
                            v-if="server.tags.length > 0"
                            class="flex items-center"
                        >
                            <span
                                class="text-base tablet:text-sm text-secondary-full dark:text-mode-secondary-light rounded-md"
                            >
                                # {{ server.tags[0]?.name }}
                            </span>

                            <tooltip
                                class="text-xs cursor-pointer ml-2"
                                :title="
                                    server.tags
                                        .slice(1)
                                        .map((tag) => tag.name)
                                        .join(', ')
                                "
                            >
                                <h6
                                    v-if="server.tags.length > 1"
                                    class="text-base text-primary-dark dark:text-primary-light"
                                >
                                    +{{ server.tags.length - 1 }}
                                </h6>
                            </tooltip>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div
            class="grid grid-cols-2 gap-20px col-span-4 wide-tablet:order-1 wide-tablet:col-span-12"
        >
            <div class="flex flex-col justify-center gap-10px grow">
                <span
                    class="flex items-center flex-wrap justify-between text-secondary-full dark:text-mode-secondary-light"
                >
                    <span class="text-sm">{{ $t('Storage') }}</span>
                    <span class="text-xs">
                        <Tooltip
                            align="top"
                            :title="
                                (server.monitoring.disk?.used ?? 0) +
                                ' GB' +
                                ' is used of ' +
                                (server.monitoring.disk?.total ?? 0) +
                                ' GB'
                            "
                        >
                            {{ (server.monitoring.disk?.used ?? 0) + " GB" }} /
                            {{ (server.monitoring.disk?.total ?? 0) + " GB" }}
                        </Tooltip>
                    </span>
                </span>
                <div
                    class="bg-gray-200 rounded-full h-5px dark:bg-gray-700 w-full"
                >
                    <div
                        :style="'width:' + usedStoragePercentage + '%'"
                        :class="
                            server.monitoring.disk?.isLow
                                ? 'bg-[#F4573C]'
                                : 'bg-primary-dark'
                        "
                        class="h-5px rounded-full"
                    ></div>
                </div>
            </div>
            <div class="flex flex-col justify-center gap-10px grow">
                <span
                    class="flex items-center flex-wrap justify-between text-secondary-full dark:text-mode-secondary-light"
                >
                    <span class="text-sm">{{ $t('Ram') }}</span>
                    <span class="text-xs">
                        <Tooltip
                            align="top"
                            :title="useUnitSize((server.monitoring.ram?.used ?? 0),'MB','GB') +
                                ' is used of '+useUnitSize((server.monitoring.ram?.total ?? 0),'MB','GB')"
                        >
                            {{ useUnitSize((server.monitoring.ram?.used ?? 0),'MB','GB') }} /
                            {{ useUnitSize((server.monitoring.ram?.total ?? 0),'MB','GB')}}
                        </Tooltip>
                    </span>
                </span>
                <div
                    class="bg-gray-200 rounded-full h-5px dark:bg-gray-700 w-full"
                >
                    <div
                        :class="
                            server.monitoring.ram?.isLow
                                ? 'bg-[#F4573C]'
                                : 'bg-primary-dark'
                        "
                        class="h-5px rounded-full"
                        :style="'width:' + usedRamPercentage + '%'"
                    ></div>
                </div>
            </div>
        </div>
        <div
            v-if="server.status !== 'deleting'"
            class="flex items-center justify-end gap-30px col-span-3 wide-tablet:col-span-4 tablet:absolute tablet:right-15px tablet:top-15px"
        >
            <Link
                :disabled="server.status !== 'provisioned'"
                :href="server.status !== 'provisioned' ? '#' : route('site.create',  {
                        server: [server.id],
                        type: 'wordpress',
                        from: 'server',
                    })"
                class="min-h-50px inline-flex justify-center items-center border-1 border-primary-light dark:border-dark bg-transparent bg px-30px py-1 rounded-10px text-primary-light dark:text-white font-medium whitespace-nowrap translate duration-75 ease-in-out tablet:hidden"
                :class="{
                    'cursor-not-allowed opacity-50': server.status !== 'provisioned',
                    'dark:group-hover:border-focused/50 hover:bg-primary-light hover:text-white': server.status === 'provisioned',
                }"
            >
                {{ $t('Add New Site') }}
            </Link>
            <div
                class="flex items-center justify-center server-toggle-button pr-10px wide-tablet:pr-0"
            >
                <ServerActions
                    :server="server"
                    position="right"
                    :can_add_site="can_add_site"
                    :can_restart_server="can_restart_server"
                    :can_restart_nginx_server="can_restart_nginx_server"
                    :can_restart_mysql_server="can_restart_mysql_server"
                    :can-delete-server="canDeleteServer"
                    :can_clone_server="can_clone_server"
                    @onChangeDropDown="onChangeDropDown"
                />
            </div>
        </div>
    </div>
</template>

<style>
.server-toggle-button > * {
    @apply text-base h-30px w-30px rounded-md border-1 border-light dark:border-dark flex items-center justify-center
            hover:border-primary-light dark:group-hover:border-focused/50 dark:hover:!border-white !basis-auto;
}
.server-toggle-button > * i.xcloud {
    @apply !text-base;
}
</style>

<script setup>
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import { useCloudProviderIcon } from "@/Composables/useCloudProviderIcon.js";
import ServerActions from "@/Pages/Server/Components/ServerActions.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import StateToolTip from "@/Shared/StateToolTip.vue";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";
import {onMounted, ref} from "vue";
import {useUnitSize} from "@/Composables/useUnitSize";
import {asset} from "laravel-vapor";

const showDropDown = ref(false);

const props = defineProps({
    server: {
        type: Object,
        required: true,
    },
    canDeleteServer: Boolean,
    can_add_site: Boolean,
    can_clone_server: Boolean,
    can_archive_server: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
});

const emit = defineEmits(["server-deleted"]);


onMounted(() => {
    if (window.Echo) {
        window.Echo.private("server.status." + props.server.id).listen(
            "ServerStatusChanged",
            (e) => {
                if (e.status === "deleted") {
                    //send to parent
                    emit("server-deleted", props.server.id);
                    return;
                }
                props.server.status = e.status;
                props.server.status_readable = e.status_readable;
                props.server.state = e.state;
            }
        );
    }
});

const usedRamPercentage =
    isNaN(parseInt(props.server.monitoring.ram?.used)) ||
    isNaN(parseInt(props.server.monitoring.ram?.total))
        ? 0
        : (parseInt(props.server.monitoring.ram?.used) * 100) /
          parseInt(props.server.monitoring.ram?.total || 1);

const usedStoragePercentage =
    isNaN(parseInt(props.server.monitoring.disk?.used)) ||
    isNaN(parseInt(props.server.monitoring.disk?.total))
        ? 0
        : (parseInt(props.server.monitoring.disk?.used) * 100) /
          parseInt(props.server.monitoring.disk?.total || 1);

const { cloudProviderIcon } = useCloudProviderIcon(
    props.server.provider_name
);

const onChangeDropDown = (value) => {
    showDropDown.value = value;
};
</script>
