<template>
    <div class="bg-white dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
        <!-- Top CPU Processes -->
        <div
            class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
            <h4
                class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                {{ $t('Top CPU Usage Processes') }}
            </h4>
            <h6 class="text-base text-secondary-full dark:text-mode-secondary-light" v-if="last_checked">
                {{ $t('Last Updated') }}: {{ last_checked }}
            </h6>
        </div>
        <div class="overflow-x-auto w-full overflow-y-hidden">
            <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('PID') }}
                    </th>
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('User') }}
                    </th>
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('Command') }}
                    </th>
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('CPU') }}%
                    </th>
                </tr>
                </thead>
                <tbody
                    class="bg-white dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                    <tr v-for="process in topProcesses.byCpu" class="divide-x-1 divide-light dark:divide-mode-light hover:bg-light dark:hover:bg-mode-light/30 transition duration-150">
                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                            <span class="bg-primary-light/10 text-primary-dark dark:text-primary-light py-0.5 px-2 rounded">{{ process.pid }}</span>
                        </td>
                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                            <span class="bg-primary-light/10 text-primary-dark dark:text-primary-light py-0.5 px-2 rounded">{{ process.user }}</span>
                        </td>
                        <td class="px-30px py-2 text-left text-base font-normal h-60px font-mono">
                            {{ process.command }}
                        </td>
                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                            <span :class="getCpuUsageClass(process.cpu)" class="py-1 px-2 rounded-md">
                                {{ formatCpuPercentage(process.cpu) }}
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Top RAM Processes -->
        <div
            class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px mt-6">
            <h4
                class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                {{ $t('Top RAM Usage Processes') }}
            </h4>
        </div>
        <div class="overflow-x-auto w-full overflow-y-hidden">
            <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('PID') }}
                    </th>
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('User') }}
                    </th>
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('Command') }}
                    </th>
                    <th class="px-30px py-2 text-left text-base font-normal h-60px">
                        {{ $t('RAM') }}
                    </th>
                </tr>
                </thead>
                <tbody
                    class="bg-white dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                    <tr v-for="process in topProcesses.byRam" class="divide-x-1 divide-light dark:divide-mode-light hover:bg-light dark:hover:bg-mode-light/30 transition duration-150">
                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                            <span class="bg-primary-light/10 text-primary-dark dark:text-primary-light py-0.5 px-2 rounded">{{ process.pid }}</span>
                        </td>
                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                            <span class="bg-primary-light/10 text-primary-dark dark:text-primary-light py-0.5 px-2 rounded">{{ process.user }}</span>
                        </td>
                        <td class="px-30px py-2 text-left text-base font-normal h-60px font-mono">
                            {{ process.command }}
                        </td>
                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                            <span :class="getMemoryUsageClass(process.ram_kb)" class="py-1 px-2 rounded-md">
                                {{ formatMemory(process.ram_kb) }}
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    topProcesses: {
        type: Object,
        required: true,
        default: () => ({
            byCpu: [],
            byRam: []
        })
    },
    last_checked: {
        type: String,
        required: false,
        default: ''
    }
})

function formatMemory(ramKb) {
    if (ramKb >= 1048576) { // 1024 * 1024
        return (ramKb / 1048576).toFixed(2) + ' GB';
    } else if (ramKb >= 1024) {
        return (ramKb / 1024).toFixed(2) + ' MB';
    } else {
        return ramKb + ' KB';
    }
}

function formatCpuPercentage(cpu) {
    const cpuValue = parseFloat(cpu);
    return cpuValue.toFixed(2) + '%';
}

function getCpuUsageClass(cpu) {
    const cpuValue = parseFloat(cpu);
    if (cpuValue >= 80) {
        return 'bg-error-light/20 text-error-light font-medium';
    } else if (cpuValue >= 50) {
        return 'bg-warning-light/20 text-warning-dark font-medium';
    } else {
        return 'bg-success-light/20 text-success-dark font-medium';
    }
}

function getMemoryUsageClass(ramKb) {
    // High RAM usage for a single process is often around 1GB+
    if (ramKb >= 1048576) { // 1GB+
        return 'bg-red-700 font-medium';
    } else if (ramKb >= 524288) { // 512MB+
         return 'bg-warning/20 text-warning-dark font-medium';
    } else {
        return 'bg-success-light/20 text-success-dark font-medium';
    }
}
</script>
