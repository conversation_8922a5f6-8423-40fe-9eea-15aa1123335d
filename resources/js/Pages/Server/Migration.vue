<template>
    <single-server :server="server" active="Full Server migration">
        <Head title="Sudo Users"/>

        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <div class="bg-focused dark:bg-mode-light rounded-10px flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
                    <h4
                        class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                        {{ $t('Full Server Migrations') }}
                    </h4>
                    <div class="flex gap-20px mobile:flex-wrap mobile:gap-10px">
                        <Link :href="route('server.migrate.source', {'server': server.id,'serverMigration': 'new'})"
                              as="button"
                              class="inline-flex items-center justify-center rounded-10px bg-transparent hover:bg-primary-dark shadow-none min-h-50px mobile:min-h-40px px-25px py-2px border-1 border-primary-light hover:border-primary-dark text-base text-primary-light hover:text-white focus:outline-0"
                              aria-expanded="true" aria-haspopup="true">
                            <i class="xcloud xc-add mr-15px flex"></i>
                            <span>{{ $t('Initiate Full Server Migration') }}</span>
                        </Link>
                    </div>
                </div>

                <div class="overflow-x-auto bg-white dark:bg-mode-base w-full overflow-y-hidden" v-if="migrations.length > 0">
                    <table class="w-full">
                        <thead class="bg-transparent">
                        <tr class="divide-x divide-light dark:divide-dark">
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('ID') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('Public Ip') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('Status') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('Actions') }}
                            </th>
                        </tr>
                        </thead>
                        <tbody
                            class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">

                        <tr
                            class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                            v-for="migration in migrations" :key="migration.id">
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ migration.id }}
                            </td>
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ migration.public_ip }}
                            </td>
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ migration.status }}
                            </td>

                            <td class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                                <div class="flex gap-15px">

                                    <Link :href="route('server.migrate.processing',[server.id, migration.id])"
                                          class="inline-flex items-center justify-center min-h-40px border-1 border-primary-light dark:border-dark text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light dark:bg-dark group-hover:bg-light group-hover:text-primary-light group-hover:border-light hover:bg-white hover:border-white hover:text-dark hover:shadow-md hover:shadow-primary-dark/30 dark:hover:shadow-secondary-full/10 transition ease-in-out duration-300 focus:outline-none disabled:opacity-75 disabled:cursor-not-allowed px-15px mr-2"
                                          href="https://xcloud.test/server/6/site/4"> View
                                    </Link>

                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </single-server>
</template>
<script setup>
import {computed, ref, watch} from "vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import TextInput from '@/Shared/TextInput.vue';
import {useForm} from '@inertiajs/inertia-vue3';
import Modal from "@/Shared/Modal.vue"
import Item from "@/Pages/Server/Components/Management/Item.vue";
import Button from "@/Jetstream/Button.vue";
import DeleteServer from "@/Pages/Server/Components/DeleteServer.vue";
import {useFlash} from "@/Composables/useFlash";
import SelectInput from "@/Shared/SelectInput.vue";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import SiteTable from "@/Pages/Site/Components/SiteTable.vue";
import Btn from "@/Shared/Btn.vue";
import Site from "@/Pages/Site/Components/Site.vue";
import Datatable from "@/Shared/Table/Datatable.vue";
import Pagination from "@/Shared/Pagination.vue";

const props = defineProps({
    server: Object,
    migrations: Object,

})

</script>
