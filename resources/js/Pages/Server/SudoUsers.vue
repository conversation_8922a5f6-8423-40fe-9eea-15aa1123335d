<template>
    <single-server
        active="Sudo Users"
        :server="server"
        :can-delete-server="can_delete_server"
        :can_add_site="can_add_site"
        :can_restart_server="can_restart_server"
        :can_restart_nginx_server="can_restart_nginx_server"
        :can_restart_mysql_server="can_restart_mysql_server"
    >
        <Head title="Sudo Users"/>
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <div class="bg-focused dark:bg-mode-light rounded-10px flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
                    <h4
                        class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                        {{ $t('Sudo Users') }}
                    </h4>
                    <div v-if="can_create_edit_sudo_user" class="flex gap-20px mobile:flex-wrap mobile:gap-10px">
                        <Link :href="route('server.sudo.create', server.id)" as="button"
                              class="inline-flex items-center justify-center rounded-10px bg-transparent hover:bg-primary-dark shadow-none min-h-50px mobile:min-h-40px px-25px py-2px border-1 border-primary-light hover:border-primary-dark text-base text-primary-light hover:text-white focus:outline-0"
                              aria-expanded="true" aria-haspopup="true">
                            <i class="xcloud xc-add mr-15px flex"></i>
                            <span>{{ $t('Add Sudo User') }}</span>
                        </Link>
                    </div>
                </div>

                <div class="overflow-x-auto bg-white dark:bg-mode-base w-full overflow-y-hidden" v-if="sudo_users.data.length > 0">
                    <table class="w-full">
                        <thead class="bg-transparent">
                        <tr class="divide-x divide-light dark:divide-dark">
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('ID') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('Sudo User Name') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('Status') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('SSH Keys') }}
                            </th>
                            <th
                                v-if="can_create_edit_sudo_user"
                                class="px-30px py-20px text-left text-lg font-normal text-dark dark:text-white">
                                {{ $t('Actions') }}
                            </th>
                        </tr>
                        </thead>
                        <tbody
                            class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">

                        <tr
                            class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                            v-for="sudo_user in sudo_users.data" :key="sudo_user.id">
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ sudo_user.id }}
                            </td>
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ sudo_user.sudo_user }}
                            </td>
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ sudo_user.status }}
                            </td>
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                {{ sudo_user.ssh_key_paris_count }}
                            </td>
                            <td v-if="can_create_edit_sudo_user" class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                                <div class="flex gap-15px">
                                    <Link :href="route('server.sudo.edit',[server.id, sudo_user.id])" class="relative group cursor-pointer mt-1.5">
                                        <span
                                            class="text-sm flex text-secondary-light group-hover:text-white">
                                            <i class="xcloud xc-edit"></i>
                                        </span>
                                    </Link>

                                    <button
                                        type="button"
                                        @click.prevent="destroy(sudo_user)"
                                        class="relative group cursor-pointer mt-1.5">
                                        <span
                                            class="text-sm flex text-secondary-light group-hover:text-white">
                                            <i class="xcloud xc-delete"></i>
                                        </span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <Pagination :links="sudo_users.links"></Pagination>
        </div>
    </single-server>
</template>

<script setup>
import SingleServer from "@/Pages/Server/SingleServer.vue";
import Pagination from '@/Shared/Pagination.vue';
import {useForm} from '@inertiajs/inertia-vue3';
import {Inertia} from "@inertiajs/inertia";
import {useFlash} from "@/Composables/useFlash";
import UpgradeableServers from "@/Shared/Server/UpgradeableServers.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const form = useForm({
    sudo_user: '',
    sudo_password: '',
    ssh_key: '',
    ssh_keypair_ids: [],
});

let {server} = defineProps({
    server: Object,
    keys: Object,
    sudo_users: Object,
    can_add_server: Boolean,
    can_delete_server: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
    can_create_edit_sudo_user: Boolean
})

const destroy = (sudo_user) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure?'),
        text: t('This will remove the sudo user from the server.'),
        btn_text: t('Yes, Remove!'),
    }, () => {
        Inertia.delete(route('api.server.sudoUsers.destroy', [server.id, sudo_user.id]));
    });
}
</script>
