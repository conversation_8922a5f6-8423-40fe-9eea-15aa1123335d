<template>
    <single-server :server="server" active="Sudo Users">
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">

            <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center wide-mobile:px-15px gap-20px mobile:gap-1 justify-between">

                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                        {{ $t('Add Sudo User') }}
                    </h4>
                </div>

                <div
                    class="p-30px wide-mobile:p-25px mobile:p-4 h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                    <form @submit.prevent="submit" class="h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                        <input type="submit" class="hidden">
                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.sudo_user"
                                        :error="form.errors.sudo_user"
                                        type="text"
                                        :placeholder="$t('Sudo User Name')"
                                        :label="$t('Sudo User Name')"
                                        autocomplete="off"
                            />
                        </div>
                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <password-input v-model="form.sudo_password"
                                            :error="form.errors.sudo_password"
                                            icon="xcloud xc-pass_lock"
                                            placeholder="********"
                                            :label="$t('Sudo Password')"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1 ">

                            <SshKeySelector
                                :can_create_ssh_key="can_create_ssh_key"
                                :keys="keys"
                                :selected="form.ssh_keypair_ids"
                                :error="form.errors.ssh_keypair_ids"
                                :emptyText="$t('No SSH Keys are available. Please add SSH Key.')"
                                @onSelectionChanged="form.ssh_keypair_ids = $event"
                                @newKeyAdded="form.ssh_keypair_ids.push($event.id)"
                            />
                        </div>

                        <div class="flex">
                            <Btn @click.prevent="submit" type="submit" :loading="form.processing">
                                {{ $t('Save') }}
                            </Btn>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </single-server>
</template>

<script setup>
import {ref} from "vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import TextInput from '@/Shared/TextInput.vue';
import SshKeySelector from '@/Shared/Ssh/SshKeySelector.vue';
import PasswordInput from '@/Shared/PasswordInput.vue'
import {useForm} from '@inertiajs/inertia-vue3';
import Box from "@/Shared/Box.vue";
import Btn from "@/Shared/Btn.vue";

let props = defineProps({
    server: Object,
    keys: Object,
    sudo_users: Object,
    can_create_ssh_key: Boolean,
})

const form = useForm({
    sudo_user: '',
    sudo_password: '',
    ssh_keypair_ids: [],
});

const submit = () => {
    form.post(route('api.server.sudoUsers.store', props.server.id));
}
</script>
