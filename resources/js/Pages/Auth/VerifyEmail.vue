<template>
    <Head title="Email Verification" />

    <jet-authentication-card>
        <template #logo>
            <img v-if="currentWhiteLabel && currentWhiteLabel.brand_photo_url" :src="currentWhiteLabel.brand_photo_url" alt="" class="w-32 mx-auto mobile:mb-2">
            <img v-else :src="asset('img/x-cloud_blue.svg')" alt="" class="h-20 w-60 mx-auto mobile:mb-2">
        </template>

        <h2 class="auth-title text-4xl font-medium leading-none text-dark dark:text-white mb-30px -tracking-wide mobile:text-3xl">
            {{ $t('Verify Email Address') }}
        </h2>
        <p class="text-base text-secondary-full dark:text-mode-secondary-light font-normal mb-30px">
            {{ $t('To continue using') }} {{ currentWhiteLabel ? currentWhiteLabel?.branding?.brand_name : $t('xCloud') }}, {{ $t('please click on the link in the verification email sent to your email.') }}
        </p>

        <div class="mb-4 font-medium text-sm text-green-600" v-if="verificationLinkSent" >
            {{ $t('A new verification link has been sent to the email address you provided during registration.') }}
        </div>

        <form @submit.prevent="submit">
            <div class="flex flex-col gap-30px">
                <text-input v-model="form.email"
                    :error="form.errors.email"
                    id="email"
                    icon="xcloud xc-email"
                    type="email"
                    :placeholder="$t('Email Address..')"
                    :label="$t('Email')+'*'"/>
            </div>

            <div class="mt-4 flex items-center justify-between">
                <!--<jet-button :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Resend Verification Email
                </jet-button>-->

                <button
                    type="submit"
                    class="flex items-center justify-center min-h-60px p-2 px-30px mt-30px rounded-10px shadow-none text-lg text-center text-white font-medium bg-primary-light w-full focus:outline-none hover:bg-primary-light ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30"
                    :class="{ 'opacity-25': form.processing || countdown > 0 }" :disabled="form.processing || countdown > 0">
                    <span class="drop-shadow-button">
                        <span v-if="countdown > 0">{{ $t('Please wait') }} {{ countdown }} {{ $t('seconds to resend email.') }}</span>
                        <span v-else>{{ $t('Resend Verification Email') }}</span>
                    </span>
                </button>
            </div>
            <div class="flex justify-center p-2">
                <Link :href="route('logout')" method="post" as="button" class="mt-2 text-dark dark:text-white font-medium hover:underline hover:text-primary-light dark:hover:text-primary-light">{{ $t('Logout') }}</Link>
            </div>
        </form>
    </jet-authentication-card>
</template>

<script>
import {defineComponent, ref} from 'vue'
    import JetAuthenticationCard from '@/Jetstream/AuthenticationCard.vue'
    import JetAuthenticationCardLogo from '@/Jetstream/AuthenticationCardLogo.vue'
    import JetButton from '@/Jetstream/Button.vue'
import {Head, Link, usePage} from '@inertiajs/inertia-vue3';
    import TextInput from "@/Shared/TextInput.vue";
    import {useFlash} from "@/Composables/useFlash";

    export default defineComponent({
        components: {
            TextInput,
            Head,
            JetAuthenticationCard,
            JetAuthenticationCardLogo,
            JetButton,
            Link,
        },

        props: {
            status: String
        },

        data() {
            return {
                form: this.$inertia.form({
                    email: ''
                }),
                countdown: 0,
                currentWhiteLabel: usePage().props.value?.current_white_label
            }
        },

        methods: {
            submit() {
                axios.post(route('api.user.email.update'), {
                    email: this.form.email
                }).then(() => {
                    this.form.post(this.route('verification.send'))
                    this.startCountdown();
                    const currentTime = new Date().getTime();
                    localStorage.setItem('verificationEmailTime', currentTime);
                }).catch(error => {

                });
            },
            startCountdown() {
                this.countdown = 60;

                const countdownInterval = setInterval(() => {
                    if (this.countdown > 0) {
                        this.countdown--;
                    } else {
                        clearInterval(countdownInterval);
                    }
                }, 1000);
            },
            checkCountdown() {
                const savedTime = localStorage.getItem('verificationEmailTime');
                const currentTime = new Date().getTime();
                const timeDifference = currentTime - savedTime;
                const remainingTime = 60000 - timeDifference;
                if (timeDifference < 60000 && remainingTime > 0) {
                    this.countdown = Math.floor(remainingTime / 1000);

                    const countdownInterval = setInterval(() => {
                        if (this.countdown > 0) {
                            this.countdown--;
                        } else {
                            clearInterval(countdownInterval);
                        }
                    }, 1000);
                } else {
                    this.countdown = 0;
                    localStorage.removeItem('verificationEmailTime');
                }
            },
            getAuthUserEmail() {
                axios.get(route('api.user.email'))
                .then(({data}) => {
                    this.form.email = data.email
                }).catch(error => {

                })
            }
        },

        mounted() {
            this.getAuthUserEmail();
            this.checkCountdown();
        },

        computed: {
            verificationLinkSent() {
                return this.status === 'verification-link-sent';
            }
        }
    })
</script>
