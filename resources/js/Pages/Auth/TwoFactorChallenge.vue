<template>
    <Head title="Two-factor Confirmation"/>

    <div class="xc-container">
        <div class="flex-1 items-center flex flex-col justify-center"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'"
        >
            <span v-if="currentWhiteLabel && currentWhiteLabel.brand_photo_url">
                <img :src="currentWhiteLabel.brand_photo_url" alt="" class="w-32 mx-auto mobile:mb-2">
            </span>

            <span v-else>
                <img :src="asset('img/x-cloud_blue.svg')" alt="" class="h-20 w-60 mx-auto mobile:mb-2">
            </span>

            <div
                class="auth-wrapper max-w-590px w-full bg-white dark:bg-mode-light rounded-10px p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <h4 class="auth-title text-2xl font-medium leading-none text-dark dark:text-white mb-30px -tracking-wide mobile:text-3xl">
                    {{ $t('Two-factor Confirmation') }}
                </h4>
                <p class="text-base text-secondary-full dark:text-mode-secondary-light font-normal mb-30px">
                    <template v-if="! recovery">
                        {{ $t('Please confirm access to your account by entering the authentication code provided by your authenticator application.') }}
                    </template>

                    <template v-else>
                        {{ $t('Please confirm access to your account by entering one of your emergency recovery codes.') }}
                    </template>
                </p>

                <form @submit.prevent="submit">
                    <div v-if="! recovery" class="flex flex-col gap-30px">
                        <text-input v-model="form.code"
                                    :error="form.errors.code"
                                    id="code"
                                    type="number"
                                    :placeholder="$t('Code')"
                                    :label="$t('Code')"
                                    :autofocus="autofocus"/>
                    </div>
                    <div v-else class="flex flex-col gap-30px">
                        <text-input v-model="form.recovery_code"
                                    :error="form.errors.recovery_code"
                                    id="recovery_code"
                                    type="text"
                                    :placeholder="$t('Recovery Code')"
                                    :label="$t('Recovery Code')"
                                    :autofocus="true"/>
                    </div>
                    <div class="flex flex-row gap-30px">
                        <a href="#" @click.prevent="toggleRecovery"
                                class="flex items-center  min-h-60px shadow-none text-md dark:text-white text-center font-medium w-full focus:outline-none ease-in-out transition duration-200 ">
                            <template v-if="! recovery">
                                {{ $t('Use a recovery code') }}
                            </template>

                            <template v-else>
                                {{ $t('Use an authentication code') }}
                            </template>
                        </a>
                        <button type="submit"
                                class="flex items-center justify-center min-h-60px p-2 px-10px mt-30px rounded-10px shadow-none text-md text-center text-white font-medium bg-primary-light w-64 focus:outline-none hover:bg-primary-light ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30">
                            <span class="drop-shadow-button"> {{ $t('Log In') }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import {Head, Link, useForm, usePage} from '@inertiajs/inertia-vue3';
import TextInput from '@/Shared/TextInput.vue';
import {useHelpStore} from "@/stores/HelpStore";
import {useFlash} from "@/Composables/useFlash";
import {useNavigationStore} from "@/stores/NavigationStore";
import {nextTick, onMounted, ref} from "vue";

let helper = useHelpStore();
let navigation = useNavigationStore();

const recovery = ref(false);
const autofocus = ref(false);
const currentWhiteLabel = ref(usePage().props.value?.current_white_label);
onMounted(async () => {
    autofocus.value = true
})

const form = useForm({
    code: '',
    recovery_code: '',
})

const toggleRecovery = async ()  => {
    //this.recovery ^= true
    recovery.value ^= true

    form.reset('code', 'recovery_code');

    //focus to input
    await nextTick();
    document.getElementById(recovery.value ? 'recovery_code' : 'code').focus();
}

function submit() {
    form.post(route('two-factor.login'), {
        preserveScroll: true
    })
}

</script>
