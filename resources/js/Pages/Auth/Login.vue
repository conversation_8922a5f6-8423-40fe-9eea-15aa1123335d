<template>
    <Head title="Log In"/>
    <div class="xc-container">
        <div class="flex-1 items-center flex flex-col justify-center"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'"
        >
            <span v-if="currentWhiteLabel && currentWhiteLabel.brand_photo_url">
                <img :src="currentWhiteLabel.brand_photo_url" alt="" class="w-32 mx-auto mobile:mb-2">
            </span>

            <span v-else>
                <img :src="asset('img/x-cloud_blue.svg')" alt="" class="h-20 w-60 mx-auto mobile:mb-2">
            </span>

            <div
                class="auth-wrapper max-w-590px w-full bg-white dark:bg-mode-light rounded-10px p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">

                <h2 class="auth-title text-4xl font-medium leading-none text-dark dark:text-white mb-30px -tracking-wide mobile:text-3xl">
                    👋 {{ $t('Glad To Have You Back!') }}
                    <span class="block mt-2 text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center">Enter your email and password to log in</span>
                </h2>

                <form @submit.prevent="submit">
                    <div class="flex flex-col gap-30px">
                        <text-input v-model="form.email"
                            :error="form.errors.email"
                            id="email"
                            icon="xcloud xc-email"
                            :placeholder="$t('Enter your email')"
                            type="email"
                            :label="$t('Email')"
                            :autofocus="autofocus"/>

                        <password-input v-model="form.password"
                            :error="form.errors.password"
                            id="password"
                            icon="xcloud xc-pass_lock"
                            placeholder="********"
                            :label="$t('Password')">
                        </password-input>

                    </div>
                    <div class="flex justify-between items-center mt-20px">
                    <label class="inline-flex">
                        <input v-model="form.remember" type="checkbox" class="hidden peer"/>
                        <span
                            class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded-full before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white">
                            <span
                                class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark">{{ $t('Remember me') }}
                            </span>
                        </span>
                    </label>
                    <span class="block text-base font-normal">
                        <Link
                          href="/forgot-password"
                          class="text-secondary-full dark:text-mode-secondary-light hover:underline hover:text-primary-light dark:hover:text-primary-light leading-none">{{ $t('forgot_password') }}</Link>
                    </span>
                    </div>
                    <button
                        type="submit"
                        class="flex items-center justify-center min-h-60px p-2 px-30px mt-30px rounded-10px shadow-none text-lg text-center text-white font-medium bg-primary-light w-full focus:outline-none hover:bg-primary-light ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30">
                        <span class="drop-shadow-button">{{ $t('Log In') }}</span>
                    </button>
                </form>
                <span class="block mt-30px text-base font-normal text-secondary-full dark:text-mode-secondary-dark">{{ $t('Haven’t registered for your free account yet?') }}
                  <Link
                      class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light dark:hover:text-primary-light"
                      href="/register">{{ $t('Sign up now') }}</Link>
                </span>
            </div>
        </div>
    </div>

</template>

<script setup>
import {useForm, usePage} from '@inertiajs/inertia-vue3';
import TextInput from '@/Shared/TextInput.vue';
import PasswordInput from '@/Shared/PasswordInput.vue';

import {useNavigationStore} from "@/stores/NavigationStore.js";
import {useHelpStore} from "@/stores/HelpStore.js";
import {onMounted, ref} from "vue";
import {useI18n} from "vue-i18n";

const navigation = useNavigationStore();
let helper = useHelpStore();
const currentWhiteLabel = ref(usePage().props.value?.current_white_label);
const autofocus = ref(false);
onMounted(async () => {
    autofocus.value = true
})

const {email,status} = defineProps({
    email: {
        type: String,
        required: false
    },
    status: String
})

const form = useForm({
    email: email,
    password: '',
    remember: true
});


function submit() {
    form.transform(data => ({
        ...data,
        remember: form.remember ? 'on' : ''
    }))
        .post(route('login'), {
            onFinish: () => form.reset('password'),
        })
}
</script>
