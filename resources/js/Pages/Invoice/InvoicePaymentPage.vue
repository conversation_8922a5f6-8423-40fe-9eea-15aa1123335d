<template>
    <div>
        <!-- Keep the current page as background -->
        <slot></slot>

        <!-- Invoice Payment Modal -->
        <pay-invoice-modal
            :openInvoicePaymentModal="openInvoicePaymentModal"
            :invoice-payment-form="invoicePaymentForm"
            :payment-methods="paymentMethodLists"
            :handle-invoice-payment-modal="handleInvoicePayment"
            @close="handleCloseInvoicePaymentModal"
            :take-payment="takeInvoicePayment"
            :is-processing-payment="isProcessingPayment"
            :inactive-card-warning="inactiveCardWarning">
        </pay-invoice-modal>
    </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useForm } from "@inertiajs/inertia-vue3";
import { useFlash } from "@/Composables/useFlash";
import PayInvoiceModal from "@/Pages/Profile/PayInvoiceModal.vue";
import { useCreditCardIcon } from "@/Composables/useCreditCardIcon";
import { asset } from "laravel-vapor";
import axios from 'axios';

const props = defineProps({
    invoice: Object,
    paymentMethods: Object,
    defaultCard: Object,
    returnUrl: String,
    redirect: String,
    urlParams: Object,
});

let openInvoicePaymentModal = ref(true);

let isProcessingPayment = ref(false);

const invoicePaymentForm = useForm({
    invoice: props.invoice,
    paymentMethodId: props.defaultCard?.id,
});

const paymentMethodLists = () => {
    let paymentMethods = [];
    for (let key in props.paymentMethods) {
        paymentMethods.push({
            value: props.paymentMethods[key].id,
            card_no: props.paymentMethods[key].card_no,
            expires_at: props.paymentMethods[key].expiry_month + '/' + props.paymentMethods[key].expiry_year,
            brand: props.paymentMethods[key].brand,
            icon: creditCardIcon(props.paymentMethods[key].brand),
            disabled: !(props.paymentMethods[key].status === 'active'),
            inactiveCard: !(props.paymentMethods[key].status === 'active')
        })
    }
    return paymentMethods;
}

const creditCardIcon = (brand) => {
    const { creditCardIcon } = useCreditCardIcon(brand);
    return creditCardIcon.value;
}

const inactiveCardWarning = () => {
    return '<img src="' + asset('img/warning.svg') + '" class="ml-1.5 h-4 w-4" alt="inactive card warning" />';
};

const handleCloseInvoicePaymentModal = () => {
    openInvoicePaymentModal.value = false;
    window.history.back();
}

const handleInvoicePayment = () => {
    openInvoicePaymentModal.value = true;
}

const takeInvoicePayment = () => {

    isProcessingPayment.value = true;

    if (invoicePaymentForm.invoice?.status === 'paid') {
        useFlash().warning('This invoice is already paid.')
        return;
    }

    axios.post(route('api.invoice.pay', [invoicePaymentForm.invoice?.invoice_number]), {
        invoice: invoicePaymentForm.invoice,
        paymentMethodId: invoicePaymentForm.paymentMethodId,
    }).then(response => {

        const downloadInvoiceRoute = route('user.invoice.download', [
            invoicePaymentForm.invoice?.invoice_number
        ]);

        if (response.data.type === 'error') {
            useFlash().error(response.data.message);
            return;
        }

        if (invoicePaymentForm.invoice?.status === 'paid' || response.data?.status === 'paid') {
            useFlash().success('Your payment has been processed successfully.');
        }

        let url = props.redirect ? props.redirect : props.returnUrl;

        for (let key in props.urlParams) {
            url += (url.includes('?') ? '&' : '?') + key + '=' + props.urlParams[key];
        }

        window.location.href = response.data?.redirect || url;

    }).catch(error => {
        const errorMessage = error.response?.data?.message || 'Failed to process payment';
        useFlash().error(errorMessage);
        openInvoicePaymentModal.value = false;
        window.history.back();
    }).finally(() => {
        isProcessingPayment.value = false;
    });
}
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
