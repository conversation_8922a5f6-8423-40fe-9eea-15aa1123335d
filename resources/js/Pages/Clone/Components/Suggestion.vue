<template>
    <div class="rounded-md flex items-center bg-white dark:bg-mode-base px-6 py-4 border-1 border-primary-light
                dark:border-mode-base w-ful mb-20px mobile:mb-5px"
        :class="{'dark:bg-mode-light' : lightMode}"
    >
    <img :src="asset('img/note.svg')" alt="Suggestion message" />
        <p class="text-sm text-dark dark:text-white leading-7 pl-4"
        v-html="message">
        </p>
    </div>
</template>

<script setup>

const props = defineProps({
    message: String,
    lightMode: {
      type: Boolean,
      default: false
    }
});

</script>
