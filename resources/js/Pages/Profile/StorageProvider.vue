<template>
    <single-profile active="Storage Provider">
        <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex justify-between items-center gap-10px p-30px w-full">
                <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Storage Provider') }}</h2>
            </div>
            <div class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="flex justify-between items-center gap-15px w-full wide-mobile:flex-wrap">
                    <div class="flex flex-col gap-10px">
                        <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('List of Storage Providers') }}</h3>
                        <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                            {{ $t('Find all the server providers associated with your account here.') }}
                        </p>
                    </div>
                    <button v-if="permissions.includes('server:add-provider')"
                            @click.prevent="addServerProviderModal = true"
                            class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                            text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent hover:bg-primary-light
                            hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition ease-in-out duration-300 focus:outline-none
                            leading-tight flex-nowrap whitespace-nowrap">
                        <span>{{ $t('Add New Provider') }}</span>
                    </button>
                </div>
                <div class="flex flex-col gap-30px">
                    <div class="rounded-md flex flex-col">
                        <div class="overflow-x-auto w-full overflow-y-hidden">
                            <table class="w-full">
                                <thead class="bg-primary-light dark:bg-dark">
                                <tr class="divide-x divide-light dark:divide-dark">
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Provider') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                       {{ $t('Bucket Name') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Region') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Access Key') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Site Count') }}
                                    </th>
                                    <th v-if="permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider')"
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Actions') }}
                                    </th>
                                </tr>
                                </thead>
                                <tbody
                                    class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                                <tr v-for="provider in providers?.data"
                                    :key="provider.id"
                                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                >

                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                          <span
                                              class="text-secondary-full capitalize dark:text-mode-secondary-light group-hover:text-white flex items-center mb-10px">
                                              <img :src="getCloudProvider(provider.provider)" :alt="provider.provider"
                                                   class="w-20px mr-2 group-hover:brightness-[100]"/>
                                              {{ provider.provider.replaceAll('_', ' ') }}
                                          </span>
                                    </td>
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        {{ provider?.bucket }}
                                    </td>
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        {{ provider?.region }}
                                    </td>
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white w-96 break-all">
                                        {{ provider?.access_key_id?.slice(0, 65) }}{{ provider?.access_key_id?.length > 65 ? '...' : '' }}
                                    </td>
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        {{ provider?.backup_settings_count }}
                                    </td>
                                    <td v-if="permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider')"
                                        class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                                          <span class="flex gap-30px">
                                            <button v-if="permissions.includes('server:edit-provider')"
                                                    @click.prevent="edit(provider)"
                                                    class="relative group cursor-pointer mt-1.5">
                                              <span class="text-sm flex text-secondary-light group-hover:text-white">
                                                <i class="xcloud xc-edit"></i>
                                              </span>
                                            </button>
                                            <button v-if="permissions.includes('server:delete-provider')"
                                                    @click.prevent="destroy(provider)"
                                                    class="relative group cursor-pointer mt-1.5"
                                            >
                                              <span class="text-sm flex text-secondary-light group-hover:text-white">
                                                <i class="xcloud xc-delete"></i>
                                              </span>
                                            </button>
                                          </span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- pagination -->
                    <pagination :links="providers?.links"/>
                </div>
            </div>
        </div>
    </single-profile>
    <Modal
        :loading="form.processing"
        @footerClick="submitServerProvider"
        :footer-button-title="form.provider === 'google_drive' && !is_edit  ? $t('Link Google Drive Account') : $t('Submit')"
        :footer-button="true"
        :closeable="true"
        @close="closeModal()"
        :show="addServerProviderModal"
        :widthClass="'max-w-850px'"
        :title="`${is_edit ? $t('Edit') : $t('Add')} `+$t('Storage Provider')">

        <div class="flex flex-col gap-30px">
            <div class="grid grid-cols-auto-60 gap-4">
                <radio-input-grid
                    v-for="(provider, key) in providerItems"
                    v-model="form.provider"
                    :error="form.errors.provider"
                    :value="key"
                    :title="provider"
                    :icon="useIntegrationIcon(key).integrationIcon.value"
                />

                <div class="flex w-full">
                    <Error :error="form.errors.provider" />
                </div>
            </div>

            <!-- S3-compatible storage provider fields -->
            <div v-if="showS3Fields" class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10">
                <text-input
                    type="text"
                    :label="$t('Bucket Name')"
                    :placeholder="$t('bucket')"
                    :note="$t('Enter the name of the bucket you have in your storage provider.')"
                    :error="form.errors.bucket"
                    v-model="form.bucket">
                </text-input>

                <!-- CloudFlare R2 Region Dropdown -->
                <select-input
                    v-if="form.provider === 'cloudflare_r2'"
                    id="region"
                    v-model="form.region"
                    :error="form.errors.region"
                    :note="$t('Select the data location')"
                    :label="$t('Select Region')"
                    :placeholder="$t('Server Provider')">
                    <option value="" disabled>{{ $t('Choose Region') }}</option>
                    <option v-for="(cloud_flare_region, cloud_flare_key) in cloud_flare_regions"
                            :value="cloud_flare_key"
                            v-text="cloud_flare_region">
                    </option>
                </select-input>

                <!-- Hetzner Region Dropdown -->
                <select-input
                    v-if="form.provider === 'hetzner'"
                    id="hetzner_region"
                    v-model="form.region"
                    :error="form.errors.region"
                    :note="$t('Select the data location')"
                    :label="$t('Select Region')"
                    :placeholder="$t('Server Provider Region')">
                    <option value="" disabled>{{ $t('Choose Region') }}</option>
                    <option v-for="(hetzner_region, region_key) in hetzner_regions"
                            :value="region_key"
                            v-text="hetzner_region">
                    </option>
                </select-input>
            </div>

            <!-- Access Key and Secret Key fields for S3-compatible providers -->
            <div v-if="showS3Fields" class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10">
                <text-input
                    type="text"
                    :note="$t('Enter the access key id here.')"
                    :label="accessKeyName"
                    :placeholder="accessKeyName"
                    :error="form.errors.access_key_id"
                    v-model="form.access_key_id">
                </text-input>

                <password-input
                    icon=""
                    autocomplete="off"
                    type="password"
                    :note="$t('Enter the secret key here.')"
                    :label="secretKeyName"
                    placeholder="***********"
                    :error="form.errors.secret_key"
                    v-model="form.secret_key">
                </password-input>
            </div>

            <!-- Endpoint field for providers that need it -->
            <div v-if="showEndpointField" class="grid grid-cols-1 wide-mobile:grid-cols-1 gap-10">
                <text-input
                    @input="(e) => updateEndpoint(e.target.value)"
                    type="text"
                    :label="$t('Endpoint')"
                    :placeholder="$t('Endpoint')"
                    :note="$t('Enter the endpoint url here and make sure to add https:// in url.')"
                    :error="form.errors.endpoint"
                    v-model="form.endpoint">
                    <template #note>
                        <p v-if="!$page?.props?.current_white_label" class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-15px">
                            {{ $t('Get help from our') }} <a target="_blank" class="underline text-primary-light" href="https://xcloud.host/docs/site-backups-in-xcloud/">{{ $t('Site Backup') }}</a>
                            {{ $t('documentation') }}
                        </p>
                    </template>
                </text-input>
            </div>

            <!-- Region field for providers that need manual region entry -->
            <div v-if="showRegionField" class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10">
                <text-input
                    type="text"
                    :label="$t('Region')"
                    :note="$t('Enter the region here.')"
                    :placeholder="$t('Region')"
                    :error="form.errors.region"
                    v-model="form.region">
                </text-input>
            </div>

            <!-- Google Drive specific fields -->
            <div v-if="showGoogleDriveFields" class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10">
                <text-input
                    type="text"
                    note="Enter the label name here."
                    label="Label"
                    placeholder="Enter the label"
                    :error="form.errors.label"
                    v-model="form.label">
                </text-input>
            </div>
        </div>
    </Modal>
</template>
<script setup>
import {computed, ref, watch} from "vue";
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Modal from "@/Shared/Modal.vue"
import TextInput from '@/Shared/TextInput.vue';
import {useForm} from '@inertiajs/inertia-vue3';
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import Button from "@/Jetstream/Button.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import PasswordInput from "@/Shared/PasswordInput.vue";
import RadioInputGrid from "@/Shared/RadioInputGrid.vue";
import Error from "@/Shared/Error.vue";
import {useIntegrationIcon} from "@/Composables/useIntegrationIcon";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
    providers: {
        type: Object,
        required: true
    },
    uniqueProviders: {
        type: Object,
    },
    access_permissions: {
        type: Object,
        required: true
    },
    providerItems: {
        type: [Array,Object],
        default: () => []
    },
    cloud_flare_regions: {
        type: [Array,Object],
        default: () => []
    },
    hetzner_regions: {
        type: [Array,Object],
        default: () => []
    }
});
const permissions = Object.values(props?.access_permissions ?? {});

let addServerProviderModal = ref(false)
const is_edit = ref(false);

const closeModal = () => {
    delete form.errors.endpoint;
    addServerProviderModal.value = false;
    is_edit.value = false;
    form.reset();
}
const form = useForm({
    provider: Object.keys(props.providerItems ?? {})[0],
    access_key_id: '',
    secret_key: '',
    bucket: '',
    region: '',
    endpoint: '',
    label: '',
});

// Provider-specific validation patterns for endpoints
const endpointPatterns = {
    vultr: /^(https:\/\/)?([a-zA-Z0-9-]+\.)?vultrobjects\.com\/?$/,
    digital_ocean: /^https?:\/\/[a-zA-Z0-9-]+\.([a-zA-Z0-9-]+)\.digitaloceanspaces\.com\/?$/,
    cloudflare_r2: /^https?:\/\/[a-zA-Z0-9-]+\.([a-zA-Z0-9-]+\.)?r2\.cloudflarestorage\.com(?:\/[\w\-]+)?\/?$/,
    backblaze_b2: /^https?:\/\/s3\.([a-z\-0-9]+)\.backblazeb2\.com\/?$/
};

// Validate and update endpoint, extracting region when possible
const updateEndpoint = (endpoint) => {
    // Reset error property from form.errors
    delete form.errors.endpoint;

    if (!endpoint) return;

    // Ensure endpoint starts with https:// for all providers
    if (!endpoint.includes('https://') && !endpoint.includes('http://')) {
        endpoint = 'https://' + endpoint;
    }

    const pattern = endpointPatterns[form.provider];

    if (pattern) {
        if (pattern.test(endpoint)) {
            form.endpoint = endpoint;
            form.region = extractRegionFromEndpoint(endpoint, form.provider);
        } else if (form.endpoint) {
            form.errors.endpoint = 'Please enter a valid endpoint for ' + props.providerItems[form.provider];
        }
    }
};

// Provider-specific field labels
const accessKeyName = computed(() => ({
    'backblaze_b2': 'Key ID',
}[form.provider] ?? 'Access Key Id'));

const secretKeyName = computed(() => ({
    'backblaze_b2': 'Application Key',
}[form.provider] ?? 'Secret Key'));

// Extract region from endpoint based on provider pattern
const extractRegionFromEndpoint = (endpoint, provider) => {
    switch (provider) {
        case 'vultr':
            return endpoint.split('.')[0].split('://')[1];
        case 'digital_ocean':
            return endpoint.split('.')[1];
        case 'cloudflare_r2':
            return form.region ? form.region : 'us-east-1';
        case 'backblaze_b2':
            const match = endpoint.match(/s3\.([a-z\-0-9]+)\.backblazeb2\.com/);
            return match ? match[1] : form.region;
        default:
            return form.region;
    }
};

// Computed properties for form visibility and behavior
const showEndpointField = computed(() => {
    return form.provider !== 'google_drive' &&
           form.provider !== 'google_cloud_storage' &&
           form.provider !== 'hetzner';
});

const showRegionField = computed(() => {
    return form.provider === 'other' || form.provider === 'google_cloud_storage';
});

const showRegionDropdown = computed(() => {
    return form.provider === 'cloudflare_r2' || form.provider === 'hetzner';
});

const showGoogleDriveFields = computed(() => {
    return form.provider === 'google_drive';
});

const showS3Fields = computed(() => {
    return form.provider !== 'google_drive';
});


// Update provider and reset related fields
const updateProvider = $event => {
    form.provider = $event.target.value;

    // Reset endpoint-related errors
    delete form.errors.endpoint;
    delete form.errors.region;

    // Update endpoint if it exists
    if (form.endpoint) {
        updateEndpoint(form.endpoint);
    }

    // Set default endpoint for certain providers
    if (form.provider === 'google_cloud_storage') {
        form.endpoint = 'https://storage.googleapis.com';
    } else if (form.provider === 'hetzner' && form.region) {
        updateHetznerEndpoint();
    }
}

// Update Hetzner endpoint based on selected region
const updateHetznerEndpoint = () => {
    if (form.provider === 'hetzner' && form.region) {
        form.endpoint = `https://${form.region}.your-objectstorage.com`;
    }
}

// Watch for region changes to update endpoint for Hetzner
watch(() => form.region, (newRegion) => {
    if (form.provider === 'hetzner' && newRegion) {
        updateHetznerEndpoint();
    }
});

// Edit an existing storage provider
const edit = (provider) => {
    // Reset form and errors
    form.reset();
    delete form.errors.endpoint;
    delete form.errors.region;

    // Set form values from provider
    form.provider = provider.provider;
    is_edit.value = provider.id;
    form.access_key_id = provider.access_key_id;
    form.secret_key = provider.secret_key;
    form.bucket = provider.bucket;
    form.region = provider.region;

    // Set provider-specific endpoint formats
    if (provider.provider === 'vultr') {
        form.endpoint = `https://${provider.region}.vultrobjects.com`;
    } else if (provider.provider === 'digital_ocean') {
        form.endpoint = `https://${provider.bucket}.${provider.region}.digitaloceanspaces.com`;
    } else if (provider.provider === 'hetzner') {
        form.endpoint = `https://${provider.region}.your-objectstorage.com`;
    } else if (provider.provider === 'google_cloud_storage') {
        form.endpoint = 'https://storage.googleapis.com';
    } else {
        form.endpoint = provider.endpoint;
    }

    // For Google Drive, use bucket as label
    if (form.provider === 'google_drive') {
        form.label = provider.bucket;
    }

    // Show the modal
    addServerProviderModal.value = true;
}

// Submit the storage provider form
const submitServerProvider = () => {
    // Handle Google Drive provider
    if (form.provider === 'google_drive') {
        if (form.label === '') {
            form.errors.label = 'The label field is required.';
            return;
        }

        form.errors.label = '';

        if (is_edit.value) {
            form.put(route('google-drive.update', is_edit.value), {
                preserveScroll: true,
                onSuccess: () => {
                    closeModal();
                    useFlash().success('Storage Provider Updated Successfully');
                },
                onError: (error) => {
                    useFlash().error(error.message);
                }
            });
        } else {
            axios.post(route('google-drive.authorize'), {
                provider: form.provider,
                label: form.label
            }).then((response) => {
                window.location.href = response.data;
            }).catch(error => {
                useFlash().error(error?.response?.data?.message);
            });
        }

        return;
    }

    // Ensure endpoints have proper format for each provider
    formatEndpointForProvider();

    // Validate endpoint before submission
    if (form.errors.hasOwnProperty('endpoint')) {
        useFlash().error('Please enter valid endpoint');
        return;
    }

    // Submit the form based on whether it's an edit or new provider
    if (is_edit.value) {
        form.put(route('user.storage-provider.update', is_edit.value), {
            preserveScroll: true,
            onSuccess: () => {
                closeModal();
                useFlash().success('Storage Provider Updated Successfully');
            },
            onError: (error) => {
                useFlash().error(error.message);
            }
        });
    } else {
        form.post(route('user.storage-provider.store'), {
            preserveScroll: true,
            onSuccess: () => {
                closeModal();
                useFlash().success('Storage Provider Added Successfully');
            },
            onError: (error) => {
                useFlash().error(error.message || 'Failed to add storage provider');
            }
        });
    }
}

// Format endpoint based on provider before submission
const formatEndpointForProvider = () => {
    // Ensure endpoint has https:// prefix
    if (form.endpoint && !form.endpoint.includes('https://') && !form.endpoint.includes('http://')) {
        form.endpoint = 'https://' + form.endpoint;
    }

    // Provider-specific endpoint formatting
    switch (form.provider) {
        case 'vultr':
            form.endpoint = `https://${form.region}.vultrobjects.com`;
            break;
        case 'digital_ocean':
            form.endpoint = `https://${form.region}.digitaloceanspaces.com`;
            break;
        case 'google_cloud_storage':
            form.endpoint = 'https://storage.googleapis.com';
            break;
        case 'hetzner':
            form.endpoint = `https://${form.region}.your-objectstorage.com`;
            break;
        case 'backblaze_b2':
            // Extract region from endpoint if not already set
            const match = form.endpoint.match(/s3\.([a-z\-0-9]+)\.backblazeb2\.com/);
            if (match) {
                form.region = match[1];
            }
            break;
    }
}

const destroy = (provider) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure?'),
        text: t("You won't be able to revert this!"),
        btn_text: t('Yes, Remove!'),
    }, () => {
        Inertia.delete(route('user.storage-provider.delete', [provider.id]), {
            preserveScroll: true,
            onSuccess: () => {
                useFlash().success('Storage provider removed successfully')
            }
        })
    });
}
const getCloudProvider = (provider) => {
    const {cloudProviderIcon} = useCloudProviderIcon(provider);
    return cloudProviderIcon.value;
};

</script>


<style>
@import '@vueform/multiselect/themes/tailwind.css';

.multiselect.is-disabled {
    @apply bg-white dark:bg-mode-light;
}
</style>
