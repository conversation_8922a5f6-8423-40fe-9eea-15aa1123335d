<template>
  <single-profile active="Team Products">
        <div class="flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex justify-between items-center gap-10px p-30px w-full">
                <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Team Products') }}</h2>
            </div>
            <div class="flex flex-col gap-30px p-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="flex flex-col gap-30px">
                    <div class="rounded-md flex flex-col">
                        <div class="overflow-x-auto w-full overflow-y-hidden">
                            <table class="w-full">
                                <thead class="bg-primary-light dark:bg-dark">
                                <tr class="divide-x divide-light dark:divide-dark">
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Title') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Type') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Service Type') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Created At') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Price') }}
                                    </th>

                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                    </th>
                                </tr>
                                </thead>
                                <tbody
                                    class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                                    <tr
                                        v-for="team_product in products?.data"
                                        :key="team_product?.id"
                                        class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                    >
                                        <td class="px-30px py-20px text-left text-base capitalize font-normal text-dark dark:text-white group-hover:text-white">
                                            {{ team_product?.title }}
                                        </td>
                                        <td class="px-30px py-20px text-left text-base capitalize font-normal text-dark dark:text-white group-hover:text-white">
                                            {{team_product?.type}}
                                        </td>
                                        <td class="px-30px py-20px text-left text-base capitalize font-normal text-dark dark:text-white group-hover:text-white">
                                            {{team_product?.renewal_type}}
                                        </td>
                                        <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                            {{team_product?.pivot?.attached_at}}
                                        </td>
                                        <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                            ${{team_product?.price}}
                                        </td>

                                        <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                          <a
                                              class="dark:text-white underline hover:cursor-pointer"
                                              :href="team_product?.white_label_id
                                                      ? route('cart.checkoutWithProductForWhiteLabel', team_product?.id)
                                                      : route('cart.checkoutWithProduct', team_product?.id)
                                              "
                                              target="_blank"
                                          >
                                            {{ $t('Setup Now') }}
                                          </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <Pagination :links="products?.links"></Pagination>
                </div>
            </div>
        </div>
  </single-profile>
</template>
<script setup>
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import { Link} from "@inertiajs/inertia-vue3";
import Pagination from "@/Shared/Pagination.vue";
import Btn from "@/Shared/Btn.vue";
defineProps({
    'products': {
        type: Object,
        required: true
    }
})

</script>
