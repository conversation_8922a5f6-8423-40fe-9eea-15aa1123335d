<template>
    <div
        class="group relative flex tablet:flex-wrap items-center gap-x-20px gap-y-10px bg-light dark:bg-mode-base  hover:drop-shadow-list-box p-5 rounded-10px duration-75 ease-in-out"
        :class="{ 'z-[1]': showDropDown }"
    >
        <div class="flex items-center grow">
            <div class="flex items-center gap-20px wide-mobile:gap-10px">
                <span class="w-100px wide-tablet:w-20 wide-mobile:w-60px shrink-0">
                    <img :src="asset('img/site_placeholder.gif')" alt="">
                </span>
                <div class="flex flex-col">
                    <h2 class="inline-flex items-center">
                        <StateToolTip
                            :align="index === 0 ? 'bottom' : 'top'"
                            class="mr-3"
                            :state="site.state"
                            :title="site.status_readable"
                            :blinking="site?.has_vulnerability_scan && site?.vulnerabilities_count > 0"
                            :blinking_title="site?.vulnerabilities_count + ' Vulnerabilities Found'"
                        />
                        <tooltip
                            :align="index === 0 ? 'bottom' : 'top'"
                            class="cursor-pointer"
                            :title="site.name"
                        >
                            <Link
                                :href="'/site/' + site.id"
                                class="inline-flex items-center text-dark dark:text-light text-3xl wide-tablet:text-2xl tablet:text-xl !leading-none hover:text-primary-dark dark:hover:text-white transition ease-in-out duration-75"
                            >
                                {{ $filters.textLimit(site.name, 20) }}
                            </Link>
                        </tooltip>
                        <tooltip
                            :align="index === 0 ? 'bottom' : 'top'"
                            v-if="site.state === 'Success'"
                            class="cursor-pointer ml-3"
                            title="View Site"
                        >
                            <a
                                :href="site.site_url"
                                target="_blank"
                                class="inline-flex items-center"
                            >
                                <i class="xcloud xc-maximize text-base text-secondary-full dark:text-secondary-light hover:text-primary-light
        dark:hover:text-primary-light cursor-pointer transition ease-in-out duration-75">
                                </i>
                            </a>
                        </tooltip>
                    </h2>
                    <div class="flex items-center flex-wrap gap-x-1 gap-y-1 mt-1">
                        <span class="text-base text-dark dark:text-light leading-none">{{ site.created_at_readable }}</span>
                        <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm"
                            v-if="site.environment"
                        >
                            <span
                                class="px-10px py-1 wide-mobile:p-1
                                rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5"
                                :class="{
                                    'text-success-light bg-success-light/10' : site.environment === 'production',
                                    'text-[#9AA4B2] bg-[#9AA4B2]/10' : site.environment === 'staging',
                                    'text-[#007EFD] bg-[#007EFD]/10' : site.environment === 'demo'
                                }"
                            >
                                <span>{{ site.environment }}</span>
                            </span>
                            </h6>

                            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full
            dark:text-mode-secondary-light"
                                v-if="site.wordpress_version"
                            >
                            <span class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base rounded-md transition ease-in-out
                                        duration-150 inline-flex items-center gap-1.5"
                            >
                                <i class="xcloud xc-wordpress text-primary-light"></i>
                                <span>{{ site.wordpress_version }}</span>
                            </span>
                        </h6>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-wrap items-center justify-end gap-10px tablet:w-full">
            <span v-if="site?.vulnerabilities_count>0" class="h-7 inline-flex items-center justify-center gap-1 px-4 py-1 bg-danger/20 text-danger rounded-full text-xs">
                <i class="xcloud xc-wordpress text-base"></i>
                    {{site?.vulnerabilities_count}} {{site?.vulnerabilities_count === 1 ? 'Issue' : 'Issues'}} Found
            </span>
            <Link
                :href="route('site.vulnerability-scan',{
                    server: site.server,
                    site: site.id
                })"
                class="h-10 inline-flex items-center justify-center px-4 py-1 border-1 border-solid border-primary-light text-primary-light rounded-lg text-sm font-medium hover:text-white hover:bg-primary-light transition duration-75 ease-in-out">
                {{ $t('Check Details') }}
            </Link>
            <div class="flex items-center justify-center server-toggle-button pr-10px">
                <SiteActions
                    :site="site"
                    :server="site?.server"
                    position="right"
                    @onChangeDropDown="onChangeDropDown"
                >
                    <template #selector>
                        <div>
                            <button
                                type="button"
                                class="inline-flex items-center justify-center min-h-40px border-1 border-primary-light dark:border-dark text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light dark:bg-dark hover:bg-white hover:border-white hover:text-dark hover:shadow-md hover:shadow-primary-dark/30 dark:hover:shadow-secondary-full/10 transition ease-in-out duration-300 focus:outline-none disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                                id="menu-button"
                                aria-expanded="true"
                                aria-haspopup="true"
                            >
                                <span class="sr-only">{{ $t('Open options') }}</span>
                                <!-- Heroicon name: mini/ellipsis-horizontal -->
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                    :data-site="props.site.id"
                                    class="w-6 h-6"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"
                                    />
                                </svg>
                            </button>
                        </div>
                    </template>
                </SiteActions>
            </div>
        </div>
    </div>
</template>

<script setup>

    import {Link} from "@inertiajs/inertia-vue3";
    import StateToolTip from "@/Shared/StateToolTip.vue";
    import Tooltip from "@/Shared/Tooltip.vue";
    import SiteActions from "@/Pages/Site/Components/SiteActions.vue";
    import {ref} from "vue";

    defineProps({
        site: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
    })

const showDropDown = ref(false);

const onChangeDropDown = (value) => {
    showDropDown.value = value;
};
</script>
