<template>
    <single-profile active="Team Management">
        <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex items-center gap-15px p-30px w-full">
                <Link
                    :href="route('user.team.show', team.id)"
                    class="h-40px w-40px shrink-0 rounded-10px flex items-center justify-center bg-light dark:bg-mode-base text-secondary-full/50 dark:text-mode-secondary-dark hover:text-dark dark:hover:text-white text-xs"
                >
                    <i class="xcloud xc-angle_left"></i>
                </Link>
                <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Update Member') }}</h2>
                <button
                    @click.prevent="form.post(route('user.team.invitation.update',{team:team.id,invitation:invitation.id}))"
                    class="inline-flex items-center justify-center min-h-40px border-1 border-success-light text-lg wide-mobile:text-base px-25px wide-mobile:px-20px mobile:px-15px rounded-md shadow-none text-white bg-success-light hover:bg-success-full hover:shadow-lg hover:shadow-success-full/30 transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none tracking-tighter ml-auto">
                    <span>{{ $t('Update Invitation') }}</span>
                </button>
            </div>
            <div
                class="flex flex-col grow gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full"
            >
                <div
                    class="grid grid-cols-2 tablet:grid-cols-1 gap-30px w-full wide-mobile:flex-wrap"
                >
                    <div>
                        <text-input
                            :model-value="invitation.email"
                            readonly
                            id="email_id"
                            placeholder="<EMAIL>"
                            :label="$t('Email ID')+'*'"
                        />
                        <select-input
                            v-model="form.role"
                            :error="form.errors.role"
                            :label="$t('User Role')"
                            :placeholder="$t('User Role')"
                            class="mt-30px tablet:mt-25px"
                        >
                            <option
                                class="capitalize"
                                v-for="(user_role, index) in user_role_list"
                                :value="user_role.key"
                                :key="index"
                            >
                                {{ user_role.name }}
                            </option>
                        </select-input>
                    </div>
                    <div v-if="form.role !== 'site-admin'">
                        <div class="mt-20px">
                            <div class="flex flex-col gap-15px mb-30px">
                                <label
                                    class="block text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none"
                                >
                                    {{ $t('Server Access') }}:
                                </label>
                                <div class="w-full flex flex-col gap-10px">
                                    <RadioInput
                                        v-model="form.server_access"
                                        value="all"
                                    >{{ $t('Access to all server') }}
                                    </RadioInput>
                                    <RadioInput
                                        v-model="form.server_access"
                                        value="choose"
                                    >{{ $t('Choose specific server') }}
                                    </RadioInput>
                                    <!-- Show validation error -->
                                    <div
                                        v-if="form.errors.selected_servers"
                                        class="mt-2 text-sm text-red-600"
                                    >
                                        {{ form.errors.selected_servers }}
                                    </div>
                                </div>
                            </div>
                            <div v-if="form.server_access === 'choose'" class="add-member-multiselect">
                                <Multiselect
                                    v-model="form.selected_servers"
                                    @search-change="searchServers($event)"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="false"
                                    :placeholder="$t('Search here...')"
                                    :options="servers"
                                />
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div class="mt-20px">
                            <div class="flex flex-col gap-15px mb-30px">
                                <label
                                    class="block text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none"
                                >
                                    {{ $t('Site Access') }}:
                                </label>
                                <div class="w-full flex flex-col gap-10px">
                                    <RadioInput
                                        v-model="form.site_access"
                                        value="all"
                                    >{{ $t('Access to all site') }}
                                    </RadioInput>
                                    <RadioInput
                                        v-model="form.site_access"
                                        value="choose"
                                    >{{ $t('Choose specific site') }}
                                    </RadioInput>
                                    <!-- Show validation error -->
                                    <div
                                        v-if="form.errors.selected_sites"
                                        class="mt-2 text-sm text-red-600"
                                    >
                                        {{ form.errors.selected_sites }}
                                    </div>
                                </div>
                            </div>
                            <div v-if="form.site_access === 'choose'" class="add-member-multiselect">
                                <Multiselect
                                    v-model="form.selected_sites"
                                    @search-change="searchSites($event)"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="false"
                                    :placeholder="$t('Search here...')"
                                    :options="sites"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="bg-transparent p-30px rounded-md border-1 border-solid border-secondary-light dark:border-mode-base dark:bg-mode-base"
                >
                    <h3 class="text-xl text-dark dark:text-white leading-none font-medium mb-1">
                        {{ $t('Role Permission') }}
                    </h3>
                    <small class="dark:text-white">
                        {{ user_role_list[form.role]?.description }}
                    </small>
                    <div class="text-sm text-danger">
                        {{ form.errors.permissions }}
                    </div>

                    <ul class="mt-15px flex flex-col gap-10px">
                        <li
                            class="text-base text-dark dark:text-white"
                            v-for="(permission, index) in user_role_list[
                                form.role
                            ]?.permissions"
                            :key="index"
                        >
                            <div class="text-lg font-semibold my-3">
                                <label class="inline-flex">
                                    <input
                                        type="checkbox"
                                        class="hidden peer"
                                        :id="index"
                                        :checked="isSelectAll(permission)"
                                        @click="togglePermissions(permission)"
                                    />
                                    <span
                                        class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:text-success-full"
                                    >
                                        <span class="text-dark dark:text-white text-lg leading-snug font-semibold capitalize">
                                            {{ index }}
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <ul
                                class="grid grid-cols-5 laptop:grid-cols-4 wide-tablet:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-15px mt-15px"
                            >
                                <li class="list-none text-base text-dark dark:text-white"
                                    v-for="(
                                        singlePermission, key, index
                                    ) in permission"
                                    :key="index"
                                >
                                    <label class="inline-flex">
                                        <input type="checkbox" class="hidden peer" :id="key" :value="key" v-model="form.permissions"/>
                                        <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:text-success-full">
                                        <span class="text-base text-dark dark:text-white">
                                            {{ key }}
                                            <Tooltip align="top" :title="singlePermission" :color="'primary'">
                                                <button class="relative group cursor-pointer mt-1.5">
                                                    <span class="text-xs flex text-secondary-full dark:text-mode-secondary-light">
                                                        <i class="xcloud xc-info"></i>
                                                    </span>
                                                </button>
                                            </Tooltip>
                                        </span>
                                    </span>
                                    </label>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </single-profile>
</template>
<script setup>
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import {Link} from '@inertiajs/inertia-vue3';
import {useForm} from '@inertiajs/inertia-vue3';
import RadioInput from '@/Shared/RadioInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import MultiSelect from "@/Shared/MultiSelect.vue";
import {ref, watchSyncEffect} from "vue";
import {forEach} from "lodash";
import Button from "@/Jetstream/Button.vue";
import VueMultiSelect from "@/Shared/VueMultiSelect.vue";
import TextInput from "@/Shared/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Multiselect from "@vueform/multiselect";
import {useCapitalizeString} from "@/Composables/useCapitalizeString";
import {useFlash} from "@/Composables/useFlash";

const {invitation, user_role_list, team, role, selected_servers, server_access,selected_sites,site_access,inactive_permissions,serverList,siteList} = defineProps({
    user_role_list: {
        type: Array,
        required: true
    },
    invitation: {
        type: Object,
        required: true
    },
    selected_servers: {
        type: Array,
        default: []
    },
    selected_sites: {
        type: Array,
        default: []
    },
    team: {
        type: Object,
        required: true
    },
    server_access: {
        type: String,
        default: 'all'
    },
    site_access: {
        type: String,
        default: 'all'
    },
    inactive_permissions: {
        type: Array,
        default: []
    },
    serverList: {
        type: Object,
        default: []
    },
    siteList: {
        type: Object,
        default: []
    },
});

const servers = ref(serverList.map(server => ({value: server.id, label: server.name})));
const sites = ref(siteList.map(site => ({value: site.id, label: site.name})));


const form = useForm({
    _method: 'PUT',
    role: invitation.role,
    server_access: server_access,
    selected_servers: selected_servers,
    site_access: site_access,
    selected_sites: selected_sites,
    permissions: invitation?.permissions?.permissions ?? [],
});

watchSyncEffect(() => {
    if (form.role===invitation.role){
        form.permissions = invitation?.permissions?.permissions ?? [];
        form.selected_servers = selected_servers;
        form.selected_sites = selected_sites;
        form.server_access =server_access;
        form.site_access = site_access;
    }else{
        form.selected_servers = [];
        form.selected_sites = [];
        form.server_access = "all";
        form.site_access = "all";
        form.permissions = Object.values(user_role_list[form.role]?.permissions || {})
            .flatMap(permission => Object.keys(permission))
            .filter((permission) => !inactive_permissions.includes(permission) || form.role === 'team-admin');
    }

}, [form.role]);

const togglePermissions = (permissions) => {
    const permission_keys = Object.keys(permissions);
    if(permission_keys.some(permission => !form.permissions.includes(permission))){
        form.permissions = [...form.permissions, ...permission_keys];
    }else {
        form.permissions = form.permissions.filter(permission => !permission_keys.includes(permission));
    }
};

const isSelectAll = (permissions) => {
    return (
        Object.keys(permissions).length ===
        form.permissions.filter((permission) =>
            Object.keys(permissions).includes(permission)
        ).length
    );
};

const fetchData = async (routeName, params, targetRef, labelKey = 'name') => {
    try {
        const { data } = await axios.get(route(routeName, params));
        targetRef.value = data.map(item => ({ value: item.id, label: useCapitalizeString(item[labelKey]).sanitizeXSSPayload() }));
    } catch ({ response }) {
        useFlash().error(response?.data?.message || 'Something went wrong');
    }
};
const searchServers = async (query) => {
    await fetchData('api.search.servers', { query }, servers);
};

const searchSites = async (query) => {
    await fetchData('api.search.sites', { query }, sites);
};

watchSyncEffect(() => {
    if ('choose' === form.server_access) {
        searchServers('');
    }
}, [form.server_access]);

watchSyncEffect(() => {
    if ('choose' === form.site_access) {
        searchSites('');
    }
}, [form.site_access]);
</script>
