<template>
    <transition>
        <div class="absolute -top-2 -right-10px flex">
            <button @click.prevent="show=!show" type="button"
                    class="inline-flex items-center justify-center text-lg text-secondary-full hover:text-primary-light dark:hover:text-white">
                <i :class="`xcloud xc-menu-vertical ${team.id}-vertical-menu`"></i>
            </button>
            <div
                v-if="show"
                class="absolute z-10 right-1/2 translate-x-1/2 top-full w-52 rounded-b-10px bg-white py-10px rounded-10px dark:bg-mode-focus-light focus:outline-none shadow-md shadow-dark/10"
                role="menu" aria-orientation="vertical" aria-labelledby="menu-button"
                tabindex="-1">
                <Link href="#"
                      v-if="team.id !== user.current_team_id"
                        @click.prevent="switchToTeam(team)"
                   class="text-secondary-full dark:text-white flex items-center px-20px py-2.5 text-base hover:text-white hover:bg-success-full"
                   role="menuitem" tabindex="-1" id="menu-item-6">
                    {{ $t('Switch to Team') }}
                </Link>
                <template v-if="!team.isPlayGround">
                    <Link  :href="route('user.team.show', team.id)"
                          v-if="is_admin_or_owner()"
                       class="text-secondary-full dark:text-white flex items-center px-20px py-2.5 text-base hover:text-white hover:bg-success-full"
                       role="menuitem" tabindex="-1" id="menu-item-6">
                        {{ $t('Manage Team') }}
                    </Link>
                    <Link :href="route('user.team.edit', team.id)"
                          v-if="is_admin_or_owner()"
                       class="text-secondary-full dark:text-white flex items-center px-20px py-2.5 text-base hover:text-white hover:bg-success-full"
                       role="menuitem" tabindex="-1" id="menu-item-6">
                        {{ $t('Edit') }}
                    </Link>
                    <Link :href="route('user.team.member.create', team.id)"
                          v-if="is_admin_or_owner() && can_add_member()"
                       class="text-secondary-full dark:text-white flex items-center px-20px py-2.5 text-base hover:text-white hover:bg-success-full"
                       role="menuitem" tabindex="-1" id="menu-item-6">
                        {{ $t('Add Member') }}
                    </Link>
                </template>

                <Link href="#" @click.prevent="removeMember(team, user)"
                      v-if="team.can_leave"
                   class="text-secondary-full dark:text-white flex items-center px-20px py-2.5 text-base hover:text-white hover:bg-success-full"
                   role="menuitem" tabindex="-1" id="menu-item-6">
                    {{ $t('Leave Team') }}
                </Link>
                <template v-if="!team.isPlayGround">
                    <button @click.prevent="deleteTeam(team)"
                          v-if="team.can_delete"
                       class="text-secondary-full dark:text-white flex items-center w-full px-20px py-2.5 text-base hover:text-white hover:bg-success-full"
                       role="menuitem" tabindex="-1" id="menu-item-6">
                        {{ $t('Delete Team') }}
                    </button>
                    <template v-else>
                        <tooltip class="w-full" v-if="!team.can_delete" :title="messageForNonDeleteTeam(team)">
                            <button
                                disabled
                                class="text-gray-300 disabled dark:text-gray-500 cursor-not-allowed flex items-center w-full px-20px py-2.5 text-base dark:hover:text-gray-100 hover:bg-success-full"
                                role="menuitem" tabindex="-1" id="menu-item-6">
                            {{ $t('Delete Team') }}
                        </button>
                        </tooltip>
                    </template>
                </template>
            </div>
        </div>
    </transition>

</template>

<script setup>
import {ref} from 'vue';
import {usePage} from "@inertiajs/inertia-vue3";
import {Link} from "@inertiajs/inertia-vue3";
import {Inertia} from "@inertiajs/inertia";
import {useNavigationStore} from "@/stores/NavigationStore.js";
import {useFlash} from "@/Composables/useFlash";
import Button from "@/Jetstream/Button.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

let navigation = useNavigationStore();
const emit = defineEmits(['deleteTeam'])

const {team,teams_has_admin_role} =defineProps({
    team: {
        type: Object,
        required: true
    },
    teams_has_admin_role: {
        type: Array,
        required: true
    }
});
const {user} = usePage().props.value;

let show = ref(false);

const is_admin_or_owner =() => {
    return !!(teams_has_admin_role.includes(team.id) || team.user_id === user.id);
}

const can_add_member = () => {
    const max_team_members = team?.permissions?.member_max_count ?? 5;
    return team?.memberships_count + team?.team_invitations_count + 1 < max_team_members
}

const removeMember = (team, member) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure?'),
        text: t("You won't be able to revert this!"),
        btn_text: t('Yes, leave team!')
    }, () => {
        Inertia.delete(route('user.team.member.destroy', [team.id, member.id]))
    })
}

/*
  Hide the menu when the user clicks outside it
 */
document.addEventListener('click', (e) => {
    if (show.value && !e.target.classList.contains(`${team.id}-vertical-menu`)) {
        show.value = false;
    }
});

const switchToTeam = (team) => {
    Inertia.put(route('current-team.update'), {
        team_id: team.id,
    }, {
        preserveState: true,
        onSuccess: () => {
            navigation.setCurrentTeam(team);
        },
    });
};

const deleteTeam = (team) => {
    emit('deleteTeam',team);
}
const messageForNonDeleteTeam = (team) => {
    let message = t('You cannot delete this team');
    if (team?.personal_team && team.user_id === user.id) {
        message = t('You cannot delete your personal team');
    }else if (team.id === user.current_team_id) {
        message = t('You cannot delete your current team');
    }
    return message
}

</script>
