<template>
  <single-profile title="User Profile" active="User Profile">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
      <div class="flex justify-between items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Invitation List') }}</h2>
      </div>
      <!-- Invitation Code -->
      <div
           class="grid grid-cols-2 tablet:grid-cols-1 gap-30px tablet:gap-y-25px">
        <div class="flex flex-col p-50px wide-mobile:p-30px mobile:p-20px w-full">
          <div class="flex flex-col gap-10px">
            <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('Invite Users') }}</h3>
            <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
              {{ $t('Share your invitation code with others. You have') }}
              <b v-if="invitationCode">
                {{
                  invitationCode?.remaining_invitation > 1
                      ? invitationCode?.remaining_invitation + ' invitations'
                      : invitationCode?.remaining_invitation + ' invitation'
                }}
              </b>
                <b v-else>0 {{ $t('invitation') }}</b>
              {{ $t('remaining') }}.
            </p>
          </div>
          <div v-if="invitationCode?.invitation_code" class="mt-30px grid grid-cols-1 tablet:grid-cols-1 gap-30px tablet:gap-y-25px">
            <text-input
                label="Code"
                type="text"
                :modelValue="invitationCode?.invitation_code"
                disabled
            >
              <CopyButton align="middle" :content="invitationCode?.invitation_code"></CopyButton>
            </text-input>

            <div>
              <text-input
                  label="Expires At"
                  type="text"
                  :modelValue="invitationCode?.expires_at"
                  disabled
              >
              </text-input>
            </div>
          </div>
        </div>

        <div class="flex flex-col p-50px wide-mobile:p-30px mobile:p-20px w-full">
          <div class="flex flex-col gap-10px">
            <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('Invited Users') }}</h3>
            <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
              {{ $t('You have invited following persons.') }}
            </p>
          </div>

          <div class="mt-30px grid grid-cols-1 tablet:grid-cols-1 gap-1 tablet:gap-y-2">
            <div v-for="(invitedUser, index) in invitedUsers.data" :key="index"
                 class="border-2 border-light dark:border-mode-base px-30px py-5 flex gap-20px items-center flex-wrap rounded-md">
              <!-- Profile Image -->
              <div class="flex items-center">
                <img :src="invitedUser.profile_photo_url" alt="Profile Image"
                     class="w-10 h-10 rounded-full">
              </div>

              <!-- Heading and Paragraph -->
              <div class="flex-grow">
                <h3 class="text-lg text-dark dark:text-white leading-none">{{ invitedUser.name }}</h3>
                <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                  {{ $t('Accepted') }} {{ invitedUser.invited_at }}
                </p>
              </div>

              <div class="flex items-center flex-wrap gap-20px ml-auto">
                <label class="inline-flex">
                  <input type="checkbox" checked disabled class="hidden peer"/>
                  <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5
                              before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark
                              before:rounded-full before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex
                              before:items-center before:justify-center before:text-transparent before:outline-none
                              before:transition before:duration-200 peer-checked:before:border-success-full
                              peer-checked:before:bg-success-full peer-checked:before:text-white">
                            </span>
                </label>
              </div>
            </div>
            <pagination :links="invitedUsers.links"/>
          </div>
        </div>
      </div>
    </div>
  </single-profile>

</template>
<script setup>
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";

import CopyButton from "@/Shared/CopyButton.vue";
import TextInput from "@/Shared/TextInput.vue";
import Pagination from "@/Shared/Pagination.vue";

const props = defineProps({
  invitationCode: {
    type: Object,
    required: false
  },
  invitedUsers: {
    type: Object,
    required: false
  }
});

</script>
