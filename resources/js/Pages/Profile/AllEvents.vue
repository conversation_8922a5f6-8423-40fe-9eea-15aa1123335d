<template>
    <Head title="All Events"/>

    <single-profile active="All Events">
        <task-events-table :member="member" :teamMembers="teamMembers" :show-server="showServer" :taskEvents="taskEvents" :filter="filter"/>
    </single-profile>

</template>
<script setup>
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import TaskEventsTable from "@/Shared/TaskEventsTable.vue";

const props = defineProps({
    taskEvents: Object,
    filter: String,
    showServer: <PERSON>olean,
    teamMembers: Array,
    member: {
        type: Number,
        default: 0
    },
})
</script>
