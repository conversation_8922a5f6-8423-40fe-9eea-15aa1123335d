<template>
    <tr
        class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
    >
        <td
            class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
            {{ server.name }}
        </td>
        <td
            class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
            {{ server.provider_readable }}
        </td>
        <td class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
           <span class="flex gap-30px">
            <button @click.prevent="$emit('restoreServer', server.id)"
                    class="relative group cursor-pointer mt-1.5">
              <span class="text-sm flex text-secondary-light group-hover:text-white">
                <i class="xcloud xc-verify_dns"></i>
              </span>
            </button>
            <button @click.prevent="$emit('deleteServer',server)"
                    class="relative group cursor-pointer mt-1.5">
              <span class="text-sm flex text-secondary-light group-hover:text-white">
                <i class="xcloud xc-delete"></i>
              </span>

              </button>
          </span>
        </td>
    </tr>
</template>

<script setup>
   defineProps(['server']);
   defineEmits(['restoreServer', 'deleteServer']);
</script>
