<template>
    <single-profile title="User Profile" active="User Profile">
        <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex justify-between items-center gap-10px p-30px w-full">
                <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Profile') }}</h2>
                <div class="flex items-center gap-2">
                    <button
                        @click="updateProfile"
                        :disabled="profileForm.processing || loading"
                        :class="{
                            'cursor-not-allowed opacity-50': profileForm.processing || loading,
                            'cursor-pointer': !profileForm.processing && !loading
                        }"
                        class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-success-light
                            text-lg mobile:text-base px-25px mobile:px-15px font-medium rounded-10px shadow-none text-white
                            bg-success-light hover:bg-success-full hover:shadow-lg hover:shadow-success-full/30 transition
                            ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none">
                        {{ $t('Save Changes') }}
                    </button>
                </div>
            </div>
            <div
                class="flex justify-between items-center gap-15px p-50px wide-mobile:p-30px mobile:p-20px w-full wide-mobile:flex-wrap">
                <div class="flex items-center gap-30px wide-mobile:gap-20px">
                    <div class="h-100px wide-mobile:h-20 w-100px wide-mobile:w-20 shrink-0 bg-light dark:bg-mode-base rounded-full
                              text-4xl wide-mobile:text-3xl text-secondary-light flex items-center justify-center relative">
                        <img :src="profile_photo_path" alt="Profile Image"
                             class="rounded-full border border-gray-100 shadow-sm h-24 w-24 object-cover
                        "/>

                        <span class="absolute right-1.5 wide-mobile:right-1 bottom-1.5 wide-mobile:bottom-1 w-20px h-20px
                                rounded-full bg-primary-light flex items-center justify-center text-xxs">
                      <i class="xcloud xc-edit"></i>
                    </span>
                    </div>
                    <div class="flex flex-col gap-10px">
                        <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('Upload Your Avatar') }}</h3>
                        <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                            {{ $t('Add a profile picture or icon for your account') }}
                        </p>
                    </div>
                </div>
                <!-- Hidden part for input file -->
                <input
                    type="file"
                    ref="file"
                    accept="image/jpeg,image/png,image/jpg"
                    class="hidden"
                    @input="openFile($event)"
                />
                <button
                    @click="$refs.file.click()"
                    class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                            text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent hover:bg-primary-light
                            hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition ease-in-out duration-300
                            focus:outline-none leading-tight flex-nowrap whitespace-nowrap">
                    <span class="i xcloud xc-upload_2 mr-2.5"></span>
                    <span>{{ $t('Upload Image') }}</span>
                </button>
            </div>
            <div class="flex flex-col p-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="flex flex-col gap-10px">
                    <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('General Information') }}</h3>
                    <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">{{ $t('Set up your profile by providing the following information') }}
                    </p>
                </div>
                <div class="mt-30px grid grid-cols-2 tablet:grid-cols-1 gap-30px tablet:gap-y-25px">
                    <div>
                        <label
                            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-dark leading-none">
                            {{ $t('Name') }}
                        </label>
                        <div class="group">
                            <input type="text" name="text" v-model="profileForm.name"
                                   class="flex-1 block bg-white dark:bg-mode-light text-base font-normal text-dark dark:text-white
                                           min-h-60px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light
                                           group-focus-within:border-success-full w-full focus:outline-none min-w-0 rounded-md autofill:bg-light
                                           dark:autofill:bg-mode-base placeholder:text-secondary-light dark:placeholder:text-mode-secondary-light
                                           shadow-none outline-none appearance-none"/>
                            <div class="text-red-500" v-if="profileForm.errors.name">{{ profileForm.errors.name }}</div>
                        </div>
                    </div>
                    <div>
                        <label
                            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-dark leading-none">
                            {{ $t('Email ID') }}
                        </label>
                        <div class="group">
                            <input type="email" name="email" v-model="profileForm.email"
                                   class="flex-1 block bg-white dark:bg-mode-light text-base font-normal text-dark dark:text-white
                                            min-h-60px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light
                                            group-focus-within:border-success-full w-full focus:outline-none min-w-0 rounded-md autofill:bg-light
                                            dark:autofill:bg-mode-base placeholder:text-secondary-light dark:placeholder:text-mode-secondary-light
                                            shadow-none outline-none appearance-none"/>
                            <div class="text-red-500" v-if="profileForm.errors.email">
                                {{ profileForm.errors.email }}
                            </div>
                        </div>
                    </div>
                    <div>
                        <label
                            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-dark leading-none">
                            {{ $t('Contact Number') }}
                        </label>
                        <div class="group">
                            <input type="text" name="text" v-model="profileForm.contact_number"
                                   class="flex-1 block bg-white dark:bg-mode-light text-base font-normal text-dark dark:text-white
                                          min-h-60px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light
                                          group-focus-within:border-success-full w-full focus:outline-none min-w-0 rounded-md
                                          autofill:bg-light dark:autofill:bg-mode-base placeholder:text-secondary-light
                                          dark:placeholder:text-mode-secondary-light shadow-none outline-none appearance-none"/>
                            <div class="text-red-500" v-if="profileForm.errors.contact_number">
                                {{ profileForm.errors.contact_number }}
                            </div>
                        </div>
                    </div>
                    <TimeZone v-model="profileForm.time_zone" :errors="profileForm.errors.time_zone"
                              :timezone_list="timezone_list"></TimeZone>
                                      <div>
                        <label
                            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none"
                            for="language"
                        >
                            {{ $t('Language Settings') }}
                        </label>
                        <Multiselect
                            class="!ml-0 w-full !min-w-full"
                            id="language"
                            v-model="language"
                            @select="changeLanguage"
                            placeholder="Choose Language"
                            label="Language"
                            :canClear="false"
                            :canDeselect="false"
                            :options="countries"
                        >
                            <template v-slot:singlelabel="{ value }">
                                <div
                                    class="multiselect-single-label rounded-md"
                                >
                                    <RegionFlag
                                        v-if="value?.region"
                                        :region="value?.region"
                                        image-container="fi-round"
                                        classList="w-[20px] h-[20px] rounded-[10px] p-3 object-cover mr-2"
                                        :showRegion="false"
                                    />
                                    {{ value?.name }}
                                </div>
                            </template>

                            <template v-slot:option="{ option }">
                                <RegionFlag
                                    v-if="option?.region"
                                    :region="option?.region"
                                    classList="w-[20px] h-[20px] rounded-[10px] p-3 object-cover mr-2"
                                    :showRegion="false"
                                />
                                {{ option?.name }}
                            </template>
                        </Multiselect>
                    </div>

                </div>
            </div>
            <div
                class="flex flex-col p-50px wide-mobile:p-30px mobile:p-20px w-full"
            >
                <div class="flex flex-col gap-10px">
                    <h3 class="text-lg text-dark dark:text-white leading-none">
                        {{ $t('Additional Information') }}
                    </h3>
                </div>
                <div
                    selectedLanguage="ref(null);"
                    class="mt-30px grid grid-cols-2 tablet:grid-cols-1 gap-30px tablet:gap-y-25px"
                >
                    <div>
                        <label
                            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-dark leading-none"
                        >
                            {{ $t('Extra Billing Information') }}
                        </label>
                        <div class="group">
                            <textarea
                                v-model="profileForm.address"
                                cols="30"
                                rows="3"
                                class="flex-1 block bg-white dark:bg-mode-light text-base font-normal text-dark dark:text-white min-h-60px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light group-focus-within:border-success-full w-full focus:outline-none min-w-0 rounded-md autofill:bg-light dark:autofill:bg-mode-base placeholder:text-secondary-light dark:placeholder-secondary-full shadow-none outline-none appearance-none"
                            ></textarea>
                            <div
                                class="text-red-500"
                                v-if="profileForm.errors.address"
                            >
                                {{ profileForm.errors.address }}
                            </div>
                            <div class="mt-3">
                                <p
                                    class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark"
                                >
                                    <img
                                        class="inline-flex mr-2.5 w-20px -mt-1"
                                        :src="asset('img/note.svg')"
                                        alt="notification"
                                    />
                                    {{ $t('If you require the inclusion of specific contact or tax details on your receipts, such as your VAT identification number, or registered address, you may add it here.') }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label
                            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-dark leading-none"
                        >
                            {{ $t('Name of Invoice') }}
                        </label>
                        <div class="group">
                            <input
                                type="text"
                                name="text"
                                v-model="profileForm.customer_name"
                                class="flex-1 block bg-white dark:bg-mode-light text-base font-normal text-dark dark:text-white min-h-60px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light group-focus-within:border-success-full w-full focus:outline-none min-w-0 rounded-md autofill:bg-light dark:autofill:bg-mode-base placeholder:text-secondary-light dark:placeholder:text-mode-secondary-light shadow-none outline-none appearance-none"
                            />
                            <div
                                class="text-red-500"
                                v-if="profileForm.errors.customer_name"
                            >
                                {{ profileForm.errors.customer_name }}
                            </div>
                        </div>
                    </div>
                    <div>
                        <div>
                            <label class="inline-flex">
                                <input
                                    type="checkbox"
                                    class="hidden peer"
                                    v-model="profileForm.billing_emails_status"
                                />
                                <span
                                    class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white"
                                >
                                    <span class="flex flex-col gap-1">
                                        <span
                                            class="text-base font-normal text-dark dark:text-white"
                                            >{{ $t('Send billing invoices only to the team email address') }}</span
                                        >
                                        <!--<span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm"></span>-->
                                    </span>
                                </span>
                            </label>
                        </div>
                        <div v-if="!profileForm.billing_emails_status">
                            <label
                                class="block text-sm font-medium mb-2.5 mt-4 text-secondary-full dark:text-mode-secondary-dark leading-none"
                            >
                                {{ $t('Billing Emails') }}
                            </label>
                            <div class="group">
                                <textarea
                                    v-model="profileForm.billing_emails"
                                    cols="30"
                                    rows="3"
                                    class="flex-1 block bg-white dark:bg-mode-light text-base font-normal text-dark dark:text-white min-h-60px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light group-focus-within:border-success-full w-full focus:outline-none min-w-0 rounded-md autofill:bg-light dark:autofill:bg-mode-base placeholder:text-secondary-light dark:placeholder-secondary-full shadow-none outline-none appearance-none"
                                ></textarea>
                                <div
                                    class="text-red-500"
                                    v-if="profileForm.errors.billing_emails"
                                >
                                    {{ profileForm.errors.billing_emails }}
                                </div>
                                <div class="mt-3">
                                    <p
                                        class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark"
                                    >
                                        <img
                                            class="inline-flex mr-2.5 w-20px -mt-1"
                                            :src="asset('img/note.svg')"
                                            alt="notification"
                                        />
                                        {{ $t('If') }} <strong>{{ $t('Send billing invoices only to the team email address') }}</strong>
                                        {{ $t('is unchecked, billing invoices will be sent to the your given email addresses, separated by commas. Example:') }} <EMAIL>, <EMAIL>.') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Invitation Code -->
            <div v-if="invitationCode"
                 class="grid grid-cols-2 tablet:grid-cols-1 gap-30px tablet:gap-y-25px">
                <div class="flex flex-col p-50px wide-mobile:p-30px mobile:p-20px w-full">
                    <div class="flex flex-col gap-10px">
                        <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('Invite Users') }}</h3>
                        <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                            {{ $t('Share your invitation code with others. You have') }}
                            <b>
                                {{
                                    invitationCode.remaining_invitation > 1
                                        ? invitationCode.remaining_invitation + ' invitations'
                                        : invitationCode.remaining_invitation + ' invitation'
                                }}
                            </b>
                            {{ $t('remaining.') }}
                        </p>
                    </div>
                    <div class="mt-30px grid grid-cols-1 tablet:grid-cols-1 gap-30px tablet:gap-y-25px">
                        <text-input
                            label="Code"
                            type="text"
                            :modelValue="invitationCode.invitation_code"
                            disabled
                        >
                            <CopyButton align="middle" :content="invitationCode.invitation_code"></CopyButton>
                        </text-input>

                        <div>
                            <text-input
                                label="Expires At"
                                type="text"
                                :modelValue="invitationCode.expires_at"
                                disabled
                            >
                            </text-input>
                        </div>
                    </div>
                </div>

                <div v-if="invitedUsers.length > 0"
                     class="flex flex-col p-50px wide-mobile:p-30px mobile:p-20px w-full">
                    <div class="flex flex-col gap-10px justify-between items-center lg:flex-row">
                      <div class="flex flex-col gap-10px">
                        <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('Invited Users') }}</h3>
                        <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                          {{ $t('You have invited the following persons.') }}
                        </p>
                      </div>
                      <div class="text-sm text-secondary-full dark:text-secondary-light">
                        <a :href="route('user.profile.invitations')" class="hover:underline" target="_blank">{{ $t('View All') }}</a>
                      </div>
                    </div>

                    <div class="mt-30px grid grid-cols-1 tablet:grid-cols-1 gap-1 tablet:gap-y-2">
                        <div v-for="(invitedUser, index) in invitedUsers.slice(0,5)" :key="index"
                             class="border-2 border-light dark:border-mode-base px-30px py-5 flex gap-20px items-center flex-wrap rounded-md">
                            <!-- Profile Image -->
                            <div class="flex items-center">
                                <img :src="invitedUser.profile_photo_url" alt="Profile Image"
                                     class="w-10 h-10 rounded-full">
                            </div>

                            <!-- Heading and Paragraph -->
                            <div class="flex-grow">
                                <h3 class="text-lg text-dark dark:text-white leading-none">{{ invitedUser.name }}</h3>
                                <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                                    {{ $t('Accepted') }} {{ invitedUser.invited_at }}
                                </p>
                            </div>

                            <div class="flex items-center flex-wrap gap-20px ml-auto">
                                <label class="inline-flex">
                                    <input type="checkbox" checked disabled class="hidden peer"/>
                                    <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5
                              before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark
                              before:rounded-full before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex
                              before:items-center before:justify-center before:text-transparent before:outline-none
                              before:transition before:duration-200 peer-checked:before:border-success-full
                              peer-checked:before:bg-success-full peer-checked:before:text-white">
                            </span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Display "and X more people" message -->
                    <div v-if="invitedUsers.length > 5"
                         class="text-sm text-secondary-full dark:text-secondary-light leading-tight mt-4 flex">
                        <span class="mt-1">and {{ invitedUsers.length - 5 }} more people</span>
                        <div class="flex -space-x-0.5 ml-2">
                            <template v-for="(invitedUser, index) in invitedUsers.slice(5,10)" :key="index">
                                <tooltip :title="invitedUser.name">
                                    <img class="h-6 w-6 rounded-full ring-2 ring-white"
                                         :src="invitedUser.profile_photo_url" :alt="invitedUser.name"
                                    >
                                </tooltip>
                            </template>
                        </div>
                        <span class="mt-1 ml-1" v-if="invitedUsers.length > 10">
                          <tooltip title="View All">
                            <a :href="route('user.profile.invitations')" target="_blank" class="hover:underline">+{{ invitedUsers.length - 10 }}</a>
                          </tooltip>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </single-profile>

</template>
<script setup>
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import {useForm, usePage} from '@inertiajs/inertia-vue3'
import Vapor, {asset} from 'laravel-vapor';
import TimeZone from "@/Shared/TimeZone.vue";
import {ref, watch} from "vue";
import CopyButton from "@/Shared/CopyButton.vue";
import TextInput from "@/Shared/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Multiselect from "@vueform/multiselect/src/Multiselect.vue";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";
import { useNavigationStore } from "@/stores/NavigationStore";
import { languagesData } from "@/Composables/LanguagesWithCountryName";

const file = ref(null);
const loading = ref(false);

let navigation = useNavigationStore();

const userLanguage = usePage().props.value?.user_language || usePage().props.value?.current_white_label?.branding?.language || 'en';
const language = ref(userLanguage);
const countries = ref(languagesData);

const {user, timezone_list, invitationCode, userMeta, team} = defineProps({
    user: {
        type: Object,
        required: true
    },
    timezone_list: {
        type: Array,
        required: true
    },
    invitationCode: {
        type: Object,
        required: false
    },
    invitedUsers: {
        type: Object,
        required: false
    },
    userMeta: {
        type: Array,
        required: false
    },
    team: {
        type: Array,
        required: true
    }
});
/*
    Open file from input
 */
const openFile = (event) => {
    if (event.target.files && event.target.files[0]) {
        let reader = new FileReader();
        reader.onload = (e) => {
            profile_photo_path.value = e.target.result;
        };
        reader.readAsDataURL(event.target.files[0]);
    }
};
/*
  User profile form
 */
const profile_photo_path = ref(user.profile_photo_url);
const profileForm = useForm({
    name: user.name,
    email: user.email,
    contact_number: user.contact_number,
    location: user.location,
    time_zone: user.time_zone,
    photo: null,
    address: userMeta.address,
    customer_name: userMeta.customer_name,
    billing_emails: team.billing_emails,
    billing_emails_status: !team.billing_emails,
});


/*
    Update user profile
 */
const updateProfile = async () => {
    loading.value = true;
    if (file.value.files.length > 0) {
        profileForm.photo = await Vapor.store(file.value.files[0], {
            visibility: 'public-read',
            progress: progress => {
                console.log(Math.round(progress * 100));
            }
        });
    }
    file.value = null;
    submitProfile();
}

const submitProfile = () => {
    profileForm.post(route('user.profile'), {
        preserveScroll: true,
        onError: (message) => {
            console.log(message);
        },
        onFinish: () => {
            loading.value = false;
        }
    });
}

const changeLanguage = () => {
    navigation.updateLanguage(language.value);
    axios.post(route('user.language'), {
        lang: language.value
    }).then(() => {
        window.location.reload();
    }).catch(error => {

    });
}

/*watch(language.value, (newLang) => {
    i18n.global.locale = newLang;
    window.location.reload();
});*/
</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
@import "flag-icons/css/flag-icons.min.css";
</style>
