<template>
    <single-profile active="Server Provider">
        <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex justify-between items-center gap-10px p-30px w-full">
                <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Server Provider') }}</h2>
                <button class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-success-light text-lg
                                mobile:text-base px-25px mobile:px-15px font-medium rounded-10px shadow-none text-white bg-success-light
                                hover:bg-success-full hover:shadow-lg hover:shadow-success-full/30 transition ease-in-out duration-300
                                focus:outline-none leading-none mobile:leading-none">
                    {{ $t('Save Changes') }}
                </button>
            </div>
            <div class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="flex justify-between items-center gap-15px w-full wide-mobile:flex-wrap">
                    <div class="flex flex-col gap-10px">
                        <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('List of Server Providers') }}</h3>
                        <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                            {{ $t('Find all the server providers associated with your account here.') }}
                        </p>
                    </div>
                    <button v-if="permissions.includes('server:add-provider')" @click.prevent="addNewServerProvider"
                        class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                            text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent hover:bg-primary-light
                            hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition ease-in-out duration-300 focus:outline-none
                            leading-tight flex-nowrap whitespace-nowrap">
                        <span>{{ $t('Add New Provider') }}</span>
                    </button>
                </div>
                <div class="flex flex-col gap-30px">
                    <div class="rounded-md flex flex-col">
                        <div class="overflow-x-auto w-full overflow-y-hidden">
                            <table class="w-full">
                                <thead class="bg-primary-light dark:bg-dark">
                                <tr class="divide-x divide-light dark:divide-dark">
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Name') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Provider') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Servers Count') }}
                                    </th>
                                    <th v-if="permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider')" class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Actions') }}
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                                <tr v-for="provider in providers?.data"
                                    :key="provider.id"
                                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                >
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        {{ provider.name }}
                                    </td>
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                          <span
                                              class="text-secondary-full capitalize dark:text-mode-secondary-light group-hover:text-white flex items-center mb-10px">
                                              <img :src="getCloudProvider(provider.provider)" :alt="provider.provider"
                                                   class="w-20px mr-2 group-hover:brightness-200"/>
                                              {{ provider.provider.replace('_', ' ') }}
                                          </span>
                                    </td>
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                      {{ provider.servers_count }}
                                    </td>
                                    <td v-if="permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider')" class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                                          <span class="flex gap-30px">
                                            <button v-if="permissions.includes('server:edit-provider')" @click.prevent="edit(provider)"
                                                    class="relative group cursor-pointer mt-1.5">
                                              <span class="text-sm flex text-secondary-light group-hover:text-white">
                                                <i class="xcloud xc-edit"></i>
                                              </span>
                                            </button>
                                            <button v-if="permissions.includes('server:delete-provider')"
                                                    @click.prevent="destroy(provider)"
                                                    class="relative group cursor-pointer mt-1.5"
                                            >
                                              <span class="text-sm flex text-secondary-light group-hover:text-white">
                                                <i class="xcloud xc-delete"></i>
                                              </span>
                                            </button>
                                          </span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- pagination -->
                    <pagination :links="providers?.links"/>
                </div>
            </div>
        </div>
    </single-profile>
    <Modal
        @footerClick="updateServerProvider"
        :footer-button-title="$t('Save Changes')"
        :footer-button="true"
        :closeable="true"
        @close="closeModal"
        :show="openServerProviderModal"
        :widthClass="'max-w-850px'"
        :title="`${is_edit? $t('Edit') : $t('Add')} `+$t('Server Provider')">

        <div class="flex flex-col gap-30px">
            <div class="grid grid-cols-2 wide-mobile:grid-cols-1">
                <Multiselect
                    :caret="false"
                    disabled
                    :can-clear="false"
                    v-model="form.provider_id"
                    :placeholder="$t('Select Server Provider')"
                    :label="$t('Server')"
                    :options="items()"
                >
                    <template v-slot:singlelabel="{ value }">
                        <div class="multiselect-single-label rounded-md">
                            <img class="character-label-icon h-6 w-6 mr-2" :src="value.icon" alt="provider">
                            {{ value.name }}
                        </div>
                    </template>

                    <template v-slot:option="{ option }">
                        <img class="character-option-icon h-6 w-6 mr-2" :src="option.icon" alt=""> {{ option.name }}
                    </template>
                </Multiselect>
            </div>
            <div class="grid grid-cols-2 wide-mobile:grid-cols-1  items-center">
                <div>
                    <text-input v-model="form.label"
                                :error="form.errors.label"
                                type="text"
                                :label="$t('Label')"/>
                    <span v-if="connection_error" class="capitalize text-red-500">{{ connection_error }}</span>
                </div>
            </div>

            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10" v-if="form.cloud_provider_name === 'vultr'">
                <password-input
                    icon=""
                    type="text"
                    :label="$t('Vultr API Key')"
                    :placeholder="$t('API Key')"
                    :error="form.errors.api_key"
                    v-model="form.api_key">
                </password-input>
            </div>

            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10" v-if="form.cloud_provider_name === 'hetzner'">
                <password-input
                    icon=""
                    type="text"
                    :label="$t('Hetzner API Key')"
                    :placeholder="$t('API Key')"
                    :error="form.errors.api_key"
                    v-model="form.api_key">
                </password-input>
            </div>

            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 items-center">
                <button @click="checkConnection()"
                        class="flex-1 bg-success-light flex justify-center items-center max-h-90px text-base font-normal
                            text-white p-2 px-10px w-44 ml-auto rounded-md">
                  <span class="capitalize">{{ connection_text }}</span>
                </button>
            </div>
        </div>
    </Modal>

    <Modal
      @footerClick="addServerProvider"
        :footer-button="true"
      :footer-button-title="form.cloud_provider_name === 'digitalocean' ? $t('Authorize on Digital Ocean') : $t('Verify & Save for')+` ${useCapitalizeString(form.cloud_provider_name).capitalize()}`"
      :closeable="true"
      @close="addServerProviderModal = false"
      :show="addServerProviderModal"
      :widthClass="'max-w-1120px'"
      :title="$t('Add Server Provider')">

        <div class="flex flex-col gap-30px">
            <div class="grid grid-cols-auto-60 gap-4">
                <radio-input-grid
                    v-for="provider in props.availableProviders"
                    v-model="form.cloud_provider_name"
                    :error="form.errors.cloud_provider_name"
                    :value="provider.provider"
                    :title="provider.name"
                    :icon="useCloudProviderIcon(provider.provider).cloudProviderIcon.value"
                />

                <div class="flex w-full">
                    <Error :error="form.errors.cloud_provider_name" />
                </div>
            </div>

            <!--<div class="grid grid-cols-2 wide-mobile:grid-cols-1">
                <div>
                    <select-input
                      class="my-2"
                      id="server_size"
                      v-model="form.cloud_provider_name"
                      :error="form.errors.cloud_provider_name"
                      label="Select Provider"
                      placeholder="Server Provider"
                      @change="clearForm"
                    >
                        <option v-for="provider in props.availableProviders"
                                :value="provider.provider"
                                v-text="provider.name"
                        >
                        </option>

                    </select-input>
                </div>
            </div>-->

            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10" v-if="form.cloud_provider_name === 'hetzner'">
                <text-input
                    type="text"
                    :label="$t('Hetzner Label')"
                    :placeholder="$t('Label')"
                    :error="form.errors.label"
                    v-model="form.label">
                </text-input>

                <password-input
                    icon=""
                    type="text"
                    :label="$t('Hetzner API Key')"
                    :placeholder="$t('API Key')"
                    :error="form.errors.api_key"
                    v-model="form.api_key">
                </password-input>
            </div>

            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10" v-if="form.cloud_provider_name === 'vultr'">
                <text-input
                    type="text"
                    :label="$t('Vultr Label')"
                    :placeholder="$t('Label')"
                    :error="form.errors.label"
                    v-model="form.label">
                </text-input>

                <password-input
                    icon=""
                    type="text"
                    :label="$t('Vultr API Key')"
                    :placeholder="$t('API Key')"
                    :error="form.errors.api_key"
                    v-model="form.api_key">
                </password-input>
            </div>

            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10" v-if="form.cloud_provider_name === 'gcp'">
                <div class="mt-auto">
                    <input
                        type="file"
                        ref="fileInput"
                        accept="application/json"
                        class="hidden"
                        @change="openFile"/>
                    <span
                        v-if="form.label === ''"
                        @click="$refs.fileInput.click()"
                        class="inline-flex items-center justify-center min-h-60px tablet:min-h-60px border-1
                                       border-primary-light text-lg px-25px py-1 font-medium rounded-10px shadow-none
                                       text-primary-light bg-transparent hover:bg-primary-light hover:text-white
                                       hover:shadow-lg hover:shadow-primary-light/30 transition ease-in-out duration-300
                                       focus:outline-none leading-tight flex-nowrap whitespace-nowrap w-full cursor-pointer">
                                    <span class="i xcloud xc-upload_2 mr-2.5"></span>
                                    <span>{{ $t('Upload JSON') }}</span>
                                </span>
                    <span v-else
                          class="inline-flex items-center justify-center min-h-60px tablet:min-h-60px border-1
                                             border-primary-light text-lg px-25px py-1 font-medium rounded-10px shadow-none
                                             text-primary-light bg-transparent transition ease-in-out duration-300
                                             focus:outline-none leading-tight flex-nowrap whitespace-nowrap w-full">
                        <span>{{form.label}}</span>
                        <button class="ml-2 text-danger" @click="clearForm">&#10005;</button>
                    </span>
                </div>
                <Error :error="form.errors.api_key" />
            </div>


            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-10" v-if="form.cloud_provider_name === 'aws'">

                <AWSServices v-model="form.service_names" :error="form.errors.service_names"/>

                <div
                    class="grid grid-cols-1 tablet:grid-cols-1 mobile:grid-cols-1 gap-30px tablet:gap-y-25px mb-10">

                    <text-input
                        type="text"
                        :label="$t('Label for AWS Credential')"
                        :placeholder="$t('Label')"
                        :error="form.errors.label"
                        v-model="form.label">
                    </text-input>


                    <password-input
                        icon=""
                        type="text"
                        :label="$t('AWS Access Key')"
                        :placeholder="$t('Access Key')"
                        :error="form.errors.aws_access_key"
                        v-model="form.aws_access_key">
                    </password-input>

                    <password-input
                        icon=""
                        type="text"
                        :label="$t('AWS Secret Key')"
                        :placeholder="$t('Secret Key')"
                        :error="form.errors.aws_secret_key"
                        v-model="form.aws_secret_key">
                    </password-input>
                </div>

            </div>


            <p v-if="form.cloud_provider_name==='digitalocean'" class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-15px">
                {{ $t('Get help from our') }} <a target="_blank" class="underline text-primary-light" href="https://xcloud.host/docs/how-to-setup-your-server-with-digital-ocean/">{{ $t('Setup A Digital Ocean Server In xCloud') }}</a> {{ $t('documentation') }}
            </p>

            <p v-else-if="form.cloud_provider_name==='vultr'" class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-15px">
                {{ $t('Get help from our') }} <a target="_blank" class="underline text-primary-light" href="https://xcloud.host/docs/how-to-get-free-hosting-with-vultr/">{{ $t('Setup A Vultr Server In xCloud') }}</a> {{ $t('documentation') }}
            </p>

            <p v-else-if="form.cloud_provider_name==='gcp'" class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-15px">
                {{ $t('Get help from our') }} <a target="_blank" class="underline text-primary-light" href="https://xcloud.host/docs/first-server-with-google-cloud-platform/">{{ $t('Setup A GCP Server In xCloud') }}</a> {{ $t('documentation') }}
            </p>


        </div>
    </Modal>
</template>
<script setup>
import {ref} from "vue";
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Modal from "@/Shared/Modal.vue"
import TextInput from '@/Shared/TextInput.vue';
import PasswordInput from '@/Shared/PasswordInput.vue';
import {useForm, usePage} from '@inertiajs/inertia-vue3';
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import Multiselect from "@vueform/multiselect";
import Button from "@/Jetstream/Button.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import {useCapitalizeString} from "@/Composables/useCapitalizeString";
import Error from "@/Shared/Error.vue";
import RadioInputGrid from "@/Shared/RadioInputGrid.vue";
import RadioInput from "@/Shared/RadioInput.vue";
import AWSServices from "@/Pages/Server/Create/AWS/AWSServices.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
    providers: {
        type: Object,
        required: true
    },
    availableProviders: {
        type: Object,
        required: true,
    },
    uniqueProviders: {
        type: Object,
    },
    access_permissions: {
        type: Object,
        required: true
    }
});
const permissions = Object.values(props?.access_permissions ?? {});

let openServerProviderModal = ref(false)
let addServerProviderModal = ref(false)
const is_edit = ref(false);
const connection_text = ref(t('check connection'));
const connection_error = ref(null);

const closeModal = () => {
    addServerProviderModal.value = false;
    openServerProviderModal.value = false;
    is_edit.value = false;
    connection_text.value = t('check connection');
}
const form = useForm({
    cloud_provider_name: props.availableProviders[0].provider,
    label: '',
    api_key: '',
    provider_id: '',
    service_names: [],
    aws_access_key: '',
    aws_secret_key: '',
    doNotRedirect: true, // this will prevent redirection and return success message in json
    is_edit: false
});

const addNewServerProvider = () => {
    clearForm();
    addServerProviderModal.value = true
}

const edit = (provider) => {
    form.provider_id = provider.id
    form.label = provider.name
    form.cloud_provider_name = provider.provider

    // TODO: need to change this conditional fields, we can store credentials in a single field
    if(provider.provider === 'digitalocean'){
        form.api_key = provider.do_token
    } else if(provider.provider === 'gcp'){
        form.api_key = provider.gcp_access
    }else {
        form.api_key = provider.api_key
    }

    is_edit.value = provider.id
    form.is_edit = true
    openServerProviderModal.value = true;
}
const checkConnection = () => {
    connection_text.value = 'checking...';
    connection_error.value = null;
    form.post(route('api.server.provider.connection'), {
        preserveScroll: true,
        onSuccess: (response) => {
            connection_text.value = 'Connection Successful';
        },
        onError: (error) => {
            connection_text.value = 'Connection Failed';
            connection_error.value = error.response?.data?.message;
            useFlash().error('Connection Failed')
        }
    });
}

const fileInput = ref(null);

const openFile = (event) => {
    const file = event.target.files[0];
    if (!file) {
        return;
    }
    const reader = new FileReader();
    reader.readAsText(file, 'UTF-8');
    reader.onload = (e) => {
        const data = JSON.parse(e.target.result);
        form.api_key = btoa(e.target.result);
        form.label = data.project_id;
    };
    reader.onerror = (e) => {

    };
};


const clearForm = () => {
    form.label = '';
    form.api_key = '';
    form.errors.label = '';
    form.errors.api_key = '';
    form.service_names = [];
    form.aws_access_key = '';
    form.aws_secret_key = '';
    form.provider_id = '';

    if (fileInput.value) {
        fileInput.value.value = '';  // Resetting the file input directly
    }
};


function addServerProvider(){
    // need to refactor frontend to remove if-else conditions
    if(form.cloud_provider_name === 'digitalocean' || form.cloud_provider_name === 'linode'){
        window.location.href = route('oauth.redirect', {
            provider: form.cloud_provider_name
        })
    }else{
        form.post(route('api.cloudProvider.validate.credential'), {
            preserveScroll: true,
            onSuccess: (response) => {
                clearForm();
                closeModal();
                useFlash().success('Cloud Provider Verified and Added Successfully')
            },
            onError: (error) => {
                useFlash().error('Cloud Provider Credential Failed to Verify')
            }
        });
    }
}

const updateServerProvider = () => {
    if (form.cloud_provider_name === 'vultr' || form.cloud_provider_name === 'hetzner') {
        form.post(route('api.server.provider.connection'), {
            preserveScroll: true,
            onSuccess: (response) => {
                if (is_edit.value) {
                    form.put(route('user.profile.server.update', is_edit.value), {
                        preserveScroll: true,
                        onSuccess: () => {
                            closeModal()
                            useFlash().success('Server Provider Updated Successfully')
                        }
                    });
                }
            },
            onError: (error) => {
                connection_error.value = error.response?.data?.message;
                useFlash().error('Connection Failed')
            }
        });
    } else {
        if (is_edit.value) {
            form.put(route('user.profile.server.update', is_edit.value), {
                preserveScroll: true,
                onSuccess: () => {
                    closeModal()
                    useFlash().success('Server Provider Updated Successfully')
                }
            });
        }
    }
}

let items = () => {
    let providers = [];
    for (let key in props.providers.data) {
        const {cloudProviderIcon} = useCloudProviderIcon(props.providers.data[key].provider);
        providers.push({
            value: props.providers.data[key].id,
            name: props.providers.data[key].name,
            icon: cloudProviderIcon
        })
    }
    return providers;
}

const destroy = (provider) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure?'),
        text: t("You won't be able to revert this!"),
        btn_text: t('Yes, Remove!'),
    }, () => {
        Inertia.delete(route('user.profile.server.remove', [provider.id]), {
            preserveScroll: true,
            onSuccess: () => {
                useFlash().success('Redirection removed successfully')
            }
        })
    });
}
const getCloudProvider = (provider) => {
    const {cloudProviderIcon} = useCloudProviderIcon(provider);
    return cloudProviderIcon.value;
};


</script>


<style>
@import '@vueform/multiselect/themes/tailwind.css';

.multiselect.is-disabled {
    @apply bg-white dark:bg-mode-light;
}
</style>
