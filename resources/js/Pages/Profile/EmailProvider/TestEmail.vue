<template>

        <div class="flex flex-col gap-30px">
            <div class="flex flex-col gap-30px">
                <text-input v-model="form.from_email"
                    :error="form.errors.from_email"
                    type="text"
                    note="The email address/domain need to be verified in your email provider."
                    :label="$t('From Email')"
                />
                <text-input v-model="form.email"
                    :error="form.errors.email"
                    type="text"
                    :label="$t('To Email')"
                    note="Type the email where you want to send the test email."
                />
            </div>
        </div>

</template>

<script setup>
import TextInput from "@/Shared/TextInput.vue";

defineProps({
    form: {
        type: Object,
        required: true
    },

})
</script>
