<template>
  <single-profile active="xCloud Storage Provider">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
      <div class="flex justify-between items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Bucket Usage Report') }}: {{ storageProvider.bucket_name }}</h2>
        <button @click.prevent="goBack"
                class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                          text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent
                          hover:bg-primary-light hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition
                          duration-300 ease-in-out">
          {{ $t('Back to Storage Providers') }}
        </button>
      </div>

      <!-- Date Range Selector -->
      <div class="p-30px wide-mobile:p-20px">
        <div class="flex flex-wrap gap-4 mb-6">
          <div class="flex flex-col gap-2">
            <label class="text-dark dark:text-white text-sm font-medium">{{ $t('Start Date') }}</label>
            <DatePicker
              v-model="dateRange.start_date"
              :max-date="new Date()"
              :masks="{ input: 'YYYY-MM-DD' }"
              :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
              class="w-48 date-picker-custom"
            >
              <template v-slot="{ inputValue, inputEvents }">
                <div class="relative">
                  <input
                    :value="inputValue"
                    v-on="inputEvents"
                    class="w-full px-3 py-2 border border-light dark:border-mode-base rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:bg-mode-base dark:text-white"
                    placeholder="YYYY-MM-DD"
                  />
                  <div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-secondary-full dark:text-secondary-light">
                    <i class="xcloud xc-calendar"></i>
                  </div>
                </div>
              </template>
            </DatePicker>
          </div>
          <div class="flex flex-col gap-2">
            <label class="text-dark dark:text-white text-sm font-medium">{{ $t('End Date') }}</label>
            <DatePicker
              v-model="dateRange.end_date"
              :max-date="new Date()"
              :masks="{ input: 'YYYY-MM-DD' }"
              :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
              class="w-48 date-picker-custom"
            >
              <template v-slot="{ inputValue, inputEvents }">
                <div class="relative">
                  <input
                    :value="inputValue"
                    v-on="inputEvents"
                    class="w-full px-3 py-2 border border-light dark:border-mode-base rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:bg-mode-base dark:text-white"
                    placeholder="YYYY-MM-DD"
                  />
                  <div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-secondary-full dark:text-secondary-light">
                    <i class="xcloud xc-calendar"></i>
                  </div>
                </div>
              </template>
            </DatePicker>
          </div>
          <div class="flex items-end">
            <button @click="applyDateFilter"
                    class="inline-flex items-center justify-center min-h-[42px] border-1 border-primary-light
                          text-base px-4 py-1 font-medium rounded-10px shadow-none text-white bg-primary-light
                          hover:shadow-lg hover:shadow-primary-light/30 transition duration-300 ease-in-out">
              {{ $t('Apply Filter') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="p-30px wide-mobile:p-20px">
        <div class="grid grid-cols-4 tablet:grid-cols-2 mobile:grid-cols-1 gap-4">
          <!-- Storage Usage Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('Storage Usage') }}</h3>
                <div class="mt-2 flex items-baseline">
                  <span class="text-2xl font-semibold text-dark dark:text-white">{{ formatNumber(summary.total_stored_gb) }}</span>
                  <span class="ml-1 text-sm text-secondary-full dark:text-secondary-light">GB</span>
                </div>
                <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
                  {{ $t('of') }} {{ summary.bucket_limit }} GB {{ $t('limit') }}
                </div>
              </div>
              <div class="w-16 h-16">
                <ProgressBar :percentage="Math.min(Math.round((summary.total_stored_gb / summary.bucket_limit) * 100), 100)" />
              </div>
            </div>
            <div v-if="summary.over_usage > 0" class="mt-2 text-sm text-danger">
              {{ $t('Over usage') }}: {{ summary.over_usage.toFixed(2) }} GB
              <div>{{ $t('Additional charge') }}: ${{ summary.over_usage_charge.toFixed(2) }}</div>
            </div>
          </div>

          <!-- Uploaded Data Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('Uploaded Data') }}</h3>
            <div class="mt-2 flex items-baseline">
              <span class="text-2xl font-semibold text-dark dark:text-white">{{ formatNumber(summary.total_uploaded_gb) }}</span>
              <span class="ml-1 text-sm text-secondary-full dark:text-secondary-light">GB</span>
            </div>
            <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
              {{ $t('Total data uploaded in period') }}
            </div>
          </div>

          <!-- Downloaded Data Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('Downloaded Data') }}</h3>
            <div class="mt-2 flex items-baseline">
              <span class="text-2xl font-semibold text-dark dark:text-white">{{ formatNumber(summary.total_downloaded_gb) }}</span>
              <span class="ml-1 text-sm text-secondary-full dark:text-secondary-light">GB</span>
            </div>
            <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
              {{ $t('Total data downloaded in period') }}
            </div>
          </div>

          <!-- API Transactions Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('API Transactions') }}</h3>
            <div class="mt-2 flex items-baseline">
              <span class="text-2xl font-semibold text-dark dark:text-white">{{ summary.total_api_transactions.toLocaleString() }}</span>
            </div>
            <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
              {{ $t('Total API calls in period') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Charts -->
      <div class="p-30px wide-mobile:p-20px">
        <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
          <!-- Storage Usage Chart -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white mb-4">{{ $t('Storage Usage Over Time') }}</h3>
            <div class="h-64">
              <div v-if="props.hasData" class="flex flex-col items-center justify-center h-full">
                <div class="text-center text-secondary-full dark:text-secondary-light">
                  <div class="text-lg font-medium mb-2">{{ $t('Current Storage') }}: {{ formatNumber(summary.total_stored_gb) }} GB</div>
                  <div class="text-sm">{{ $t('of') }} {{ summary.bucket_limit }} GB {{ $t('limit') }}</div>
                  <div class="w-full max-w-md mx-auto mt-4 bg-light dark:bg-mode-base rounded-full h-4 overflow-hidden">
                    <div class="bg-primary-light h-full" :style="{ width: Math.min(Math.round((summary.total_stored_gb / summary.bucket_limit) * 100), 100) + '%' }"></div>
                  </div>
                  <div class="mt-2 text-sm">{{ Math.min(Math.round((summary.total_stored_gb / summary.bucket_limit) * 100), 100) }}% {{ $t('used') }}</div>
                </div>
              </div>
              <div v-else class="flex justify-center items-center h-full text-secondary-full dark:text-secondary-light">
                {{ $t('No data available for the selected period') }}
              </div>
            </div>
          </div>

          <!-- Data Transfer Chart -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white mb-4">{{ $t('Data Transfer') }}</h3>
            <div class="h-64">
              <div v-if="props.hasData" class="flex flex-col items-center justify-center h-full">
                <div class="grid grid-cols-2 gap-6 w-full">
                  <div class="text-center">
                    <div class="text-3xl font-bold text-primary-light">{{ formatNumber(summary.total_uploaded_gb) }}</div>
                    <div class="text-sm text-secondary-full dark:text-secondary-light">{{ $t('GB Uploaded') }}</div>
                  </div>
                  <div class="text-center">
                    <div class="text-3xl font-bold text-warning">{{ formatNumber(summary.total_downloaded_gb) }}</div>
                    <div class="text-sm text-secondary-full dark:text-secondary-light">{{ $t('GB Downloaded') }}</div>
                  </div>
                </div>
              </div>
              <div v-else class="flex justify-center items-center h-full text-secondary-full dark:text-secondary-light">
                {{ $t('No data available for the selected period') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Reports Table -->
      <div class="p-30px wide-mobile:p-20px">
        <h3 class="text-xl font-medium text-dark dark:text-white mb-4">{{ $t('Detailed Reports') }}</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-light dark:divide-mode-base">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Date') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Stored (GB)') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Uploaded (GB)') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Downloaded (GB)') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Class A Txns') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Class B Txns') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Class C Txns') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-mode-light divide-y divide-light dark:divide-mode-base">
              <tr v-for="report in reports" :key="report.id" class="hover:bg-light dark:hover:bg-mode-base">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.formatted_date || formatDate(report.date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.stored_gb }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.uploaded_gb }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.downloaded_gb }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.api_txn_class_a }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.api_txn_class_b }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.api_txn_class_c }}
                </td>
              </tr>
              <tr v-if="reports.length === 0">
                <td colspan="7" class="px-6 py-4 text-center text-sm text-secondary-full dark:text-secondary-light">
                  {{ $t('No reports available for the selected period') }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </single-profile>
</template>

<script setup>
import { ref, computed } from 'vue';
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import ProgressBar from "@/Shared/Icons/ProgressBar.vue";
import { DatePicker } from 'v-calendar';
import 'v-calendar/style.css';
import { Inertia } from '@inertiajs/inertia';
import { useI18n } from 'vue-i18n';



const { t } = useI18n();

// Props
const props = defineProps({
  storageProvider: Object,
  reports: Array,
  summary: Object,
  chartData: Object,
  dateRange: Object,
  hasData: Boolean
});

// Reactive state
const dateRange = ref({
  start_date: props.dateRange?.start_date || formatDateForDisplay(new Date(new Date().setDate(new Date().getDate() - 30))),
  end_date: props.dateRange?.end_date || formatDateForDisplay(new Date())
});





// Methods
const goBack = () => {
  Inertia.visit(route('addons.s3-storage-provider'));
};

const applyDateFilter = () => {
  Inertia.visit(route('addons.s3-storage-provider.report', props.storageProvider.id), {
    data: {
      start_date: formatDateForApi(dateRange.value.start_date),
      end_date: formatDateForApi(dateRange.value.end_date)
    }
  });
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const formatDateForApi = (date) => {
  if (typeof date === 'string') {
    return date; // Already formatted as YYYY-MM-DD
  }
  return date instanceof Date ? date.toISOString().split('T')[0] : '';
};

const formatDateForDisplay = (date) => {
  if (!date) return '';
  const d = date instanceof Date ? date : new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

const formatNumber = (value) => {
  if (value === null || value === undefined) return '0.00';
  return Number(value).toFixed(2);
};

const formatApiTransactions = (value) => {
  if (value === null || value === undefined) return '0';
  return Number(value).toLocaleString();
};
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';

/* Custom styles for date picker */
.date-picker-custom .vc-container {
  border: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.date-picker-custom .vc-header {
  padding: 10px 0;
}

.date-picker-custom .vc-weeks {
  padding: 0 10px 10px;
}

.date-picker-custom .vc-day {
  transition: all 0.2s;
}

.date-picker-custom .vc-day-content {
  font-weight: 500;
}

.date-picker-custom .vc-day-content:hover {
  background-color: rgba(var(--primary-light-rgb), 0.1);
  color: var(--primary-light);
}

.date-picker-custom .vc-highlight {
  background-color: var(--primary-light);
}

.date-picker-custom .vc-nav-title {
  font-weight: 600;
  color: var(--dark);
}

.dark .date-picker-custom .vc-nav-title {
  color: var(--white);
}

.dark .date-picker-custom .vc-container {
  background-color: var(--mode-base);
  border-color: var(--mode-light);
}

.dark .date-picker-custom .vc-day-content {
  color: var(--white);
}

.dark .date-picker-custom .vc-weekday {
  color: var(--secondary-light);
}
</style>
