<template>
  <single-profile active="xCloud Storage Provider">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
      <div class="flex justify-between items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Bucket Usage Report') }}: {{ storageProvider.bucket_name }}</h2>
        <button @click.prevent="goBack"
                class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                          text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent
                          hover:bg-primary-light hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition
                          duration-300 ease-in-out">
          {{ $t('Back to Storage Providers') }}
        </button>
      </div>

      <!-- Date Range Selector -->
      <div class="p-30px wide-mobile:p-20px">
        <div class="flex flex-wrap gap-4 mb-6">
          <div class="flex flex-col gap-2">
            <label class="text-dark dark:text-white text-sm font-medium">{{ $t('Start Date') }}</label>
            <DatePicker v-model="dateRange.start_date" :max-date="new Date()" class="w-48" />
          </div>
          <div class="flex flex-col gap-2">
            <label class="text-dark dark:text-white text-sm font-medium">{{ $t('End Date') }}</label>
            <DatePicker v-model="dateRange.end_date" :max-date="new Date()" class="w-48" />
          </div>
          <div class="flex items-end">
            <button @click="applyDateFilter"
                    class="inline-flex items-center justify-center min-h-[42px] border-1 border-primary-light
                          text-base px-4 py-1 font-medium rounded-10px shadow-none text-white bg-primary-light
                          hover:shadow-lg hover:shadow-primary-light/30 transition duration-300 ease-in-out">
              {{ $t('Apply Filter') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="p-30px wide-mobile:p-20px">
        <div class="grid grid-cols-4 tablet:grid-cols-2 mobile:grid-cols-1 gap-4">
          <!-- Storage Usage Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('Storage Usage') }}</h3>
                <div class="mt-2 flex items-baseline">
                  <span class="text-2xl font-semibold text-dark dark:text-white">{{ formatNumber(summary.total_stored_gb) }}</span>
                  <span class="ml-1 text-sm text-secondary-full dark:text-secondary-light">GB</span>
                </div>
                <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
                  {{ $t('of') }} {{ summary.bucket_limit }} GB {{ $t('limit') }}
                </div>
              </div>
              <div class="w-16 h-16">
                <ProgressBar :percentage="Math.min(Math.round((summary.total_stored_gb / summary.bucket_limit) * 100), 100)" />
              </div>
            </div>
            <div v-if="summary.over_usage > 0" class="mt-2 text-sm text-danger">
              {{ $t('Over usage') }}: {{ summary.over_usage.toFixed(2) }} GB
              <div>{{ $t('Additional charge') }}: ${{ summary.over_usage_charge.toFixed(2) }}</div>
            </div>
          </div>

          <!-- Uploaded Data Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('Uploaded Data') }}</h3>
            <div class="mt-2 flex items-baseline">
              <span class="text-2xl font-semibold text-dark dark:text-white">{{ formatNumber(summary.total_uploaded_gb) }}</span>
              <span class="ml-1 text-sm text-secondary-full dark:text-secondary-light">GB</span>
            </div>
            <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
              {{ $t('Total data uploaded in period') }}
            </div>
          </div>

          <!-- Downloaded Data Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('Downloaded Data') }}</h3>
            <div class="mt-2 flex items-baseline">
              <span class="text-2xl font-semibold text-dark dark:text-white">{{ formatNumber(summary.total_downloaded_gb) }}</span>
              <span class="ml-1 text-sm text-secondary-full dark:text-secondary-light">GB</span>
            </div>
            <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
              {{ $t('Total data downloaded in period') }}
            </div>
          </div>

          <!-- API Transactions Card -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white">{{ $t('API Transactions') }}</h3>
            <div class="mt-2 flex items-baseline">
              <span class="text-2xl font-semibold text-dark dark:text-white">{{ summary.total_api_transactions.toLocaleString() }}</span>
            </div>
            <div class="mt-1 text-sm text-secondary-full dark:text-secondary-light">
              {{ $t('Total API calls in period') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Charts -->
      <div class="p-30px wide-mobile:p-20px">
        <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
          <!-- Storage Usage Chart -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white mb-4">{{ $t('Storage Usage Over Time') }}</h3>
            <div v-if="props.hasData" class="h-64">
              <canvas ref="storageChartRef"></canvas>
            </div>
            <div v-else class="flex justify-center items-center h-64 text-secondary-full dark:text-secondary-light">
              {{ $t('No data available for the selected period') }}
            </div>
          </div>

          <!-- Data Transfer Chart -->
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white mb-4">{{ $t('Data Transfer') }}</h3>
            <div v-if="props.hasData" class="h-64">
              <canvas ref="transferChartRef"></canvas>
            </div>
            <div v-else class="flex justify-center items-center h-64 text-secondary-full dark:text-secondary-light">
              {{ $t('No data available for the selected period') }}
            </div>
          </div>
        </div>

        <!-- API Transactions Chart -->
        <div class="mt-6">
          <div class="bg-white dark:bg-mode-light rounded-10px shadow-sm p-5 border border-light dark:border-mode-base">
            <h3 class="text-lg font-medium text-dark dark:text-white mb-4">{{ $t('API Transactions Over Time') }}</h3>
            <div v-if="props.hasData" class="h-64">
              <canvas ref="apiChartRef"></canvas>
            </div>
            <div v-else class="flex justify-center items-center h-64 text-secondary-full dark:text-secondary-light">
              {{ $t('No data available for the selected period') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Reports Table -->
      <div class="p-30px wide-mobile:p-20px">
        <h3 class="text-xl font-medium text-dark dark:text-white mb-4">{{ $t('Detailed Reports') }}</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-light dark:divide-mode-base">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Date') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Stored (GB)') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Uploaded (GB)') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Downloaded (GB)') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Class A Txns') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Class B Txns') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-secondary-full dark:text-secondary-light uppercase tracking-wider">
                  {{ $t('Class C Txns') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-mode-light divide-y divide-light dark:divide-mode-base">
              <tr v-for="report in reports" :key="report.id" class="hover:bg-light dark:hover:bg-mode-base">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.formatted_date || formatDate(report.date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.stored_gb }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.uploaded_gb }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.downloaded_gb }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.api_txn_class_a }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.api_txn_class_b }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-dark dark:text-white">
                  {{ report.api_txn_class_c }}
                </td>
              </tr>
              <tr v-if="reports.length === 0">
                <td colspan="7" class="px-6 py-4 text-center text-sm text-secondary-full dark:text-secondary-light">
                  {{ $t('No reports available for the selected period') }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </single-profile>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import ProgressBar from "@/Shared/Icons/ProgressBar.vue";
import { DatePicker } from 'v-calendar';
import 'v-calendar/style.css';
import { Inertia } from '@inertiajs/inertia';
import { useI18n } from 'vue-i18n';
import Chart from 'chart.js/auto';

// Chart references
const storageChartRef = ref(null);
const transferChartRef = ref(null);
const apiChartRef = ref(null);

// Chart instances
const storageChart = ref(null);
const transferChart = ref(null);
const apiChart = ref(null);

const { t } = useI18n();

// Props
const props = defineProps({
  storageProvider: Object,
  reports: Array,
  summary: Object,
  chartData: Object,
  dateRange: Object,
  hasData: Boolean
});

// Reactive state
const dateRange = ref({
  start_date: props.dateRange?.start_date ? new Date(props.dateRange.start_date) : new Date(new Date().setDate(new Date().getDate() - 30)),
  end_date: props.dateRange?.end_date ? new Date(props.dateRange.end_date) : new Date()
});

// Chart theme state
const isDarkMode = ref(document.documentElement.classList.contains('dark'));

// Initialize and render charts
onMounted(async () => {
  // Set up a MutationObserver to watch for class changes on the html element
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        isDarkMode.value = document.documentElement.classList.contains('dark');
        // Re-render charts when theme changes
        renderCharts();
      }
    });
  });

  observer.observe(document.documentElement, { attributes: true });

  // Initial chart rendering
  await nextTick();
  renderCharts();
});

// Re-render charts when props change
watch(() => props.chartData, () => {
  nextTick(() => renderCharts());
}, { deep: true });

// Function to render all charts
const renderCharts = () => {
  if (!props.hasData) return;

  renderStorageChart();
  renderTransferChart();
  renderApiTransactionsChart();
};

// Render Storage Usage Chart
const renderStorageChart = () => {
  if (storageChart.value) {
    storageChart.value.destroy();
  }

  if (!storageChartRef.value) return;

  const ctx = storageChartRef.value.getContext('2d');
  storageChart.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: props.chartData?.labels || [],
      datasets: [{
        label: t('Storage Usage (GB)'),
        data: props.chartData?.stored_gb || [],
        borderColor: '#32BA7C',
        backgroundColor: 'rgba(50, 186, 124, 0.1)',
        borderWidth: 2,
        tension: 0.4,
        fill: true
      }]
    },
    options: getChartOptions()
  });
};

// Render Data Transfer Chart
const renderTransferChart = () => {
  if (transferChart.value) {
    transferChart.value.destroy();
  }

  if (!transferChartRef.value) return;

  const ctx = transferChartRef.value.getContext('2d');
  transferChart.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: props.chartData?.labels || [],
      datasets: [
        {
          label: t('Uploaded (GB)'),
          data: props.chartData?.uploaded_gb || [],
          borderColor: '#3c9ee5',
          backgroundColor: 'rgba(60, 158, 229, 0.1)',
          borderWidth: 2,
          tension: 0.4
        },
        {
          label: t('Downloaded (GB)'),
          data: props.chartData?.downloaded_gb || [],
          borderColor: '#ff9f00',
          backgroundColor: 'rgba(255, 159, 0, 0.1)',
          borderWidth: 2,
          tension: 0.4
        }
      ]
    },
    options: getChartOptions()
  });
};

// Render API Transactions Chart
const renderApiTransactionsChart = () => {
  if (apiChart.value) {
    apiChart.value.destroy();
  }

  if (!apiChartRef.value) return;

  const ctx = apiChartRef.value.getContext('2d');
  apiChart.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: props.chartData?.labels || [],
      datasets: [{
        label: t('API Transactions'),
        data: props.chartData?.api_transactions || [],
        borderColor: '#9c27b0',
        backgroundColor: 'rgba(156, 39, 176, 0.1)',
        borderWidth: 2,
        tension: 0.4,
        fill: true
      }]
    },
    options: getChartOptions()
  });
};

// Get chart options based on current theme
const getChartOptions = () => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          color: isDarkMode.value ? '#fff' : '#333'
        },
        grid: {
          color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
        }
      },
      x: {
        ticks: {
          color: isDarkMode.value ? '#fff' : '#333'
        },
        grid: {
          color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    plugins: {
      legend: {
        labels: {
          color: isDarkMode.value ? '#fff' : '#333'
        }
      },
      tooltip: {
        backgroundColor: isDarkMode.value ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)',
        titleColor: isDarkMode.value ? '#fff' : '#333',
        bodyColor: isDarkMode.value ? '#fff' : '#333',
        borderColor: isDarkMode.value ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
        borderWidth: 1
      }
    }
  };
};



// Methods
const goBack = () => {
  Inertia.visit(route('addons.s3-storage-provider'));
};

const applyDateFilter = () => {
  Inertia.visit(route('addons.s3-storage-provider.report', props.storageProvider.id), {
    data: {
      start_date: formatDateForApi(dateRange.value.start_date),
      end_date: formatDateForApi(dateRange.value.end_date)
    }
  });
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const formatDateForApi = (date) => {
  return date.toISOString().split('T')[0];
};

const formatNumber = (value) => {
  if (value === null || value === undefined) return '0';
  return Number(value).toFixed(2);
};
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
