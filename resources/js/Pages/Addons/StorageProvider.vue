<template>
  <single-profile active="xCloud Storage Provider">
    <div class="grow flex flex-col bg-gray-50 dark:bg-mode-base min-h-screen">
      <!-- Header -->
      <div class="bg-white dark:bg-mode-light shadow-sm px-6 py-4 flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold text-gray-800 dark:text-white">{{ $t('Storage Provider') }}</h1>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ $t('Manage your cloud storage buckets') }}</p>
        </div>
        <button
          @click.prevent="modalOpen = true"
          class="flex items-center gap-2 bg-primary-light text-white px-4 py-2 rounded-lg hover:bg-primary-light/90 transition-all duration-200 shadow-sm hover:shadow-md"
        >
          <i class="xcloud xc-plus-circle text-lg"></i>
          <span>{{ $t('Add New Bucket') }}</span>
        </button>
      </div>

      <!-- Main Content -->
      <div class="p-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div class="bg-white dark:bg-mode-light rounded-xl shadow-sm p-5 border border-gray-100 dark:border-mode-base">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('Total Buckets') }}</p>
                <h3 class="text-2xl font-semibold text-gray-800 dark:text-white mt-1">{{ storageProviders?.data?.length || 0 }}</h3>
              </div>
              <div class="w-12 h-12 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <i class="xcloud xc-database text-blue-500 dark:text-blue-400 text-xl"></i>
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-mode-light rounded-xl shadow-sm p-5 border border-gray-100 dark:border-mode-base">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('Total Storage') }}</p>
                <h3 class="text-2xl font-semibold text-gray-800 dark:text-white mt-1">
                  {{ calculateTotalStorage() }} GB
                </h3>
              </div>
              <div class="w-12 h-12 bg-green-50 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <i class="xcloud xc-hard-drive text-green-500 dark:text-green-400 text-xl"></i>
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-mode-light rounded-xl shadow-sm p-5 border border-gray-100 dark:border-mode-base">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('Regions') }}</p>
                <h3 class="text-2xl font-semibold text-gray-800 dark:text-white mt-1">
                  {{ calculateUniqueRegions() }}
                </h3>
              </div>
              <div class="w-12 h-12 bg-purple-50 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                <i class="xcloud xc-globe text-purple-500 dark:text-purple-400 text-xl"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Buckets Table -->
        <div class="bg-white dark:bg-mode-light rounded-xl shadow-sm border border-gray-100 dark:border-mode-base overflow-hidden">
          <div class="p-5 border-b border-gray-100 dark:border-mode-base">
            <h2 class="text-lg font-medium text-gray-800 dark:text-white">{{ $t('Your Buckets') }}</h2>
          </div>

          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 dark:bg-mode-base text-left">
                <tr>
                  <th class="px-6 py-4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ $t('Bucket Name') }}
                  </th>
                  <th class="px-6 py-4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ $t('Region') }}
                  </th>
                  <th class="px-6 py-4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ $t('Plan') }}
                  </th>
                  <th class="px-6 py-4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ $t('Usage') }}
                  </th>
                  <th class="px-6 py-4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {{ $t('Actions') }}
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-100 dark:divide-mode-base">
                <tr v-for="storageProvider in storageProviders?.data" :key="storageProvider.id"
                    class="hover:bg-gray-50 dark:hover:bg-mode-base transition-colors duration-150">
                  <td class="px-6 py-4">
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-md flex items-center justify-center mr-3">
                        <i class="xcloud xc-database text-blue-500 dark:text-blue-400"></i>
                      </div>
                      <div>
                        <Link :href="route('addons.s3-storage-provider.report', storageProvider.id)"
                              class="text-gray-900 dark:text-white font-medium hover:text-primary-light dark:hover:text-primary-light transition-colors">
                          {{ storageProvider.bucket_name ?? '-'}}
                        </Link>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 capitalize text-gray-700 dark:text-gray-300">
                    <div class="flex items-center">
                      <i class="xcloud xc-globe text-gray-400 mr-2"></i>
                      {{ storageProvider.region?.replaceAll('-',' ') }}
                    </div>
                  </td>
                  <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                      {{ storageProvider.product?.title ?? '-' }}
                    </span>
                  </td>
                  <td class="px-6 py-4">
                    <div v-if="storageProvider.usage_data">
                      <!-- Storage Usage -->
                      <div class="mb-2">
                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                          <span>{{ formatNumber(storageProvider.usage_data.stored_gb) }} GB
                            <span v-if="storageProvider.usage_data.usage_percentage>0">({{ storageProvider.usage_data.usage_percentage }})%</span>
                          </span>
                          <span>{{ storageProvider?.usage_data?.bucket_limit }} GB</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-mode-base rounded-full h-2">
                          <div
                            class="h-2 rounded-full transition-all duration-500"
                            :class="getUsageColorClass(storageProvider.usage_data.usage_percentage)"
                            :style="{ width: storageProvider.usage_data.usage_percentage + '%' }"
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div v-else class="text-xs text-gray-500 dark:text-gray-400">
                      {{ $t('No usage data available') }}
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="flex space-x-3">
                      <Tooltip :title="$t('Browse Files')" color="primary">
                        <a :href="route('addons.s3-storage-provider.file-manager', storageProvider.id)"
                           class="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors">
                          <i class="xcloud xc-file"></i>
                        </a>
                      </Tooltip>
                      <Tooltip :title="$t('Usage Report')" color="info">
                        <a :href="route('addons.s3-storage-provider.report', storageProvider.id)"
                           class="p-2 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 rounded-full hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors">
                          <i class="xcloud xc-account-1"></i>
                        </a>
                      </Tooltip>
                      <Tooltip :title="$t('Delete Storage Provider')" color="danger">
                        <button @click.prevent="deleteStorageProvider(storageProvider)"
                                class="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                          <i class="xcloud xc-delete"></i>
                        </button>
                      </Tooltip>
                    </div>
                  </td>
                </tr>

                <!-- Empty State -->
                <tr v-if="!storageProviders?.data?.length">
                  <td colspan="5" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center">
                      <div class="w-16 h-16 bg-gray-100 dark:bg-mode-base rounded-full flex items-center justify-center mb-4">
                        <i class="xcloud xc-database text-gray-400 text-2xl"></i>
                      </div>
                      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">{{ $t('No buckets found') }}</h3>
                      <p class="text-gray-500 dark:text-gray-400 mb-4">{{ $t('Get started by creating a new bucket') }}</p>
                      <button
                        @click.prevent="modalOpen = true"
                        class="flex items-center gap-2 bg-primary-light text-white px-4 py-2 rounded-lg hover:bg-primary-light/90 transition-all duration-200"
                      >
                        <i class="xcloud xc-plus-circle"></i>
                        <span>{{ $t('Add New Bucket') }}</span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="p-4 border-t border-gray-100 dark:border-mode-base">
            <pagination :links="storageProviders?.links"/>
          </div>
        </div>
      </div>
    </div>
  </single-profile>

  <!-- Add Provider Modal -->
  <Modal
    @footerClick="submitForm"
    :footer-button="true"
    :footer-button-title="$t('Create Bucket')"
    :closeable="true"
    :loading="form.processing"
    @close="modalOpen = false; form.reset()"
    :show="modalOpen"
    :widthClass="'max-w-850px'"
    :title="$t('Add New Storage Bucket')"
  >
    <div class="p-6">
      <!-- Bucket Icon -->
      <div class="flex justify-center mb-6">
        <div class="w-20 h-20 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
          <i class="xcloud xc-database text-blue-500 dark:text-blue-400 text-3xl"></i>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="grid grid-cols-1 gap-6">
        <!-- Bucket Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ $t('Bucket Name') }}</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="xcloud xc-database text-gray-400"></i>
            </div>
            <input
              v-model="form.bucket_name"
              type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-mode-base rounded-md shadow-sm focus:ring-primary-light focus:border-primary-light dark:bg-mode-base dark:text-white sm:text-sm"
              :placeholder="$t('Enter bucket name')"
            />
          </div>
          <p v-if="form.errors.bucket_name" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.errors.bucket_name }}</p>
          <p v-else class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ $t('Bucket names must be 3–63 characters, lowercase alphanumeric, and can include hyphens.') }}</p>
        </div>

        <!-- Region -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ $t('Region') }}</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="xcloud xc-globe text-gray-400"></i>
            </div>
            <select
              v-model="form.region"
              class="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-mode-base rounded-md shadow-sm focus:ring-primary-light focus:border-primary-light dark:bg-mode-base dark:text-white sm:text-sm"
            >
              <option value="">{{ $t('Select a region') }}</option>
              <option v-for="(region, key) in regions" :key="key" :value="key">
                {{ region }}
              </option>
            </select>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <i class="xcloud xc-chevron-down text-gray-400"></i>
            </div>
          </div>
          <p v-if="form.errors.region" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.errors.region }}</p>
        </div>

        <!-- Plan -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ $t('Plan') }}</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="xcloud xc-tag text-gray-400"></i>
            </div>
            <select
              v-model="form.product_id"
              class="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-mode-base rounded-md shadow-sm focus:ring-primary-light focus:border-primary-light dark:bg-mode-base dark:text-white sm:text-sm"
            >
              <option value="">{{ $t('Select a plan') }}</option>
              <option v-for="(product, key) in products" :key="key" :value="key">
                {{ product }}
              </option>
            </select>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <i class="xcloud xc-chevron-down text-gray-400"></i>
            </div>
          </div>
          <p v-if="form.errors.product_id" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.errors.product_id }}</p>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script setup>
import {ref, computed} from "vue";
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Modal from "@/Shared/Modal.vue"
import {Link, useForm} from '@inertiajs/inertia-vue3';
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import Tooltip from "@/Shared/Tooltip.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import Pagination from "@/Shared/Pagination.vue";

// Main modal state
const modalOpen = ref(false);

const props = defineProps({
  storageProviders: Object,
  regions: Object,
  products: Object
});

const form = useForm({
  bucket_name: '',
  product_id:'',
  region:''
});

// Calculate total storage used across all buckets
function calculateTotalStorage() {
  if (!props.storageProviders?.data?.length) return '0.00';

  const totalGB = props.storageProviders.data.reduce((total, provider) => {
    return total + (provider.usage_data?.stored_gb || 0);
  }, 0);

  return formatNumber(totalGB);
}

// Calculate number of unique regions
function calculateUniqueRegions() {
  if (!props.storageProviders?.data?.length) return 0;

  const uniqueRegions = new Set();
  props.storageProviders.data.forEach(provider => {
    if (provider.region) {
      uniqueRegions.add(provider.region);
    }
  });

  return uniqueRegions.size;
}

// Form submission for adding a new bucket
const submitForm = () => {
  let hasError = false;

  // Clear previous errors
  form.clearErrors();

  // Bucket name should be alphanumeric and allow hyphens but not start or end with a hyphen
  if (!isValidS3BucketName(form.bucket_name)) {
    form.errors.bucket_name = t('Bucket name must be 3–63 characters, lowercase alphanumeric, and can include hyphens (not at the start or end).');
    hasError = true;
  }
  if (!props.regions?.[form.region]) {
    form.errors.region = t('Region is required');
    hasError = true;
  }

  if (!props.products?.[form.product_id]) {
    form.errors.product_id = t('Plan is required');
    hasError = true;
  }
  if (hasError) return;

  // Submit the form
  form.post(route('api.addons.storage-provider.store'), {
    onSuccess: () => {
      modalOpen.value = false;
      form.reset();
      useFlash().success(t('Bucket created successfully'));
    },
    onError: (error) => {
      useFlash().error(error.message || t('Failed to create bucket'));
    }
  });
}

// Delete storage provider
const deleteStorageProvider = (storageProvider) => {
  useFlash().deleteConfirmation({
    title: t('Are you sure you want to delete this storage provider?'),
    text: t('Deleting storage provider will remove it permanently.'),
    width: '600px',
    confirmButtonText: t('Yes, Delete')
  }, () => {
    Inertia.delete(route('api.addons.storage-provider.destroy', storageProvider.id), {
      onSuccess: () => {
        useFlash().success(t('Storage provider deleted successfully'));
      },
      onError: ({error}) => {
        useFlash().error(error ?? t('Failed to delete storage provider'));
      }
    });
  })
}

// Utility functions
function isValidS3BucketName(name) {
  return /^[A-Za-z0-9\-]+$/.test(name) &&
      !name.startsWith('-') &&
      !name.endsWith('-') &&
      name.length >= 3 &&
      name.length <= 63;
}

/**
 * Format a number to 2 decimal places
 */
function formatNumber(value) {
  if (value === null || value === undefined) return '0.00';
  return Number(value).toFixed(2);
}

/**
 * Get the CSS class for the usage progress bar based on percentage
 */
function getUsageColorClass(percentage) {
  if (percentage >= 90) {
    return 'bg-red-500 dark:bg-red-600';
  } else if (percentage >= 75) {
    return 'bg-yellow-500 dark:bg-yellow-600';
  } else {
    return 'bg-primary-light';
  }
}

</script>


<style>
@import '@vueform/multiselect/themes/tailwind.css';

.multiselect.is-disabled {
  @apply bg-white dark:bg-mode-light;
}
</style>
