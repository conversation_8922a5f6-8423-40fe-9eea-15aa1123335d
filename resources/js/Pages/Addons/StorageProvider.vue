<template>
  <single-profile
      active="xCloud Storage Provider">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
      <div class="flex justify-between items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Storage Provider') }}</h2>
      </div>
      <div class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full">
        <div class="flex justify-between items-center gap-15px w-full wide-mobile:flex-wrap">
          <div class="flex flex-col gap-10px">
            <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('List of Storage Providers') }}</h3>
            <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
              {{ $t('Find all storage providers associated with your account here.') }}
            </p>
          </div>
          <button @click.prevent="modalOpen = true"
                  class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                            text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent
                            hover:bg-primary-light hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition
                            ease-in-out duration-300 focus:outline-none leading-tight flex-nowrap whitespace-nowrap">
            <span>{{ $t('Add New Bucket') }}</span>
          </button>
        </div>
        <div class="flex flex-col gap-30px">
          <div class="rounded-md flex flex-col">
            <div class="overflow-x-auto w-full overflow-y-hidden">
              <table class="w-full">
                <thead class="bg-primary-light dark:bg-dark">
                <tr class="divide-x divide-light dark:divide-dark">
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Bucket Name') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Region') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Plan') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Actions') }}
                  </th>
                </tr>
                </thead>
                <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                <tr v-for="storageProvider in storageProviders?.data"
                    :key="storageProvider.id"
                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                >
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                    <Link :href="route('addons.s3-storage-provider.report', storageProvider.id)">
                        {{ storageProvider.bucket_name ?? '-'}}
                    </Link>
                  </td>
                  <td class=" capitalize px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                    {{ storageProvider.region?.replaceAll('-',' ')}}
                  </td>
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                    {{ storageProvider.product?.title ?? '-' }}
                  </td>
                  <td class="relative px-30px py-20px">
                    <div class="flex space-x-4 text-black dark:text-white group-hover:text-white">
                      <Tooltip :title="$t('Browse Files')" color="primary">
                        <a :href="route('addons.s3-storage-provider.file-manager', storageProvider.id)" class="relative group cursor-pointer mt-1.5">
                          <i class="xcloud xc-file "></i>
                        </a>
                      </Tooltip>
                      <Tooltip :title="$t('Usage Report')" color="info">
                        <a :href="route('addons.s3-storage-provider.report', storageProvider.id)" class="relative group cursor-pointer mt-1.5">
                          <i class="xcloud xc-chart-bar "></i>
                        </a>
                      </Tooltip>
                      <Tooltip :title="$t('Delete Storage Provider')" color="danger">
                        <button @click.prevent="deleteStorageProvider(storageProvider)" class="relative group cursor-pointer mt-1.5">
                          <i class="xcloud xc-delete "></i>
                        </button>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <!-- pagination -->
          <pagination :links="storageProviders?.links"/>
        </div>
      </div>
    </div>
  </single-profile>

  <!-- Add Provider Modal -->
  <Modal
      @footerClick="submitForm"
      :footer-button="true"
      :footer-button-title="$t('Submit')"
      :closeable="true"
      :loading="form.processing"
      @close="modalOpen = false; form.reset()"
      :show="modalOpen"
      :widthClass="'max-w-850px'"
      :title="$t('Add Storage Provider')"
  >

    <div class="flex flex-col gap-30px">
    <div class="grid tablet:grid-cols-1 gap-30px">
          <!-- Bucket Name -->
          <TextInput
              :label="$t('Bucket Name')"
              v-model="form.bucket_name"
              :error="form.errors.bucket_name"
              type="text"
              :placeholder="$t('Enter bucket name')"
          >
          </TextInput>

          <!-- region -->
          <select-input
              v-model="form.region"
              :error="form.errors.region"
              id="region"
              :label="$t('Choose a region')"
          >
              <option value="">{{ $t('Select a region') }}</option>
              <option v-for="(region, key) in regions" :key="key" :value="key">
                {{ region }}
              </option>
          </select-input>

          <!-- plan -->
          <select-input
              v-model="form.product_id"
              :error="form.errors.product_id"
              id="plan"
              :label="$t('Choose a plan')"
          >
            <option value="">{{ $t('Select a plan') }}</option>
            <option v-for="(product, key) in products" :key="key" :value="key">
              {{ product }}
            </option>
          </select-input>
        </div>
    </div>
  </Modal>


</template>
<script setup>
import {ref} from "vue";
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Modal from "@/Shared/Modal.vue"
import {Link, useForm} from '@inertiajs/inertia-vue3';
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import Tooltip from "@/Shared/Tooltip.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import TextInput from "@/Shared/TextInput.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import Pagination from "@/Shared/Pagination.vue";

// Main modal state
const modalOpen = ref(false);

const props = defineProps({
  storageProviders: Object,
  regions: Object,
  products: Object
});

const form = useForm({
  bucket_name: '',
  product_id:'',
  region:''
});





// Form submission for adding a new bucket
const submitForm = () => {
    let hasError = false;

    // Clear previous errors
    form.clearErrors();

    // Bucket name should be alphanumeric and allow hyphens but not start or end with a hyphen
  if (!isValidS3BucketName(form.bucket_name)) {
    form.errors.bucket_name = t('Bucket name must be 3–63 characters, lowercase alphanumeric, and can include hyphens (not at the start or end).');
    hasError = true;
  }
  if (!props.regions?.[form.region]) {
    form.errors.region = t('Region is required');
    hasError = true;
  }

    if (!props.products?.[form.product_id]) {
        form.errors.product_id = t('Plan is required');
        hasError = true;
    }
    if (hasError) return;

    // Submit the form
    form.post(route('api.addons.storage-provider.store'), {
        onSuccess: () => {
            modalOpen.value = false;
        }
    });
}

// Delete storage provider
const deleteStorageProvider = (storageProvider) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure you want to delete this storage provider?'),
        text: t('Deleting storage provider will remove it permanently.'),
        width: '600px',
        confirmButtonText: t('Yes, Delete')
    }, () => {
        Inertia.delete(route('api.addons.storage-provider.destroy', storageProvider.id), {
            onSuccess: () => {
                useFlash().success('Storage provider deleted successfully');
            },
            onError: ({error}) => {
                useFlash().error(error ?? 'Failed to delete storage provider');
            }
        });
    })
}



// Utility functions
function isValidS3BucketName(name) {
  return /^[A-Za-z0-9\-]+$/.test(name) &&
      !name.startsWith('-') &&
      !name.endsWith('-') &&
      name.length >= 3 &&
      name.length <= 63;
}



</script>


<style>
@import '@vueform/multiselect/themes/tailwind.css';

.multiselect.is-disabled {
  @apply bg-white dark:bg-mode-light;
}
</style>
