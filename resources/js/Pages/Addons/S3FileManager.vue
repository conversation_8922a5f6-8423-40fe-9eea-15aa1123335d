<template>
  <single-profile
      active="xCloud Storage Provider">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light h-full">
      <div class="flex justify-between flex-col sm:flex-row items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('S3 File Manager') }}: {{ currentBucket?.bucket_name }}</h2>
        <button @click.prevent="goBack"
                class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                          text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent
                          hover:bg-primary-light hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition
                          ease-in-out duration-300 focus:outline-none leading-tight flex-nowrap whitespace-nowrap">
          <span>{{ $t('Back to Storage Providers') }}</span>
        </button>
      </div>
      <div class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full h-full">
        <div class="h-full flex flex-col">
          <!-- Action Buttons -->
          <div class="mb-4 flex items-center flex-col sm:flex-row justify-between gap-2">
            <div class="flex flex-wrap gap-2">
              <button
                @click="showUploadModal = true"
                class="inline-flex items-center justify-center rounded-[4px] border-1 border-primary-light
                      shadow-none min-h-[36px] px-[16px] py-[10px] bg-transparent
                      text-[14px] text-primary-light  focus:outline-0 hover:bg-primary-light hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition
                          ease-in-out duration-300 focus:outline-none leading-tight flex-nowrap whitespace-nowrap"
              >
                  <span class="inline-flex mr-2 text-[14px]">
                      <i class="xcloud xc-upload_2"></i>
                  </span>
                {{ $t('Upload File') }}
              </button>
              <button
                @click="refreshFiles"
                class="inline-flex items-center rounded-3xl shadow-none px-2 py-1 pr-10px bg-success-full/10 text-sm text-success-full focus:outline-0 gap-1 font-medium">
                  <span class="rounded-full bg-success-full h-25px w-25px shrink-0 flex items-center justify-center">
                      <i class="xcloud xc-refresh bg-success text-white" :class="{'animate-spin': isRefreshing}"></i>
                  </span>
                {{ $t('Refresh') }}
              </button>
            </div>
            <div class="flex items-center flex-col md:flex-row gap-[16px]">
              <div class="relative flex items-center group rounded-[4px]">
                <input type="text"
                    v-model="searchQuery"
                    @input="searchFiles"
                    class="flex-auto w-full appearance-none bg-light dark:bg-mode-base px-[16px] py-[10px] rounded text-secondary-full dark:text-[#697586] text-base placeholder-secondary-light dark:placeholder-secondary-full focus:outline-none font-normal border-transparent focus:border-success-light !ring-0"
                    :placeholder="$t('Search file')"/>
                <div
                    class="icon flex absolute top-1/2 right-15px -translate-y-1/2 pointer-events-none text-lg">
                    <i class="xcloud xc-search text-secondary-light group-focus-within:text-success-light" v-if="!searchQuery"></i>
                </div>
                <div
                    v-if="searchQuery"
                    class="icon flex absolute top-1/2 right-15px -translate-y-1/2 cursor-pointer text-sm"
                    @click="clearSearch">
                    <i class="xcloud xc-close1 text-danger"></i>
                </div>
              </div>
              <button
                @click="showCreateFolderModal = true"
                class="whitespace-nowrap inline-flex items-center justify-center rounded-[4px] border-success-full
                        shadow-none min-h-[36px] px-[16px] py-[10px] bg-primary-light
                        text-[14px] text-white focus:outline-0"
              >
                <span class="inline-flex mr-2 text-[14px]">
                    <i class="xcloud xc-add"></i>
                </span>
                {{ $t('Add New Folder') }}
              </button>
            </div>
          </div>
                    <!-- Breadcrumb Navigation -->
          <div class="mb-4 flex items-center text-[14px] font-medium overflow-x-auto whitespace-nowrap">
            <button
              @click="navigateToRoot"
              class="text-primary-light"
            >
              {{ currentBucket?.bucket_name }}
            </button>
                <span class="mx-2">
                  <i class="xcloud xc-angle_right text-primary-light text-[9px]"></i>
                </span>
            <template v-for="(part, index) in pathParts" :key="index">
              <button
                @click="navigateToPath(index)"
                class="text-[#CDD5DF]"
              >
                {{ part }}
              </button>
                <span v-if="index < pathParts.length - 1" class="mx-2">
                <i class="xcloud xc-angle_right text-primary-light text-[9px]"></i>
              </span>
            </template>
          </div>

          <!-- Loading Indicator -->
          <skeleton :rows="8" v-if="loading" />

          <!-- Empty State -->
          <div v-else-if="s3Files.length === 0" class="py-10 text-center">
            <div class="text-4xl font-bold text-gray-300 dark:text-gray-600 mb-2">📁</div>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ $t('No files') }}</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {{ $t('This folder is empty. Upload a file or create a new folder.') }}
            </p>
          </div>

          <!-- File List -->
          <div v-else class="text-gray-700 dark:text-[#CDD5DF] font-sans flex-1">
              <div class="h-full overflow-x-auto">
                <table class="w-full border-collapse relative">
                  <thead>
                    <tr class="bg-[#c9d8ea] dark:bg-[#2a325c] text-gray-600 dark:text-[#9AA4B2] text-left text-[14px] border-b border-gray-300 dark:border-[#3A457E]">
                      <th class="py-[12px] px-[24px] w-[12px] h-[12px] text-gray-600 dark:text-[#9AA4B2] rounded-tl-[8px]">
                        <input 
                          type="checkbox" 
                          :checked="areAllFilesSelected"
                          @change="toggleSelectAll"
                          class="focus:ring-0 focus:ring-offset-0 bg-transparent rounded-[2px]" 
                        />
                      </th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('File Name') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Size') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Last Modified') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Type') }}</th>
                      <th class="py-[12px] px-[24px] font-medium rounded-tr-[8px]"> {{ $t('Action') }}</th>
                    </tr>
                  </thead>
                  <tbody class="rounded-b-[8px]">
                    <tr v-for="file in s3Files" :key="file.key" class="border-b border-gray-100 dark:border-[#232747] text-[14px] font-medium even:bg-gray-50 dark:even:bg-[#171A30] odd:bg-white dark:odd:bg-[#181E37]">
                      <td class="py-[12px] px-[24px]">
                        <input
                          type="checkbox"
                          v-model="file.selected"
                          class="rounded-[2px] focus:ring-0 focus:ring-offset-0 bg-transparent w-[14px] h-[14px]"
                        />
                      </td>
                      <td class="py-[12px] px-[24px]">
                        <div
                          :class="{
                            'cursor-pointer': file.type === 'folder'
                          }"
                          class="flex items-center"
                          @click="navigateTo(file)"
                        >
                          <i
                            class="mr-2 xcloud"
                            :class="file.type === 'folder' ? 'xc-folder text-primary-light' : 'xc-document_3 text-success-full'"
                          >
                          </i>
                          <tooltip :title="getFileName(file.key)">
                            <span
                              class="truncate max-w-[300px] whitespace-nowrap overflow-hidden block">
                              {{ getFileName(file.key) }}
                            </span>
                          </tooltip>
                        </div>
                      </td>
                      <td class="py-[12px] px-[24px]">{{ file.type === 'folder' ? '-' : formatFileSize(file.size) }}</td>
                      <td class="py-[12px] px-[24px]">{{ file.lastModified ? formatDate(file.lastModified) : '-' }}</td>
                      <td class="py-[12px] px-[24px]">{{ file.contentType || (file.type === 'folder' ? 'Directory' : '-') }}</td>
                      <td class="py-[12px] px-[24px]">
                        <div class="flex space-x-2">
                          <div class="relative">
                            <button @click="handleFileActions(file)" class="text-gray-700 dark:text-white hover:text-gray-500 dark:hover:text-gray-400 rotate-90">
                              <i class="xcloud xc-menu-vertical text-[14px]"></i>
                            </button>
                            <div v-if="openFileActions === file" class="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white dark:bg-[#2a325c] shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                              <div class="py-1">
                                <button
                                  @click="showFileInfo(file)"
                                  class="block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-[#181e37] font-medium cursor-pointer">
                                  {{ $t('File Info') }}
                                </button>
                                <button v-if="file.type !== 'folder'" @click="downloadFile(file)" class="block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-[#181e37] font-medium cursor-pointer">
                                  {{ $t('Download') }}
                                </button>
                                <button @click="openRenameModal(file)" class="block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-[#181e37] font-medium cursor-pointer">
                                  {{ $t('Rename') }}
                                </button>
                                <button @click="deleteFile(file)" class="block w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-[#181e37] font-medium cursor-pointer">
                                  {{ $t('Delete') }}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>

              </div>
          </div>
        </div>
      </div>
    </div>
  </single-profile>

  <!-- Upload Modal -->
  <Modal
  :show="showUploadModal"
  @close="showUploadModal = false"
  :title="$t('Upload File')"
  :widthClass="'max-w-4xl'"
  >
    <div>
      <FileUploader
        :upload-url="route('api.addons.storage-provider.upload-file', currentBucket?.id)"
        :additional-data="{ path: currentPath === '/' ? '' : currentPath }"
        @upload-complete="handleUploadComplete"
        @upload-error="handleUploadError"
        @upload-cancel="showUploadModal = false"
      />
    </div>
  </Modal>

  <!-- Create Folder Modal -->
  <Modal :show="showCreateFolderModal" @close="showCreateFolderModal = false" :title="$t('Create New Folder')">
    <div class="p-6">
      <div class="mt-4">
        <div class="mt-2">
          <input
            type="text"
            v-model="newFolderName"
            :placeholder="$t('Folder name')"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        <div class="mt-6 flex justify-end">
          <button
            @click="showCreateFolderModal = false"
            class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none mr-2"
          >
            {{ $t('Cancel') }}
          </button>
          <button
            @click="createFolder"
            :disabled="!newFolderName"
            class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ $t('Create') }}
          </button>
        </div>
      </div>
    </div>
  </Modal>

  <!-- Rename Modal -->
  <Modal :show="showRenameModal" @close="closeRenameModal" :title="fileToRename?.type === 'folder' ? $t('Rename Folder') : $t('Rename File')">
    <div class="p-6">
      <div class="mt-4">
        <div class="mt-2">
          <input
            type="text"
            v-model="newName"
            :placeholder="$t('New name')"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          />
        </div>
        <div class="mt-6 flex justify-end">
          <button
            @click="closeRenameModal"
            class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none mr-2"
          >
            {{ $t('Cancel') }}
          </button>
          <button
            @click="renameFile"
            :disabled="!newName"
            class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ $t('Rename') }}
          </button>
        </div>
      </div>
    </div>
  </Modal>

  <!-- File Info Modal -->
  <Modal
  :show="showFileInfoModal"
  @close="showFileInfoModal = false"
  :title="$t('File Information')"
  :widthClass="'max-w-4xl'"
  >
    <div class="p-6">
      <div v-if="selectedFileForInfo" class="space-y-4">
        <div class="flex justify-between items-center border-b pb-2">
          <h3 class="text-lg font-medium text-dark dark:text-white">{{ getFileName(selectedFileForInfo.key) }}</h3>
          <span class="px-2 py-1 rounded-md text-xs uppercase"
                :class="selectedFileForInfo.type === 'folder' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'">
            {{ selectedFileForInfo.type }}
          </span>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('Key') }}</h4>
            <p class="text-sm font-mono break-all text-dark dark:text-white">{{ selectedFileForInfo.key }}</p>
          </div>

          <div v-if="selectedFileForInfo.type !== 'folder'">
            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('Size') }}</h4>
            <p class="text-sm font-mono break-all text-dark dark:text-white">{{ formatFileSize(selectedFileForInfo.size) }}</p>
          </div>

          <div v-if="selectedFileForInfo.lastModified">
            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('Last Modified') }}</h4>
            <p class="text-sm font-mono break-all text-dark dark:text-white">{{ formatDate(selectedFileForInfo.lastModified) }}</p>
          </div>

          <div v-if="selectedFileForInfo.contentType">
            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('Content Type') }}</h4>
            <p class="text-sm font-mono break-all text-dark dark:text-white">{{ selectedFileForInfo.contentType }}</p>
          </div>

          <div v-if="selectedFileForInfo.etag">
            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('ETag') }}</h4>
            <p class="text-sm font-mono break-all text-dark dark:text-white">{{ selectedFileForInfo.etag }}</p>
          </div>

          <div v-if="selectedFileForInfo.storageClass">
            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('Storage Class') }}</h4>
            <p class="text-sm font-mono break-all text-dark dark:text-white">{{ selectedFileForInfo.storageClass }}</p>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script setup>
import {ref, computed, onMounted} from "vue";
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Modal from "@/Shared/Modal.vue"
import FileUploader from "@/Shared/FileUploader.vue"
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import { useI18n } from "vue-i18n";
import axios from 'axios';
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Tooltip from "@/Shared/Tooltip.vue";
const { t } = useI18n();

// Props
const props = defineProps({
  storageProvider: Object
});

// S3 File Manager state
const currentBucket = ref(props.storageProvider);
const currentPath = ref('/');
const s3Files = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const originalFiles = ref([]);
const isRefreshing = ref(false);

// File upload state
const showUploadModal = ref(false);
const fileInput = ref(null);
const selectedFile = ref(null);
const uploading = ref(false);
const uploadProgress = ref(0);


// Create a folder state
const showCreateFolderModal = ref(false);
const newFolderName = ref('');

// Rename state
const showRenameModal = ref(false);
const fileToRename = ref(null);
const newName = ref('');

// File info state
const showFileInfoModal = ref(false);
const selectedFileForInfo = ref(null);
const openFileActions = ref(null);

// Computed properties
const pathParts = computed(() => {
  if (currentPath.value === '/') return [];
  return currentPath.value.split('/').filter(Boolean);
});

const areAllFilesSelected = computed(() => {
  return s3Files.value.length > 0 && s3Files.value.every(file => file.selected);
});

const toggleSelectAll = () => {
  const newValue = !areAllFilesSelected.value;
  s3Files.value.forEach(file => {
    file.selected = newValue;
  });
};

// Navigation
const goBack = () => {
  Inertia.visit(route('addons.s3-storage-provider'));
};

// S3 File Manager functions
const loadFiles = async () => {
  if (!props.storageProvider) return;

  loading.value = true;
  try {
    const response = await axios.get(route('api.addons.storage-provider.list-files', props.storageProvider.id), {
      params: {
        prefix: currentPath.value === '/' ? '' : currentPath.value
      }
    });

    if (response.data.success) {
      s3Files.value = response.data.files.map(file => ({
        ...file,
        selected: false
      }));
      originalFiles.value = [...s3Files.value];
    } else {
      useFlash().error(response.data.message || 'Failed to load files');
    }
  } catch (error) {
    console.error('Error loading files:', error);
    useFlash().error('Error loading files');
  } finally {
    loading.value = false;
    isRefreshing.value = false;
  }
}

const refreshFiles = () => {
  isRefreshing.value = true;
  loadFiles();
}

const navigateToRoot = () => {
  currentPath.value = '/';
  loadFiles();
}

const navigateToPath = (index) => {
  const parts = currentPath.value.split('/').filter(Boolean);
  currentPath.value = '/' + parts.slice(0, index + 1).join('/') + '/';
  loadFiles();
}

const navigateTo = (file) => {
  if (file.type === 'folder') {
    currentPath.value = file.key;
    loadFiles();
  } else {
    // For files, show file info or preview
    showFileInfo(file);
  }
}

const getFileName = (path) => {
  if (!path) return '';
  const parts = path.split('/');
  return parts[parts.length - 1] || parts[parts.length - 2] || '';
}

const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let i = 0;

  while (bytes >= 1024 && i < units.length - 1) {
    bytes /= 1024;
    i++;
  }

  return `${bytes.toFixed(2)} ${units[i]}`;
}

const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString();
}

// Search functionality
const searchFiles = () => {
  if (!searchQuery.value) {
    s3Files.value = [...originalFiles.value];
    return;
  }

  const query = searchQuery.value.toLowerCase();
  s3Files.value = originalFiles.value.filter(file =>
    getFileName(file.key).toLowerCase().includes(query)
  );
}

const clearSearch = () => {
  searchQuery.value = '';
  s3Files.value = [...originalFiles.value];
}

// File upload methods
const handleFileChange = (event) => {
  selectedFile.value = event.target.files[0];
}

const uploadFile = async () => {
  if (!selectedFile.value || !props.storageProvider) return;

  uploading.value = true;
  uploadProgress.value = 0;

  const formData = new FormData();
  formData.append('file', selectedFile.value);
  formData.append('path', currentPath.value === '/' ? '' : currentPath.value);

  try {
    const response = await axios.post(
      route('api.addons.storage-provider.upload-file', props.storageProvider.id),
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        }
      }
    );

    if (response.data.success) {
      showUploadModal.value = false;
      selectedFile.value = null;
      if (fileInput.value) fileInput.value.value = '';
      loadFiles();
      useFlash().success('File uploaded successfully');
    } else {
      useFlash().error(response.data.message || 'Failed to upload file');
    }
  } catch (error) {
    console.error('Error uploading file:', error);
    useFlash().error('Error uploading file');
  } finally {
    uploading.value = false;
  }
}

// Folder operations
const createFolder = async () => {
  if (!newFolderName.value || !props.storageProvider) return;

  try {
    const folderKey = `${currentPath.value === '/' ? '' : currentPath.value}${currentPath.value.endsWith('/') ? '' : '/'}${newFolderName.value}/`;

    const response = await axios.post(route('api.addons.storage-provider.create-folder'), {
      bucket_id: props.storageProvider.id,
      key: folderKey
    });

    if (response.data.success) {
      showCreateFolderModal.value = false;
      newFolderName.value = '';
      loadFiles();
      useFlash().success('Folder created successfully');
    } else {
      useFlash().error(response.data.message || 'Failed to create folder');
    }
  } catch (error) {
    console.error('Error creating folder:', error);
    useFlash().error('Error creating folder');
  }
}

// File operations
const downloadFile = async (file) => {
  if (!file || !props.storageProvider) return;

  try {
    const response = await axios.get(route('api.addons.storage-provider.download-file', props.storageProvider.id), {
      params: {
        key: file.key
      },
    });
    const url = response.data.url;
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', getFileName(file.key));
    document.body.appendChild(link);
    link.click();
    link.remove();
    openFileActions.value = null;
  } catch (error) {
    console.error('Error downloading file:', error);
    useFlash().error('Error downloading file');
  }
}

const handleFileActions = (file) => {
  // Close any other open dropdowns first
  if (openFileActions.value && openFileActions.value !== file) {
    openFileActions.value = null;
  }

  // Toggle the dropdown for the current file
  openFileActions.value = openFileActions.value === file ? null : file;
}

const openRenameModal = (file) => {
  fileToRename.value = file;
  newName.value = getFileName(file.key);
  showRenameModal.value = true;
  openFileActions.value = null;
}

const closeRenameModal = () => {
  showRenameModal.value = false;
  fileToRename.value = null;
  newName.value = '';
}

const renameFile = async () => {
  if (!fileToRename.value || !newName.value || !props.storageProvider) return;

  try {
    const oldKey = fileToRename.value.key;
    const isFolder = fileToRename.value.type === 'folder';

    // For folders, we need to maintain the trailing slash
    const oldKeyWithoutName = oldKey.substring(0, oldKey.lastIndexOf('/') + 1);
    const newKey = oldKeyWithoutName + newName.value + (isFolder ? '/' : '');

    const response = await axios.post(route('api.addons.storage-provider.rename-file', props.storageProvider.id), {
      old_key: oldKey,
      new_key: newKey,
      is_folder: isFolder
    });

    if (response.data.success) {
      closeRenameModal();
      loadFiles();
      useFlash().success('Renamed successfully');
    } else {
      useFlash().error(response.data.message || 'Failed to rename');
    }
  } catch (error) {
    console.error('Error renaming:', error);
    useFlash().error('Error renaming');
  }
}

const deleteFile = async (file) => {
  if (!file || !props.storageProvider) return;

  openFileActions.value = null;

  useFlash().deleteConfirmation({
    title: t(`Are you sure you want to delete this ${file.type === 'folder' ? 'folder' : 'file'}?`),
    text: t('This action cannot be undone.'),
  }, async () => {
    try {
      const response = await axios.delete(route('api.addons.storage-provider.delete-file',props.storageProvider.id), {
        data: {
          key: file.key,
          is_folder: file.type === 'folder'
        }
      });

      if (response.data.success) {
        loadFiles();
        useFlash().success(`${file.type === 'folder' ? 'Folder' : 'File'} deleted successfully`);
      } else {
        useFlash().error(response.data.message || 'Failed to delete');
      }
    } catch (error) {
      console.error('Error deleting:', error);
      useFlash().error('Error deleting');
    }
  });
}

// File info methods
const showFileInfo = (file) => {
  selectedFileForInfo.value = file;
  showFileInfoModal.value = true;
  openFileActions.value = null;
}

// Load files when component is mounted
onMounted(() => {
  loadFiles();
});

// Add these methods in the script section after the existing methods
const handleUploadComplete = (response) => {
  showUploadModal.value = false;
  loadFiles();
  useFlash().success('File uploaded successfully');
}

const handleUploadError = (error) => {
  console.error('Error uploading file:', error);
  useFlash().error('Error uploading file');
}
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';

.multiselect.is-disabled {
  @apply bg-white dark:bg-mode-light;
}
</style>
