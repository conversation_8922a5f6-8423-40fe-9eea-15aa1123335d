<template>
  <single-profile active="Email Provider">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
      <div class="flex justify-between items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Email Provider') }}</h2>
      </div>
      <div class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full">
        <div class="flex justify-between items-center gap-15px w-full wide-mobile:flex-wrap">
          <div class="flex flex-col gap-10px">
            <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('List of Email Providers') }}</h3>
            <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
              {{ $t('Find all the email providers associated with your account here.') }}
            </p>
          </div>
          <button @click.prevent="addMailboxModal = true"
                  class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                            text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent
                            hover:bg-primary-light hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition
                            ease-in-out duration-300 focus:outline-none leading-tight flex-nowrap whitespace-nowrap">
            <span>{{ $t('Add New Mailbox') }}</span>
          </button>
        </div>
        <div class="flex flex-col gap-30px">
          <div class="rounded-md flex flex-col">
            <div class="overflow-x-auto w-full overflow-y-hidden">
              <table class="w-full">
                <thead class="bg-primary-light dark:bg-dark">
                <tr class="divide-x divide-light dark:divide-dark">
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Email Account') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Password') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Status') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Actions') }}
                  </th>
                </tr>
                </thead>
                <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                <tr v-for="emailAccount in emailAccounts?.data"
                    :key="emailAccount.id"
                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                >
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                    {{ emailAccount.email }}
                  </td>
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                    {{ emailAccount.masked_pwd }}
                  </td>
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white"
                  >
                    <span
                        :class="{
                          'text-warning': emailAccount.status === 'Pending Verification',
                          'text-success-full': emailAccount.status === 'Active',
                          'text-danger': emailAccount.status === 'Inactive' || emailAccount.status === 'Failed'
                      }"
                    >
                      {{ emailAccount.status }}

                      <tooltip
                          v-if="emailAccount.status === 'Pending Verification'"
                          title="See details"
                          class="hover:cursor-pointer"
                          @click.prevent="mailboxDnsRecordForm.emailAccount = emailAccount; showDnsRecordModal = true"
                      >
                      <i class="xcloud xc-info text-warning"></i>
                    </tooltip>
                    </span>


                  </td>
                  <td class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                      <span class="flex gap-30px">
                        <button @click.prevent="upgradeMailbox(emailAccount)"
                                class="relative group cursor-pointer mt-1.5">
                            <span class="text-sm flex text-secondary-light group-hover:text-white">
                              <i class="xcloud xc-edit"></i>
                            </span>
                        </button>
                        <Tooltip :title="'Cancel Mailbox'"
                                 color="warning"
                        >
                          <button @click.prevent="cancelMailbox(emailAccount)"
                                  class="relative group cursor-pointer mt-1.5"
                          >
                            <span class="text-sm flex text-secondary-light group-hover:text-white">
                              <i class="xcloud xc-delete"></i>
                            </span>
                          </button>
                        </Tooltip>
                        <Tooltip :title="'Delete Mailbox'"
                                 color="danger"
                        >
                          <button @click.prevent="deleteMailbox(emailAccount)"
                                  class="relative group cursor-pointer mt-1.5"
                          >
                            <span class="text-sm flex text-secondary-light group-hover:text-white">
                              <i class="xcloud xc-delete"></i>
                            </span>
                          </button>
                        </Tooltip>
                      </span>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <!-- pagination -->
          <pagination :links="emailAccounts?.links"/>
        </div>
      </div>
    </div>
  </single-profile>

  <!-- Add Provider Modal -->
  <Modal
      @footerClick="addMailbox"
      :footer-button="true"
      :footer-button-title="$t('Add Mailbox')"
      :closeable="true"
      :loading="mailboxForm.processing"
      @close="addMailboxModal = false; mailboxForm.reset()"
      :show="addMailboxModal"
      :widthClass="'max-w-850px'"
      :title="$t('Add Mailbox')"
  >

    <div class="flex flex-col gap-30px">
      <div v-for="(mailbox, index) in mailboxForm.mailboxes" :key="index" class="mailbox-container">
        <div>
          <p class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mb-3">
            Mailbox {{ index + 1 }}
          </p>
        </div>
        <div class="grid tablet:grid-cols-1 gap-30px">
          <!-- Email -->
          <TextInput
              :label="$t('Email')"
              v-model="mailbox.email"
              :error="mailboxForm.errors[`mailboxes.${index}.email`]"
              type="email"
              placeholder="<EMAIL>"
          >
          </TextInput>
          <!-- Password -->
          <PasswordInput
              :label="$t('Password')"
              v-model="mailbox.password"
              :error="mailboxForm.errors[`mailboxes.${index}.password`]"
          >
          </PasswordInput>

          <!-- Mailbox plan -->
          <select-input
              v-model="mailboxForm.mailboxes[index].selected_plan"
              :error="mailboxForm.errors[`mailboxes.${index}.selected_plan`]"
              id="mailbox_plan"
              :label="$t('Choose a plan')"
          >
            <option v-for="(mailboxPlan, key) in mailboxPlans" :key="key" :value="key">
              {{ mailboxPlan }}
            </option>
          </select-input>

          <!-- Remove mailbox -->
          <div
            v-if="mailboxForm.mailboxes.length > 1 && index > 0"
            class="flex justify-end -mt-6"
            @click.prevent="mailboxForm.mailboxes.splice(index, 1)"
          >
            <p class="text-sm text-secondary-full font-normal mt-15px underline text-primary-light cursor-pointer">
              Remove Mailbox
            </p>
          </div>
        </div>
      </div>

      <!-- Add more mailbox -->
      <div>
          <button type="button"
                @click.prevent="addMoreMailbox"
                class="inline-flex items-center justify-center py-2 px-4 rounded-md shadow-none text-sm text-center
                        text-primary-light font-normal bg-primary-light/10 focus:outline-none hover:bg-primary-light hover:text-white ease-in-out
                        transition duration-200 hover:shadow-lg hover:shadow-primary-light/30"
          >
            <span class="mr-2 text-xs inline-flex"><i class="xcloud xc-add"></i></span>
            <span class="drop-shadow-button">{{ $t('Add More Mailbox') }}</span>
        </button>
      </div>
    </div>
  </Modal>

  <!-- DNS Record Modal -->
  <Modal
      :footer-button="false"
      :closeable="true"
      :loading="mailboxForm.processing"
      @close="showDnsRecordModal = false; mailboxForm.reset()"
      :show="showDnsRecordModal"
      :widthClass="'max-w-850px'"
      :title="$t('DNS Records')"
  >
    <div class="p-30px h-full bg-white dark:bg-mode-base rounded-md flex flex-col items-start gap-30px tablet:gap-10px">
      <MailboxDns
          :email-account="mailboxDnsRecordForm.emailAccount"
      ></MailboxDns>
    </div>
  </Modal>

</template>
<script setup>
import {ref} from "vue";
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Modal from "@/Shared/Modal.vue"
import {useForm, usePage} from '@inertiajs/inertia-vue3';
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import Button from "@/Jetstream/Button.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import PasswordInput from "@/Shared/PasswordInput.vue";
import TextInput from "@/Shared/TextInput.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import MailboxDns from "@/Pages/Addons/Mailbox/Component/MailboxDns.vue";

const props = defineProps({
  emailAccounts: Object,
  mailboxPlans: Object
});

const mailboxForm = useForm({
  mailboxes: [
    {
      email: '',
      password: '',
      selected_plan: 'mailbox_8gb_plan',
      mailbox_settings: {
        mailbox_size: 1000
      }
    }
  ]
});

const mailboxDnsRecordForm = useForm({
  emailAccount: null,
})

const addMoreMailbox = () => {
  mailboxForm.mailboxes.push({
    email: '',
    password: '',
    selected_plan: 'mailbox_8gb_plan',
    mailbox_settings: {
      mailbox_size: 1000
    }
  });
}
const addMailbox = () => {
  mailboxForm.post(route('api.addons.mailbox.create'), {
    onSuccess: () => {
      addMailboxModal.value = false;
    }
  });
}

const addMailboxModal = ref(false);
const showDnsRecordModal = ref(false);
const upgradeMailbox = async (mailbox) => {

}

const cancelMailbox = async (mailbox) => {
  const { flash } = useFlash();
  Inertia.post(route('mailbox.cancel', mailbox.id), {}, {
    onSuccess: () => {
      flash('success', t('Mailbox cancelled successfully'));
    },
    onError: () => {
      flash('error', t('Failed to cancel mailbox'));
    }
  });
}

const deleteMailbox = async (mailbox) => {
  const { flash } = useFlash();
  Inertia.post(route('mailbox.delete', mailbox.id), {}, {
    onSuccess: () => {
      flash('success', t('Mailbox deleted successfully'));
    },
    onError: () => {
      flash('error', t('Failed to delete mailbox'));
    }
  });
}

</script>


<style>
@import '../../../../../node_modules/@vueform/multiselect/themes/tailwind.css';

.multiselect.is-disabled {
  @apply bg-white dark:bg-mode-light;
}
</style>
