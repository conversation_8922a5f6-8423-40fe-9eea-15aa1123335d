<template>
  <div class="">
    <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-10px">
      {{ $t('DNS Setup') }}
    </h3>
    <suggestion
        :lightMode="true"
        :message="'Add the following DNS records to your domain provider.'"
    />
    <div class="w-full">
      <p class="text-secondary-full block text-base font-normal">
        {{ $t('In your domain settings, you need to add the following records for configuring your site email.') }}
      </p>

      <!-- A Record -->
      <div class="flex mt-20px mobile:mt-15px gap-0.5 flex-wrap">
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Type') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ $t('A') }}
          </p>
        </div>
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Name') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ dnsRecords.dnsOwnershipRecord.name }}
            <span v-if="dnsOwnershipValid !== ''">
                <tooltip :title="dnsOwnershipValid
                                ? 'Your DNS Ownership record is added properly.'
                                : 'Your DNS Ownership record is not added properly.'">
                  <i class="xcloud text-sm"
                     :class="dnsOwnershipValid ? 'xc-tick-o text-success-full' : 'xc-close-o text-danger'"></i>
                </tooltip>
            </span>
          </p>

          <CopyButton
              :content="dnsRecords.dnsOwnershipRecord.name"
              class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
          />
        </div>
        <div class="max-w-md">
          <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
            <h5 class="text-sm text-secondary-full">{{ $t('Value') }}</h5>
            <p class="text-sm mt-1 font-medium text-dark dark:text-white break-all" v-text="dnsRecords.dnsOwnershipRecord.value"></p>
            <CopyButton
                :content="dnsRecords.dnsOwnershipRecord.value"
                class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
            />
          </div>
        </div>
      </div>

      <!-- MX 01 Record -->
      <div class="flex mt-20px mobile:mt-15px gap-0.5 flex-wrap">
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Type') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ $t('MX') }}
          </p>
        </div>
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Name') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ dnsRecords.mx1Record.name }}
            <span v-if="cnameRecordValid !== ''">
                <tooltip :title="cnameRecordValid
                                ? 'Your CNAME record is added properly.'
                                : 'Your CNAME record is not added properly.'">
                  <i class="xcloud text-sm"
                     :class="cnameRecordValid ? 'xc-tick-o text-success-full' : 'xc-close-o text-danger'"></i>
                </tooltip>
            </span>
          </p>

          <CopyButton
              :content="dnsRecords.mx1Record.name"
              class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
          />
        </div>
        <div class="max-w-md">
          <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
            <h5 class="text-sm text-secondary-full">{{ $t('Mail Server') }}</h5>
            <p class="text-sm mt-1 font-medium text-dark dark:text-white break-all"
               v-text="dnsRecords.mx1Record.value"
            ></p>
            <CopyButton
                :content="dnsRecords.mx1Record.value"
                class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
            />
          </div>
        </div>

        <div class="max-w-md">
          <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
            <h5 class="text-sm text-secondary-full">{{ $t('Priority') }}</h5>
            <p class="text-sm mt-1 font-medium text-dark dark:text-white break-all"
               v-text="dnsRecords.mx1Record.priority"
            ></p>
            <CopyButton
                :content="dnsRecords.mx1Record.priority"
                class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light pb-10"
            />
          </div>
        </div>
      </div>

      <!-- MX 02 Record -->
      <div class="flex mt-20px mobile:mt-15px gap-0.5 flex-wrap">
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Type') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ $t('MX') }}
          </p>
        </div>
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Name') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ dnsRecords.mx2Record.name }}
            <span v-if="cnameRecordValid !== ''">
                <tooltip :title="cnameRecordValid
                                ? 'Your CNAME record is added properly.'
                                : 'Your CNAME record is not added properly.'">
                  <i class="xcloud text-sm"
                     :class="cnameRecordValid ? 'xc-tick-o text-success-full' : 'xc-close-o text-danger'"></i>
                </tooltip>
            </span>
          </p>

          <CopyButton
              :content="dnsRecords.mx2Record.name"
              class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
          />
        </div>
        <div class="max-w-md">
          <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
            <h5 class="text-sm text-secondary-full">{{ $t('Mail Server') }}</h5>
            <p class="text-sm mt-1 font-medium text-dark dark:text-white break-all"
               v-text="dnsRecords.mx2Record.value"
            ></p>
            <CopyButton
                :content="dnsRecords.mx2Record.value"
                class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
            />
          </div>
        </div>

        <div class="max-w-md">
          <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
            <h5 class="text-sm text-secondary-full">{{ $t('Priority') }}</h5>
            <p class="text-sm mt-1 font-medium text-dark dark:text-white break-all"
               v-text="dnsRecords.mx2Record.priority"
            ></p>
            <CopyButton
                :content="dnsRecords.mx2Record.priority"
                class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light pb-10"
            />
          </div>
        </div>
      </div>

      <!-- SPF Record -->
      <div class="flex mt-20px mobile:mt-15px gap-0.5 flex-wrap">
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Record(SPF)') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ $t('TXT') }}
          </p>
        </div>
        <div class="grow relative max-w-[300px] rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Name') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            <p class="whitespace-pre-wrap">
              {{ dnsRecords.spfRecord.name }}
            </p>
            <span v-if="spfRecordValid !== ''">
                <tooltip :title="spfRecordValid
                                ? 'Your SPF record is added properly.'
                                : 'Your SPF record is not added properly.'">
                  <i class="xcloud text-sm"
                     :class="spfRecordValid ? 'xc-tick-o text-success-full' : 'xc-close-o text-danger'"></i>
                </tooltip>
            </span>
          </p>
        </div>
        <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
          <h5 class="text-sm text-secondary-full">{{ $t('Value') }}</h5>
          <p class="text-sm mt-1 font-medium text-dark dark:text-white">
            {{ dnsRecords.spfRecord.value }}
          </p>

          <CopyButton
              :content="dnsRecords.spfRecord.value"
              class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
          />
        </div>
      </div>

      <div class="flex">
        <button
            @click.prevent="verifyDnsRecord"
            type="button"
            class="inline-flex items-center border-1 border-primary-light justify-center min-h-40px p-2 px-20px pl-15px mt-30px
                    mobile:mt-15px rounded-md shadow-none text-base text-center text-white font-normal bg-primary-light focus:outline-none
                    hover:bg-primary-dark hover:border-primary-dark hover:text-white ease-in-out transition duration-200 hover:shadow-lg
                    hover:shadow-primary-dark/30"
            :class="{ 'opacity-50 cursor-not-allowed': disabled }"
            :disabled="disabled"
        >
        <span class="inline-flex justify-center items-center text-lg mr-2.5">
            <i class="xcloud xc-verify_dns" :class="{ 'animate-spin': dnsVerifyForm.processing }"></i>
        </span>
          <span class="drop-shadow-button">{{ $t('Verify Record') }}</span>
        </button>
      </div>

    </div>
  </div>
</template>

<script setup>
import { useFlash } from "@/Composables/useFlash";
import CopyButton from "@/Shared/CopyButton.vue";
import {computed, reactive, ref} from "vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {asset} from "laravel-vapor";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import {useForm} from "@inertiajs/inertia-vue3";

let props = defineProps({
  emailAccount: Object
});

let dnsRecords = reactive({
  dnsOwnershipRecord: {
    name: props.emailAccount.mailbox_domain.domain_code?.toLowerCase() + '.' + props.emailAccount.mailbox_domain.domain,
    value: '*************',
  },
  mx1Record:{
    name: 'MX 1',
    value: 'mx01.' + props.emailAccount.mailbox_domain.domain,
    priority: '10'
  },
  mx2Record:{
    name: 'MX 2',
    value: 'mx02.' + props.emailAccount.mailbox_domain.domain,
    priority: '20'
  },
  spfRecord:{
    name: props.emailAccount.mailbox_domain.domain,
    value: 'v=spf1 include:spf.' + props.emailAccount.mailbox_domain.domain + ' mx a -all',
  },
});

let dnsVerifyForm = useForm( {
  dnsRecords: dnsRecords,
});

let dnsOwnershipValid = ref('');
let spfRecordValid = ref('');
let cnameRecordValid = ref('');
let dmarcRecordValid = ref('');

const disabled = computed(() => {
  return (
      dnsVerifyForm.processing
  );
});

const getDomainName = (domainName) => {
  let name = domainName.length > 0 ? domainName : 'domain';
  return "<b>"+name+"</b>";
}

let verifyDnsRecord = () => {
  dnsVerifyForm.processing = true;
  axios
      .post(route("api.addons.mailbox.verify-dns-records", [props.emailAccount.mailbox_domain.id] ), {
        dnsRecords: dnsRecords,
      })
      .then((response) => {
        dnsOwnershipValid.value = response.data.dns_ownership_verified;

        if (response?.data?.success) {
          useFlash().success(
              response?.data?.message
          );
          dnsVerifyForm.processing = false;
        } else {
          useFlash().error(
              response?.data?.message
          );
          dnsVerifyForm.processing = false;
        }
      })
      .catch((error) => {
        useFlash().error(
            error?.response?.data?.message
                ? error.response.data.message
                : error
        );
      })
      .finally(() => {
        dnsVerifyForm.processing = false;
      });
};
</script>
