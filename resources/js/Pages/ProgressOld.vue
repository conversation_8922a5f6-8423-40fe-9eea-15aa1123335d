<template>
    <Head :title="title"/>

    <div class="xc-container">
        <div class="flex-1 flex items-center mr-0">
            <div class="max-w-850px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0 flex flex-col">
                <h2
                    class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark dark:text-white mobile:text-28px">
                    {{ title }}
                </h2>

                <div class="my-30px">
                    <ProgressBar :percentage="progress.percentage" maxHeight="150px"/>
                </div>

                <div
                    class="relative w-full p-50px pr-100px wide-mobile:px-30px wide-mobile:py-50px mobile:px-25px mobile:py-40px mx-auto bg-white dark:bg-mode-light rounded-md">
                    <h3
                        class="absolute top-30px right-30px wide-mobile:top-20px wide-mobile:right-20px mobile:top-15px mobile:right-15px font-bold text-success-full text-40px wide-mobile:text-4xl mobile:text-3xl leading-none tracking-tight mobile:leading-none">
                        {{ progress.percentage }}<span class="text-xl">%</span>
                    </h3>
                    <div class="flex flex-col">
                        <template v-for="(progressText, step) in list">

                            <div v-if="progress.status >= parseInt(step)">
                                <!-- fininshed -->
                                <p class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal tracking-wider mb-4 after:content-['\e92b'] after:translate-y-0.5 after:font-xc after:ml-1 after:text-xs after:inline-flex after:items-center after:justify-center after:text-success-full after:transition after:duration-200"
                                   v-if="progress.status > step">
                                    [{{ step }}/{{ statusMax }}] {{ progressText }}
                                </p>

                                <!-- runnig -->
                                <template v-else>

                                    <!-- last item has exception -->
                                    <template v-if="progress.exception">
                                        <p
                                            class="text-sm text-danger font-normal tracking-wider mb-4 after:content-['\e92a'] after:translate-y-0.5 after:font-xc after:ml-1 after:text-xs after:inline-flex after:items-center after:justify-center after:text-danger after:transition after:duration-200">
                                            [{{ step }}/{{ statusMax }}] {{ progressText }}: {{ progress.exception }}
                                        </p>
                                    </template>

                                    <!-- last loading with success -->
                                    <template v-else-if="parseInt(step) === statusMax">
                                        <p class="text-sm text-success-full font-normal tracking-wider">
                                            [{{ step }}/{{ statusMax }}] {{ progressText }}{{ dots }}
                                        </p>
                                    </template>

                                    <!-- last item is loading -->
                                    <template v-else>
                                        <p
                                            class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal tracking-wider mb-4">
                                            [{{ step }}/{{ statusMax }}] {{ progressText }}{{ dots }}
                                        </p>
                                    </template>
                                </template>

                            </div>

                            <p v-else
                               class="text-sm text-secondary-full/30 dark:text-mode-secondary-dark/30 font-normal tracking-wider mb-4">
                                [{{ step }}/{{ statusMax }}] {{ progressText }}
                            </p>

                        </template>

                        <!-- experimental, might change the data structure later -->
                        <template v-for="(status, step, index) in progressData" v-if="false">
                            <p
                                v-if="status === 'done'"
                                class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal tracking-wider mb-4 after:content-['\e92b'] after:translate-y-0.5 after:font-xc after:ml-1 after:text-xs after:inline-flex after:items-center after:justify-center after:text-success-full after:transition after:duration-200">
                                [{{ index + 1 }}/{{ Object.entries(progressData).length }}] {{ progressMessages[step] }}
                            </p>

                            <p
                                v-else-if="status === 'error'"
                                class="text-sm text-danger font-normal tracking-wider mb-4 after:content-['\e92a'] after:translate-y-0.5 after:font-xc after:ml-1 after:text-xs after:inline-flex after:items-center after:justify-center after:text-danger after:transition after:duration-200">
                                [{{ index + 1 }}/{{ Object.entries(progressData).length }}] {{ progressMessages[step] }}
                                {{ progress.exception }}
                            </p>

                            <p v-else-if="status === 'skip'"
                               class="text-sm text-secondary-full/30 dark:text-mode-secondary-dark/50 font-normal tracking-wider mb-4">
                                [{{ index + 1 }}/{{ Object.entries(progressData).length }}] {{ progressMessages[step] }} [Skipped]
                            </p>

                            <p v-else-if="status === 'running'"
                               class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal tracking-wider mb-4">
                                [{{ index + 1 }}/{{ Object.entries(progressData).length }}] {{ progressMessages[step] }} {{ dots }}
                            </p>

                            <p v-else
                               class="text-sm text-secondary-full/30 dark:text-mode-secondary-dark/30 font-normal tracking-wider mb-4">
                                [{{ index + 1 }}/{{ Object.entries(progressData).length }}] {{ progressMessages[step] }}
                            </p>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, onUnmounted, reactive, ref} from 'vue'
import ProgressBar from "@/Shared/Icons/ProgressBar.vue";
import {Inertia} from "@inertiajs/inertia";

let dots = ref('...');

let interval = null;

let props = defineProps({
    title: String,
    list: Object,
    status: Number,
    percentage: Number,
    statusMax: Number,
    exception: String,
    socketChannel: String,
    socketListenFor: String,

    progressData: Object,
    progressMessages: Object,
});

let progress = reactive({
    status: props.status,
    percentage: props.percentage,
    exception: props.exception,
});

function changeHostToCurrent(url) {
    let newUrl = new URL(url);
    newUrl.host = location.host;
    newUrl.protocol = location.protocol;
    return newUrl.toString();
}

onMounted(() => {
    interval = setInterval(() => {
        if (dots.value === '.') {
            dots.value = '..';
        } else if (dots.value === '..') {
            dots.value = '...';
        } else if (dots.value === '...') {
            dots.value = '....';
        } else if (dots.value === '....') {
            dots.value = '';
        } else if (dots.value === '') {
            dots.value = '.';
        }
    }, 500);

    if (window.Echo && props.socketChannel) {
        console.log('joining', props.socketChannel, props.socketListenFor);

        window.Echo.private(props.socketChannel)
            .listen(props.socketListenFor, (e) => {
                console.log(props.socketListenFor, e);

                if (progress.status !== e.status) {
                    dots.value = '.';
                }

                if (e.status) {
                    progress.status = e.status;
                }

                if (e.percentage) {
                    progress.percentage = e.percentage;
                }

                progress.exception = e.exception;

                if (e.redirect) {
                    Inertia.visit(changeHostToCurrent(e.redirect));
                }
            });
    }
});

onUnmounted(() => {
    clearInterval(interval);

    if (window.Echo && props.socketChannel) {
        console.log('leaving', props.socketChannel, props.socketListenFor);
        window.Echo.private(props.socketChannel).stopListening(props.socketListenFor)
    }
})


</script>
