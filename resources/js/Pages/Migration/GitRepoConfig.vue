<template>
    <migration-layout
        :steps="steps"
        :form="form"
        :site_migration="site_migration"
        :previous_step="previous_step"
        :current_step="current_step"
        :next_step="next_step"
        :suggestion_message="suggestion_message"
        :post_route="route(post_route,{server: server.id,siteMigration: site_migration.id})"
        :previous_route="previous_route"
    >

        <form>
            <div class="grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px">
                <text-input
                    v-model="form.git_repository"
                    :error="form.errors.git_repository"
                    id="git_repository"
                    placeholder="****************:user/repository.git"
                    :label="$t('Git Repository')"
                />
                <text-input
                    v-model="form.git_branch"
                    :error="form.errors.git_branch"
                    id="git_branch"
                    :placeholder="$t('master')"
                    :label="$t('Git Branch')"
                />
            </div>
            <div class="grid grid-cols-1 wide-mobile:grid-cols-1 gap-20px mt-5">
                <div class="flex items-center">
                    <Switch
                        @click.prevent="form.enable_push_deploy = !form.enable_push_deploy"
                        :checked="form.enable_push_deploy"
                        :hiddenSlot="true"
                    >
                        <span class="text-dark dark:text-white font-normal leading-tight ml-3">{{ $t('Enable push to deploy') }}</span>
                    </Switch>
                </div>
                <div v-if="form.enable_push_deploy" class="relative block mt-5 border-1 border-secondary-light dark:border-mode-focus-light rounded-md p-20px mobile:px-10px mobile:py-15px">
                    <text-input
                        v-model="form.git_deployment_url"
                        :error="form.errors.git_deployment_url"
                        id="deployment_url"
                        :label="$t('Deployment URL')"
                        :note="$t('Add this URL as a webhook in your Git repository settings to enable automated deployments')"
                        readonly
                    />
                    <CopyButton
                        :content="form.git_deployment_url"
                        align="top"
                        color="primary"
                    />
                </div>
            </div>

            <div class="grid grid-cols-1 wide-mobile:grid-cols-1 gap-20px my-5">
                <textarea-input
                    v-model="form.deploy_script"
                    :error="form.errors.deploy_script"
                    id="deploy_script"
                    :placeholder="$t('npm run deploy')"
                    :label="$t('Deploy Script')"
                    note="Here you can add any script that needs to be executed after the site is deployed. For example, you can use it to install dependencies or run database migrations."
                />
                <div class="flex items-center">
                    <Switch
                        @click.prevent="form.run_after_deployment = !form.run_after_deployment"
                        :checked="form.run_after_deployment"
                        :hiddenSlot="true"
                    >
                        <span class="text-dark dark:text-white font-normal leading-tight ml-3">{{ $t('Run this script after every site deployment') }}</span>
                    </Switch>
                </div>
            </div>
            <suggestion :message="gitRepoSuggestion"/>

            <div
                class="relative block mt-5 border-1 border-secondary-light dark:border-mode-focus-light rounded-md p-50px mobile:px-20px mobile:py-30px">
                <div
                    class="dark:text-white text-black break-words text-sm leading-6 select-all cursor-pointer"
                    v-text="ssh_pub_key"></div>

                <CopyButton :content="ssh_pub_key"/>
            </div>

        </form>
    </migration-layout>
</template>

<script setup>

import MigrationLayout from "@/Pages/Migration/Components/MigrationLayout.vue";
import TextInput from '@/Shared/TextInput.vue'
import TextareaInput from '@/Shared/TextareaInput.vue'
import {useForm, usePage} from "@inertiajs/inertia-vue3";
import {useHelpStore} from "@/stores/HelpStore";
import Switch from "@/Shared/Switch.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import {onMounted} from "vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();


let helper = useHelpStore();

let props = defineProps({
    'form': Object,
    'steps': Object,
    'previous_step': String,
    'current_step': String,
    'next_step': String,
    'site_migration': Object,
    'suggestion_message': String,
    'server': Object,
    'post_route': String,
    'previous_route': String,
    'git_deployment_url': String,
    'ssh_pub_key': String,
    'git_info': {
        type: Object,
        default: () => {
            return {
                git_repository: '',
                git_branch: '',
                deploy_script: '',
                enable_push_deploy: false,
                run_after_deployment: false,
            }
        }
    }
})
let form = useForm({
    git_repository: props.git_info ?  props.git_info.git_repository : '',
    git_branch: props.git_info ? props.git_info.git_branch : '',
    git_deployment_url:props.git_deployment_url,
    deploy_script: props.git_info ? props.git_info.deploy_script : '',
    enable_push_deploy: props.git_info ? props.git_info.enable_push_deploy : false,
    run_after_deployment: props.git_info ? props.git_info.run_after_deployment : false,
});

let brandName = usePage().props.value?.current_white_label ? usePage().props.value?.current_white_label?.branding?.brand_name : 'xCloud';
let gitRepoSuggestion = t('Add this public key in your Git repository as deploy key. This is necessary to enable')+' '+brandName+' '+t('to clone the repository, ensuring a secure and authorized access for automated processes such as cloning.');
</script>
