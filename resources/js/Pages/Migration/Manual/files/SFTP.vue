<template>


    <h3
        class="text-dark dark:text-white text-2xl wide-mobile:text-xl mb-30px tablet:mb-25px wide-mobile:mb-20px mobile:mb-15px">
        {{ $t('Use These Credentials To Upload Your WordPress Website') }}
    </h3>
    <div
        class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px mb-30px tablet:mb-25px">
        <text-input
            v-model="form.server_address"
            :error="form.errors.server_address"
            id="server_address"
            :placeholder="$t('Server Address')"
            :label="$t('Server Address')"/>
        <text-input
            v-model="form.server_username"
            :error="form.errors.server_username"
            id="server_username"
            :placeholder="$t('Server Username')"
            :label="$t('Server Username')"/>
        <text-input
            v-model="form.server_password"
            :error="form.errors.server_password"
            id="server_password"
            :placeholder="$t('Password')"
            :label="$t('Password')"/>
        <text-input
            v-model="form.server_port"
            :error="form.errors.server_port"
            id="server_port"
            :placeholder="$t('22')"
            :label="$t('Port')"/>

        <text-input
            v-model="form.remote_path"
            :error="form.errors.remote_path"
            id="remote_path"
            placeholder="/home/<USER>/example.com"
            :label="$t('Remote Path')"/>
    </div>
    <button
        class="inline-flex items-center justify-center min-h-60px tablet:min-h-50px border-1 border-primary-light dark:border-dark text-lg px-25px font-medium rounded-10px shadow-none text-white bg-primary-light dark:bg-dark hover:bg-primary-light dark:hover:bg-mode-focus-light hover:text-white hover:shadow-lg hover:shadow-primary-dark/30 dark:shadow-white/10 transition ease-in-out duration-300 focus:outline-none">
        {{ $t('Verify Upload') }}
    </button>
</template>

<script setup>

import TokenVerification from "@/Pages/Migration/Plugin/layouts/TokenVerification.vue";
import AuthenticationToken from "@/Pages/Migration/Plugin/layouts/AuthenticationToken.vue";
import ListItem from "@/Pages/Migration/Plugin/layouts/ListItem.vue";
import StepList from "@/Pages/Migration/Plugin/layouts/StepList.vue";
import TextInput from "@/Shared/TextInput.vue";

let props = defineProps({
    plugin_url: String,
    site_plugin_url: String,
    token: String,
    form: Object
})
</script>
