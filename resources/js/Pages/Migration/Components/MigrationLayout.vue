<template>
    <!DOCTYPE html>
    <div class="xc-container">
        <div class="flex-1 flex flex-col items-center small-laptop:mr-0"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : null">
            <div class="max-w-1050px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <h2
                    class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark dark:text-white
                    mb-50px wide-mobile:text-28px mobile:text-2xl tablet:mb-40px wide-mobile:mb-30px">
                    <slot name="header"></slot>
                </h2>

                <HorizontalTab
                    :tabs="steps"
                    :previous_tab="previous_step"
                    :active_tab="current_step"
                    :next_tab="next_step">
                    <suggestion
                        v-if="suggestion_message"
                        :message="suggestion_message"/>

                    <slot></slot>
                </HorizontalTab>

                <slot name="footer"></slot>

                <WizardProgress :processing="form.processing"
                                :form="form"
                                :back="previous_route ?? steps[previous_step]?.get_route"
                                @next="onNext"
                                :progress-width="progressWidth(steps[current_step].step_no, steps[current_step].total_steps)"
                                :progress-text="'Step '+steps[current_step].step_no+' of '+steps[current_step].total_steps">
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                        <span>{{ $t('Verifying') }}</span>
                    </template>
                    <template v-else>
                        <span>
                            {{ steps[current_step].step_no === steps[current_step].total_steps ? $t('Start') : $t('Next') }}
                        </span>
                    </template>
                </WizardProgress>

            </div>
        </div>
    </div>
</template>

<script setup>
import WizardProgress from '@/Shared/WizardProgress.vue'
import HorizontalTab from "@/Shared/HorizontalTab.vue";
import {useNavigationStore} from "@/stores/NavigationStore";
import {useHelpStore} from "@/stores/HelpStore";
import {useForm} from "@inertiajs/inertia-vue3";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import {computed, watch} from "vue";

let props = defineProps({
    'form': Object,
    'steps': Object,
    'previous_step': String,
    'current_step': String,
    'next_step': String,
    'site_migration': Object,
    'suggestion_message': String,
    'post_route': String,
    'previous_route': String,
    'submit': Function,
    'site_type': {
        type: String,
        default: 'wordpress'
    }
})

const progressWidth = (start, end) => (start * 100) / (end ?? 1);

let defaultSubmit = (form, url) => {
   // console.log(url);
    // remove https? from the domain name before submission
    if (form.domain_name && (form.domain_name.startsWith("http://") || form.domain_name.startsWith("https://"))) {
        form.domain_name = form.domain_name.replace(/^https?:\/\//, "");
    }

    form.post(url, {
        preserveScroll: true
    });
}

let onNext = () => {
    if (props.form.processing) {
        return;
    }

    let postRoute = props.steps[props.current_step]?.post_route ?? props.post_route;

    // Use custom submit method if provided, otherwise use default submit method
    if (typeof props.submit === 'function') {
        props.submit(props.form, postRoute);
    } else {
        defaultSubmit(props.form, postRoute);
    }
};


let navigation = useNavigationStore();
let helper = useHelpStore();

</script>

