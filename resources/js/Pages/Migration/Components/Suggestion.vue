<template>
    <div class="rounded-md flex items-center bg-primary-light/10 px-6 py-4 border-none border-primary-light
                dark:border-mode-base w-ful mb-30px tablet:mb-25px mobile:mb-20px"
         :class="{'dark:bg-mode-light' : lightMode, 'dark:bg-mode-base' : !lightMode}"
    >
      <div v-if="type === 'error'" class="flex items-center">
          <i class="xcloud xc-close-o text-xl text-danger pl-2"></i>
          <p class="text-sm text-dark dark:text-white leading-7 pl-4">
            <div v-html="message"></div>
          </p>
      </div>

      <div v-else-if="type === 'success'" class="flex items-center">
        <i class="xcloud xc-tick-o text-xl text-success-full pl-2"></i>
        <p class="text-sm text-dark dark:text-white leading-7 pl-4">
          <div v-html="message"></div>
        </p>
      </div>
      <div v-else-if="type === 'slot'" class="flex items-center">
        <img :src="asset('img/note.svg')"
             alt="Suggestion message" />
        <p class="text-sm text-dark dark:text-white leading-7 pl-4">
          <slot/>
        </p>
      </div>

      <div v-else class="flex items-center">
          <img :src="asset('img/note.svg')"
               alt="Suggestion message" />
          <p class="text-sm text-dark dark:text-white leading-7 pl-4">
            <div v-html="message"></div>
          </p>
      </div>

      </div>
</template>


<script setup>

const props = defineProps({
    title: {
      type: String,
    },
    message: String,
    type: String,
    lightMode: {
      type: Boolean,
      default: false
    }
});

</script>
