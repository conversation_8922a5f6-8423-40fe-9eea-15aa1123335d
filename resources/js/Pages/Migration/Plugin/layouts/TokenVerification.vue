<template>

    <div class="mt-30px pl-20 tablet:pl-60px wide-mobile:pl-0">

        <div v-if="form?.errors?.verification_message !== undefined"
             class="warning flex items-center bg-warning/20 px-6 py-4 rounded-md mt-20px mb-30px mobile:mt-10px mobile:mb-15px">
            <img :src="asset('img/warning.svg')" alt="warning_img">
            <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                {{ form?.errors?.verification_message }}
            </p>
        </div>
        <h4 class="class_heading text-lg tablet:text-base text-dark dark:text-white mb-30px" v-if="site_plugin_url">
            {{ $t('Plugin Page URL') }}: <a :href="site_plugin_url" target="_blank" class="underline pl-1"> {{ site_plugin_url }} </a>
        </h4>

        <label>
            <input type="checkbox" class="hidden peer" v-model="form.confirmed" v-bind:checked="form.confirmed"/>
            <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent
                         before:border-1 before:border-secondary-light before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                         before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                         before:transition before:duration-200 peer-checked:before:border-success-full
                         peer-checked:before:bg-success-full peer-checked:before:text-white">
                <span class="text-base font-normal text-secondary-full dark:text-mode-secondary-light">
                    {{ $t('I have added the authentication token to my site') }}
                </span>
            </span>

            <div v-if="form?.errors?.confirmed !== undefined" class="mt-2 text-sm dark:text-light text-red-600 flex gap-2 items-center">
                <img :src="asset('img/eeror.svg')" alt="warning_img" class="h-4"/>
                <span class="mt-1">
                    {{ $t('Please agree that you have added the authentication token to your site') }}
                </span>
            </div>
        </label>
    </div>

</template>

<script setup>

let props = defineProps({
    site_plugin_url : String,
    token : String,
    form : Object
});

</script>
