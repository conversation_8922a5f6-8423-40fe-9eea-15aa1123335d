<template>
    <migration-layout
        :steps="steps"
        :previous_step="previous_step"
        :current_step="current_step"
        :next_step="next_step"
        :suggestion_message="suggestion_message"
        :form="form"
        :submit="submit"
    >

        <template v-slot:header>
            {{ $t('Select Websites to Migrate') }}
        </template>
        <div class="bg-white dark:bg-mode-light rounded-10px">
            <div class="flex items-center justify-between mt-25px tablet:mt-20px" v-if="already_migrated_sites.length > 0">
                <h5 class="text-2xl text-dark dark:text-white">{{ $t('Previously migrated sites from') }} <b>{{ from_server }}</b> {{ $t('to') }} <b>{{ to_server }}</b>
                </h5>
            </div>
            <div  v-if="already_migrated_sites.length > 0" class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px mt-20px">
                <MigratedSite
                    v-for="site in already_migrated_sites"
                    :key="site.id"
                    :site="site"
                />

            </div>

            <div class="flex items-center justify-between mt-50px tablet:mt-40px">
                <h5 class="text-2xl text-dark dark:text-white">{{ $t('Available Sites') }}
                </h5>
                <div class="flex items-center gap-15px">
                    <button type="button"
                            v-if="showSelectAll"
                            @click.prevent="selectAllSites"
                            class="inline-flex items-center border-none border-success-light/10 justify-center min-h-40px p-2 px-15px rounded-md shadow-none text-base text-center text-success-light font-normal bg-success-light/10 focus:outline-none hover:bg-success-full/10 hover:border-success-full/10 ease-in-out transition duration-200 hover:shadow-none hover:shadow-success-dark/30"
                            fdprocessedid="jylqgi">
                        <label class="inline-flex">
                            <input type="checkbox" checked="" class="hidden peer">
                            <span
                                class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded-full before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white"><span
                                class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark "></span></span>
                        </label>
                        <span>{{ $t('Select All') }}</span>
                    </button>
                    <button type="button"
                            :disabled="isFetching"
                            @click.prevent="fetchSites"
                            :class="['inline-flex items-center justify-center min-h-40px border-1 border-success-light text-lg wide-mobile:text-base px-25px wide-mobile:px-20px mobile:px-15px rounded-md shadow-none text-white', isFetching ? 'opacity-50 cursor-not-allowed text-success-light' : 'bg-success-light hover:bg-success-full hover:shadow-lg hover:shadow-success-full/30 transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none tracking-tighter']">
                        <span class="inline-flex justify-center items-center text-lg mr-2.5">
                        <i class="xcloud xc-verify_dns"></i>
                        </span>
                        <span v-if="!isFetching" class="drop-shadow-button">{{ $t('Fetch Websites') }}</span>
                        <span v-else>{{ $t('Fetching...') }}</span>
                    </button>
                </div>
            </div>
            <div v-if="!isFetching">
                <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px mt-20px">
                    <div
                        v-for="(site,index) in serverArray"
                        @click="siteChecked(site['domain'],!form.selectedSites.includes(site['domain']))"
                        class="relative p-20px bg-white dark:bg-mode-light dark:hover:bg-dark rounded-10px border-2 border-light hover:border-primary-light dark:border-dark transition ease-in-out duration-150 group cursor-pointer"
                        :class="{'border-primary-light dark:border-primary-dark': form.selectedSites.includes(site['domain'])}"
                    >
                        <div class="flex items-center justify-between mb-15px">
                            <div class="flex items-center gap-15px">
                                <img :src="asset('img/glove.svg')" alt="authorized_keys" class="max-w-full inline-block" >
                                <text-input
                                    @click.stop=""
                                    v-if="site['editing']"
                                    v-model="updatedDomainName"
                                    id="domain_name"
                                    placeholder="Domain"
                                />
                                <h5 @click.stop="site['editing'] = true" v-else
                                    class="font-2xl tablet:text-lg text-dark dark:text-white">
                                    {{
                                        site['domain']
                                    }}</h5>
                                <i  @click.stop="updateDomain(updatedDomainName,index)"  v-if="site['editing']"
                                    class="xc-done cursor-pointer text-green-300 dark:text-green-300 xcloud text-base wide-mobile:text-sm mr-1" />
                                <i  @click.stop="site['editing'] = true;updatedDomainName=site['domain']"  v-else
                                    class="xc-edit cursor-pointer text-dark dark:text-white xcloud text-base wide-mobile:text-sm mr-1"></i>
                                <tooltip :title="$t('If the fetched domain is incorrect, you can edit it here, the domain name needs to be accurate for successful migration')">
                                    <span class="text-xs inline-flex text-secondary-full dark:text-mode-secondary-light">
                                        <i class="xcloud xc-info-2 text-2xl"></i>
                                    </span>
                                </tooltip>
                            </div>
                            <label class="inline-flex">
                                <span
                                    class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded-full before:mt-0.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200"
                                    :class="{'before:border-success-full before:bg-success-full before:text-white': form.selectedSites.includes(site['domain'])}"
                                ><span
                                    class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark "></span></span>
                            </label>
                        </div>
                        <span
                            class="text-base tablet:text-sm px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base dark:group-hover:border-transparent text-secondary-full dark:text-mode-secondary-light dark:group-hover:bg-mode-focus-dark group-hover:bg-white/10 divide-x divide-light dark:divide-mode-base group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md">
                        {{ $t('Size') }}: {{ site['storage'] }} {{ $t('GB') }}
                        </span>
                    </div>
                </div>
                <suggestion
                    class="mt-5"
                    :message="`<span class='${(storageRequired.toFixed(2) +1) > storageAvailable ? 'text-red-500' :
                    'text-green-500'}'>Current storage available in Server <b>${storageAvailable.toFixed(2)} GB</b>. <br>Required storage to migrate <b>${(storageRequired+1).toFixed(2)} GB(${storageRequired.toFixed(2)} + extra 1 GB)</b></span>`"
                />
            </div>
            <div v-else>
                <div class="flex items-center justify-center mt-50px">
                    <div class="flex items-center justify-center">
                       <i class="xcloud xc-spinner mr-2.5 animate-spin text-2xl text-dark dark:text-white"></i>
                        <span class="text-lg text-dark dark:text-white ml-10px">{{ $t('Fetching sites...') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </migration-layout>
</template>

<script setup>
import {useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";
import {useHelpStore} from "@/stores/HelpStore";
import {useNavigationStore} from "@/stores/NavigationStore";
import MigrationLayout from "@/Pages/Migration/Components/MigrationLayout.vue";
import {computed, onMounted, onUnmounted, reactive, ref, watch} from "vue";
import Button from "@/Jetstream/Button.vue";
import MigratedSite from "@/Shared/Site/MigratedSite.vue";
import {asset} from "laravel-vapor";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import TextInput from "@/Shared/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";


let helper = useHelpStore();
let navigation = useNavigationStore();

const {providers, server_migration, server_id, sitesToMigrate,
    sitesSelectedToMigrate,steps,storageAvailable,isRefetching} =
    defineProps({
    providers: Object,
    'steps': Object,
    'previous_step': String,
    'current_step': String,
    'next_step': String,
    'server_migration': Object,
    'suggestion_message': String,
    'selected_server': Object,
    'server_id': String,
    'already_migrated_sites': Object,
    'from_server': String,
    'to_server': String,
    'sitesToMigrate': Object,
    'sitesSelectedToMigrate': Object,
    'storageAvailable': String,
    'isRefetching': Boolean,
});

const isFetching = ref(isRefetching);

let form = useForm({
    'selectedSites': [],
});

let serverArray = ref(sitesToMigrate);
let storageRequired = ref(0);

//for all sites in serverArray add domain to form.selectedSites
sitesSelectedToMigrate.forEach(function (site) {
    form.selectedSites.push(site['domain']);
});

function siteChecked(site, checked) {
    if (checked) {
        form.selectedSites.push(site);
    } else {
        form.selectedSites = form.selectedSites.filter(item => item !== site);
    }
}

const showSelectAll = computed(() => {
    return form.selectedSites.length !== sitesToMigrate.length;
});

function selectAllSites() {
    form.selectedSites = [];
    serverArray.value.forEach(function (site) {
        form.selectedSites.push(site['domain']);
    });
}
//watch for  form.selectedSites changes and update storageRequired
watch(form, (newValue, oldValue) => {
    storageRequired.value = 0;
    newValue.selectedSites.forEach(function (site) {
        serverArray.value.forEach(function (server) {
            if (server['domain'] === site) {
                storageRequired.value += parseFloat(server['storage']);
            }
        });
    });
    storageRequired.value = parseFloat(storageRequired.value.toFixed(2));
});


function fetchSites(force = false) {
    isFetching.value = true;
    axios.get(route('api.server.migrate.get.sites.force', {'serverMigration': server_migration.id}), {
        params: {
            preserveScroll: true
        }
    })
        .then(response => {
            serverArray.value = [];
            if (response.data['fetching']){
                isFetching.value = true;
            }else {
                 useFlash().swal().fire({
                    icon: 'warning',
                    title: 'Fetching Sites',
                    html: 'Already fetching sites, please wait for the process to complete.'
                });
            }
        })
        .catch(error => {
            console.log(error);
            if (error.response && error.response.data && error.response.data.ssh_auth_error) {
                useFlash().swal().fire({
                    icon: 'error',
                    title: 'Authentication Failed',
                    html: error.response.data.ssh_auth_error,
                });
            }
            isFetching.value = false;
        });

}

let submit = () => {
    //if no sites selected
    if (form.selectedSites.length === 0) {
        useFlash().swal().fire({
            icon: 'error',
            title: 'Error',
            html: 'Please select at least one site to migrate',
        });
        return;
    }
    if ((storageRequired.value +1) > storageAvailable){
        useFlash().swal().fire({
            icon: 'error',
            title: 'Error',
            html:
                `Current storage available in Server <b>${storageAvailable} GB</b>. <br>Required storage to migrate <b>${(storageRequired.value+1).toFixed(2)} GB(${storageRequired.value.toFixed(2)} + extra 1 GB)</b>`,
        });
        return;
    }
    form.post(route('api.server.migrate.store.sites',{'serverMigration' : server_migration.id}), {
        preserveScroll: true,
        onError: (message) => {
            if (message.selectedSitesError) {
                useFlash().swal().fire({
                    icon: 'error',
                    title: 'Error',
                    html: message.selectedSitesError,
                })
            }
        },
    });
}

let updatedDomainName = ref('');

watch(updatedDomainName, (newValue, oldValue) => {
    updatedDomainName.value = newValue.toLowerCase();
});


function updateDomain(updatedDomainName,index){
    axios.post(route('api.server.migrate.update.domain', {'serverMigration': server_migration.id}), {
            domain: updatedDomainName,
            index: index
        })
        .then(response => {
            if (response.data['error']) {
                useFlash().swal().fire({
                    icon: 'error',
                    title: 'Error',
                    html: response.data['error'],
                });
                serverArray.value[index]['editing'] = false;
                return;
            }
            serverArray.value[index]['editing'] = false;
            serverArray.value[index]['domain'] = response.data['updatedDomain'];
            //clear form selectedSites
            form.selectedSites = [];
        })
        .catch(error => {
            serverArray.value[index]['editing'] = false;
            useFlash().swal().fire({
                icon: 'error',
                title: 'Error',
                html: 'An error occurred while updating domain name',
            });
        });


}

onMounted(() => {
    if (window.Echo) {
        console.log("Listening for site backup status changed:", server_migration?.id);
        window.Echo.private("server.migration.fetchsites." + server_migration?.id).listen(
            "SiteFetchingCompleted",
            (e) => {
                isFetching.value = false;
                e.sites.forEach(function (site) {
                    serverArray.value.push(site);
                });
            }
        );
    }
});
onUnmounted(() => {
    if (window.Echo) {
        window.Echo.private("server.migration.fetchsites." + server_migration?.id).stopListening(
            "SiteFetchingCompleted"
        );
    }
});
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
