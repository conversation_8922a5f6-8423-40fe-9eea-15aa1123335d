<template>
    <migration-layout
        :steps="steps"
        :previous_step="previous_step"
        :current_step="current_step"
        :next_step="next_step"
        :suggestion_message="suggestion_message"
        :form="form"
        :submit="submit"
    >

        <template v-slot:header>
            {{ $t('Select Websites to Migrate') }}
        </template>
        <div class="bg-white dark:bg-mode-light rounded-10px">
            <div class="flex items-center justify-between mt-25px tablet:mt-20px"
                 v-if="already_migrated_sites.length > 0">
                <h5 class="text-2xl text-dark dark:text-white">{{ $t('Previously migrated sites from') }} <b>{{ from_server }}</b>
                    {{ $t('to') }} <b>{{ to_server }}</b>
                </h5>
            </div>
            <div v-if="already_migrated_sites.length > 0"
                 class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px mt-20px">
                <MigratedSite
                    v-for="site in already_migrated_sites"
                    :key="site.id"
                    :site="site"
                />

            </div>


            <div class="flex items-center justify-between mt-50px tablet:mt-40px">
                <h5 class="text-2xl text-dark dark:text-white">{{ $t('Available Sites') }}
                </h5>
                <div class="flex items-center gap-15px">
                    <button type="button"
                            v-if="showSelectAll"
                            @click.prevent="selectAllSites"
                            class="inline-flex items-center border-none border-success-light/10 justify-center min-h-40px p-2 px-15px rounded-md shadow-none text-base text-center text-success-light font-normal bg-success-light/10 focus:outline-none hover:bg-success-full/10 hover:border-success-full/10 ease-in-out transition duration-200 hover:shadow-none hover:shadow-success-dark/30"
                            fdprocessedid="jylqgi">
                        <label class="inline-flex">
                            <input type="checkbox" checked="" class="hidden peer">
                            <span
                                class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded-full before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white"><span
                                class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark "></span></span>
                        </label>
                        <span>{{ $t('Select All') }}</span>
                    </button>
                    <button type="button"
                            :disabled="isFetching"
                            @click.prevent="fetchSites"
                            :class="['inline-flex items-center justify-center min-h-40px border-1 border-success-light text-lg wide-mobile:text-base px-25px wide-mobile:px-20px mobile:px-15px rounded-md shadow-none text-white', isFetching ? 'opacity-50 cursor-not-allowed text-success-light' : 'bg-success-light hover:bg-success-full hover:shadow-lg hover:shadow-success-full/30 transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none tracking-tighter']">
                        <span class="inline-flex justify-center items-center text-lg mr-2.5">
                        <i class="xcloud xc-verify_dns"></i>
                        </span>
                        <span v-if="!isFetching" class="drop-shadow-button">{{ $t('Fetch Websites') }}</span>
                        <span v-else>{{ $t('Fetching...') }}</span>
                    </button>
                </div>
            </div>

            <div v-if="!isFetching" class="p-4">
                <div v-if="sitesArray?.wordpress_sites?.length > 0">
                    <h6 class="text text-dark dark:text-white">{{ $t('Wordpress Sites To Migrate') }}</h6>
                    <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px mt-5px">
                        <div
                            v-for="site in sitesArray.wordpress_sites"
                            @click="siteChecked(site['domain'],!form.selectedSites.includes(site['domain']))"
                            class="relative p-20px bg-white dark:bg-mode-light dark:hover:bg-dark rounded-10px border-2 border-light hover:border-primary-light dark:border-dark transition ease-in-out duration-150 group cursor-pointer"
                            :class="{'border-primary-light dark:border-primary-dark': form.selectedSites.includes(site['domain'])}"
                        >
                            <div class="flex items-center justify-between mb-15px">
                                <div class="flex items-center gap-15px">
                                    <img :src="asset('img/wordpress-blue.svg')" alt="authorized_keys"
                                         class="max-w-full inline-block w-8 h-8">
                                    <a :href="'https://' + site['domain']" target="_blank"
                                       class="hover:underline font-2xl tablet:text-lg text-dark dark:text-white"> {{
                                        site['domain']
                                    }}</a>
                                </div>
                                <label class="inline-flex">
                            <span
                                class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded-full before:mt-0.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200"
                                :class="{'before:border-success-full before:bg-success-full before:text-white': form.selectedSites.includes(site['domain'])}"
                            ><span
                                class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark "></span></span>
                                </label>
                            </div>
                            <span
                                class="text-sm tablet:text-sm px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base dark:group-hover:border-transparent text-secondary-full dark:text-mode-secondary-light dark:group-hover:bg-mode-focus-dark group-hover:bg-white/10 divide-x divide-light dark:divide-mode-base group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md">
                                {{ $t('Type') }}: {{ titleCase(site['type']) }}
                            </span>
                            <br/>

                            <span
                                class="text-sm tablet:text-sm px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base dark:group-hover:border-transparent text-secondary-full dark:text-mode-secondary-light dark:group-hover:bg-mode-focus-dark group-hover:bg-white/10 divide-x divide-light dark:divide-mode-base group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md">
                                {{ $t('Directory') }}: {{ titleCase(site['dir']) }}
                            </span>
                        </div>
                    </div>
                </div>

                <div v-if="sitesArray?.non_wordpress_sites?.length > 0">
                    <h6 class="text text-dark dark:text-white mt-20px">{{ $t('Non-Wordpress Sites') }}</h6>
                    <tooltip title="Currently, Non Wordpress sites migration is not available">
                        <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px mt-5px">
                            <div
                                v-for="site in sitesArray.non_wordpress_sites"
                                class="relative p-20px bg-white dark:bg-mode-light  rounded-10px border-2 border-light dark:border-dark transition ease-in-out duration-150 group cursor-not-allowed"
                                :class="{'border-primary-light dark:border-primary-dark': form.selectedSites.includes(site['domain'])}"
                            >
                                <div class="flex items-center justify-between mb-15px">
                                    <div class="flex items-center gap-15px">
                                        <img :src="asset('img/glove.svg')" alt="authorized_keys"
                                             class="max-w-full inline-block w-8 h-8">
                                        <a :href="'https://' + site['domain']" target="_blank"
                                           class="hover:underline font-2xl tablet:text-lg text-dark dark:text-white"> {{
                                                site['domain']
                                            }}</a>
                                    </div>
                                </div>
                                <span
                                    class="text-sm tablet:text-sm px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base  text-secondary-full dark:text-mode-secondary-light  divide-x divide-light dark:divide-mode-base group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md">
                                    {{ $t('Type') }}: {{ titleCase(site['type']) }}
                                </span>
                                <br/>

                                <span
                                    class="text-sm tablet:text-sm px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base text-secondary-full dark:text-mode-secondary-light  divide-x divide-light dark:divide-mode-base  rounded-md">
                                    {{ $t('Directory') }}: {{ titleCase(site['dir']) }}
                                </span>
                            </div>
                        </div>
                    </tooltip>
                </div>

            </div>

            <div v-else>
                <div class="flex items-center justify-center mt-50px">
                    <div class="flex items-center justify-center">
                       <i class="xcloud xc-spinner mr-2.5 animate-spin text-2xl text-dark dark:text-white"></i>
                        <span class="text-lg text-dark dark:text-white ml-10px">{{ $t('Fetching sites...') }}</span>
                    </div>
                </div>
            </div>


        </div>
    </migration-layout>
</template>

<script setup>
import {useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";
import {useHelpStore} from "@/stores/HelpStore";
import {useNavigationStore} from "@/stores/NavigationStore";
import MigrationLayout from "@/Pages/Migration/Components/MigrationLayout.vue";
import {computed, onMounted, reactive, ref, watch} from "vue";
import Button from "@/Jetstream/Button.vue";
import MigratedSite from "@/Shared/Site/MigratedSite.vue";
import {asset} from "laravel-vapor";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Tooltip from "@/Shared/Tooltip.vue";


let helper = useHelpStore();
let navigation = useNavigationStore();

const {providers, keypair, tags, server_migration, server_id, sitesToMigrate, sitesSelectedToMigrate, steps,isRefetching} =
    defineProps({
        providers: Object,
        keypair: Object,
        tags: {
            type: Array,
            required: true,
        },
        'steps': Object,
        'previous_step': String,
        'current_step': String,
        'next_step': String,
        'server_migration': Object,
        'suggestion_message': String,
        'selected_server': Object,
        'server_id': String,
        'already_migrated_sites': Object,
        'from_server': String,
        'to_server': String,
        'sitesToMigrate': Object,
        'sitesSelectedToMigrate': Object,
        'isRefetching': Boolean,
    });

const isFetching = ref(isRefetching);

let form = useForm({
    'selectedSites': [],
});

let sitesArray = ref(sitesToMigrate);

//for all sites in sitesArray add domain to form.selectedSites
sitesSelectedToMigrate.forEach(function (site) {
    form.selectedSites.push(site['domain']);
});

function siteChecked(site, checked) {
    if (checked) {
        form.selectedSites.push(site);
    } else {
        form.selectedSites = form.selectedSites.filter(item => item !== site);
    }
}

const showSelectAll = computed(() => {
    if (!sitesToMigrate?.wordpress_sites) {
        return false;
    }
    return form.selectedSites.length !== sitesToMigrate?.wordpress_sites?.length;
});

function selectAllSites() {
    form.selectedSites = [];
    sitesArray.value.wordpress_sites.forEach(function (site) {
        form.selectedSites.push(site['domain']);
    });
}


function fetchSites() {
    isFetching.value = true;
    axios.get(route('api.server.migrate.cpanel.sites.refresh', {
        'server': server_id,
        'serverMigration': server_migration.id
    }), {
        params: {
            preserveScroll: true
        }
    })
        .then(response => {
            sitesArray.value = [];
            if (response.data['fetching']){
                isFetching.value = true;
            }else {
                 useFlash().swal().fire({
                    icon: 'warning',
                    title: 'Fetching Sites',
                    html: 'Already fetching sites, please wait for the process to complete.'
                });
            }
        })
        .catch(error => {
            useFlash().swal().fire({
                icon: 'error',
                title: 'Unable to fetch sites',
                html: 'Please try again later',
            });
            isFetching.value = false;
        });

}

function titleCase(str) {
    //conver str_hello to Str Hello
    return str.toLowerCase().split('_').map(function (word) {
        return word.charAt(0).toUpperCase() + word.slice(1);
    }).join(' ');
}

let submit = () => {
    //if no sites selected
    if (form.selectedSites.length === 0) {
        useFlash().swal().fire({
            icon: 'error',
            title: 'Error',
            html: 'Please select at least one site to migrate',
        });
        return;
    }
    form.post(route('api.server.migrate.store.cpanel.api.sites', {
        'server': server_id,
        'serverMigration': server_migration.id
    }), {
        preserveScroll: true,
        onError: (message) => {
            if (message.selectedSitesError) {
                useFlash().swal().fire({
                    icon: 'error',
                    title: 'Error',
                    html: message.selectedSitesError,
                })
            }
        },
    });
}

onMounted(() => {
    if (window.Echo) {
        window.Echo.private("server.migration.fetchsites." + server_migration?.id).listen(
            "SiteFetchingCompleted",
            (e) => {
                isFetching.value = false;
                if (e.sites?.wordpress_sites){
                    sitesArray.value.wordpress_sites = [];
                    e.sites.wordpress_sites.forEach(function (site) {
                         sitesArray.value.wordpress_sites.push(site);
                    });
                }
                if (e.sites?.non_wordpress_sites){
                    sitesArray.value.non_wordpress_sites = [];
                    e.sites.non_wordpress_sites.forEach(function (site) {
                        sitesArray.value.non_wordpress_sites.push(site);
                    });
                }
            }
        );
    }
});

</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
