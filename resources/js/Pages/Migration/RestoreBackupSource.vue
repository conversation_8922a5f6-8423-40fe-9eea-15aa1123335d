<template>
    <migration-layout
        :steps="steps"
        :form="form"
        :site_migration="site_migration"
        :previous_step="previous_step"
        :current_step="current_step"
        :next_step="next_step"
        :suggestion_message="suggestion_message"
        :post_route="route(post_route,{server: server.id,siteMigration: site_migration.id})"
        :previous_route="previous_route"
    >
        <form>
            <div class="grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px wide-mobile:gap-4 mb-30px">
                <div
                    :class="{
                                'border-primary-light ring-1 ring-primary-light':  form.import_type === 'site',
                                'border-secondary-light dark:border-mode-focus-light':  form.import_type !== 'site',
                            }"
                    class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                    @click="form.import_type = 'site'"
                >
                    <div class="shrink-0 inline-flex items-center justify-center">
                        <i class="xcloud text-4xl xc-server dark:text-white"></i>
                    </div>
                    <div class="flex flex-col gap-2">
                        <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                            Existing Site/Server Import
                        </h5>
                        <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                            Choose the server or site from which you want to recover the necessary files.
                        </p>
                    </div>
                </div>
                <div
                    :class="{
                                    'border-secondary-light dark:border-mode-focus-light': form.import_type !== 'bucket',
                                    'border-primary-light ring-1 ring-primary-light': form.import_type === 'bucket'
                                }"
                    class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                    @click="form.import_type = 'bucket'"
                >
                    <div class="shrink-0 inline-flex items-center justify-center">
                        <i class="xcloud text-4xl xc-file dark:text-white"></i>
                    </div>
                    <div class="flex flex-col gap-2">
                        <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                            Import from Bucket
                        </h5>
                        <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                            Choose the bucket from which you want to recover the necessary files.
                        </p>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px">
                <template v-if="form.import_type === 'site'">
                    <div class="server-select-multiselect">
                      <label class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none" for="site_id">
                        Select Server
                      </label>
                      <Multiselect
                          @search-change="searchServers($event)"
                          :can-clear="false"
                          id="site_id"
                          v-model="form.server_id"
                          :searchable="true"
                          :create-option="false"
                          placeholder="Select here..."
                          :options="backupServers"
                          :class="{
                            '!border-danger !bg-danger/5': form.errors.server_id,
                          }"
                      />
                        <Error :error="form.errors.server_id" />
                    </div>
                    <div class="site-select-multiselect">
                      <label class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none" for="site_id">
                        Select Site
                      </label>
                      <Multiselect
                          @search-change="searchSites($event)"
                          :can-clear="false"
                          :disable="null === form.server_id"
                          id="site_id"
                          v-model="form.site_id"
                          :searchable="true"
                          :create-option="false"
                          placeholder="Select here..."
                          :options="backupSites"
                      />
                      <Error :error="form.errors.site_id" />
                    </div>
                    <div class="site-select-multiselect">
                      <label class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none" for="backup_type">
                        Select Backup Type
                      </label>
                      <Multiselect
                          id="backup_type"
                          v-model="form.backup_type"
                          :can-clear="false"
                          :searchable="true"
                          :create-option="false"
                          placeholder="Select here..."
                          :options="[
                                {label: 'Local', value: 'local'},
                                {label: 'Remote', value: 'remote'},
                          ]"
                      />
                      <Error :error="form.errors.backup_type" />
                    </div>
                </template>
                <template v-else>
                    <div class="server-select-multiselect">
                        <label class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none" for="bucket_id">
                            Select Bucket
                        </label>
                        <Multiselect
                            :can-clear="false"
                            @search-change="searchBucket($event)"
                            id="bucket_id"
                            :searchable="true"
                            v-model="form.bucket_id"
                            :create-option="false"
                            placeholder="Select here..."
                            :options="buckets"
                        />
                        <Error :error="form.errors.bucket_id" />
                    </div>
                    <div class="server-select-multiselect">
                        <text-input
                            :error="form.errors.domain_name"
                            v-model="form.domain_name"
                            label="Domain"
                            placeholder="Enter domain"
                            id="domain"
                            type="text"
                            note="Domain name(without http/https) of the site you want to restore."
                        />
                    </div>
                </template>
            </div>
        </form>
    </migration-layout>
</template>
<script setup>
import MigrationLayout from "@/Pages/Migration/Components/MigrationLayout.vue";
import { useForm } from "@inertiajs/inertia-vue3";
import Multiselect from "@vueform/multiselect";
import { onMounted, ref, watch } from "vue";
import { useFlash } from "@/Composables/useFlash";
import TextInput from "@/Shared/TextInput.vue";
import Error from "@/Shared/Error.vue";
import {useCapitalizeString} from "@/Composables/useCapitalizeString";
const props = defineProps({
    form: Object,
    steps: Object,
    previous_step: String,
    current_step: String,
    next_step: String,
    site_migration: Object,
    suggestion_message: String,
    server: Object,
    sites: [Array, Object],
    post_route: String,
    previous_route: String,
    git_deployment_url: String,
    ssh_pub_key: String
});

const data = props.form;
const form = useForm({
    server_id: data?.server_id,
    site_id: data?.site_id,
    bucket_id: data?.bucket_id,
    import_type: data?.import_type || 'site',
    backup_type: data?.backup_type || 'local',
    domain_name: data?.domain_name || '',
});

const backupSites = ref([]);
const backupServers = ref([]);
const buckets = ref([]);

const fetchData = async (routeName, params, targetRef, labelKey = 'name') => {
    try {
        const { data } = await axios.get(route(routeName, params));
        targetRef.value = data.map(item => ({ value: item.id, label: useCapitalizeString(item[labelKey]).sanitizeXSSPayload() }));
    } catch ({ response }) {
        useFlash().error(response?.data?.message || 'Something went wrong');
    }
};

onMounted(() => {
    if (form.import_type === 'bucket') {
        fetchData('api.search.bucket', { query: '' }, buckets, 'bucket');
    } else {
        fetchData('api.search.server.site-backup', { query: '' }, backupServers);
        if (form.server_id) fetchData('api.search.sites.site-backup', { server: form.server_id, query: '' }, backupSites);
    }
});

watch(() => form.server_id, (server_id) => {
    if (server_id) {
        fetchData('api.search.sites.site-backup', { server: server_id, query: '' }, backupSites);
    } else {
        backupSites.value = [];
        form.site_id = null;
    }
});

watch(() => form.import_type, async (import_type) => {
    if (import_type === 'bucket' && !buckets.value.length) {
        await fetchData('api.search.bucket', {query: ''}, buckets, 'bucket');
    } else if (import_type === 'site' && !backupServers.value.length) {
        await fetchData('api.search.server.site-backup', {query: ''}, backupServers);
        if (form.server_id) await fetchData('api.search.sites.site-backup', {
            server: form.server_id,
            query: ''
        }, backupSites);
    }
});

const searchServers = async (query) => {
    await fetchData('api.search.server.site-backup', { query }, backupServers);
};

const searchBucket = async (query) => {
    await fetchData('api.search.bucket', { query }, buckets, 'bucket');
};

const searchSites = async (query) => {
    if (form.server_id) {
        await fetchData('api.search.sites.site-backup', { server: form.server_id, query }, backupSites);
    }
};

</script>



<!-- style -->
<style>
@import "@vueform/multiselect/themes/tailwind.css";
</style>
