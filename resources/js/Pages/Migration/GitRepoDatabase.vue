<template>
<migration-layout
    :steps="steps"
    :form="form"
    :site_migration="site_migration"
    :previous_step="previous_step"
    :current_step="current_step"
    :next_step="next_step"
    :suggestion_message="suggestion_message"
>
    <template v-slot:header>
        {{ $t('Files and Database Migration') }} ({{ site_migration.domain_name }})
    </template>
    <h3
        class="text-dark dark:text-white text-2xl wide-mobile:text-xl mb-30px tablet:mb-25px wide-mobile:mb-20px mobile:mb-15px">
        {{ $t('Select Databases To Migrate') }}
    </h3>
    <h4 class="text-lg text-dark dark:text-white mb-30px tablet:mb-15px">
        {{ $t('Migrate The Following Content') }}:
    </h4>
    <div
        class="bg-light dark:bg-mode-light rounded-md mt-30px">
        <RadioInputGroup heading="Database Management">
            <RadioInput
                v-model="form.database_provider"
                value="in_server">{{ $t('Create Database In Server') }}
            </RadioInput>
 <!--            not ready -->
<!--            <RadioInput-->
<!--                v-if="server?.cloudProvider?.provider !== undefined"-->
<!--                v-model="form.database_provider"-->
<!--                value="from_provider">-->
<!--                Managed Database From Provider-->
<!--            </RadioInput>-->
            <RadioInput
                v-model="form.database_provider"
                value="custom">{{ $t('Add Your Existing Database') }}
            </RadioInput>
            <RadioInput
                v-model="form.database_provider"
                value="null">{{ $t('Without Database') }}
            </RadioInput>
        </RadioInputGroup>
        <div
            v-if="form.database_provider !== 'null'"
            class="bg-white dark:bg-mode-base divide-y-2 divide-light dark:divide-mode-light
                           border-2 border-light dark:border-mode-light rounded-b-md">
            <Database :server="server" :form="form"></Database>
        </div>
    </div>



</migration-layout>
</template>

<script setup>
    import MigrationLayout from "@/Pages/Migration/Components/MigrationLayout.vue";
    import {computed, ref} from 'vue';
    import RadioInput from '@/Shared/RadioInput.vue'
    import {useForm} from "@inertiajs/inertia-vue3";
    import TextInput from '@/Shared/TextInput.vue'
    import Switch from "@/Shared/Switch.vue";
    import Database from "@/Pages/Site/New/Components/Database.vue";
    import RadioInputGroup from "@/Shared/RadioInputGroup.vue";

    let props = defineProps({
        'form': ref(Object),
        'siteDefault': Object,
        'steps': Object,
        'previous_step': String,
        'current_step': String,
        'next_step': String,
        'site_migration': Object,
        'suggestion_message': String,
        'site_default': Object,
        'server': Object,
    })

    let form = useForm({
        site_user: props.siteDefault.site_user,
        deploy_script: props.siteDefault.deploy_script,
        admin_user: props.siteDefault.admin_user,
        admin_password: props.siteDefault.admin_password,
        admin_email: props.siteDefault.admin_email,
        database_name: props.siteDefault.database_name,
        database_user: props.siteDefault.database_user,
        database_password: props.siteDefault.database_password,
        database_host: props.siteDefault.database_host,
        database_port: props.siteDefault.database_port,
        prefix: props.siteDefault.prefix,
        wordpress_version: props.siteDefault.wordpress_version,
        php_version: props.siteDefault.php_version,
        database_provider: props.siteDefault.database_provider,
        managed_database_mode: props.siteDefault.managed_database_mode,
        managed_database_options: props.siteDefault.managed_database_options
    });
</script>
