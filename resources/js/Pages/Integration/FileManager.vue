<template>
  <SingleSite :site="site" :server="server">
      <div class="bg-white dark:bg-mode-light rounded-lg shadow-sm p-6">
          <!-- Breadcrumb Navigation -->
          <div class="mb-4 flex items-center text-[24px] font-medium">
            <button
              @click="navigateToRoot"
              class="text-[#74778E]"
            >
              {{site?.name}}
            </button>
            <span class="mx-2 text-gray-700 dark:text-[#FCFCFD]">/</span>
            <template v-for="(part, index) in pathParts" :key="index">
              <button
                @click="navigateToPath(index)"
                class="text-gray-700 dark:text-[#FCFCFD]"
              >
                {{ part }}
              </button>
              <span v-if="index < pathParts.length - 1" class="mx-2 text-gray-700 dark:text-[#FCFCFD]">/</span>
            </template>
          </div>

          <!-- Action Buttons -->
          <div class="mb-4 flex items-center flex-col sm:flex-row justify-between gap-2">
            <div class="flex flex-wrap gap-2">
              <Btn @click="showUploadModal = true" class="bg-transparent hover:bg-mode-base underline">
                {{ $t('Upload File') }}
              </Btn>
            <Btn @click="showChunkUploadModal = true" class="bg-transparent hover:bg-mode-base underline">
              {{ $t('Chunk Upload') }}
            </Btn>
              <Btn @click="refreshFiles" class="bg-transparent hover:bg-mode-base">
                <i class="xcloud xc-refresh"></i>
                {{ $t('Refresh') }}
              </Btn>
            </div>
            <div class="flex items-center flex-col md:flex-row gap-[16px]">
              <button type="button"
                    ref="dropDown"
                    class="inline-flex items-center rounded-[4px] border-light dark:border-mode-light shadow-none px-[16px] wide-mobile:px-[12px] py-[10px] wide-mobile:py-[8px] bg-light dark:bg-mode-base text-base wide-mobile:text-sm text-dark focus:outline-0 px-20px wide-mobile:px-15px gap-20px dark:text-[#697586]">
                <span class="capitalize">Actions</span>
                <span class="inline-flex text-xs text-secondary-light dark:text-[#697586]">
                <i class="xcloud xc-angle_down"
                   :class="{'rotate-180': showDropDown}"
                ></i>
            </span>
            </button>

              <div class="relative flex items-center group rounded-[4px]">
                <input type="text"
                    v-model="searchQuery"
                    @input="searchFiles"
                    class="flex-auto w-full appearance-none bg-light dark:bg-mode-base px-[16px] wide-mobile:px-[12px] py-[10px] wide-mobile:py-[8px] rounded text-secondary-full dark:text-[#697586] text-base placeholder-secondary-light dark:placeholder-secondary-full focus:outline-none font-normal border-transparent focus:border-success-light !ring-0"
                    :placeholder="$t('Search file')"/>
                <div
                    class="icon flex absolute top-1/2 right-15px -translate-y-1/2 pointer-events-none text-lg">
                    <i class="xcloud xc-search text-secondary-light group-focus-within:text-success-light" v-if="!searchQuery"></i>
                </div>
                <div
                    v-if="searchQuery"
                    class="icon flex absolute top-1/2 right-15px -translate-y-1/2 cursor-pointer text-sm"
                    @click="clearSearch">
                    <i class="xcloud xc-close1 text-danger"></i>
                </div>
              </div>
                   <button
                      @click="showCreateFolderModal = true"
                      class="whitespace-nowrap inline-flex items-center justify-center rounded-[4px] border-success-full
                                  shadow-none min-h-[36px] wide-mobile:min-h-[30px] px-[16px] wide-mobile:px-[12px] py-[10px] wide-mobile:py-[8px] bg-primary-light
                                  text-[14px] wide-mobile:text-sm text-white focus:outline-0"
                    >
                        <span class="inline-flex mr-2 text-[14px]">
                            <i class="xcloud xc-add"></i>
                        </span>
                        {{ $t('Add New Folder') }}
                    </button>

            </div>
          </div>

          <!-- Loading Indicator -->
          <skeleton v-if="loading" :rows="8" ></skeleton>
          <!-- Empty State -->
          <div v-else-if="files.length === 0" class="py-10 text-center">
            <div class="text-4xl font-bold text-gray-300 dark:text-gray-600 mb-2">📁</div>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ $t('No files') }}</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {{ $t('This folder is empty. Upload a file or create a new folder.') }}
            </p>
          </div>

          <!-- File List -->
          <div v-else class="bg-gray-100 dark:bg-[#181f38] text-gray-700 dark:text-[#CDD5DF] font-sans">
            <div class="">
              <div class="overflow-x-auto">
                <table class="w-full border-collapse">
                  <thead>
                    <tr class="bg-[#c9d8ea] dark:bg-[#2a325c] text-gray-600 dark:text-[#9AA4B2] text-left text-[14px] border-b border-gray-300 dark:border-[#3A457E]">
                      <th class="py-[12px] px-[24px] w-[12px] h-[12px] text-gray-600 dark:text-[#9AA4B2] rounded-tl-[8px]">
                        <input
                          type="checkbox"
                          :checked="allFilesSelected"
                          @change="toggleSelectAll"
                          class="focus:ring-0 focus:ring-offset-0 bg-transparent rounded-[2px]"
                        />
                      </th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('File Name') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Size') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Modified') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Permissions') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Owner') }}</th>
                      <th class="py-[12px] px-[24px] font-medium"> {{ $t('Group') }}</th>
                      <th class="py-[12px] px-[24px] font-medium rounded-tr-[8px]"> {{ $t('Action') }}</th>
                    </tr>
                  </thead>
                  <tbody class="rounded-b-[8px]">
                    <tr v-for="file in files" :key="file.path" class="border-b border-gray-100 dark:border-[#232747] text-[14px] font-medium even:bg-gray-50 dark:even:bg-[#171A30] odd:bg-white dark:odd:bg-[#181E37]">
                      <td class="py-[12px] px-[24px]">
                        <input
                          type="checkbox"
                          v-model="file.selected"
                          class="rounded-[2px] focus:ring-0 focus:ring-offset-0 bg-transparent w-[14px] h-[14px]"
                        />
                      </td>
                      <td class="py-[12px] px-[24px]">
                        <div
                            :class="{
                                'cursor-pointer': file.type === 'dir'
                            }"
                        class="flex items-center"

                        @click="navigateTo(file)"
                        >
                          <i
                            class="mr-2 xcloud"
                            :class="file.type === 'dir' ? 'xc-folder text-primary-light' : 'xc-document_3 text-success-full'"
                          >
                          </i>
                          <span v-if="file.type === 'dir'">{{ getFileName(file.path) }}</span>
                          <span v-else>{{ getFileName(file.path) }}</span>
                        </div>
                      </td>
                      <td class="py-[12px] px-[24px]">{{ file.type === 'directory' ? '-' : formatFileSize(file?.stat?.size || file.size) }}</td>
                      <td class="py-[12px] px-[24px]">{{ file.last_modified || '-' }}</td>
                      <td class="py-[12px] px-[24px]">
                        <span v-if="file.stat" class="font-mono">
                          {{ modeToSymbolic(file.stat.mode) }}
                          <span class="text-gray-500 dark:text-gray-400 ml-1">({{ formatPermissions(file.stat.mode) }})</span>
                        </span>
                        <span v-else>-</span>
                      </td>
                      <td class="py-[12px] px-[24px]">
                        <span v-if="file.stat">
                          {{ file.stat?.uid }}
                          <span class="text-gray-500 dark:text-gray-400 ml-1">({{ file.stat.uid }})</span>
                        </span>
                        <span v-else>-</span>
                      </td>
                      <td class="py-[12px] px-[24px]">
                        <span v-if="file.stat">
                          {{ file.stat.gid }}
                          <span class="text-gray-500 dark:text-gray-400 ml-1">({{ file.stat.gid }})</span>
                        </span>
                        <span v-else>-</span>
                      </td>
                      <td class="py-[12px] px-[24px]">
                        <div class="flex space-x-2">
                          <button
                            @click="showFileInfo(file)"
                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            :title="('View file details')"
                          >
                            <i class="xcloud xc-info text-[14px]"></i>
                          </button>
                          <div class="relative">
                            <button @click="handleFileActions(file)" class="text-gray-700 dark:text-white hover:text-gray-500 dark:hover:text-gray-400 rotate-90 dropdown-trigger">
                              <i class="xcloud xc-menu-vertical text-[14px]"></i>
                            </button>
                            <SftpFileActions
                              v-if="openFileActions === file"
                              :file="file"
                              @download="(path) => { downloadFile(path); openFileActions.value = null; }"
                              @rename="(file) => { openRenameModal(file); openFileActions.value = null; }"
                              @delete="(file) => { deleteFile(file); openFileActions.value = null; }"
                              @edit="(file) => { openEditModal(file); openFileActions.value = null; }"
                            />
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
    <!-- Upload Modal -->
    <Modal :show="showUploadModal" @close="showUploadModal = false" title="Upload File">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">{{ $t('Upload File') }}</h2>
        <div class="mt-4">
          <div class="mt-2">
            <input
              type="file"
              ref="fileInput"
              @change="handleFileChange"
              class="block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-semibold
                file:bg-indigo-50 file:text-indigo-700
                hover:file:bg-indigo-100"
            />
          </div>
          <div v-if="uploadProgress > 0" class="mt-4">
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div
                class="bg-indigo-600 h-2.5 rounded-full"
                :style="{ width: `${uploadProgress}%` }"
              ></div>
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ uploadProgress }}% {{ $t('uploaded') }}</p>
          </div>
          <div class="mt-6 flex justify-end">
            <Btn
              @click="showUploadModal = false"
              classes="bg-secondary-full hover:bg-secondary-full mr-2"
            >
              {{ $t('Cancel') }}
            </Btn>
            <Btn
              @click="uploadFile"
              classes="bg-primary-full hover:bg-primary-full"
              :disabled="!selectedFile || uploading"
              :loading="uploading"
            >
              <span v-if="uploading">{{ $t('Uploading...') }}</span>
              <span v-else>{{ $t('Upload') }}</span>
            </Btn>
          </div>
        </div>
      </div>
    </Modal>

    <!-- Chunk Upload Modal -->
    <Modal :show="showChunkUploadModal" @close="showChunkUploadModal = false" title="Chunk Upload (for large files)">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">{{ $t('Chunk Upload (for large files)') }}</h2>
        <div class="mt-4">
          <div class="mt-2">
            <input
              type="file"
              ref="chunkFileInput"
              @change="handleChunkFileChange"
              class="block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-semibold
                file:bg-indigo-50 file:text-indigo-700
                hover:file:bg-indigo-100"
            />
          </div>
          <div v-if="chunkUploadProgress > 0" class="mt-4">
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div
                class="bg-indigo-600 h-2.5 rounded-full"
                :style="{ width: `${chunkUploadProgress}%` }"
              ></div>
            </div>
            <p class="text-sm text-gray-500 mt-1">
              {{ chunkUploadProgress }}% uploaded (Chunk {{ currentChunk }}/{{ totalChunks }})
            </p>
          </div>
          <div class="mt-6 flex justify-end">
            <Btn
              @click="showChunkUploadModal = false"
              classes="bg-secondary-full hover:bg-secondary-full mr-2"
            >
              {{ $t('Cancel') }}
            </Btn>
            <Btn
              @click="uploadFileInChunks"
              classes="bg-primary-full hover:bg-primary-full"
              :disabled="!selectedChunkFile || chunkUploading"
              :loading="chunkUploading"
            >
              <span v-if="chunkUploading">{{ $t('Uploading...') }}</span>
              <span v-else>{{ $t('Upload') }}</span>
            </Btn>
          </div>
        </div>
      </div>
    </Modal>

    <!-- Create Folder Modal -->
    <Modal :show="showCreateFolderModal" @close="showCreateFolderModal = false" title="Create New Folder">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">{{ $t('Create New Folder') }}</h2>
        <div class="mt-4">
          <div class="mt-2">
            <input
              type="text"
              v-model="newFolderName"
              placeholder="Folder name"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>
          <div class="mt-6 flex justify-end">
            <JetButton
              @click="showCreateFolderModal = false"
              class="bg-white text-gray-700 border border-gray-300 mr-2"
            >
              {{ $t('Cancel') }}
            </JetButton>
            <JetButton
              @click="createFolder"
              class="bg-indigo-600"
              :disabled="!newFolderName"
            >
              {{ $t('Create') }}
            </JetButton>
          </div>
        </div>
      </div>
    </Modal>

    <!-- Rename Modal -->
    <Modal :show="showRenameModal" @close="closeRenameModal" :title="fileToRename?.type === 'directory' ? 'Rename Folder' : 'Rename File'">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">Rename {{ fileToRename?.type === 'directory' ? 'Folder' : 'File' }}</h2>
        <div class="mt-4">
          <div class="mt-2">
            <input
              type="text"
              v-model="newName"
              placeholder="New name"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>
          <div class="mt-6 flex justify-end">
            <JetButton
              @click="closeRenameModal"
              class="bg-white text-gray-700 border border-gray-300 mr-2"
            >
              {{ $t('Cancel') }}
            </JetButton>
            <JetButton
              @click="renameFile"
              class="bg-indigo-600"
              :disabled="!newName"
            >
              {{ $t('Rename') }}
            </JetButton>
          </div>
        </div>
      </div>
    </Modal>


    <!-- Edit File Modal -->
    <Modal :show="showEditModal" @close="closeEditModal" :title="'Edit File: ' + (fileToEdit ? getFileName(fileToEdit.path) : '')" :widthClass="'max-w-4xl'">
      <div class="p-6">
        <div v-if="editError" class="mb-4 p-3 bg-red-100 text-red-800 rounded-md">
          {{ editError }}
        </div>
        <div class="h-[500px] border border-gray-300 dark:border-gray-700 rounded-md">
          <v-ace-editor
            v-model:value="fileContent"
            lang="text"
            :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
            style="height: 100%; width: 100%"
            :options="{ fontSize: '14px' }"
          />
        </div>
        <div class="mt-6 flex justify-end">
          <Btn
            @click="closeEditModal"
            classes="bg-secondary-full hover:bg-secondary-full mr-2"
          >
            {{ $t('Cancel') }}
          </Btn>
          <Btn
            @click="saveFile"
            classes="bg-primary-full hover:bg-primary-full"
            :loading="isEditing"
          >
            <span v-if="isEditing">{{ $t('Saving...') }}</span>
            <span v-else>{{ $t('Save') }}</span>
          </Btn>
        </div>
      </div>
    </Modal>

    <!-- File Info Modal -->
    <Modal :show="showFileInfoModal" @close="showFileInfoModal = false" :title="('File Information')" :widthClass="'max-w-4xl'">
      <div class="p-6">
        <div v-if="selectedFileForInfo" class="space-y-4">
          <div class="flex justify-between items-center border-b pb-2">
            <h3 class="text-lg font-medium">{{ getFileName(selectedFileForInfo.path) }}</h3>
            <span class="px-2 py-1 rounded-md text-xs uppercase"
                  :class="selectedFileForInfo.type === 'directory' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'">
              {{ selectedFileForInfo.type }}
            </span>
          </div>

          <div class="grid grid-cols-2 gap-6">
            <div class="col-span-2 bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ $t('Path') }}</h4>
              <p class="text-sm font-mono break-all">{{ selectedFileForInfo.path }}</p>
            </div>

            <div v-if="selectedFileForInfo.type === 'file'" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ $t('Size') }}</h4>
              <p class="text-base">{{ formatFileSize(selectedFileForInfo.size) }}</p>
            </div>

            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ $t('Last Modified') }}</h4>
              <p class="text-base">{{ selectedFileForInfo.last_modified || '-' }}</p>
            </div>

            <div v-if="selectedFileForInfo.mime_type" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ $t('MIME Type') }}</h4>
              <p class="text-base">{{ selectedFileForInfo.mime_type }}</p>
            </div>

            <div v-if="selectedFileForInfo.type === 'file'" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{{ $t('Type') }}</h4>
              <p class="text-base">{{ selectedFileForInfo.type }}</p>
            </div>
          </div>

          <div class="border-t pt-4">
            <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">{{ $t('Permissions') }}</h4>
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
              <div v-if="selectedFileForInfo.stat" class="grid grid-cols-2 gap-4">
                <div class="bg-white dark:bg-gray-700 p-3 rounded-md">
                  <span class="text-sm text-gray-500 dark:text-gray-400 block mb-1">{{ $t('Symbolic') }}</span>
                  <p class="font-mono text-lg">{{ modeToSymbolic(selectedFileForInfo.stat.mode) }}</p>
                </div>
                <div class="bg-white dark:bg-gray-700 p-3 rounded-md">
                  <span class="text-sm text-gray-500 dark:text-gray-400 block mb-1">{{ $t('Octal') }}</span>
                  <p class="font-mono text-lg">{{ formatPermissions(selectedFileForInfo.stat.mode) }}</p>
                </div>
              </div>
              <p v-else class="text-gray-500 dark:text-gray-400">{{ $t('No permission information available') }}</p>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-6 border-t pt-4">
            <div>
              <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">{{ $t('Owner') }}</h4>
              <div v-if="selectedFileForInfo.stat" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
                <div class="flex justify-between items-center mb-2 bg-white dark:bg-gray-700 p-3 rounded-md">
                  <span class="text-sm font-medium">{{$t('User ID') }}</span>
                  <span class="font-mono text-base">{{ selectedFileForInfo.stat.uid }}</span>
                </div>
                <div class="flex justify-between items-center bg-white dark:bg-gray-700 p-3 rounded-md">
                  <span class="text-sm font-medium">{{ $t('User Name') }}</span>
                  <span class="font-mono text-base">{{ selectedFileForInfo.stat.uid }}</span>
                </div>
              </div>
              <p v-else class="text-gray-500 dark:text-gray-400">{{$t('No owner information available') }}</p>
            </div>

            <div>
              <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">{{ $t('Group') }}</h4>
              <div v-if="selectedFileForInfo.stat" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
                <div class="flex justify-between items-center mb-2 bg-white dark:bg-gray-700 p-3 rounded-md">
                  <span class="text-sm font-medium">{{ $t('Group ID') }}</span>
                  <span class="font-mono text-base">{{ selectedFileForInfo.stat.gid }}</span>
                </div>
                <div class="flex justify-between items-center bg-white dark:bg-gray-700 p-3 rounded-md">
                  <span class="text-sm font-medium">{{ $t('Group Name') }}</span>
                  <span class="font-mono text-base">{{ selectedFileForInfo.stat.gid }}</span>
                </div>
              </div>
              <p v-else class="text-gray-500 dark:text-gray-400">{{ $t('No group information available') }}</p>
            </div>
          </div>

          <div v-if="selectedFileForInfo.stat" class="border-t pt-4">
            <h4 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">{{ $t('Timestamps') }}</h4>
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-md grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="bg-white dark:bg-gray-700 p-3 rounded-md">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400 block mb-1">{{ $t('Access Time') }}</span>
                <span class="block text-base">{{ selectedFileForInfo.last_modified }}</span>
              </div>
              <div class="bg-white dark:bg-gray-700 p-3 rounded-md">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400 block mb-1">{{ $t('Modify Time') }}</span>
                <span class="block text-base">{{ selectedFileForInfo.last_modified }}</span>
              </div>
              <div class="bg-white dark:bg-gray-700 p-3 rounded-md">
                <span class="text-sm font-medium text-gray-500 dark:text-gray-400 block mb-1">{{ $t('Change Time') }}</span>
                <span class="block text-base">{{ selectedFileForInfo.last_modified }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </SingleSite>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import SingleSite from '@/Pages/Site/SingleSite.vue'
import Btn from '@/Shared/Btn.vue'
import Modal from '@/Shared/Modal.vue'
import axios from 'axios'
import {usePage} from '@inertiajs/inertia-vue3'
import {useI18n} from 'vue-i18n'
import SftpFileActions from '@/Pages/Sftp/SftpFileActions.vue'
import Skeleton from "@/Shared/Table/Skeleton.vue";
import {VAceEditor} from "vue3-ace-editor";
import {useNavigationStore} from "@/stores/NavigationStore";
import 'ace-builds/src-noconflict/mode-php';
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/mode-css';
import 'ace-builds/src-noconflict/mode-json';
import 'ace-builds/src-noconflict/mode-text';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/theme-tomorrow_night_blue';
import {useFlash} from "@/Composables/useFlash";

const searchQuery = ref('');
const originalFiles = ref([]);
const navigation = useNavigationStore();

const { t } = useI18n()

const props = defineProps({
  server: Object,
  site: Object
})

const showFileActions = ref(false)

const openFileActions = ref(null)

const handleFileActions = (file) => {
  // Close any other open dropdowns first
  if (openFileActions.value && openFileActions.value !== file) {
    openFileActions.value = null
  }

  // Toggle the dropdown for the current file
  setTimeout(() => {
    openFileActions.value = openFileActions.value === file ? null : file
  }, 0)
}

const handleDropdownChange = (isOpen) => {
  if (!isOpen) {
    openFileActions.value = null
  }
}

// State
const page = usePage()
const serverId = props.server.id
const siteId = props.site.id

// File browser state
const currentPath = ref('/')
const files = ref([])
const loading = ref(true)
const showFileInfoModal = ref(false)
const selectedFileForInfo = ref(null)

// Upload state
const showUploadModal = ref(false)
const showDropDown = ref(false)
const fileInput = ref(null)
const selectedFile = ref(null)
const uploading = ref(false)
const uploadProgress = ref(0)

// Chunk upload state
const showChunkUploadModal = ref(false)
const chunkFileInput = ref(null)
const selectedChunkFile = ref(null)
const chunkUploading = ref(false)
const chunkUploadProgress = ref(0)
const chunkSize = 1024 * 1024 * 2 // 2MB chunks
const currentChunk = ref(0)
const totalChunks = ref(0)

// Create folder state
const showCreateFolderModal = ref(false)
const newFolderName = ref('')

// Rename state
const showRenameModalFlag = ref(false)
const fileToRename = ref(null)
const newName = ref('')

// Delete state
const fileToDelete = ref(null)

// Edit state
const showEditModal = ref(false)
const fileToEdit = ref(null)
const fileContent = ref('')
const isEditing = ref(false)
const editError = ref('')

// Computed properties
const pathParts = computed(() => {
  if (currentPath.value === '/') return []
    currentPath.value = currentPath.value.replace(`/var/www/${props.site?.name}`, '')
  return currentPath.value.split('/').filter(Boolean)
})

// Check if all files are selected
const allFilesSelected = computed(() => {
  if (files.value.length === 0) return false
  return files.value.every(file => file.selected)
})

// Toggle select all files
const toggleSelectAll = () => {
  const newState = !allFilesSelected.value
  files.value.forEach(file => {
    file.selected = newState
  })
}

const showRenameModal = computed({
  get: () => showRenameModalFlag.value,
  set: (value) => {
    showRenameModalFlag.value = value
    if (!value) {
      fileToRename.value = null
      newName.value = ''
    }
  }
})

// Methods for file navigation and display
const loadFiles = async () => {
  loading.value = true
  try {
    const response = await axios.get(`/server/${serverId}/site/${siteId}/sftp`, {
      params: {
        path: currentPath.value === '/' ? '/' : currentPath.value,
        recursive: false
      }
    })

    if (response.data.success) {
      // Add selected property to each file
      files.value = response.data.contents.map(file => ({
        ...file,
        selected: false
      }))
      originalFiles.value = [...files.value]
      loading.value = false
    } else {
      console.error('Failed to load files:', response.data.message)
    }
  } catch (error) {
    console.error('Error loading files:', error)
  } finally {
    loading.value = false
  }
}

const refreshFiles = () => {
  loadFiles()
}

const navigateTo = (file) => {
  currentPath.value = file.path;
  if (file.type === 'dir') {
      loadFiles()
  }
  if (file.type === 'file') {
      readFile(file.path)
  }

}

const navigateToRoot = () => {
  currentPath.value = '/'
  loadFiles()
}

const navigateToPath = (index) => {
  const parts = currentPath.value.split('/').filter(Boolean)
    currentPath.value = '/' + parts.slice(0, index + 1).join('/')
  loadFiles()
}

const readFile = async (path) => {
  try {
    const response = await axios.get(`/server/${serverId}/site/${siteId}/sftp/read`, {
      params: { path }
    })

    if (response.data.success) {
      return response.data.contents;
    } else {
      console.error('Failed to read file:', response.data.message)
      return null;
    }
  } catch (error) {
    console.error('Error reading file:', error)
    return null;
  }
}

const getFileName = (path) => {
  return path.split('/').pop()
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = 0

  while (bytes >= 1024 && i < units.length - 1) {
    bytes /= 1024
    i++
  }

  return `${bytes.toFixed(2)} ${units[i]}`
}

// Search functionality
const searchFiles = () => {
  if (!searchQuery.value) {
    files.value = [...originalFiles.value]
    return
  }

  const query = searchQuery.value.toLowerCase()
  // Filter files while preserving the selected property
  files.value = originalFiles.value.filter(file =>
    getFileName(file.path).toLowerCase().includes(query)
  )
}

const clearSearch = () => {
  searchQuery.value = ''
  // Reset files to original state while preserving selected property
  files.value = [...originalFiles.value]
}

// File upload methods
const handleFileChange = (event) => {
  selectedFile.value = event.target.files[0]
}

const uploadFile = async () => {
  if (!selectedFile.value) return

  uploading.value = true
  uploadProgress.value = 0

  const formData = new FormData()
  formData.append('file', selectedFile.value)
  formData.append('path', `${currentPath.value}${currentPath.value.endsWith('/') ? '' : '/'}${selectedFile.value.name}`)

  try {
    const response = await axios.post(
      `/server/${serverId}/site/${siteId}/sftp/upload`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        }
      }
    )

    if (response.data.success) {
      showUploadModal.value = false
      selectedFile.value = null
      if (fileInput.value) fileInput.value.value = ''
      loadFiles()
    } else {
      console.error('Failed to upload file:', response.data.message)
    }
  } catch (error) {
    console.error('Error uploading file:', error)
  } finally {
    uploading.value = false
  }
}

// Chunk upload methods
const handleChunkFileChange = (event) => {
  selectedChunkFile.value = event.target.files[0]
  if (selectedChunkFile.value) {
    totalChunks.value = Math.ceil(selectedChunkFile.value.size / chunkSize)
  }
}

const uploadFileInChunks = async () => {
  if (!selectedChunkFile.value) return

  chunkUploading.value = true
  chunkUploadProgress.value = 0
  currentChunk.value = 0

  const file = selectedChunkFile.value
  const fileSize = file.size
  totalChunks.value = Math.ceil(fileSize / chunkSize)

  const targetPath = `${currentPath.value}${currentPath.value.endsWith('/') ? '' : '/'}${file.name}`

  try {
    for (let chunkIndex = 0; chunkIndex < totalChunks.value; chunkIndex++) {
      currentChunk.value = chunkIndex + 1

      const start = chunkIndex * chunkSize
      const end = Math.min(fileSize, start + chunkSize)
      const chunk = file.slice(start, end)

      const formData = new FormData()
      formData.append('chunk', chunk)
      formData.append('path', targetPath)
      formData.append('chunk_index', chunkIndex)
      formData.append('total_chunks', totalChunks.value)
      formData.append('filename', file.name)
      formData.append('total_size', fileSize)
      formData.append('is_last_chunk', chunkIndex === totalChunks.value - 1)

      const response = await axios.post(
        `/server/${serverId}/site/${siteId}/sftp/upload-chunk`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      )

      if (!response.data.success) {
        throw new Error(response.data.message)
      }

      // Update progress
      chunkUploadProgress.value = Math.round(((chunkIndex + 1) * 100) / totalChunks.value)

      // If this was the last chunk and it's complete, we're done
      if (response.data.is_complete) {
        break
      }
    }

    // Upload completed successfully
    showChunkUploadModal.value = false
    selectedChunkFile.value = null
    if (chunkFileInput.value) chunkFileInput.value.value = ''
    loadFiles()

  } catch (error) {
    console.error('Error uploading file in chunks:', error)
  } finally {
    chunkUploading.value = false
  }
}

// Folder operations
const createFolder = async () => {
  if (!newFolderName.value) return

  try {
    const response = await axios.post(`/server/${serverId}/site/${siteId}/sftp/create-folder`, {
      path: `${currentPath.value}${currentPath.value.endsWith('/') ? '' : '/'}${newFolderName.value}`
    })

    if (response.data.success) {
      showCreateFolderModal.value = false
      newFolderName.value = ''
      loadFiles()
    } else {
      console.error('Failed to create folder:', response.data)
    }
  } catch (error) {
    console.error('Error creating folder:', error)
  }
}

// File editing methods
const openEditModal = async (file) => {
  fileToEdit.value = file;
  editError.value = '';
  fileContent.value = '';
  showEditModal.value = true;

  try {
    const content = await readFile(file.path);
    if (content !== null) {
      fileContent.value = content;
    } else {
      editError.value = 'Failed to load file content. The file may be too large or not a text file.';
    }
  } catch (error) {
    console.error('Error opening file for editing:', error);
    editError.value = 'An error occurred while loading the file.';
  }
};

const closeEditModal = () => {
  showEditModal.value = false;
  fileToEdit.value = null;
  fileContent.value = '';
  editError.value = '';
};

const saveFile = async () => {
  if (!fileToEdit.value) return;

  isEditing.value = true;
  editError.value = '';

  try {
    const response = await axios.post(`/server/${serverId}/site/${siteId}/sftp/write`, {
      path: fileToEdit.value.path,
      content: fileContent.value
    });

    if (response.data.success) {
      closeEditModal();
      loadFiles(); // Refresh the file list
    } else {
      editError.value = response.data.message || 'Failed to save file';
    }
  } catch (error) {
    console.error('Error saving file:', error);
    editError.value = 'An error occurred while saving the file.';
  } finally {
    isEditing.value = false;
  }
};

// File operations
const downloadFile = async (path) => {
  try {
    const response = await axios.get(`/server/${serverId}/site/${siteId}/sftp/download`, {
      params: { path },
      responseType: 'blob'
    })

    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', getFileName(path))
    document.body.appendChild(link)
    link.click()
    link.remove()
  } catch (error) {
    console.error('Error downloading file:', error)
  }
}

const openRenameModal = (file) => {
  fileToRename.value = file
  newName.value = getFileName(file.path)
  showRenameModalFlag.value = true
}

const closeRenameModal = () => {
  showRenameModalFlag.value = false
  fileToRename.value = null
  newName.value = ''
}

const renameFile = async () => {
  if (!fileToRename.value || !newName.value) return

  try {
    const response = await axios.post(`/server/${serverId}/site/${siteId}/sftp/rename`, {
      path: fileToRename.value.path,
      new_name: newName.value
    })

    if (response.data.success) {
      closeRenameModal()
      loadFiles()
    } else {
      console.error('Failed to rename:', response.data.message)
    }
  } catch (error) {
    console.error('Error renaming:', error)
  }
}

const deleteFile = async (file) => {
  if (!file) return
  useFlash().deleteConfirmation({
    title: t('Are you sure you want to delete this file?'),
    text: t('This action cannot be undone.'),
  }, async () => {
    try {
      const response = await axios.post(`/server/${serverId}/site/${siteId}/sftp/delete`, {
        path: file.path,
        type: file.type
      })

      if (response.data.success) {
        await loadFiles();
      } else {
        console.error('Failed to delete:', response.data.message)
      }
    } catch (error) {
      console.error('Error deleting:', error)
    }
  })
}

// File info methods
const showFileInfo = (file) => {
  selectedFileForInfo.value = file
  showFileInfoModal.value = true
}
function modeToSymbolic(mode) {
    // File type detection
    const fileType = {
        0o040000: 'd', // directory
        0o100000: '-', // regular file
        0o120000: 'l', // symlink
        0o060000: 'b', // block device
        0o020000: 'c', // character device
        0o010000: 'p', // FIFO
        0o140000: 's', // socket
    };

    let type = '-';
    for (const [bitmask, char] of Object.entries(fileType)) {
        if ((mode & 0o170000) === parseInt(bitmask)) {
            type = char;
            break;
        }
    }

    const perms = [
        (mode & 0o400) ? 'r' : '-',
        (mode & 0o200) ? 'w' : '-',
        (mode & 0o100) ? ((mode & 0o4000) ? 's' : 'x') : ((mode & 0o4000) ? 'S' : '-'),

        (mode & 0o040) ? 'r' : '-',
        (mode & 0o020) ? 'w' : '-',
        (mode & 0o010) ? ((mode & 0o2000) ? 's' : 'x') : ((mode & 0o2000) ? 'S' : '-'),

        (mode & 0o004) ? 'r' : '-',
        (mode & 0o002) ? 'w' : '-',
        (mode & 0o001) ? ((mode & 0o1000) ? 't' : 'x') : ((mode & 0o1000) ? 'T' : '-'),
    ];

    return type + perms.join('');
}
function formatPermissions(mode) {
    // Extract only the permission bits (lowest 3 octal digits)
    const octal = mode & 0o777; // mask off only the permission bits
    return '0' + octal.toString(8).padStart(3, '0'); // return as string like "0755"
}

// Set up CSRF token for axios requests
axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')

// Lifecycle hooks
onMounted(() => {
  loadFiles()

  // Add global click handler to close dropdown menus when clicking elsewhere
  document.addEventListener('click', (e) => {
    // If the click is not on a dropdown or its trigger, close all dropdowns
    if (openFileActions.value && !e.target.closest('.dropdown-menu') && !e.target.closest('.dropdown-trigger')) {
      openFileActions.value = null
    }
  })
})
</script>

<style scoped>
/* Checkbox styling */
input[type="checkbox"] {
  accent-color: #0BA961;
}

input[type="checkbox"]:checked {
  background-color: #0BA961;
  border-color: #0BA961;
}
</style>
