<template>
    <Progress v-bind="$props" @onDelete="onDeleteProgress" @reportIssue="onReportIssue"
              completed-message="Site deleted successfully"/>
    <DeleteSite :status="siteStatus" :server-id="serverId" :site-id="siteId" :site-name="siteName" :openDeleteModal="openDeleteModal"
                @closeModal="openDeleteModal=false"/>
</template>

<script setup>
import Progress from "@/Pages/Progress.vue";
import DeleteSite from "@/Pages/Site/Components/DeleteSite.vue";
import {ref} from "vue";
import Vapor from "laravel-vapor";


function onDeleteProgress() {
    openDeleteModal.value = true;
    console.log('delete progress');
}

function onReportIssue() {
    console.log('delete progress');
}

let props = defineProps({
    title: String,
    list: Object,
    status: Number,
    siteStatus: String,
    percentage: Number,
    statusMax: Number,
    exception: String,
    progressText: String,
    socketChannel: String,
    socketListenFor: String,
    progressData: Object,
    progressMessages: Object,
    additionalData: Object,
    animate_object: {
        type: String,
        default: Vapor.asset('img/progress/wp_site_install.svg')
    },
    serverId: Number,
    siteId: Number,
    siteName: String,
    retry: {
        type: Boolean,
        default: false
    },
    retryUrl: {
        type: String,
        default: ''
    },
});

let openDeleteModal = ref(false)
</script>


