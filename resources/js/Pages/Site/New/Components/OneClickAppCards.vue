<template>
  <div class="grid grid-cols-4 tablet:grid-cols-3 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
    <div
      v-for="app in oneClickApps"
      :key="app.id"
      @click.prevent="selectApp(app.slug)"
    >
        <div v-if="app.is_active"
             class="p-4 rounded-md transition-shadow border-1 border-solid cursor-pointer"
             :class="{ 
                'border-primary-light ring-1 ring-primary-light': selectedAppSlug === app.slug,
                'border-secondary-light dark:border-mode-focus-light': selectedAppSlug !== app.slug,
             }"
        >
            <div class="flex items-center mb-4">
                <img :src="asset(app.icon)" class="w-8 h-8 mr-2" :alt="app.name">
                <h4 class="text-lg font-medium text-dark dark:text-white">{{ app.name }}</h4>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-300 mb-4 h-12 overflow-hidden">{{ app.description }}</p>
                <button
                    class="w-full py-1.5 px-3 rounded-md text-center text-sm"
                    :class="selectedAppSlug === app.slug ? 'bg-primary-light text-white' : 'bg-gray-100 dark:bg-mode-base text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-mode-focus-light'"
                >
                    {{ selectedAppSlug === app.slug ? $t('Selected') : $t('Select') }}
                </button>
        </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { asset } from 'laravel-vapor';

const props = defineProps({
  oneClickApps: {
    type: Array,
    required: true
  },
  selectedAppSlug: {
    type: String,
    default: null
  }
});

const emit = defineEmits(['select-app']);

const selectApp = (appId) => {
  emit('select-app', appId);
};
</script>
