<template>
    <div
        class="flex flex-wrap mt-20px py-30px mobile:mt-15px gap-0.5 divide-x-1 divide-secondary-light dark:divide-dark border-1 border-secondary-light dark:border-dark rounded-10px">
        <div class="grow px-50px mobile:px-30px">
            <h5 class="text-sm text-secondary-full dark:text-mode-secondary-light">
                {{ $t('Certificate Issuer') }}
            </h5>
            <p class="text-sm mt-1 font-normal text-dark dark:text-white">
                {{ capitalizeFirstLetter(ssl_certificate.obtained_from) }}
            </p>
        </div>
        <div class="grow px-50px mobile:px-30px">
            <h5 class="text-sm text-secondary-full dark:text-mode-secondary-light">
                {{ $t('Expires On') }}
            </h5>
            <p class="text-sm mt-1 font-normal text-dark dark:text-white">
                {{ ssl_certificate.expires_at }}
                <span v-if="ssl_certificate.is_expired"
                    class="'text-danger bg-danger/50 hover:bg-danger hover:text-white text-dark dark:text-white text-sm mt-1 px-4 py-5px text-center font-normal rounded-3xl inline-flex shrink-0">
                    Expired
                </span>
            </p>
        </div>
        <div class="grow px-20px mobile:px-30px">
            <h5 class="text-sm text-secondary-full dark:text-mode-secondary-light">
                {{ $t('Status') }}
            </h5>
            <p :class="{
                    'text-success-full bg-success-full/10': ssl_certificate.status === 'obtained' || ssl_certificate.status === 'installed',
                    'text-danger bg-danger/50 hover:bg-danger hover:text-white': ssl_certificate.status === 'failed' || ssl_certificate.status === 'expired',
                    'text-dark dark:text-white': !['obtained', 'installed'].includes(ssl_certificate.status),
                }" class="text-sm mt-1 px-4 py-5px text-center font-normal rounded-3xl inline-flex shrink-0 capitalize">
                {{ ssl_certificate.status }}
            </p>

            <tooltip
                v-if="ssl_certificate.provider === 'xcloud' && (ssl_certificate.status === 'failed' || ssl_certificate.status === 'expired')"
                :title="$t('SSL renewal may fail due to DNS or server issues. Auto-retries in 12 hours. Contact support if needed.')">
                <span class="text-xs text-secondary-full dark:text-mode-secondary-light ml-2">
                    <i class="xcloud xc-info-2 text-xl"></i>
                </span>
            </tooltip>
        </div>
        <div class="grow px-50px mobile:px-30px">
            <h5 class="text-sm text-secondary-full dark:text-mode-secondary-light">
                {{ $t('Renew Date') }}
            </h5>
            <p class="text-sm mt-1 font-normal text-dark dark:text-white">
                {{ ssl_certificate.renew_date }}
            </p>
        </div>
    </div>

    <suggestion
        v-if="ssl_certificate.provider === 'xcloud' && (ssl_certificate.status === 'failed' || ssl_certificate.status === 'expired')"
        class="mt-5"
        :light-mode="true"
        :message="$t('SSL renewal may fail if the DNS is not correctly pointed to the domain or if the server is offline. The system will automatically retry in 12 hours. To resolve the issue faster, ensure your DNS settings are correct and your server is accessible. If the problem persists, contact support for assistance.')"
    />
</template>
<script setup>
import Tooltip from "@/Shared/Tooltip.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";

const {ssl_certificate} = defineProps({
    ssl_certificate: Object
})

const capitalizeFirstLetter = (string) => {
  if(!string) return string;
  return string.charAt(0).toUpperCase() + string.slice(1);
}
</script>
