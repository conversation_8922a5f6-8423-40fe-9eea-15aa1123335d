<template xmlns="http://www.w3.org/1999/html">
    <div class="w-full">
        <div v-if="form?.errors?.db_connection_error" class="mt-10px p-30px wide-mobile:p-20px flex items-center bg-warning/20 px-6 py-4 rounded-md">
            <img :src="asset('img/warning.svg')" alt="warning_img" class="w-6" />
            <p class="text-sm text-dark dark:text-white leading-7 pl-3.5">
                {{ $t('Could not connect to the database server. Please check your database credentials and try again.') }}
            </p>
        </div>
        <div
            v-if="form.database_provider === 'in_server'"
            class="p-30px wide-mobile:p-20px">
            <div
                class="grid grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px">
                <text-input
                    @keyup="$emit('updateUserVal', 'database_name')"
                    v-model="form.database_name"
                    :error="form.errors.database_name"
                    id="database_name"
                    :label="$t('Database Name')"
                    :placeholder="$t('Database Name')"/>
                <text-input
                    @keyup="$emit('updateUserVal', 'database_user')"
                    v-model="form.database_user"
                    :error="form.errors.database_user"
                    id="database_user"
                    :label="$t('Database Username')"
                    :placeholder="$t('Database Username')"/>
                <password-input
                    id="database_password"
                    v-model="form.database_password"
                    :error="form.errors.database_password"
                    autocomplete="password"
                    placeholder="********"
                    :label="$t('Database Password')"/>
            </div>
        </div>

        <div class="p-30px wide-mobile:p-20px"
            v-if="form.database_provider === 'from_provider'">
            <DigitalOcean v-if="server.cloudProvider.provider === 'digitalocean' || server.cloudProvider.provider === 'xcloud'"
                :form="form"
                :server="server"/>

        </div>

        <div
            v-if="form.database_provider === 'custom'"
            class="p-30px wide-mobile:p-20px">
            <div
                class="grid grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px">
                <text-input
                    v-model="form.database_host"
                    :error="form.errors.database_host"
                    id="database_host"
                    :label="$t('Database Host')"
                    :placeholder="$t('Database Host')"/>

                <text-input
                    v-model="form.database_port"
                    :error="form.errors.database_port"
                    id="database_port"
                    :label="$t('Database Port')"
                    :placeholder="$t('Database Port')"/>

                <text-input
                    @keyup="$emit('updateUserVal', 'database_user')"
                    v-model="form.database_user"
                    :error="form.errors.database_user"
                    id="database_user"
                    :label="$t('Database User')"
                    :placeholder="$t('Database User')"/>

                <text-input
                    @keyup="$emit('updateUserVal', 'database_name')"
                    v-model="form.database_name"
                    :error="form.errors.database_name"
                    id="database_name"
                    :label="$t('Database Name')"
                    :placeholder="$t('Database Name')"/>

                <password-input
                    id="database_password"
                    v-model="form.database_password"
                    :error="form.errors.database_password"
                    autocomplete="password"
                    placeholder="********"
                    :label="$t('Database Password')"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import DigitalOcean from "@/Pages/Site/New/Components/ProviderDatabase/DigitalOcean.vue";
import PasswordInput from "@/Shared/PasswordInput.vue";
import TextInput from "@/Shared/TextInput.vue";

defineProps({
    form: Object,
    server: Object
});
defineEmits(["updateUserVal"]);
</script>
