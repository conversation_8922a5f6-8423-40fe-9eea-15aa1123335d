<template>
    <div
        :class="{
            'grid-cols-3': form.needPrefix,
            'grid-cols-2': !form.needPrefix,
        }"
        class="grid tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px">

        <template v-if="!isMigrating">

            <text-input
                    v-model="form.admin_user"
                    :error="form.errors.admin_user"
                    id="admin_user"
                    :label="$t('Admin Username')"
                    :placeholder="$t('Admin Username')"/>

            <password-input
                    id="admin_password"
                    :label="$t('Admin Password')"
                    :error="form.errors.admin_password"
                    v-model="form.admin_password"
                    :show-password="true"
                    autocomplete="password"
                    placeholder="********"/>

            <text-input
                    v-model="form.admin_email"
                    :error="form.errors.admin_email"
                    id="admin_email"
                    :label="$t('Admin Email Address')"
                    placeholder="<EMAIL>"/>

            <select-input
                    v-model="form.wordpress_version"
                    :error="form.errors.wordpress_version"
                    id="wordpress_version"
                    :label="$t('WordPress Version')">
                <option v-for="wordPressVersion in availableWordPressVersions"
                        :key="wordPressVersion"
                        :value="wordPressVersion"
                        v-text="wordPressVersion"/>
            </select-input>

        </template>

        <select-input
                v-model="form.php_version"
                :error="form.errors.php_version"
                id="php_version"
                :label="$t('PHP Version')">
            <option v-for="phpVersion in availablePhpVersions"
                    :key="phpVersion"
                    :value="phpVersion"
                    v-text="phpVersion"
                    v-bind:selected="form.php_version === phpVersion"/>
        </select-input>

        <text-input
                v-if="form.needPrefix"
                v-model="form.prefix"
                :error="form.errors.prefix"
                id="prefix"
                :label="$t('Prefix')"
                :placeholder="$t('wp_')"/>

        <text-input
                @keyup="$emit('updateUserVal', 'site_user')"
                v-model="form.site_user"
                :error="form.errors.site_user"
                id="site_user"
                :label="$t('Site User')"
                :placeholder="$t('xcloud')"/>
    </div>
    <template
        v-if="site_type === 'wordpress'">

    <div v-if="show_caching_fields" class="mt-20px grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px">
        <div>
            <check-box-switch
                :label="$t('Full Page Caching')"
                v-model="form.enable_full_page_cache" id="enable_full_page_cache"/>
        </div>
        <div>
            <check-box-switch
                :label="$t('Redis Object Caching')"
                v-model="form.enable_redis_object_caching" id="enable_redis_object_cache"/>
        </div>
    </div>

    <!-- Site indexing -->
    <div
        class="rounded-md mt-6 px-8 pb-4"
        :class="{'bg-light dark:bg-mode-light' : !searchEngineVisibilityDarkMode, 'bg-light dark:bg-mode-base dark:divide-mode-light' : searchEngineVisibilityDarkMode}"
    >
      <h4 class="text-dark dark:text-white text-lg leading-none pt-4 mb-3">{{ $t('Search Engine Visibility') }}</h4>
      <label class="inline-flex mr-5">
        <input
            type="checkbox"
            class="hidden peer"
            v-model="form.disable_search_engine_visibility"
        />
        <span
            class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                        before:bg-transparent before:border-1 before:border-secondary-light
                        dark:border-mode-secondary-dark before:rounded before:mt-0.5
                        before:mr-2.5 before:text-xxxs before:inline-flex before:items-center
                        before:justify-center before:text-transparent before:outline-none
                        before:transition before:duration-200 peer-checked:before:border-success-full
                        peer-checked:before:bg-success-full peer-checked:before:text-white"
        >
            <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
                <span class="flex">
                  <span>{{ $t('Discourage search engines from indexing this site') }}</span>
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                  {{ $t('It is up to search engines to honor this request.') }}
                </span>
            </span>
          </span>
      </label>
    </div>
    </template>
    <template v-else>
        <text-input
            v-model="form.web_root"
            :error="form.errors.web_root"
            id="web_root"
            :label="$t('Web Root')"
            placeholder="e.g. public"
        />
    </template>
    <div v-if="show_deploy_script" class="mt-25px">
        <textarea-input
            v-model="form.deploy_script"
            :error="form.errors.deploy_script"
            id="deploy_script"
            :label="$t('Deploy Script (Optional)')"
            :placeholder="site_type === 'wordpress' ? 'wp plugin install woocommerce': 'composer install'"/>
    </div>

    <div
        class="bg-light dark:bg-mode-light rounded-md mt-30px">
        <RadioInputGroup :heading="$t('Database Management')">
            <RadioInput
                v-model="form.database_provider"
                value="in_server">{{ $t('Create Database In Server') }}
            </RadioInput>
<!--            not ready -->
<!--            <RadioInput-->
<!--                v-if="server?.cloudProvider?.provider === 'digitalocean' || server?.cloudProvider?.provider === 'do'"-->
<!--                v-model="form.database_provider"-->
<!--                value="from_provider">-->
<!--                Managed Database From Provider-->
<!--            </RadioInput>-->
            <RadioInput
                v-model="form.database_provider"
                value="custom">{{ $t('Add Your Existing Database') }}
            </RadioInput>
            <RadioInput
                v-if="show_no_database"
                v-model="form.database_provider"
                value="null">{{ $t('No Database') }}
            </RadioInput>
        </RadioInputGroup>
        <div
            class="bg-white dark:bg-mode-base divide-y-2 divide-light dark:divide-mode-light
                           border-2 border-light dark:border-mode-light rounded-b-md">
            <Database
                @updateUserVal="$emit('updateUserVal', $event)"
                :server="server" :form="form"></Database>
        </div>
    </div>
</template>

<script setup>
import TextInput from '@/Shared/TextInput.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import RadioInputGroup from '@/Shared/RadioInputGroup.vue'
import RadioInput from '@/Shared/RadioInput.vue'
import PasswordInput from '@/Shared/PasswordInput.vue'
import Database from '@/Pages/Site/New/Components/Database.vue'
import TextareaInput from "@/Shared/TextareaInput.vue";
import {onMounted, reactive, ref, watch} from "vue";
import Label from "@/Jetstream/Label.vue";
import CheckBoxSwitch from "@/Jetstream/CheckBoxSwitch.vue";

let _form = ref();

onMounted(() => {
    _form.value = reactive({...props.form});

    // Initialize web_root to 'public' for Laravel sites on component mount
    if (props.site_type === 'laravel' && !props.form.web_root) {
        props.form.web_root = 'public';
    }
});

watch(() => props.form.name, (name) => {
    _form.value = _form.value = reactive({...props.form});
});

watch(() => props.form.database_provider, (provider) => {
   //if provider is null then we don't need to do anything
    if (provider === 'null') {
        props.form.database_host = '';
        props.form.database_port = '';
        props.form.database_user = '';
        props.form.database_name = '';
        props.form.database_password = '';
    } else {
        props.form.database_host = _form.value.database_host;
        props.form.database_port = _form.value.database_port;
        props.form.database_user = _form.value.database_user;
        props.form.database_name = _form.value.database_name;
        props.form.database_password = _form.value.database_password;
    }
});
defineEmits(['updateUserVal']);
let props = defineProps({
    siteDefault: Object,
    form: Object,
    availablePhpVersions: Array,
    availableWordPressVersions: Array,
    isMigrating: false,
    server: Object,
    show_deploy_script: {
        type: Boolean,
        default: true
    },
    show_caching_fields: {
        type: Boolean,
        default: true
    },
    disableExistingDatabase: {
        type: Boolean,
        default: false
    },
    show_no_database: {
        type: Boolean,
        default: false
    },
    searchEngineVisibilityDarkMode: {
        type: Boolean,
        default: false
    },
    site_type: {
        type: String,
        default: 'wordpress'
    }
})

watch(() => props.form?.database_provider, (provider) => {
    if (provider === 'custom') {
        props.form.database_host = '';
        props.form.database_port = '';
        props.form.database_user = '';
        props.form.database_name = '';
        props.form.database_password = '';
    } else {
        props.form.database_host = _form.value.database_host;
        props.form.database_port = _form.value.database_port;
        props.form.database_user = _form.value.database_user;
        props.form.database_name = _form.value.database_name;
        props.form.database_password = _form.value.database_password;
    }
})

</script>
