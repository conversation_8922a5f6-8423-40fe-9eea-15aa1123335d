<template>
  <nav class="text-lg font-medium leading-[22px] bg-white dark:bg-mode-light mb-8 rounded-[4px] border-b border-[#e5e7eb] dark:border-[#313A6C]">
    <ul class="flex items-center flex-col sm:flex-row justify-center gap-4">
        <li
            v-for="(siteType, index) in siteTypes"
            :class="{
                'h-full relative after:absolute after:h-0.5 after:bottom-0 after:inset-x-0 after:bg-primary-light': activeTab === siteType.value
            }">
            <button
                :disabled="siteType.disabled"
                @click.prevent="selectTab(siteType)"
                class="px-5 py-3 sm:px-8 sm:py-6 header-menu-item-link inline-flex items-center gap-x-2 justify-center shadow-none h-full focus:outline-none focus:border-none font-medium text-base"
                :class="{
                    'text-dark dark:text-white': activeTab !== siteType.value,
                    '!text-primary-light': activeTab === siteType.value,
                    'cursor-not-allowed': siteType.disabled
                }"
            >
                <span class="inline-flex items-center justify-center w-6 aspect-square shrink-0 text-xl">
                    <i
                        :class="activeTab === siteType.value ? '!text-primary-light '+siteType.icon : 'text-secondary-full dark:text-mode-secondary-light '+siteType.icon"
                        class="text-2xl"></i>
                </span>
                {{$t(siteType.name)}}
                <!--comming soon-->
                <span v-if="siteType.disabled" class="p-1 px-2 rounded bg-light dark:bg-dark dark:text-white  text-xs text-primary-light  ml-1">{{$t('Coming Soon')}}</span>
            </button>
        </li>
    </ul>
  </nav>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  serverId: {
    type: [String, Number],
    required: true
  },
    siteTypes: {
        type: Object,
        required: true
    },
    defaultType: {
        type: String,
        required: true
    }
});

const selectType = defineEmits(['selectType']);


const activeTab = ref(props.defaultType ?? props.siteTypes[0].value);

const selectTab = (type) => {
    if (!type.disabled){
        activeTab.value = type.value;
        selectType('selectType', type.value);
    }
};
</script>
