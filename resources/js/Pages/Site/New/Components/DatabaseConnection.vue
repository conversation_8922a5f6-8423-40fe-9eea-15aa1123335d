<template>
  <div >
    <suggestion
        class="mt-3"
        :message="$t('Make sure to add your SSH key on the SSH Authentication section.')"
        :light-mode="true"
    />
    <div class="grid grid-cols-1">
        <div class="flex mt-20px mobile:mt-15px gap-0.5 flex-wrap">
            <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
              <h5 class="text-sm text-secondary-full">{{ $t('Connection URL') }}</h5>
              <a :href="databaseConnectionUrl"
                 class="text-sm mt-1 font-medium text-dark dark:text-white hover:underline break-all"
                 target="_blank"
              >
                {{ databaseConnectionUrlWithMaskedPassword }}
              </a>

              <CopyButton
                  :content="databaseConnectionUrl"
                  class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
              />
            </div>
        </div>
        <p class="text-secondary-full block text-base font-normal">
            {{ $t('Please use the database connection URL provided to establish a connection between your database client and the database. We suggest using TablePlus as your database client.') }}
        </p>
    </div>
  </div>
</template>

<script setup>
import CopyButton from "@/Shared/CopyButton.vue";
import {computed} from "vue";
import Suggestion from "@/Pages/Clone/Components/Suggestion.vue";

let props = defineProps({
  site: {
    type: Object,
    required: true
  },
  server: {
    type: Object,
    required: true
  },
  databaseInfo: {
    type: Array,
    required: true
  },
  sshAuthenticationMode: {
    type: String,
    required: true
  }
});

const maskedPassword = computed(() => {
  return props.databaseInfo.database_password.replace(/./g, '*'); // don't show the password
});

const maskedSshPassword = computed(() => {
  return props.databaseInfo.site_ssh_password.replace(/./g, '*'); // don't show the password
});

const databaseConnectionUrlWithMaskedPassword = computed( () => {
  if(props.sshAuthenticationMode === 'public_key'){
    return  'mysql+ssh://' + props.site.site_user + '@' + props.server.public_ip + '/' + props.databaseInfo.database_user + ':' + maskedPassword.value + '@127.0.0.1/' + props.databaseInfo.database_name  + '?name='+ props.databaseInfo.database_name + '&usePrivateKey=true';
  }else if(props.sshAuthenticationMode === 'password'){
    return  'mysql+ssh://' + props.site.site_user + ':' + maskedSshPassword.value + '@' + props.server.public_ip + '/' + props.databaseInfo.database_user + ':' + maskedPassword.value + '@127.0.0.1/' + props.databaseInfo.database_name  + '?name='+ props.databaseInfo.database_name + '&usePrivateKey=false';
  }
});

const databaseConnectionUrl = computed( () => {
  if(props.sshAuthenticationMode === 'public_key'){
    return  'mysql+ssh://' + props.site.site_user + '@' + props.server.public_ip + '/' + props.databaseInfo.database_user + ':' + props.databaseInfo.database_password + '@127.0.0.1/' + props.databaseInfo.database_name  + '?name='+ props.databaseInfo.database_name + '&usePrivateKey=true';
  }else if(props.sshAuthenticationMode === 'password'){
    return  'mysql+ssh://' + props.site.site_user + ':' + props.databaseInfo.site_ssh_password + '@' + props.server.public_ip + '/' + props.databaseInfo.database_user + ':' + props.databaseInfo.database_password + '@127.0.0.1/' + props.databaseInfo.database_name  + '?name='+ props.databaseInfo.database_name + '&usePrivateKey=false';
  }
});

</script>
