<template>
  <div class="mt-5 grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px wide-mobile:gap-4 mb-30px">
    <div
      :class="{
        'border-primary-light ring-1 ring-primary-light':
          form.domain_parking_method === 'go_live',
        'border-secondary-light dark:border-mode-focus-light':
          form.domain_parking_method !== 'go_live',
      }"
      class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
      @click="goLive()"
    >
      <div class="shrink-0 inline-flex items-center justify-center">
        <img
          :src="asset('img/png/live.png')"
          alt="xcloud_logo"
          class="w-12 h-auto"
        />
      </div>
      <div class="flex flex-col gap-2">
        <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
          {{ $t('Go Live') }}
        </h5>
        <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
          {{ $t('Get your site up and running for the world to see by simply pointing your domain to the server.') }}
        </p>
      </div>
    </div>
    <div
      :class="{
        'border-secondary-light dark:border-mode-focus-light':
          form.domain_parking_method !== 'staging_env',
        'border-primary-light ring-1 ring-primary-light':
          form.domain_parking_method === 'staging_env',
      }"
      class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
      @click="toggleStaging"
    >
      <div class="shrink-0 inline-flex items-center justify-center">
        <img
          :src="asset('img/png/staging2.png')"
          alt="xcloud_logo"
          class="w-12 h-auto"
        />
      </div>
      <div class="flex flex-col gap-2">
        <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
          {{ $t('Demo Site') }}
        </h5>
        <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
          {{ $t('Create a demo site with our test domain and customize before going live.') }}
        </p>
      </div>
    </div>
  </div>

  <suggestion
    v-if="form.domain_parking_method === 'staging_env'"
    :message="stagingHelper"
  />

  <div>
    <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md">
      <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
        {{
          form.domain_parking_method === "go_live"
            ? $t("Domain Setup")
            : $t("Demo Site Setup")
        }}
      </h3>
      <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px w-full">
        <div class="domain-selector" >
          <div>
            <div v-if="form.domain_parking_method !== 'staging_env'">
              <text-input
                v-model="form.name"
                :error="form.errors.name"
                :label="$t('Provide your Domain Name')"
                :placeholder="$t('Domain Name')"
                class="flex-1"
              />
            </div>

            <div v-if="form.domain_parking_method === 'staging_env'" class="relative w-full font-normal text-sm leading-[20px]">
              <text-input
                v-model="form.name"
                :error="form.errors.name"
                :label="$t('Domain Name')"
                :placeholder="$t('testing')"
                class="flex-1 bg-transparent-input"
              />

              <div class="absolute top-9 right-2"
                   @click="toggleDropdown"
                   ref="dropdownRef">
                <div
                  class="max-w-[117px] w-full px-3 z-50 py-2 bg-[#EDF2F8] dark:bg-[#1D2238] rounded-[4px] text-[#2A3268] dark:text-[#919DB9] flex items-center justify-between cursor-pointer"
                >
                  <div class="flex-1 min-w-0">
                    <Tooltip
                      :title="'.' + form.selected_staging_domain || 'Select a Domain'"
                      align="top"
                      color="primary">
                      <p class="truncate text-sm leading-5 max-w-[90px]">{{ '.' + form.selected_staging_domain || 'Select a Domain' }}</p>
                    </Tooltip>
                  </div>
                  <div
                    class="h-4 aspect-square shrink-0 text-xxxs flex items-center justify-end rounded cursor-pointer"
                  >
                    <i
                      :class="[
                        'xcloud xc-angle_down transition-transform duration-300 ',
                        isDropdownOpen ? 'rotate-180' : 'rotate-0',
                      ]"
                    ></i>
                  </div>
                </div>
              </div>

              <!-- Dropdown menu -->
              <div
                v-if="isDropdownOpen"
                class="absolute min-w-[130px] top-[72px] right-0 z-10 bg-white dark:bg-[#171A30] border border-[#C1C5DE] dark:border-[#313A6C] rounded-[4px] text-[#919DB9] dark:text-[#919DB9] flex flex-col items-center gap-[12px] cursor-pointer p-[10px] whitespace-nowrap overflow-x-scroll sm:overflow-hidden text-ellipsis"
                style="max-width: 100%;"
              >
                <ul class="flex flex-col mx-auto">
                  <li
                    v-for="domain in availableDomains"
                    :key="domain"
                    @click="selectDomain(domain)"
                    class="p-1 cursor-pointer hover:text-[#2A3268] dark:hover:text-[#FFFFFF]"
                  >
                    {{ '.' + domain }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <AdditionalDomain
          v-if="form.domain_parking_method === 'go_live' && showAdditionalDomain"
          :is_disable="form.enable_multisite && form.multisite_subdomain"
          :form="form"
        />
      </div>

      <div class="mt-3"
        v-if="showCloudflareDnsOption && form.domain_parking_method === 'go_live' && hasCloudflareIntegration"
      >
        <label class="inline-flex mr-5">
          <input
            type="checkbox"
            class="hidden peer"
            :disabled="!hasCloudflareIntegration"
            v-model="useCloudflareDns"
            :class="{
              'opacity-50 cursor-not-allowed': !hasCloudflareIntegration,
            }"
          />
          <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                      before:bg-transparent before:border-1 before:border-secondary-light
                      dark:border-mode-secondary-dark before:rounded before:mt-0.5
                      before:mr-2.5 before:text-xxxs before:inline-flex before:items-center
                      before:justify-center before:text-transparent before:outline-none
                      before:transition before:duration-200 peer-checked:before:border-success-full
                      peer-checked:before:bg-success-full peer-checked:before:text-white"
          >
            <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
              <span
                class="flex"
                :class="{
                  'opacity-50 cursor-not-allowed': !hasCloudflareIntegration,
                }"
              >
                <span>
                  {{ $t('Add DNS and SSL Certificate on Cloudflare') }}
                </span>
                <img :src="
                  asset(
                    'img/CF_logomark.png'
                  )"
                  alt="cloudflare logo"
                  class="ml-1 w-8 h-4"
                />
                <span>&nbsp;{{ $t('(Optional)') }}</span>
              </span>
              <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                {{ $t('Integrate Cloudflare forAutomatic DNS and SSL management.') }} (<Link
                  class="underline"
                  :href="
                    route(
                      'user.integration.cloudflare'
                    )
                  "
                  v-text="
                    'Manage your Cloudflare Integration'
                  "
                />)
              </span>
            </span>
          </span>
        </label>

        <suggestion
          class="mt-5"
          v-if="hasCloudflareIntegration && form.domain_active_on_cloudflare && hasAdditionalDomains"
          :message="$t('If you\'re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.')"
          :light-mode="true"
        >
        </suggestion>
      </div>
    </div>

    <div
      v-if="
        !loader && !isIpSite &&
        form.domain_parking_method === 'go_live' &&
        showDnsHttps
      "
      class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md"
    >
      <div v-if="showDomainSetup">
        <DNS :form="form" :server="server" />
        <Https :form="form" :server="server" :disable-https="form.multisite_subdomain" />

        <suggestion
          v-if="form.multisite_subdomain"
          class="col-span-2 mt-5"
          :message="$t('You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.')"
          :light-mode="true"
        />
      </div>

      <div v-else>
        <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
          {{ $t('DNS Setup') }}
        </h3>
        <div class="grid grid-cols-1 w-full">
          <suggestion
            class="mt-3"
            type="success"
            :message="dnsHelpText"
            :light-mode="true"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- No indexing -->
  <div
    v-if="showIndexingOption && form.domain_parking_method === 'staging_env'"
    class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md"
  >
    <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
      {{ $t('Turn off Indexing') }}
    </h3>
    <div class="grid grid-cols-1 w-full">
      <p class="text-secondary-full block text-base font-normal">
        {{ $t('Turning on this setting will prevent search engines from indexing your staging site.') }}
      </p>
      <div class="mt-20px grid grid-cols-2 gap-30px">
        <check-box-switch
          label="Turn off Indexing"
          v-model="form.turn_off_indexing"
          id="turn_off_staging_indexing"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { usePage } from "@inertiajs/inertia-vue3";
import { asset } from "laravel-vapor";
import { userClickOutside } from "@/Shared/Events/userClickOutside";
import { Inertia } from "@inertiajs/inertia";
import { useFlash } from "@/Composables/useFlash";

import TextInput from "@/Shared/TextInput.vue";
import AdditionalDomain from "@/Pages/Site/New/Components/AdditionalDomain.vue";
import DNS from "@/Pages/Site/New/Components/DNS.vue";
import Https from "@/Pages/Site/New/Components/Https.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import CheckBoxSwitch from "@/Jetstream/CheckBoxSwitch.vue";
import { Link } from "@inertiajs/inertia-vue3";

const { t } = useI18n();

const props = defineProps({
  form: {
    type: Object,
    required: true
  },
  server: {
    type: Object,
    required: true
  },
  availableDomains: {
    type: Array,
    required: true
  },
  hasCloudflareIntegration: {
    type: Boolean,
    default: false
  },
  showAdditionalDomain: {
    type: Boolean,
    default: true
  },
  showIndexingOption: {
    type: Boolean,
    default: true
  },
  showDnsHttps: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:form', 'domain-change']);

const dropdownRef = ref(null);
const isDropdownOpen = ref(false);
const showDomainSetup = ref(true);
const loader = ref(false);
const useCloudflareDns = ref(false);

// Computed properties
const isIpSite = computed(() => {
  return props.form.name.trim() === props.server.public_ip || props.form.name.trim().match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/);
});

const showCloudflareDnsOption = computed(() => {
  return !isIpSite.value;
});

const hasAdditionalDomains = computed(() => {
  return props.form.additional_domains && props.form.additional_domains.length > 0 &&
         props.form.additional_domains.some(domain => domain.value && domain.value.trim() !== '');
});

const brandName = usePage().props.value?.current_white_label ? usePage().props.value?.current_white_label?.branding?.brand_name : 'xCloud';
const stagingHelper = brandName + t(' offers a temporary test domain that allows you to quickly deploy your site. ') +
  t('This temporary domain enables you to share your work in progress with teammates or clients for review ') +
  t('and input before you finalize and launch it with your own custom domain for public access.');

const dnsHelpText = computed(() => {
  const brandName = Inertia.page.props?.current_white_label
    ? Inertia.page.props.current_white_label.branding.brand_name
    : 'xCloud';
  return `Your DNS setup and SSL Certificate will be done by Cloudflare and managed by ${brandName}.`;
});

// Methods
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};

const selectDomain = (domain) => {
  isDropdownOpen.value = false;
  props.form.selected_staging_domain = domain;
  checkStagingDomain();
  emit('update:form', props.form);
};

const goLive = () => {
  props.form.domain_parking_method = 'go_live';
  props.form.disable_search_engine_visibility = false;
  emit('update:form', props.form);
  emit('domain-change', 'go_live');
};

const toggleStaging = () => {
  props.form.domain_parking_method = 'staging_env';
  props.form.disable_search_engine_visibility = true;
  emit('update:form', props.form);
  emit('domain-change', 'staging_env');
};

const checkStagingDomain = () => {
  if (props.form.domain_parking_method === "staging_env" && props.form.name) {
    if (props.form.name.includes('.')) {
      props.form.errors.name = "Staging/demo domains can not contain multi level domain.";
      return;
    }

    props.form.processing = true;
    axios
      .get(route("api.site.check-domain-exists") + "?domain=" + props.form.name + "." + props.form.selected_staging_domain)
      .then((res) => {
        if (res.data.exists) {
          props.form.errors.name =
            "Looks like the domain is already in use. Please try changing the site title again.";
        } else {
          props.form.errors.name = "";
        }
        props.form.processing = false;
      })
      .catch((err) => {
        props.form.processing = false;
        useFlash().error(err.errors?.domain?.[0] || "An error occurred");
      });
  }
};

const resetCloudflareIntegration = () => {
  useCloudflareDns.value = false;
  props.form.domain_active_on_cloudflare = false;
  props.form.cloudflare_account_id = '';
  props.form.cloudflare_zone_id = '';
  props.form.subdomain = '';
  props.form.site_name = '';
};

// Watch for changes
watch(
  () => props.form.name,
  () => {
    showCloudflareDnsOption.value = !isIpSite.value;
  }
);

watch(
  () => props.form.additional_domains,
  () => {
    for (let i = 0; i < props.form.additional_domains.length; i++) {
      if (props.form.additional_domains[i].value !== "") {
        props.form.additional_domains[i].value = props.form.additional_domains[i]?.value.toLowerCase();
        resetCloudflareIntegration();
        showDomainSetup.value = true;
      }
    }
  },
  { deep: true }
);

watch(
  () => useCloudflareDns.value,
  (newVal) => {
    if (newVal) {
      checkDomain();
    } else {
      resetCloudflareIntegration();
      showDomainSetup.value = true;
    }
  }
);

const checkDomain = async () => {
  try {
    loader.value = true;
    showDomainSetup.value = false;

    const res = await axios.get(route("api.user.integration.cloudflare.check-domain-exists", [props.form.name]));

    props.form.domain_active_on_cloudflare = res.data.response.domain_active_on_cloudflare;
    props.form.cloudflare_account_id = res.data.response.account_id;
    props.form.cloudflare_zone_id = res.data.response.zone_id;
    props.form.subdomain = res.data.response.subdomain;
    props.form.site_name = res.data.response.site_name;

    loader.value = false;

    if (!res.data.response.domain_active_on_cloudflare) {
      useFlash().error('No cloudflare account found for this domain. Please add your domain to cloudflare first.');
    }
  } catch (err) {
    showDomainSetup.value = true;
    loader.value = false;
    props.form.processing = false;
    if (err.response && err.response.data && err.response.data.message) {
      useFlash().error(err.response.data.message);
    } else {
      useFlash().error('An error occurred while processing your request.');
    }
  }
};

// Click outside to close dropdown
userClickOutside(dropdownRef, () => {
  isDropdownOpen.value = false;
});
</script>
