<template>
    <Head :title="$t('Install One Click App')" />

    <div class="xc-container">
        <div
            class="flex-1 flex items-center"
            :class="
                navigation.isOpenHelpbar(helper.isOpen)
                    ? 'mr-400px small-laptop:mr-0'
                    : 'mr-0'
            "
        >
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark
                              dark:text-white mb-50px wide-mobile:text-3xl mobile:text-28px tablet:mb-40px wide-mobile:mb-30px"
                    >
                        <span>{{ $t('Install One Click App Into') }}</span>
                        <cloud-provider-logo
                            :provider-name="
                                server?.cloud_provider_name ??
                                server?.cloud_provider_readable
                            "
                            class="w-40px inline-flex mx-2.5"
                        />
                        <Link
                            :href="route('server.show', server.id)"
                            class="text-primary-light hover:text-primary-dark dark:hover:text-primary-light"
                        >
                            {{ server.name }}
                        </Link>
                    </h2>

                    <form @submit.prevent="submit">
                        <div class="mb-30px tablet:mb-25px wide-mobile:mb-20px">
                            <h3 class="text-xl font-medium text-dark dark:text-white mb-30px tablet:mb-25px wide-mobile:mb-20px">
                                {{ $t('Select an Application') }}
                            </h3>
                            <OneClickAppCards
                                :oneClickApps="oneClickApps"
                                :selectedAppSlug="form.app_slug"
                                @select-app="selectApp"
                            />
                            <Error :error="form.errors.app_slug" />
                        </div>

                        <div class="mb-30px tablet:mb-25px wide-mobile:mb-20px">
                            <h3 class="text-xl font-medium text-dark dark:text-white mb-30px tablet:mb-25px wide-mobile:mb-20px">
                                {{ $t('Site Details') }}
                            </h3>
                            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                                <text-input
                                    @keyup="checkStagingDomain"
                                    v-model="form.title"
                                    :error="form.errors.title"
                                    id="title"
                                    :label="$t('Site Title')"
                                    :placeholder="$t('Site Title')"
                                />
                                <div>
                                    <label
                                        class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none"
                                        for="tags"
                                    >
                                        {{ $t('Add Tag (optional)') }}
                                    </label>
                                    <multiselect
                                        class="!ml-0"
                                        id="tags"
                                        :class="{
                                            'ring-1 ring-danger-light':
                                                tagsErrors(),
                                        }"
                                        v-model="form.tags"
                                        mode="tags"
                                        :close-on-select="false"
                                        :searchable="true"
                                        :create-option="true"
                                        @select="onTagSelect"
                                        :placeholder="$t('Select or create tags')"
                                        :options="tagList"
                                        regex="^[a-zA-Z0-9_ -]+$"
                                   />
                                    <Error :error="tagsErrors()" />
                                </div>
                            </div>
                        </div>

                        <div class="mb-30px tablet:mb-25px wide-mobile:mb-20px">
                            <h3 class="text-xl font-medium text-dark dark:text-white mb-30px tablet:mb-25px wide-mobile:mb-20px">
                                {{ $t('Domain Setup') }}
                            </h3>
                            <DomainSetup
                                :form="form"
                                :server="server"
                                :availableDomains="availableDomains"
                                :hasCloudflareIntegration="hasCloudflareIntegration"
                                :showIndexingOption="false"
                                :showAdditionalDomain="false"
                                :showDnsHttps="true"
                                @update:form="updateFormFromDomainSetup"
                                @domain-change="handleDomainChange"
                            />
                        </div>

                        <div class="grid tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px grid-cols-2">
                            <select-input
                                v-if="form.app_slug !== 'n8n' && form.app_slug !== 'uptime-kuma'"
                                v-model="form.php_version"
                                :error="form.errors.php_version"
                                id="php_version"
                                :label="$t('PHP Version')">
                                <option v-for="phpVersion in availablePhpVersions"
                                        :key="phpVersion"
                                        :value="phpVersion"
                                        v-text="phpVersion"
                                        v-bind:selected="form.php_version === phpVersion"/>
                            </select-input>

                            <text-input
                                v-if="form.app_slug === 'n8n' || form.app_slug === 'uptime-kuma'"
                                v-model="form.port"
                                :error="form.errors.port"
                                id="port"
                                type="number"
                                :label="$t('Port')"
                                :placeholder="5678"/>

                            <text-input
                                @keyup="$emit('updateUserVal', 'site_user')"
                                v-model="form.site_user"
                                :error="form.errors.site_user"
                                id="site_user"
                                :label="$t('Site User')"
                                :placeholder="$t('xcloud')"/>
                        </div>

                        <!-- Admin User Section for Mautic -->
                        <div class="mt-30px tablet:mb-25px wide-mobile:mb-20px" v-if="form.app_slug === 'mautic'">
                            <h3 class="text-xl font-medium text-dark dark:text-white mb-30px tablet:mb-25px wide-mobile:mb-20px">
                                {{ $t('Admin User') }}
                            </h3>
                            <div class="grid grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                                <text-input
                                    v-model="form.admin_user"
                                    :error="form.errors.admin_user"
                                    id="admin_user"
                                    :label="$t('Admin Username')"
                                    placeholder="admin"/>

                                <password-input
                                    id="admin_password"
                                    :label="$t('Admin Password')"
                                    :error="form.errors.admin_password"
                                    v-model="form.admin_password"
                                    :show-password="true"
                                    autocomplete="password"
                                    placeholder="Secret123!"/>

                                <text-input
                                    v-model="form.admin_email"
                                    :error="form.errors.admin_email"
                                    id="admin_email"
                                    :label="$t('Admin Email Address')"
                                    placeholder="<EMAIL>"/>
                            </div>

                            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                                <p class="text-sm text-blue-700 dark:text-blue-300">
                                    <i class="xcloud xc-info-circle mr-2"></i>
                                    {{ $t('Please make sure to copy your admin password. You will not be able to see it again after installation.') }}
                                </p>
                            </div>
                        </div>

                        <!-- Database Section -->
                        <div class="mt-30px tablet:mb-25px wide-mobile:mb-20px" v-if="form.app_slug === 'n8n' || form.app_slug === 'uptime-kuma' || form.app_slug === 'mautic'">
                            <h3 class="text-xl font-medium text-dark dark:text-white mb-30px tablet:mb-25px wide-mobile:mb-20px">
                                {{ $t('Database') }}
                            </h3>
                            <div class="bg-light dark:bg-mode-light rounded-md">
                                <RadioInputGroup :heading="$t('Database Management')">
                                    <RadioInput
                                        v-model="form.database_provider"
                                        value="in_server">{{ $t('Create Database In Server') }}
                                    </RadioInput>
                                    <RadioInput
                                        v-model="form.database_provider"
                                        value="custom">{{ $t('Add Your Existing Database') }}
                                    </RadioInput>
                                </RadioInputGroup>
                                <div class="bg-white dark:bg-mode-base divide-y-2 divide-light dark:divide-mode-light
                                           border-2 border-light dark:border-mode-light rounded-b-md">
                                    <Database
                                        @updateUserVal="updateUserFields"
                                        :server="server" :form="form"></Database>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <WizardProgress
                    :processing="form.processing"
                    :back="route('site.create', server.id)"
                    @next="submit"
                    :progress-width="progressWidth(1, 1)"
                    progress-text="Step 1 of 1"
                >
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                    </template>
                    <span>{{ $t('Next') }}</span>
                </WizardProgress>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useNameGenerator } from "@/Composables/useNameGenerator";
import AdvancedFields from "@/Pages/Site/New/Components/Advanced.vue";
import Advanced from "@/Shared/Advanced.vue";
import TextInput from "@/Shared/TextInput.vue";
import WizardProgress from "@/Shared/WizardProgress.vue";
import { useHelpStore } from "@/stores/HelpStore";
import { useNavigationStore } from "@/stores/NavigationStore";
import { useForm, usePage } from "@inertiajs/inertia-vue3";
import Multiselect from "@vueform/multiselect";
import { computed, onMounted, ref, watch } from "vue";
import { useFlash } from "@/Composables/useFlash";
import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
import Error from "@/Shared/Error.vue";
import { Inertia } from "@inertiajs/inertia";
import { useI18n } from "vue-i18n";
import { asset } from "laravel-vapor";
import OneClickAppCards from "@/Pages/Site/New/Components/OneClickAppCards.vue";
import DomainSetup from "@/Pages/Site/New/Components/DomainSetup.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import Database from "@/Pages/Site/New/Components/Database.vue";
import RadioInputGroup from "@/Shared/RadioInputGroup.vue";
import RadioInput from "@/Shared/RadioInput.vue";
import PasswordInput from "@/Shared/PasswordInput.vue";

const { t } = useI18n();
let navigation = useNavigationStore();
let helper = useHelpStore()
let userUpdateFields = [];
// const { generateRandomString } = useNameGenerator();

let props = defineProps({
    server: Object,
    siteDefault: Object,
    availablePhpVersions: Array,
    tags: Array,
    prefix_id: [String, Number],
    hasCloudflareIntegration: Boolean,
    staging_domain: {
        type: String,
        default: "x-cloud.app",
    },
    oneClickApps: Array,
    availableDomains: {
        type: Array,
        required: true,
    },
    selectedAppSlug: {
        type: String,
        default: null
    }
});

console.log(props.selectedAppSlug)

let form = useForm({
    app_slug: props.selectedAppSlug || "",
    name: "",
    title: "",
    type: "oneclick",
    additional_domains: [],
    tags: props.server.tags.map((tag) => tag.name),
    domain_parking_method: "staging_env",
    selected_staging_domain: props.availableDomains[0]?.value || props.staging_domain,
    ssl_provider: "",
    ssl_certificate: "",
    ssl_private_key: "",
    site_user: props.siteDefault.site_user,
    php_version: props.siteDefault.php_version,
    port: props.siteDefault?.port ? props.siteDefault?.port : 5678,

    // Database fields
    database_provider: props.siteDefault.database_provider,
    database_name: props.siteDefault.database_name,
    database_user: props.siteDefault.database_user,
    database_password: props.siteDefault.database_password,
    database_host: props.siteDefault.database_host,
    database_port: props.siteDefault.database_port,

    // Admin user fields
    admin_user: props.siteDefault.admin_user,
    admin_password: props.siteDefault.admin_password,
    admin_email: props.siteDefault.admin_email,
});

const domainParkingMethod = computed(() => form.domain_parking_method);

const tagList = computed(() => {
    return props.tags.map((tag) => ({
        label:
            tag?.counter > 1 && !form.tags.includes(tag.value)
                ? tag.label + " (" + tag?.counter + ")"
                : tag.label,
        value: tag.value,
    }));
});

const selectApp = (appId) => {
    form.app_slug = appId;
};

const onTagSelect = (tag) => {
    if (form.tags.length > 5) {
        form.tags.pop();
    }
};

const maybeRemoveHttp = () => {
    if (form.name.startsWith("http://")) {
        form.name = form.name.substring(7);
    } else if (form.name.startsWith("https://")) {
        form.name = form.name.substring(8);
    }
};

let submit = () => {
    maybeRemoveHttp();
    if (domainParkingMethod.value === "staging_env" && form.name === "") {
        form.name = generateRandomString();
    }

    form
        .transform((data) => {
            if(form.domain_parking_method === 'staging_env'){
                data.name = form.name + '.' + form.selected_staging_domain
            }
            return data;
        })
        .post(route("api.site.store-oneclick", props.server.id), {
            preserveScroll: true,
        });
};

const showAdvanced = (isShow) => {
    helper.toggleAccordions([], [form.database_provider]);
};

const updateUserFields = (field) => {
    if (!userUpdateFields.includes(field)) {
        userUpdateFields.push(field);
    }

    if (field === 'site_user') {
        form.site_user = form.site_user;
    } else if (field === 'database_name') {
        form.database_name = form.database_name;
    } else if (field === 'database_user') {
        form.database_user = form.database_user;
    }
};

const progressWidth = (current, total) => {
    return (current / total) * 100 + "%";
};

const updateFormFromDomainSetup = (updatedForm) => {
    // Update any form properties that might have changed in the DomainSetup component
    form.name = updatedForm.name;
    form.domain_parking_method = updatedForm.domain_parking_method;
    form.selected_staging_domain = updatedForm.selected_staging_domain;
};

const handleDomainChange = (method) => {
    if (method === 'go_live') {
        form.name = "";
    }
};

const goLive = () => {
    form.domain_parking_method = "go_live";
    form.name = "";
};

const tagsErrors = function () {
    const errors = [];

    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
};

let checkStagingDomain = () => {
    // This function is now handled by the DomainSetup component
};

watch(() => form.title, (newVal) => {
    if (newVal && domainParkingMethod.value === "staging_env") {
        form.name = newVal.toLowerCase().replace(/[^a-z0-9]/g, "");
    }
});
const generateRandomString = (length = 6) => {
    const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(
            Math.floor(Math.random() * charactersLength)
        );
    }
    return result;
};
watch(
    () => form.title,
    () => {
        if (form.domain_parking_method === "staging_env") {
            const generated_name = useNameGenerator(form.title);
            form.site_user =
                "u" +
                props.prefix_id +
                "_" +
                useNameGenerator(
                    form.title,
                    16 - ("u" + props.prefix_id + "_").length
                );

            if (form.title) {
                let additional = "." + props.staging_domain;
                let form_name = generated_name.replace(/[^a-zA-Z0-9-]/g, "");
                form.name =
                    form_name === ""
                        ? ""
                        : form_name +
                        "-" +
                        generateRandomString(4).toLocaleLowerCase()
            } else {
                form.name = "";
            }

            if (form.database_provider === "in_server" || form.database_provider === "from_provider") {
                form.database_name = "db" + props.prefix_id + "_" + generated_name;
                form.database_user = "u" + props.prefix_id + "_" + generated_name;
            }
        }
    }
);

watch(
    () => form.name,
    () => {
        maybeRemoveHttp();
        form.name = form.name.toLowerCase();
        const generated_name = useNameGenerator(form.name);

        if (!userUpdateFields.includes("site_user") || form.site_user.length === 0) {
            form.site_user =
                "u" +
                props.prefix_id +
                "_" +
                useNameGenerator(
                    form.name,
                    16 - ("u" + props.prefix_id + "_").length
                );
        }

        if (form.database_provider === "in_server" || form.database_provider === "from_provider") {
            if (!userUpdateFields.includes("database_name") || form.database_name.length === 0) {
                form.database_name = "db" + props.prefix_id + "_" + generated_name;
            }
            if (!userUpdateFields.includes("database_user") || form.database_user.length === 0) {
                form.database_user = "u" + props.prefix_id + "_" + generated_name;
            }
        }
    }
);

</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
</style>
