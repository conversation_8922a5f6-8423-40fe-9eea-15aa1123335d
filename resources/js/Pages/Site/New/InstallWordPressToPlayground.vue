<template>
    <Head title="Install New WordPress Website" />

    <div class="xc-container">
        <div
            class="flex-1 flex items-center"
            :class="
                navigation.isOpenHelpbar(helper.isOpen)
                    ? 'mr-400px small-laptop:mr-0'
                    : 'mr-0'
            "
        >
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark dark:text-white mb-50px wide-mobile:text-3xl mobile:text-28px tablet:mb-40px wide-mobile:mb-30px">
                        <span>{{ $t('Create Your New Site Into') }}</span>
                        <cloud-provider-logo
                            :provider-name="server?.provider_name ?? server?.cloud_provider_readable"
                            class="w-40px inline-flex mx-2.5"
                        />
                        <Link class="font-medium text-primary-dark">{{ $t('xCloud Playground') }}</Link>
                    </h2>

                    <form @submit.prevent="submit">
                        <button type="submit" class="hidden">{{ $t('Submit') }}</button>

                        <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
<!--                            @input-blur="checkDomain"-->
                            <text-input
                                @keyup="checkDomain"
                                v-model="form.title"
                                :error="form.errors.title"
                                id="title"
                                :label="$t('Site Title')"
                                :placeholder="$t('Site Title')"
                            />
                            <div>
                                <label class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none" for="tags">
                                    {{ $t('Add Tag (optional)') }}
                                </label>
                                <multiselect
                                    class="!ml-0"
                                    id="tags"
                                    :class="{ 'ring-1 ring-danger-light': tagsErrors()}"
                                    v-model="form.tags"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    @select="onTagSelect"
                                    :placeholder="$t('Select or create tags')"
                                    regex="^[a-zA-Z0-9_ -]+$"
                                    :options="tagList"
                                />
                                <Error :error="tagsErrors()"/>
                            </div>
                        </div>
                        <div class="mt-5 grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px wide-mobile:gap-4 mb-30px">
                            <div
                                :class="{
                                    'border-secondary-light dark:border-mode-focus-light': form.domain_parking_method !== 'staging_env',
                                    'border-primary-light ring-1 ring-primary-light': form.domain_parking_method === 'staging_env',
                                }"
                                class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                                @click="toggleStaging"
                            >
                                <div class="shrink-0 inline-flex items-center justify-center">
                                    <img
                                        :src="asset('img/png/staging2.png')"
                                        alt="xcloud_logo"
                                        class="w-12 h-auto"
                                    />
                                </div>
                                <div class="flex flex-col gap-2">
                                    <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                        {{ $t('Playground Environment') }}
                                    </h5>
                                    <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                        {{ $t('This demo site will expire 24 hours after creation.') }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <suggestion
                            v-if="form.domain_parking_method === 'staging_env'"
                            :message="stagingHelper"
                        />

                        <div>
                            <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light
                                  dark:border-mode-base dark:bg-mode-base rounded-md">
                                <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                   {{ $t('Demo Site Setup') }}
                                </h3>
                                <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px w-full">
                                    <text-input
                                        :disabled="domainParkingMethod === 'staging_env'"
                                        v-model="form.name"
                                        :error="form.errors.name"
                                        id="name"
                                        :label="$t('This will be auto-generated according to your site title')"
                                        :placeholder="$t('Domain Name')"
                                    />
                                </div>
                            </div>

                            <Skeleton v-if="loader" class="mt-30px" />
                        </div>
                        <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                                      dark:bg-mode-base rounded-md">
                            <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                {{ $t('Enhancing Website Performance') }}
                            </h3>
                            <div class="grid grid-cols-1 w-full">
                                <p class="text-secondary-full block text-base font-normal">
                                    {{ $t('Speed up your website by using smart caching! Combine Full Page and Redis to make your site work faster and give your visitors a better experience.') }}
                                </p>
                                <div class="mt-20px grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px">
                                    <check-box-switch
                                        :label="$t('Full Page Caching')"
                                        v-model="form.enable_full_page_cache" id="enable_full_page_cache"/>

                                    <check-box-switch
                                        :label="$t('Redis Object Caching')"
                                        v-model="form.enable_redis_object_caching" id="enable_redis_object_cache"/>
                                </div>
                            </div>
                        </div>
                        <div
                            class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md text-dark dark:text-white"
                        >
                            <div>
                                <div
                                    class="flex wide-mobile:flex-wrap gap-2 items-center justify-between mb-4"
                                >
                                    <div class="flex flex-col gap-y-3">
                                        <h4
                                            class="text-2xl font-medium leading-none text-dark dark:text-white"
                                        >
                                            {{ $t('Blueprints') }}
                                        </h4>
                                        <p
                                            class="text-base text-secondary-full dark:text-mode-secondary-light"
                                        >
                                            {{ $t('Choose a blueprint to install WordPress with pre-configured plugins and themes.') }}
                                        </p>
                                    </div>
                                    <Switch
                                        @click.prevent="enableBlueprint = !enableBlueprint"
                                        :checked="enableBlueprint"
                                    ></Switch>
                                </div>
                                <template v-if="enableBlueprint">
                                    <div class="flex flex-col gap-4 md:gap-6">
                                        <div
                                            class="grid grid-cols-[repeat(auto-fill,minmax(18rem,1fr))] gap-3 md:gap-4"
                                        >
                                            <blue-print-card
                                                v-model="form.blueprint_id"
                                                contentClasses="!bg-light dark:!bg-mode-light"
                                                :blueprints="topBluePrints"
                                            />
                                        </div>
                                    </div>
                                    <div class="flex justify-end items-center gap-8 mt-4">
                                        <button
                                            @click.prevent="
                                            showMoreBlueprints = true
                                        "
                                            class="text-primary-light text-base flex items-center gap-2"
                                        >
                                            {{ $t('View all') }}
                                            <i
                                                class="xcloud xc-right-arrow text-sm"
                                            ></i>
                                        </button>
                                    </div>
                                    <BlueprintModal
                                        :show="showMoreBlueprints"
                                        :widthClass="'max-w-1120px'"
                                        @close="showMoreBlueprints = false"
                                    >
                                        <template #header>
                                            <div
                                                class="flex wide-mobile:flex-wrap gap-2 items-center justify-between"
                                            >
                                                <div class="flex flex-col gap-y-3">
                                                    <h4
                                                        class="text-2xl font-medium leading-none text-dark dark:text-white"
                                                    >
                                                        {{ $t('All Blueprints') }}
                                                    </h4>
                                                    <p
                                                        class="text-base text-secondary-full dark:text-mode-secondary-light"
                                                    >
                                                        {{ $t('Manage your all blueprints as you like, you can edit, delete or create new from here') }}
                                                    </p>
                                                </div>
                                            </div>
                                        </template>
                                        <div
                                            class="grid grid-cols-[repeat(auto-fill,minmax(18rem,1fr))] gap-3 md:gap-4"
                                        >
                                            <blue-print-card
                                                v-model="form.blueprint_id"
                                                contentClasses="!bg-light dark:!bg-mode-light"
                                                :blueprints="blueprints"
                                            />
                                        </div>

                                        <template #footer>
                                            <div class="flex justify-end items-center">
                                                <button
                                                    aria-expanded="true"
                                                    aria-haspopup="true"
                                                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-40px px-6 py-3px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                                                    @click.prevent="
                                            showMoreBlueprints = false
                                        "
                                                >
                                                    <span>{{ $t('OK') }}</span>
                                                </button>
                                            </div>
                                        </template>
                                    </BlueprintModal>
                                </template>
                            </div>
                        </div>
                        <Advanced
                            @showAdvance="showAdvanced"
                            :accordion_button="false"
                            :switch="[
                                form?.database_provider,
                                form?.managed_database_mode,
                            ]"
                        >
                            <AdvancedFields
                                :show_caching_fields="false"
                                :form="form"
                                :server="server"
                                :siteDefault="siteDefault"
                                :availablePhpVersions="availablePhpVersions"
                                :availableWordPressVersions="availableWordPressVersions"
                            />
                        </Advanced>
                    </form>
                </div>

                <WizardProgress
                    :processing="form.processing"
                    :back="route('dashboard')"
                    @next="submit"
                    :progress-width="progressWidth(1, 1)"
                    progress-text="Step 1 of 1"
                >
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                    </template>
                    <span>{{ $t('Next') }}</span>
                </WizardProgress>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useNameGenerator } from "@/Composables/useNameGenerator";
import AdvancedFields from "@/Pages/Site/New/Components/Advanced.vue";
import Advanced from "@/Shared/Advanced.vue";
import TextInput from "@/Shared/TextInput.vue";
import WizardProgress from "@/Shared/WizardProgress.vue";
import { useHelpStore } from "@/stores/HelpStore";
import { useNavigationStore } from "@/stores/NavigationStore";
import { useForm } from "@inertiajs/inertia-vue3";
import Multiselect from "@vueform/multiselect";
import {computed, ref, watch} from "vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import {useFlash} from "@/Composables/useFlash";
import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
import CheckBoxSwitch from "@/Jetstream/CheckBoxSwitch.vue";
import Error from "@/Shared/Error.vue";
import BlueprintModal from "@/Shared/BlueprintModal.vue";
import BluePrintCard from "@/Pages/Site/New/Components/Bluetprints/BluePrintCard.vue";
import Switch from "@/Shared/Switch.vue";
import CreateBluePrint from "@/Pages/BluePrint/CreateBluePrint.vue";

let navigation = useNavigationStore();
let helper = useHelpStore();


let props = defineProps({
    server: Object,
    siteDefault: Object,
    availablePhpVersions: Array,
    availableWordPressVersions: Array,
    tags: Array,
    prefix_id: [String, Number],
    hasCloudflareIntegration: Boolean,
    staging_domain:{
        type: String,
        default: 'x-cloud.app'
    },
    blueprints: [Array,Object]
});

const progressWidth = (start, end) => (start * 100) / (end ?? 1);
let domainParkingMethod = ref("staging_env");
let enableBlueprint = ref(true);
let showMoreBlueprints = ref(false);

const topBluePrints = computed(() => {
    const blueprint_list = props.blueprints.slice(0, 3);
    if (form.blueprint_id) {
        const onList = blueprint_list.some((blueprint) => blueprint.id == form.blueprint_id);
        const selected = props.blueprints.find((blueprint) => blueprint.id == form.blueprint_id);
        if (!onList && selected) {
            blueprint_list.splice(1, 1, selected);
        }
    }
    return blueprint_list;
});

const stagingHelper =
    "xCloud provides a temporary staging domain to make your site live. This helps quickly share progress as well as gather feedback from your teammates or clients";

let form = useForm({
    name: "",
    title: "",
    type: "wordpress",
    additional_domains: [],
    tags: props.server.tags.map((tag) => tag.name),
    enable_full_page_cache: true,
    enable_redis_object_caching: true,
    ssl_provider: "",
    ssl_certificate: "",
    ssl_private_key: "",
    site_user: props.siteDefault.site_user,
    deploy_script: props.siteDefault.deploy_script,
    admin_user: props.siteDefault.admin_user,
    admin_password: props.siteDefault.admin_password,
    admin_email: props.siteDefault.admin_email,
    database_name: props.siteDefault.database_name,
    database_user: props.siteDefault.database_user,
    database_password: props.siteDefault.database_password,
    database_host: props.siteDefault.database_host,
    database_port: props.siteDefault.database_port,
    prefix: props.siteDefault.prefix,
    wordpress_version: props.siteDefault.wordpress_version,
    php_version: props.siteDefault.php_version,
    database_provider: props.siteDefault.database_provider,
    managed_database_mode: props.siteDefault.managed_database_mode,
    managed_database_options: props.siteDefault.managed_database_options,
    domain_parking_method: domainParkingMethod,
    domain_active_on_cloudflare: '',
    cloudflare_account_id: '',
    cloudflare_zone_id: '',
    subdomain: '',
    site_name: '',
    blueprint_id: props.blueprints.find((blueprint) => blueprint.is_default)?.id
});

let showCloudflareDnsOption = ref(false)
let useCloudflareDns = ref(false)
let showDomainSetup = ref(true)
let loader = ref(false)

const tagList = computed(() => {
    return props.tags.map((tag) => ({
        label:
            tag?.counter > 1 && !form.tags.includes(tag.value)
                ? tag.label + " (" + tag?.counter + ")"
                : tag.label,
        value: tag.value,
    }));
});
let submit = () => {
    maybeRemoveHttp();
    // if (domainParkingMethod.value === "staging_env") {
    //     form.name = generateRandomString() + "." + props.staging_domain;
    // }
    form.transform((data) => {
        data.blueprint_id = enableBlueprint.value ? data.blueprint_id : null;
        return data;
    }).post(route("api.site.store-to-playground"), {
        preserveScroll: true,
    });
};

const showAdvanced = (isShow) => {
    helper.toggleAccordions([], [form.database_provider]);
};

watch(
    () => form.database_provider,
    () => {
        helper.toggleOpenWithAccordions([], [form.database_provider]);
    }
);

watch(
    () => form.name,
    () => {
        maybeRemoveHttp();
        form.name = form.name.toLowerCase();
        var generated_name = useNameGenerator(form.name);
        form.prefix = generated_name + "_" + props.prefix_id + "_";
        form.site_user = form.site_user ?? "u" + props.prefix_id + "_" + useNameGenerator(form.name,16 - ("u" + props.prefix_id + "_").length);
        if (form.database_provider === "in_server" || form.database_provider === "from_provider") {
            form.database_name = form.database_name ?? "db" + props.prefix_id + "_" + generated_name;
            form.database_user = form.database_user ?? "u" + props.prefix_id + "_" + generated_name;
        }
        form.managed_database_options.do_cluster_name = form.managed_database_options.do_cluster_name ?? "do-db-c-" + generateRandomString(4).toLocaleLowerCase() + "-" + generated_name;
        showCloudflareDnsOption.value = true;
    }
);

watch(
    () => form.title,
    () => {
        if (domainParkingMethod.value === "staging_env") {
            var generated_name = useNameGenerator(form.title);
            form.prefix = generated_name + "_" + props.prefix_id + "_";
            form.site_user =
                "u" +
                props.prefix_id +
                "_" +
                useNameGenerator(
                    form.title,
                    16 - ("u" + props.prefix_id + "_").length
                );
            if (form.database_provider === "in_server" || form.database_provider === "from_provider") {
                form.database_name = "db" + props.prefix_id + "_" + generated_name;
                form.database_user = "u" + props.prefix_id + "_" + generated_name;
            }
            form.managed_database_options.do_cluster_name = "do-db-c-" + generateRandomString(4).toLocaleLowerCase() + "-" + generated_name;
            // Replace invalid characters with an empty string
            if (form.title){
                let additional = "." + props.staging_domain;

                let form_name = generated_name.replace(/[^a-zA-Z0-9-]/g, '');

                form.name = form_name === '' ? '' : form_name + '-' + generateRandomString(4).toLocaleLowerCase() + additional;
            } else {
                form.name = '';
            }
        }
    }
);

watch(
    () => form.additional_domains,
    (newVal, oldVal) => {
      for(let i = 0; i < form.additional_domains.length; i++){
        if(form.additional_domains[i].value !== ''){
          form.additional_domains[i].value = form.additional_domains[i].value?.toLowerCase();
          // console.log(form.additional_domains[i].value)
          // if user adds additional domains, reset cloudflare integration(user will do manual dns setup)
          resetCloudflareIntegration();
          showDomainSetup.value = true;
        }
      }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);

const resetCloudflareIntegration = function (){
  form.domain_active_on_cloudflare = false;
  form.cloudflare_account_id = '';
  form.cloudflare_zone_id = '';
  form.subdomain = '';
  form.site_name = '';
  useCloudflareDns.value = false;
}

const updateFormFromDomainSetup = (updatedForm) => {
  // Update any form properties that might have changed in the DomainSetup component
  form.name = updatedForm.name;
  form.domain_parking_method = updatedForm.domain_parking_method;
  form.selected_staging_domain = updatedForm.selected_staging_domain;
  form.disable_search_engine_visibility = updatedForm.disable_search_engine_visibility;
  form.turn_off_indexing = updatedForm.turn_off_indexing;

  // Update Cloudflare integration fields
  form.domain_active_on_cloudflare = updatedForm.domain_active_on_cloudflare || false;
  form.cloudflare_account_id = updatedForm.cloudflare_account_id || '';
  form.cloudflare_zone_id = updatedForm.cloudflare_zone_id || '';
  form.subdomain = updatedForm.subdomain || '';
  form.site_name = updatedForm.site_name || '';
};


watch(
    () => useCloudflareDns,
    (newVal, oldVal) => {
      if(useCloudflareDns.value){
        if(form.name === ''){
          useCloudflareDns.value = false;
          useFlash().swal().fire({
            icon: 'error',
            title: 'Please add a domain name first',
          })
        }else{
          checkCloudFlareDomain();
        }
      }else{
        showDomainSetup.value = true;
        resetCloudflareIntegration()
      }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);


let checkDomain = () =>{
  form.processing = true;
  axios.get(route("api.site.check-domain-exists") + "?domain=" + form.name)
    .then((res) => {

    // let _name = form.name.split('.')[0];

    if(res.data.exists){
        form.errors.name = 'Looks like the domain is already in use. Please try changing the site title again.';
    } else {
        form.errors.name = '';
    }

    form.processing = false;

    }).catch((err) => {
      form.processing = false;

      useFlash().error(err.errors.domain[0]);
    })
}

let checkCloudFlareDomain = () =>{
  loader.value = true;
  form.processing = true;
  axios.get(route("api.user.integration.cloudflare.check-domain-exists", form.name))
    .then((res) => {
      // console.log(res.data.response.domain_active_on_cloudflare)
      showDomainSetup.value = !res.data.response.domain_active_on_cloudflare;

      form.domain_active_on_cloudflare = res.data.response.domain_active_on_cloudflare;
      form.cloudflare_account_id = res.data.response.account_id;
      form.cloudflare_zone_id = res.data.response.zone_id;
      form.subdomain = res.data.response.subdomain;
      form.site_name = res.data.response.site_name;

      loader.value = false;
      form.processing = false;

      useCloudflareDns.value = false;

      if(!res.data.response.domain_active_on_cloudflare){
        useFlash().error('No cloudflare account found for this domain. Please add your domain to cloudflare first.');
      }
    })
    .catch((err) => {
      showDomainSetup.value = true;
      loader.value = false;
      form.processing = false;
      if (err.response && err.response.data && err.response.data.message) {
        useFlash().error(err.response.data.message);
      } else {
        useFlash().error('An error occurred while processing your request.');
      }
    })
}

function maybeRemoveHttp() {
    if (form.name && (form.name.startsWith("http://") || form.name.startsWith("https://"))) {
        form.name = form.name.replace(/^https?:\/\//, "");
    }
}

function generateRandomString(length = 5) {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

const onTagSelect = (tag) => {
    //check does tag has contained any special character like #, @, $, %, ^, /, \, |, {, }, [, ], ~
    if (tag.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
        //remove from tag list
        form.tags = form.tags.filter((item) => item !== tag);
    }
};

function toggleStaging() {
    form.domain_parking_method = "staging_env";
    const generated_name = useNameGenerator(form.title);
    form.prefix = generated_name + "_" + props.prefix_id + "_";
    if (form.site_user === props.siteDefault.site_user) {
        form.site_user ="u" + props.prefix_id + "_" + useNameGenerator(form.title, 16 - ("u" + props.prefix_id + "_").length);
    }
    form.name = form.title + "." + props.staging_domain;
    /*if (form.database_name === '' && (form.database_provider === "in_server" || form.database_provider === "from_provider")) {
        form.database_name = (form.database_name === props.siteDefault.database_name) ? "db" + props.prefix_id + "_" + generated_name : props.siteDefault.database_name;
        form.database_user = (form.database_user === props.siteDefault.database_user) ? "u" + props.prefix_id + "_" + generated_name : props.siteDefault.database_user;
    }
    if (form.managed_database_options?.do_cluster_name === '' && !(form.database_name === props.siteDefault.database_name&&form.database_user === props.siteDefault.database_user)){
        form.managed_database_options.do_cluster_name = "do-db-c-" + generateRandomString(4).toLocaleLowerCase() + "-" + generated_name;
    }*/
}
function toggleLive(){
    form.domain_parking_method = 'go_live';
    //check if the staging domain is set
    if(form.name.endsWith(props.staging_domain)){
        form.name = '';
    }
}
const tagsErrors = function() {
    const errors = [];
    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });
    return errors.length ? errors[0] : null;
};
</script>
<style>
@import "@vueform/multiselect/themes/tailwind.css";
</style>
