<template>
    <Head title="Install New WordPress Website" />

    <div class="xc-container">
        <div
            class="flex-1 flex items-center"
            :class="
                navigation.isOpenHelpbar(helper.isOpen)
                    ? 'mr-400px small-laptop:mr-0'
                    : 'mr-0'
            "
        >
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark
                              dark:text-white mb-50px wide-mobile:text-3xl mobile:text-28px tablet:mb-40px wide-mobile:mb-30px"
                    >
                        <span>{{ $t('Add Your New Site Into') }}</span>
                        <cloud-provider-logo
                            :provider-name="
                                server?.cloud_provider_name ??
                                server?.cloud_provider_readable
                            "
                            class="w-40px inline-flex mx-2.5"
                        />
                        <Link
                            :href="route('server.show', server.id)"
                            class="font-medium text-primary-dark"
                        >
                          {{ server.name }}
                        </Link>
                    </h2>

                    <form @submit.prevent="submit">
                        <button type="submit" class="hidden">{{ $t('Submit') }}</button>

                        <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                            <text-input
                                @keyup="checkStagingDomain"
                                v-model="form.title"
                                :error="form.errors.title"
                                id="title"
                                :label="$t('Site Title')"
                                :placeholder="$t('Site Title')"
                            />
                            <div>
                                <label
                                    class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none"
                                    for="tags"
                                >
                                    {{ $t('Add Tag (optional)') }}
                                </label>
                                <multiselect
                                    class="!ml-0"
                                    id="tags"
                                    :class="{
                                        'ring-1 ring-danger-light':
                                            tagsErrors(),
                                    }"
                                    v-model="form.tags"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    @select="onTagSelect"
                                    :placeholder="$t('Select or create tags')"
                                    :options="tagList"
                                    regex="^[a-zA-Z0-9_ -]+$"
                               />
                                <Error :error="tagsErrors()" />
                            </div>
                        </div>

                        <DomainSetup
                            :form="form"
                            :server="server"
                            :availableDomains="availableDomains"
                            :hasCloudflareIntegration="hasCloudflareIntegration"
                            :title="form.title"
                            :prefixId="prefix_id"
                            @update:form="updateFormFromDomainSetup"
                            @domain-change="handleDomainChange"
                        />

                        <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md">
                          <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                            {{ $t('WordPress Multisite') }}
                          </h3>
                          <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px w-full">
                            <check-box-switch
                                :label="$t('Enable Multisite')"
                                v-model="form.enable_multisite"
                                id="enable_multisite"
                            />

                            <multiselect
                                :canClear="false"
                                :canSearch="false"
                                v-if="form.enable_multisite"
                                v-model="form.multisite_subdomain"
                                :options="multiSiteOptions"
                                :placeholder="$t('Select Multisite Type')"
                                id="multisite_subdomain"
                           />
                          </div>
                        </div>

                        <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                              dark:bg-mode-base rounded-md"
                        >
                            <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                {{ $t('Enhancing Website Performance') }}
                            </h3>
                            <div class="grid grid-cols-1 w-full">
                                <p class="text-secondary-full block text-base font-normal">
                                    {{ $t('Speed up your website by using smart caching!') }}
                                    <template v-if="server.stack === 'nginx'">
                                        {{ $t('Combine Full Page and Redis to make your site work faster and give your visitors a better experience.') }}
                                    </template>
                                    <template v-else>
                                        {{ $t('Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.') }}
                                    </template>
                                </p>
                                <div class="mt-20px grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px">
                                    <check-box-switch
                                        :label="server.stack === 'nginx' ? $t('Full Page Caching') : $t('LiteSpeed Cache')"
                                        v-model="form.enable_full_page_cache"
                                        id="enable_full_page_cache"
                                    />


                                    <check-box-switch
                                        v-if="server.stack === 'nginx'"
                                        :label="$t('Redis Object Caching')"
                                        v-model="form.enable_redis_object_caching"
                                        id="enable_redis_object_cache"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- Email Provider -->
                        <div v-if="!$page?.props?.current_white_label" class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                              dark:bg-mode-base rounded-md"
                        >
                            <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                {{ $t('Email Provider Configuration') }}
                            </h3>
                            <div class="grid grid-cols-1 w-full">
                                <p class="text-secondary-full block text-base font-normal">
                                    {{ $t('Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.') }}
                                    <br />
                                    <a
                                        href="https://xcloud.host/docs/add-xcloud-managed-email-service/"
                                        class="text-sm underline"
                                        target="_blank"
                                    >
                                        {{ $t('Read our documentation') }}
                                    </a>
                                </p>
                                <div class="mt-20px grid grid-cols-2 gap-30px">
                                    <check-box-switch
                                        label="Use xCloud Managed Email Service"
                                        v-model="form.use_xcloud_default_email_provider"
                                        id="xcloud_default_email_provider"
                                    />
                                </div>
                            </div>
                        </div>

                        <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                            dark:bg-mode-base rounded-md text-dark dark:text-white"
                        >
                            <div>
                                <div class="flex wide-mobile:flex-wrap gap-2 items-center justify-between mb-4">
                                    <div class="flex flex-col gap-y-3">
                                        <h4 class="text-2xl font-medium leading-none text-dark dark:text-white">
                                            {{ $t('Blueprints') }}
                                        </h4>
                                        <p class="text-base text-secondary-full dark:text-mode-secondary-light">
                                            {{ $t('Choose a blueprint to install WordPress with pre-configured plugins and themes.') }}
                                        </p>
                                    </div>
                                    <Switch
                                        @click.prevent="enableBlueprint = !enableBlueprint"
                                        :checked="enableBlueprint"
                                    ></Switch>
                                </div>
                                <template v-if="enableBlueprint">
                                    <div class="flex flex-col gap-4 md:gap-6">
                                        <div class="grid grid-cols-[repeat(auto-fill,minmax(18rem,1fr))] gap-3 md:gap-4">
                                            <blue-print-card
                                                v-model="form.blueprint_id"
                                                contentClasses="!bg-light dark:!bg-mode-light"
                                                :blueprints="topBluePrints"
                                            />
                                        </div>
                                    </div>
                                    <div class="flex justify-end items-center gap-8 mt-4">
                                    <button
                                        @click.prevent="showMoreBlueprints = true"
                                        class="text-primary-light text-base flex items-center gap-2"
                                    >
                                        {{ $t('View all') }}
                                        <i class="xcloud xc-right-arrow text-sm"></i>
                                    </button>
                                    <button
                                        v-if="can_manage_blueprints"
                                        aria-expanded="true"
                                        aria-haspopup="true"
                                        class="flex gap-2 items-center justify-center rounded-md border-transparent shadow-none min-h-10 px-4 py-3 text-sm font-semibold text-white bg-primary-light focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50 whitespace-nowrap"
                                        @click.prevent="showBluePrintCreateModal = true"
                                    >
                                        <i class="xcloud xc-add"></i>
                                        <span>{{ $t('Create New Blueprint') }}</span>
                                    </button>
                                </div>
                                    <create-blue-print
                                        :blue-print-count="blueprints.length"
                                        :showBluePrintCreateModal="showBluePrintCreateModal"
                                        @close="closeBluePrintCreateModal"
                                    />
                                    <BlueprintModal
                                        :show="showMoreBlueprints"
                                        :widthClass="'max-w-1120px'"
                                        @close="showMoreBlueprints = false"
                                    >
                                        <template #header>
                                            <div class="flex wide-mobile:flex-wrap gap-2 items-center justify-between">
                                                <div class="flex flex-col gap-y-3">
                                                    <h4 class="text-2xl font-medium leading-none text-dark dark:text-white">
                                                        {{ $t('All Blueprints') }}
                                                    </h4>
                                                    <p class="text-base text-secondary-full dark:text-mode-secondary-light">
                                                        {{ $t('Manage your all blueprints as you like, you can edit, delete or create new from here') }}
                                                    </p>
                                                </div>
                                                <button
                                                    v-if="can_manage_blueprints"
                                                    aria-expanded="true"
                                                    aria-haspopup="true"
                                                    class="flex gap-2 items-center justify-center rounded-md border-transparent shadow-none min-h-10 px-4 py-3 text-sm font-semibold text-white bg-primary-light focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50 whitespace-nowrap ml-auto"
                                                    @click.prevent="
                                                        showMoreBlueprints = false;
                                                        showBluePrintCreateModal = true;
                                                    "
                                                >
                                                    <i class="xcloud xc-add"></i>
                                                    <span>{{ $t('Create New Blueprint') }}</span>
                                                </button>
                                            </div>
                                        </template>
                                        <div class="grid grid-cols-[repeat(auto-fill,minmax(18rem,1fr))] gap-3 md:gap-4">
                                            <blue-print-card
                                                v-model="form.blueprint_id"
                                                contentClasses="!bg-light dark:!bg-mode-light"
                                                :blueprints="blueprints"
                                            />
                                        </div>

                                        <template #footer>
                                            <div class="flex justify-end items-center">
                                                <button
                                                    aria-expanded="true"
                                                    aria-haspopup="true"
                                                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-40px px-6 py-3px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                                                    @click.prevent="showMoreBlueprints = false"
                                                >
                                                    <span>{{ $t('OK') }}</span>
                                                </button>
                                            </div>
                                        </template>
                                    </BlueprintModal>
                                </template>
                            </div>
                        </div>

                        <Advanced
                            @showAdvance="showAdvanced"
                            :accordion_button="false"
                            :switch="[
                                form?.database_provider,
                                form?.managed_database_mode,
                            ]"
                        >
                            <AdvancedFields
                                :show_caching_fields="false"
                                :form="form"
                                :server="server"
                                :siteDefault="siteDefault"
                                :availablePhpVersions="availablePhpVersions"
                                :availableWordPressVersions="
                                    availableWordPressVersions
                                "
                                @updateUserVal="updateUserFields"
                           />
                        </Advanced>
                    </form>
                </div>

                <WizardProgress
                    :processing="form.processing"
                    :back="route('site.create', {
                        server: server.id,
                        type: 'wordpress',
                    })"
                    @next="submit"
                    :progress-width="progressWidth(3, 3)"
                    progress-text="Step 3 of 3"
                >
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                    </template>
                    <span>{{ $t('Next') }}</span>
                </WizardProgress>
            </div>
        </div>
    </div>
 </template>

 <script setup>
 import { useNameGenerator } from "@/Composables/useNameGenerator";
 import AdvancedFields from "@/Pages/Site/New/Components/Advanced.vue";
 import DomainSetup from "@/Pages/Site/New/Components/DomainSetup.vue";
 import Advanced from "@/Shared/Advanced.vue";
 import TextInput from "@/Shared/TextInput.vue";
 import WizardProgress from "@/Shared/WizardProgress.vue";
 import { useHelpStore } from "@/stores/HelpStore";
 import { useNavigationStore } from "@/stores/NavigationStore";
 import {useForm, usePage} from "@inertiajs/inertia-vue3";
 import Multiselect from "@vueform/multiselect";
 import { computed, onMounted, ref, watch } from "vue";
 import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
 import Skeleton from "@/Shared/Table/Skeleton.vue";
 import { useFlash } from "@/Composables/useFlash";
 import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
 import CheckBoxSwitch from "@/Jetstream/CheckBoxSwitch.vue";
 import Error from "@/Shared/Error.vue";
 import BluePrintCard from "@/Pages/Site/New/Components/Bluetprints/BluePrintCard.vue";
 import BlueprintModal from "@/Shared/BlueprintModal.vue";
 import CreateBluePrint from "@/Pages/BluePrint/CreateBluePrint.vue";
 import Switch from "@/Shared/Switch.vue";
 import {Inertia} from "@inertiajs/inertia";
 import { useI18n } from "vue-i18n";
 const { t } = useI18n();

 let navigation = useNavigationStore();
 let helper = useHelpStore();
 let showMoreBlueprints = ref(false);
 let showBluePrintCreateModal = ref(false);

let props = defineProps({
    server: Object,
    siteDefault: Object,
    availablePhpVersions: Array,
    availableWordPressVersions: Array,
    tags: Array,
    prefix_id: [String, Number],
    hasCloudflareIntegration: Boolean,
    staging_domain: {
        type: String,
        default: "x-cloud.app",
    },
    blueprints: [Array,Object],
    can_manage_blueprints: Boolean,
    availableDomains: {
        type: Array,
        required: true,
    }
});

 const progressWidth = (start, end) => (start * 100) / (end ?? 1);

 let form = useForm({
    name: "",
    title: "",
    type: "wordpress",
    additional_domains: [],
    tags: props.server.tags.map((tag) => tag.name),
    enable_full_page_cache: true,
    enable_redis_object_caching: props.server.stack === 'nginx', // redis_object_caching is only for nginx
    ssl_provider: "",
    ssl_certificate: "",
    ssl_private_key: "",
    site_user: props.siteDefault.site_user,
    deploy_script: props.siteDefault.deploy_script,
    admin_user: props.siteDefault.admin_user,
    admin_password: props.siteDefault.admin_password,
    admin_email: props.siteDefault.admin_email,
    database_name: props.siteDefault.database_name,
    database_user: props.siteDefault.database_user,
    database_password: props.siteDefault.database_password,
    database_host: props.siteDefault.database_host,
    database_port: props.siteDefault.database_port,
    prefix: props.siteDefault.prefix,
    wordpress_version: props.siteDefault.wordpress_version,
    php_version: props.siteDefault.php_version,
    database_provider: props.siteDefault.database_provider,
    managed_database_mode: props.siteDefault.managed_database_mode,
    managed_database_options: props.siteDefault.managed_database_options,
    domain_parking_method: "go_live",
    domain_active_on_cloudflare: "",
    cloudflare_account_id: "",
    cloudflare_zone_id: "",
    subdomain: "",
    site_name: "",
    use_xcloud_default_email_provider: true,
    enable_multisite: false,
    multisite_subdomain: false,
    blueprint_id: props.blueprints.find((blueprint) => blueprint.is_default)?.id,
    disable_search_engine_visibility: false,
    selected_staging_domain: props.staging_domain,
    turn_off_indexing: true,
 });

 let enableBlueprint = ref(true);
 let userUpdateFields = [];
 let loader = ref(false);

 const updateUserFields = (field) => {
    if (!userUpdateFields.includes(field)) {
        userUpdateFields.push(field);
    }
 };

 const topBluePrints = computed(() => {
    const blueprint_list = props.blueprints.slice(0, 3);
    if (form.blueprint_id) {
        const onList = blueprint_list.some((blueprint) => blueprint.id == form.blueprint_id);
        const selected = props.blueprints.find((blueprint) => blueprint.id == form.blueprint_id);
        if (!onList && selected) {
            blueprint_list.splice(1, 1, selected);
        }
    }
    return blueprint_list;
 });

 const tagList = computed(() => {
    return props.tags.map((tag) => ({
        label:
            tag?.counter > 1 && !form.tags.includes(tag.value)
                ? tag.label + " (" + tag?.counter + ")"
                : tag.label,
        value: tag.value,
    }));
 });

 let submit = () => {
    maybeRemoveHttp();
    if (form.domain_parking_method === "staging_env" && form.name === "") {
        form.name = generateRandomString() + "." + form.selected_staging_domain;
    }

    form
        .transform((data) => {
            data.blueprint_id = enableBlueprint.value ? data.blueprint_id : null;
            if(form.domain_parking_method === 'staging_env'){
              data.name = form.name + '.' + form.selected_staging_domain
            }
            return data;
        })
        .post(route("api.site.store", props.server.id), {
        preserveScroll: true,
    });
 };

 watch(()=> form.domain_parking_method, () => {
         generateStagingEnvDomainName();
     }
 );


 const showAdvanced = (isShow) => {
    helper.toggleAccordions([], [form.atabase_provider]);
 };

 watch(
    () => form.database_provider,
    () => {
        helper.toggleOpenWithAccordions([], [form.database_provider]);
    }
 );

 watch(
    () => form.multisite_subdomain,
    () => {
      form.ssl_provider = '';
      form.ssl_certificate = '';
      form.ssl_private_key = '';
      for (let i = 0; i < form.additional_domains.length; i++) {
        form.additional_domains[i].value = '';
      }
    }
 );

 const isIpSite = computed(() => {
    return form.name.trim() === props.server.public_ip || form.name.trim().match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/);
 });

 const multiSiteOptions = computed(() => {
    if (isIpSite.value) {
        return [
            {
                label: "Subdirectory",
                value: false,
            },
        ];
    }
    return [
        {
            label: "Subdirectory",
            value: false,
        },
        {
            label: "Subdomain",
            value: true,
        },
    ];
 });

 const updateFormFromDomainSetup = (updatedForm) => {
    // Update any form properties that might have changed in the DomainSetup component
    form.name = updatedForm.name;
    form.domain_parking_method = updatedForm.domain_parking_method;
    form.selected_staging_domain = updatedForm.selected_staging_domain;
    form.disable_search_engine_visibility = updatedForm.disable_search_engine_visibility;
    form.turn_off_indexing = updatedForm.turn_off_indexing;
    form.domain_active_on_cloudflare = updatedForm.domain_active_on_cloudflare;
    form.cloudflare_account_id = updatedForm.cloudflare_account_id;
    form.cloudflare_zone_id = updatedForm.cloudflare_zone_id;
    form.subdomain = updatedForm.subdomain;
    form.site_name = updatedForm.site_name;
};

const handleDomainChange = () => {
    // The domain name is now preserved in the DomainSetup component
    // We don't need to clear it here anymore
};

const generateStagingEnvDomainName = () => {
    if (form.domain_parking_method === "staging_env"&& form.title) {
        const generated_name = useNameGenerator(form.title);
        form.site_user =
            "u" +
            props.prefix_id +
            "_" +
            useNameGenerator(
                form.title,
                16 - ("u" + props.prefix_id + "_").length
            );
        if (form.database_provider === "in_server" || form.database_provider === "from_provider") {
            if (!userUpdateFields.includes("database_name") || form.database_name.length === 0) {
                form.database_name ="db" + props.prefix_id + "_" + generated_name;
            }
                if (!userUpdateFields.includes("database_user") || form.database_user.length === 0) {
                form.database_user ="u" + props.prefix_id + "_" + generated_name;
        }
        }form.managed_database_options.do_cluster_name =
            "do-db-c-" +
            generateRandomString(4).toLocaleLowerCase() +
            "-" +
            generated_name;

    }
};

 watch(
    () => form.title,
    () => {
        generateStagingEnvDomainName();
    }
 );

 watch(
    () => form.name,
    () => {
        maybeRemoveHttp();
        form.name = form.name.toLowerCase();
        const generated_name = useNameGenerator(form.name);

        if (!userUpdateFields.includes("site_user") || form.site_user.length === 0) {
            form.site_user =
                "u" +
                props.prefix_id +
                "_" +
                useNameGenerator(
                    form.name,
                    16 - ("u" + props.prefix_id + "_").length
                );
        }
        if (form.database_provider === "in_server" || form.database_provider === "from_provider") {
            if (!userUpdateFields.includes("database_name") || form.database_name.length === 0) {
                form.database_name = "db" + props.prefix_id + "_" + generated_name;
            }
            if (!userUpdateFields.includes("database_user") || form.database_user.length === 0) {
                form.database_user = "u" + props.prefix_id + "_" + generated_name;
            }
        }
        form.managed_database_options.do_cluster_name =
            form.managed_database_options.do_cluster_name ??
            "do-db-c-" +
                generateRandomString(4).toLocaleLowerCase() +
                "-" +
                generated_name;
    }
 );

 const onTagSelect = (tag) => {
    if (form.tags.length > 5) {
        form.tags.pop();
    }
 };

 const maybeRemoveHttp = () => {
    if (form.name.startsWith("http://")) {
        form.name = form.name.substring(7);
    } else if (form.name.startsWith("https://")) {
        form.name = form.name.substring(8);
    }
 };

 const tagsErrors = function () {
    const errors = [];

    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
 };

 const closeBluePrintCreateModal = () => {
    showBluePrintCreateModal.value = false;
 };

 const checkStagingDomain = () => {
    // This function is now handled by the DomainSetup component
 };

 const generateRandomString = (length = 6) => {
    const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(
            Math.floor(Math.random() * charactersLength)
        );
    }
    return result;
 };
 </script>

 <style>
 @import "@vueform/multiselect/themes/tailwind.css";
 </style>
