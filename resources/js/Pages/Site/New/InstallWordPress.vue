<template>
    <Head title="Install New WordPress Website" />

    <div class="xc-container">
        <div
            class="flex-1 flex items-center"
            :class="
                navigation.isOpenHelpbar(helper.isOpen)
                    ? 'mr-400px small-laptop:mr-0'
                    : 'mr-0'
            "
        >
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark
                              dark:text-white mb-50px wide-mobile:text-3xl mobile:text-28px tablet:mb-40px wide-mobile:mb-30px"
                    >
                        <span>{{ $t('Add Your New Site Into') }}</span>
                        <cloud-provider-logo
                            :provider-name="
                                server?.cloud_provider_name ??
                                server?.cloud_provider_readable
                            "
                            class="w-40px inline-flex mx-2.5"
                        />
                        <Link
                            :href="route('server.show', server.id)"
                            class="font-medium text-primary-dark"
                        >
                          {{ server.name }}
                        </Link>
                    </h2>

                    <form @submit.prevent="submit">
                        <button type="submit" class="hidden">{{ $t('Submit') }}</button>

                        <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                            <text-input
                                @keyup="checkStagingDomain"
                                v-model="form.title"
                                :error="form.errors.title"
                                id="title"
                                :label="$t('Site Title')"
                                :placeholder="$t('Site Title')"
                            />
                            <div>
                                <label
                                    class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none"
                                    for="tags"
                                >
                                    {{ $t('Add Tag (optional)') }}
                                </label>
                                <multiselect
                                    class="!ml-0"
                                    id="tags"
                                    :class="{
                                        'ring-1 ring-danger-light':
                                            tagsErrors(),
                                    }"
                                    v-model="form.tags"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    @select="onTagSelect"
                                    :placeholder="$t('Select or create tags')"
                                    :options="tagList"
                                    regex="^[a-zA-Z0-9_ -]+$"
                               />
                                <Error :error="tagsErrors()" />
                            </div>
                        </div>
                        <div class="mt-5 grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px wide-mobile:gap-4 mb-30px">
                            <div
                                :class="{
                                    'border-primary-light ring-1 ring-primary-light':
                                        form.domain_parking_method ===
                                        'go_live',
                                    'border-secondary-light dark:border-mode-focus-light':
                                        form.domain_parking_method !==
                                        'go_live',
                                }"
                                class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                                @click="goLive()"
                            >
                                <div class="shrink-0 inline-flex items-center justify-center">
                                    <img
                                        :src="asset('img/png/live.png')"
                                        alt="xcloud_logo"
                                        class="w-12 h-auto"
                                    />
                                </div>
                                <div class="flex flex-col gap-2">
                                    <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                        {{ $t('Go Live') }}
                                    </h5>
                                    <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full
                                          dark:text-mode-secondary-light"
                                    >
                                        {{ $t('Get your site up and running for the world to see by simply pointing your domain to the server.') }}
                                    </p>
                                </div>
                            </div>
                            <div
                                :class="{
                                    'border-secondary-light dark:border-mode-focus-light':
                                        form.domain_parking_method !==
                                        'staging_env',
                                    'border-primary-light ring-1 ring-primary-light':
                                        form.domain_parking_method ===
                                        'staging_env',
                                }"
                                class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                                @click="toggleStaging"
                            >
                                <div class="shrink-0 inline-flex items-center justify-center">
                                    <img
                                        :src="asset('img/png/staging2.png')"
                                        alt="xcloud_logo"
                                        class="w-12 h-auto"
                                    />
                                </div>
                                <div class="flex flex-col gap-2">
                                    <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                        {{ $t('Demo Site') }}
                                    </h5>
                                    <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                      {{ $t('Create a demo site with our test domain and customize before going live.') }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <suggestion
                            v-if="form.domain_parking_method === 'staging_env'"
                            :message="stagingHelper"
                       />

                        <div>
                            <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md">
                                <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                    {{
                                        form.domain_parking_method === "go_live"
                                            ? $t("Domain Setup")
                                            : $t("Demo Site Setup")
                                    }}
                                </h3>
                            <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px w-full">
                                <div class="domain-selector" >
                                    <div>
                                        <div v-if="form.domain_parking_method !== 'staging_env'">
                                        <text-input
                                            v-model="form.name"
                                            :error="form.errors.name"
                                            :label="$t('Provide your Domain Name')"
                                            :placeholder="$t('Domain Name')"
                                            class="flex-1"
                                        />
                                        </div>


                                        <div v-if="domainParkingMethod === 'staging_env'" class="relative w-full font-normal text-sm  leading-[20px]">
                                            <text-input
                                                v-model="form.name"
                                                :error="form.errors.name"
                                                :label="$t('Domain Name')"
                                                :placeholder="$t('testing')"
                                                class="flex-1 bg-transparent-input"
                                            />

                                            <div class="absolute top-9 right-2"
                                                 @click="toggleDropdown"
                                                 ref="dropdownRef">
                                                <div
                                                    class="max-w-[117px] w-full px-3 z-50 py-2 bg-[#EDF2F8] dark:bg-[#1D2238] rounded-[4px] text-[#2A3268] dark:text-[#919DB9]  flex items-center justify-between cursor-pointer"
                                                >
                                                    <div class="flex-1 min-w-0">
                                                        <Tooltip
                                                            :title="'.' + form.selected_staging_domain || 'Select a Domain'"
                                                            align="top"
                                                            color="primary">
                                                            <p class="truncate text-sm leading-5 max-w-[90px]">{{ '.' + form.selected_staging_domain || 'Select a Domain' }}</p>
                                                        </Tooltip>
                                                    </div>
                                                    <div
                                                        class="h-4 aspect-square shrink-0 text-xxxs flex items-center justify-end rounded cursor-pointer"
                                                    >
                                                        <i
                                                            :class="[
                                                            'xcloud xc-angle_down transition-transform duration-300 ',
                                                            isDropdownOpen ? 'rotate-180' : 'rotate-0',
                                                        ]"
                                                        ></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Dropdown menu -->
                                            <div
                                                v-if="isDropdownOpen"

                                                class="absolute min-w-[130px] top-[72px] right-0 z-10   bg-white dark:bg-[#171A30] border border-[#C1C5DE] dark:border-[#313A6C] rounded-[4px] text-[#919DB9] dark:text-[#919DB9] flex flex-col items-center gap-[12px] cursor-pointer p-[10px] whitespace-nowrap overflow-x-scroll sm:overflow-hidden text-ellipsis"
                                                style="max-width: 100%;"
                                                >
                                                <ul class=" flex flex-col mx-auto">
                                                <li
                                                    v-for="domain in availableDomains"
                                                    :key="domain"
                                                    @click="selectDomain(domain)"
                                                    class="p-1 cursor-pointer hover:text-[#2A3268] dark:hover:text-[#FFFFFF]"
                                                >
                                                    {{ '.' + domain }}
                                                </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                    <AdditionalDomain
                                    v-if="form.domain_parking_method === 'go_live'"
                                    :is_disable="form.enable_multisite && form.multisite_subdomain"
                                    :form="form"
                                    />
                                </div>

                                <div class="mt-3"
                                    v-if="
                                        showCloudflareDnsOption &&
                                        form.domain_parking_method === 'go_live'
                                    "
                                >
                                    <label class="inline-flex mr-5">
                                        <input
                                            type="checkbox"
                                            class="hidden peer"
                                            :disabled="
                                                !hasCloudflareIntegration
                                            "
                                            v-model="useCloudflareDns"
                                            :class="{
                                                'opacity-50 cursor-not-allowed':
                                                    !hasCloudflareIntegration,
                                            }"
                                        />
                                        <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                                                    before:bg-transparent before:border-1 before:border-secondary-light
                                                    dark:border-mode-secondary-dark before:rounded before:mt-0.5
                                                    before:mr-2.5 before:text-xxxs before:inline-flex before:items-center
                                                    before:justify-center before:text-transparent before:outline-none
                                                    before:transition before:duration-200 peer-checked:before:border-success-full
                                                    peer-checked:before:bg-success-full peer-checked:before:text-white"
                                        >
                                            <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
                                                <span
                                                    class="flex"
                                                    :class="{
                                                        'opacity-50 cursor-not-allowed':
                                                            !hasCloudflareIntegration,
                                                    }"
                                                >
                                                    <span>
                                                      {{ $t('Add DNS and SSL Certificate on Cloudflare') }}
                                                    </span>
                                                    <img :src="
                                                        asset(
                                                            'img/CF_logomark.png'
                                                        )"
                                                        alt="cloudflare logo"
                                                        class="ml-1 w-8 h-4"
                                                    />
                                                    <span>&nbsp;{{ $t('(Optional)') }}</span>
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                                                    {{ $t('Integrate Cloudflare forAutomatic DNS and SSL management.') }} (<Link
                                                        class="underline"
                                                        :href="
                                                            route(
                                                                'user.integration.cloudflare'
                                                            )
                                                        "
                                                        v-text="
                                                            'Manage your Cloudflare Integration'
                                                        "
                                                    />)
                                                </span>
                                            </span>
                                        </span>
                                    </label>

                                    <suggestion
                                        class="mt-5"
                                        v-if="
                                            hasCloudflareIntegration &&
                                            form.domain_active_on_cloudflare &&
                                            hasAdditionalDomains
                                        "
                                        :message="$t('If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.')"
                                        :light-mode="true"
                                    >
                                    </suggestion>
                                </div>
                            </div>

                            <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md">
                              <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                {{ $t('WordPress Multisite') }}
                              </h3>
                              <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px w-full">
                                <check-box-switch
                                    :label="$t('Enable Multisite')"
                                    v-model="form.enable_multisite"
                                    id="enable_multisite"
                                />

                                <multiselect
                                    :canClear="false"
                                    :canSearch="false"
                                    v-if="form.enable_multisite"
                                    v-model="form.multisite_subdomain"
                                    :options="multiSiteOptions"
                                    :placeholder="$t('Select Multisite Type')"
                                    id="multisite_subdomain"
                               />
                              </div>
                            </div>

                            <Skeleton v-if="loader" class="mt-30px" columns="2"/>
                            <div
                                v-else-if="
                                    !loader &&  !isIpSite &&
                                    form.domain_parking_method === 'go_live'
                                "
                                class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md"
                            >
                                <div v-if="showDomainSetup">
                                    <DNS :form="form" :server="server" />
                                    <Https :form="form" :server="server" :disable-https="form.multisite_subdomain" />


                                  <suggestion
                                      v-if="form.multisite_subdomain"
                                      class="col-span-2 mt-5"
                                      :message="$t('You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.')"
                                      :light-mode="true"
                                 />
                                </div>

                                <div v-else>
                                    <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                        {{ $t('DNS Setup') }}
                                    </h3>
                                    <div class="grid grid-cols-1 w-full">
                                        <suggestion
                                            class="mt-3"
                                            type="success"
                                            :message="dnsHelpText"
                                            :light-mode="true"
                                       />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                              dark:bg-mode-base rounded-md"
                        >
                            <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                {{ $t('Enhancing Website Performance') }}
                            </h3>
                            <div class="grid grid-cols-1 w-full">
                                <p class="text-secondary-full block text-base font-normal">
                                    {{ $t('Speed up your website by using smart caching!') }}
                                    <template v-if="server.stack === 'nginx'">
                                        {{ $t('Combine Full Page and Redis to make your site work faster and give your visitors a better experience.') }}
                                    </template>
                                    <template v-else>
                                        {{ $t('Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.') }}
                                    </template>
                                </p>
                                <div class="mt-20px grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px">
                                    <check-box-switch
                                        :label="server.stack === 'nginx' ? $t('Full Page Caching') : $t('LiteSpeed Cache')"
                                        v-model="form.enable_full_page_cache"
                                        id="enable_full_page_cache"
                                    />


                                    <check-box-switch
                                        v-if="server.stack === 'nginx'"
                                        :label="$t('Redis Object Caching')"
                                        v-model="form.enable_redis_object_caching"
                                        id="enable_redis_object_cache"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- Email Provider -->
                        <div v-if="!$page?.props?.current_white_label" class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                              dark:bg-mode-base rounded-md"
                        >
                            <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                {{ $t('Email Provider Configuration') }}
                            </h3>
                            <div class="grid grid-cols-1 w-full">
                                <p class="text-secondary-full block text-base font-normal">
                                    {{ $t('Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.') }}
                                    <br />
                                    <a
                                        href="https://xcloud.host/docs/add-xcloud-managed-email-service/"
                                        class="text-sm underline"
                                        target="_blank"
                                    >
                                        {{ $t('Read our documentation') }}
                                    </a>
                                </p>
                                <div class="mt-20px grid grid-cols-2 gap-30px">
                                    <check-box-switch
                                        label="Use xCloud Managed Email Service"
                                        v-model="form.use_xcloud_default_email_provider"
                                        id="xcloud_default_email_provider"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- No indexing -->
                        <div
                            v-if="form.domain_parking_method === 'staging_env'"
                            class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                                    dark:bg-mode-base rounded-md"
                        >
                          <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                            {{ $t('Turn off Indexing') }}
                          </h3>
                          <div class="grid grid-cols-1 w-full">
                            <p class="text-secondary-full block text-base font-normal">
                              {{ $t('Turning on this setting will prevent search engines from indexing your staging site.') }}
                            </p>
                            <div class="mt-20px grid grid-cols-2 gap-30px">
                              <check-box-switch
                                  label="Turn off Indexing"
                                  v-model="form.turn_off_indexing"
                                  id="turn_off_staging_indexing"
                              />
                            </div>
                          </div>
                        </div>

                        <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                            dark:bg-mode-base rounded-md text-dark dark:text-white"
                        >
                            <div>
                                <div class="flex wide-mobile:flex-wrap gap-2 items-center justify-between mb-4">
                                    <div class="flex flex-col gap-y-3">
                                        <h4 class="text-2xl font-medium leading-none text-dark dark:text-white">
                                            {{ $t('Blueprints') }}
                                        </h4>
                                        <p class="text-base text-secondary-full dark:text-mode-secondary-light">
                                            {{ $t('Choose a blueprint to install WordPress with pre-configured plugins and themes.') }}
                                        </p>
                                    </div>
                                    <Switch
                                        @click.prevent="enableBlueprint = !enableBlueprint"
                                        :checked="enableBlueprint"
                                    ></Switch>
                                </div>
                                <template v-if="enableBlueprint">
                                    <div class="flex flex-col gap-4 md:gap-6">
                                        <div class="grid grid-cols-[repeat(auto-fill,minmax(18rem,1fr))] gap-3 md:gap-4">
                                            <blue-print-card
                                                v-model="form.blueprint_id"
                                                contentClasses="!bg-light dark:!bg-mode-light"
                                                :blueprints="topBluePrints"
                                            />
                                        </div>
                                    </div>
                                    <div class="flex justify-end items-center gap-8 mt-4">
                                    <button
                                        @click.prevent="showMoreBlueprints = true"
                                        class="text-primary-light text-base flex items-center gap-2"
                                    >
                                        {{ $t('View all') }}
                                        <i class="xcloud xc-right-arrow text-sm"></i>
                                    </button>
                                    <button
                                        v-if="can_manage_blueprints"
                                        aria-expanded="true"
                                        aria-haspopup="true"
                                        class="flex gap-2 items-center justify-center rounded-md border-transparent shadow-none min-h-10 px-4 py-3 text-sm font-semibold text-white bg-primary-light focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50 whitespace-nowrap"
                                        @click.prevent="showBluePrintCreateModal = true"
                                    >
                                        <i class="xcloud xc-add"></i>
                                        <span>{{ $t('Create New Blueprint') }}</span>
                                    </button>
                                </div>
                                    <create-blue-print
                                        :blue-print-count="blueprints.length"
                                        :showBluePrintCreateModal="showBluePrintCreateModal"
                                        @close="closeBluePrintCreateModal"
                                    />
                                    <BlueprintModal
                                        :show="showMoreBlueprints"
                                        :widthClass="'max-w-1120px'"
                                        @close="showMoreBlueprints = false"
                                    >
                                        <template #header>
                                            <div class="flex wide-mobile:flex-wrap gap-2 items-center justify-between">
                                                <div class="flex flex-col gap-y-3">
                                                    <h4 class="text-2xl font-medium leading-none text-dark dark:text-white">
                                                        {{ $t('All Blueprints') }}
                                                    </h4>
                                                    <p class="text-base text-secondary-full dark:text-mode-secondary-light">
                                                        {{ $t('Manage your all blueprints as you like, you can edit, delete or create new from here') }}
                                                    </p>
                                                </div>
                                                <button
                                                    v-if="can_manage_blueprints"
                                                    aria-expanded="true"
                                                    aria-haspopup="true"
                                                    class="flex gap-2 items-center justify-center rounded-md border-transparent shadow-none min-h-10 px-4 py-3 text-sm font-semibold text-white bg-primary-light focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50 whitespace-nowrap ml-auto"
                                                    @click.prevent="
                                                        showMoreBlueprints = false;
                                                        showBluePrintCreateModal = true;
                                                    "
                                                >
                                                    <i class="xcloud xc-add"></i>
                                                    <span>{{ $t('Create New Blueprint') }}</span>
                                                </button>
                                            </div>
                                        </template>
                                        <div class="grid grid-cols-[repeat(auto-fill,minmax(18rem,1fr))] gap-3 md:gap-4">
                                            <blue-print-card
                                                v-model="form.blueprint_id"
                                                contentClasses="!bg-light dark:!bg-mode-light"
                                                :blueprints="blueprints"
                                            />
                                        </div>

                                        <template #footer>
                                            <div class="flex justify-end items-center">
                                                <button
                                                    aria-expanded="true"
                                                    aria-haspopup="true"
                                                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-40px px-6 py-3px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                                                    @click.prevent="showMoreBlueprints = false"
                                                >
                                                    <span>{{ $t('OK') }}</span>
                                                </button>
                                            </div>
                                        </template>
                                    </BlueprintModal>
                                </template>
                            </div>
                        </div>

                        <Advanced
                            @showAdvance="showAdvanced"
                            :accordion_button="false"
                            :switch="[
                                form?.database_provider,
                                form?.managed_database_mode,
                            ]"
                        >
                            <AdvancedFields
                                :show_caching_fields="false"
                                :form="form"
                                :server="server"
                                :siteDefault="siteDefault"
                                :availablePhpVersions="availablePhpVersions"
                                :availableWordPressVersions="
                                    availableWordPressVersions
                                "
                                @updateUserVal="updateUserFields"
                           />
                        </Advanced>
                    </form>
                </div>

                <WizardProgress
                    :processing="form.processing"
                    :back="route('site.create', {
                        server: server.id,
                        type: 'wordpress',
                    })"
                    @next="submit"
                    :progress-width="progressWidth(3, 3)"
                    progress-text="Step 3 of 3"
                >
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                    </template>
                    <span>{{ $t('Next') }}</span>
                </WizardProgress>
            </div>
        </div>
    </div>
 </template>

 <script setup>
 import { useNameGenerator } from "@/Composables/useNameGenerator";
 import AdditionalDomain from "@/Pages/Site/New/Components/AdditionalDomain.vue";
 import AdvancedFields from "@/Pages/Site/New/Components/Advanced.vue";
 import DNS from "@/Pages/Site/New/Components/DNS.vue";
 import Https from "@/Pages/Site/New/Components/Https.vue";
 import Advanced from "@/Shared/Advanced.vue";
 import TextInput from "@/Shared/TextInput.vue";
 import WizardProgress from "@/Shared/WizardProgress.vue";
 import { useHelpStore } from "@/stores/HelpStore";
 import { useNavigationStore } from "@/stores/NavigationStore";
 import {useForm, usePage} from "@inertiajs/inertia-vue3";
 import Multiselect from "@vueform/multiselect";
 import { computed, onMounted, ref, watch } from "vue";
 import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
 import Skeleton from "@/Shared/Table/Skeleton.vue";
 import { useFlash } from "@/Composables/useFlash";
 import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
 import CheckBoxSwitch from "@/Jetstream/CheckBoxSwitch.vue";
 import Error from "@/Shared/Error.vue";
 import BluePrintCard from "@/Pages/Site/New/Components/Bluetprints/BluePrintCard.vue";
 import BlueprintModal from "@/Shared/BlueprintModal.vue";
 import CreateBluePrint from "@/Pages/BluePrint/CreateBluePrint.vue";
 import Switch from "@/Shared/Switch.vue";
 import {Inertia} from "@inertiajs/inertia";
 import { useI18n } from "vue-i18n";
 import { userClickOutside } from "@/Shared/Events/userClickOutside";
 import Tooltip from "@/Shared/Tooltip.vue";
 const { t } = useI18n();

 let navigation = useNavigationStore();
 let helper = useHelpStore();
 let showMoreBlueprints = ref(false);
 let showBluePrintCreateModal = ref(false);

const dropdownRef = ref(null);
const isDropdownOpen = ref(false);

let props = defineProps({
    server: Object,
    siteDefault: Object,
    availablePhpVersions: Array,
    availableWordPressVersions: Array,
    tags: Array,
    prefix_id: [String, Number],
    hasCloudflareIntegration: Boolean,
    staging_domain: {
        type: String,
        default: "x-cloud.app",
    },
    blueprints: [Array,Object],
    can_manage_blueprints: Boolean,
    availableDomains: {
        type: Array,
        required: true,
    }
});

 const progressWidth = (start, end) => (start * 100) / (end ?? 1);
 let domainParkingMethod = ref("go_live");
 let brandName = usePage().props.value?.current_white_label ? usePage().props.value?.current_white_label?.branding?.brand_name : 'xCloud';
 const stagingHelper =
    brandName+ t(' offers a temporary test domain that allows you to quickly deploy your site. ') +
    t('This temporary domain enables you to share your work in progress with teammates or clients for review ') +
    t('and input before you finalize and launch it with your own custom domain for public access.');

 const domainNameHelpText = computed(() => {
    return !useCloudflareDns && !isIpSite ? 'Click <a class="underline" target="_blank" href="https://xcloud.host/docs/how-to-setup-dns-record-in-xcloud-server/">here</a>\n' +
        "to know how to setup your DNS provider." : '';
 });

 const dnsHelpText = computed(() => {
  const brandName = Inertia.page.props?.current_white_label
      ? Inertia.page.props.current_white_label.branding.brand_name
      : 'xCloud';
  return `Your DNS setup and SSL Certificate will be done by Cloudflare and managed by ${brandName}.`;
 });

 let form = useForm({
    name: "",
    title: "",
    type: "wordpress",
    additional_domains: [],
    tags: props.server.tags.map((tag) => tag.name),
    enable_full_page_cache: true,
    enable_redis_object_caching: props.server.stack === 'nginx', // redis_object_caching is only for nginx
    ssl_provider: "",
    ssl_certificate: "",
    ssl_private_key: "",
    site_user: props.siteDefault.site_user,
    deploy_script: props.siteDefault.deploy_script,
    admin_user: props.siteDefault.admin_user,
    admin_password: props.siteDefault.admin_password,
    admin_email: props.siteDefault.admin_email,
    database_name: props.siteDefault.database_name,
    database_user: props.siteDefault.database_user,
    database_password: props.siteDefault.database_password,
    database_host: props.siteDefault.database_host,
    database_port: props.siteDefault.database_port,
    prefix: props.siteDefault.prefix,
    wordpress_version: props.siteDefault.wordpress_version,
    php_version: props.siteDefault.php_version,
    database_provider: props.siteDefault.database_provider,
    managed_database_mode: props.siteDefault.managed_database_mode,
    managed_database_options: props.siteDefault.managed_database_options,
    domain_parking_method: domainParkingMethod,
    domain_active_on_cloudflare: "",
    cloudflare_account_id: "",
    cloudflare_zone_id: "",
    subdomain: "",
    site_name: "",
    use_xcloud_default_email_provider: true,
    enable_multisite: false,
    multisite_subdomain: false,
    blueprint_id: props.blueprints.find((blueprint) => blueprint.is_default)?.id,
    disable_search_engine_visibility: false,
    selected_staging_domain: props.staging_domain,
 });

 let showCloudflareDnsOption = ref(false);
 let useCloudflareDns = ref(false);
 let showDomainSetup = ref(true);
 let loader = ref(false);
 let enableBlueprint = ref(true);
 let userUpdateFields = [];

 const toggleDropdown = () => {
     isDropdownOpen.value = !isDropdownOpen.value;
 };

 const selectDomain = (domain) => {
     isDropdownOpen.value = false;
     form.selected_staging_domain = domain;
     checkStagingDomain();
 };

 userClickOutside(dropdownRef, () => {
     isDropdownOpen.value = false;
 });

 const updateUserFields = (field) => {
    if (!userUpdateFields.includes(field)) {
        userUpdateFields.push(field);
    }
 };

 const topBluePrints = computed(() => {
    const blueprint_list = props.blueprints.slice(0, 3);
    if (form.blueprint_id) {
        const onList = blueprint_list.some((blueprint) => blueprint.id == form.blueprint_id);
        const selected = props.blueprints.find((blueprint) => blueprint.id == form.blueprint_id);
        if (!onList && selected) {
            blueprint_list.splice(1, 1, selected);
        }
    }
    return blueprint_list;
 });

 const tagList = computed(() => {
    return props.tags.map((tag) => ({
        label:
            tag?.counter > 1 && !form.tags.includes(tag.value)
                ? tag.label + " (" + tag?.counter + ")"
                : tag.label,
        value: tag.value,
    }));
 });
 let submit = () => {
    maybeRemoveHttp();
    if (domainParkingMethod.value === "staging_env" && form.name === "") {
        //form.name = generateRandomString() + "." + props.staging_domain;
        form.name = generateRandomString() + "." + form.selected_staging_domain;
    }

    form
        .transform((data) => {
            data.blueprint_id = enableBlueprint.value ? data.blueprint_id : null;
            if(form.domain_parking_method === 'staging_env'){
              //data.name = form.name + '.' + props.staging_domain
              data.name = form.name + '.' + form.selected_staging_domain
            }
            return data;
        })
        .post(route("api.site.store", props.server.id), {
        preserveScroll: true,
    });
 };

 const showAdvanced = (isShow) => {
    helper.toggleAccordions([], [form.database_provider]);
 };

 watch(
    () => form.database_provider,
    () => {
        helper.toggleOpenWithAccordions([], [form.database_provider]);
    }
 );

 watch(
    () => form.multisite_subdomain,
    () => {
      form.ssl_provider = '';
      form.ssl_certificate = '';
      form.ssl_private_key = '';
      for (let i = 0; i < form.additional_domains.length; i++) {
        form.additional_domains[i].value = '';
      }
    }
 );

 watch(
    () => form.name,
    () => {
        maybeRemoveHttp();
        form.name = form.name.toLowerCase();
        const generated_name = useNameGenerator(form.name);

        if (!userUpdateFields.includes("site_user") || form.site_user.length === 0) {
            form.site_user =
                "u" +
                props.prefix_id +
                "_" +
                useNameGenerator(
                    form.name,
                    16 - ("u" + props.prefix_id + "_").length
                );
        }
        if (form.database_provider === "in_server" || form.database_provider === "from_provider") {
            if (!userUpdateFields.includes("database_name") || form.database_name.length === 0) {
                form.database_name = "db" + props.prefix_id + "_" + generated_name;
            }
            if (!userUpdateFields.includes("database_user") || form.database_user.length === 0) {
                form.database_user = "u" + props.prefix_id + "_" + generated_name;
            }
        }
        form.managed_database_options.do_cluster_name =
            form.managed_database_options.do_cluster_name ??
            "do-db-c-" +
                generateRandomString(4).toLocaleLowerCase() +
                "-" +
                generated_name;
        showCloudflareDnsOption.value =  !isIpSite.value;

      // check if the domain is already in use
      if (domainParkingMethod.value === "staging_env") {
        checkStagingDomain();
      }
    }
 );

 const isIpSite = computed(() => {
    return form.name.trim() === props.server.public_ip || form.name.trim().match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/);
 });

 const multiSiteOptions = computed(() => {
    if (isIpSite.value) {
        return [
            {
                label: "Subdirectory",
                value: false,
            },
        ];
    }
    return [
        {
            label: "Subdirectory",
            value: false,
        },
        {
            label: "Subdomain",
            value: true,
        },
    ];
 });

 watch(
    () => form.title,
    () => {
        if (domainParkingMethod.value === "staging_env") {
            const generated_name = useNameGenerator(form.title);
            form.site_user =
                "u" +
                props.prefix_id +
                "_" +
                useNameGenerator(
                    form.title,
                    16 - ("u" + props.prefix_id + "_").length
                );
            if (
                form.database_provider === "in_server" ||
                form.database_provider === "from_provider"
            ) {
                form.database_name =
                    "db" + props.prefix_id + "_" + generated_name;
                form.database_user =
                    "u" + props.prefix_id + "_" + generated_name;
            }
            form.managed_database_options.do_cluster_name =
                "do-db-c-" +
                generateRandomString(4).toLocaleLowerCase() +
                "-" +
                generated_name;
            if (form.title) {
                let additional = "." + props.staging_domain;


                let form_name = generated_name.replace(/[^a-zA-Z0-9-]/g, "");


                form.name =
                    form_name === ""
                        ? ""
                        : form_name +
                          "-" +
                          generateRandomString(4).toLocaleLowerCase()
            } else {
                form.name = "";
            }
        }
    }
 );

 watch(
    () => form.additional_domains,
    (newVal, oldVal) => {
        for (let i = 0; i < form.additional_domains.length; i++) {
            if (form.additional_domains[i].value !== "") {
                form.additional_domains[i].value = form.additional_domains[i]?.value.toLowerCase();
                // console.log(form.additional_domains[i].value)
                // if user adds additional domains, reset cloudflare integration(user will do manual dns setup)
                resetCloudflareIntegration();
                showDomainSetup.value = true;
            }
        }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
 );

 let hasAdditionalDomains = computed(() => {
    return (
        form.additional_domains.length > 0 &&
        form.additional_domains[0].value !== ""
    );
 });

 const resetCloudflareIntegration = function () {
    form.domain_active_on_cloudflare = false;
    form.cloudflare_account_id = "";
    form.cloudflare_zone_id = "";
    form.subdomain = "";
    form.site_name = "";
    useCloudflareDns.value = false;
 };

 watch(
    () => useCloudflareDns,
    (newVal, oldVal) => {
        if (useCloudflareDns.value) {
            if (form.name === "") {
                useCloudflareDns.value = false;
                useFlash().swal().fire({
                    icon: "error",
                    title: "Please add a domain name first",
                });
            } else {
                checkDomain();
            }
        } else {
            showDomainSetup.value = true;
            resetCloudflareIntegration();
        }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
 );

 let checkDomain = () => {
    loader.value = true;
    form.processing = true;

    axios
        .get(
            route(
                "api.user.integration.cloudflare.check-domain-exists",
                form.name
            )
        )
        .then((res) => {
            // console.log(res.data.response.domain_active_on_cloudflare)
            showDomainSetup.value =
                !res.data.response.domain_active_on_cloudflare;


            form.domain_active_on_cloudflare =
                res.data.response.domain_active_on_cloudflare;
            form.cloudflare_account_id = res.data.response.account_id;
            form.cloudflare_zone_id = res.data.response.zone_id;
            form.subdomain = res.data.response.subdomain;
            form.site_name = res.data.response.site_name;
            form.ssl_provider = "cloudflare";


            loader.value = false;
            form.processing = false;

            // useCloudflareDns.value = false;

            if (!res.data.response.domain_active_on_cloudflare) {
                useFlash().error(
                    "No cloudflare account found for this domain. Please add your domain to cloudflare first."
                );
            }
        })
        .catch((err) => {
            showDomainSetup.value = true;
            loader.value = false;
            form.processing = false;

            if (
                err.response &&
                err.response.data &&
                err.response.data.message
            ) {
                useFlash().error(err.response.data.message);
            } else {
                useFlash().error(
                    "An error occurred while processing your request."
                );
            }
        });
 };

 let checkStagingDomain = () => {
    if (domainParkingMethod.value === "staging_env" && form.title) {
        if(form.name.includes('.')){
          form.errors.name = "Staging/demo domains can not contain multi level domain.";
          return;
        }

        form.processing = true;
        axios
            //.get(route("api.site.check-domain-exists") + "?domain=" + form.name + "." + props.staging_domain)
            .get(route("api.site.check-domain-exists") + "?domain=" + form.name + "." + form.selected_staging_domain)
            .then((res) => {
                if (res.data.exists) {
                    form.errors.name =
                        "Looks like the domain is already in use. Please try changing the site title again.";
                } else {
                    form.errors.name = "";
                }
                form.processing = false;
            })
            .catch((err) => {
                form.processing = false;


                useFlash().error(err.errors.domain[0]);
            });
    }
 };

 function maybeRemoveHttp() {
    if (
        form.name &&
        (form.name.startsWith("http://") || form.name.startsWith("https://"))
    ) {
        form.name = form.name.replace(/^https?:\/\//, "");
    }
 }

 function generateRandomString(length = 5) {
    const characters =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(
            Math.floor(Math.random() * characters.length)
        );
    }
    return result;
 }

 const onTagSelect = (tag) => {
    //check does tag has contained any special character like #, @, $, %, ^, /, \, |, {, }, [, ], ~
    if (tag.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
        //remove from tag list
        form.tags = form.tags.filter((item) => item !== tag);
    }
 };

 function toggleStaging() {
    form.domain_parking_method = "staging_env";
    const generated_name = useNameGenerator(form.title);

    form.disable_search_engine_visibility = true;

    if ( form.site_user === props.siteDefault.site_user && form.title.length > 0) {
        form.site_user =
            "u" +
            props.prefix_id +
            "_" +
            useNameGenerator(
                form.title,
                16 - ("u" + props.prefix_id + "_").length
            );
    }
    if (form.title) {
        let additional = "." + props.staging_domain;

        let form_name = generated_name.replace(/[^a-zA-Z0-9-]/g, "");

        form.name =
            form_name === ""
                ? ""
                : form_name +
                  "-" +
                  generateRandomString(4).toLocaleLowerCase();
    } else {
        form.name = "";
    }
    checkStagingDomain();
 }
 const goLive = () => {
    form.domain_parking_method = "go_live";
    form.name = "";
    form.disable_search_engine_visibility = false;
 };

 const tagsErrors = function () {
    const errors = [];

    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
 };

 const closeBluePrintCreateModal = (data) => {
    if(data.hasOwnProperty('bluePrint')) {
        props.blueprints.splice(1, 0, data.bluePrint);
        form.blueprint_id = data.bluePrint.id;
    }
    showBluePrintCreateModal.value = false;
 };
 </script>
 <style>
 @import "@vueform/multiselect/themes/tailwind.css";
 </style>



