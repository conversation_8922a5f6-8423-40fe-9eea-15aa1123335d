<template><!DOCTYPE html>
    <div class="xc-container">
        <div class="flex-1 flex flex-col items-center small-laptop:mr-0"
             :class="helper.isOpen ? 'mr-400px small-laptop:mr-0' : null"
        >
            <div class="max-w-1050px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <h2
                    class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark dark:text-white mb-50px wide-mobile:text-28px mobile:text-2xl tablet:mb-40px wide-mobile:mb-30px">
                     <Link :href="route('server.show', server.id)">{{ server.name }}</Link>
                </h2>
                <HorizontalTab>
                    <!-- <Domain :server="server" /> -->
                    <!-- <Settings server="server" /> -->
                    <!-- <Plugin /> -->
                    <Database />
                    <!-- <Confirm /> -->
                </HorizontalTab>
                <div class="flex gap-30px items-center w-full mt-20 tablet:mt-50px mobile:mt-30px">
                    <div class="basis-100px mobile:basis-20">
                    <a href="#"
                        class="inline-flex items-center justify-center min-h-50px w-full border-1 border-primary-light dark:border-dark text-lg font-medium rounded-10px shadow-none text-primary-light dark:text-white bg-transparent hover:bg-primary-light dark:hover:bg-dark-light hover:text-white dark:hover:text-white hover:shadow-lg hover:shadow-primary-dark/30 dark:hover:shadow-white/10 transition ease-in-out duration-300 focus:outline-none">{{ $t('Back') }}</a>
                    </div>
                    <div class="flex-1">
                    <div class="relative max-w-356px text-center mx-auto">
                        <div class="h-1 rounded-full overflow-hidden bg-success-full/20">
                        <div class="transition-all ease-out duration-1000 h-full bg-green-500 w-1/3 rounded-full"></div>
                        </div>
                        <span
                        class="absolute left-0 right-0 top-2.5 block text-sm text-secondary-full dark:text-mode-secondary-light font-medium text-center">{{ $t('Step 1 of 3') }}</span>
                    </div>
                    </div>
                    <div class="basis-100px mobile:basis-20">
                    <a href="#"
                        class="inline-flex items-center justify-center min-h-50px w-full border-1 border-primary-light dark:border-dark text-lg font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light dark:bg-dark hover:bg-primary-light dark:hover:bg-mode-focus-light hover:border-primary-dark dark:hover:border-mode-focus-light hover:shadow-lg hover:shadow-primary-dark/30 dark:hover:shadow-white/10 transition ease-in-out duration-300 focus:outline-none">{{ $t('Next') }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {useNavigationStore} from "@/stores/NavigationStore.js";
import HorizontalTab from "@/Shared/HorizontalTab.vue";
import Database from "../../Migration/Database.vue";
import {useHelpStore} from "@/stores/HelpStore";

let navigation = useNavigationStore();
let helper = useHelpStore();
let props = defineProps({
    'server': Object,
})

</script>
