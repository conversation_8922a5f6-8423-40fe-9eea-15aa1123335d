<template>
    <Head :title="`Add Your New Site Into ${server.name}`"/>
    <div class="xc-container">

        <div class="flex-1 flex items-center" :class="navigation.isOpenHelpbar(helper.isOpen) && forceOpenHelpBar ? 'mr-400px small-laptop:mr-0' : 'mr-0'">
            <div class="max-w-1350px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark dark:text-white mb-50px wide-mobile:text-3xl mobile:text-28px tablet:mb-40px wide-mobile:mb-30px">
                    <span>{{ $t('Add Your New Site Into') }}</span>
                    <cloud-provider-logo
                        :provider-name="server?.provider_name ?? server?.cloud_provider_readable"
                        class="w-40px inline-flex mx-2.5"
                    />
                    <Link :href="route('server.show', server.id)" class="font-medium text-primary-dark">{{ server.name }}</Link>
                </h2>
                <!-- Free Billing Warning -->
                <div v-if="latestBill?.billing_plan?.name === 'free' && !$page?.props?.current_white_label"
                     class="warning flex items-center bg-warning/20 px-6 py-4 rounded-md mb-10">

                    <img :src="asset('img/warning.svg')"
                         alt="warning_img"
                         class="w-6"/>

                    <p class="text-sm text-dark dark:text-white leading-7 pl-3.5">
                        {{ $t('This server created under xCloud Free plan which includes 1 server and') }} {{ canAvailInTotal }} {{ $t('website with Self Hosting. To unlock full features, please consider upgrading the server bill from free to paid plan.') }}
                    </p>
                </div>

                <!-- Selecting site creating platform -->
                <SitePlatformSelections
                    @select-type="(type) => selectedType = type"
                    :siteTypes="siteTypes"
                    :defaultType="defaultType"
                    :serverId="server?.id"/>
                <!-- Site creation options -->
                <SiteCreationOptions
                    :selectedType="selectedType"
                    :server="server"
                />

                <div class="flex justify-center mt-5 md:mt-10">
                    <div v-if="error" class="text-sm text-red-600 block">{{ error }}</div>
                </div>

                <WizardProgress @next="error='Please select a a method.'" :progress-width="progressWidth(2,3)" progress-text="Step 2 of 3"
                                :has-next-button="false" :back="back"/>
            </div>
        </div>
    </div>
    <Modal
        :closeable="true"
        :show="monitoring?.disk?.isLow || monitoring?.ram?.isLow"
        :title="server?.name +' '+lowDiskRamText+' Low'"
        :widthClass="'max-w-850px'"
    >
        <!--<DiskSpaceWarning/>-->
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-warning/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img" class="w-6" />
                <p class="text-sm text-dark dark:text-white leading-7 pl-3.5">
                    <b>{{ $t('Warning') }}:</b> {{ $t('Your') }} {{ lowDiskRamText }} {{ $t('is low. Please upgrade your plan to avoid any downtime.') }}
                </p>
            </div>
        </div>
        <template #footer>
            <div class="px-50px py-30px wide-mobile:px-30px wide-mobile:py-20px">
                <button
                    @click.prevent="lowSpaceModal = false"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none
                          min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    aria-expanded="true" aria-haspopup="true"
                >
                    <span>{{ $t('Close') }}</span>
                </button>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import {onMounted, ref, watch} from 'vue'
import Modal from "@/Shared/Modal.vue"
import WizardProgress from '@/Shared/WizardProgress.vue'
import SiteCreationOptions from "@/Pages/Site/New/Components/SiteCreationOptions.vue";
import {useHelpStore} from "@/stores/HelpStore.js";
import {useNavigationStore} from "@/stores/NavigationStore";
import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
import {asset} from "laravel-vapor";
import SitePlatformSelections from './Components/SitePlatformSelections.vue';

let navigation = useNavigationStore();
let helper = useHelpStore();
let error = ref('');
const {server, monitoring, siteTypes, defaultType} = defineProps({
    canAvailInTotal: Number,
    latestBill: Object,
    server: Object,
    monitoring: Object,
    siteTypes: Object,
    defaultType: String,
    back: {
        type: String,
        default: "/site/create"
    },
    forceOpenHelpBar: {
        type: Boolean,
        default: true
    }
})
onMounted(() => {
    getLowDiskRam();
});

const selectedType = ref(defaultType ?? siteTypes[0].value);

watch(selectedType, (newType) => {
    const url = new URL(window.location.href);
    url.searchParams.set('type', newType);
    window.history.replaceState({}, '', url);
});

const lowDiskRamText = ref('')
const getLowDiskRam = () => {
    if (monitoring?.disk?.isLow) {
        lowDiskRamText.value = 'Disk Space'
    }

    if (monitoring?.ram?.isLow) {
        lowDiskRamText.value = 'RAM'
    }

    if (monitoring?.disk?.isLow && monitoring?.ram?.isLow) {
        lowDiskRamText.value = 'Disk Space and RAM'
    }
}

const progressWidth = (start, end) => (start * 100) / (end ?? 1);
</script>
