<template>
  <single-site :server="server" :site="site" active="Site Settings">
    <div :class="[site?.is_wordpress ? '' : 'grid grid-cols-2 small-laptop:grid-cols-1']">
      <div v-if="site?.permissions?.includes('site:settings')" class="xl:flex">
          <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px flex-1">
              <!-- PHP Version -->
              <php-settings
                  :defaultEnvVars="defaultEnvVars"
                  :available-php-versions="availablePhpVersions"
                  :php_version="php_version"
                  :site="site"
                  :server="server"
                  :can_perform_support_level_action="can_perform_support_level_action"
              />
              <!-- update web root card -->
              <Card
                  :loading="webRootForm.processing"
                  v-if="site?.permissions?.includes('site:settings') && !site?.is_wordpress"
                  :title="$t('Update Web Root')"
                  :button-title="$t('Update')"
                  @onSubmit="updateWebRoot"
                  class="w-full"
              >
                  <text-input
                      v-model="webRootForm.web_root"
                      id="web_root"
                      :error="webRootForm.errors.web_root"
                      :label="$t('Web Root')"
                  />
              </Card>
          </div>
          <div v-if="site?.is_wordpress" class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px flex-1"
          >
              <!-- WP-Cron and xCloud-Cron -->
              <Card
                  v-if="site?.permissions?.includes('site:manage-wpconfig') && site?.is_wordpress"
                  :title="$page?.props?.current_white_label ? 'WP-Cron and '+$page?.props?.current_white_label?.branding?.brand_name+'-Cron' : $t('WP-Cron and xCloud-Cron')"
                  buttonStyle="success"
                  :button-title="$t('Save Settings')"
                  @onSubmit="updateWpCron"
                  :loading="wpCronForm.processing"
                  :disable-use-button="wpCronForm.processing || !server.is_connected || site?.is_disabled"
              >
                  <skeleton v-if="loading || !server.is_connected"
                            :has-header="false"
                            :rows="5"
                            :columns="1"
                            row-div-classes="h-5"
                            rowClasses="h-20"
                  />
                  <template v-else>
                      <fieldset>
                          <div>
                              <RadioInput v-model="wpCronForm.enable_wp_cron" :value="true" @click.prevent="wpCronForm.enable_wp_cron = true">
                                  {{ $t('WP-Cron') }}
                              </RadioInput>
                              <p class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-10px">
                                  {{ $t('WP-Cron manages time-based tasks in WordPress, relying on site visits.') }}
                              </p>
                          </div>

                          <div class="mt-20px">
                              <RadioInput v-model="wpCronForm.enable_wp_cron" :value="false" @click.prevent="wpCronForm.enable_wp_cron = false">
                                  {{ $page?.props?.current_white_label ? $page?.props?.current_white_label?.branding?.brand_name : 'xCloud'}}-Cron
                              </RadioInput>
                              <p class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-15px mobile:text-xs">
                             {{ $page?.props?.current_white_label ? $page?.props?.current_white_label?.branding?.brand_name : 'xCloud'}}-Cron does the same thing at the server level, ensures scheduled tasks like post publishing occur reliably at specified intervals.
                              </p>
                          </div>
                      </fieldset>
                      <select-input
                          v-if="!wpCronForm.enable_wp_cron"
                          v-model="wpCronForm.wp_cron_interval"
                          :error="wpCronForm.errors.wp_cron_interval"
                          id="wp_cron_interval"
                          :label="$t('Cron Interval for Server')">
                          <option v-for="(cron_interval,cron_value) in wp_cron_intervals" :value="cron_value" v-text="cron_interval"/>
                          </select-input>
                          <suggestion
                           type="slot"
                            :light-mode="true"
                            >
                {{ $t('If you want to also add additional custom cronjob then you can configure on your server from') }}
                <Link
                  :href="route('server.cron.job', { server: server.id })"
                  class="underline"
                >
                  {{ $t('here') }}
                </Link>
              </suggestion>
            </template>
          </Card>
        </div>
      </div>
      <div v-if="site?.is_wordpress">
        <div v-if="site?.permissions?.includes('site:manage-wpconfig')" class="xl:flex">
          <div
            :class="{
              'grid-cols-2 wide-tablet:grid-cols-1 mobile:grid-cols-1': site?.permissions?.includes('site:manage-wpconfig') && site?.permissions?.includes('site:settings') && site?.is_wordpress,
              'grid-cols-1': !site?.permissions?.includes('site:manage-wpconfig') || !site?.permissions?.includes('site:settings') || !site?.is_wordpress,
            }"
            class="grid gap-30px pl-30px pt-30px mobile:gap-20px mobile:pl-20px mobile:pt-20px flex-1"
          >
            <!-- Site Tags -->
            <Card
              :title="$t('Edit Site Tags')"
              buttonStyle="success"
              :button-title="$t('Save Settings')"
              @onSubmit="submit"
            >
              <multiselect
                @select="onTagSelect"
                class="mt-3 !ml-0 !min-w-full"
                :class="{ 'ring-1 ring-danger-light': tagsErrors() }"
                v-model="form.tags"
                mode="tags"
                :close-on-select="false"
                :searchable="true"
                :create-option="true"
                :placeholder="$t('Select or create tags')"
                :options="tagList"
                regex="^[a-zA-Z0-9_ -]+$"
              />
              <Error :error="tagsErrors()" />
            </Card>
            
            <!-- WP Debug -->
            <Card
              v-if="site?.permissions?.includes('site:manage-wpconfig')"
              :title="$t('WP Debug')"
              buttonStyle="success"
              :button-title="$t('Save Settings')"
              @onSubmit="updateWpDebug"
              :disableUseButton="wpDebugForm.processing || !server.is_connected || wpDebugForm.initializing || site?.is_disabled"
              :loading="wpDebugForm.processing"
            >
              <div>
                <template v-if="wpDebugForm.initializing">
                  <skeleton :rows="1" :columns="1" />
                </template>
                <Switch
                  v-else
                  :checked="wpDebugForm.enable_wp_debug"
                  @click.prevent="wpDebugForm.enable_wp_debug = !wpDebugForm.enable_wp_debug"
                >
                  <span class="mr-2 mobile:text-sm">{{ $t('Enable WP Debug') }}</span>
                </Switch>
              </div>
              <suggestion type="slot" :light-mode="true">
                {{ $t('Enable this to view WordPress debug logs on the') }}
                <Link
                  :href="route('site.logs', [server.id, site.id])"
                  class="underline"
                >
                  {{ $t("site's logs page") }}
                </Link>.
              </suggestion>
            </Card>
          </div>
        </div>
      </div>
      <div class="flex flex-col">
        <div v-if="site?.permissions?.includes('site:settings')">
          <div
              class="grid wide-tablet:grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px flex-1"
              :class="[site?.is_wordpress? 'grid-cols-2 ':'grid-cols-1 ']">
            <Card
              :use-button="can_perform_support_level_action"
              :title="$t('Rescue Site')"
              buttonStyle="success"
              :button-title="$t('Run Now')"
              @onSubmit="updateSiteIsolation"
              :disableUseButton="siteRescueForm.processing || !server.is_connected || site?.is_disabled"
              :loading="siteRescueForm.processing"
            >

                <Switch
                  :checked="siteRescueForm.isolate_user"
                  @click.prevent="siteRescueForm.isolate_user = !siteRescueForm.isolate_user"
                >
                  <span class="mr-2 mobile:text-sm">{{ $t('Repair Site User') }}</span>
                </Switch>
                <Switch
                  :checked="siteRescueForm.directory_permissions"
                  @click.prevent="siteRescueForm.directory_permissions = !siteRescueForm.directory_permissions"
                >
                  <span class="mr-2 mobile:text-sm">
                    {{ $t('Update Directory Permissions') }}
                  </span>
                </Switch>
                <Switch
                  :checked="siteRescueForm.reinstall_php"
                  @click.prevent="siteRescueForm.reinstall_php = !siteRescueForm.reinstall_php"
                >
                  <span class="mr-2 mobile:text-sm">
                    {{ $t('Repair PHP') }} {{ php_version }}
                  </span>
                </Switch>
                <Switch
                  :checked="siteRescueForm.regenerate_nginx"
                  @click.prevent="siteRescueForm.regenerate_nginx = !siteRescueForm.regenerate_nginx"
                >
                  <span class="mr-2 mobile:text-sm">
                    {{ $t('Regenerate') }} {{ server.stack === 'nginx' ? 'Nginx' : 'OpenLiteSpeed' }} Configuration
                  </span>
                </Switch>
                <Switch
                  v-if="server.stack === 'nginx' && siteRescueForm.regenerate_nginx"
                  :checked="siteRescueForm.restart_nginx"
                  @click.prevent="siteRescueForm.restart_nginx = !siteRescueForm.restart_nginx"
                >
                  <span class="mr-2 mobile:text-sm">{{ $t('Restart Nginx') }}</span>
                </Switch>
           
              <suggestion
                :light-mode="true"
                :message="$t('It is recommended to keep the above options turned on before running the rescue action.')"
              />
            </Card>
            <!-- Site Indexing Settings -->
            <Card
              v-if="site?.permissions?.includes('site:settings') && !is_playground && site?.is_wordpress"
              :title="$t('Search Engine Visibility')"
              buttonStyle="success"
              :button-title="$t('Save Settings')"
              @onSubmit="updateSiteSearchEngineVisibilitySettings"
              :disableUseButton="siteVisibilitySettingsForm.processing || !server.is_connected"
              :loading="siteVisibilitySettingsForm.processing"
              :use-button="can_perform_support_level_action"
            >
              <Switch
                :checked="siteVisibilitySettingsForm.search_engine_visibility"
                @click.prevent="siteVisibilitySettingsForm.search_engine_visibility = !siteVisibilitySettingsForm.search_engine_visibility"
              >
                <span class="mr-2 mobile:text-sm">
                  {{ $t('Discourage search engines from indexing this site') }}
                </span>
              </Switch>
              <suggestion
                :light-mode="true"
                :message="$t('It is up to search engines to honor this request.')"
              />
            </Card>
          </div>
        </div>
        <div v-if="site?.permissions?.includes('site:disable') || site?.permissions?.includes('site:settings') || site?.permissions?.includes('site:delete')">
              <div
                  :class="{
                      'grid-cols-2 wide-tablet:grid-cols-1': site?.permissions?.includes('site:disable') && site?.permissions?.includes('site:settings'),
                      'grid-cols-1': !site?.permissions?.includes('site:disable') || !site?.permissions?.includes('site:settings'),
                      'grid grid-cols-2':site?.is_wordpress,
                  }"
                  class="flex flex-col gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <!-- Disable Site -->
            <Card
              v-if="site?.permissions?.includes('site:disable') && !is_playground"
              :title="$t('Disable Site')"
              buttonStyle="success"
              :button-title="$t('Save Settings')"
              @onSubmit="updateSiteState"
              :disableUseButton="wpStateForm.processing || !server.is_connected || wpStateForm.initializing"
              :loading="wpStateForm.processing"
              :use-button="can_perform_support_level_action"
            >
                <Switch
                  :checked="wpStateForm.is_disable"
                  @click.prevent="wpStateForm.is_disable = !wpStateForm.is_disable"
                >
                  <span class="mr-2 mobile:text-sm">{{ $t('Disable Site') }}</span>
                </Switch>
                <Switch
                  v-if="wpStateForm.is_disable"
                  :checked="wpStateForm.disable_site_cron"
                  @click.prevent="wpStateForm.disable_site_cron = !wpStateForm.disable_site_cron"
                >
                  <span class="mr-2 mobile:text-sm">
                    {{ $t('Also Disable Cron For This Site') }}
                  </span>
                </Switch>
              
              <div v-if="wpStateForm.is_disable">
                <label
                  class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none mobile:text-sm"
                >
                  {{ $t('Disable HTML Markup') }}
                </label>
                <skeleton v-if="wpStateForm.processing" :rows="5" :columns="1" />
                <v-ace-editor
                  v-else
                  :options="{ fontSize: '10px' }"
                  @init="editorInit"
                  v-model:value="wpStateForm.disable_html"
                  lang="html"
                  :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
                  style="height: 200px"/>
              </div>
              <suggestion
                :light-mode="true"
                :message="$t('After turning on Disable Site, you won’t be able to use this site on the web anymore along with losing access through SFTP/SSH. Plus, all the scheduled tasks for the site will be paused once you turn on Disable Cron For This Site.')"
              />
            </Card>
            <!-- Delete Site -->
            <Card
              v-if="site?.permissions?.includes('site:delete')"
              :title="$t('Delete Site Confirmation')"
              buttonStyle="danger"
              :button-title="$t('Delete Site')"
              @onSubmit="openDeleteModal = true"
              class="w-full"
            >
              <suggestion
                :light-mode="true"
                :message="$t('This action is irreversible. Delete sites cannot be restored.')"
              />
            </Card>
          </div>
        </div>
      </div>
    </div>
    <DeleteSite
      :status="site.status"
      :server-id="server.id"
      :site-id="site.id"
      :site-name="site.name"
      :openDeleteModal="openDeleteModal"
      @closeModal="openDeleteModal = false"
    />
    <modal
      :closeable="true"
      v-if="showNginxModal"
      :show="showNginxModal"
      @close="showNginxModal = false"
      :title="conf_file_name"
      :widthClass="'max-w-950px'"
      @footer-click="showNginxModal = false"
      :footer-button-title="$t('Close')"
      :footer-button="true"
    >
      <div class="flex flex-col">
        <skeleton v-if="isReadingConf" :columns="1" :rows="10" />
        <v-ace-editor
          v-else
          :readonly="true"
          :options="{ fontSize: '10px' }"
          @init="editorInit"
          v-model:value="nginxConf"
          lang="text"
          :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
          style="height: 500px"
        />
      </div>
    </modal>
  </single-site>
</template>

<!-- script -->
<script setup>

import SingleSite from "@/Pages/Site/SingleSite.vue";
import DeleteSite from "@/Pages/Site/Components/DeleteSite.vue";
import {computed, onMounted, ref} from "vue";
import Card from "@/Shared/Card.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import Multiselect from '@vueform/multiselect';
import SelectInput from "@/Shared/SelectInput.vue";
import Switch from "@/Shared/Switch.vue";
import TextInput from "@/Shared/TextInput.vue";
import {useFlash} from "@/Composables/useFlash";
import Modal from "@/Shared/Modal.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import {VAceEditor} from "vue3-ace-editor";
import {useNavigationStore} from "@/stores/NavigationStore";
import RadioInput from "@/Shared/RadioInput.vue";
import Error from "@/Shared/Error.vue";
import Authentication from "@/Pages/Site/Authentication.vue";
import 'ace-builds/src-noconflict/mode-nginx';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/theme-tomorrow_night_blue';
import 'ace-builds/src-noconflict/ext-searchbox';
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import PhpSettings from "@/Pages/Site/Components/PhpSettings.vue";
import Checkbox from "@/Jetstream/Checkbox.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

let navigation = useNavigationStore();
let openDeleteModal = ref(false);

const {
site, server, tags, availablePhpVersions,
php_version, enable_php_execution_on_upload_directory,
enable_xml_rpc,modify_x_frame_options,x_frame_options,
disable_nginx_config_regeneration,enable_wp_cron,wp_cron_interval, enable_7g_firewall, enable_8g_firewall, disable_html ,
disable_site_cron, search_engine_visibility
} = defineProps({
  site: Object,
  server: Object,
  tags: Object,
  availablePhpVersions: Object,
  php_version: String,
  conf_file_name: String,
  enable_php_execution_on_upload_directory: Boolean,
  enable_xml_rpc: Boolean,
  disable_nginx_config_regeneration: {
      type: Boolean,
      default: false
  },
  disable_site_cron: {
      type: Boolean,
      default: true
  },
  modify_x_frame_options: {
      type: Boolean,
      default: false
  },
  disable_html: String,
  x_frame_options: {
      type: String,
      default: 'SAMEORIGIN'
  },
  enable_wp_cron: Boolean,
  wp_cron_interval: String,
  wp_cron_intervals: [Array, Object],
  username: String,
  has_basic_auth: Boolean,
  enable_7g_firewall: {
    type: Boolean,
    default: false
  },
  is_playground: {
    type: Boolean,
    default: false
  },
  enable_8g_firewall: {
    type: Boolean,
    default: false
  },
  defaultEnvVars: Object,
  search_engine_visibility: {
      type: Boolean,
      default: false
  },
  can_perform_support_level_action: Boolean
});



let form = useForm({
tags: site.tags.map(tag => tag.name),
search_engine_visibility: site.search_engine_visibility,
});
const nginxConf = ref('');

const loading = ref(false);

const showNginxModal = ref(false);
const isReadingConf = ref(false);
let nginxOptionsForm = useForm({
  disable_nginx_config_regeneration: disable_nginx_config_regeneration,
  enable_php_execution_on_upload_directory: enable_php_execution_on_upload_directory,
  enable_xml_rpc: enable_xml_rpc,
  modify_x_frame_options: modify_x_frame_options,
  x_frame_options: x_frame_options,
  enable_7g_firewall: enable_7g_firewall,
  enable_8g_firewall: enable_8g_firewall,
});

const wpCronForm = useForm({
  enable_wp_cron: enable_wp_cron,
  wp_cron_interval: wp_cron_interval
});

let wpDebugForm = useForm({
  initializing: true,
  enable_wp_debug: false,
});

const siteVisibilitySettingsForm = useForm({
  search_engine_visibility: search_engine_visibility,
});

const updateMessage = server.stack==='nginx' ?  'Nginx options have been updated successfully' : 'OpenLiteSpeed options have been updated successfully'

onMounted(() => {
  if (server.is_connected && site?.is_wordpress){
      loadCronData();
      if(site?.is_disabled){
          loadDisableHtml();
      }
      // load site indexing settings from wordpress
      if (site?.status === 'provisioned') {
          loadSiteVisibilitySettings();
      }
      loadWpDebug();
  }

  if (!server.is_connected){
      useFlash().error("Server is not connected");
  }
});

const loadSiteVisibilitySettings = () => {
  axios.get(route('api.site.indexing-settings.get', {server: server.id, site: site.id}))
      .then(({data}) => {
          siteVisibilitySettingsForm.search_engine_visibility = data?.searchEngineVisibility;
      }).catch(({response}) => {
      if (response?.status === 422) {
          useFlash().error(response?.data?.message ?? 'Failed to load indexing settings');
      } else {
          useFlash().error("Failed to load indexing settings");
      }
  });
}

const loadWpDebug = () => {
  wpDebugForm.initializing = true;
  axios.get(route('site.wp.debug', [server.id, site.id])).then(response => {
      wpDebugForm.enable_wp_debug = response.data.enable_wp_debug;
  }).catch(error => {
      wpDebugForm.enable_wp_debug = false;
  }).then(() => {
      wpDebugForm.initializing = false;
  });
}
const loadCronData = () => {
  if (site?.is_wordpress) {
      loading.value = true;
      //get current wp cron option
      axios.get(route('api.site.wpCron.get', {server: server.id, site: site.id}))
          .then(({data}) => {
              wpCronForm.enable_wp_cron = !data?.wp_cron;
          }).catch(({response}) => {
          if (response?.status === 422) {
              useFlash().error(response?.data?.message ?? 'Failed to load wp cron settings');
          } else {
              useFlash().error("Failed to load wp cron settings");
          }
      }).finally(() => {
          loading.value = false;
      });
  }
}
const loadDisableHtml = () => {
  if (site?.is_wordpress) {
      wpStateForm.processing = true;
      //get current wp cron option
      axios.get(route('api.site.disable-html.get', {server: server.id, site: site.id}))
          .then(({data}) => {
              wpStateForm.disable_html = data?.html;
          }).catch(({response}) => {
          if (response?.status === 422) {
              useFlash().error(response?.data?.message ?? 'Failed to load disable html');
          } else {
              useFlash().error("Failed to load disable html");
          }
      }).finally(() => {
          wpStateForm.processing = false;
      });
  }
}
let updateNginxConf = () => {
  nginxOptionsForm.processing = true;
  nginxOptionsForm.post(route('api.site.nginxOptions.update', {server: server.id, site: site.id}), {
      preserveScroll: true,
      onSuccess: () => {
          useFlash().success(updateMessage);
      }
  });
}

let updateWpDebug = () => {
  wpDebugForm.post(route('api.site.wpDebug.update', {server: server.id, site: site.id}), {
      preserveScroll: true
  });
}

const tagList = computed(() => {
  return tags.map((tag) =>  ({
          label: (tag?.counter>1) && !form.tags.includes(tag.value) ?  tag.label+' ('+tag?.counter+')' : tag.label,
          value: tag.value,
      })
  )
})

const submit = () => {
  form.put(route('api.site.tags.update', {server: server.id, site: site.id}), {
      preserveScroll: true
  });
}

const editorInit = (editor) => {
  const mode = 'nginx';
  editor.getSession().setMode(`ace/mode/${mode}`);
}

const onTagSelect = (tag) => {
  //check does tag has contained any special character like #, @, $, %, ^, /, \, |, {, }, [, ], ~
  if (tag.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
      //remove from tag list
      form.tags = form.tags.filter((item) => item !== tag);
  }
}
const updateWpCron = () => {
  wpCronForm.post(route('api.site.wpCron.update', {server: server.id, site: site.id}), {
      preserveScroll: true
  });
}
const previewNginxConf = () => {
  isReadingConf.value = true;
  axios.get(route('api.site.nginxOptions.preview', {server: server.id, site: site.id}))
      .then(({data}) => {
          showNginxModal.value = true;
          nginxConf.value = data?.content;
      }).catch(({response}) => {
          if (response?.status===422){
              useFlash().error(response?.data?.message?? 'Failed to load nginx options');
          }else{
              useFlash().error("Failed to load nginx options");
          }
          showNginxModal.value = false;
      }).finally(() => {
          isReadingConf.value = false;
      });
}

const wpStateForm = useForm({
  is_disable: site?.is_disabled,
  disable_site_cron: true,
  disable_html: disable_html
});

const siteRescueForm = useForm({
  isolate_user: true,
  directory_permissions: true,
  regenerate_nginx: false,
  restart_nginx: false,
  reinstall_php: false,
})

const webRootForm = useForm({
  web_root: site.web_root,
});
const updateSiteState = () => {
  useFlash().deleteConfirmation(
      {
          title: wpStateForm.is_disable ?  t('Disable Site') : t('Enable Site'),
          text: wpStateForm.is_disable ? t('Disabling the site will make it inaccessible. Are you sure you want to disable it?') : t('Enabling the site will make it accessible. Are you sure you want to enable it?'),
          confirmButtonText: wpStateForm.is_disable ? t('Yes, Disable') : t('Yes, Enable'),
      },
      () => {
          wpStateForm.post(route('api.site.state.update', {server: server.id, site: site.id}), {
                  preserveScroll: true
              },
              {
                  onSuccess: () => {
                      useFlash().success('Site state has been updated successfully');
                  }
              });
      }
  );
}
const updateSiteIsolation = () => {
  if (Object.values(siteRescueForm.data()).some((item) => item === true)) {
      useFlash().deleteConfirmation(
          {
              title: t("Rescue Site"),
              text: t("This action will run the rescue process on the site. Are you sure you want to run it?"),
              width: '600px',
              confirmButtonText: t('Yes, Run Now')
          },
          () => {
              axios.post(route('api.site.rescue', {server: server.id, site: site.id}), siteRescueForm.data())
                  .then(({data}) => {
                      useFlash().success(data?.message ?? 'Site user isolation and permissions have been updated successfully');
                  }).catch(error => {
                  console.log(error);
                  useFlash().error('Failed to update site isolation settings');
              });
          }
      );
  }else{
      useFlash().error('Please select at least one option to update');
  }
}
const tagsErrors = function() {
  const errors = [];

  form.tags.forEach((tag, index) => {
      const error = form.errors[`tags.${index}`] ?? null;
      if (error) {
          errors.push(error);
      }
  });

  return errors.length ? errors[0] : null;
};

const updateSiteSearchEngineVisibilitySettings = () => {
  siteVisibilitySettingsForm.post(route('api.site.indexing-settings.update', {server: server.id, site: site.id}), {
      preserveScroll: true,
      onSuccess: () => {
          useFlash().success('Site indexing settings have been updated successfully');
      }
  });
}

const updateWebRoot = () => {
  webRootForm.clearErrors();
  webRootForm.post(route('api.site.web-root.update', {server: server.id, site: site.id}), {
      preserveScroll: true,
      onSuccess: () => {
          useFlash().success('Web root has been updated successfully');
      }
  });
}
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
