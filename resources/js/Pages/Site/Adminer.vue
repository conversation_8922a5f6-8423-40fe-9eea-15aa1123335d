<template>
  <single-site :server="server" :site="site" active="Database">
    <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
      <!-- phpMyAdmin Card -->
      <PhpMyAdminCard
        :server="server"
        :phpMyAdminSite="phpMyAdminSite"
        :site="site"
        :useSiteSpecificLogin="true"
      />

      <!-- Adminer Card -->
      <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col pb-0" v-if="hasDatabase && is_adminer_available">
        <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center
              wide-mobile:px-15px gap-20px mobile:gap-1">
          <tooltip :title="form.processing ? 'Please wait while we are processing your request' : ''">
            <Switch
                :disabled="form.processing || site?.is_disabled"
                @click.prevent="toggleAdminer"
                :checked="form.enable_adminer && !site?.is_disabled"
            >
              <span class="mr-2">{{ $t('Enable Adminer') }}</span>
              <span class="inline-flex items-center">
                  <i class="xcloud xc-verify_dns animate-spin items-center origin-center inline-flex pt-[0.011em]"
                     v-show="form.processing"
                  />
              </span>
            </Switch>
          </tooltip>
        </div>

          <div class="w-full px-30px pt-30px h-full bg-white dark:bg-mode-base grid grid-cols-3 tablet:grid-cols-1 gap-30px wide-mobile:grid-gap-20px">

              <text-input
                  type="text"
                  label="Database Name"
                  :modelValue="db_name"
                  disabled
              >
                  <CopyButton align="middle" :content="db_name"></CopyButton>
              </text-input>
              <text-input
                  type="text"
                  label="Database User"
                  :modelValue="db_user"
                  disabled
              >
                  <CopyButton align="middle" :content="db_user"></CopyButton>
              </text-input>
              <text-input
                  class="w-full"
                  type="text"
                  label="Database Password"
                  :modelValue="'*'.repeat(db_password.length)"
                  disabled
              >
                  <CopyButton align="middle" :content="db_password"></CopyButton>
              </text-input>
          </div>

        <div class="pl-30px pb-30px pr-30px h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid
              tablet:grid-cols-1 tablet:gap-10px mb-2"
        >
          <div class="mt-5" v-if="enable_7g_firewall">
            <suggestion
                :lightMode="true"
                :message="message"
            />
          </div>

          <div class="flex flex-col items-start">
            <div class="flex items-center">
              <img :src="asset('img/adminer-logo.png')" alt="" class="h-1/2">
            </div>

            <form class="w-full">
              <p class="text-secondary-full dark:text-secondary-light">
                  {{ $t('Adminer can enhance your database management experience. Activate it only when necessary to ensure optimal security measures.') }}
              </p>
                <suggestion
                    class="mt-4"
                    :message="$t('Adminer and File Manager won\'t work with 7G/8G firewall. We recommend SFTP if you\'re familiar with it.')"
                />

                <suggestion
                    v-if="disabled_adminer_time && enable_adminer"
                    class="mt-4"
                    :message="$t('The Adminer will be disable within')+' '+disabled_adminer_time+'.'"
                />
              <div class="p-4 border-1 flex items-center wide-mobile:flex-col wide-mobile:items-start border-solid border-secondary-light
                    dark:border-dark rounded-md mt-6 gap-2">
                <div class="flex flex-col gap-1.5">
                  <h5 class="text-lg font-medium leading-tight tracking-tight text-dark dark:text-white">
                      {{ $t('Database Manager') }}
                  </h5>
                    <p class="text-secondary-full dark:text-secondary-light">
                        {{ $t('Manage your databases with Adminer, an opensource database management tool.') }}
                    </p>
                </div>
                  <tooltip
                      class="ml-auto rounded-10px bg-success-full dark:bg-dark inline-flex items-center justify-center text-white shrink-0"
                      :title="form.enable_adminer?'':'Enable Adminer to manage database'"
                      align="bottom"
                  >
                    <a
                        :target="form.enable_adminer && !site?.is_disabled?'_blank':'_self'"
                        :href="form.enable_adminer && !site?.is_disabled ?route('site.adminer-login', [props.site.id]):'javascript:void(0)'"
                        class="px-4 py-3.5"
                            :class="{'cursor-not-allowed opacity-50' : form.processing || !form.enable_adminer || site?.is_disabled}"
                    >
                      <span class="inline-flex items-center justify-center ml-1 mr-2.5 text-xl">
                          <i class="xcloud xc-logout rotate-180"></i>
                      </span>
                      <span class="text-white">{{ $t('Launch Adminer') }}</span>
                    </a>
                  </tooltip>
              </div>
            </form>
          </div>
        </div>


      </div>
    </div>
  </single-site>
</template>

<!-- script -->
<script setup>
import SingleSite from "@/Pages/Site/SingleSite.vue";
import Switch from "@/Shared/Switch.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash.js";
import Tooltip from "@/Shared/Tooltip.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import { useI18n } from "vue-i18n";
import TextInput from "@/Shared/TextInput.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import PhpMyAdminCard from "@/Components/PhpMyAdminCard.vue";
const { t } = useI18n();

const props = defineProps({
    site: Object,
    server: Object,
    hasDatabase: {
      type: Boolean,
      default: true,
    },
    enable_adminer: [Boolean, String],
    enable_7g_firewall: {
        type: Boolean,
        default: false,
    },
    db_user: String,
    db_name: String,
    db_password: String,
    disabled_adminer_time: String,
    phpMyAdminSite: {
        type: Object,
        default: () => ({})
    },
    is_adminer_available: {
        type: Boolean,
        default: true,
    }
});

let form = useForm({
  enable_adminer: props.enable_adminer,
});

const disable7GRoute = route('site.settings', [props.server.id, props.site.id]);

let message = `
    <p class="text-sm text-dark dark:text-white leading-tight">
        This website is protected by a 7G firewall, which currently restricts the use of certain tools, including Adminer.
        To access Adminer, you must disable the 7G firewall from
      <a href="${disable7GRoute}" class="underline text-secondary-full dark:text-secondary-light">here</a>
    </p>
  `;


let toggleAdminer = () => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        useFlash().deleteConfirmation({
            title: form.enable_adminer ? t('Do you want to deactivate Adminer?') : t('Do you want to activate Adminer?'),
            text: t('We recommend deactivating it when not required.'),
        }, () => {
            form.enable_adminer = !form.enable_adminer;
            form.post(route('api.site.adminer', [props.server.id, props.site.id]), {preserveScroll: true});
        })
    }
}
</script>
