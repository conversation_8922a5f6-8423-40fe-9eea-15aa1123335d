<template>
    <!--object caching pro -->
    <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
        <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col pb-0">
            <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center
            wide-mobile:px-15px gap-20px mobile:gap-1"
            >
                <Switch
                    :checked="form.isEnable"
                    @click.prevent="confirmRedisUpdateCaching"
                >
                  <span class="mr-2 cursor-pointer">
                    {{ $t('Object Cache Pro') }}
                  </span>
                <span class="inline-flex items-center">
                    <i class="xcloud xc-verify_dns animate-spin items-center origin-center inline-flex pt-[0.011em] cursor-pointer"
                       v-show="false">
                    </i>
                  </span>
                </Switch>
            </div>

            <div class="flex flex-col" v-if="form.isEnable">
                <div class="p-30px h-full bg-white dark:bg-mode-base rounded-b-md flex flex-col tablet:grid tablet:grid-cols-1 ">
                    <div class="flex items-center">
                        <img :src="asset('img/integration/object-cache-pro.svg')" alt="" class="h-11 w-11">
                        <h4 class="text-dark dark:text-white text-lg font-medium leading-tight tracking-tighter -mt-2 ml-3">
                            {{ $t('Object Cache Pro') }}
                        </h4>
                    </div>
                    <p class="text-secondary-full dark:text-secondary-light mt-3">
                        {{ $t('Boost your WordPress site’s performance with Object Cache Pro by caching frequently accessed data in Redis. This reduces database queries, enhances speed, and is especially beneficial for dynamic, content-heavy websites.') }}
                    </p>

                    <div class="py-5">
                        <label class="block text-base font-medium text-secondary-full dark:text-light leading-none" for="site_id">
                            {{ $t('Select Integrated Plugin') }}
                        </label>
                        <div class="grid items-center grid-cols-1 md:grid-cols-3 md:flex-row gap-30px py-6">
                            <div>

                                <Multiselect
                                    @search-change="searchPlugin($event)"
                                    :can-clear="false"
                                    id="site_id"
                                    class="!ml-0 w-full !min-w-full"
                                    v-model="form.plugin_id"
                                    :searchable="true"
                                    :create-option="false"
                                    :placeholder="$t('Select Plugin')"
                                    :options="pluginList"
                                    :class="{
                                        '!border-danger !bg-danger/5': form.errors.plugin_id,
                                      }"
                                />
                                <Error :error="form.errors.plugin_id" />
                            </div>
                            <check-box-switch
                                :label="$t('Debug Mode')"
                                v-model="form.debug_mode"
                                id="debug_mode"
                            />
                        </div>
                    </div>
                    <!-- Clear Cache -->
                    <div class="p-4 border-1 flex items-center wide-mobile:flex-col wide-mobile:items-start border-solid border-secondary-light dark:border-dark rounded-md mt-6 gap-2">
                        <div class="flex flex-col gap-1.5">
                            <h5 class="text-lg font-medium leading-tight tracking-tight text-dark dark:text-white">
                                {{ $t('Clear Object Cache') }}
                            </h5>
                            <p class="text-secondary-full dark:text-secondary-light">
                                {{ $t('This will slow down your site until the caches are rebuilt.') }}
                            </p>
                        </div>
                        <button
                            class="ml-auto px-4 py-3.5 rounded-10px bg-primary-light/10 inline-flex items-center justify-center text-primary-light shrink-0"
                            @click.prevent="purgeCache"
                            :class="{'cursor-not-allowed opacity-70' : flushCacheForm.processing}">
                          <span class="inline-flex items-center justify-center ml-1 mr-2.5 text-xl">
                              <i class="xcloud xc-data-cleaning"></i>
                          </span>
                            <span :class="{'opacity-70 cursor-not-allowed' : flushCacheForm.processing}">
                            {{ $t('Purge Cache') }}
                          </span>
                        </button>
                    </div>
                </div>
                <div class="flex justify-center md:justify-end  px-30px py-20px flex-col md:flex-row gap-2 font-semibold">
                    <btn
                        @click.prevent="handleSubmitObjectCachePro"
                        :loading="form.processing" icon="xcloud"
                         class="bg-primary-light mt-5 dark:bg-dark"
                        :class="{'cursor-not-allowed opacity-70' : form.processing || flushCacheForm.processing || !form.plugin_id}"
                         :disabled="form.processing || flushCacheForm.processing || !form.plugin_id"
                    >
                        {{ $t('Save Changes') }}
                    </btn>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import Switch from "@/Shared/Switch.vue";
import {onMounted, ref} from "vue";
import {useForm} from "@inertiajs/inertia-vue3";
import Error from "@/Shared/Error.vue";
import Multiselect from "@vueform/multiselect";
import {useCapitalizeString} from "@/Composables/useCapitalizeString";
import {useFlash} from "@/Composables/useFlash";
import { useI18n } from "vue-i18n";
import CheckBoxSwitch from "@/Jetstream/CheckBoxSwitch.vue";
import Btn from "@/Shared/Btn.vue";
const props = defineProps({
    server: Object,
    site: Object,
    isEnable: Boolean,
    plugin_id: Number,
    redisDb: String,
    debug_mode: Boolean,
    object_cache_pro:Object
});
const pluginList = ref([]);

onMounted(() => {
    searchPlugin('');
});

const flushCacheForm = useForm({
    plugin_id:  null,
});
const form = useForm({
    isEnable: !!props.object_cache_pro,
    plugin_id: props.object_cache_pro?.pivot?.plugin_integration_id || null,
    debug_mode: props.object_cache_pro?.pivot?.debug_mode || false,
});

const searchPlugin = async (query) => {
    await fetchData('integration.plugins.search', { query }, pluginList);
};

const fetchData = async (routeName, params, targetRef, labelKey = 'name') => {
    try {
        const { data } = await axios.get(route(routeName, params));
        targetRef.value = data.map(item => ({ value: item.id, label: useCapitalizeString(item[labelKey]).sanitizeXSSPayload() }));
    } catch ({ response }) {
        useFlash().error(response?.data?.message || 'Something went wrong');
    }
};


const handleSubmitObjectCachePro = () => {
    form.post(route('api.site.object-cache-pro.update', [props.server.id, props.site.id]), {
        preserveScroll: true,
        onError: () => {
            useFlash().error('Failed to enable Object Cache Pro');
        }
    })
};

const confirmRedisUpdateCaching = () => {
    if (form.isEnable) {
        useFlash().deleteConfirmation({
            btn_text: 'Yes, disable it!',
            text: 'Are you sure you want to disable Object Cache Pro?',
        }, () => {
            form.isEnable = false;
            form.post(route('api.site.object-cache-pro.update', [props.server.id, props.site.id]), {
                preserveScroll: true,
                onError: () => {
                    useFlash().error('Failed to disable Object Cache Pro');
                }
            })
        })
    } else {
        form.isEnable = true;
    }
};
const purgeCache = () => {
    flushCacheForm.delete(
        route('api.site.purge-object-cache-pro', {
            server: props.server.id,
            site: props.site.id
        })
    );
};
</script>


