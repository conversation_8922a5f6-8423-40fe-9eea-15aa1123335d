<template>
    <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col mb-30px">
        <div class="overflow-x-auto w-full table-responsive">
            <Datatable
                :fields="['name' ,'site_user', 'php_version', 'status','created_at','actions']"
                :multiSelect="false"
                :allItemsSelected="allSelected"
                @on-select-all="selectAll($event)"
            >
                <Site
                    :checkBox="false"
                    v-for="site in sites.data"
                    v-model="checked"
                    :site="site"
                    :server="site.server"
                    :key="site.id"/>
            </Datatable>
        </div>
    </div>

    <!-- pagination -->

    <Pagination :links="sites.links"/>
</template>

<!-- script -->
<script setup>
import {computed, ref} from "vue";
import Datatable from "@/Shared/Table/Datatable.vue";
import Site from "@/Pages/Site/Components/Site.vue";
import Pagination from "@/Shared/Pagination.vue";

const props = defineProps({
    sites: Object
})

const checked = ref([]);

function selectAll(select) {
    if (select) {
        checked.value = props.sites.data.map(site => site.id);
    } else {
        checked.value = [];
    }
}

//all checked computed
const allSelected = computed(() => {
    return checked.value.length === props.sites.data.length;
});

</script>

<style>
@media (max-width: 767px) {
    .table-responsive .dropdown-menu {
        position: static !important;
    }
}
@media (min-width: 768px) {
    .table-responsive {
        overflow: inherit;
    }
}
</style>
