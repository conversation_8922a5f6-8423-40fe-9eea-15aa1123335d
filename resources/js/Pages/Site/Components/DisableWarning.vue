<template>
    <transition
        v-if="site?.is_disabled"
        leave-to-class="opacity-0"
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0"
        enter-to-class="opacity-100"
        leave-active-class="transition ease-in duration-300"
        leave-from-class="opacity-100"
    >
        <div
            :class="{ 'warning bg-warning/20' : _type === 'warning',  'danger bg-danger/20' : _type === 'danger', 'bg-success-full/20' : _type === 'success', 'bg-info/20' : _type === 'info' }"
            class="flex items-center px-6 mt-20px ml-30px py-4 rounded-md">
            <img :src="svg_icon()" alt="warning_img" class="w-6"/>
            <p class="text-sm text-dark dark:text-white leading-7 pl-3.5">
                {{ $t('Site is currently disabled.') }} <Link class="underline" :href="route('site.settings',[site.server.id, site.id])"> {{ $t('Click here') }} </Link> {{ $t('to enable this site') }}
            </p>
        </div>
    </transition>
</template>

<script setup>
import {Link} from "@inertiajs/inertia-vue3";
import {asset} from "laravel-vapor";

const {_type} =defineProps({
    site: {
        type: Object,
        default: {}
    },
    _type:{
        type: String,
        default: 'warning'
    }
})
let svg_icon = () => {
    if (['danger', 'success', 'warning'].includes(_type)){
        return asset(`img/${_type}.svg`);
    }
    return  asset('img/info.svg');
}
</script>
