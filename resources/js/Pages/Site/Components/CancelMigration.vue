<template>
    <Modal
        @close="$emit('closeModal')"
        :footer-button-title="$t('Delete')"
        :footer-button="true"
        :show="openCancelModal"
        :title="'Are You Sure You Want To cancel migration for '+ siteName +'?'"
        :widthClass="'max-w-850px'">
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img"/>
                <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b>{{ $t('Warning') }}:</b> {{ $t('When you cancel migration, already migrated data will be removed from the server.') }}
                </p>
            </div>
        </div>
        <template #footer>
                <button
                    :disabled="form.processing"
                    @click.prevent="cancelMigration()"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    aria-expanded="true" aria-haspopup="true">
                    <span>{{ $t('Cancel Migration') }}</span>
                </button>
        </template>
    </Modal>
</template>

<script setup>
import Modal from "@/Shared/Modal.vue";
import {computed} from "vue";
import {useForm} from "@inertiajs/inertia-vue3";


const {siteName,siteMigrationId,status} = defineProps({
    siteName: String,
    siteMigrationId: Number,
    openCancelModal: Boolean,
    status: String
});

const emit = defineEmits(['closeModal']);


const form = useForm({
    cancel_confirmation: '',
});

function cancelMigration() {
    console.log('cancel migration');
    form.delete(route('api.site.migrate.cancel', {siteMigration: siteMigrationId}), {
        preserveScroll: true,
        onFinish: () => {
            emit('closeModal')
        }
    });
}

</script>
