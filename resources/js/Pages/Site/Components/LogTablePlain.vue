<template>
    <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col mb-30px">
        <div class="overflow-x-auto w-full">
            <Datatable :fields="['Logs']">
                <tr class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark" v-for="(access_log, index) in access_logs.value?.log">
                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                        {{ access_log }}
                    </td>
                </tr>
            </Datatable>
        </div>
    </div>
</template>

<!-- script -->
<script setup>

import Datatable from "@/Shared/Table/Datatable.vue";
import NginxLogRow from "@/Pages/Site/Components/NginxLogRow.vue";

const {access_logs} = defineProps({
    access_logs: {
        type: Object,
        required: true
    },
});
</script>
