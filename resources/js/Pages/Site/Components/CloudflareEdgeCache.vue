<template>
    <!--Cloudflare Edge Cache -->
    <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
        <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col pb-0">
            <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center
            wide-mobile:px-15px gap-20px mobile:gap-1"
            >
                <Switch
                    :checked="form.isEnable"
                    @click.prevent="confirmCloudflareEdgeCacheUpdate"
                    :disabled="form.processing || flushCacheForm.processing || !cloudflare_edge_cache.can_enable"
                    :tooltip="cloudflare_edge_cache.can_enable ? '' : cloudflare_edge_cache.error_message"
                >
                  <span class="mr-2 cursor-pointer">
                    {{ $t('Cloudflare Edge Cache') }}
                  </span>
                <span class="inline-flex items-center">
                    <i class="xcloud xc-verify_dns animate-spin items-center origin-center inline-flex pt-[0.011em] cursor-pointer"
                       v-show="false">
                    </i>
                  </span>
                </Switch>
            </div>

            <div class="flex flex-col" v-if="form.isEnable && cloudflare_edge_cache.can_enable">
                <div class="p-30px h-full bg-white dark:bg-mode-base rounded-b-md flex flex-col tablet:grid tablet:grid-cols-1 ">
                    <div class="flex items-center">
                        <img :src="asset('img/integration/cloudflare.svg')" alt="" class="h-11 w-11">
                        <h4 class="text-dark dark:text-white text-lg font-medium leading-tight tracking-tighter -mt-2 ml-3">
                            {{ $t('Cloudflare Edge Cache') }}
                        </h4>
                    </div>
                    <p class="text-secondary-full dark:text-secondary-light mt-3">
                        {{ $t('Boost your website\'s performance with Cloudflare Edge Cache by caching content at Cloudflare\'s global edge network. This reduces server load, enhances speed, and improves user experience worldwide.') }}
                    </p>

                    <Advanced
                        class="p-0"
                        @showAdvance="true"
                        :accordion_button="false"
                        :title="$t('Advanced Settings')"
                    >
                        <div class="grid grid-cols-2 tablet:grid-cols-1 gap-30px w-full">
                            <textarea-input
                                :rows="10"
                                v-model="form.cache_exclusion_http_rules"
                                :error="form.errors.cache_exclusion_http_rules"
                                :label="$t('Cache Exclusion HTTP URL Rules')"
                                placeholder="/wp-admin/.."
                            />

                            <textarea-input
                                :rows="10"
                                v-model="form.cache_exclusion_cookie_rules"
                                :error="form.errors.cache_exclusion_cookie_rules"
                                :label="$t('Cache Exclusion Cookie Rules')"
                                placeholder="comment_author..."
                            />
                        </div>
                    </Advanced>


                    <!-- Clear Cache -->
                    <div class="p-4 border-1 flex items-center wide-mobile:flex-col wide-mobile:items-start border-solid border-secondary-light dark:border-dark rounded-md mt-6 gap-2">
                        <div class="flex flex-col gap-1.5">
                            <h5 class="text-lg font-medium leading-tight tracking-tight text-dark dark:text-white">
                                {{ $t('Clear Edge Cache') }}
                            </h5>
                            <p class="text-secondary-full dark:text-secondary-light">
                                {{ $t('This will purge all cached content from Cloudflare\'s edge network.') }}
                            </p>
                        </div>
                        <button
                            class="ml-auto px-4 py-3.5 rounded-10px bg-primary-light/10 inline-flex items-center justify-center text-primary-light shrink-0"
                            @click.prevent="purgeCache"
                            :class="{'cursor-not-allowed opacity-70' : flushCacheForm.processing}">
                          <span class="inline-flex items-center justify-center ml-1 mr-2.5 text-xl">
                              <i class="xcloud xc-data-cleaning"></i>
                          </span>
                            <span :class="{'opacity-70 cursor-not-allowed' : flushCacheForm.processing}">
                            {{ $t('Purge Cache') }}
                          </span>
                        </button>
                    </div>
                </div>
                <div class="flex justify-center md:justify-end px-30px py-20px flex-col md:flex-row gap-2 font-semibold">
                    <btn
                        @click.prevent="handleSubmitCloudflareEdgeCache"
                        :loading="form.processing" icon="xcloud"
                         class="bg-primary-light mt-5 dark:bg-dark"
                        :class="{'cursor-not-allowed opacity-70' : form.processing || flushCacheForm.processing}"
                         :disabled="form.processing || flushCacheForm.processing"
                    >
                        {{ $t('Save Changes') }}
                    </btn>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import Switch from "@/Shared/Switch.vue";
import {onMounted, ref} from "vue";
import {useForm} from "@inertiajs/inertia-vue3";
import Error from "@/Shared/Error.vue";
import {useFlash} from "@/Composables/useFlash";
import { useI18n } from "vue-i18n";
import TextareaInput from "@/Shared/TextareaInput.vue";
import Btn from "@/Shared/Btn.vue";
import Advanced from "@/Shared/Advanced.vue";
const { t } = useI18n();


const props = defineProps({
    server: Object,
    site: Object,
    isEnable: Boolean,
    cloudflare_edge_cache: Object
});

const flushCacheForm = useForm({});

const form = useForm({
    isEnable: !!props.cloudflare_edge_cache.enable,
    cache_exclusion_http_rules: props.cloudflare_edge_cache?.cache_exclusion_http_rules,
    cache_exclusion_cookie_rules: props.cloudflare_edge_cache?.cache_exclusion_cookie_rules,
});

const handleSubmitCloudflareEdgeCache = () => {
    form.post(route('api.site.cloudflare-edge-cache.update', [props.server.id, props.site.id]), {
        preserveScroll: true,
        onError: () => {
            useFlash().error($t('Failed to update Cloudflare Edge Cache settings'));
        }
    })
};

const confirmCloudflareEdgeCacheUpdate = () => {
    if(!props.cloudflare_edge_cache.can_enable){
        useFlash().error(props.cloudflare_edge_cache.error_message);
        return;
    }
    if (form.isEnable) {
        useFlash().deleteConfirmation({
            btn_text: t('Yes, disable it!'),
            text: t('Are you sure you want to disable Cloudflare Edge Cache?'),
        }, () => {
            form.isEnable = false;
            form.post(route('api.site.cloudflare-edge-cache.update', [props.server.id, props.site.id]), {
                preserveScroll: true,
                onError: () => {
                    useFlash().error(t('Failed to disable Cloudflare Edge Cache'));
                }
            })
        })
    } else {
        form.isEnable = true;
    }
};

const purgeCache = () => {
    flushCacheForm.delete(
        route('api.site.purge-cloudflare-edge-cache', {
            server: props.server.id,
            site: props.site.id
        }),
        {
            preserveScroll: true,
            onSuccess: () => {
                useFlash().success(t('Cloudflare Edge Cache purged successfully'));
            },
            onError: () => {
                useFlash().error(t('Failed to purge Cloudflare Edge Cache'));
            }
        }
    );
};
</script>
