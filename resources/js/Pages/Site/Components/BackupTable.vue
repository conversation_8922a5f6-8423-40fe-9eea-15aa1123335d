<template>
  <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
    <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
      <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
        {{ title }}
      </h4>
      <button
        :class="{ 'cursor-not-allowed opacity-50': !backupSettings || backupTaskRunning }"
        :disabled="!backupSettings || backupTaskRunning"
        class="inline-flex items-center justify-center rounded-10px border-transparent shadow-none min-h-50px mobile:min-h-40px px-20px tablet:px-25px py-2px bg-success-full text-sm font-medium text-white focus:outline-0"
        type="button"
        @click.prevent="$emit('backupNow', backupSettings)">
        <i v-if="backupTaskRunning && backupSettings"
           class="xcloud xc-verify_dns text-white mr-1 animate-spin">
        </i>
        <span>{{ $t('Backup Now') }}</span>
      </button>
    </div>
    <div
      :class="{
        'bg-white dark:bg-mode-base py-10': !backupFiles || Object.keys(backupFiles).length === 0,
      }"
      class="overflow-x-auto w-full">
      <table v-if="backupFiles && Object.keys(backupFiles).length > 0"
             class="w-full divide-y-1 divide-light dark:divide-mode-light">
        <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
          <tr class="divide-x-1 divide-light dark:divide-mode-light">
            <th class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('Date') }}
            </th>
            <th class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('Type') }}
            </th>
            <th class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('User') }}
            </th>
            <th class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('Note') }}
            </th>
            <th v-if="!isLocal" class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('Bucket') }}
            </th>
            <th v-if="hasSiteFiles" class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('File') }}
            </th>
            <th v-if="hasSiteDatabase" class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('Database') }}
            </th>
            <th class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ $t('Action') }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
          <!-- Loading state row -->
          <tr v-if="backupSettings?.status === 'running'" class="divide-x-1 divide-light dark:divide-mode-light">
            <td class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ backupSettings?.last_backup_at_format }}
            </td>
            <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
              <div class="animate-pulse h-2 bg-slate-300 rounded"></div>
            </td>
            <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
              <div class="animate-pulse h-2 bg-slate-300 rounded"></div>
            </td>
            <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
              <div class="animate-pulse h-2 bg-slate-300 rounded"></div>
            </td>
            <td v-if="!isLocal" class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
              <div class="animate-pulse h-2 bg-slate-300 rounded"></div>
            </td>
            <td v-if="hasSiteFiles" class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
              <div class="animate-pulse h-2 bg-slate-300 rounded"></div>
            </td>
            <td v-if="hasSiteDatabase" class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
              <div class="animate-pulse h-2 bg-slate-300 rounded w-1/2"></div>
            </td>
            <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
              <div class="animate-pulse h-2 bg-slate-300 rounded w-1/2"></div>
            </td>
          </tr>

          <!-- Data rows -->
          <tr v-for="(files, date) in backupFiles"
              class="divide-x-1 divide-light dark:divide-mode-light">
            <td class="px-30px py-2 text-left text-base font-normal h-60px">
              <span v-if="files.some(file => (file.type === 'incremental' || file.type === 'incremental_full'))">
                {{ files.find(file => file.last_backup)?.last_backup }}
              </span>
              <span v-else-if="isLocal && files.find(file => !file.is_sql)?.type==='full'">
                {{ date }}
              </span>
              <span v-else-if="isLocal">
                {{ files.find(file => file.is_sql)?.last_backup }}
              </span>
              <span v-else>
                {{ files.find(file => file.date_format)?.date_format }}
              </span>
            </td>
            <td class="px-30px py-2 capitalize text-left text-base font-normal h-60px">
              {{ hasSiteFiles ? files.find(file => !file?.is_sql)?.type?.replace('_', ' ') : 'Full' }}
            </td>
            <td class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ files.find(file => file.user_id)?.user?.name ?? backupUserName }}
            </td>
            <td class="px-10px py-2 text-base font-normal h-60px w-80">
              <div
                class="w-80"
                @blur="$emit('updateBackupNote', files, $event.target.innerText.trim())"
                contenteditable="true">
                {{ files.find(file => file.user_note)?.user_note }}
              </div>
            </td>
            <td v-if="!isLocal" class="px-30px py-2 text-left text-base font-normal h-60px">
              {{ files.find(file => file.is_remote)?.storage_provider?.bucket }}
            </td>
            <td v-if="!isFailed(files) && hasSiteFiles"  class="px-30px py-2 text-left text-base font-normal h-60px">
              <p v-if="hasNonDatabaseFile(files)" class="inline-flex items-center gap-2.5">
                <i class="xcloud xc-file"></i> {{ useUnitSize(files.find(file => file.is_sql === false)?.file_size, 'KB') }}
              </p>
              <a
                v-if="!isLocal && hasNonDatabaseFile(files) && !isIncremental(files)"
                :href="`/server/${serverId}/site/${siteId}/backups/${files.find(file => !file.is_sql)?.id}/download`"
                class="ml-2 inline-flex items-center justify-center min-h-40px dark:border-dark
                text-sm font-medium rounded-10px shadow-none text-white dark:text-white
                transition ease-in-out duration-300 focus:outline-none px-10px">
                <i class="xcloud xc-download text-xl text-dark dark:text-white"></i>
              </a>
            </td>
            <td v-if="!isFailed(files) && hasSiteDatabase"
                class="px-30px py-2 text-left text-base font-normal h-60px">
              <p v-if="hasDatabaseFile(files)" class="inline-flex items-center gap-2.5">
                <i class="xcloud xc-database"></i>
                {{ useUnitSize(files.find(file => file.is_sql === true)?.file_size, 'KB') }}
              </p>
              <a
                v-if="!isLocal && hasDatabaseFile(files)"
                :href="`/server/${serverId}/site/${siteId}/backups/${files.find(file => file.is_sql)?.id}/download`"
                class="ml-2 inline-flex items-center justify-center min-h-40px dark:border-dark
                text-sm font-medium rounded-10px shadow-none text-white dark:text-white
                transition ease-in-out duration-300 focus:outline-none px-10px">
                <i class="xcloud text-xl xc-download text-dark dark:text-white"></i>
              </a>
            </td>
            <td v-if="isFailed(files)" class="px-30px py-2 text-left text-base font-normal h-60px"
                :colspan="hasSiteFiles && hasSiteDatabase ? 2 : 1"
            >
              <p class="inline-flex items-center gap-2.5">
                {{ files.find(file => file.status === 'failed').note }}
              </p>
            </td>
            <td class="px-30px py-2 text-left text-base font-normal h-60px">
              <span class="flex gap-x-2 gap-y-1">
                <button
                  v-if="!isFailed(files)"
                  :class="{ 'cursor-not-allowed opacity-50': backupTaskRunning }"
                  class="inline-flex items-center justify-center min-h-40px border-1 border-primary-light dark:border-dark
                  text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light
                  group-hover:bg-light group-hover:text-primary-light group-hover:border-light
                  transition ease-in-out duration-300 focus:outline-none
                  disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                  @click.prevent="isIncremental(files) ? $emit('openIncrementalModal', files, isLocal) : $emit('openRestoreModal', files)"
                  v-text="isIncremental(files) ? $t('View') : $t('Restore')"
                >
                </button>
                <button
                  :disabled="files.find(file => file.status === 'processed')"
                  v-if="!isFailed(files)"
                  :class="{ 'cursor-not-allowed opacity-50': backupTaskRunning || files.find(file => file.status === 'processed') }"
                  class="inline-flex items-center justify-center min-h-40px border-1 border-danger dark:border-dark
                  text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-delete
                  group-hover:bg-light group-hover:text-danger group-hover:border-light
                  transition ease-in-out duration-300 focus:outline-none
                  disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                  @click.prevent="openDeleteConfirmation(files, isLocal)">
                  {{ $t('Delete') }}
                </button>

                <template v-if="isFailed(files)">
                  <button
                    class="inline-flex items-center justify-center min-h-40px border-1 border-warning dark:border-dark
                    text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-warning
                    group-hover:bg-light group-hover:text-warning group-hover:border-light
                    transition ease-in-out duration-300 focus:outline-none
                    disabled:opacity-75 disabled:cursor-not-allowed px-15px">
                    {{ $t('Failed') }}
                  </button>
                  <button
                    :class="{ 'cursor-not-allowed opacity-50': backupTaskRunning }"
                    class="inline-flex items-center justify-center min-h-40px border-1 border-danger dark:border-dark
                    text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-delete
                    group-hover:bg-light group-hover:text-danger group-hover:border-light
                    transition ease-in-out duration-300 focus:outline-none
                    disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                    @click.prevent="removeFailedFiles(files, isLocal)">
                    {{ $t('Remove') }}
                  </button>
                </template>
              </span>
            </td>
          </tr>
        </tbody>
      </table>

      <Empty v-else
        :canPerformAction="!backupSettings"
        :visit-url="route('site.backup', { server: serverId, site: siteId })"
        :item-to-create="$t('Backup')"
        type="site"/>
    </div>
    <div class="m-4">
      <pagination :links="paginationLinks"/>
    </div>
  </div>
</template>

<script setup>
import { useUnitSize } from "@/Composables/useUnitSize";
import Empty from "@/Shared/Empty.vue";
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import {useI18n} from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
  title: String,
  backupFiles: Object,
  backupSettings: Object,
  backupTaskRunning: Boolean,
  paginationLinks: Array,
  hasSiteDatabase: Boolean,
  hasSiteFiles: Boolean,
  isLocal: {
    type: Boolean,
    default: false
  },
  serverId: [Number, String],
  siteId: [Number, String],
  backupUserName: String
});

defineEmits([
  'backupNow',
  'updateBackupNote',
  'openRestoreModal',
  'openIncrementalModal',
]);

const removeFailedFiles = (files,is_local) => {
    useFlash().deleteConfirmation(
        {
            title: t('Are you sure you want to remove this failed backup?'),
            description: t('This action cannot be undone.'),
            buttonText: t('Delete'),
            method: 'delete',
        },
        ()=>{
            Inertia.delete(route('api.site.backup.remove', {server: props.serverId, site: props.siteId}), {
                data: {
                    files: files,
                    is_local: is_local,
                },
                preserveScroll: true,
            });
        }
    )
}

const openDeleteConfirmation = (files,is_local) => {
    useFlash().deleteConfirmation(
        {
            title: t('Are you sure you want to delete this backup?'),
            description: t('This action cannot be undone.'),
            buttonText: t('Delete'),
            method: 'delete',
        },
        ()=>{
            Inertia.delete(route('api.site.backup.delete', {server: props.serverId, site: props.siteId}), {
                data: {
                    files: files,
                    is_local: is_local,
                },
                preserveScroll: true,
            });
        }
    )
}

const isFailed = (files) => {
    return files.some(file => file.status === 'failed');
}

const hasDatabaseFile = (files) => {
    return files.some(file => file.is_sql);
}
const hasNonDatabaseFile = (files) => {
    return files.some(file => !file.is_sql);
}
const isIncremental = (files) => {
    return hasNonDatabaseFile(files) && files.some(file => file.type === 'incremental' || file.type === 'incremental_full');
}
</script>
