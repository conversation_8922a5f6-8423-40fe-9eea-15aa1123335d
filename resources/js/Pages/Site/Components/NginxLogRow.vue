<template>
    <tr
        class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
    >
        <td
            class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white"
        >
            {{ ip_address(access_log) }}
        </td>
        <td
            class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white"
        >
            {{ request_method(access_log) }}
        </td>
        <td
            class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white"
        >
            {{ date_time(access_log) }}
        </td>
        <td
            class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white"
        >
            {{ browser_info(access_log) }}
        </td>
        <td
            class="px-30px max-w-2xl text-clip py-20px text-left text-base font-normal text-dark dark:text-white break-all group-hover:text-white"
        >
            <span class="wide-tablet:min-w-[30ch] wide-tablet:flex"> {{ message(access_log) }}</span>
        </td>
    </tr>
</template>

<!-- script -->
<script setup>
import { useHelpStore } from "@/stores/HelpStore";
import { useNavigationStore } from "@/stores/NavigationStore";

let helper = useHelpStore();
let navigation = useNavigationStore();
defineProps({
    access_log: Object | String,
    sl: Number | String,
});

//remove ip address datetime request method and browser info from log
const message = (line) => {
    return line
        .replace(ip_address(line), "")
        .replace(date_time(line), "")
        .replace(request_method(line), "")
        .replace(browser_info(line), "")
        .replace("- - [] ", " ")
        .trim();
};

//get ip address from log
const ip_address = (line) => {
    const ip_address_regx = /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/g;
    return line.match(ip_address_regx)
        ? line.match(ip_address_regx)[0]
        : "Unknown";
};

//get request method from log by regex
const request_method = (line) => {
    return line.match(/(GET|POST|PUT|DELETE|PATCH)/g)
        ? line.match(/(GET|POST|PUT|DELETE|PATCH)/g)[0]
        : "";
};

//get date and time from log by regex
const date_time = (line) => {
    return line.match(/(\d{2}\/\w{3}\/\d{4}:\d{2}:\d{2}:\d{2} \+\d{4})/g)
        ? line.match(/(\d{2}\/\w{3}\/\d{4}:\d{2}:\d{2}:\d{2} \+\d{4})/g)[0]
        : "Unknown";
};

//get browser info from log by regex
const browser_info = (line) => {
    const browser = line.match(
        /(GuzzleHttp|Mozilla|Chrome|Safari|Opera|Firefox|MSIE|Edge)/g
    );
    if (browser) {
        return browser[0];
    }
    return "Unknown";
};
</script>
