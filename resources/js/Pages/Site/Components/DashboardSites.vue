<template>
    <div
        class="relative flex flex-col p-30px bg-white dark:bg-mode-light rounded-10px border-2 border-white hover:drop-shadow-grid-box dark:border-mode-light transition ease-in-out duration-150 group"
        :class="{ 'z-[1]': dropdownShowing }"
    >
        <div class="pr-30px mb-3">
            <tooltip class="cursor-pointer" :title="site.name">
              <Link
                    :href="'/site/' + site.id"
                    class="inline-flex items-center text-dark dark:text-light text-2xl leading-7 hover:text-primary-dark dark:hover:text-white transition ease-in-out duration-75"
                    >{{ $filters.textLimit(site.name, 20) }}
              </Link>
            </tooltip>
            <tooltip class="cursor-pointer ml-2" title="View Site">
                <a
                    :href="site.site_url"
                    target="_blank"
                    class="inline-flex items-center"
                    v-if="site.state === 'Success'"
                >
                    <i
                        class="xcloud xc-maximize text-base text-secondary-full dark:text-secondary-light hover:text-primary-light dark:hover:text-primary-light cursor-pointer transition ease-in-out duration-75"
                    >
                    </i>
                </a>
            </tooltip>
        </div>
        <span
            v-if="false"
            class="text-sm text-secondary-full dark:text-mode-secondary-light mt-1.5"
            >{{ $t('Created') }}: {{ site.created_at_readable }}</span
        >
        <div
            class="flex items-center justify-between flex-wrap gap-x-20px gap-y-2 mt-1.5 mb-2"
        >
            <h6 class="flex items-center">
                <span
                    class="text-base tablet:text-sm px-2 py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base divide-x divide-light dark:divide-mode-base rounded-md transition ease-in-out duration-150"
                >
                    <span
                        class="text-secondary-full dark:text-mode-secondary-light flex items-center transition ease-in-out duration-150"
                    >
                        <cloud-provider-logo
                            :provider-name="site?.server?.provider_name ?? site.cloud_provider"
                            class="w-20px mr-1.5"
                        />
                        <Tooltip
                            align="top"
                            :title="site?.server?.name ?? site.cloud_provider"
                        >
                            <span class="max-w-[150px] hover:cursor-pointer">
                                <Link
                                    v-if="site?.can_view_server"
                                    :href="route('server.show', site.server.id)"
                                >
                                    {{
                                        $filters.textLimit(
                                            site?.server?.name ??
                                                site.cloud_provider,
                                            15
                                        )
                                    }}
                                </Link>
                                <span
                                    v-else
                                    class="text-secondary-full dark:text-mode-secondary-light"
                                >
                                    {{
                                        $filters.textLimit(
                                            site?.server?.name ??
                                                site.cloud_provider,
                                            15
                                        )
                                    }}
                                </span>
                            </span>
                        </Tooltip>
                    </span>
                </span>
            </h6>
            <h6
                class="flex items-center flex-wrap text-base tablet:text-sm text-secondary-full dark:text-mode-secondary-light"
            >
                {{ $t('PHP') }}:
                <span
                    class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base divide-x divide-light dark:divide-mode-base rounded-md ml-1.5 transition ease-in-out duration-150"
                >
                    {{ site.php_version }}
                </span>
            </h6>
        </div>
        <div
            class="flex items-center justify-between flex-wrap mb-10px"
        >
            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm"
                v-if="site.environment"
            >
                <span
                :class="{
                  'text-success-light bg-success-light/20' : site.environment === 'production',
                  'text-[#9AA4B2] bg-[#9AA4B2]/20' : site.environment === 'staging' || site.environment === 'staging_with_own_domain',
                  'text-[#007EFD] bg-[#007EFD]/20' : site.environment === 'demo'
                }"
                class="px-10px py-1 wide-mobile:p-1
                        rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5"
                >
                    <span>{{ getSiteEnvironmentReadable(site.environment) }}</span>
                </span>
            </h6>
            <div class="flex items-center"
                v-if="site.tags?.length > 0">
                <tooltip class="cursor-pointer" :title="site.tags[0]?.name">
                    <span
                        class="text-base tablet:text-sm text-secondary-full dark:text-mode-secondary-light rounded-md"
                    >
                        # {{ $filters.textLimit(site.tags[0]?.name) }}
                    </span>
                </tooltip>

                <tooltip
                    class="text-xs cursor-pointer ml-2"
                    :title="
                        site.tags
                            .slice(1)
                            .map((tag) => tag.name)
                            .join(', ')
                    "
                >
                    <h6
                        class="text-base tablet:text-sm text-primary-dark dark:text-primary-light"
                        v-if="site.tags.length > 1"
                    >
                        +{{ site.tags.length - 1 }}
                    </h6>
                </tooltip>
            </div>
        </div>
        <div
            class="border-1 flex border-light dark:border-mode-base divide-x divide-light dark:divide-mode-base rounded-10px transition ease-in-out duration-150 mt-auto"
        >
            <Link :href="route('site.caching', [site?.server?.id, site.id])"
                v-if="site?.is_wordpress"
                class="text-xs flex items-center grow px-1 min-h-50px justify-center cursor-pointer"
            >
                <i
                    class="xcloud xc-rocket mr-10px text-lg flex"
                    :class="{
                        'text-primary-light dark:text-white': site?.has_caching,
                        'text-secondary-light dark:text-secondary-full':
                            !site?.has_caching,
                    }"
                >
                </i>
                <span
                    :class="{
                        'text-dark dark:text-white': site?.has_caching,
                        'text-secondary-full dark:text-secondary-full':
                            !site?.has_caching,
                    }"
                >
                    {{ $t('Cache') }}
                </span>
            </Link>
            <Link :href="route('site.ssl', [site?.server?.id, site.id])"
                class="text-xs flex items-center grow px-1 min-h-50px justify-center cursor-pointer"
            >
                <i
                    class="xcloud xc-https mr-10px text-lg flex"
                    :class="{
                        'text-primary-light dark:text-white': site?.has_https,
                        'text-secondary-light dark:text-secondary-full':
                            !site?.has_https,
                    }"
                >
                </i>
                <span
                    :class="{
                        'text-dark dark:text-white': site?.has_https,
                        'text-secondary-full dark:text-secondary-full':
                            !site?.has_https,
                    }"
                >
                    {{ $t('HTTPS') }}
                </span>
            </Link>
            <Link :href="route('site.git', [site?.server?.id, site.id])"
                  v-if="site?.is_git"
                  class="text-xs flex items-center grow px-1 min-h-50px justify-center cursor-pointer"
            >
                <i
                    class="xcloud xc-git mr-10px text-lg flex text-primary-light dark:text-white"
                >
                </i>
                <span class="text-dark dark:text-white">
                    {{ $t('Git') }}
                </span>
            </Link>
            <Link :href="route('site.application', [site?.server?.id, site.id])"
                  v-if="site.type === 'laravel'"
                  class="text-xs flex items-center grow px-1 min-h-50px justify-center cursor-pointer"
            >
                <i
                    class="xcloud xc-laravel mr-10px text-lg flex text-primary-light dark:text-white"
                >
                </i>
                <span class="text-dark dark:text-white">
                    {{ $t('Laravel') }}
                </span>
            </Link>
            <siteActions
                :site="site"
                :server="site?.server"
                position="right"
                @onChangeDropDown="onChangeDropDown"
            ></siteActions>
        </div>
        <div class="absolute top-30px right-30px">
            <StateToolTip
                :align="index === 0 ? 'bottom' : 'top'"
                class="mr-3"
                :state="site.state"
                :title="site.status_readable"
                :blinking="false"
                :blinking_title="site?.vulnerabilities_count + ' Vulnerabilities Found'"
            />
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { userClickOutside } from "@/Shared/Events/userClickOutside.js";
import SiteActions from "@/Pages/Site/Components/SiteActions.vue";
import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import StateToolTip from "@/Shared/StateToolTip.vue";
import {Link} from "@inertiajs/inertia-vue3";

const props = defineProps({
    site: {
        type: Object,
        required: true,
    },
    index: {
        type: Number,
        required: true,
    },
});

const showDropDown = ref(false);
const dropdownShowing = ref(false);

const dropDown = ref(null);
userClickOutside(dropDown, () => {
    showDropDown.value = false;
});

const emit = defineEmits(["site-deleted"]);

onMounted(() => {
    if (window.Echo) {
        console.log(
            "joining",
            "site.status." + props.site.id,
            "SiteStatusChanged"
        );
        window.Echo.private("site.status." + props.site.id).listen(
            "SiteStatusChanged",
            (e) => {
                console.log("SiteStatusChanged", e);
                if (e.status === "deleted") {
                    //send to parent
                    emit("site-deleted", props.site.id);
                    return;
                }
                props.site.status = e.status;
                props.site.status_readable = e.status_readable;
                props.site.state = e.state;
            }
        );
    }
});


const getSiteEnvironmentReadable = (environment) => {
  if(environment === 'production') {
    return 'Production';
  } else if(environment === 'demo') {
      return 'Demo';
  } else if(environment === 'staging_with_own_domain' || environment === 'staging') {
    return 'Staging';
  }
}

const onChangeDropDown = (value) => {
    dropdownShowing.value = value;
};
</script>
