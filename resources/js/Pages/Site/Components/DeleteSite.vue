<template>
    <Modal
        @close="$emit('closeModal')"
        :footer-button-title="$t('Delete')"
        :footer-button="true"
        :show="openDeleteModal"
        :title="$t('Are You Sure You Want To Delete Site')+' '+ siteName +'?'"
        :widthClass="'max-w-850px'">
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img"/>
                <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b>{{ $t('Warning') }}:</b> {{ $t('Proceeding will permanently delete this Site and all of its data.') }}

                  <ul class="list-disc pl-6">
                    <li v-for="stagingSite in stagingSites">
                      <Link :href="route('site.overview', {server: stagingSite.server.id, site: stagingSite.id})"
                            class="underline"
                      >
                        {{ stagingSite.name }}
                      </Link>
                    </li>
                  </ul>
                </p>
            </div>

            <div class="flex flex-col gap-30px">
                <!--Delete file switch/checkbox -->
                <div v-if="showOptions" class="flex flex-col gap-30px">
                    <Switch
                        :disabled="true"
                        :checked="form.delete_files">
                        {{ form.delete_files ? $t('Delete All Files and Configurations.') : $t('Do You Want To Delete Files?') }}
                    </Switch>

                    <Switch @click.prevent="form.delete_database=!form.delete_database" :checked="form.delete_database">
                        {{ form.delete_database ? $t('Delete Database') : $t('Do You Want To Delete Database?') }}
                    </Switch>

                    <Switch @click.prevent="form.delete_user=!form.delete_user" :checked="form.delete_user">
                        {{ form.delete_user ? $t('Delete Site User') : $t('Do You Want To Delete User?') }}
                    </Switch>
                    <Switch v-if="deleteLocalBackups" @click.prevent="form.delete_local_backups=!form.delete_local_backups" :checked="form.delete_local_backups">
                        {{ form.delete_local_backups ? $t('Delete Local Backups') : $t('Do You Want To Delete Local Backups?') }}
                    </Switch>
                </div>
                <text-input v-model="form.delete_confirmation"
                            :error="form.errors.delete_confirmation"
                            :placeholder="$t('Type site name to confirm')"
                            type="text"
                            :label="$t('Type')+` ${siteName} `+$t('to confirm')"/>
            </div>

          <div v-if="hasCloudflareIntegration && sslProvider === 'cloudflare'">
            <label class="inline-flex mr-5">
              <input type="checkbox" class="hidden peer"
                     :disabled="!hasCloudflareIntegration || sslProvider !== 'cloudflare'"
                     v-model="form.delete_dns_record"
              />
              <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                              before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded
                              before:mt-0.5 before:mr-2.5 before:text-xxxs before:inline-flex before:items-center before:justify-center
                              before:text-transparent before:outline-none before:transition before:duration-200
                              peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white"
              >
                  <span class="font-normal text-secondary-full dark:text-white text-sm">
                      <span class="flex" >
                        <span>{{ $t('Delete DNS record from your Cloudflare account') }}</span>
                        <img :src="asset('img/CF_logomark.png')" alt="cloudflare logo" class="ml-1 w-8 h-4">
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                          {{ $t('Your DNS record for the site on this server will be deleted from your Cloudflare account.') }}
                      </span>
                  </span>
                </span>
            </label>
          </div>
        </div>
        <template #footer>
                <button
                    :disabled="!checkDeleteInputMatch||form.processing"
                    @click.prevent="deleteSite()"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    aria-expanded="true" aria-haspopup="true">
                    <span>{{ $t('Delete') }}</span>
                </button>
        </template>
    </Modal>
</template>

<script setup>
import TextInput from '@/Shared/TextInput.vue';
import Modal from "@/Shared/Modal.vue";
import {computed, ref} from "vue";
import {useForm, usePage} from "@inertiajs/inertia-vue3";
import Switch from "@/Shared/Switch.vue";
import {Inertia} from "@inertiajs/inertia";
import {asset} from "laravel-vapor";
import {useNavigationStore} from "@/stores/NavigationStore";

const statusForHideOptions = [
    'new',
    'migration_init',
    'provisioning_failed',
    'migration_failed',
    'clone_init',
    'clone_failed'
];

const {siteName,siteId,serverId,status,sslProvider,stagingSites} = defineProps({
    siteName: String,
    siteId: Number,
    serverId: Number,
    openDeleteModal: Boolean,
    status: String,
    deleteLocalBackups:{
        type: Boolean,
        default: false
    },
    sslProvider: String,
    stagingSites: Object
});

const hasCloudflareIntegration = computed(() => {
    return useNavigationStore().hasCloudflareIntegration;
});

const showOptions = computed(() => {
    return !statusForHideOptions.includes(status);
});
const emit = defineEmits(['closeModal']);


const form = useForm({
    delete_files: true,
    delete_database: true,
    delete_user: true,
    delete_local_backups: false,
    delete_confirmation: '',
    delete_dns_record: false
});


const checkDeleteInputMatch = computed(() => {
    return form.delete_confirmation === siteName;
})

function deleteSite() {
    form.delete(route('api.site.delete', {server: serverId, site: siteId}), {
        preserveScroll: true,
        onFinish: () => {
            form.reset('delete_files', 'delete_database', 'delete_confirmation')
            emit('closeModal');
            if (route().current('site.index')) {
                Inertia.reload({preserveScroll: true, preserveState: true});
            }
        }
    });
}

</script>
