<template>
    <single-site :server="server" :site="site" active="Previous Backups">
        <template v-if="is_playground">
            <div class="xc-container">
                <Empty
                    :documentation="$t('This feature is not available in Playground.')"
                    :canPerformAction="false"
                    type="site"/>
            </div>
        </template>
        <template v-else>
        <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <backup-table
                :backup-files="remote_backup_files"
                :backup-settings="remote_backup_settings"
                :backup-task-running="backup_task_running"
                :backup-user-name="backupUserName"
                :has-site-database="site?.has_database"
                :has-site-files="site?.type !== 'laravel'"
                :is-local="false"
                :pagination-links="remote_backup_files_pagination?.links"
                :server-id="server?.id"
                :site-id="site?.id"
                :title="$t('Previous Remote Backups')"
                @backup-now="backupNow(remote_backup_settings)"
                @open-incremental-modal="(files) => openIncrementalModal(files)"
                @open-restore-modal="(files) => openRestoreModal(files)"
                @update-backup-note="(files,note)=>updateBackupNote(files,note)"
            />

            <backup-table
                :backup-files="local_backup_files"
                :backup-settings="local_backup_settings"
                :backup-task-running="backup_task_running"
                :backup-user-name="backupUserName"
                :has-site-database="site?.has_database"
                :has-site-files="site?.type !== 'laravel'"
                :is-local="true"
                :pagination-links="local_backup_files_pagination?.links"
                :server-id="server?.id"
                :site-id="site?.id"
                :title="$t('Previous Local Backups')"
                @backup-now="backupNow(local_backup_settings)"
                @open-incremental-modal="(files) => openIncrementalModal(files)"
                @open-restore-modal="(files) => openRestoreModal(files)"
                @update-backup-note="(files,note)=>updateBackupNote(files,note)"
            />
        </div>
        </template>
    </single-site>

    <Modal
        :show="openConfirmModal"
        :widthClass="'max-w-850px'"
        :footer-button-title="$t('Restore')"
        :footer-button="true"
        :title="$t('Are you sure you want to restore this backup?')"
        @close="closeConfirmModal()">
        <div class="flex flex-col gap-30px dark:text-white">
            <div class="warning flex items-center bg-delete/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img"/>
                <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b>{{ $t('Warning') }}:</b> {{ $t('All existing files and data on this site will be deleted') }}
                </p>
            </div>
            {{ $t('Create a new backup before restoring?') }}

            <div class="flex flex-col gap-30px">
                <text-input v-model="restoreForm.backup_confirm"
                            :error="restoreForm.errors.backup_confirm"
                            :label="$t('Type')+' RESTORE '+$t('to confirm')"
                            type="text"/>
            </div>
        </div>
        <template #footer>
                <button
                    :disabled="restoreForm.backup_confirm !== 'RESTORE'"
                    aria-expanded="true"
                    aria-haspopup="true"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50" @click.prevent="restoreNow()">
                    <span>{{ $t('Restore') }}</span>
                </button>
        </template>
    </Modal>
    <Modal
        :show="isTakeBackupModalOpen"
        widthClass="max-w-850px"
        :footer-button-title="$t('Take Backup')"
        :footer-button="true"
        :title="$t('Take Backup')"
        @close="isTakeBackupModalOpen=!isTakeBackupModalOpen">
        <div class="flex flex-col gap-30px dark:text-white">
            <label class="inline-flex">
                <input type="radio"
                       value="full"
                       v-model="backup_type"
                       class="hidden peer" />
                <span
                    class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white">
                    <span class="flex flex-col gap-1">
                        <span
                            class="text-base font-normal text-dark dark:text-white">
                            {{ $t('Full Backup') }}
                        </span>
                        <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
                            {{ $t('Are you sure you want to take a full backup? This will create a complete backup of all your data. Future incremental backups will be based on this full backup.') }}
                        </span>

                    </span>
                </span>
            </label>
            <label
                :class="{
                    'cursor-not-allowed opacity-50': !backupSettings?.backup_files?.some(file => file.type === 'incremental_full')
                }"
                class="inline-flex">
                <input
                    :disabled="!backupSettings?.backup_files?.some(file => file.type === 'incremental_full')"
                    type="radio"
                    v-model="backup_type"
                    value="incremental"
                    class="hidden peer" />
                <span
                    class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white">
                    <span class="flex flex-col gap-1">
                        <span
                            class="text-base font-normal text-dark dark:text-white">
                            {{ $t('Incremental Backup') }}
                        </span>
                        <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
                            {{ $t('This will add to your previous backups by only saving the changes since the last backup.') }}
                        </span>

                    </span>
                </span>
            </label>
        </div>
        <template #footer>
            <div class="flex gap-3 flex-row-reverse">
                <button
                    @click.prevent="isTakeBackupModalOpen=!isTakeBackupModalOpen"
                    aria-expanded="true"
                    aria-haspopup="true"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-danger focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50" >
                    <span>{{ $t('Close') }}</span>
                </button>
                <button
                    @click.prevent="takeBackup(backupSettings,backup_type)"
                    aria-expanded="true"
                    aria-haspopup="true"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50" >
                    <span>{{ $t('Backup') }}</span>
                </button>
            </div>
        </template>
    </Modal>
    <Modal
        :show="isIncrementalModalOpen"
        widthClass="w-2/3"
        :footer-button-title="$t('Restore')"
        :footer-button="true"
        title="Incremental Restore"
        @close="isIncrementalModalOpen=!isIncrementalModalOpen">
        <div class="flex flex-col gap-30px dark:text-white">
            <div class="flex flex-col gap-30px">
                <skeleton v-if="load_incremental_files" :columns="4" :rows="4" />
                <table v-else  class="w-full divide-y-1 divide-light dark:divide-mode-light">
                    <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Date') }}
                            </th>
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Type') }}
                            </th>
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Note') }}
                            </th>
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('File') }}
                            </th>
                            <th v-if="site?.has_database" class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Database') }}
                            </th>
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Action') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody
                        v-for="(files,date) in incremental_files"
                        class="bg-white dark:bg-mode-base divide-y-1 divide-light  dark:divide-mode-light text-dark dark:text-white">
                        <tr  class="divide-x-1 divide-light dark:divide-mode-light">
                            <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                {{date}}
                            </td>
                            <td class="px-30px py-2 capitalize text-left text-base font-normal h-60px">
                                {{ files.find(file => file.is_sql === false)?.type?.replace('_', ' ') }}
                            </td>
                            <td class="px-10px py-2 text-base font-normal h-60px w-80">
                                <div
                                    class="w-80"
                                    @blur="updateBackupNote(files,$event.target.innerText.trim())"
                                    contenteditable="true">
                                    {{ files.find(file => file.user_note)?.user_note }}
                                </div>
                            </td>
                            <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                {{files.find(file => file.is_sql === false)?.file_size }}
                            </td>
                            <td v-if="site?.has_database" class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                {{useUnitSize(files.find(file => file.is_sql === true)?.file_size,'KB') }}
                            </td>
                            <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                <button
                                    v-if="!files.some(file => file.status === 'failed')"
                                    :class="{ 'cursor-not-allowed opacity-50': backup_task_running }"
                                    class="inline-flex items-center justify-center min-h-40px border-1 border-primary-light dark:border-dark
                            text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light
                             group-hover:bg-light group-hover:text-primary-light group-hover:border-light
                            transition ease-in-out duration-300 focus:outline-none
                            disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                                    @click.prevent="openRestoreModal(files,files.some(file => !file.is_remote))">
                                    {{ $t('Restore') }}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <template #footer>

                <button
                    @click.prevent="isIncrementalModalOpen=!isIncrementalModalOpen"
                    aria-expanded="true"
                    aria-haspopup="true"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50" >
                    <span>{{ $t('Close') }}</span>
                </button>
        </template>
    </Modal>
</template>

<!-- script -->
<script setup>
import {useForm, usePage} from "@inertiajs/inertia-vue3";
import SingleSite from "@/Pages/Site/SingleSite.vue";
import {useFlash} from "@/Composables/useFlash";
import Modal from "@/Shared/Modal.vue";
import TextInput from "@/Shared/TextInput.vue";
import {onMounted, onUnmounted, ref} from "vue";
import Empty from "@/Shared/Empty.vue";
import {useUnitSize} from "@/Composables/useUnitSize";
import {Inertia} from "@inertiajs/inertia";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import BackupTable from "@/Pages/Site/Components/BackupTable.vue";

const props = defineProps({
    site: Object,
    server: Object,
    remote_backup_settings: Object,
    local_backup_settings: Object,
    local_backup_files: Array | null,
    remote_backup_files: Array | null,
    remote_backup_files_pagination: Object,
    local_backup_files_pagination: Object,
    is_playground: Boolean,
})

const backupUserName = ref(usePage().props.value?.current_white_label ? usePage().props.value?.current_white_label?.branding?.brand_name : 'xCloud');

let openConfirmModal = ref(false);
let isIncrementalModalOpen = ref(false);
let backupSettings = ref(null);
let isTakeBackupModalOpen = ref(false);
const backup_task_running = ref(props.site?.backup_task_running || false);
const incremental_files = ref([]);
const load_incremental_files = ref(false);
const backup_type = ref('full');


onMounted(() => {
    if (window.Echo) {
        console.log("Listening for site backup status changed:", props?.site?.id);
        window.Echo.private("site.backup." + props?.site?.id).listen(
            "SiteBackupStateUpdate",
            (e) => {
                backup_task_running.value = e.backup_task_running;
                Inertia.reload({preserveScroll: true});
            }
        );
    }
});
onUnmounted(() => {
    if (window.Echo) {
        window.Echo.private("site." + props?.site?.id).stopListening(
            "SiteBackupStateUpdate"
        );
    }
});

const openIncrementalModal = (files) => {
    load_incremental_files.value = true;
    isIncrementalModalOpen.value = true;
    axios.get(route('api.site.backup.incremental', {server: props.server.id, site: props.site.id}), {
        params: {
            file: files.find(file => file.is_sql === false)?.id,
        }
    }).then(({data}) => {
        incremental_files.value = data;
    }).catch((error) => {
        console.log(error);
    }).finally(() => {
        load_incremental_files.value = false;
    });
}

const restoreForm = useForm({
    identifier: '',
    backup_confirm: '',
    is_local: null,
});

function closeConfirmModal() {
    openConfirmModal.value = false;
    restoreForm.reset();
}

const openRestoreModal = (files, is_local) => {
    if (backup_task_running.value) {
        useFlash().error('Backup is running, please wait until it is finished.');
    } else {
        restoreForm.identifier = files;
        restoreForm.is_local = is_local;
        openConfirmModal.value = true;
    }
}
const restoreNow = () => {
    if (backup_task_running.value) {
        useFlash().error('Backup is running, please wait until it is finished.');
    } else {
        restoreForm.backup_confirm = '';
        openConfirmModal.value = false;
        restoreForm.post(route('api.site.backup.restore', {server: props.server.id, site: props.site.id}), {
            preserveScroll: true,
            onSuccess: () => {
                useFlash().success('Backup restoring start successfully!')
            },
            onError: (e) => {
                useFlash().error(Object.values(e)[0])
                backup_task_running.value = false;
            },
            onFinish: () => {
                closeConfirmModal();
            }
        })
    }
}

const backupNow = (settings) => {
    if (backup_task_running.value) {
        useFlash().error('Backup is running, please wait until it is finished.');
    } else {
        if (settings?.type === 'incremental'){
            backupSettings.value = settings;
            isTakeBackupModalOpen.value = true;
        }else{
            takeBackup(settings);
        }
    }
}

const takeBackup = (settings,type='full') => {
    isTakeBackupModalOpen.value = false;
    backup_task_running.value = true;
    Inertia.post(route('api.site.backup.backup_now', {server: props.server.id, site: props.site.id}),
        {'is_local': settings?.is_local, type:type },
        {
            preserveScroll: true,
            //for error
            onError: (e) => {
                useFlash().error(Object.values(e)[0])
                backup_task_running.value = false;
            },
        });
}


const getCloudProvider = (provider) => {
    const {cloudProviderIcon} = useCloudProviderIcon(provider);
    return cloudProviderIcon.value;
};

const updateBackupNote = (files,note) => {
    const file =  files.sort((a, b) => b.is_sql - a.is_sql)[0];
    axios.post(route('api.site.backup.update_note', {server: props.server.id, site: props.site.id}), {
        file_id: file?.id,
        user_note: note,
    }).then(({data}) => {
        useFlash().success('Note updated successfully!')
    }).catch((error) => {
        useFlash().error('Failed to update note!')
    });
}
</script>

<!-- style -->
<style scoped>

</style>
