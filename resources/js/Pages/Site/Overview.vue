<template>
    <single-site :server="server" :site="site" active="Site Overview">
        <template v-if="hasIntegrityChecksumError">
            <checksum-verification-alert :site="site" />
        </template>
        <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <div
                v-if="show_mail_alert"
                class="info flex items-center bg-info/20 px-6 py-4 rounded-md"
            >
                <img
                    :src="asset('img/info.svg')"
                    alt="info_img"
                    class="w-6"
                />
                <p
                    class="text-sm text-dark dark:text-white leading-7 pl-3.5"
                >
                    {{ $t('With xCloud you get 100 emails free per month in each team. Manage email for this site from') }} <Link class="underline" :href="route('site.email_provider', [ server.id, site.id])">{{ $t('here') }}</Link>. {{ $t('You can purchase more or add your own email providers from') }} <Link class="underline" :href="route('team.email_provider')">email integration section</Link>. {{ $t('Learn more from our') }} <a target="_blank" href="https://xcloud.host/docs/setting-up-site-emails-for-wordpress-on-xcloud/" class="underline">{{ $t('documentation') }}.</a>
                </p>
                <!-- Close Alert button -->
                <button @click.prevent="hideMailAlert" class="ml-auto flex items-center">
                    <i class="xcloud xc-close-sq text-xl text-blue-500"></i>
                </button>
            </div>
            <div class="grid grid-cols-5 small-laptop:grid-cols-2 wide-tablet:grid-cols-1 gap-30px mobile:gap-20px">
                <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col col-span-2 small-laptop:col-span-1">
                    <!-- Domain -->
                    <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex items-center wide-mobile:px-15px">
                        <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                            {{ $t('Domain') }}
                        </h4>
                    </div>
                    <div class="flex flex-col gap-2">
                        <div class="p-30px bg-white dark:bg-mode-base rounded-md flex flex-col gap-30px tablet:gap-10px">
                            <div class="text-base font-normal flex items-start mobile:flex-wrap">
                                <span class="text-secondary-full dark:text-mode-secondary-dark mobile:block mobile:w-full mr-2 whitespace-nowrap">
                                    {{ $t('Primary Domain') }} :
                                </span>
                                <span class="flex flex-wrap">
                                    <a
                                        v-if="!site.is_domain_updating"
                                        :href="`http://${site.name}`"
                                        class="text-dark dark:text-white hover:underline mr-30px"
                                    >
                                        {{ site.name }}
                                    </a>
                                  <template v-else>
                                      <tooltip title="Updating Domain">
                                        <div class="animate-pulse">
                                          <div class="bg-gray-300 mt-1 h-4 wide-mobile:h-3 w-40 animate-pulse bg-gradient-to-r from-blue-400
                                                    dark:from-[rgb(255 255 255 /.5)] to-blue-500 dark:from-[rgb(255 255 255 /1)] rounded"></div>
                                        </div>
                                      </tooltip>
                                  </template>
                                    <Link
                                        v-if="site?.permissions?.includes('site:manage-domain') && !site.is_domain_updating"
                                        :href="
                                            site.environment === 'staging' || site.environment === 'demo' ?
                                            route('site.staging.environment', [
                                                server.id,
                                                site.id,
                                            ])
                                            : route('site.domain', [
                                                server.id,
                                                site.id,
                                            ])
                                        "
                                        class="relative group cursor-pointer mt-1.5"
                                    >
                                        <span class="text-sm flex text-secondary-light group-hover:text-success-full">
                                            <tooltip title="Edit">
                                              <i class="xcloud xc-edit"></i>
                                            </tooltip>
                                        </span>
                                    </Link>
                                </span>
                            </div>
                        </div>
                        <div
                            v-if="site?.permissions?.includes('site:manage-domain') || additional_domains?.length > 0"
                            class="p-30px bg-white dark:bg-mode-base rounded-md flex flex-col gap-30px tablet:gap-10px">
                            <div class="text-base font-normal flex items-start mobile:flex-wrap">
                                <span class="text-secondary-full dark:text-mode-secondary-dark mobile:block mobile:w-full mr-2 whitespace-nowrap">
                                    {{ $t('Additional Domains') }} :
                                </span>
                                <span class="flex flex-wrap">
                                    <span class="flex flex-col mr-30px">
                                      <div v-for="additional_domain in additional_domains" class="flex">
                                        <a
                                            v-if="additional_domains"
                                            :href="`http://${additional_domain.value}`"
                                            target="_blank"
                                            class="text-dark dark:text-white hover:underline"
                                            :key="additional_domain.value"
                                        >
                                            {{ additional_domain.value }}
                                        </a>

                                        <span v-if="additional_domain.value && additional_domain.status === 'invalid'"
                                              class="text-sm flex text-secondary-light group-hover:text-success-full items-center ml-1 cursor-pointer">
                                          <tooltip title="Subdomain must be under primary domain" :color="'danger'">
                                            <i class="xcloud xc-hexa-warning text-danger"></i>
                                          </tooltip>
                                        </span>
                                      </div>

                                        <Link
                                            v-if="site?.permissions?.includes('site:manage-domain')"
                                            :href="
                                            route('site.domain', [
                                                server.id,
                                                site.id,
                                            ])
                                        "
                                            class="text-dark dark:text-white underline"
                                        >
                                            {{ $t('Add New') }}
                                        </Link>
                                    </span>
                                    <Link
                                        :href="
                                            route('site.domain', [
                                                server.id,
                                                site.id,
                                            ])
                                        "
                                        class="relative group cursor-pointer mt-1.5"
                                    >
                                        <span
                                            v-if="
                                                additional_domains &&
                                                additional_domains?.length > 0
                                            "
                                            class="text-sm flex text-secondary-light group-hover:text-success-full"
                                        >
                                            <tooltip title="Edit">
                                                <i class="xcloud xc-edit"></i>
                                            </tooltip>
                                        </span>

                                    </Link>
                                </span>
                            </div>
                        </div>
                        <div
                            v-if="site?.permissions?.includes('site:manage-domain') && !site?.is_ip_site"
                            class="p-30px bg-white dark:bg-mode-base rounded-md flex flex-col gap-30px tablet:gap-10px">
                            <div class="text-base font-normal flex items-start mobile:flex-wrap">
                                <span class="text-secondary-full dark:text-mode-secondary-dark mobile:block mobile:w-full mr-2 whitespace-nowrap">
                                    {{ $t('Cloudflare Integration') }} :
                                </span>
                                <span class="flex flex-wrap">
                                    <Link
                                        v-if="site?.permissions?.includes('site:manage-domain')"
                                        :href="route('user.integration.cloudflare')"
                                        class="text-dark dark:text-white hover:underline mr-30px underline"
                                    >{{ $t('View Details') }}</Link>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col col-span-3 small-laptop:col-span-1">
                    <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex items-center wide-mobile:px-15px">
                        <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                            {{ $t('Others') }}
                        </h4>
                    </div>
                    <div class="overflow-x-auto w-full">
                        <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                            <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                                <tr v-if="site?.is_wordpress" class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        {{ $t('Page Cache') }}
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <span v-if="has_page_cache"
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-success-full/10 text-sm text-success-full focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-right text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('on') }}</span>
                                        </span>
                                        <span v-else
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-danger/10 text-sm text-danger focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-danger h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-close1 text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('off') }}</span>
                                        </span>
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <Link
                                            v-if="site?.permissions?.includes('site:manage-caching')"
                                            :href="
                                                route('site.caching', [
                                                    server.id,
                                                    site.id,
                                                ])
                                            "
                                            class="text-dark dark:text-white font-medium underline"
                                        >
                                            {{ $t('View Details') }}
                                        </Link>
                                    </td>
                                </tr>
                                <tr v-if="!site.is_ip_site" class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        {{ $t('SSL/HTTPS') }}
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <span v-if="has_ssl_certificate"
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-success-full/10 text-sm text-success-full focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-right text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('on') }}</span>
                                        </span>
                                        <span v-else
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-danger/10 text-sm text-danger focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-danger h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-close1 text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('off') }}</span>
                                        </span>
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <Link
                                            v-if="site?.permissions?.includes('site:manage-ssl')"
                                            :href="
                                                route('site.ssl', [
                                                    server.id,
                                                    site.id,
                                                ])
                                            "
                                            class="text-dark dark:text-white font-medium underline"
                                        >
                                            {{ $t('View Details') }}
                                        </Link>
                                    </td>
                                </tr>
                                <tr v-if="!site.is_node_app" class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        {{ $t('Basic Auth') }}
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <span v-if="has_basic_auth_enabled"
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-success-full/10 text-sm text-success-full focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-right text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('on') }}</span>
                                        </span>
                                        <span v-else
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-danger/10 text-sm text-danger focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-danger h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-close1 text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('off') }}</span>
                                        </span>
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <Link
                                            v-if="site?.permissions?.includes('site:manage-authentication')"
                                            :href="
                                                route('site.basic.authentication', [
                                                    server.id,
                                                    site.id,
                                                ])
                                            "
                                            class="text-dark dark:text-white font-medium underline"
                                        >
                                            {{ $t('View Details') }}
                                        </Link>
                                    </td>
                                </tr>
                                <tr v-if="!site.is_node_app" class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                       {{ $t('Backup') }}
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <span v-if="site?.is_backup_enabled"
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-success-full/10 text-sm text-success-full focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-right text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('on') }}</span>
                                        </span>
                                        <span v-else
                                            class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-danger/10 text-sm text-danger focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-danger h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-close1 text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('off') }}</span>
                                        </span>
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <Link
                                            v-if="site?.permissions?.includes('site:settings')"
                                            :href="
                                                route('site.backup', [
                                                    server.id,
                                                    site.id,
                                                ])
                                            "
                                            class="text-dark dark:text-white font-medium underline"
                                        >
                                            {{ $t('View Details') }}
                                        </Link>
                                    </td>
                                </tr>
                                <tr v-if="site?.is_wordpress" class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        {{ $t('Updates') }}
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <span v-if="site.theme_updates?.length + site.plugin_updates?.length === 0"
                                              class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-success-full/10 text-sm text-success-full focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-right text-white text-xxs"></i>
                                            </span>
                                            <span>{{ $t('No Updates') }}</span>
                                        </span>
                                        <span v-else
                                              class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1 pr-2.5 bg-danger/10 text-sm text-danger focus:outline-0 gap-1.5 uppercase"
                                        >
                                            <span class="rounded-full bg-danger h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center mr-0.5">
                                                <i class="xcloud xc-update text-white text-xxs"></i>
                                            </span>
                                            <span>{{site.theme_updates?.length + site.plugin_updates?.length}} {{ $t('Update') }}</span>
                                        </span>
                                    </td>
                                    <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                        <Link
                                            v-if="site?.permissions?.includes('site:settings')"
                                            :href="
                                                route('site.updates', [
                                                    server.id,
                                                    site.id,
                                                ])
                                            "
                                            class="text-dark dark:text-white font-medium underline"
                                        >
                                            {{ $t('View Details') }}
                                        </Link>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div
                v-if="site?.permissions?.includes('site:manage-events')"
                class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                        {{ $t('Most Recent Event') }}
                    </h4>
                    <div class="flex gap-20px mobile:flex-wrap mobile:gap-10px">
                        <Link
                            :href="route('site.events', [server.id, site.id])"
                            class="inline-flex items-center justify-center rounded-10px border-transparent shadow-none min-h-50px mobile:min-h-40px px-25px py-2px bg-success-full text-base text-white focus:outline-0"
                            aria-expanded="true"
                            aria-haspopup="true"
                        >
                            <span>{{ $t('View All Events') }}</span>
                        </Link>
                    </div>
                </div>

                <task-events-table
                    :show-server="false"
                    :show-site="false"
                    :show-header="false"
                    :has-pagination="false"
                    tableParentClass="p-0 wide-mobile:p-0 mobile:p-0"
                    :taskEvents="taskEvents"
                />
            </div>
        </div>
    </single-site>
</template>

<!-- script -->
<script setup>
import { ref } from "vue";
import SingleSite from "@/Pages/Site/SingleSite.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import TaskEventsTable from "@/Shared/TaskEventsTable.vue";
import {asset} from "laravel-vapor";
import {Link} from "@inertiajs/inertia-vue3";
import ChecksumVerificationAlert from "@/Shared/Site/ChecksumVerificationAlert.vue";

const props = defineProps({
    site: Object,
    server: Object,
    additional_domains: Array,
    has_page_cache: Boolean,
    has_ssl_certificate: Boolean,
    has_basic_auth_enabled: Boolean,
    has_php_execution_on_upload_directory: Boolean,
    has_xml_rpc: Boolean,
    hasIntegrityChecksumError: Boolean,
    taskEvents: Object,
    mail_alert: {
        type: Boolean,
        default: true
    },
})

const show_mail_alert = ref(props.mail_alert && props.site?.is_wordpress);

const hideMailAlert = () => {
    show_mail_alert.value = false;
    axios.post(route('api.site.hide-mail-alert',[
        props.server,
        props.site
    ])).then((response) => {
        console.log(response);
    });
}

</script>

<!-- style -->
<style scoped>

</style>
