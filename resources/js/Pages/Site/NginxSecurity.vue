<template>
    <single-site :server="server" :site="site" :active="isNginx ? t('Nginx and Security') : t('Security')" page-title="Security">
        <Head :title="isNginx ? t('Nginx and Security') : t('Security')" />
        <div v-if="site?.permissions?.includes('site:manage-wpconfig') || site?.permissions?.includes('site:settings')" class="xl:flex">
          <div
              :class="{
                  'grid-cols-1 wide-tablet:grid-cols-1': site?.permissions?.includes('site:manage-wpconfig') && site?.permissions?.includes('site:settings') && site?.is_wordpress,
                  'grid-cols-1': !site?.permissions?.includes('site:manage-wpconfig') || !site?.permissions?.includes('site:settings') || !site?.is_wordpress
              }"
              class="grid gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px flex-1"
          >

              <!-- Nginx Options -->
              <Card
                  v-if="site?.permissions?.includes('site:settings')"
                  :title="t('Security Settings')"
                  buttonStyle="success"
                  :button-title="$t('Save Settings')"
                  @onSubmit="updateNginxConf"
                  @onPrimaryButtonClick="previewNginxConf"
                  :primary-button="true"
                  :use-button="can_edit_security_settings"
                  :disable-primary-button="nginxOptionsForm.processing || isReadingConf || !server.is_connected"
                  :primary-button-loading="isReadingConf"
                  :disableUseButton="disableUseButton()"
                  :loading="nginxOptionsForm.processing"
              >
                <Switch v-if="isRegenerationEnabled"
                    :checked="nginxOptionsForm.enable_7g_firewall"
                        @click.prevent="nginxOptionsForm.enable_7g_firewall = !nginxOptionsForm.enable_7g_firewall"
                >
                  <span class="mr-2">{{ $t('7G Firewall') }}</span>
                </Switch>
                <Error v-if="nginxOptionsForm.errors.enable_7g_firewall" :error="nginxOptionsForm.errors.enable_7g_firewall" />

                <Switch v-if="isRegenerationEnabled && server.stack === 'openlitespeed'"
                    :checked="nginxOptionsForm.enable_8g_firewall"
                        @click.prevent="nginxOptionsForm.enable_8g_firewall = !nginxOptionsForm.enable_8g_firewall"
                >
                  <span class="mr-2">{{ $t('8G Firewall') }}</span>
                </Switch>
                  <Error v-if="nginxOptionsForm.errors.enable_8g_firewall" :error="nginxOptionsForm.errors.enable_8g_firewall" />

                <Switch
                    v-if="isRegenerationEnabled"
                    :checked="nginxOptionsForm.enable_ai_bot_blocker"
                        @click.prevent="nginxOptionsForm.enable_ai_bot_blocker = !nginxOptionsForm.enable_ai_bot_blocker"
                >
                  <span class="mr-2">
                      {{ $t('AI Bot Blocker') }}
                  </span>

                    <Tooltip align="right" title="Blocks known AI bots, scrapers, and automated crawlers from accessing your site.">
                        <i class="xcloud xc-info"></i>
                    </Tooltip>
                </Switch>
                  <Error v-if="nginxOptionsForm.errors.enable_ai_bot_blocker" :error="nginxOptionsForm.errors.enable_ai_bot_blocker" />

                <Switch :checked="nginxOptionsForm.disable_nginx_config_regeneration"
                        @click.prevent="switchNginxFileGeneration"
                >
                  <span class="mr-2">{{ isNginx ? $t('Disable Nginx Config Regeneration') : $t('Disable OpenLiteSpeed Config Regeneration') }}</span>

                    <Tooltip align="right" title="Useful if you want to take over and manage the config files manually."><i class="xcloud xc-info"></i></Tooltip>
                </Switch>
                    <Switch :checked="enable_php_execution_on_upload_directory"
                            :disabled="true"
                        tooltip="This option is disabled by default for security reasons. If you need to enable it, please contact support."
                        @click.prevent="nginxOptionsForm.enable_php_execution_on_upload_directory = false"
                    >
                      <span class="mr-2 opacity-50 text-secondary-full dark:text-mode-secondary-dark">{{ $t('PHP Execution on Upload Directory') }}</span>
                    </Switch>

                    <Switch v-if="isRegenerationEnabled && site?.is_wordpress" :checked="nginxOptionsForm.enable_xml_rpc"
                            @click.prevent="nginxOptionsForm.enable_xml_rpc = !nginxOptionsForm.enable_xml_rpc"
                    >
                      <span class="mr-2">{{ $t('Enable XML-RPC') }}</span>
                    </Switch>

                    <Switch v-if="isRegenerationEnabled" :checked="nginxOptionsForm.modify_x_frame_options"
                            @click.prevent="nginxOptionsForm.modify_x_frame_options = !nginxOptionsForm.modify_x_frame_options"
                    >
                      <span class="mr-2">{{ $t('Edit X-Frame-Options') }}</span>
                    </Switch>
                    <text-input
                        v-if="isRegenerationEnabled && nginxOptionsForm.modify_x_frame_options"
                        v-model="nginxOptionsForm.x_frame_options"
                        :error="nginxOptionsForm.errors.x_frame_options"
                        id="x_frame_options"
                        :label="$t('X-Frame-Options')"
                        :placeholder="$t('SAMEORIGIN')"
                        :note="nginxOptionsForm.errors.x_frame_options ? '' : $t('Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com')"
                    />

                  <Switch
                      v-if="isNginx && isRegenerationEnabled"
                      :checked="nginxOptionsForm.enable_custom_robots_txt"
                          @click.prevent="nginxOptionsForm.enable_custom_robots_txt = !nginxOptionsForm.enable_custom_robots_txt"
                  >
                      <span class="mr-2">
                          {{ $t('Serve robots.txt from file system') }}
                      </span>
                      <Tooltip align="right" title="This option lets you use a custom robots.txt file from your site’s root directory instead of the default one served by the application.">
                          <i class="xcloud xc-info"></i>
                        </Tooltip>
                  </Switch>

                  <Switch
                      v-if="isRegenerationEnabled && site?.is_wordpress"
                     :checked="nginxOptionsForm.wp_fail2ban.enable"
                          @click.prevent="nginxOptionsForm.wp_fail2ban.enable = !nginxOptionsForm.wp_fail2ban.enable"
                  >
                      <span class="mr-2">{{ $t('WP Fail2Ban') }}</span>

                    <Tooltip align="right" title="Protect your site by logging malicious activity to fail2ban for automated banning."><i class="xcloud xc-info"></i> </Tooltip>
                  </Switch>

                  <div
                      class="grid md:grid-cols-1 grid-cols-1 gap-[5px] pl-10px md:-mt-2 -mt-0"
                      v-if="isRegenerationEnabled && nginxOptionsForm.wp_fail2ban.enable && site?.is_wordpress">
                    <check-box
                        v-model="nginxOptionsForm.wp_fail2ban.log_failed_logins"
                    >
                       {{ $t('Block Failed Login Attempts') }}
                        <Tooltip align="right" title="Monitor and ban repeated failed login attempts via system logs."><i class="xcloud xc-info"></i></Tooltip>
                    </check-box>

                    <check-box
                        v-model="nginxOptionsForm.wp_fail2ban.block_common_usernames"
                    >
                        <span class="mr-2">{{ $t('Block Common Usernames') }}</span>
                        <Tooltip align="right" title="Prevent logins using usernames like admin or root—common targets for attacks."><i class="xcloud xc-info"></i></Tooltip>
                    </check-box>

                    <check-box
                        v-model="nginxOptionsForm.wp_fail2ban.block_user_enumeration"
                    >
                        <span class="mr-2">{{ $t('Block User Enumeration') }}</span>
                        <Tooltip align="right" title="Stops attackers from discovering usernames via the REST API or author scans."><i class="xcloud xc-info"></i></Tooltip>
                    </check-box>

                    <check-box
                        v-model="nginxOptionsForm.wp_fail2ban.protect_comments"
                    >
                        <span class="mr-2">{{ $t('Protect Comments') }}</span>
                        <Tooltip align="right" title="Log suspicious comment behavior like spam attempts, closed/draft comment abuse, and password-protected posts."><i class="xcloud xc-info"></i></Tooltip>
                    </check-box>

                    <check-box
                        v-model="nginxOptionsForm.wp_fail2ban.block_spam"
                    >
                        <span class="mr-2">{{ $t('Block Spam') }}</span>
                        <Tooltip align="right" title="Detect and log spam activity for fail2ban to catch bots and spammers."><i class="xcloud xc-info"></i></Tooltip>
                    </check-box>

                    <check-box
                        v-model="nginxOptionsForm.wp_fail2ban.guard_password_resets"
                    >
                        <span class="mr-2">{{ $t('Guard Password Resets') }}</span>
                        <Tooltip align="right" title="Log password reset attempts to detect and block brute-force or phishing activity."><i class="xcloud xc-info"></i></Tooltip>
                    </check-box>

                    <check-box
                        v-model="nginxOptionsForm.wp_fail2ban.guard_pingbacks"
                    >
                        <span class="mr-2">{{ $t('Guard Pingbacks') }}</span>
                        <Tooltip align="right" title="Log pingback errors to prevent abuse via XML-RPC (often used for DDoS attacks)."><i class="xcloud xc-info"></i></Tooltip>
                    </check-box>
                  </div >
              </Card>
          </div>
        </div>

        <modal
            :closeable="true"
            v-if="showNginxModal"
            :show="showNginxModal"
            @close="showNginxModal = false"
            :title="conf_file_name"
            :widthClass="'max-w-950px'"
            @footer-click="showNginxModal = false"
            :footer-button-title="$t('Close')"
            :footer-button="true"
        >
            <div class="flex flex-col">
                <skeleton v-if="isReadingConf" :columns="1" :rows="10"/>
                <v-ace-editor
                    v-else
                    :readonly="true"
                    :options="{
                        fontSize: '10px'
                    }"
                    @init="editorInit"
                    v-model:value="nginxConf"
                    lang="text"
                    :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
                    style="height: 500px"/>
            </div>
        </modal>
    </single-site>
</template>

<!-- script -->
<script setup>

import SingleSite from "@/Pages/Site/SingleSite.vue";
import {computed, onMounted, ref} from "vue";
import Card from "@/Shared/Card.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import Switch from "@/Shared/Switch.vue";
import TextInput from "@/Shared/TextInput.vue";
import {useFlash} from "@/Composables/useFlash";
import Modal from "@/Shared/Modal.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import {VAceEditor} from "vue3-ace-editor";
import {useNavigationStore} from "@/stores/NavigationStore";
import Error from "@/Shared/Error.vue";
import 'ace-builds/src-noconflict/mode-nginx';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/theme-tomorrow_night_blue';
import 'ace-builds/src-noconflict/ext-searchbox';
import Tooltip from "@/Shared/Tooltip.vue";
import { useI18n } from "vue-i18n";
import CheckBox from "@/Shared/CheckBox.vue";
const { t } = useI18n();

let navigation = useNavigationStore();

const {
  site, server, enable_php_execution_on_upload_directory,
  enable_xml_rpc,modify_x_frame_options,x_frame_options,
  disable_nginx_config_regeneration, enable_7g_firewall, enable_8g_firewall, enable_ai_bot_blocker, enable_custom_robots_txt, can_edit_security_settings, wp_fail2ban
} = defineProps({
    site: Object,
    server: Object,
    enable_php_execution_on_upload_directory: Boolean,
    enable_xml_rpc: Boolean,
    disable_nginx_config_regeneration: {
        type: Boolean,
        default: false
    },
    modify_x_frame_options: {
        type: Boolean,
        default: false
    },
    x_frame_options: {
        type: String,
        default: 'SAMEORIGIN'
    },
    enable_custom_robots_txt: {
        type: Boolean,
        default: false
    },
    enable_7g_firewall: {
      type: Boolean,
      default: false
    },
    enable_8g_firewall: {
      type: Boolean,
      default: false
    },
    enable_ai_bot_blocker: {
      type: Boolean,
      default: false
    },
    wp_fail2ban: {
      type: Object,
      default: () => ({
          enable: false,
          log_failed_logins: true,
          block_common_usernames: true,
          block_user_enumeration: true,
          protect_comments: true,
          block_spam: true,
          guard_password_resets: true,
          guard_pingbacks: true,
      })
    },
    can_edit_security_settings: Boolean,
});

const nginxConf = ref('');


const showNginxModal = ref(false);
const isReadingConf = ref(false);

let nginxOptionsForm = useForm({
    disable_nginx_config_regeneration: disable_nginx_config_regeneration,
    enable_php_execution_on_upload_directory: enable_php_execution_on_upload_directory,
    enable_xml_rpc: enable_xml_rpc,
    modify_x_frame_options: modify_x_frame_options,
    x_frame_options: x_frame_options,
    enable_7g_firewall: enable_7g_firewall,
    enable_8g_firewall: enable_8g_firewall,
    enable_custom_robots_txt: enable_custom_robots_txt,
    enable_ai_bot_blocker: enable_ai_bot_blocker,
    wp_fail2ban: wp_fail2ban,
});

const isNginx = server.stack === 'nginx';

const updateMessage = isNginx ?  'Nginx options have been updated successfully' : 'OpenLiteSpeed options have been updated successfully'
const conf_file_name = isNginx ? 'Nginx Configuration' : 'OpenLiteSpeed Configuration';

onMounted(() => {
    if (server.is_connected){

    }else{
        useFlash().error("Server is not connected");
    }
});

const disableUseButton = () => {
    return nginxOptionsForm.disable_nginx_config_regeneration || nginxOptionsForm.processing || !server.is_connected || !can_edit_security_settings;
}

let updateNginxConf = () => {
    nginxOptionsForm.processing = true;
    nginxOptionsForm.post(route('api.site.nginxOptions.update', {server: server.id, site: site.id}), {
        preserveScroll: true,
        onSuccess: () => {
            useFlash().success(updateMessage);
        }
    });
}

const isRegenerationEnabled  = computed(() => {
    return !nginxOptionsForm.disable_nginx_config_regeneration;
});

const editorInit = (editor) => {
    const mode = 'nginx';
    editor.getSession().setMode(`ace/mode/${mode}`);
}

const previewNginxConf = () => {
    isReadingConf.value = true;
    axios.get(route('api.site.nginxOptions.preview', {server: server.id, site: site.id}))
        .then(({data}) => {
            showNginxModal.value = true;
            nginxConf.value = data?.content;
        }).catch(({response}) => {
            if (response?.status===422){
                useFlash().error(response?.data?.message?? t('Failed to load nginx options'));
            }else{
                useFlash().error(t("Failed to load nginx options"));
            }
            showNginxModal.value = false;
        }).finally(() => {
            isReadingConf.value = false;
        });
}
const switchNginxFileGeneration = () => {
    let enableMessage = t("This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config.");
    let disableMessage = t("This will allow xCloud to regenerate OpenLiteSpeed config on any changes made to the site. You will not have to manually regenerate the OpenLiteSpeed config.");
    let titleMessage = isRegenerationEnabled ? t('Disable OpenLiteSpeed Config Regeneration') : t('Enable OpenLiteSpeed Config Regeneration');

    if (isNginx){
        enableMessage = t("This will prevent xCloud from regenerating nginx config on any changes made to the site. You will have to manually regenerate the nginx config.");
        disableMessage = t("This will allow xCloud to regenerate nginx config on any changes made to the site. You will not have to manually regenerate the nginx config.");
        titleMessage = isRegenerationEnabled ? t('Disable Nginx Config Regeneration') : t('Enable Nginx Config Regeneration');
    }

    useFlash().deleteConfirmation({
            title: titleMessage,
            text: nginxOptionsForm.disable_nginx_config_regeneration ? disableMessage : enableMessage,
            width: '600px',
            confirmButtonText: isRegenerationEnabled ? t('Yes, Disable') : t('Yes, Enable'),
            cancelButtonText: t('Cancel')
        }, () => {
            nginxOptionsForm.processing = true;
            axios.post(route('api.site.nginxOptions.switch.regenerate', {server: server.id, site: site.id}), {
                disable_nginx_config_regeneration: !nginxOptionsForm.disable_nginx_config_regeneration
            }).then(({data}) => {
                nginxOptionsForm.disable_nginx_config_regeneration = !nginxOptionsForm.disable_nginx_config_regeneration
                useFlash().success(data?.message?? t('Config file regeneration has been updated successfully'));
            }).catch(error => {
                useFlash().error('Failed to update config file regeneration');
            }).finally(() => {
                nginxOptionsForm.processing = false;
            });
        }
    );
}
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
