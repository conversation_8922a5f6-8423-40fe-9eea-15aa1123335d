<template>
    <single-site :server="server" :site="site" active="SSH/sFTP">
        <Box v-if="can_create_ssh_key">
            <template #header>
                {{ $t('Site SSH/sFTP Access') }}
            </template>
            <template #body>
                <div class="grid grid-cols-3 tablet:grid-cols-1 gap-30px wide-mobile:grid-gap-20px">

                    <text-input
                        :error="form.errors.user_name"
                        type="text"
                        :label="$t('Site Username')"
                        :modelValue="site_user"
                        disabled
                    >
                        <CopyButton align="middle" :content="site_user"></CopyButton>
                    </text-input>

                    <tooltip :title="site_path">
                        <text-input
                            class="w-full"
                            :error="form.errors.user_name"
                            type="text"
                            :label="$t('Site Path')"
                            :modelValue="$filters.textLimit(site_path)"
                            disabled
                        >
                            <CopyButton align="middle" :content="site_path"></CopyButton>
                        </text-input>
                    </tooltip>


                    <text-input
                        :error="form.errors.user_name"
                        type="text"
                        :label="$t('SSH String')"
                        :modelValue="`ssh ${site_user}@${server.public_ip}`"
                        disabled
                    >
                        <CopyButton align="middle" :content="`ssh ${site_user}@${server.public_ip}`"></CopyButton>
                    </text-input>

                </div>

                <div class="mt-8px w-full">
                    <div>
                        <h5 class="text-secondary-full dark:text-mode-secondary-dark pb-2">{{ $t('SSH Authentication') }}</h5>
                        <option-input
                            class="bg-primary-light/10 dark:bg-mode-light"
                            :options="{'public_key': 'Public Key', 'password': 'Password',}"
                            v-model="form.ssh_authentication_mode"
                        />
                    </div>

                    <div v-if="form.ssh_authentication_mode === 'public_key'" class="grid grid-cols-2 wide-tablet:grid-cols-3 tablet:grid-cols-1 gap-30px wide-mobile:grid-gap-20px">
                        <div class="wide-tablet:col-span-2">
                        <SshKeySelector
                            :can_create_ssh_key="can_create_ssh_key"
                            :keys="keys"
                            :selected="form.ssh_keypair_ids"
                            :emptyText="$t('No SSH Keys are available. Please add SSH Key.')"
                            @onSelectionChanged="form.ssh_keypair_ids = $event"
                            @newKeyAdded="form.ssh_keypair_ids.push($event.id)"
                        />
                    </div>
                    </div>

                    <div v-if="form.ssh_authentication_mode === 'password'"
                         class="grid grid-cols-3 tablet:grid-cols-1 gap-30px wide-mobile:grid-gap-20px">
                        <password-input
                            :error="form.errors.ssh_password"
                            v-model="form.ssh_password"
                            :label="$t('Password')"
                            autocomplete="password"
                            placeholder="********"
                        />
                    </div>

                    <Btn
                        :class="{
                         'cursor-not-allowed opacity-70':site.is_disable
                        }"
                        class="mt-5" @click.prevent="submit" :loading="form.processing">
                        {{ $t('Save') }}
                    </Btn>
                </div>
            </template>
        </Box>
        <Box v-if="hasDatabase">
            <template #header>
                {{ $t('Database URL Connection') }}
            </template>
            <template #body>
                <DatabaseConnection
                    :site="site"
                    :server="server"
                    :database-info="database_info"
                    :ssh-authentication-mode="site_ssh_authentication_mode"
                />
            </template>
        </Box>
    </single-site>
</template>

<!-- script -->
<script setup>

import SingleSite from "@/Pages/Site/SingleSite.vue";
import TextInput from '@/Shared/TextInput.vue'
import PasswordInput from '@/Shared/PasswordInput.vue'
import {useForm} from "@inertiajs/inertia-vue3";
import CopyButton from "@/Shared/CopyButton.vue";
import OptionInput from "@/Shared/OptionInput.vue";
import Box from "@/Shared/Box.vue";
import Btn from "@/Shared/Btn.vue";
import SshKeySelector from "@/Shared/Ssh/SshKeySelector.vue";
import {useHelpStore} from "@/stores/HelpStore";
import {onMounted, watch} from "vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Advanced from "@/Shared/Advanced.vue";
import AdvancedFields from "@/Pages/Site/New/Components/Advanced.vue";
import DatabaseConnection from "@/Pages/Site/New/Components/DatabaseConnection.vue";
import {useFlash} from "@/Composables/useFlash";
let helper = useHelpStore();

const props = defineProps({
    site: Object,
    server: Object,
    site_user: String,
    keys: Object,
    site_path: String,
    site_ssh_authentication_mode: String,
    selected_ssh_keypair_ids: Array,
    can_create_ssh_key: Boolean,
    hasDatabase: Boolean,
    database_info: Array
})

let form = useForm({
    user_name: '',
    server_name: '',
    ssh_password: '',
    ssh_authentication_mode: props.site_ssh_authentication_mode,
    ssh_keypair_ids: props.selected_ssh_keypair_ids,
});

const submit = () => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        form.post(route('api.site.ssh.update', [props.server.id, props.site.id]), {
            onSuccess() {

            }
        })
    }
}

onMounted(() => {
    setTimeout(() => {
        helper.toggleOpenWithAccordions([],[form.ssh_authentication_mode]);
    }, 100);
})

//ssh_authentication_mode
watch(
    () => form.ssh_authentication_mode,
    () => {
        helper.toggleOpenWithAccordions([],[form.ssh_authentication_mode]);
    }
);
</script>
