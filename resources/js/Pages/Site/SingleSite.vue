<template>
    <Head :title='(pageTitle ? pageTitle: $inertia.page.component.split("/")[1]) + " - " + site.name'/>

    <SingleLayout>
        <template v-slot:header>
            <div class="basis-250px small-laptop:basis-50px shrink-0 bg-white dark:bg-mode-light mr-2px
                    p-30px small-laptop:px-10px rounded-tl-lg">
                <Link
                    :href="route('site.index')"
                    class="inline-flex"
                >
                    <span class="bg-light dark:bg-mode-base rounded-md inline-flex items-center justify-center text-xs
                            text-secondary-light ease-in-out duration-300 w-30px h-30px shrink-0">
                        <i class="xcloud xc-angle_left"></i>
                    </span>
                    <span class="text-xl ml-10px text-secondary-full hover:text-dark dark:hover:text-white small-laptop:hidden">
                        {{ $t('Back To Sites') }}
                    </span>
                </Link>
            </div>
            <div class="grow flex items-center p-30px bg-white dark:bg-mode-light rounded-tr-lg gap-20px wide-mobile:flex-wrap
                        mobile:p-20px wide-mobile:gap-10px w-[calc(theme(width.full)-theme(spacing.50px))]">
                <div class="flex items-center">
                    <span class="w-50px tablet:w-40px mobile:w-30px mr-15px shrink-0 ">
                        <img
                            :src="siteFavicon"
                            alt="site favicon"
                            @load="handleImageLoad"
                            @error="handleImageError"
                            class="w-12 h-12"
                        />
                    </span>
                    <div class="flex flex-col">
                        <div class="flex items-center flex-wrap gap-x-3 gap-y-1 mb-10px tablet:mb-1.5">
                          <h2 class="text-3xl tablet:text-2xl mobile:text-lg text-dark dark:text-white leading-none relative flex items-center">
                            <template v-if="!site.is_domain_updating">
                              <CopyAbleText :text="site.site_url">
                                {{ site.name }}
                              </CopyAbleText>
                            </template>
                            <template v-else>
                              <tooltip title="Updating Domain">
                                <div class="animate-pulse">
                                  <div class="bg-gray-300 wide-mobile:mb-2 h-4 wide-mobile:h-3 w-36 animate-pulse bg-gradient-to-r from-blue-400
                                            dark:from-[rgb(255 255 255 /.5)] to-blue-500 dark:from-[rgb(255 255 255 /1)] rounded"></div>
                                </div>
                              </tooltip>
                            </template>
                          </h2>

                          <!-- Staging Switcher -->
                          <StagingSwitcher
                              v-if="site?.is_wordpress"
                              :title="getSiteEnvironmentReadable(site.environment)"
                              :position="'center'"
                              @onChangeDropDown="onChangeDropDown"
                              :environment="site.environment"
                          >
                            <template #items>
                              <div class="flex flex-col gap-1.5">
                                <!-- Staging Site -->
                                <button
                                    v-if="site.environment === 'staging'"
                                    class="inline-flex gap-2 items-center p-2 text-left rounded "
                                    :class="{'bg-light dark:bg-mode-light' : site.environment === 'staging'}"
                                    @click.prevent="$inertia.visit(route('site.overview',{server: server.id, site: site.id}))"
                                >
                                  <span class="text-sm text-secondary-full dark:text-mode-secondary-dark">
                                    {{ site.name }}
                                  </span>
                                  <span class="text-xxs text-secondary-full dark:text-mode-secondary-dark px-1 py-0.5 rounded bg-light dark:bg-mode-light leading-tight">
                                    {{ getSiteEnvironmentReadable(site.environment) }}
                                  </span>
                                  <span v-if="site.environment === 'staging'"
                                      class="inline-flex items-center justify-center text-xxs text-success-light ml-auto mr-1" ref="dropDown">
                                      <i class="xcloud xc-right"></i>
                                  </span>
                                </button>

                                <!-- Production Site -->
                                <button
                                    v-if="site.environment === 'production' || (site.environment === 'staging' || site.environment === 'staging_with_own_domain' && productionSite)"
                                    class="inline-flex gap-2 items-center p-2 text-left rounded "
                                    :class="{'bg-light dark:bg-mode-light' : site.environment === 'production' }"
                                    @click.prevent="$inertia.visit(route('site.overview',{server: server.id, site: productionSite?.id ?? site.id}))"
                                >
                                  <span class="text-sm text-secondary-full dark:text-mode-secondary-dark">
                                    {{ productionSite?.name ?? site.name }}
                                  </span>
                                  <span class="text-xxs text-success-light px-1 py-0.5 rounded bg-success-light/20 leading-tight">
                                    {{ productionSite?.environment ?? site?.environment }}
                                  </span>
                                  <span v-if="site.environment === 'production'"
                                      class="inline-flex items-center justify-center text-xxs text-success-light ml-auto mr-1" ref="dropDown">
                                      <i class="xcloud xc-right"></i>
                                  </span>
                                </button>

                                <!-- Staging Sites -->
                                <button
                                    v-for="stagingSite in stagingSites"
                                    class="inline-flex gap-2 items-center p-2 text-left rounded"
                                    :class="{ 'bg-light dark:bg-mode-light' : stagingSite.environment === 'staging' && site.id === stagingSite.id}"
                                    @click.prevent="$inertia.visit(route('site.overview',{server: server.id, site: stagingSite.id}))"
                                >
                                  <span class="text-sm text-secondary-full dark:text-mode-secondary-dark">
                                    {{ stagingSite.name }}
                                  </span>
                                  <span class="text-xxs text-secondary-full dark:text-mode-secondary-dark px-1 py-0.5 rounded bg-light dark:bg-mode-light leading-tight">
                                    {{ $t('staging') }}
                                  </span>
                                  <span v-if="stagingSite.environment === 'staging' && site.id === stagingSite.id"
                                      class="inline-flex items-center justify-center text-xxs text-success-light ml-auto mr-1" ref="dropDown">
                                      <i class="xcloud xc-right"></i>
                                  </span>
                                </button>

                                <!-- Add Staging Environment -->
                                <button
                                    class="inline-flex items-center p-2 text-left rounded"
                                    @click.prevent="prepareStagingDeployment"
                                    v-if="site.environment === 'production' && site.status === 'provisioned'
                                          && site?.permissions?.includes('site:manage-staging')
                                          && Inertia.page.props.current_team?.active_plan?.name !== 'free'
                                    "
                                >
                                  <span class="hover:underline underline-offset-2 text-sm text-secondary-full dark:text-mode-secondary-dark">{{ $t('Add Staging environment') }}</span>
                                </button>
                              </div>
                            </template>
                          </StagingSwitcher>
                        </div>
                        <div class="flex gap-x-15px gap-y-1.5 flex-wrap">
                            <span class="text-dark dark:text-mode-secondary-light hover:text-primary-light dark:hover:text-primary-light
                                    text-sm flex items-center">
                                <span class="w-4 mobile:w-3 mr-2">
                                    <img :src="cloudProviderIcon"/>
                                </span>
                                <Link
                                    v-if="site?.can_view_server"
                                    :href="route('server.show', props.server.id)" class="whitespace-nowrap">
                                    {{ props.server?.name }}
                                </Link>
                                <span
                                    v-else
                                    class="whitespace-nowrap"
                                >
                                    {{ props.server?.name }}
                                </span>
                            </span>
                            <span class="text-dark dark:text-mode-secondary-light hover:text-primary-light dark:hover:text-primary-light text-sm flex items-center">
                                <span class="w-4 mobile:w-3 mr-1.5 text-primary-light inline-flex">
                                    <i class="xcloud xc-web"></i>
                                </span>
                                <a
                                    v-if="!site.is_domain_updating"
                                    target="_blank"
                                    :href="site.site_url"
                                    class="whitespace-nowrap">
                                    {{ $t('Visit Site') }}
                                </a>
                                <template v-else>
                                  <tooltip title="Updating Domain">
                                    <div class="animate-pulse">
                                      <div class="bg-gray-300 wide-mobile:mb-2 h-4 wide-mobile:h-3 w-24 animate-pulse bg-gradient-to-r from-blue-400
                                                dark:from-[rgb(255 255 255 /.5)] to-blue-500 dark:from-[rgb(255 255 255 /1)] rounded"></div>
                                    </div>
                                  </tooltip>
                                </template>
                            </span>
                            <span
                                v-if="site?.permissions?.includes('site:access-magic-login') &&  site?.is_wordpress"
                                class="md:hidden text-dark dark:text-mode-secondary-light hover:text-primary-light dark:hover:text-primary-light text-sm flex items-center">
                                <MagicLoginButton
                                v-if="!site.is_domain_updating"
                                :site="site"
                                :external="true"
                                :buttonClass="'whitespace-nowrap'"
                                :showLogoutIcon="true"
                                />
                                <template v-else>
                                  <tooltip title="Updating Domain">
                                    <div class="animate-pulse">
                                      <div class="bg-gray-300 wide-mobile:mb-2 h-4 wide-mobile:h-3 w-24 animate-pulse bg-gradient-to-r from-blue-400
                                                dark:from-[rgb(255 255 255 /.5)] to-blue-500 dark:from-[rgb(255 255 255 /1)] rounded"></div>
                                    </div>
                                  </tooltip>
                                </template>
                            </span>
                            <div
                                class="flex items-center"
                                v-if="site.tags.length > 0"
                            >
                                <span
                                    class="text-sm text-dark dark:text-mode-secondary-light rounded-md"
                                >
                                    #{{ site.tags[0]?.name }}
                                </span>

                                <tooltip
                                    class="text-xs cursor-pointer ml-2"
                                    :title="
                                        site.tags
                                            .slice(1)
                                            .map((tag) => tag.name)
                                            .join(', ')
                                    "
                                >
                                    <h6 class="text-sm text-primary-dark dark:text-primary-light"
                                        v-if="site.tags.length > 1"
                                    >
                                        +{{ site.tags.length - 1 }}
                                    </h6>
                                </tooltip>
                            </div>
                            <span v-if="site?.has_vulnerability_scan && site?.vulnerabilities_count > 0" class="text-dark dark:text-mode-secondary-light hover:text-primary-light dark:hover:text-primary-light text-sm flex items-center">
                                <span class="w-4 mobile:w-3 mr-1.5 text-primary-light inline-flex">
                                    <i class="xcloud xc-bug text-danger"></i>
                                </span>
                                <span class="mr-1">{{ site?.vulnerabilities_count }}</span>
                                <Link
                                    target="_blank"
                                    :href="route('site.vulnerability-scan',{
                                        server: site.server,
                                        site: site.id
                                    })"
                                    class="whitespace-nowrap">
                                    {{ site?.vulnerabilities_count > 1 ? $t('Issues') : $t('Issue') }}
                                </Link>
                            </span>
                        </div>
                        <!-- Load Sites Tags -->
                        <!-- <div class="flex gap-2 mt-10px flex-wrap">
                            <span
                                v-for="(tag, id) in site.tags"
                                :key="id"
                                class="text-xs p-2 text-secondary-full dark:text-mode-secondary-light bg-light dark:bg-mode-base px-5px py-2px rounded-md"
                            >
                                {{ tag.name }}
                            </span>
                        </div> -->
                    </div>
                </div>
                <span class="relative group cursor-pointer ml-auto inline-flex gap-2.5 items-center">
                    <tooltip
                        :title="site.is_multisite && site.multisite_subdomain ? 'Staging is disabled for multisite subdomain' : ''"
                        color="warning"
                    >
                      <button
                          v-if="site.status === 'provisioned' && site.state === 'Success'
                                && site?.permissions?.includes('site:manage-staging') && site?.is_wordpress
                                && site?.environment === 'production'
                                && Inertia.page.props.current_team?.active_plan?.name !== 'free'
                          "
                          @click.prevent="prepareStagingDeployment"
                         class="hidden md:inline-flex min-h-50px justify-center items-center gap-x-2 border-1 border-primary-light
                                  dark:border-dark bg-transparent bg px-4 py-1 rounded-10px text-primary-light dark:text-white text-base
                                  wide-mobile:text-sm whitespace-nowrap translate duration-75 ease-in-out"
                        :disabled="site.is_multisite && site.multisite_subdomain"
                        :class="{'cursor-not-allowed opacity-50': site.is_multisite && site.multisite_subdomain}"
                      >
                          <span class="text-lg inline-flex">
                              <i class="xcloud xc-rocket"></i>
                          </span>
                          {{ $t('Deploy Staging') }}
                      </button>

                      <button
                          v-if="site.status === 'provisioned' && site.state === 'Success'
                                && site?.permissions?.includes('site:manage-staging') && site?.type === 'laravel'
                                && Inertia.page.props.current_team?.active_plan?.name !== 'free'
                          "
                          @click.prevent="deployLaravel"
                             class="hidden md:inline-flex min-h-50px justify-center items-center gap-x-2 border-1 border-primary-light
                                      dark:border-dark bg-transparent bg px-4 py-1 rounded-10px text-primary-light dark:text-white text-base
                                      wide-mobile:text-sm whitespace-nowrap translate duration-75 ease-in-out ml-2"
                            :disabled="deployingLaravel"
                            :class="{'cursor-not-allowed opacity-50': deployingLaravel}"
                          >
                              <span class="text-lg inline-flex">
                                  <i class="xcloud xc-rocket"></i>
                              </span>
                              <span v-text="deployingLaravel ? $t('Deploying...') : $t('Deploy Now')"></span>
                          </button>
                    </tooltip>
                    <MagicLoginButton
                    v-if="site.status === 'provisioned' && site.state === 'Success' && site?.permissions?.includes('site:access-magic-login') && site?.is_wordpress"
                    :site="site"
                    :external="false"
                    :buttonClass="'hidden md:inline-flex min-h-50px justify-center items-center gap-x-2 border-1 border-primary-light dark:border-dark bg-transparent px-4 py-1 rounded-10px text-primary-light dark:text-white text-base wide-mobile:text-sm whitespace-nowrap transition duration-75 ease-in-out'"
                    :showLogoutIcon="true"
                    />
                    <a
                        v-if="
                            site.type === 'phpmyadmin' && site.state === 'Success'
                            && site?.permissions?.includes('site:access-magic-login')
                            && server?.permissions?.includes('server:manage-databases')
                           "
                        :href="route('server.phpmyadmin-login', server.id)"
                        target="_blank"
                        class="min-h-50px inline-flex justify-center items-center gap-x-2 border-1 border-primary-light dark:border-dark bg-transparent bg px-4 py-1 rounded-10px text-primary-light dark:text-white font-medium whitespace-nowrap translate duration-75 ease-in-out"
                    >
                        <span class="text-lg inline-flex rotate-180"><i class="xcloud xc-logout"></i></span>
                        {{ $t('Magic Login') }}
                    </a>
                    <SiteActions
                        :site="site"
                        :staging-sites="stagingSites"
                        :server="server"
                        :title="$t('Actions')"
                        position="right"
                    ></SiteActions>
                </span>
            </div>
        </template>

        <template v-slot:sidebar>
            <Sidebar @updateSearch="handleSearch" :filteredItems="filteredMenuItems" :items="sidebarItems"></Sidebar>
        </template>

        <template v-slot:content>
            <div
                class="flex py-20px divide-x-2 divide-white/10 dark:divide-transparent bg-primary-light dark:bg-dark w-full tablet:grid tablet:grid-cols-1 tablet:gap-30px tablet:divide-x-0 mobile:gap-15px"
            >
                <div
                    class="flex divide-x-2 divide-white/10 dark:divide-transparent grow mobile:flex-col mobile:divide-x-0 mobile:gap-15px"
                >
                    <div
                        class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px"
                    >
                        <h4
                            class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2"
                        >
                            {{ $t('IP Address') }}
                        </h4>
                        <h5 class="text-white text-base leading-none">
                            <CopyAbleText :text="server.public_ip"/>
                        </h5>
                    </div>
                    <div
                        v-if="site?.is_wordpress"
                        class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px"
                    >
                        <h4
                            class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2"
                        >
                            {{ $t('WordPress') }}
                        </h4>
                        <h5 class="text-white text-base leading-none">
                            <Link
                                v-if="site?.permissions?.includes('site:manage-update')"
                                :href="route('site.updates',{
                                    server: server.id,
                                    site: site.id
                                })"
                            >  {{ site?.wordpress_version }} </Link>
                            <span v-else>
                                {{ site?.wordpress_version }}
                            </span>
                        </h5>
                    </div>
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ $t('PHP') }}
                        </h4>
                        <h5 class="text-white text-base leading-none">
                            <Link
                                v-if="site?.permissions?.includes('site:settings')"
                                :href="route('site.settings',{
                                    server: server.id,
                                    site: site.id
                                })"
                            >  {{ site?.php_version }} </Link>
                            <span v-else>
                                {{ site?.php_version }}
                            </span>
                        </h5>
                    </div>
                </div>
                <div class="flex divide-x-2 divide-white/10 dark:divide-transparent grow mobile:flex-col
                            mobile:divide-x-0 mobile:gap-15px">
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase
                                leading-none mobile:basis-1/2">
                            {{ $t('Site User') }}
                        </h4>
                        <h5 class="text-white text-base leading-none">
                            <Link
                                v-if="site?.permissions?.includes('site:manage-ssh-sftp')"
                                :href="route('site.ssh',{
                                    server: server.id,
                                    site: site.id
                                })"
                            >
                                {{site?.site_user}}
                            </Link>
                            <span v-else>
                                {{ site?.site_user }}
                            </span>
                        </h5>
                    </div>
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ $t('Size') }}
                        </h4>
                        <h5 class="text-white text-base leading-none">
                            <Link
                                v-if="site?.permissions?.includes('site:manage-monitoring')"
                                :href="route('site.monitoring',{
                                    server: server.id,
                                    site: site.id
                                })"
                            >
                              {{  site?.site_size ? site?.site_size.disk : '0' }} </Link>
                            <span v-else>
                                {{  site?.site_size ? site?.site_size.disk : '0' }}
                            </span>
                        </h5>
                    </div>
                    <div v-if="site?.is_wordpress" class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ $t('Updates') }}
                        </h4>
                        <Link v-if="site?.permissions?.includes('site:manage-update')" :href="`${BASE_URL}/updates`" >
                        <h5 class="text-white text-base leading-none">
                            {{ site?.theme_updates.length + site?.plugin_updates.length + site?.wp_core_updates.length }}
                        </h5>
                        </Link>
                        <span v-else class="text-white text-base leading-none">
                            {{ site?.theme_updates.length + site?.plugin_updates.length + site?.wp_core_updates.length }}
                        </span>
                    </div>
                </div>
            </div>
            <!--
            <div
                class="flex items-center gap-20px px-50px small-laptop:px-30px py-15px bg-primary-dark
                            dark:bg-mode-focus-dark rounded-br-lg mobile:px-20px mobile:flex-col mobile:items-start
                            mobile:gap-10px">
                <div>
                          <span class="text-white dark:text-mode-secondary-light text-base">
                              Disk Usage:
                              <span class="font-bold px-1">4.9 GB</span>of
                              <span class="font-bold px-1 dark:text-white">18 GB</span>used
                          </span>
                </div>

                <div class="ml-auto mobile:ml-0">
                          <span class="text-white dark:text-mode-secondary-light text-base">
                              Checked: 1 Hour Ago
                            <Link href="#" class="font-bold pl-2 underline dark:text-white">Check Again</Link>
                          </span>
                </div>
            </div>
            -->
            <disable-warning :site="site" />
            <slot></slot>
        </template>
    </SingleLayout>

    <!-- Deploy staging modal -->
    <Modal
        @close="deployStagingModal = false; resetDeploymentForm()"
        :footer-button-title="$t('Archive')"
        :footer-button="true"
        :show="deployStagingModal"
        :title="$t('Want To Create A Staging Site For')+' '+ site.name +'?'"
        :width-class="'max-w-[1000px]'"
       :is-overflow-hidden="false"
        >
      <div class="flex flex-col gap-30px">
        <div class="warning flex items-center bg-warning/20 px-6 py-4 rounded-md">
          <img :src="asset('img/warning.svg')" alt="warning_img" />
          <p class="text-sm text-dark dark:text-white leading-7 pl-4">
            {{ $t('A staging site is a secure clone of your live website for testing and development. It lets you experiment with plugins, themes, and changes risk-free before deploying them to live.') }}
          </p>
          </div>
          <div class="relative w-full font-normal text-sm  leading-[20px]">
              <div class="mt-5 grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px wide-mobile:gap-4 mb-30px">
                  <div
                      :class="{
                          'border-secondary-light dark:border-mode-focus-light':
                              deploymentSiteForm.staging_env_type !== 'temporary_domain',
                          'border-primary-light ring-1 ring-primary-light':
                              deploymentSiteForm.staging_env_type === 'temporary_domain',
                      }"
                      class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                      @click="toggleTemporaryDomain"
                  >
                      <div class="shrink-0 inline-flex items-center justify-center">
                          <img
                              :src="asset('img/png/staging2.png')"
                              alt="xcloud_logo"
                              class="w-12 h-auto"
                          />
                      </div>
                      <div class="flex flex-col gap-2">
                          <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                              {{ $t('Test Domain') }}
                          </h5>
                          <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                              {{ $t('Create a demo site with our test domains and customize it before going live.') }}
                          </p>
                      </div>
                  </div>

                  <div
                      :class="{
                          'border-primary-light ring-1 ring-primary-light':
                              deploymentSiteForm.staging_env_type ===
                              'own_domain',
                          'border-secondary-light dark:border-mode-focus-light':
                              deploymentSiteForm.staging_env_type !==
                              'own_domain',
                      }"
                      class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                      @click="toggleOwnDomain()"
                  >
                      <div class="shrink-0 inline-flex items-center justify-center">
                          <img
                              :src="asset('img/png/live.png')"
                              alt="xcloud_logo"
                              class="w-12 h-auto"
                          />
                      </div>
                      <div class="flex flex-col gap-2">
                          <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                              {{ $t('Custom Domain') }}
                          </h5>
                          <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full
                                          dark:text-mode-secondary-light"
                          >
                              {{ $t('Enter your custom domain by simply pointing your domain to the server.') }}
                          </p>
                      </div>
                  </div>
              </div>

              <div>
                  <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md">
                      <div class="grid grid-cols-1 w-full">
                          <div class="domain-selector">
                              <div>
                                  <div v-if="deploymentSiteForm.staging_env_type !== 'temporary_domain'">
                                      <text-input
                                          v-model="deploymentSiteForm.custom_domain"
                                          :error="deploymentSiteForm.errors.name"
                                          :label="$t('Domain Name')"
                                          :placeholder="$t('yourdomain.com')"
                                      />
                                  </div>


                                  <div v-if="stagingEnvType === 'temporary_domain'" class="relative w-full font-normal text-sm  leading-[20px]">
                                      <div>
                                          <text-input
                                              v-model="deploymentSiteForm.name"
                                              :error="deploymentSiteForm.errors.name"
                                              :label="$t('Domain Name')"
                                              :placeholder="$t('testing')"
                                              class="flex-1 bg-transparent-input"
                                          />

                                          <div class="absolute top-9 right-2"
                                               @click="toggleDropdown"
                                               ref="dropdownRef">
                                              <div class="max-w-[117px] w-full px-3 z-50 py-2 bg-[#EDF2F8] dark:bg-[#1D2838] rounded-[4px] text-[#2A3268] dark:text-[#919DB9]  flex items-center justify-between cursor-pointer">
                                                  <div class="flex-1 min-w-0">
                                                      <Tooltip
                                                          :title="'.' + selected_staging_domain || 'Select a Domain'"
                                                          align="top"
                                                          color="primary">
                                                          <p class="truncate text-sm leading-5 max-w-[90px]">{{ '.' + selected_staging_domain || 'Select a Domain' }}</p>
                                                      </Tooltip>
                                                  </div>
                                                  <div
                                                      class="h-4 aspect-square shrink-0 text-xxxs flex items-center justify-end rounded cursor-pointer">
                                                      <i :class="[
                                                        'xcloud xc-angle_down transition-transform duration-300 ',
                                                        isDropdownOpen ? 'rotate-180' : 'rotate-0',
                                                    ]"
                                                      ></i>
                                                  </div>
                                              </div>
                                          </div>

                                          <div v-if="isDropdownOpen"
                                               class="absolute min-w-[130px] top-[72px] right-0 z-50  bg-white dark:bg-[#171A30] border border-[#C1C5DE] dark:border-[#313A6C] rounded-[4px] text-[#919DB9] dark:text-[#919DB9] flex flex-col items-center gap-[12px] cursor-pointer p-[10px] whitespace-nowrap overflow-hidden text-ellipsis"
                                               style="max-width: 100%;">
                                              <ul class=" flex flex-col mx-auto">
                                                  <li
                                                      v-for="domain in availableDomains"
                                                      :key="domain"
                                                      @click="selectDomain(domain)"
                                                      class="p-1 cursor-pointer hover:text-[#2A3268] dark:hover:text-[#FFFFFF] "
                                                  >
                                                      {{ '.' + domain }}
                                                  </li>
                                              </ul>
                                          </div>
                                      </div>
                                  </div>

                                  <div class="mt-3"
                                       v-if="
                                        showCloudflareDnsOption &&
                                        deploymentSiteForm.staging_env_type === 'own_domain'
                                    "
                                  >
                                      <label class="inline-flex mr-5">
                                          <input
                                              type="checkbox"
                                              class="hidden peer"
                                              :disabled="
                                                !hasCloudflareIntegration
                                            "
                                              v-model="useCloudflareDns"
                                              :class="{
                                                'opacity-50 cursor-not-allowed':
                                                    !hasCloudflareIntegration,
                                            }"
                                          />
                                          <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                                                    before:bg-transparent before:border-1 before:border-secondary-light
                                                    dark:border-mode-secondary-dark before:rounded before:mt-0.5
                                                    before:mr-2.5 before:text-xxxs before:inline-flex before:items-center
                                                    before:justify-center before:text-transparent before:outline-none
                                                    before:transition before:duration-200 peer-checked:before:border-success-full
                                                    peer-checked:before:bg-success-full peer-checked:before:text-white"
                                          >
                                            <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
                                                <span
                                                    class="flex"
                                                    :class="{
                                                        'opacity-50 cursor-not-allowed':
                                                            !hasCloudflareIntegration,
                                                    }"
                                                >
                                                    <span>
                                                      {{ $t('Add DNS and SSL Certificate on Cloudflare') }}
                                                    </span>
                                                    <img :src="
                                                        asset(
                                                            'img/CF_logomark.png'
                                                        )"
                                                         alt="cloudflare logo"
                                                         class="ml-1 w-8 h-4"
                                                    />
                                                    <span>&nbsp;{{ $t('(Optional)') }}</span>
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                                                    {{ $t('Integrate Cloudflare forAutomatic DNS and SSL management.') }} (<Link
                                                    class="underline"
                                                    :href="
                                                            route(
                                                                'user.integration.cloudflare'
                                                            )
                                                        "
                                                    v-text="
                                                            'Manage your Cloudflare Integration'
                                                        "
                                                />)
                                                </span>
                                            </span>
                                        </span>
                                      </label>

                                      <suggestion
                                          class="mt-5"
                                          v-if="
                                            hasCloudflareIntegration &&
                                            deploymentSiteForm.domain_active_on_cloudflare &&
                                            hasAdditionalDomains
                                        "
                                          :message="$t('If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.')"
                                          :light-mode="true"
                                      >
                                      </suggestion>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>

              <Skeleton v-if="loader" class="mt-30px" columns="2"/>

              <div v-else-if="
                            !loader &&  !isIpSite &&
                            deploymentSiteForm.staging_env_type === 'own_domain'
                        ">
                  <div
                       class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base dark:bg-mode-base rounded-md"
                  >
                      <div v-if="showDomainSetup">
                          <DNS :form="deploymentSiteFormCustomize" :server="server" :dns-verification-toaster="true"/>
                          <Https :form="deploymentSiteForm" :server="server" />
                      </div>

                      <div v-else>
                          <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                              {{ $t('DNS Setup') }}
                          </h3>
                          <div class="grid grid-cols-1 w-full">
                              <suggestion
                                  class="mt-3"
                                  type="success"
                                  :message="dnsHelpText"
                                  :light-mode="true"
                              />
                          </div>
                      </div>
                  </div>
              </div>
          </div>
        </div>
      <template #footer>
          <button
              @click.prevent="deployStaging()"
              class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                                py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50 float-right"
              aria-expanded="true" aria-haspopup="true"
              :disabled="deployingStaging"
              :class="{ 'cursor-not-allowed opacity-50': deployingStaging }"
          >
            <span v-text="deployingStaging ? $t('Preparing Deployment..') : $t('Deploy Staging Site')"></span>
          </button>
      </template>
    </Modal>
</template>

<script setup>
import SingleLayout from "@/Layouts/SingleLayout.vue";
import SiteActions from "@/Pages/Site/Components/SiteActions.vue";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import Sidebar from "@/Shared/Sidebar.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {Link, useForm, usePage} from "@inertiajs/inertia-vue3";
import {reactive, ref, computed, watch, onMounted} from "vue";
import { useCloudProviderIcon } from "@/Composables/useCloudProviderIcon.js";
import {asset} from "laravel-vapor";
import DisableWarning from "@/Pages/Site/Components/DisableWarning.vue";
import Fuse from 'fuse.js';

const searchKeyword = ref('');
import TextInput from "@/Shared/TextInput.vue";
import Modal from "@/Shared/Modal.vue";
import {Inertia} from "@inertiajs/inertia";
import {useFlash} from "@/Composables/useFlash";
import StagingSwitcher from "@/Shared/StagingSwitcher.vue";
import {useNameGenerator} from "@/Composables/useNameGenerator";
import {useNavigationStore} from "@/stores/NavigationStore";
import { useI18n } from "vue-i18n";
import { userClickOutside } from "@/Shared/Events/userClickOutside";
import MagicLoginButton from "@/Shared/MagicLoginButton.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Https from "@/Pages/Site/New/Components/Https.vue";
import DNS from "@/Pages/Site/New/Components/DNS.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import {useSiteTypeIcon} from "@/Composables/useSiteTypeIcon.js";

const { t } = useI18n();

const props = defineProps({
    site: Object,
    server: Object,
    active: String,
    pageTitle: String,
});

let stagingEnvType = ref("temporary_domain");
let deployingStaging = ref(false);
let deployingLaravel = ref(false);
let showCloudflareDnsOption = ref(false);
let hasCloudflareIntegration = ref(usePage().props?.value?.has_cloudflare_integration ?? false);
let hasAdditionalDomains = ref(false);
let useCloudflareDns = ref(false);

const deploymentSiteForm = useForm({
    previousDomain: props.site.name,
    name: '',
    title: "",
    staging_env_type: stagingEnvType,
    ssl_provider: "",
    ssl_certificate: "",
    ssl_private_key: "",
    custom_domain: "",
    domain_active_on_cloudflare: "",
    cloudflare_account_id: "",
    cloudflare_zone_id: "",
    subdomain: "",
    site_name: "",
});

const deploymentSiteFormCustomize = computed(() => {
    return {
        ...deploymentSiteForm,
        name: deploymentSiteForm.custom_domain
    };
});

const getSiteEnvironmentReadable = (environment) => {
  if(environment === 'production') {
    return 'Production';
  } else if(environment === 'staging_with_own_domain' || environment === 'staging') {
    return 'Staging';
  }else{
    return 'Demo';
  }
}

let loader = ref(false);
let showDomainSetup = ref(true);
const navigation = useNavigationStore();

const isIpSite = computed(() => {
    return deploymentSiteForm.name.trim() === props.server.public_ip || deploymentSiteForm.name.trim().match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/);
});

const dnsHelpText = computed(() => {
    const brandName = Inertia.page.props?.current_white_label
        ? Inertia.page.props.current_white_label.branding.brand_name
        : 'xCloud';
    return `Your DNS setup and SSL Certificate will be done by Cloudflare and managed by ${brandName}.`;
});

const emit = defineEmits(['update:filteredItems']);
const {cloudProviderIcon} = useCloudProviderIcon(props.server.provider_name);

const stagingDomain = ref(Inertia.page.props.staging_domain ?? 'wp1.site')

let productionSite = ref({});
let stagingSites = ref([]);

const dropdownRef = ref(null);
const isDropdownOpen = ref(false);
const selected_staging_domain = ref(stagingDomain.value);
const availableDomains = ref(Inertia.page?.props?.available_staging_domain ?? []);

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};

const selectDomain = (domain) => {
  isDropdownOpen.value = false;
  selected_staging_domain.value = domain;
  checkStagingDomain();
};

userClickOutside(dropdownRef, () => {
  isDropdownOpen.value = false;
});

const toggleOwnDomain = () => {
  deploymentSiteForm.name = "";
  deploymentSiteForm.staging_env_type = "own_domain";
};

function toggleTemporaryDomain() {
  deploymentSiteForm.staging_env_type = "temporary_domain";
  // generate staging site title based on production site domain
  const generated_name = useNameGenerator(props.site.title);
  let form_name = generated_name.replace(/[^a-zA-Z0-9-]/g, "");
  deploymentSiteForm.name = form_name + '-' + generateRandomString(4).toLocaleLowerCase();
}

onMounted(() => {
  axios.get(route('api.site.fetch-staging-and-production-sites', [props.server.id, props.site.id]))
      .then(response => {
        productionSite.value = response.data.production_site;
        stagingSites.value = response.data.staging_sites;
      })
      .catch(error => {
        console.log(error);
      });
});

const BASE_URL = "/server/" + props.server.id + "/site/" + props.site.id;
let domainMenu;
if (props.site.environment === 'staging' || props.site.environment === 'demo') {
    domainMenu = {
        title: t("Go Live"),
        name: "Go Live",
        isShow: true,
        icon: "xc-domains",
        url: BASE_URL + "/staging_environment",
        keyword: ["Staging", "Primary Domain", "Additional Domains"]
    }
} else if(props.site.environment === 'production') {
    domainMenu = {
        title: t("Domain"),
        name: "Domain",
        isShow: true,
        icon: "xc-domains",
        url: BASE_URL + "/domain",
        keyword: ["Domain", "Primary Domain", "Additional Domains"]
    }
}

const sidebarItems = reactive({
    activeItem: props.active,
    data: [
        {
            title: t("Site Overview"),
            name: "Site Overview",
            icon: "xc-browser",
            url: BASE_URL + "/site-overview",
            isShow: true,
            keyword: ["Site Overview"]
        },
        {
            title: t("Manage Staging"),
            name: "Manage Staging",
            isShow: props.site?.permissions?.includes("site:manage-staging") && props.site?.parent_site_id && (props.site?.environment === 'staging' || props.site?.environment === 'staging_with_own_domain') && Inertia.page.props.current_team?.active_plan?.name !== 'free',
            icon: "xc-domains",
            url: BASE_URL + "/staging-management",
        },
        {
          title: t("Domain"),
          name: "Domain",
          icon: "xc-https",
          isShow: props.site?.permissions?.includes("site:manage-ssl") || props.site?.permissions?.includes("site:manage-redirects"),
          subItems: [
            ...(domainMenu ? [domainMenu] : []),
            {
              title: t("SSL/HTTPS"),
              name: "SSL/HTTPS",
              icon: "xc-https",
              url: BASE_URL + "/ssl",
              isShow: props.site?.permissions?.includes("site:manage-ssl"),
              keyword: ["SSL/HTTPS", "Enable HTTPS"]
            },
            {
              title: t("Redirection"),
              name: "Redirection",
              icon: "xc-maximize",
              url: BASE_URL + "/redirection",
              isShow: props.site?.permissions?.includes("site:manage-redirects"),
              keyword: ["Redirection", "Add Redirection"]
            },
          ]
        },
        {
            title: props.site?.type === 'laravel' ? t("Laravel") : t("WordPress"),
            name: props.site?.type === 'laravel' ? "Laravel" : "WordPress",
            icon: "xc-web-settings",
            isShow: (props.site?.is_wordpress || props.site?.type === 'laravel') && (props.site?.permissions?.includes("site:manage-wordpress") || props.site?.permissions?.includes("site:manage-update") || props.site?.permissions?.includes("site:manage-caching") || props.site?.permissions?.includes("site:vulnerability-scan") || props.site?.permissions?.includes("site:manage-wpconfig")),
            subItems: [
                {
                    title: t("Application"),
                    name: "Application",
                    icon: "xc-web-settings",
                    url: BASE_URL + "/application",
                    isShow: props.site?.permissions?.includes("site:settings") && props.site?.type === 'laravel',
                    keyword: ["Application", "Laravel", "Maintenance Mode", "Horizon", "Scheduler"]
                },
                {
                    title: props.site?.type === 'laravel' ? t("Environment") : t("WP Config"),
                    name: props.site?.type === 'laravel' ? "Environment" : "WP Config",
                    icon: "xc-web-settings",
                    url: BASE_URL + (props.site?.is_wordpress ? "/wp-config" : "/environment"),
                    isShow: props.site?.permissions?.includes("site:manage-wpconfig") && (props.site?.is_wordpress || props.site?.type === 'laravel'),
                    keyword: props.site?.type === 'laravel' ? ["Environment", "Update Environment", "Config"] : ["WP Config", "Update Config"]
                },
                {
                    title: t("Updates"),
                    name: "Updates",
                    icon: "xc-update",
                    url: BASE_URL + "/updates",
                    isShow: props.site?.permissions?.includes("site:manage-update") && props.site?.is_wordpress,
                    badge: props.site?.theme_updates.length + props.site?.plugin_updates.length,
                    keyword: ["Updates", "WordPress Core", "Plugins", "Themes"]
                },
                {
                    title: t("Caching"),
                    name: "Caching",
                    icon: "xc-data-cleaning",
                    url: BASE_URL + "/caching",
                    isShow: props.site?.permissions?.includes("site:manage-caching") && props.site?.is_wordpress,
                    keyword: ["Page Caching", "Object Cache"]
                },
                {
                    title: t("Vulnerability Scan"),
                    name: "Vulnerability Scan",
                    url: BASE_URL + "/vulnerability-scan",
                    isShow: props.site?.permissions?.includes("site:vulnerability-scan") && props.site?.is_wordpress,
                    keyword: ["Vulnerability Scan", "Scan WordPress", "Scan Plugins", "Scan Themes", "Scan Core"]
                },
                {
                    title: t("Integrity Monitor"),
                    name: "Integrity Monitor",
                    url: BASE_URL + "/integrity-monitor",
                    isShow:  props.site?.is_wordpress && props.site?.permissions?.includes("site:manage-monitoring"),
                    keyword: [
                        "Integrity Monitor",
                        "Monitor WordPress",
                        "Monitor Plugins",
                        "Monitor Themes",
                        "Monitor Core"
                    ]
                },
            ]
        },
        {
            title: t("Email Configuration"),
            name: "Email Configuration",
            icon: "xc-email",
            url: BASE_URL + "/email-configuration",
            isShow: props.site?.permissions?.includes("site:manage-email-providers") && props.site?.is_wordpress,
            keyword: ["Email Provider", "Send Test Email", "Connect New Provider"]
        },
        {
            title: props.site?.is_wordpress ? t("Site Backup") : t("Backup"),
            name: "Site Backup",
            icon: "xc-data-recovery",
            isShow: props.site?.permissions?.includes("site:settings"),
            subItems: [
                {
                    title: t("Previous Backups"),
                    name: "Previous Backups",
                    icon: "xc-settings",
                    url: BASE_URL + "/backups",
                    isShow: props.site?.permissions?.includes("site:settings"),
                    keyword: ["Previous Remote Backups", "Previous Local Backups"]
                },
                {
                    title: t("Backup Settings"),
                    name: "Backup Settings",
                    icon: "xc-settings",
                    url: BASE_URL + "/backup",
                    isShow: props.site?.permissions?.includes("site:settings"),
                    keyword: ["Remote Backup Settings", "Remote Backup Settings"]
                }
            ]
        },
        {
            title: props.site?.is_wordpress ? t("Site Monitoring") : t("Monitoring"),
            name: "Site Monitoring",
            icon: "xc-analytics-1",
            isShow: props.site?.permissions?.includes("site:manage-monitoring") || props.site?.permissions?.includes("site:manage-logs")  || props.site?.permissions?.includes("site:manage-events"),
            subItems: [
                {
                    title: t("Monitoring"),
                    name: "Monitoring",
                    icon: "xc-analytics-1",
                    url: BASE_URL + "/monitoring",
                    isShow: props.site?.permissions?.includes("site:manage-monitoring"),
                    keyword: ["Monitoring","Site Monitoring", "Monitoring Stats", "SSL Overview", "WordPress Logs"]
                },
                {
                    title: t("Logs"),
                    name: "Logs",
                    icon: "xc-data-transfer",
                    url: BASE_URL + "/logs",
                    isShow: props.site?.permissions?.includes("site:manage-logs"),
                    keyword: props.server?.stack === 'nginx' ? ["WordPress Debug Log", "Nginx Log", "Nginx Access Log", "Nginx Error Log"] : ["WordPress Debug Log", "OpenLiteSpeed Log", "OpenLiteSpeed Access Log", "OpenLiteSpeed Error Log"]
                },
                {
                    title: t("Events"),
                    name: "Events",
                    icon: "xc-analytics",
                    url: BASE_URL + "/events",
                    isShow: props.site?.permissions?.includes("site:manage-events"),
                    keyword: ["Events"]
                },
            ]
        },
        {
            title: t("Access Data"),
            name: "Access Data",
            icon: "xc-keys",
            isShow: props.site?.permissions?.includes("site:manage-database") || props.site?.permissions?.includes("site:manage-ssh-sftp"),
            subItems: [
                {
                    title: t("SSH/sFTP"),
                    name: "SSH/sFTP",
                    icon: "xc-keys",
                    url: BASE_URL + "/ssh",
                    isShow: props.site?.permissions?.includes("site:manage-ssh-sftp"),
                    keyword: ["Site SSH/sFTP Access", "SSH Authentication", "Database URL Connection"]
                },
                {
                    title: t("Database"),
                    name: "Database",
                    icon: "xc-database",
                    url: BASE_URL + "/database",
                    isShow: props.site?.permissions?.includes("site:manage-database") && props.site?.has_database,
                    keyword: props.site?.is_node_app ? ["Database", "PHPMyAdmin", "Enable PHPMyAdmin"] : ["Database", "Enable Adminer", "Launch Adminer", "PHPMyAdmin", "Enable PHPMyAdmin"]
                },
                {
                    title: t("File Manager"),
                    name: "File Manager",
                    icon: "xc-file",
                    url: BASE_URL + "/file-manager",
                    isShow: props.site?.permissions?.includes("site:manage-file-manager") && !props.site?.is_node_app,
                    keyword: ["File Manager", "Enable File Manager", "Tiny File Manager", "Launch File Manager"]
                }
            ]
        },
        {
            title: t("Tools"),
            name: "Tools",
            icon: "xc-icon-tools-black",
            isShow: props.site?.permissions?.includes("site:manage-wpconfig") && props.site?.is_wordpress && props.server?.stack === 'nginx' || props.site?.permissions?.includes("site:settings") || props.site?.permissions?.includes("site:delete"),
            subItems: [
                {
                    title: props.server?.stack === 'nginx' ? t("Nginx and Security") : t("Security"),
                    name: props.server?.stack === 'nginx' ? "Nginx and Security" : "Security",
                    icon: "xc-web-settings",
                    url: BASE_URL + "/web-server-security",
                    isShow: props.site?.permissions?.includes("site:settings") || props.site?.permissions?.includes("site:delete"),
                    keyword: [props.server?.stack === 'nginx' ? "Nginx and Security" : "Security", "Preview", "7g Firewall", props.server?.stack === 'nginx' ? "Disable Nginx File Regeneration" : "8g Firewall", "PHP Execution on Upload Directory", "Enable XML-RPC", "Edit X-Frame-Options"]
                },
                {
                    title: t("Nginx Customization"),
                    name: "Nginx Customization",
                    icon: "xc-web-settings",
                    url: BASE_URL + "/nginx-customization",
                    isShow: props.site?.permissions?.includes("site:settings") && props.server?.stack === 'nginx',
                    keyword: props.server?.stack === 'nginx' ? ["Nginx Customization", "Custom Nginx Config", "Nginx Preview", "Add a New Config"] : []
                },
                {
                    title: t("Basic Authentication"),
                    name: "Basic Authentication",
                    icon: "xc-web-settings",
                    url: BASE_URL + "/basic-authentication",
                    isShow: props.site?.permissions?.includes("site:settings") || props.site?.permissions?.includes("site:delete"),
                    keyword: ["Basic Authentication"]
                },
                {
                    title: t("Commands"),
                    name: "Commands",
                    icon: "xc-analytics",
                    url: BASE_URL + "/command-runner",
                    isShow: props.site?.permissions?.includes("site:custom-command-runner"),
                    keyword: ["Commands", "Command Runner", "Custom Command"]
                },
                {
                    title: t("IP Management"),
                    name: "IP Management",
                    url: BASE_URL + "/ip-management",
                    isShow: props.site?.permissions?.includes("site:settings") || props.site?.permissions?.includes("site:delete"),
                    keyword: ["IP Management", "Add IP", "Remove IP","Whitelist IP", "Blacklist IP"]
                },
            ]
        },
        {
            title: t("Git"),
            name: "Git",
            icon: "xc-git",
            url: BASE_URL + "/git",
            isShow: props.site?.permissions?.includes("site:manage-update") && props.site?.is_git,
            keyword: ["Git"]
        },
        {
            title: props.site?.is_wordpress ? t("Site Settings") : t("Settings"),
            name: "Site Settings",
            icon: "xc-settings",
            url: BASE_URL + "/settings",
            isShow: props.site?.permissions?.includes("site:settings") || props.site?.permissions?.includes("site:delete"),
            keyword: ["Update PHP Settings", "WP-Cron and xCloud-Cron", "Edit Site Tags", "WP Debug", "Disable Site", "Delete Site Confirmation", "Site Settings", "Update Web Root", "Rescue Site"]
        },
    ],
});

const siteFavicon = ref(`https://icons.duckduckgo.com/ip3/${props.site.name}.ico`);

const deployStagingModal = ref(false);

function handleImageLoad(e) {
  let imgElement = e.target;
  if(imgElement){
      if (imgElement.src.includes('wordpress-blue.svg') || imgElement.src.includes('php_version.svg')) {
          return;
      }
      if(imgElement.naturalWidth <= 48 || imgElement.naturalHeight <= 48){
        siteFavicon.value = useSiteTypeIcon(props.site.type).siteTypeIcon.value;
    }
  }
}

function handleImageError() {
  console.log('Favicon not found, using custom icon.');
    if (siteFavicon.value.includes('wordpress-blue.svg') || siteFavicon.value.includes('php_version.svg')) {
        return;
    }
    siteFavicon.value = props.site?.is_wordpress ?  asset('img/wordpress-blue.svg') : asset('img/php_version.svg');
}

watch(
    () => deploymentSiteForm.name,
    () => {
      deploymentSiteForm.errors.name = '';

      if(deploymentSiteForm.staging_env_type ==='temporary_domain' && deploymentSiteForm.name.includes('.')){
        deploymentSiteForm.errors.name = "Staging/demo domains can not contain multi level domain.";
        return;
      }

      showCloudflareDnsOption.value =  !isIpSite.value;

      if (deploymentSiteForm.staging_env_type === 'temporary_domain') {
          checkStagingDomain();
      }

      if (deploymentSiteForm.staging_env_type === 'own_domain') {
          deploymentSiteForm.name = deploymentSiteForm.custom_domain;
      }
    }
);

watch(
    () => deploymentSiteForm.custom_domain,
    () => {
      if (deploymentSiteForm.staging_env_type === 'own_domain') {
          deploymentSiteForm.name = deploymentSiteForm.custom_domain;
      }
    }
);

watch(
    () => useCloudflareDns.value,
    (newVal, oldVal) => {
        if (useCloudflareDns.value) {
            if (deploymentSiteForm.name === "") {
                useCloudflareDns.value = false;
                useFlash().error("Please add a domain name first");
                /*useFlash().swal().fire({
                    icon: "error",
                    title: "Please add a domain name first",
                });*/
            } else {
                checkDomain();
            }
        } else {
            showDomainSetup.value = true;
            resetCloudflareIntegration();
        }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);

const resetDeploymentForm = function (){
  deploymentSiteForm.name = '';
  deploymentSiteForm.custom_domain = '';
  deploymentSiteForm.errors = {};

}

const resetCloudflareIntegration = function () {
    deploymentSiteForm.domain_active_on_cloudflare = false;
    deploymentSiteForm.cloudflare_account_id = "";
    deploymentSiteForm.cloudflare_zone_id = "";
    deploymentSiteForm.subdomain = "";
    deploymentSiteForm.site_name = "";
    useCloudflareDns.value = false;
};

let checkDomain = () => {
    loader.value = true;
    deploymentSiteForm.processing = true;

    axios
        .get(
            route(
                "api.user.integration.cloudflare.check-domain-exists",
                deploymentSiteForm.name
            )
        )
        .then((res) => {
            // console.log(res.data.response.domain_active_on_cloudflare)
            showDomainSetup.value =
                !res.data.response.domain_active_on_cloudflare;


            deploymentSiteForm.domain_active_on_cloudflare =
                res.data.response.domain_active_on_cloudflare;
            deploymentSiteForm.cloudflare_account_id = res.data.response.account_id;
            deploymentSiteForm.cloudflare_zone_id = res.data.response.zone_id;
            deploymentSiteForm.subdomain = res.data.response.subdomain;
            deploymentSiteForm.site_name = res.data.response.site_name;
            deploymentSiteForm.ssl_provider = "cloudflare";

            loader.value = false;
            deploymentSiteForm.processing = false;

            if (!res.data.response.domain_active_on_cloudflare) {
                useFlash().error(
                    "No cloudflare account found for this domain. Please add your domain to cloudflare first."
                );
            }
        })
        .catch((err) => {
            showDomainSetup.value = true;
            loader.value = false;
            deploymentSiteForm.processing = false;

            if (
                err.response &&
                err.response.data &&
                err.response.data.message
            ) {
                useFlash().error(err.response.data.message);
            } else {
                useFlash().error(
                    "An error occurred while processing your request."
                );
            }
        });
};

const prepareStagingDeployment = () => {
  // generate staging site title based on production site domain
  const generated_name = useNameGenerator(props.site.title);
  let form_name = generated_name.replace(/[^a-zA-Z0-9-]/g, "");
  deploymentSiteForm.name = form_name + '-' + generateRandomString(4).toLocaleLowerCase();

  // open modal
  deployStagingModal.value = true;
};

const deployStaging = () => {
  deployingStaging.value = true;

  deploymentSiteForm.transform((data) => {
    //data.name = deploymentSiteForm.name + '.' + stagingDomain.value
    data.name = deploymentSiteForm.staging_env_type === 'temporary_domain' ? ( deploymentSiteForm.name + '.' + selected_staging_domain.value ) : deploymentSiteForm.name;
    return data;
  }).post(route('api.site.staging.deploy', [
    props.server.id,
    props.site.id,

  ]), {
    preserveScroll: true,
    onSuccess: () => {
      deployingStaging.value = false;
      useFlash().success('Your staging site will be deployed soon..')
    },
    onError: (errors) => {
      deployingStaging.value = false;
      useFlash().error('Failed to deploy staging site. Please try again.')
    }
  });
};

let checkStagingDomain = () => {
  axios
      //.get(route("api.site.check-domain-exists") + "?domain=" + deploymentSiteForm.name + "." + stagingDomain.value)
      .get(route("api.site.check-domain-exists") + "?domain=" + deploymentSiteForm.name + "." + selected_staging_domain.value)
      .then((res) => {
        if (res.data.exists) {
          deploymentSiteForm.errors.name =
              "Looks like the domain is already in use. Please try changing the site title again.";
        } else {
          deploymentSiteForm.errors.name = "";
        }
      })
      .catch((err) => {
        useFlash().error(err.errors.domain[0]);
      });
};

const deployLaravel = () => {
  deployingLaravel.value = true;

  axios.post(route('api.site.git.pull-and-deploy', [
    props.server.id,
    props.site.id
  ]))
  .then(response => {
    deployingLaravel.value = false;
    useFlash().success(t('Deployment has been initiated.'));
  })
  .catch(error => {
    deployingLaravel.value = false;
    useFlash().error(error.response?.data?.message || 'Failed to deploy Laravel site. Please try again.');
  });
};

function generateRandomString(length = 5) {
  const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(
        Math.floor(Math.random() * characters.length)
    );
  }
  return result;
}

const handleSearch = (newSearch) => {
    searchKeyword.value = newSearch;
};

const itemsToSearch = sidebarItems.data.flatMap(item => {
    const combinedItem = {
        ...item,
        subItems: item.subItems || [],
    };

    const allItems = [combinedItem, ...combinedItem.subItems];

    return allItems.flatMap(currentItem => {
        const keywords = currentItem.keyword || [];
        return keywords.map(keyword => ({
            icon: currentItem.icon,
            isShow: currentItem.isShow,
            keyword: keyword,
            name: currentItem.title,
            title: keyword,
            url: `${currentItem.url}#:~:text=${encodeURIComponent(keyword)}`,
        }));
    });
});

const fuse = new Fuse(itemsToSearch, {
    keys: ["keyword"],
    includeScore: true,
    minMatchCharLength: 3,
});

const filteredMenuItems = computed(() => {
    if (!searchKeyword.value) return [];

    const results = fuse.search(searchKeyword.value);
    const items = results.map(result => result.item).filter(item => item.isShow);

    // Reconstruct the hierarchical structure
    const filteredMenu = [];
    items.forEach(matchedItem => {
        if (matchedItem?.subItems && matchedItem?.subItems?.length > 0) {
            matchedItem.subItems.forEach(subItem => {
                if (!filteredMenu.some(item => item.name === subItem.name)) {
                    filteredMenu.push({ ...subItem });
                }
            });
        } else {
            if (!filteredMenu.some(item => item.name === matchedItem.name)) {
                filteredMenu.push({ ...matchedItem });
            }
        }
    });

    return filteredMenu;
});

watch(filteredMenuItems, (newFilteredItems) => {
    emit('update:filteredItems', newFilteredItems);
});

const onChangeDropDown = (value) => {
  emit('onChangeDropDown', value);
}

</script>
