<template>
    <single-report active="Admin Action">
        <Head title="Admin Action" />
        <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex justify-between items-center gap-10px p-30px w-full">
                <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">Admin Action</h2>
            </div>
            <div class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="flex justify-between items-center gap-15px w-full wide-mobile:flex-wrap">
                    <div class="flex flex-col gap-10px">
                        <h3 class="text-lg text-dark dark:text-white leading-none">List of Opt-in</h3>
                    </div>
                    <button
                            @click.prevent="addOptinModal = true"
                            class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                            text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent hover:bg-primary-light
                            hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition ease-in-out duration-300 focus:outline-none
                            leading-tight flex-nowrap whitespace-nowrap">
                        <span>Add New Opt-in</span>
                    </button>
                </div>
                <div class="flex flex-col gap-30px">
                    <div class="rounded-md flex flex-col">
                        <div class="overflow-x-auto w-full overflow-y-hidden">
                            <table class="w-full">
                                <thead class="bg-primary-light dark:bg-dark">
                                <tr class="divide-x divide-light dark:divide-dark">
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        Key
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        Value
                                    </th>
                                    <th
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        Actions
                                    </th>
                                </tr>
                                </thead>

                                <tbody
                                    class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                                <tr
                                    v-for="(optin, index) in optins.data" :key="optin.key"
                                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                >
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        {{ index }}
                                    </td>
                                    <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        <span v-html="optinText(optin)"></span>
                                    </td>
                                    <td
                                        class="relative w-14 px-30px py-20px sm:w-16 sm:px-8">
                                          <span class="flex gap-30px">
                                            <button
                                                    @click.prevent="deleteOptin(index)"
                                                    class="relative group cursor-pointer mt-1.5"
                                            >
                                              <span class="text-sm flex dark:text-secondary-light group-hover:text-white">
                                                <i class="xcloud xc-delete mr-2"></i> Delete
                                              </span>
                                            </button>
                                          </span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- pagination -->
                    <pagination :links="optins?.links"/>
                </div>
            </div>
        </div>
    </single-report>
    <Modal
        :loading="form.processing"
        @footerClick="submitOptin"
        footer-button-title="Save Opt-in"
        :footer-button="true"
        :closeable="true"
        @close="addOptinModal = false;form.reset();"
        :show="addOptinModal"
        :widthClass="'max-w-850px'"
        title="Add Opt-in">

        <div class="flex flex-col gap-30px">
            <div class="grid grid-cols-1 gap-10">
                <text-input
                    type="text"
                    label="Opt-in Key"
                    placeholder="lifetime-deal"
                    :error="form.errors.optin_key"
                    v-model="form.optin_key">
                </text-input>
            </div>

            <div class="grid grid-cols-1 gap-5">
                <label for="button_type" class="block text-base font-medium text-secondary-full dark:text-light leading-none">Opt-in Type</label>
                <div class="flex gap-2 w-[30%]">
                    <radio-input v-model="form.optin_type"
                                 :error="form.errors.optin_type"
                                 value="default">
                        Default
                    </radio-input>

                    <radio-input v-model="form.optin_type"
                                 :error="form.errors.optin_type"
                                 value="custom">
                        Custom
                    </radio-input>
                </div>
            </div>

            <div class="grid grid-cols-1 gap-10">
                <div>
                    <label class="text-dark dark:text-white text-base font-normal mb-5">Opt-in Text</label>
                    <v-ace-editor
                        :options="{
                  fontSize: '10px',
                  showLineNumbers: true,
                  wrap: true,
              }"
                        @init="editorInit"
                        v-model:value="form.optin_text"
                        lang="php"
                        :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
                        style="min-height: 100px"
                    />
                    <Error class="mt-4" v-if="form.errors.optin_text" :error="form.errors.optin_text" />
                </div>
            </div>

            <div v-if="form.optin_type === 'default'" class="grid grid-cols-1 gap-5">
                <label for="button_type" class="block text-base font-medium text-secondary-full dark:text-light leading-none">Button Type</label>
                <div class="flex gap-2 w-[30%]">
                    <radio-input v-model="form.button_type"
                         :error="form.errors.button_type"
                         value="optin_btn_1">
                        <img :src="asset('img/optin/optin-btn-1.png')" alt="optin-btn-1"/>
                    </radio-input>

                    <radio-input v-model="form.button_type"
                         :error="form.errors.button_type"
                         value="optin_btn_2">
                        <img :src="asset('img/optin/optin-btn-2.png')" alt="optin-btn-2"/>
                    </radio-input>

                    <radio-input v-model="form.button_type"
                         :error="form.errors.button_type"
                         value="optin_btn_3">
                        <img :src="asset('img/optin/optin-btn-3.png')" alt="optin-btn-3"/>
                    </radio-input>
                </div>
            </div>

            <div v-if="form.optin_type === 'default'" class="grid grid-cols-1 gap-10">
                <text-input
                    type="text"
                    label="Opt-in Button Text"
                    placeholder="Grab LTD Now"
                    :error="form.errors.button_text"
                    v-model="form.button_text">
                </text-input>
            </div>

            <div v-if="form.optin_type === 'default'" class="grid grid-cols-1 gap-10">
                <text-input
                    type="text"
                    label="Opt-in Button URL"
                    placeholder="https://xcloud.host/deal"
                    :error="form.errors.button_url"
                    v-model="form.button_url">
                </text-input>
            </div>

            <div class="grid grid-cols-1 gap-10">
                <text-input
                    type="number"
                    label="Opt-in Transition"
                    placeholder="5"
                    :error="form.errors.transition"
                    v-model="form.transition">
                </text-input>
            </div>

            <div v-if="form.optin_type === 'default'" class="grid grid-cols-1 gap-10">
                <label>
                    <input type="checkbox" class="hidden peer" v-model="form.open_new_page" v-bind:checked="form.open_new_page"/>
                    <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent
                                 before:border-1 before:border-secondary-light before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                                 before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                 before:transition before:duration-200 peer-checked:before:border-success-full
                                 peer-checked:before:bg-success-full peer-checked:before:text-white">
                        <span class="text-base font-normal text-secondary-full dark:text-mode-secondary-light">
                            Open in New Page
                        </span>
                    </span>
                </label>
                <div class="mt-2 text-sm dark:text-light text-red-600 flex gap-2 items-center" v-if="form.errors.open_new_page">
                    <img :src="asset('img/eeror.svg')" alt="warning_img" class="h-4"/><span class="mt-1">{{ form.errors.open_new_page }}</span>
                </div>
            </div>
        </div>
    </Modal>
</template>
<script setup>
import {ref} from "vue";
import Modal from "@/Shared/Modal.vue"
import {useForm} from '@inertiajs/inertia-vue3';
import Button from "@/Jetstream/Button.vue";
import SingleReport from "@/Pages/Reports/SingleReport.vue";
import TextInput from "@/Shared/TextInput.vue";
import RadioInput from "@/Shared/RadioInput.vue";
import {asset} from "laravel-vapor";
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import Error from "@/Shared/Error.vue";
import {VAceEditor} from "vue3-ace-editor";
import {useNavigationStore} from "@/stores/NavigationStore";
import ace from 'ace-builds';
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/theme-tomorrow_night_blue';

const props = defineProps({
    optins: {
       type: Object,
       required: true
    },
    access_permissions: {
        type: Object,
        required: true
    },
});

const permissions = Object.values(props?.access_permissions ?? {});
let addOptinModal = ref(false)
let navigation = useNavigationStore();

const closeModal = () => {
    addOptinModal.value = false;
    form.reset();
}

const form = useForm({
    optin_key: '',
    optin_type: 'default',
    optin_text: '',
    button_type: 'optin_btn_1',
    button_text: '',
    button_url: '',
    transition: 5,
    open_new_page: false
});

const submitOptin = () => {
    form.post(route('admin.action.optin.store'), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset();
            addOptinModal.value = false;
        }
    });
}

const deleteOptin = (key) => {
    axios.post(route('admin.action.optin.destroy'), {optin_key: key})
        .then(response => {
            useFlash().success(response.data.message);
            Inertia.reload();
        })
        .catch((error) => {
            useFlash().error(error?.response?.data?.message ? error.response.data.message : error)
        });
}

const editorInit = (editor) => {
    const mode = 'html';
    editor.getSession().setMode(`ace/mode/${mode}`);
}

const optinText = (optin) => {
    const parts = [];
    if (optin.optin_type) {
        parts.push(`Opt-in Type: ${optin.optin_type}`);
    }
    if (optin.optin_text) {
        parts.push(`Opt-in Text: ${optin.optin_text}`);
    }
    if (optin.button_type) {
        parts.push(`Button Type: ${optin.button_type}`);
    }
    if (optin.button_text) {
        parts.push(`Button Text: ${optin.button_text}`);
    }
    if (optin.button_url) {
        parts.push(`Button URL: ${optin.button_url}`);
    }
    if (optin.transition) {
        parts.push(`Transition: ${optin.transition}`);
    }
    if (optin.open_new_page !== undefined) {
        parts.push(`Open New Page: ${optin.open_new_page}`);
    }
    return parts.join(', ');
}
</script>
