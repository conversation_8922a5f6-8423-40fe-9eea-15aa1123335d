<template>
    <single-report active="Refunded Invoices">
        <Head title="Refunded Invoices" />
        <div class="xc-container-2">
            <div class="flex flex-col gap-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="rounded-md flex flex-col">
                    <div class="flex flex-wrap mb-5 items-center justify-between gap-x-2 gap-y-2">
                        <h4 class="text-dark dark:text-white text-xl md:text-xl leading-none">
                            Refunded Invoices ({{count}}) <br>
                            <span class="text-base text-secondary-full dark:text-secondary-full">
                                Refunded Amount: <span class="text-danger"> ${{receivable_amount}}</span>
                            </span> <br>
                            <span class="text-base text-secondary-full dark:text-secondary-full">
                                Total Amount: <span class="text-danger"> ${{total_receivable_amount}}</span>
                            </span>
                        </h4>
                        <div class="flex items-center gap-3">
                            <div class="relative inline-block text-left wide-mobile:ml-2 small-laptop:hidden w-72">
                                <form class="relative flex items-center group">
                                    <input
                                        v-model="search"
                                        @mouseleave="searchFilter"
                                        type="text"
                                        class="flex-auto w-full appearance-none bg-white dark:bg-mode-light pl-4 pr-12 h-12 min-h-12 rounded-10px text-dark dark:text-white text-base placeholder-secondary-full dark:placeholder-secondary-full focus:outline-none font-normal border-none focus:ring-success-light"
                                        placeholder="Find refunded invoices"
                                    />
                                    <div
                                        class="icon flex absolute top-1/2 right-4 -translate-y-1/2 pointer-events-none text-lg"
                                    >
                                        <i
                                            class="xcloud xc-search text-secondary-light group-focus-within:text-success-light"
                                        ></i>
                                    </div>
                                </form>
                            </div>
                            <div class="flex items-center justify-end">
                                <button
                                    @click.prevent="resetFilter"
                                    type="button"
                                    class="inline-flex items-center gap-2 justify-center text-lg mobile:text-base font-medium shadow-none text-white transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none"
                                >
                                    <i class="xcloud xc-verify_dns"></i>
                                    Reset Search
                                </button>
                            </div>
                            <!--<button
                                @click.prevent="openConfirmModal = true"
                                type="button"
                                class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-success-light text-lg mobile:text-base px-25px mobile:px-15px font-medium rounded-10px shadow-none text-white bg-success-light hover:bg-success-full hover:shadow-lg hover:shadow-success-full/30 transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none"
                            >
                                Export to CSV
                            </button>-->
                        </div>
                    </div>

                    <div class="overflow-x-auto w-full">
                        <table class="w-full">
                            <thead class="bg-primary-light dark:bg-dark">
                            <tr class="divide-x divide-light dark:divide-dark">
                                <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap">
                                    Invoice No
                                </th>
                                <th class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap">
                                    Date
                                </th>
                                <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap">
                                    Team
                                </th>
                                <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap">
                                    Email
                                </th>
                                <th class="px-30px py-20px text-lg font-normal text-white dark:text-white text-right whitespace-nowrap">
                                    Amount
                                </th>
                                <th class="px-30px py-20px text-lg font-normal text-white dark:text-white text-center whitespace-nowrap">
                                    Status
                                </th>
                                <th class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap">
                                    Due Date
                                </th>
                                <th class="px-30px py-20px text-right text-lg font-normal text-white dark:text-white whitespace-nowrap">
                                    Download
                                </th>
                            </tr>
                            </thead>
                            <tbody
                                class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark"
                            >
                            <tr
                                v-for="invoice in invoices?.data"
                                :key="invoice.id"
                                class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                            >
                                <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white whitespace-nowrap">
                                    <a :href="invoice?.invoice_url"
                                       target="_blank"
                                       class="text-success-full group-hover:text-white mr-2">
                                        {{ invoice?.invoice_number ?? invoice?.reference_no }}
                                    </a>
                                    <CopyButton
                                        position-class=""
                                        :content="invoice?.invoice_number ?? invoice?.reference_number"
                                        align="top"
                                        color="primary"
                                        :hideCopyText="true"
                                    />
                                </td>

                                <td class="px-30px py-20px text-center text-base font-normal text-dark dark:text-white group-hover:text-white whitespace-nowrap">
                                    {{ invoice?.date }}
                                </td>

                                <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white whitespace-nowrap">
                                    <span class="whitespace-nowrap">
                                        <a
                                            :href="invoice?.team_url"
                                            target="_blank"
                                            class="text-success-full group-hover:text-white"
                                        >{{ invoice?.team?.name }}</a
                                        >
                                    </span>
                                </td>

                                <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white whitespace-nowrap">
                                    {{ invoice?.customer_email }}
                                </td>

                                <td class="px-30px py-20px text-right text-base font-normal text-dark dark:text-white group-hover:text-white">
                                    ${{ invoice?.amount }}
                                </td>

                                <td class="px-30px py-20px text-right text-base font-normal text-dark dark:text-white group-hover:text-white whitespace-nowrap">
                                    <span v-if="invoice?.status === 'refunded'"
                                          class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full">
                                          {{ invoice?.status_readable }}
                                        </span>
                                    <span
                                        v-else-if="invoice?.status === 'failed' || invoice?.status === 'payment_failed'"
                                        class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10 group-hover:bg-danger">
                                          {{ invoice?.status_readable }}
                                        </span>
                                    <span v-else
                                          class="text-warning group-hover:text-white px-6 py-1.5 rounded-3xl bg-warning/10 group-hover:bg-warning">
                                          {{ invoice?.status_readable }}
                                        </span>
                                </td>

                                <td class="px-30px py-20px text-base font-normal text-dark dark:text-white group-hover:text-white text-center whitespace-nowrap">
                                    {{ invoice?.format_due_date ?? 'No Due Date' }}
                                </td>

                                <td class="px-30px py-20px text-right text-base font-normal text-dark dark:text-white group-hover:text-white whitespace-nowrap">
                                    <div class="grid grid-cols-2 gap-x-5">
                                        <a v-if="invoice?.type === 'manual'" :href="route('user.invoice.manual.download',[invoice.invoice_number])"
                                           class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white">
                                            Download
                                        </a>
                                        <a v-else :href="route('user.invoice.download',[invoice.invoice_number])"
                                           class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white">
                                            Download
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <InvoiceExportConfirmation
                    :openConfirmModal="openConfirmModal"
                    @closeModal="closeConfirmationModal"
                    @confirmExport="confirmExport"
                    :is-initiate-export="isInitiateExport"
                    :export-url="exportUrl"
                />

                <!-- pagination -->
                <Pagination
                    v-if="hasPagination"
                    :links="invoices.links"
                    :query-string="generateFilterQueryString()"
                />
            </div>
        </div>
    </single-report>
</template>

<script setup>
import Pagination from "@/Shared/Pagination.vue";
import SingleReport from "@/Pages/Reports/SingleReport.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import Button from "@/Jetstream/Button.vue";
import {onMounted, ref} from "vue";
import {Inertia} from "@inertiajs/inertia";
import InvoiceExportConfirmation from "@/Pages/Reports/InvoiceExportConfirmation.vue";

const props = defineProps({
    invoices: Object,
    hasPagination: {
        type: Boolean,
        default: true
    },
    search_filter: String,
    count: Number,
    receivable_amount: Number,
    total_receivable_amount: Number,
});

const search = ref(props?.search_filter);
const openConfirmModal = ref(false);
const isInitiateExport = ref(false);
const exportUrl = ref("");

const searchFilter = () => {
    if (search.value && search.value.length > 0) {
        Inertia.get(route("reports.refunded-invoice"), {
            search: search.value,
        });
    } else {
        Inertia.get(route("reports.refunded-invoice"), {
            search: '',
        });
    }
};

const closeConfirmationModal = () => {
    openConfirmModal.value = false;
    isInitiateExport.value = false;
    exportUrl.value = "";
};

const confirmExport = (exportType) => {
    axios.post(route("reports.unpaid-invoice.export"), {
            export_type: exportType,
            search: search.value,
        })
        .then((response) => {
            if (response.data === "initiate") {
                isInitiateExport.value = true;
            }
        })
        .catch((error) => {});
};

const resetFilter = () => {
    Inertia.get(route("reports.refunded-invoice"));
};

const generateFilterQueryString = () => {
    let queryString = "";

    if (search.value) {
        queryString += `&search=${search.value}`;
    }

    return queryString;
};

onMounted(() => {
    if (window.Echo) {
        console.log("joining invoice-exported for unpaid invoice exported");
        window.Echo.private("invoice-exported").listen("UnpaidInvoiceExportEvent", (e) => {
            if (e.status === "finished") {
                exportUrl.value = e.temporarySignedUrl;
                isInitiateExport.value = false;
            }
        });
    }
});
</script>

<style scoped>

</style>
