<template>
    <single-report active="Bill Report">
        <Head title="Bill Reports" />
        <div class="xc-container-2">
            <div
                class="flex flex-wrap items-center justify-between mb-15px gap-x-2 gap-y-2"
            >
                <h4
                    class="text-dark dark:text-white text-xl md:text-xl leading-none"
                >
                    Bill Reports
                </h4>
            </div>
            <div
                v-if="isFilterApplied"
                class="grid grid-cols-1 small-laptop:grid-cols-1 tablet:grid-cols-1 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <span class="text-dark dark:text-white text-xl md:text-xl">
                    <strong>Applied Filters: </strong>
                    <span
                        v-if="
                            billing_plan_filter &&
                            billing_plan_filter.length > 0
                        "
                        >Filter Bills By Plan:
                        <span class="text-green-700">{{
                            billingPlanFilter
                        }}</span></span
                    >
                    <span
                        class="ml-2"
                        v-if="
                            bill_status_filter && bill_status_filter.length > 0
                        "
                        >| Bill Status Filter:
                        <span class="text-green-700">{{
                            billStatusFilter
                        }}</span></span
                    >
                    <span
                        class="ml-2"
                        v-if="bill_type_filter && bill_type_filter.length > 0"
                        >| Bill Type Filter:
                        <span class="text-green-700">{{
                            billTypeFilter
                        }}</span></span
                    >
                    <span
                        class="ml-2"
                        v-if="
                            offer_filter &&
                            offer_filter !== 'Bill Has Offer Filter'
                        "
                        >| Bill Has Offer Filter:
                        <span class="text-green-700">{{
                            offer_filter
                        }}</span></span
                    >
                    <span
                        class="ml-2"
                        v-if="
                            period_filter &&
                            period_filter !== 'Bill Period Filter'
                        "
                        >| Bill Period Filter:
                        <span class="text-green-700">{{
                            period_filter
                        }}</span></span
                    >
                    <span
                        class="ml-2"
                        v-if="
                            package_filter &&
                            package_filter !== 'Package Filter'
                        "
                        >| Package Filter:
                        <span class="text-green-700">{{
                            package_filter
                        }}</span></span
                    >
                    <span
                        class="ml-2"
                        v-if="
                            product_filter &&
                            product_filter !== 'Product Filter'
                        "
                        >| Product Filter:
                        <span class="text-green-700">{{
                            product_filter
                        }}</span></span
                    >
                    <span
                        class="ml-2"
                        v-if="start_date_filter && start_date_filter.length > 0"
                        >| Date Filter:
                        <span class="text-green-700">{{
                            start_date_filter + " to " + end_date_filter
                        }}</span></span
                    >
                </span>
            </div>
            <div
                class="grid grid-cols-4 small-laptop:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <stat-box
                    image-link="img/report/has_offer.svg"
                    title="Total paid"
                    :is-price="true"
                    :price="total_paid_sales"
                />
                <stat-box
                    image-link="img/report/has_offer.svg"
                    title="Total unpaid"
                    :is-price="true"
                    :price="total_unpaid_sales"
                />
                <stat-box
                    image-link="img/report/unpaid_bill.svg"
                    title="Bills Unpaid By User"
                    :is-price="true"
                    :price="bills_unpaid_by_user"
                />
                <stat-box
                    image-link="img/report/paid_bill.svg"
                    class="bg-amber-200"
                    title="Total Refunds"
                    :is-price="true"
                    :price="total_refunds"
                />
                <stat-box
                    image-link="img/report/refundable_bill.svg"
                    title="Bills Refundable"
                    :is-price="true"
                    :price="bills_refundable"
                />
                <stat-box
                    image-link="img/report/paid_bill.svg"
                    title="Bills Paid Using Plan"
                    :is-price="true"
                    :price="bills_paid_using_plan"
                />
                <stat-box
                    image-link="img/report/paid_bill.svg"
                    title="Total Product Bills"
                    :is-price="true"
                    :price="total_product_bills"
                />
                <stat-box
                    image-link="img/report/has_offer.svg"
                    title="Bills Has Offer"
                    :is-price="true"
                    :price="bills_has_offer"
                />
            </div>

            <div
                class="flex flex-col gap-50px wide-mobile:p-30px mobile:p-20px w-full"
            >
                <div class="rounded-md flex flex-col">
                    <div
                        class="flex flex-wrap mb-5 items-center justify-between gap-x-2 gap-y-2"
                    >
                        <h4
                            class="text-dark dark:text-white text-xl md:text-xl leading-none"
                        >
                            Bills
                        </h4>
                        <div class="flex items-center gap-3">
                            <div
                                class="relative inline-block text-left wide-mobile:ml-2 small-laptop:hidden w-72"
                            >
                                <form class="relative flex items-center group">
                                    <input
                                        type="text"
                                        v-model="search"
                                        class="flex-auto w-full appearance-none bg-white dark:bg-mode-light pl-4 pr-12 h-12 min-h-12 rounded-10px text-dark dark:text-white text-base placeholder-secondary-full dark:placeholder-secondary-full focus:outline-none font-normal border-none focus:ring-success-light"
                                        placeholder="Find your servers or sites"
                                        @mouseleave="searchFilter"
                                    />
                                    <div
                                        class="icon flex absolute top-1/2 right-4 -translate-y-1/2 pointer-events-none text-lg"
                                    >
                                        <i
                                            class="xcloud xc-search text-secondary-light group-focus-within:text-success-light"
                                        ></i>
                                    </div>
                                </form>
                            </div>
                            <button
                                @click.prevent="openConfirmModal = true"
                                type="button"
                                class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-success-light text-lg mobile:text-base px-25px mobile:px-15px font-medium rounded-10px shadow-none text-white bg-success-light hover:bg-success-full hover:shadow-lg hover:shadow-success-full/30 transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none"
                            >
                                Export to CSV
                            </button>
                        </div>
                    </div>
                    <div
                        class="flex flex-col gap-5 mb-3 p-25px rounded-xl bg-focused dark:bg-mode-focus-dark"
                    >
                        <div
                            class="w-full grid grid-cols-[repeat(auto-fill,minmax(18rem,1fr))] gap-5"
                        >
                            <div
                                class="inline-flex items-center justify-between rounded-10px border-light dark:border-mode-light min-h-50px wide-mobile:min-h-40px dark:bg-mode-light text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 gap-20px w-full"
                            >
                                <multiselect
                                    @select="onBillingPlanSelect"
                                    class="border-none rounded-10px"
                                    :class="{
                                        'ring-1 ring-danger-light':
                                            billingPlanErrors(),
                                    }"
                                    v-model="form.billing_plans"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    placeholder="Filter Bills By Plan"
                                    :options="billing_plan"
                                    regex="^[a-zA-Z0-9_ -]+$"
                                    @close="closeFilter"
                                />
                                <Error :error="billingPlanErrors()" />
                            </div>

                            <div
                                class="inline-flex items-center justify-between rounded-10px border-light dark:border-mode-light w-full min-h-50px wide-mobile:min-h-40px dark:bg-mode-light text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 gap-20px"
                            >
                                <multiselect
                                    @select="onBillStatusSelect"
                                    class="border-none rounded-10px"
                                    :class="{
                                        'ring-1 ring-danger-light':
                                            billStatusErrors(),
                                    }"
                                    v-model="form.bill_status"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    placeholder="Bill Status Filter"
                                    :options="bill_status"
                                    regex="^[a-zA-Z0-9_ -]+$"
                                    @close="closeFilter"
                                />
                                <Error :error="billStatusErrors()" />
                            </div>

                            <div
                                class="inline-flex items-center justify-between rounded-10px border-light dark:border-mode-light w-full min-h-50px wide-mobile:min-h-40px dark:bg-mode-light text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 gap-20px"
                            >
                                <multiselect
                                    @select="onBillTypeSelect"
                                    class="border-none rounded-10px"
                                    :class="{
                                        'ring-1 ring-danger-light':
                                            billTypeErrors(),
                                    }"
                                    v-model="form.bill_type"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    placeholder="Bill Type"
                                    :options="bill_type"
                                    regex="^[a-zA-Z0-9_ -]+$"
                                    @close="closeFilter"
                                />
                                <Error :error="billTypeErrors()" />
                            </div>

                            <DatePicker v-model.range="date">
                                <template #default="{ togglePopover }">
                                    <button
                                        type="button"
                                        class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light text-lg mobile:text-base px-25px mobile:px-15px font-medium rounded-10px shadow-none text-white bg-primary-light hover:bg-primary-light hover:shadow-lg hover:shadow-primary-dark/30 transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none"
                                        @click="togglePopover"
                                    >
                                        Filter by Date
                                    </button>
                                </template>
                            </DatePicker>



                            <SelectorTitleDropdown
                                :selected-item="offer_filter"
                                :is-full="true"
                            >
                                <div
                                    class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                                >
                                    <div class="p-2px">
                                        <SelectItem
                                            title="Bill Has Offer Filter"
                                            :is-active="
                                                offer_filter ===
                                                'Bill Has Offer Filter'
                                            "
                                            @onItemSelected="
                                                onBillOfferFilterChanged($event)
                                            "
                                        />
                                        <SelectItem
                                            title="Has Offer"
                                            :is-active="
                                                offer_filter === 'Has Offer'
                                            "
                                            @onItemSelected="
                                                onBillOfferFilterChanged($event)
                                            "
                                        />
                                        <SelectItem
                                            title="No Offer"
                                            :is-active="
                                                offer_filter === 'No Offer'
                                            "
                                            @onItemSelected="
                                                onBillOfferFilterChanged($event)
                                            "
                                        />
                                    </div>
                                </div>
                            </SelectorTitleDropdown>

                            <SelectorTitleDropdown
                                :selected-item="period_filter"
                                :is-full="true"
                            >
                                <div
                                    class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                                >
                                    <div class="p-2px">
                                        <SelectItem
                                            title="Bill Period Filter"
                                            :is-active="
                                                period_filter ===
                                                'Bill Period Filter'
                                            "
                                            @onItemSelected="
                                                onBillPeriodFilterChanged(
                                                    $event
                                                )
                                            "
                                        />
                                        <SelectItem
                                            title="Monthly"
                                            :is-active="
                                                period_filter === 'Monthly'
                                            "
                                            @onItemSelected="
                                                onBillPeriodFilterChanged(
                                                    $event
                                                )
                                            "
                                        />
                                        <SelectItem
                                            title="Yearly"
                                            :is-active="
                                                period_filter === 'Yearly'
                                            "
                                            @onItemSelected="
                                                onBillPeriodFilterChanged(
                                                    $event
                                                )
                                            "
                                        />
                                        <SelectItem
                                            title="Lifetime"
                                            :is-active="
                                                period_filter === 'Lifetime'
                                            "
                                            @onItemSelected="
                                                onBillPeriodFilterChanged(
                                                    $event
                                                )
                                            "
                                        />
                                    </div>
                                </div>
                            </SelectorTitleDropdown>

                            <SelectorTitleDropdown
                                :selected-item="package_filter"
                                :is-full="true"
                            >
                                <div
                                    class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                                >
                                    <div class="p-2px">
                                        <SelectItem
                                            title="No Package"
                                            :is-active="
                                                package_filter ===
                                                'No Package'
                                            "
                                            @onItemSelected="
                                                onPackageFilterChanged($event)
                                            "
                                        />
                                        <SelectItem
                                            v-for="(
                                                packageFilter, index
                                            ) in packages"
                                            :key="index"
                                            :title="packageFilter"
                                            :is-active="
                                                package_filter === packageFilter
                                            "
                                            @onItemSelected="
                                                onPackageFilterChanged($event)
                                            "
                                        />
                                    </div>
                                </div>
                            </SelectorTitleDropdown>

                            <SelectorTitleDropdown
                                :selected-item="product_filter"
                                :is-full="true"
                            >
                                <div
                                    class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                                >
                                    <div class="p-2px">
                                        <SelectItem
                                            title="Product Filter"
                                            :is-active="
                                                product_filter ===
                                                'Product Filter'
                                            "
                                            @onItemSelected="
                                                onProductFilterChanged($event)
                                            "
                                        />
                                        <SelectItem
                                            v-for="(product, index) in products"
                                            :key="index"
                                            :title="product"
                                            :is-active="
                                                product_filter === product
                                            "
                                            @onItemSelected="
                                                onProductFilterChanged($event)
                                            "
                                        />
                                    </div>
                                </div>
                            </SelectorTitleDropdown>

                            <div
                                class="inline-flex items-center justify-between rounded-10px border-light dark:border-mode-light w-full min-h-50px wide-mobile:min-h-40px dark:bg-mode-light text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 gap-20px"
                            >
                                <multiselect
                                    @select="onTeamFilterSelect"
                                    class="border-none rounded-10px"
                                    :class="{
                                        'ring-1 ring-danger-light':
                                            teamFilterErrors(),
                                    }"
                                    v-model="form.team_filter"
                                    mode="tags"
                                    :close-on-select="false"
                                    :searchable="true"
                                    :create-option="true"
                                    placeholder="Filter By Team"
                                    :options="allTeams"
                                    @open="getAllTeam"
                                    @search-change="getAllTeam"
                                    regex="^[a-zA-Z0-9_ -]+$"
                                    @close="closeFilter"
                                />
                                <Error :error="billTypeErrors()" />
                            </div>

                            <div
                                class="inline-flex items-center justify-between rounded-10px border-light dark:border-mode-light w-full min-h-50px wide-mobile:min-h-40px dark:bg-mode-light text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 gap-20px"
                            >
                                <multiselect
                                    @select="mrrMonthlyFilter"
                                    class="border-none rounded-10px"
                                    v-model="form.filter_by_month"
                                    :multiple="false"
                                    :close-on-select="true"
                                    :searchable="true"
                                    :create-option="false"
                                    placeholder="Choose month to filter"
                                    :options="months"/>
                            </div>

                            <div
                                class="inline-flex items-center justify-between rounded-10px border-light dark:border-mode-light w-full min-h-50px wide-mobile:min-h-40px dark:bg-mode-light text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 gap-20px"
                            >
                                <multiselect
                                    @select="mrrYearlyFilter"
                                    class="border-none rounded-10px"
                                    v-model="form.filter_by_year"
                                    :multiple="false"
                                    :close-on-select="true"
                                    :searchable="true"
                                    :create-option="false"
                                    placeholder="Choose year to filter"
                                    :options="years"/>
                            </div>
                        </div>
                        <div class="flex items-center justify-end">
                            <button
                                @click.prevent="resetFilter"
                                type="button"
                                class="inline-flex items-center gap-2 justify-center text-lg mobile:text-base font-medium shadow-none text-white transition ease-in-out duration-300 focus:outline-none leading-none mobile:leading-none"
                            >
                                <i class="xcloud xc-verify_dns"></i>
                                Reset Filter
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto w-full">
                        <table class="w-full">
                            <thead class="bg-primary-light dark:bg-dark">
                                <tr
                                    class="divide-x divide-light dark:divide-dark"
                                >
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        ID
                                    </th>
                                    <th
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Title
                                    </th>
                                    <th
                                        class="px-30px py-20px text-lg font-normal text-white dark:text-white text-center whitespace-nowrap"
                                    >
                                        Bill From
                                    </th>
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Is Active
                                    </th>
                                    <th
                                        class="px-30px py-20px text-lg font-normal text-white dark:text-white text-right whitespace-nowrap"
                                    >
                                        Bill
                                    </th>
                                    <th
                                        class="px-30px py-20px text-lg font-normal text-white dark:text-white text-right whitespace-nowrap"
                                    >
                                        To Pay
                                    </th>
                                    <th
                                        class="px-30px py-20px text-right text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Adjustable
                                    </th>
                                    <th
                                        class="px-30px py-20px text-right text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Transparent Cost
                                    </th>
                                    <th
                                        class="px-30px py-20px text-right text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Refundable
                                    </th>
                                    <th
                                        class="px-30px py-20px text-right text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Source Bill Amount
                                    </th>
                                    <th
                                        class="px-30px py-20px text-right text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Application Fee To Charge
                                    </th>
                                    <th
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Team
                                    </th>
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Generated ID
                                    </th>
                                    <th
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Generator Type
                                    </th>
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Type
                                    </th>
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Status
                                    </th>
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Has Offer
                                    </th>
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Renew
                                    </th>
                                    <th
                                        class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Next Billing
                                    </th>
                                    <th
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Billing Plan
                                    </th>
                                    <th
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Package
                                    </th>
                                    <th
                                        class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white whitespace-nowrap"
                                    >
                                        Product
                                    </th>
                                </tr>
                            </thead>
                            <tbody
                                class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark"
                            >
                                <tr
                                    v-for="bill in bills.data"
                                    :key="bill.id"
                                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                >
                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white"
                                    >
                                        {{ bill.id }}
                                    </td>

                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">{{
                                            bill?.title
                                        }}</span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-dark dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">{{
                                            bill?.bill_from
                                        }}</span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-base font-normal text-dark dark:text-white group-hover:text-white text-center"
                                    >
                                        <span class="whitespace-nowrap">
                                            <span
                                                v-if="bill?.service_is_active"
                                            >
                                                <img
                                                    src="/img/success.svg"
                                                    class="m-auto"
                                                    width="24"
                                                    alt=""
                                                />
                                            </span>
                                            <span v-else>
                                                <img
                                                    src="/img/cross.svg"
                                                    class="m-auto"
                                                    width="24"
                                                    alt=""
                                                />
                                            </span>
                                        </span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-right text-base font-normal text-dark dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap"
                                            >${{ bill?.billing_amount }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-right text-base font-normal text-dark dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap"
                                            >${{ bill?.amount_to_pay }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-base font-normal text-dark dark:text-white group-hover:text-white text-right"
                                    >
                                        <span class="whitespace-nowrap"
                                            >${{
                                                bill?.adjustable_amount
                                            }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-right text-base font-normal text-dark dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap"
                                            >${{ bill?.transparent_cost }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-right text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap"
                                            >${{
                                                bill?.refundable_amount
                                            }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-right text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap"
                                            >${{
                                                bill?.source_bill_amount ?? 0
                                            }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-right text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap"
                                            >${{
                                                bill?.application_fee_to_charge ?? 0
                                            }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">
                                            <a
                                                :href="bill?.team_url"
                                                target="_blank"
                                                class="text-success-full group-hover:text-white"
                                                >{{ bill?.team }}</a
                                            >
                                        </span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">{{
                                            bill?.generator_id
                                        }}</span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">{{
                                            bill?.generator_type
                                        }}</span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span
                                            class="whitespace-nowrap capitalize"
                                            >{{ bill?.type }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span
                                            class="whitespace-nowrap capitalize"
                                            >{{ bill?.status }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">
                                            <span v-if="bill?.has_offer">
                                                <img
                                                    src="/img/success.svg"
                                                    class="m-auto"
                                                    width="24"
                                                    alt=""
                                                />
                                            </span>
                                            <span v-else>
                                                <img
                                                    src="/img/cross.svg"
                                                    class="m-auto"
                                                    width="24"
                                                    alt=""
                                                />
                                            </span>
                                        </span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span
                                            class="whitespace-nowrap capitalize"
                                            >{{ bill?.renewal_period }}</span
                                        >
                                    </td>
                                    <td
                                        class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">{{
                                            bill?.next_date
                                        }}</span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">
                                            <a
                                                :href="bill?.billing_plan_url"
                                                target="_blank"
                                                class="text-success-full group-hover:text-white"
                                                >{{ bill?.billing_plan }}</a
                                            >
                                        </span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">
                                            <a
                                                :href="bill?.package_url"
                                                target="_blank"
                                                class="text-success-full group-hover:text-white"
                                                >{{ bill?.package }}</a
                                            >
                                        </span>
                                    </td>
                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-secondary-full dark:text-white group-hover:text-white"
                                    >
                                        <span class="whitespace-nowrap">
                                            <a
                                                :href="bill?.product_url"
                                                target="_blank"
                                                class="text-success-full group-hover:text-white"
                                                >{{ bill?.product }}</a
                                            >
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <ExportConfirmation
                    :openConfirmModal="openConfirmModal"
                    @closeModal="closeConfirmationModal"
                    @confirmExport="confirmExport"
                    :is-initiate-export="isInitiateExport"
                    :export-url="exportUrl"
                />

                <!-- pagination -->
                <Pagination
                    v-if="hasPagination"
                    :links="bills.links"
                    :query-string="generateFilterQueryString()"
                />
            </div>
        </div>
    </single-report>
</template>

<script setup>
import { onMounted, ref } from "vue";
import SingleReport from "@/Pages/Reports/SingleReport.vue";
import Pagination from "@/Shared/Pagination.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import SelectorTitleDropdown from "@/Shared/SelectorTitleDropdown.vue";
import Multiselect from "@vueform/multiselect";
import Error from "@/Shared/Error.vue";
import { useForm } from "@inertiajs/inertia-vue3";
import { DatePicker } from "v-calendar";
import "v-calendar/style.css";
import { Inertia } from "@inertiajs/inertia";
import StatBox from "@/Pages/Reports/StatBox.vue";
import ExportConfirmation from "@/Pages/Reports/ExportConfirmation.vue";

const props = defineProps({
    total_refunds: Number,
    total_paid_sales: Number,
    total_unpaid_sales: Number,
    bills_has_offer: Number,
    bills_unpaid_by_user: Number,
    bills_refundable: Number,
    bills_paid_using_plan: Number,
    total_product_bills: Number,
    total_package_bills: Number,
    bills: Object,
    hasPagination: {
        type: Boolean,
        default: true,
    },
    billing_plan: Array,
    bill_status: Array,
    bill_type: Array,
    products: Object,
    teams: Array,
    packages: Object,
    billing_plan_filter: Array,
    bill_status_filter: Array,
    bill_type_filter: Array,
    offer_filter: String,
    period_filter: String,
    package_filter: String,
    product_filter: String,
    team_filter: Array,
    search_filter: String,
    start_date_filter: String,
    end_date_filter: String,
    is_filter_apply: Boolean,
    filter_by_month: String,
    filter_by_year: String
});

const urlParams = new URLSearchParams(window.location.search);
let date = ref({
    start: urlParams.has("startDate") ? urlParams.get("startDate") : null,
    end: urlParams.has("endDate") ? urlParams.get("endDate") : null,
});

const openConfirmModal = ref(false);
const isFilterApplied = ref(props?.is_filter_apply);

const isInitiateExport = ref(false);
const exportUrl = ref("");
const allTeams = ref(props.teams ?? [])

let form = useForm({
    billing_plans: props?.billing_plan_filter ?? [],
    bill_status: props?.bill_status_filter ?? [],
    bill_type: props?.bill_type_filter ?? [],
    team_filter: props?.team_filter ?? [],
    filter_by_month: props?.filter_by_month ?? '',
    filter_by_year: props?.filter_by_year ?? '',
});
const offer_filter = ref(props?.offer_filter);
const period_filter = ref(props?.period_filter);
const package_filter = ref(props?.package_filter);
const product_filter = ref(props?.product_filter);
const search = ref(props?.search_filter);

const closeConfirmationModal = () => {
    openConfirmModal.value = false;
    isInitiateExport.value = false;
    exportUrl.value = "";
};

const billingPlanFilter =
    props.billing_plan_filter &&
    props.billing_plan_filter
        .map((plan, index) => {
            if (index < props.billing_plan_filter.length - 1) {
                return `${plan.charAt(0).toUpperCase()}${plan
                    .slice(1)
                    .toLowerCase()}, `;
            } else {
                return `${plan.charAt(0).toUpperCase()}${plan
                    .slice(1)
                    .toLowerCase()}`;
            }
        })
        .join("");
const billStatusFilter =
    props.bill_status_filter &&
    props.bill_status_filter
        .map((status, index) => {
            if (index < props.bill_status_filter.length - 1) {
                return `${status.charAt(0).toUpperCase()}${status
                    .slice(1)
                    .toLowerCase()}, `;
            } else {
                return `${status.charAt(0).toUpperCase()}${status
                    .slice(1)
                    .toLowerCase()}`;
            }
        })
        .join("");
const billTypeFilter =
    props.bill_type_filter &&
    props.bill_type_filter
        .map((type, index) => {
            if (index < props.bill_type_filter.length - 1) {
                return `${type.charAt(0).toUpperCase()}${type
                    .slice(1)
                    .toLowerCase()}, `;
            } else {
                return `${type.charAt(0).toUpperCase()}${type
                    .slice(1)
                    .toLowerCase()}`;
            }
        })
        .join("");

const generateFilterQueryString = () => {
    let queryString = "";

    if (date.value.start && date.value.end) {
        queryString += `&startDate=${date.value.start}&endDate=${date.value.end}`;
        loadReportsBasedOnFilters();
    }

    return queryString;
};

function onBillOfferFilterChanged(offerFilter) {
    offer_filter.value = offerFilter;
    loadReportsBasedOnFilters();
}
function onBillPeriodFilterChanged(periodFilter) {
    period_filter.value = periodFilter;
    loadReportsBasedOnFilters();
}
function onPackageFilterChanged(packageFilter) {
    package_filter.value = packageFilter;
    loadReportsBasedOnFilters();
}
function onProductFilterChanged(productFilter) {
    product_filter.value = productFilter;
    loadReportsBasedOnFilters();
}

const onBillingPlanSelect = (plan) => {
    if (plan.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
        form.billing_plans = form.billing_plans.filter((item) => item !== plan);
    }
    loadReportsBasedOnFilters();
};

const onBillStatusSelect = (status) => {
    if (status.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
        form.bill_status = form.bill_status.filter((item) => item !== status);
    }
    loadReportsBasedOnFilters();
};

const onBillTypeSelect = (type) => {
    if (type.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
        form.bill_type = form.bill_type.filter((item) => item !== type);
    }
    loadReportsBasedOnFilters();
};

const onTeamFilterSelect = (team) => {
    form.team_filter = form.team_filter.filter((item) => item.value !== team);
    loadReportsBasedOnFilters();
};

const mrrMonthlyFilter = (month) => {
    loadReportsBasedOnFilters();
}

const mrrYearlyFilter = (year) => {
    loadReportsBasedOnFilters();
}

const getAllTeam = (search = "") => {
    if (search && search.length > 0) {
        axios
            .get(
                route("reports.all.team", {
                    team_name: search,
                })
            )
            .then(({ data }) => {
                allTeams.value = data
            })
            .catch((error) => {});
    }
};

const months = ref([
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
]);

const years = ref([
    "2023",
    "2024",
    "2025",
]);

const loadReportsBasedOnFilters = () => {
    //load reports based on filter
    isFilterApplied.value = true;
    Inertia.get(route("reports.bill"), {
        billing_plan: form.billing_plans,
        bill_status: form.bill_status,
        bill_type: form.bill_type,
        offer_filter: offer_filter.value,
        period_filter: period_filter.value,
        start_date: date.value.start,
        end_date: date.value.end,
        package_filter: package_filter.value,
        product_filter: product_filter.value,
        team_filter: form.team_filter,
        is_filter_apply: isFilterApplied.value,
        filter_by_month: form.filter_by_month,
        filter_by_year: form.filter_by_year,
    });
};

const closeFilter = () => {
    loadReportsBasedOnFilters();
}

const resetFilter = () => {
    Inertia.get(route("reports.bill"));
};

const searchFilter = () => {
    if (search.value && search.value.length > 0) {
        Inertia.get(route("reports.bill"), {
            search: search.value,
        });
    }
};

const confirmExport = (exportType) => {
    axios
        .post(route("reports.bill.export"), {
            billing_plan: form.billing_plans,
            bill_status: form.bill_status,
            bill_type: form.bill_type,
            offer_filter: offer_filter.value,
            period_filter: period_filter.value,
            start_date: date.value.start,
            end_date: date.value.end,
            package_filter: package_filter.value,
            product_filter: product_filter.value,
            is_filter_apply: isFilterApplied.value,
            team_filter: form.team_filter,
            filter_by_month: form.filter_by_month,
            filter_by_year: form.filter_by_year,
            export_type: exportType,
        })
        .then((response) => {
            //window.open(response.data, '_blank');
            if (response.data === "initiate") {
                isInitiateExport.value = true;
            }
        })
        .catch((error) => {});
};

const billingPlanErrors = function () {
    const errors = [];

    if (form.billing_plans.length > 0) {
        form.billing_plans.forEach((plan, index) => {
            const error = form.errors[`billing_plans.${index}`] ?? null;
            if (error) {
                errors.push(error);
            }
        });
    }

    return errors.length ? errors[0] : null;
};

const billStatusErrors = function () {
    const errors = [];

    if (form.bill_status.length > 0) {
        form.bill_status.forEach((status, index) => {
            const error = form.errors[`bill_status.${index}`] ?? null;
            if (error) {
                errors.push(error);
            }
        });
    }

    return errors.length ? errors[0] : null;
};

const billTypeErrors = function () {
    const errors = [];

    if (form.bill_type.length > 0) {
        form.bill_type.forEach((type, index) => {
            const error = form.errors[`bill_type.${index}`] ?? null;
            if (error) {
                errors.push(error);
            }
        });
    }

    return errors.length ? errors[0] : null;
};

const teamFilterErrors = function () {
    const errors = [];

    if (form.team_filter.length > 0) {
        form.team_filter.forEach((type, index) => {
            const error = form.errors[`team_filter.${index}`] ?? null;
            if (error) {
                errors.push(error);
            }
        });
    }

    return errors.length ? errors[0] : null;
};

onMounted(() => {
    if (window.Echo) {
        console.log("joining bills-exported for bills exported");
        window.Echo.private("bills-exported").listen("BillsExported", (e) => {
            if (e.status === "finished") {
                exportUrl.value = e.temporarySignedUrl;
                isInitiateExport.value = false;
            }
        });
    }
});

const isGrid = ref(false);
</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
</style>
