<template>
  <h3 class="text-lg text-dark dark:text-white leading-none px-20 py-10">
    {{ $t('Processing payment... Please do not cancel or refresh the page.') }}
  </h3>
</template>

<script setup>

import {useForm} from "@inertiajs/inertia-vue3";
import { loadStripe } from '@stripe/stripe-js';
import {onMounted, ref} from "vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";

const props = defineProps({
  clientSecret: String,
  paymentIntentStatus: String,
  paymentIntentId: String,
  sessionId: String,
  productId: String,
  affiliateId: String,
  cartId: String,
  invoiceId: String,
  data: Object,
  stripePublishableKey: String
});

let form = useForm({
  paymentIntentId: props.paymentIntentId,
  sessionId: props.sessionId,
  productId: props.productId,
  affiliateId: props.affiliateId,
  cartId: props.cartId,
  invoiceId: props.invoiceId,
  requestData: props.data,
  resize_confirmation: props.data?.resize_confirmation,
  size: props.data?.size,
  backup: props.data?.backup,
});

const clientSecret = ref(props.clientSecret);
// let stripePromise = loadStripe(VITE_STRIPE_PUBLISH_KEY);
let stripePromise = loadStripe(props.stripePublishableKey);

onMounted(async () => {
  const stripe = await stripePromise;

  // Check if stripe is properly initialized and clientSecret is available
  if (!stripe || !clientSecret.value) {
    console.error('Stripe is not initialized or clientSecret is missing');
    return;
  }

  await handle3DSecurePayment(stripe);
});

const handle3DSecurePayment = async (stripe) => {
  const result = await stripe.confirmCardPayment(clientSecret.value);

  if (result.error) {
    console.error('Payment confirmation failed:', result.error.message);
    Inertia.visit(route('user.detailedInvoices'), {
      preserveScroll: true,
      onSuccess: () => {
        useFlash().error('Payment failed.');
      }
    });
  } else {
    if (result.paymentIntent && result.paymentIntent.status === 'succeeded') {
      // Payment succeeded, perform further actions such as informing your server
      confirmPayment(result.paymentIntent.id);
    }
  }
};

const confirmPayment = (paymentIntentId) => {
  // Implement your logic to inform the server to confirm the payment
  form.post(route('api.product.payment.confirm-3d-secured-payment'), {
    preserveScroll: true,
    onSuccess: () => {
      useFlash().success('Payment successfully processed.');
    }
  });
};

</script>
