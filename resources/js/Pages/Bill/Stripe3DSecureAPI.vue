<template>
  <div class="fixed inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 z-50">
    <div class="text-center">
      <!-- Animated Loading Spinner -->
      <div class="mb-6">
        <div class="relative mx-auto w-16 h-16">
          <div class="absolute inset-0 border-4 border-blue-200 dark:border-blue-800 rounded-full"></div>
          <div class="absolute inset-0 border-4 border-transparent border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
        </div>
      </div>

      <!-- Main Message -->
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-3">
        {{ $t('Processing Payment') }}
      </h2>

      <!-- Animated Dots -->
      <div class="flex justify-center space-x-1 mb-4">
        <div class="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
        <div class="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
        <div class="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
      </div>

      <!-- Warning Message -->
      <p class="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
        {{ $t('Please do not cancel or refresh the page.') }}<br>
        {{ $t('This process may take a few moments.') }}
      </p>

      <!-- Security Badge -->
      <div class="mt-6 flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
        </svg>
        <span>{{ $t('Secure Payment Processing') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useForm } from "@inertiajs/inertia-vue3";
import { loadStripe } from '@stripe/stripe-js';
import { onMounted } from "vue";
import { useFlash } from "@/Composables/useFlash";
import { Inertia } from "@inertiajs/inertia";

const props = defineProps({
  invoiceNumber: String,
  clientSecret: String,
  paymentIntentId: String,
  successRedirectUrl: String,
  failureRedirectUrl: String,
  stripePublishableKey: String
});

const form = useForm({
  paymentIntentId: props.paymentIntentId
});

onMounted(async () => {
  const stripe = await loadStripe(props.stripePublishableKey);

  if (!stripe || !props.clientSecret) {
    console.error('Stripe is not initialized or clientSecret is missing');
    return;
  }

  await handle3DSecurePayment(stripe);
});

const handle3DSecurePayment = async (stripe) => {
  const result = await stripe.confirmCardPayment(props.clientSecret);

  if (result.error) {
    console.error('Payment confirmation failed:', result.error.message);
    if (props.failureRedirectUrl) {
      Inertia.visit(props.failureRedirectUrl, {
        preserveScroll: true,
        onSuccess: () => {
          useFlash().error(result.error.message);
        }
      });
    } else {
      useFlash().error(result.error.message);
      Inertia.reload({ preserveState: true });
    }
  } else if (result.paymentIntent?.status === 'succeeded') {
    confirmPayment();
  }
};

const confirmPayment = () => {
  form.post(route('api.payment.verify-3d-secured-payment', {
      invoice: props.invoiceNumber
  }), {
    preserveScroll: true,
    onSuccess: () => {
      useFlash().success('Payment successfully processed.');
      if (props.successRedirectUrl) {
        Inertia.visit(props.successRedirectUrl);
      }
    }
  });
};
</script>
