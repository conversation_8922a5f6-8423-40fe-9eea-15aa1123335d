<template>
  <div class="xc-container">
    <div>
      <h4 class="text-dark dark:text-white text-40px leading-none"> {{ $t('Packages') }} </h4>
      <div class="grid grid-cols-5 gap-5 mt-5">
        <div v-for="item in packages" :key="item.id">
          <p class="text-white">Package#{{item.id}} - {{ item.name }}</p>
          <Link
              :href="`${route('cart.checkoutWithPackage', {
              'package': item.id
            })}&name=${item.slug}`"
              id="payButton"
              class="stripe-button mt-3 inline-flex items-center justify-center rounded-md border-transparent shadow-none
                          min-h-20px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
              aria-expanded="true" aria-haspopup="true"
          >
            {{ $t('Buy Now') }}
          </Link>
        </div>
      </div>
    </div>

    <div class="mt-10">
      <h4 class="text-dark dark:text-white text-40px leading-none"> {{ $t('Products') }} </h4>
      <div class="grid grid-cols-5 gap-5 mt-5">
        <div v-for="item in products" :key="item.id">
          <p class="text-white">Product#{{item.id}} - {{ item.title }}</p>
          <Link :href="`${route('cart.checkoutWithProduct', {
              'product': item.id
            })}&name=${item.slug}`"
              target="_blank"
              id="payButton"
              class="stripe-button mt-3 inline-flex items-center justify-center rounded-md border-transparent shadow-none
                          min-h-20px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
              aria-expanded="true" aria-haspopup="true"
          >
              {{ $t('Buy Now') }}
          </Link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Button from "@/Jetstream/Button.vue";
import {Link} from "@inertiajs/inertia-vue3";
export default {
  components: {Button},
  props: {
    packages: Object,
    products: Object
  },
}
</script>
