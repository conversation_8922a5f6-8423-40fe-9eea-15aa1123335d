<template>
    <div class="xc-container">
        <div
            :class="
                navigation.isOpenHelpbar(helper.isOpen)
                    ? 'mr-400px small-laptop:mr-0'
                    : 'mr-0'
            "
            class="flex-1 flex items-center"
        >
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div
                    class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <div class="text-center">
                        <img
                            :src="asset('img/logo.svg')"
                            alt="xcloud_logo"
                            class="h-50px wide-mobile:h-40px inline-flex -mt-1 text-center"
                        />
                    </div>
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark
                        dark:text-white mb-20px wide-mobile:text-3xl mobile:text-28px tablet:mb-15px wide-mobile:mb-2.5">
                        {{ $t('Just One Click Away from Completing Your Order') }}
                    </h2>
                    <template v-if="!$page.props.user">
                      <p class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center mb-50px mobile:mb-25px">
                          {{ $t('Already have an account?') }}
                          <Link class="text-primary-light" href="/login">{{ $t('Sign In') }}</Link>
                      </p>
                    </template>
                    <div class="mt-30px">
                        <h3 class="text-lg font-medium text-dark dark:text-white leading-tight mb-15px">
                            {{ $t('Service Type') }}
                        </h3>
                        <div class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                            <template
                                v-for="(service_type,key) in service_types"
                                :key="key"
                            >
                            <div
                                 :class="{
                                    'border-secondary-light dark:border-mode-focus-light':
                                        selected_type !== key,
                                    'border-primary-light ring-1 ring-primary-light':
                                        selected_type === key,
                                }"
                                 v-if="selectedPackage?.service_type === key || !selectedPackage"
                                 class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                                 @click="selected_type = key"
                            >

                                <div class="shrink-0 inline-flex items-center justify-center">
                                    <img
                                        :src="
                                            asset('img/lifetime2.png')
                                        "
                                        alt="xcloud_logo"
                                        class="w-12 h-auto"
                                    />
                                </div>
                                <div class="flex flex-col gap-2">
                                    <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                       {{service_type}} <!--{{selected_type}}-->
                                    </h5>
                                    <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                        {{ $t('Integrate any cloud provider to manage server and sites in xCloud') }}
                                    </p>
                                </div>
                            </div>
                            </template>
                        </div>
                    </div>

                    <div class="mt-30px">
                        <div class="flex items-cent mb-15px">
                            <h3 class="text-lg font-medium text-dark dark:text-white leading-tight">
                                {{ $t('Choose Your Plan') }}
                            </h3>
                        </div>
                        <div class="grid grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-5 mobile:gap-4">
                            <Error :is-html="true" :error="form.errors.package" class="col-span-3" />
                            <template
                                v-for="pckg in packages ?? []"
                                :key="pckg.id"
                            >
                            <div
                                v-if="(0 !== pckg?.price || !$page.props.user) && selected_type === pckg?.service_type && (selectedPackage?.id === pckg?.id || !selectedPackage)"
                                :class="{
                                    'border-primary-light ring-1 ring-primary-light':
                                        pckg.id === form.package,
                                    'border-secondary-light dark:border-mode-focus-light':
                                        pckg.id === form.package,
                                }"
                                class="cursor-pointer flex flex-col justify-center items-stretch w-full rounded-xl border-1 border-solid divide-y-1 divide-secondary-light dark:divide-mode-focus-light"
                                @click.prevent="form.package = pckg.id"
                            >
                                <div class="flex items-center gap-3 p-4 justify-start">
                                    <h4 class="text-lg leading-none font-medium tracking-tighter text-dark dark:text-white">
                                        {{ pckg?.name }}
                                    </h4>
                                    <tooltip v-if="pckg?.tooltip" :title="pckg?.tooltip">
                                        <span
                                            class="text-xs inline-flex text-secondary-full dark:text-mode-secondary-light">
                                            <i class="xcloud xc-info-2"></i>
                                        </span>
                                    </tooltip>
                                </div>
                                <div class="px-4 py-5">
                                    <ul class="list-disc marker:text-light flex flex-col gap-2">
                                        <li v-for="description in pckg?.description" class="text-secondary-full dark:text-mode-secondary-light ml-3">
                                            <span class="text-dark dark:text-white">
                                              {{description}}
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="flex items-center gap-3 p-4 justify-start">
                                    <h4 class="text-lg leading-none font-medium tracking-tighter text-dark dark:text-white">
                                        ${{ pckg?.price }}/
                                        <small class="text-secondary-full dark:text-mode-secondary-light">{{pckg?.renewal_type}}</small>
                                    </h4>
                                </div>
                            </div>
                            </template>
                        </div>
                    </div>

<!--                    <div v-if="eligibleForSplitPayment">-->
<!--                      &lt;!&ndash; checkbox &ndash;&gt;-->
<!--                      <div class="flex items center justify-start mt-30px">-->
<!--                        <input-->
<!--                            type="checkbox"-->
<!--                            class="h-5 w-5 text-primary-light border-primary-light rounded-[4px]"-->
<!--                            id="terms"-->
<!--                            v-model="useSplitPayment"-->
<!--                        />-->
<!--                        <label for="terms" class="ml-2 block font-medium text-dark dark:text-white -mt-px">-->
<!--                          I want to pay via Split Payment option-->
<!--                        </label>-->
<!--                      </div>-->

<!--                      <div class="px-4 py-5 dark:bg-mode-base w-[40%] mt-5 rounded-lg"-->
<!--                           v-if="useSplitPayment && splitPaymentSummary.packageName">-->
<!--                        <p class="text-secondary-full dark:text-mode-secondary-light ml-3">-->
<!--                          <span class="text-dark dark:text-white">-->
<!--                            {{ splitPaymentSummary.packageName }} Plan - {{ splitPaymentSummary.serverCount }} Servers-->
<!--                          </span>-->
<!--                        </p>-->

<!--                        <p class="text-secondary-full dark:text-mode-secondary-light ml-3">-->
<!--                          <span class="text-dark dark:text-white">-->
<!--                            {{ splitPaymentSummary.installment }}-->
<!--                          </span>-->
<!--                        </p>-->
<!--                      </div>-->
<!--                    </div>-->

                    <div class="mt-10" v-show="!useSplitPayment && !$page.props.user">
                      <h3 class="text-lg font-medium text-dark dark:text-white leading-tight mb-15px">
                        Create Account
                      </h3>
                      <div class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                        <text-input
                            v-model="form.account_name"
                            :error="form.errors.account_name"
                            :label="$t('Account Name')"
                            :placeholder="$t('Account Name')"
                            type="text"
                        >
                        </text-input>

                        <text-input
                            v-model="form.account_email"
                            :error="form.errors.account_email"
                            :label="$t('Account Email')"
                            :placeholder="$t('Account Email')"
                            type="email"
                            @input-blur="checkEmail"
                        >
                        </text-input>
                      </div>
                      <div
                          class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px mt-15px">
                        <password-input
                            v-model="form.password"
                            :error="form.errors.password"
                            :label="$t('Password')"
                            placeholder="*********"
                            :note="form.errors.password ? '' : `Password must be at least  8 characters and should contain uppercase, number and special character`"
                        >
                        </password-input>
                      </div>
                    </div>

                    <template v-if="packages.length > 0">
                        <div class="mt-30px" v-if="originalCost && coupon && originalCost !== totalCost">
                            <p class="text-lg font-medium leading-none tracking-tighter text-dark dark:text-white">
                                {{ $t('Cost') }} : <b class="text-[2rem]">${{ originalCost }}</b>
                            </p>
                        </div>

                        <!--Applied Coupon code-->

                        <div class="mt-30px" v-if="originalCost && coupon && originalCost !== totalCost">
                            <p v-if="coupon?.discount_type === 'fixed_amount'" class="text-lg font-medium leading-none tracking-tighter text-dark dark:text-white">
                                {{ $t('Applied Coupon') }} : <b class="text-[2rem]">${{ coupon?.discount }}</b>
                            </p>

                            <p v-else-if="coupon?.discount_type === 'percentage'" class="text-lg font-medium leading-none tracking-tighter text-dark dark:text-white">
                                {{ $t('Applied Coupon') }} : <b class="text-[2rem]">{{ coupon?.discount }}%</b>
                            </p>
                        </div>

                        <div class="mt-30px" v-if="!useSplitPayment">
                            <p class="text-lg font-medium leading-none tracking-tighter text-dark dark:text-white">
                                {{ $t('Total Cost') }} : <b class="text-[2rem]">${{ totalCost }}</b>
                            </p>
                        </div>
                    </template>

                  <div class="mt-30px" v-if="selectedPackage && selectedPackage.max_purchase_limit">
                    <p class="text-lg font-medium leading-none tracking-tighter text-delete"
                       v-if="selectedPackage.purchase_count >= selectedPackage.max_purchase_limit">
                      {{ $t('Purchase Limit Reached!') }}
                    </p>
                    <div v-else>
                      <p class="text-lg font-medium leading-none tracking-tighter text-dark dark:text-white"
                         v-if="selectedPackage.max_purchase_limit === 1">
                        {{ $t('Only one purchase remaining!') }}
                      </p>
                      <p v-else class="text-lg font-medium leading-none tracking-tighter text-dark dark:text-white">
                        Total Purchases : {{ selectedPackage.max_purchase_limit }} times.
                        (Only {{ (selectedPackage.max_purchase_limit - selectedPackage.purchase_count) ?? 0 }} purchase remaining)
                      </p>
                    </div>
                  </div>
                </div>
                <WizardProgress
                    :has-next-button="packages.length > 0"
                    :show-progress="false"
                    :back="route('server.create')"
                    :processing="!useSplitPayment && (form.processing||form.errors?.account_email?.length > 0 || maxPurchaseLimitReached)"
                    :progress-width="progressWidth(2, 3)"
                    progress-text="Step 2 of 3"
                    @next="submit"
                >
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                        <span>{{ $t('Verifying') }}</span>
                    </template>
                    <template v-else>
                        <span v-text="useSplitPayment ? $t('Split Pay') : $t('Pay Now')" class="whitespace-nowrap"></span>
                    </template>
                </WizardProgress>
            </div>
        </div>
    </div>

    <Modal
        :show="showPaymentConfirmation"
        :widthClass="'max-w-850px'"
        :footer-button="true"
        :footer-button-title="$t('Disable Auto Backup')"
        :title="$t('Ready to confirm your purchase?')"
        @close="showPaymentConfirmation = false">
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img"/>
                <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b>{{ $t('Warning') }}:</b> {{ $t("By clicking 'Confirm', a charge of") }} ${{ totalCost }} {{ $t('will be applied to your saved credit card.') }}
                </p>
            </div>
        </div>
        <template #footer>
            <Btn
                :disabled="form.processing"
                @click.prevent="confirmPayNow" type="submit" :loading="form.processing">
                {{ form.processing ? $t('Processing Payment...') : $t('Confirm') }}
            </Btn>
        </template>
    </Modal>
</template>

<script setup>
import TextInput from "@/Shared/TextInput.vue";
import {Link, useForm, usePage} from "@inertiajs/inertia-vue3";
import {useNavigationStore} from "@/stores/NavigationStore";
import {useHelpStore} from "@/stores/HelpStore";
import {computed, onMounted, ref, watch} from "vue";
import PasswordInput from "@/Shared/PasswordInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {asset} from "laravel-vapor";
import WizardProgress from "@/Shared/WizardProgress.vue";
import Error from "@/Shared/Error.vue";
import {useFlash} from "@/Composables/useFlash";
import Modal from "@/Shared/Modal.vue";
import Btn from "@/Shared/Btn.vue";

let navigation = useNavigationStore();
let helper = useHelpStore();
const props = defineProps({
    coupon: {
        type: Object,
        default: null
    },
    packages: Array,
    selectedPackage: Object|Array,
    service_types: Array,
    eligibleForSplitPayment: {
        type: Boolean,
        default: false
    }
});
let showPaymentConfirmation = ref(false);
let user = usePage().props.value?.user;
let form = useForm({
    account_name: user?.name ?? "",
    account_email: user?.email ?? "",
    name: "",
    password: "",
    package: props.selectedPackage ? props.selectedPackage?.id : props.packages[0]?.id,
});

const selected_type = ref(props.selectedPackage ? props.selectedPackage?.service_type : props.packages[0]?.service_type);
let useSplitPayment = ref(false)
let splitPaymentSummary = ref({
    packageName: '',
    serverCount: 0,
    installment: ''
})

onMounted(() => {
    if (props.selectedPackage) {
        for(let i = 0; i < props.selectedPackage?.description?.length; i++){
            if(props.selectedPackage?.description?.[i].toLowerCase().includes('server')){
                // extract server count
                const match = props.selectedPackage?.description?.[i].toLowerCase().match(/\d+/);
                if (match) {
                  splitPaymentSummary.value.serverCount = parseInt(match[0], 10);
                } else {
                  console.error("No number found in the string.");
                  return;
                }
                break;
            }
        }

        splitPaymentSummary.value.packageName = props.selectedPackage?.name;
        if(props.selectedPackage?.name === 'GROWTH'){
          splitPaymentSummary.value.installment = '$203.00 for each month, for 3 installments';
        }else if(props.selectedPackage?.name === 'PERFORMANCE') {
          splitPaymentSummary.value.installment = '$252.00 for each month, for 4 installments';
        }else if(props.selectedPackage?.name === 'SUPREME') {
          splitPaymentSummary.value.installment = '$402.00 for each month, for 5 installments';
        }
    }
})

watch(selected_type, (value) => {
    form.package = props.packages.find((item) => item.service_type === value)?.id;
})

const totalCost = computed(() => props.packages.find((item) => item.id === form.package)?.price ?? 0);
const originalCost = computed(() => props.packages.find((item) => item.id === form.package)?.original_price ?? 0);

const progressWidth = (start, end) => (start * 100) / (end ?? 1);

const submit = () => {
    if (form.errors?.account_email?.length > 0){
        useFlash().error(form.errors.account_email)
        return;
    }
    if(useSplitPayment.value){
      if(props.selectedPackage?.name === 'GROWTH') {
        window.location = 'https://xcloud.host/?ff_landing=4';
      }else if(props.selectedPackage?.name === 'PERFORMANCE') {
        window.location = 'https://xcloud.host/?ff_landing=4&selected_plan=1';
      }else if(props.selectedPackage?.name === 'SUPREME') {
        window.location = 'https://xcloud.host/?ff_landing=4&selected_plan=2';
      }

      return;
    }

    if (user) {
        showPaymentConfirmation.value = true
    } else {
        form.post(
            route("api.cart.storeWithPackage", {
                package: form.package,
                coupon: props.coupon?.code
            }),
            {
                preserveScroll: true,
                onSuccess(response) {
                    if(response?.props?.jetstream?.flash?.checkout_url){
                        window.location = response?.props?.jetstream?.flash?.checkout_url;
                        // window.open(response?.props?.jetstream?.flash?.checkout_url);
                    }
                },
                onError(error) {
                    window.scrollTo(0, 0);
                },
            },
        );
    }
};

const confirmPayNow = () => {
    form.post(
        route("api.cart.storeWithPackage", {
            package: form.package,
            coupon: props.coupon?.code
        }),
        {
            preserveScroll: true,
            onSuccess(response) {
                if(response?.props?.jetstream?.flash?.checkout_url){
                    window.location = response?.props?.jetstream?.flash?.checkout_url;
                    // window.open(response?.props?.jetstream?.flash?.checkout_url);
                }
            },
            onError(error) {
                window.scrollTo(0, 0);
            },
        },
    );
}

const checkEmail = () => {
   // const emailRegex = /^[a-zA-Z0-9._+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    form.errors.account_email = null;
    if (emailRegex.test(form.account_email)) {
        axios.post(route('api.user.check', {email: form.account_email}))
            .then(response => {
                form.errors.account_email = null;
            }).catch((error) => {
            form.errors.account_email = error?.response?.data?.message ? error.response.data.message : error;
            useFlash().error(form.errors.account_email)
        })
    } else {
        form.errors.account_email = "Please enter a valid email address";
    }

};

const maxPurchaseLimitReached = computed(() => {
    return props.selectedPackage?.max_purchase_limit && props.selectedPackage?.max_purchase_limit <= props.selectedPackage?.purchase_count;
})

</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
@import "flag-icons/css/flag-icons.min.css";
</style>
