<template>
    <span>
        <span @click="startConfirmingPassword">
            <slot />
        </span>

        <jet-dialog-modal :show="confirmingPassword" @close="closeModal">
            <template #title>
                <span class="text-xl font-medium text-white dark:text-white">
                    {{ title }}
                </span>
            </template>

            <template #content>
                <div class="text-white dark:text-white">
                    {{ content }}
                </div>

                <div class="mt-4">
                     <PasswordInput v-model="form.password"
                                     :error="form.error"
                                     icon="xcloud xc-pass_lock"
                                     ref="password"
                                     type="password"
                                     placeholder="********"
                                     :label="$t('Password')+'*'"
                                     :autofocus="true"
                                     @keyup.enter="confirmPassword"/>
                </div>
            </template>

            <template #footer>
                <div class="flex gap-2 justify-end">
                    <button @click="closeModal"
                            class="inline-flex items-center justify-center rounded-10px border-transparent shadow-none px-5
                                  py-3 bg-delete text-sm font-medium text-white focus:outline-0">
                    {{ $t('Cancel') }}
                </button>
                <button
                    class="inline-flex items-center justify-center rounded-10px border-transparent shadow-none px-5
                                  py-3 bg-success-full text-sm font-medium text-white focus:outline-0"
                    @click="confirmPassword" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    {{ button }}
                </button>
                </div>
            </template>
        </jet-dialog-modal>
    </span>
</template>

<script>
    import { defineComponent } from 'vue'
    import JetButton from './Button.vue'
    import JetDialogModal from './DialogModal.vue'
    import JetInput from './Input.vue'
    import JetInputError from './InputError.vue'
    import JetSecondaryButton from './SecondaryButton.vue'
    import PasswordInput from '@/Shared/PasswordInput.vue'

    export default defineComponent({
        emits: ['confirmed'],

        props: {
            title: {
                default: 'Confirm Password',
            },
            content: {
                default: 'For your security, please confirm your password to continue.',
            },
            button: {
                default: 'Confirm',
            }
        },

        components: {
            JetButton,
            JetDialogModal,
            JetInput,
            JetInputError,
            JetSecondaryButton,
            PasswordInput
        },

        data() {
            return {
                confirmingPassword: false,
                form: {
                    password: '',
                    error: '',
                },
            }
        },

        methods: {
            startConfirmingPassword() {
                axios.get(route('password.confirmation')).then(response => {
                    if (response.data.confirmed) {
                        this.$emit('confirmed');
                    } else {
                        this.confirmingPassword = true;
                    }
                })
            },

            confirmPassword() {
                this.form.processing = true;

                axios.post(route('password.confirm'), {
                    password: this.form.password,
                }).then(() => {
                    this.form.processing = false;
                    this.closeModal()
                    this.$nextTick(() => this.$emit('confirmed'));
                }).catch(error => {
                    this.form.processing = false;
                    this.form.error = error.response.data.errors.password[0];
                });
            },

            closeModal() {
                this.confirmingPassword = false
                this.form.password = '';
                this.form.error = '';
            },
        }
    })
</script>
