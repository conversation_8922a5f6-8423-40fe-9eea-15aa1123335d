<template>
    <div 
      class="p-[40px] bg-white dark:bg-[#171A30] rounded-[4px] text-center border border-dashed border-gray-300 dark:border-[#495294]"
      @dragenter.prevent="isDragging = true"
      @dragover.prevent="isDragging = true"
      @dragleave.prevent="isDragging = false"
      @drop.prevent="handleFileDrop"
    >
      <div class="flex flex-col items-center justify-center gap-6">
        <!-- Upload icon -->
        <div class="text-blue-500 dark:text-blue-400 mb-2">
            <i class="xcloud xc-upload_2 text-[48px]"></i>
        </div>
        
        <!-- Title -->
      <div class="flex flex-col items-center justify-center gap-1">   
          <span class="text-gray-900 dark:text-white text-[24px] font-semibold">
          {{ $t('Drag and drop or click here') }}
          </span>
          <span class="text-gray-600 dark:text-[#CAC6DD] text-[16px] font-normal">
              {{ $t('to upload your file') }}
          </span>
      </div>
  
        
        <!-- File selection area -->
        <div class="w-full">
          <div v-if="selectedFile" class="mb-4 text-left bg-gray-50 dark:bg-[#1e2747] p-3 rounded-md">
            <div class="flex items-center justify-between">
              <div class="flex items-center text-gray-900 dark:text-white">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="truncate max-w-xs">{{ selectedFile.name }}</span>
              </div>
              <button @click="resetUpload" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>
          
          <button 
            v-if="!selectedFile"
            @click="triggerFileInput" 
            class="bg-gray-100 hover:bg-gray-200 dark:bg-[#1e2747] dark:hover:bg-[#252f54] text-gray-900 dark:text-white py-2 px-6 rounded-md transition duration-200"
          >
            {{ $t('Choose File') }}
          </button>
          
          <span v-if="!selectedFile" class="ml-3 text-gray-500 dark:text-gray-400">
            {{ $t('No file chosen') }}
          </span>
          
          <input
            type="file"
            ref="fileInput"
            :accept="accept"
            :multiple="multiple"
            @change="handleFileChange"
            class="hidden"
          />
        </div>
        
        <!-- Progress bar -->
        <div v-if="uploadProgress > 0" class="w-full mt-2">
          <div class="w-full bg-gray-100 dark:bg-[#1e2747] rounded-full h-2">
            <div
              class="bg-blue-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${uploadProgress}%` }"
            ></div>
          </div>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 text-right">{{ uploadProgress }}% {{ $t('uploaded') }}</p>
        </div>
        
        <!-- Action buttons -->
        <div class="flex justify-between w-full mt-4">
          <button
            @click="handleCancel"
            class="text-gray-700 hover:text-gray-900 dark:text-white dark:hover:text-gray-300 transition duration-200"
          >
            {{ cancelText }}
          </button>
          
          <button
            @click="handleUpload"
            :disabled="!selectedFile || uploading"
            class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded-md transition duration-200 flex items-center"
            :class="{ 'opacity-50 cursor-not-allowed': !selectedFile || uploading }"
          >
            <svg v-if="!uploading" class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
            </svg>
            <svg v-if="uploading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>{{ uploading ? uploadingText : uploadText }}</span>
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import Btn from '@/Shared/Btn.vue'
  import { useI18n } from 'vue-i18n'
  
  const { t } = useI18n()
  
  const props = defineProps({
    title: {
      type: String,
      default: 'Upload File'
    },
    accept: {
      type: String,
      default: '*/*'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    uploadUrl: {
      type: String,
      required: true
    },
    cancelText: {
      type: String,
      default: 'Cancel'
    },
    uploadText: {
      type: String,
      default: 'Upload File'
    },
    uploadingText: {
      type: String,
      default: 'Uploading...'
    },
    onSuccess: {
      type: Function,
      default: () => {}
    },
    onError: {
      type: Function,
      default: () => {}
    },
    onCancel: {
      type: Function,
      default: () => {}
    },
    additionalData: {
      type: Object,
      default: () => ({})
    }
  })
  
  const emit = defineEmits(['update:modelValue', 'upload-complete', 'upload-error', 'upload-cancel'])
  
  // State
  const fileInput = ref(null)
  const selectedFile = ref(null)
  const uploading = ref(false)
  const uploadProgress = ref(0)
  const isDragging = ref(false)
  
  // Methods
  const triggerFileInput = () => {
    fileInput.value.click()
  }
  
  const handleFileChange = (event) => {
    selectedFile.value = event.target.files[0]
    emit('update:modelValue', selectedFile.value)
  }
  
  const handleUpload = async () => {
    if (!selectedFile.value) return
  
    uploading.value = true
    uploadProgress.value = 0
  
    const formData = new FormData()
    formData.append('file', selectedFile.value)
  
    // Add any additional data to the form
    Object.entries(props.additionalData).forEach(([key, value]) => {
      formData.append(key, value)
    })
  
    try {
      const response = await axios.post(
        props.uploadUrl,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          }
        }
      )
  
      if (response.data.success) {
        props.onSuccess(response.data)
        emit('upload-complete', response.data)
        resetUpload()
      } else {
        throw new Error(response.data.message)
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      props.onError(error)
      emit('upload-error', error)
    } finally {
      uploading.value = false
    }
  }
  
  const handleCancel = () => {
    resetUpload()
    props.onCancel()
    emit('upload-cancel')
  }
  
  const resetUpload = () => {
    selectedFile.value = null
    uploadProgress.value = 0
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
  
  const handleFileDrop = (event) => {
    isDragging.value = false
    const file = event.dataTransfer.files[0]
    if (file) {
      selectedFile.value = file
      emit('update:modelValue', file)
    }
  }
  </script> 
  