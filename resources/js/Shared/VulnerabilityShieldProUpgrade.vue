<template>
    <!-- Main component wrapper -->
    <div>
        <!-- Initial Upgrade Modal -->
        <Modal
            @close="closeUpgradeModal"
            :footer-button="false"
            :show="showUpgradeModal"
            :widthClass="'max-w-1050px'"
            :enable-heading="false"
            :title="null">

            <div class="flex flex-col justify-start gap-6 px-[32px] py-[19px] rounded-[8px]">
                <div class="flex flex-col gap-[24px]">
                    <div class="flex items-center justify-center w-full">
                        <img :src="nightMode ? asset('img/vulnerability_shield_pro.svg') : asset('img/vulnerability_shield_pro_light.svg')" alt="vulnerability-shield-pro" class="object-cover w-[100px] h-[100px]">
                    </div>
                    <p class="text-dark dark:text-white text-[24px] md:text-[32px] font-medium flex flex-col items-center justify-center">
                        <span>{{ $t('Vulnerability Shield') }} <span class="text-[#AFE614]">{{ $t('Pro') }}</span></span>
                    </p>
                    <div class="flex items-center justify-center">
                        <span class="text-[#A8ACBC] dark:text-[#A8ACBC] text-[16px] font-normal text-center">
                            {{$t('Enable Vulnerability Shield Pro to secure this site with automated virtual patching and faster notification alerts.')}}
                        </span>
                    </div>
                    <div class="flex flex-col gap-[32px]">
                        <div class="flex justify-center items-center gap-[24px]">
                            <button
                                @click="upgradeNow"
                                class="bg-[#147AFF] text-white text-[14px] md:text-[16px] font-semibold px-[20px] md:px-[32px] py-[8px] md:py-[12px] rounded-[8px]">
                                {{ isUpgradeModalRunning ? $t('Processing...') : $t('Pay now for') + ' $' + productPrice +'/'+ $t('mo')}}
                            </button>
                            <button
                                @click="closeUpgradeModal"
                                class="text-dark dark:text-white text-[14px] md:text-[16px] font-medium flex flex-col items-center justify-center border-b border-dark dark:border-white cursor-pointer">
                                {{$t('Continue free plan')}}
                            </button>
                        </div>
                        <div class="flex items-center gap-[8px] justify-center">
                            <input
                                type="checkbox"
                                id="terms"
                                name="terms"
                                v-model="doNotShowAgain"
                                class="w-[16px] h-[16px] bg-transparent border border-[#313A6C] focus:outline-none focus:ring-0 focus:ring-offset-0 rounded-sm">
                            <label for="terms" class="text-[#74778E] text-[14px] md:text-[16px] font-normal flex flex-col items-center justify-center">
                                {{$t('I am not interested at this moment. Please, do not show this message again.')}}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </Modal>

        <!-- Payment Invoice Modal -->
        <PayInvoiceModal
            :open-invoice-payment-modal="openInvoicePaymentModal"
            :invoice-payment-form="invoicePaymentForm"
            :handle-close-invoice-payment-modal="handleCloseInvoicePaymentModal"
            :handle-invoice-payment-modal="handleInvoicePaymentModal"
            :take-payment="takeInvoicePayment"
            :payment-methods="paymentMethodLists"
            :inactive-card-warning="inactiveCardWarning"
        />

        <!-- Payment Success Message -->
        <div v-if="paymentSuccess" class="fixed top-4 right-4 z-50">
            <div class="bg-success-full text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3">
                <i class="xcloud xc-check text-xl"></i>
                <div>
                    <p class="font-semibold">{{ $t('Payment Successful!') }}</p>
                    <p class="text-sm">{{ $t('Vulnerability Shield Pro has been activated.') }}</p>
                </div>
            </div>
        </div>

        <!-- Processing Indicator -->
        <div v-if="isProcessingPayment" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-mode-light rounded-lg p-8 flex flex-col items-center gap-4 max-w-md">
                <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary-light"></div>
                <p class="text-dark dark:text-white text-xl font-semibold">{{ $t('Processing Payment') }}</p>
                <p class="text-secondary-full dark:text-secondary-light text-center">{{ $t('Please wait while we activate Vulnerability Shield Pro for your site...') }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import Modal from "@/Shared/Modal.vue";
import PayInvoiceModal from "@/Pages/Profile/PayInvoiceModal.vue";
import { ref, computed } from "vue";
import { useForm } from "@inertiajs/inertia-vue3";
import { useFlash } from "@/Composables/useFlash";
import { useCreditCardIcon } from "@/Composables/useCreditCardIcon";
import axios from "axios";
import { Inertia } from "@inertiajs/inertia";
import { useNavigationStore } from "@/stores/NavigationStore";
const navigation = useNavigationStore();

const nightMode = computed(() => navigation.nightMode);

const props = defineProps({
    serverId: {
        type: [Number, String],
        required: true,
        default: ''
    },
    siteId: {
        type: [Number, String],
        required: true,
        default: ''
    },
    paymentMethods: {
        type: Object,
        default: () => ({})
    },
    defaultCard: {
        type: Object,
        default: () => ({})
    },
    productPrice: {
        type: Number,
        default: 5.0
    },
    initiallyOpen: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close', 'success', 'hidden']);

// State variables
const showUpgradeModal = ref(props.initiallyOpen);
const isUpgradeModalRunning = ref(false);
const openInvoicePaymentModal = ref(false);
const paymentSuccess = ref(false);
const isProcessingPayment = ref(false);
const doNotShowAgain = ref(false);
const patchstackVulnerability = ref(null);

// Form for payment
const invoicePaymentForm = useForm({
    invoice: null,
    paymentMethodId: props.defaultCard?.id,
});

// Methods for payment methods list
const paymentMethodLists = () => {
    let paymentMethods = [];
    for (let key in props.paymentMethods) {
        paymentMethods.push({
            value: props.paymentMethods[key].id,
            card_no: props.paymentMethods[key].card_no,
            expires_at: props.paymentMethods[key].expiry_month + '/' + props.paymentMethods[key].expiry_year,
            brand: props.paymentMethods[key].brand,
            icon: creditCardIcon(props.paymentMethods[key].brand),
            disabled: !(props.paymentMethods[key].status === 'active'),
            inactiveCard: !(props.paymentMethods[key].status === 'active')
        });
    }
    return paymentMethods;
};

const creditCardIcon = (brand) => {
    const { creditCardIcon } = useCreditCardIcon(brand);
    return creditCardIcon.value;
};

const inactiveCardWarning = () => {
    return '<i class="xcloud xc-warning text-warning"></i>';
};

// Open the upgrade modal
const openUpgradeModal = () => {
    showUpgradeModal.value = true;
};

// Close the upgrade modal
const closeUpgradeModal = () => {
    showUpgradeModal.value = false;
    emit('close');

    if (doNotShowAgain.value) {
        axios.post(
            route('api.site.hide.patchstack-vulnerability', [props.serverId, props.siteId]),
            { is_hide: doNotShowAgain.value }
        )
            .then(response => {
                emit('hidden', doNotShowAgain.value);
            })
            .catch(error => {
                emit('hidden', doNotShowAgain.value);
            });
    }
};

// Handle the upgrade now button click
const upgradeNow = () => {
    // Validate that we have valid server and site IDs
    if (!props.serverId || !props.siteId) {
        useFlash().error('Invalid server or site ID. Please try again.');
        return;
    }

    isUpgradeModalRunning.value = true;

    axios.post(route('api.site.patchstack-invoice', [props.serverId, props.siteId]))
        .then(response => {
            invoicePaymentForm.invoice = response.data;
            setTimeout(() => {
                isUpgradeModalRunning.value = false;
                showUpgradeModal.value = false;
                openInvoicePaymentModal.value = true;
            }, 150);
        })
        .catch(error => {
            isUpgradeModalRunning.value = false;
            showUpgradeModal.value = true;
            useFlash().error('Failed to generate invoice, please contact support.');
        });
};

// Handle closing the invoice payment modal
const handleCloseInvoicePaymentModal = () => {
    openInvoicePaymentModal.value = false;
    isProcessingPayment.value = false;
};

// Handle opening the invoice payment modal
const handleInvoicePaymentModal = () => {
    openInvoicePaymentModal.value = true;
};

// Process the invoice payment
const takeInvoicePayment = async () => {
    // Validate that we have valid server and site IDs
    if (!props.serverId || !props.siteId) {
        useFlash().error('Invalid server or site ID. Please try again.');
        return;
    }

    if (invoicePaymentForm.invoice?.status === 'paid') {
        useFlash().warning('This invoice is already paid.');
        return;
    }

    try {
        invoicePaymentForm.post(route('api.invoice.pay', [invoicePaymentForm.invoice?.invoice_number]), {
            preserveScroll: true,
            onSuccess: async (response) => {
                invoicePaymentForm.processing = true;
                const flash = response?.props?.jetstream?.flash;

                if (flash?.success) {
                    useFlash().success('Payment successful! Patchstack activation has started...');
                    openInvoicePaymentModal.value = false;

                    // Show processing indicator
                    isProcessingPayment.value = true;
                    paymentSuccess.value = true;

                    try {
                        // Validate server and site IDs again before making the API call
                        if (!props.serverId || !props.siteId) {
                            useFlash().error('Invalid server or site ID. Please try again.');
                            isProcessingPayment.value = false;
                            return;
                        }

                        // Call the Patchstack activation API
                        const response = await axios.post(route('api.site.enable.patchstack-vulnerability', [props.serverId, props.siteId]));
                        patchstackVulnerability.value = response.data.patchstack_vulnerability;

                        // Emit success event
                        emit('success', patchstackVulnerability.value);

                        // Redirect to vulnerability scan page
                        Inertia.visit(route('site.vulnerability-scan', { server: props.serverId, site: props.siteId }));

                    } catch (error) {
                        useFlash().warning('Payment successful! Patchstack activation starting failed.');
                        isProcessingPayment.value = false;
                    } finally {
                        invoicePaymentForm.processing = false;
                    }
                } else if (flash?.error) {
                    useFlash().error(flash.error);
                    invoicePaymentForm.processing = false;
                    isProcessingPayment.value = false;
                }
            },
            onError: () => {
                openInvoicePaymentModal.value = false;
                invoicePaymentForm.processing = false;
                isProcessingPayment.value = false;
            },
        });
    } catch (error) {
        openInvoicePaymentModal.value = false;
        invoicePaymentForm.processing = false;
        isProcessingPayment.value = false;
        useFlash().error('Payment failed.');
    }
};

// Expose methods to parent component
defineExpose({
    openUpgradeModal
});
</script>
