<template>
    <button
        @click="copy"
        type="button"
        class="right-30px mobile:right-15px inline-flex items-center font-normal shadow-none text-sm text-green-500 focus:outline-0 dark:hover:text-white/80"
        :class="`${align[props.align]} ${positionClass}`"
        aria-expanded="true" aria-haspopup="true">
        <i class="xcloud text-base mobile:text-sm mr-1" :class="copying ? 'xc-right' : 'xc-copy'"></i>
        <slot>
          <span
              class="wide-mobile:hidden"
              :class="{'hidden' : props.hideCopyText}"
          >
            Copy
          </span>
        </slot>
    </button>
</template>

<script setup>

import {ref} from "vue";
import {useClipBoard} from "@/Composables/useClipBoard.js";

let props = defineProps({
    content: String,
    align: {
        type: String,
        default: 'center'
    },
    hideCopyText: {
        type: Boolean,
        default: false
    },
    positionClass: {
        type: String,
        default: 'absolute'
    }
})

const align = {
    top: 'top-15px mobile:top-10px',
    middle: 'top-1/2 -translate-y-1/2'
}

let copying = ref(false);

let copy = () => {
    useClipBoard(props.content)

    copying.value = true;

    setTimeout(() => {
        copying.value = false;
    }, 1000)
}
</script>
