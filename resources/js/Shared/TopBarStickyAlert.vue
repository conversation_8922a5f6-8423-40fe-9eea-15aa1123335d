<template>
    <transition
        leave-to-class="opacity-0"
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0"
        enter-to-class="opacity-100"
        leave-active-class="transition ease-in duration-300"
        leave-from-class="opacity-100"
    >
        <div
            :class="'alert-'+type+' bg-'+type+'/20'"
            class="flex items-center px-6 py-4 rounded-md">
            <img :src="asset('img/warning.svg')" alt="warning_img" class="w-6"/>
            <p class="text-sm text-dark leading-7 pl-3.5">
                <slot></slot>
            </p>
        </div>
    </transition>
</template>

<script setup>
defineProps({
    type: {
        type: String,
        default: 'warning'
    }
})
</script>
