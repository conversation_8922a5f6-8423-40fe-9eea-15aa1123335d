<template>
    <div
        v-if="servers.length > 0"
        class="mb-10px small-laptop:mb-40px tablet:mb-30px wide-mobile:mb-20px"
    >
        <h5
            v-if="show_title"
            class="text-dark dark:text-white text-lg font-medium leading-none tablet:text-base mb-20px wide-tablet:mb-15px wide-mobile:mb-10px"
        >
            {{  $t('Reboot Required') }}
        </h5>
        <div v-for="(server,index) in servers" :key="index"
             class="flex flex-col gap-20px tablet:gap-15px wide-mobile:gap-10px mb-2"
        >
            <transition
                leave-to-class="opacity-0"
                enter-active-class="transition ease-out duration-300"
                enter-from-class="opacity-0"
                enter-to-class="opacity-100"
                leave-active-class="transition ease-in duration-300"
                leave-from-class="opacity-100"
            >
                <div class="warning flex flex-wrap gap-1 items-center justify-between bg-warning/20 px-6 py-4 rounded-md">
                    <div class="flex items-center">
                        <div class="flex items-center">
                        <img :src="asset('img/warning.svg')" alt="warning_img" class="w-6"/>
                        <p v-if="isAutomaticReboot(server)" class="text-dark dark:text-white leading-7 pl-3.5">
                            {{ $t('Security Update') }} - <Link class="underline" :href="route('server.security-update',server)"> <strong>{{server.name }}</strong> </Link> {{ $t('will automatically update and reboot on') }} <strong>{{convertTo12Hour(server?.reboot_time)}}, <span>{{server?.time_zone}}</span></strong>. {{ $t('You can also reboot now.') }}
                        </p>
                        <p v-else class="text-dark dark:text-white leading-7 pl-3.5">
                           {{ $t('Security Update - Server') }} <Link class="underline" :href="route('server.security-update',server)"> <strong>{{server.name }}</strong> </Link> {{ $t('requires a reboot.') }}
                        </p>
                    </div>
                    </div>
                    <div class="flex items-center gap-4">
                        <button @click.prevent="openModal(server)" class="h-30px inline-flex items-center justify-center rounded border-1 border-solid border-success-full shrink-0 px-3 py-1 cursor-pointer bg-success-full dark:bg-success-dark text-white text-sm font-medium leading-snug transition duration-150 ease-in-out">
                            {{ $t('Reboot Now') }}
                        </button>
                    </div>
                </div>
            </transition>
        </div>
    </div>
    <Modal
        :closeable="true"
        v-if="show_modal"
        :show="show_modal"
        :loading="loading"
        @close="closeModal"
        :title="$t('Reboot Required')"
        :footer-button="true"
        :widthClass="'max-w-950px'"
        @footer-click="rebootServer()"
        :footer-button-title="$t('Reboot')"
    >
        <div class="flex flex-col">
            <skeleton v-if="upgradeable_packages==null" :columns="1" :rows="10"/>
            <template v-else>
                <p class="text-dark dark:text-white py-2">
                    {{ $t('Security updates were automatically installed on your server, but a reboot is required to complete the installation. The following packages were upgraded:') }}
                </p>
                <textarea
                    v-if="packages"
                    class="dark:autofill:bg-mode-base placeholder-gray-400 dark:placeholder-gray-500
                        bg-white dark:bg-mode-light text-dark dark:text-white border-secondary-light text-sm
                        dark:border-mode-focus-light"
                    disabled
                    :rows="Math.min(upgradeable_packages.length/4,20)"
                >{{packages.trim()}}</textarea>
                <p class="text-dark dark:text-white pt-2">
                    {{ $t('Be sure to test the sites on your server after rebooting.') }}
                </p>
            </template>
        </div>
    </Modal>
</template>

<script setup>
import {Link} from "@inertiajs/inertia-vue3";
import {computed, ref} from "vue";
import Modal from "@/Shared/Modal.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import {useFlash} from "@/Composables/useFlash";

const {servers} = defineProps({
    servers: {
        type: Array,
        required: true,
    },
    show_title: {
        type: Boolean,
        default: true,
    }
});
const upgradeable_packages = ref(null);
const show_modal = ref(false);
const loading = ref(false);

const packages = computed(() => {
    const packagesList = upgradeable_packages.value;

    // Return null early if there's no data
    if (!packagesList) {
        return null;
    }
    // Use map directly without unnecessary optional chaining if you're sure about the data structure
    return packagesList
        .map(item => `${item.package} - ${item.version}`)
        .join('\n');
});

let selected_server = null;
const openModal = (server) => {
    selected_server = server;
   //get upgradeable packages
    axios.get(route('api.server.upgradeable.packages',{server:server?.id}))
        .then(({data}) => {
            upgradeable_packages.value = data;
            show_modal.value = true;
        })
        .catch(error => {
            console.log(error);
        });
}
const closeModal = () => {
    show_modal.value = false;
    upgradeable_packages.value = null;
    selected_server = null;
}

const rebootServer = () => {
    loading.value = true;
    //reboot server
    const { success, error } = useFlash();
    axios.post(route('api.server.upgrade.packages',{server:selected_server?.id}))
        .then(({data}) => {
            const message = data?.message;
            data?.type === 'success' ? success(message) : error(message);
        })
        .catch(error => {
            console.log(error);
        }).finally(() => {
            closeModal();
            loading.value = false;
        });
}
const editorInit = (editor) => {
    console.log('editor');
}

const isAutomaticReboot = (server) => {
    if('string' ==typeof server?.automatic_reboot){
        return server?.automatic_reboot === 'true';
    }
    return server?.automatic_reboot;
}
const convertTo12Hour = (time24) => {
    //check is time24 is valid
    if (!time24) {
        return '';
    }
    const [hours, minutes] = time24.split(':');
    let hours12 = ((+hours % 12) || 12).toString();
    let period = +hours < 12 ? 'AM' : 'PM';

    return `${hours12.padStart(2, '0')}:${minutes} ${period}`;
}
</script>
