<template>
    <div>
        <span class="flex items-center">
            <label
                v-if="label"
                class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none"
                :for="id"
                v-text="label"
            >
            </label>
            <span
                class="ml-auto text-secondary-full dark:text-mode-secondary-light hove:cursor-pointer"
                v-if="generateRandomValue"
                @click.prevent="generatePassword"
            >
                <tooltip class="cursor-pointer" :title="'Generate'">
                    <h6>
                        <i class="xcloud xc-settings"></i>
                    </h6>
                </tooltip>
            </span>
        </span>
        <div class="relative group">
            <div
                v-if="icon"
                class="flex absolute top-1/2 -translate-y-1/2 left-5 text-secondary-light dark:text-mode-secondary-light text-lg pointer-events-none"
                :class="{ 'group-focus-within:text-success-full': !readonly }"
            >
                <i :class="icon"></i>
            </div>
            <Tooltip class="w-full" :title="disabledNote" v-if="disabled" color="warning">
                <input
                    :id="id"
                    ref="input"
                    :autocomplete="autocomplete ?? id"
                    :placeholder="placeholder"
                    :disabled="disabled"
                    @blur="handleBlur"
                    class="input-style disabled:cursor-not-allowed placeholder:center !pr-20"
                    :class="{
                    '!border-danger !bg-danger/5': error,
                    'pl-60px': icon,
                    'pl-25px': !icon,
                    'white-button': whiteInput,
                    'white-password': whitePasswordInput,
                    'white-text': whiteInput,
                    'input-readonly': readonly,
                    'group-focus-within:bg-white dark:group-focus-within:bg-mode-light':
                        whitePasswordInput && !readonly,
                    'group-focus-within:border-success-full': !readonly,
                }"
                    :type="type"
                    :value="modelValue"
                    :readonly="readonly"
                    @input="$emit('update:modelValue', $event.target.value)"
                />

                <div v-if="showAddons && addons"
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                  <span class="text-gray-500 sm:text-sm" id="price-currency">
                    {{ modelValue + '.' + addons }}
                  </span>
                </div>
            </Tooltip>

            <input
                v-else
                :id="id"
                ref="input"
                :autocomplete="autocomplete ?? id"
                :placeholder="placeholder"
                :disabled="disabled"
                @blur="handleBlur"
                class="input-style disabled:cursor-not-allowed placeholder:center"
                :class="{
                    '!border-danger !bg-danger/5': error,
                    'pl-60px': icon,
                    'pl-25px': !icon,
                    'white-button': whiteInput,
                    'white-password': whitePasswordInput,
                    'white-text': whiteInput,
                    'input-readonly': readonly,
                    'group-focus-within:bg-white dark:group-focus-within:bg-mode-light':
                        whitePasswordInput && !readonly,
                    'group-focus-within:border-success-full': !readonly,
                    'pr-50px': isPassword,
                    'pr-25px': !isPassword,
                }"
                :type="type"
                :value="modelValue"
                :readonly="readonly"
                :required="required"
                @input="$emit('update:modelValue', $event.target.value)"
            />

            <div v-if="showAddons && addons"
                 class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <span class="text-gray-500 sm:text-sm" id="price-currency">
                  {{ addons }}
                </span>
            </div>

            <slot>

            </slot>
            <button
                class="top-1/2 -translate-y-1/2 absolute right-20px mobile:right-15px inline-flex items-center font-normal shadow-none text-sm text-black/50 dark:text-white/50 focus:outline-0 cursor-default"
                v-if="textOnRight"
                v-text="textOnRight"
            ></button>
        </div>

        <div class="flex items-center justify-between">
          <p
              v-if="note && !noteAsText"
              class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-15px"
              v-html="note"
          >
          </p>
          <p
              v-else
              class="text-sm text-secondary-full dark:text-mode-secondary-dark font-normal mt-15px"
              v-text="note"
          >
          </p>
          <slot name="note"></slot>
        </div>
        <div class="mt-2 text-base text-secondary-full dark:text-mode-secondary-dark font-normal" v-if="html" v-html="html"></div>
        <Error :error="error" />
    </div>
</template>

<script setup>

import {computed, nextTick, onMounted, ref, watch, watchEffect} from "vue";
import Error from "@/Shared/Error.vue";
import Tooltip from "@/Shared/Tooltip.vue";

const props = defineProps({
    id: String,
    styleType: {
        type: String,
        default: "whiteInput",
    },
    type: {
        type: String,
        default: "text",
    },
    generateRandomValue: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: "",
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    disabledNote: {
        type: String,
        default: '',
    },
    noteAsText: {
      type: Boolean,
      default: false
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    required: {
        type: Boolean,
        default: false,
    },
    textOnRight: {
        type: String,
        default: null,
    },
    icon: String,
    error: String,
    note: String,
    html: String,
    label: String,
    modelValue: [String, Number],
    autofocus: {
        type: Boolean,
        default: false
    },
    isPassword: {
        type: Boolean,
        default: false
    },
    autocomplete: {
        type: String,
        default: null
    },
    showAddons: {
        type: Boolean,
        default: false
    },
    addons: {
        type: String,
        default: null
    }
});

const emits = defineEmits(["update:modelValue", "handleBlur"]);

const input = ref(null)

onMounted(async () => {
  if(props.generateRandomValue){
    generatePassword();
  }
})

watch(() => props.autofocus, (newValue, oldValue) => {
    if (newValue === true) {
        nextTick(() => {
            input.value.focus();
        });
    }
});

function focus() {
    this.$refs.input.focus();
}

function select() {
    this.$refs.input.select();
}

function generatePassword() {
    // generate a 32 length random string
    if (props.generateRandomValue) {
        let randomString = "";
        while (randomString.length < 32) {
            randomString += Math.random().toString(36).substring(2);
        }
        randomString = randomString.substring(0, 32);

        emits("update:modelValue", randomString);
    }
}

const whiteInput = computed(() => {
    return props.styleType === "whiteInput";
});

const whitePasswordInput = computed(() => {
    return props.styleType === "whitePassword";
});

function setSelectionRange(start, end) {
    this.$refs.input.setSelectionRange(start, end);
}

function handleBlur() {
  // Emit a custom event when the input loses focus
  emits("input-blur");
}

</script>

<style scoped>
.input-style {
    @apply flex-1 block text-base font-normal min-h-60px py-2 border-1 w-full focus:outline-none min-w-0 rounded-md shadow-none focus:shadow-none focus:ring-0 outline-none appearance-none autofill:bg-light dark:autofill:bg-mode-base placeholder-gray-400 dark:placeholder-gray-500;
}

.white-password {
    @apply bg-light pr-50px dark:bg-mode-base text-dark dark:text-white border-secondary-light dark:border-mode-focus-light;
}
.white-text {
    @apply bg-white dark:bg-mode-light text-dark dark:text-white border-secondary-light dark:border-mode-focus-light;
}
.input-readonly {
    @apply bg-secondary-light/20 dark:bg-dark/20 border-secondary-light dark:border-mode-focus-light group-focus-within:border-secondary-light dark:group-focus-within:border-mode-focus-light  group-focus:bg-secondary-light/20 dark:group-focus:bg-dark/20;
}
.bg-transparent-input ::v-deep(.input-style) {
    background-color: transparent !important; /* Make background transparent */
}
</style>
