<template>
    <div class="relative mobile:static text-left flex" ref="events">
        <button type="button"
            @click="navigation.toggleEvents(navigation.headers.events)"
            class="analytics relative inline-flex items-center justify-center shadow-none h-12 tablet:h-10 w-6 shrink-0 focus:outline-none focus:border-none text-lg"
            :class="{'text-dark dark:text-white': navigation.headers.events, 'text-secondary-light': !navigation.headers.events }">
            <object v-if="taskEvents.executingEvents?.length" type="image/svg+xml" :data="asset('img/progress/Activity-L.svg')" class="h-4 cursor pointer-events-none"></object>
            <i v-else class="xcloud xc-analytics"></i>
            <div
                v-if="taskEvents.executingEvents?.length"
                class="absolute top-0 -right-1 h-1 w-1 p-2 bg-primary-dark dark:bg-primary-dark rounded-full flex justify-center items-center">
                <p class="text-xxs font-black text-white">{{ taskEvents.executingEvents?.length }}</p>
            </div>
        </button>
        <div
            v-if="navigation.headers.events"
            class="origin-top-right top-full absolute right-1/2 translate-x-1/2 w-96 rounded-lg shadow-lg bg-light dark:bg-dark focus:outline-none z-dropdown mt-1"
            role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
            <div class="px-5 py-3.5 flex items-center">
                <div class="text-base flex items-center text-dark dark:text-white mr-auto">
                    {{ $t('Recent Events') }}
                    <span class="inline-flex justify-center items-center leading-none px-1.5 py-1 min-w-5 rounded-full text-xs font-medium bg-primary-light text-white ml-2">
                        {{ taskEvents.total_events }}
                    </span>
                </div>
<!--                <button
                    class="flex text-xs text-secondary-light hover:text-dark dark:hover:text-white font-normal ml-2 whitespace-normal leading-none">
                    Close All
                </button>-->
                <button class="flex text-base text-secondary-light hover:text-dark dark:hover:text-white font-normal ml-2 whitespace-normal">
                    <i class="xcloud xc-settings"></i>
                </button>
            </div>
            <div class="rounded-b-lg py-1 bg-white dark:bg-mode-focus-dark divide-y divide-light dark:divide-mode-base max-h-100 flex flex-col">
                <template v-if="taskEvents.events.length > 0">
                    <!-- tasks -->
                     <div class="divide-y divide-light dark:divide-mode-base flex flex-col overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                        <div class="px-5 py-3.5 flex items-center" v-for="task in taskEvents.events" :key="task.id">
                            <div class="flex items-center shadow-none focus:outline-none focus:border-none text-base font-normal text-dark dark:text-white mr-1">
                                <div class="w-8 aspect-square shrink-0 rounded-full mr-2.5 flex items-center justify-center relative">
                                    <img
                                        v-if="task.team"
                                        :src="task.team.team_photo_url"
                                        :alt="task.team.name"
                                        :title="task.team.name"
                                        class="w-full aspect-square rounded-full">
                                    <img
                                        v-if="task.initiatedBy"
                                        class="w-3.5 h-3.5 shrink-0 object-cover rounded-full absolute bottom-0 right-0 border-1"
                                        :src="task.initiatedBy.profile_photo_url" :alt="task.initiatedBy.name"
                                        :title="task.initiatedBy.name"/>
                                </div>
                                <div class="flex flex-col gap-0.5">
                                    <span class="text-sm leading-tight">{{ task.name }}</span>
                                    <span class="text-xs text-secondary-full dark:text-secondary-light">
                                        <template v-if="task.site">
                                                {{ $t('Site') }}: <Link :href="route('site.redirect', task.site.id)">{{ task.site.name }}</Link>
                                        </template>
                                            <template v-else-if="task.server && task.server.name">
                                                {{ $t('Server') }}: <Link :href="route('server.show', task.server.id)">{{ task.server.name }}</Link>
                                        </template>
                                            <template v-else-if="task.server && task.server.id">
                                                {{ $t('Server') }}: {{ task.server.id}}
                                        </template>
                                    </span>
                                </div>
                            </div>
                            <div class="flex flex-col items-end text-end ml-auto gap-1">
                                <span class="text-xs text-secondary-full dark:text-secondary-light whitespace-nowrap">{{ task.created_at }}</span>

                                <div class="flex items-center gap-1.5">
                                    <button v-if="(task.status !== 'running' && user.role === 'user') || ['admin','super_admin'].includes(user?.role)" @click.prevent="showSingleEvent(task)" class="text-primary-light text-sm leading-none">{{ $t('View') }}</button>
                                    <span
                                        :title="task.status"
                                        class="h-5 aspect-square shrink-0 rounded-full text-xs flex items-center justify-center text-white" :class="{
                                            'bg-success-light' : task.status === 'finished',
                                            'bg-primary-light' : task.status !== 'finished' && task.status !== 'timeout',
                                            'bg-danger' : task.status === 'timeout' || task.status === 'failed',
                                        }">
                                        <i class="xcloud xc-spinner animate-spin" v-if="task.status === 'running'"></i>
                                        <i class="xcloud xc-right text-xxs" v-else-if="task.status === 'finished'"></i>
                                        <i class="xcloud xc-close-o" v-else></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center justify-center px-5 py-3.5">
                        <Link v-if="$page.props?.can_view_events" :href="route('user.all-events')" class="text-primary-light text-base">
                            {{ $t('View All Events') }}
                        </Link>
                    </div>
                </template>
                <div v-else class="px-5 py-3.5 flex">
                    <p class="w-full text-center text-sm my-1 text-dark dark:text-white leading-none">{{ $t('Currently No Event Available') }}</p>
                </div>
            </div>
        </div>
    </div>

    <Modal
        @close="openEventModal = false"
        :show="openEventModal"
        :footerButton="false"
        :widthClass="'max-w-244'"
        :title="modalTask.name">
        <div class="flex flex-col text-secondary-full dark:text-white leading-loose">
            <div class="" v-if="modalTaskIsLoading">
                <div class="flex justify-center items-center h-24">
                    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                </div>
            </div>
            <div v-else>
                <pre class="text-left whitespace-pre-wrap break-words text-xs border-b-1 mb-4 pb-4"
                     v-if="modalTask.script">{{ modalTask.script }}</pre>
                <pre class="text-left whitespace-pre-wrap break-words text-xs">{{ modalTask.output }}</pre>
            </div>
        </div>
    </Modal>
</template>

<script setup>
import {useNavigationStore} from "@/stores/NavigationStore.js";
import {useEventsStore} from "@/stores/EventStore";
import {userClickOutside} from "@/Shared/Events/userClickOutside.js";
import {onMounted, onUnmounted, ref, watch} from "vue";
import Modal from "@/Shared/Modal.vue"
import { usePage } from '@inertiajs/inertia-vue3';
import {asset} from "laravel-vapor";

const page = usePage();

let navigation = useNavigationStore();
let taskEvents = useEventsStore();

let openEventModal = ref(false)
let modalTask = ref({})
let modalTaskIsLoading = ref(false)

const events = ref(null);
userClickOutside(events, () => {
    navigation.toggleEvents(true);
});
const {user} = page.props.value;
onMounted(() => {
    taskEvents.fill();
    if (window.Echo && user) {
        console.log("joining channel", `team.task.${user.current_team_id}`, "and event TeamHasNewTask for events");
        window.Echo.private(`team.task.${user.current_team_id}`)
            .listen('TeamHasNewTask', (e) => {
                if (user?.current_team?.is_playground){
                    if (e?.task?.initiatedBy?.id === user?.id){ // @Todo: @Arif vai this is a hotfix, throws error on vulnerability scan I've added ? there
                        taskEvents.addNewEvent(e.task);
                    }
                } else if(Array.isArray(e?.users) && e?.users?.includes(user.id)){
                 taskEvents.addNewEvent(e.task);
               }
            });
    }
})

watch(() => navigation.current_team?.id, (n) => taskEvents.fill());


onUnmounted(() => {
    if (window.Echo && user) {
        console.log("leaving channel", `team.task.${user.current_team_id}`, "and event TeamHasNewTask for events");
        window.Echo.leave(`team.task.${user.current_team_id}`);
    }
})

const showSingleEvent = (task) => {
    openEventModal.value = true;
    modalTaskIsLoading.value = true;
    modalTask.value = task;
    axios.get(route('api.task.show', task.id)).then(response => {
        modalTaskIsLoading.value = false;
        modalTask.value = response.data;
    })
}
</script>
