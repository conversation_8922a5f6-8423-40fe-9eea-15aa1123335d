<template>
    <div class="relative mobile:static text-left flex" ref="notification">
        <button type="button"
            @click="navigation.toggleNotification(navigation.headers.notification)"
            class="notification relative inline-flex items-center justify-center shadow-none h-12 tablet:h-10 w-6 shrink-0 focus:outline-none focus:border-none text-lg after:absolute after:top-2 after:-right-0 after:h-2 after:w-2 after:rounded-full"
            :class="{'text-dark dark:text-white': navigation.headers.notification, 'text-secondary-light': !navigation.headers.notification, 'after:bg-success-light ': alert.total }">
            <i class="xcloud xc-notifications"></i>
        </button>
        <div v-if="navigation.headers.notification" class="origin-top-right top-full absolute right-1/2 translate-x-1/2 w-96 rounded-lg shadow-lg bg-light dark:bg-dark focus:outline-none z-dropdown mt-1" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
            <div class="px-5 py-3.5 flex items-center">
                <div class="text-base flex items-center text-dark dark:text-white mr-auto">
                    {{ $t('Notifications') }}
                    <span v-if="alert.total>0" class="inline-flex justify-center items-center leading-none px-1.5 py-1 min-w-5 rounded-full text-xs font-medium bg-primary-light text-white ml-2">
                        {{ alert.total }}
                    </span>
                </div>
                <button
                    @click="clearNotification"
                    class="flex text-xs text-secondary-light hover:text-dark dark:hover:text-white font-normal ml-2 whitespace-normal leading-none">
                    {{ $t('Mark all as read') }}
                </button>
                <Link @click="navigation.toggleNotification(navigation.headers.notification)" :href="route('user.notifications')" class="flex text-base text-secondary-light hover:text-dark dark:hover:text-white font-normal ml-2 whitespace-normal">
                    <i class="xcloud xc-settings"></i>
                </Link>
            </div>
            <div
                v-if="alert?.data?.length>0"
                @scroll="onScroll"
                class="rounded-b-lg py-1 bg-white dark:bg-mode-focus-dark divide-y divide-light dark:divide-mode-base max-h-100 overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                <div v-for="notification in alert.data" :key="notification.id" class="px-5 py-3.5 flex flex-col">
                    <div class="text-dark dark:text-white flex items-center text-base leading-tight">
                        <span class="mr-2">{{ notification?.meta?.message ?? notification.level }}</span>
                        <a v-if="notification.is_read" href="#" class="ml-auto text-secondary-light flex items-center">
                            <i class="xcloud xc-double_right"></i>
                        </a>
                    </div>
                    <div class="text-sm text-secondary-full dark:text-secondary-light">
                        {{ notification.type }}
                    </div>
                    <div class="text-dark dark:text-white flex items-center text-sm mt-1">
                        <Link v-if="notification?.meta?.url" :href="notification?.meta?.url" @click.prevent="clickNotification(notification)" class="text-primary-light flex items-center mr-2">
                            {{notification?.meta?.button ?? $t('View')}}
                        </Link>
                        <span class="ml-auto text-secondary-full dark:text-secondary-light text-xs">{{ notification.created_at }}</span>
                    </div>
                </div>
            </div>
            <div v-else class="rounded-b-lg py-1 bg-white dark:bg-mode-focus-dark divide-y divide-light dark:divide-mode-base max-h-100 flex flex-col">
                <div class="px-5 py-3.5 flex">
                    <p class="w-full text-center text-sm my-1 text-dark dark:text-white leading-none">
                        {{ $t('No Notifications Available!') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {useNavigationStore} from "@/stores/NavigationStore.js";
import {ref, onMounted, onUnmounted, watch} from "vue";
import {userClickOutside} from "@/Shared/Events/userClickOutside.js";
import axios from "axios";
import {usePage} from '@inertiajs/inertia-vue3';
import {Link} from "@inertiajs/inertia-vue3";
import {Inertia} from "@inertiajs/inertia";
import {useFlash} from "@/Composables/useFlash.js";

const page = usePage();

const alert = ref({
    total: 0,
    data: []
});

let navigation = useNavigationStore();

const {user} = page.props.value;

const notification = ref(null);
userClickOutside(notification, () => {
    navigation.toggleNotification(true);
});

//watch for current team id change, if change then fetch notification, if previous team id is null then don't fetch notification
watch(() => user.current_team_id, (newVal, oldVal) => {
    if (newVal !== null && oldVal !== null) {
        getNotifications();
    }
});


/*
 Load notification when component is mounted
 */
onMounted(() => {
    getNotifications();
    if (window.Echo && user) {
      //  console.log("joining channel", `team.alert.${user.current_team_id}`, "and event TeamHasNewAlert for notifications");
        window.Echo.private(`team.alert.${user.current_team_id}`)
            .listen('TeamHasNewAlert', (e) => {
                newNotification(e);
            });

        window.Echo.private(`user.alert.${user.id}`)
            .listen('TeamHasNewAlert', (e) => {
                newNotification(e);
            });
    }
});

function onScroll({target: {scrollTop, clientHeight, scrollHeight}}) {
    //check if scroll is at bottom
    if (scrollTop + clientHeight >= scrollHeight) {
        getNotifications();

    }

}

function showFlashMessage(meta) {
    if (meta.flash.type === 'success') {
        useFlash().success(meta.flash.message);
    } else if (meta.flash.type === 'error') {
        useFlash().error(meta.flash.message);
    } else {
        useFlash().info(meta.flash.message);
    }

}

const newNotification = (e) => {
    alert.value.total = alert.value.total + 1;
    //if already exist then update it
    if (e.alert.meta?.flash) showFlashMessage(e.alert.meta);

    let index = alert.value.data.findIndex((item) => item.id === e.alert.id);
    if (index !== -1) {
        alert.value.data[index] = e.alert;
    } else {
        alert.value.data.unshift(e.alert);
    }
};

/*
 Leave notification channel when component is unmounted
 */
onUnmounted(() => {
    if (window.Echo && user) {
        window.Echo.leave(`user.alert.${user.id}`);
        window.Echo.leave(`team.alert.${user.current_team_id}`);
    }
});

const currentPageUrl = ref(route('api.alerts', {page: 1}))

/*
 Get initial notifications
 */
const getNotifications = () => {
    //pagination
    if (currentPageUrl.value) {
        axios.get(currentPageUrl.value).then((response) => {
            alert.value.total = response.data.total;
            alert.value.data = alert.value.data.concat(response.data.data);
            currentPageUrl.value = response.data.next_page_url;
        });
    }
}

/*
 click the notification
 */
const clickNotification = (notification) => {
    if (!notification.is_read) {
        axios.put(route('api.alerts.view', notification.id)).then(response => {
            notification.is_read = true;
            alert.value.total = alert.value.total - 1;
        });
    }
    navigation.toggleNotification(true);
    //redirect to the url
    if (notification?.meta?.url) {
        Inertia.visit(notification?.meta?.url);
    }
}

/*
 Clear notification when user click on close all button
 */
const clearNotification = () => {
    axios.put(route('api.alerts.readAll')).then(response => {
        alert.value.data.map((notification) => {
            notification.is_read = true;
        });
        alert.value.total = 0;
    });

};

</script>
