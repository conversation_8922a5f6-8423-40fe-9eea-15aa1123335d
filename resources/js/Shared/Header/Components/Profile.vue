<template>
    <div
        class="relative flex text-left small-laptop:hidden"
        ref="profile"
    >
        <button
            v-if="navigation.current_team"
            type="button"
            @click="navigation.toggleProfile(navigation.headers.profile)"
            class="user-profile inline-flex items-center justify-center p-2 rounded-t-lg shadow-none h-12 min-w-12 focus:outline-none text-sm font-normal text-dark dark:text-white bg-light dark:bg-mode-base"
            :class="{ 'rounded-b-lg': !navigation.headers.profile }"
        >
            <div
                class="w-8 aspect-square shrink-0 rounded-full mr-2 flex items-center justify-center relative"
            >
                <template v-if="!$page?.props?.current_white_label">
                    <img
                        class="w-full aspect-square shrink-0 rounded-full flex items-center justify-center object-cover bg-light dark:bg-mode-base"
                        :src="navigation.current_team.team_photo_url"
                        alt=""
                    />
                    <img
                        class="w-4 aspect-square shrink-0 object-cover rounded-full absolute -bottom-0.5 -right-0.5 border-1 dark:border-1 dark:focus:border-1 dark:focus:border-solid dark:border-mode-base bg-light dark:bg-mode-base"
                        :src="user.profile_photo_url"
                        :alt="user.name"
                    />
                </template>
                <template v-else>
                    <img
                        class="w-full aspect-square shrink-0 rounded-full flex items-center justify-center object-cover bg-light dark:bg-mode-base"
                        :src="user.profile_photo_url"
                        :alt="user?.name"
                    />
                </template>
            </div>
            <template v-if="$page?.props?.current_white_label">
                <span class="inline-flex gap-2 items-center">
                    <span class="font-medium">{{ user.name }}</span>
                </span>
            </template>
            <template v-else>
                <span class="inline-flex gap-2 items-center">
                    <span class="font-medium">{{ navigation.current_team.name }}</span>
                    <i
                        v-if="$page?.props?.current_team?.active_plan"
                        class="xcloud xc-crown text-base inline-flex"
                        :class="{
                            'text-pro': $page?.props?.current_team?.active_plan.name !== 'free',
                            'text-gray-400 opacity-60': $page?.props?.current_team?.active_plan.name === 'free',
                        }"
                    ></i>

                </span>
            </template>
            <div
                class="h-4 aspect-square shrink-0 text-xxs flex items-center justify-center ml-2 text-secondary-light"
            >
                <i
                    v-if="navigation.headers.profile"
                    class="xcloud xc-angle_up"
                ></i>
                <i v-else class="xcloud xc-angle_down"></i>
            </div>
        </button>
        <div
            v-if="navigation.headers.profile"
            class="origin-top-right absolute top-full right-0 w-60 rounded-lg rounded-tr-none shadow-dropdown bg-light dark:bg-dark focus:outline-none z-header mt-1"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="menu-button"
            tabindex="-1"
        >
            <div class="py-2" role="none">
                <Link
                    :href="route('user.profile')"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="flex items-center px-5 py-2 text-sm"
                    :class="getActiveClass('Profile/UserProfile')"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-0"
                >
                    {{ $t('My Profile') }}
                </Link>
                <Link
                    v-if="!$page?.props?.current_white_label"
                    :href="route('user.team')"
                    class="flex items-center px-5 py-2 text-sm"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    :class="getActiveClass('TeamSettings')"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Team Settings') }}
                </Link>

                <Link
                    :href="route('user.vulnerability_scanner')"
                    class="flex items-center px-5 py-2 text-sm"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    :class="getActiveClass('Profile/Team/VulnerableSites')"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Vulnerable Sites') }}
                </Link>
                <Link
                    :href="route('user.bills-payment')"
                    class="flex items-center px-5 py-2 text-sm"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    :class="getActiveClass('Profile/BillsPayment')"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Bills & Payment') }}
                </Link>
                <a
                    :href="$page?.props?.current_white_label ? 'mailto:'+$page?.props?.current_white_label?.branding?.support_email : 'https://support.xcloud.host/'"
                    target="_blank"
                    @click="
                          navigation.toggleProfile(navigation.headers.profile)
                      "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Support') }}
                </a>
                <template v-if="!$page?.props?.current_white_label">
                <a
                    href="https://xcloud.host/docs/"
                    target="_blank"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Documentation') }}
                </a>
                <a
                    href="https://www.facebook.com/groups/xcloud.community"
                    target="_blank"
                    @click="
                    navigation.toggleProfile(navigation.headers.profile)
                "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Community') }}
                </a>
                <a
                    href="https://xcloud.host/affiliates/"
                    target="_blank"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="text-dark dark:text-white flex items-center px-5 py-2 text-sm hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Affiliates') }}
                </a>
                <a
                    v-if="user.role === 'admin' || user.role === 'super_admin' || user.role === 'support_level_1' || user.role === 'support_level_2'"
                    href="/admin"
                    target="_blank"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Admin Panel') }}
                </a>
                <a
                    v-if="user.role === 'admin' || user.role === 'super_admin'"
                    href="/reports"
                    target="_blank"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Reports') }}
                </a>
                <a
                    v-if="user.role === 'admin' || user.role === 'super_admin'"
                    href="/horizon"
                    target="_blank"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Horizon') }}
                </a>
                <a
                    v-if="user.role === 'admin' || user.role === 'super_admin'"
                    href="/telescope"
                    target="_blank"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Telescope') }}
                </a>
                <a
                    v-if="user.role === 'admin' || user.role === 'super_admin'"
                    href="/vapor-ui"
                    target="_blank"
                    @click="
                        navigation.toggleProfile(navigation.headers.profile)
                    "
                    class="flex items-center px-5 py-2 text-sm text-dark dark:text-white hover:text-white hover:bg-success-full"
                    role="menuitem"
                    tabindex="-1"
                    id="menu-item-1"
                >
                    {{ $t('Vapor UI') }}
                </a>
                </template>
                <div class="relative inline-block text-left w-full">
                    <!-- Language Selection Button -->
                    <div
                        ref="languageButton"
                        @click="toggleDropdown"
                        class="w-full flex items-center justify-between px-4 py-2 text-sm  text-dark dark:text-white cursor-pointer"
                    >
                        <div class="flex flex-row gap-3 items-center">
                            <!-- Flag of Selected Language -->
                            <RegionFlag
                                v-if="selectedLanguage?.region"
                                :region="selectedLanguage?.region"
                                image-container="fi-round"
                                classList="rounded-[10px]"
                                :showRegion="false"
                                :disableTooltip="true"
                            />
                            <span>{{ selectedLanguage?.name }}</span>
                        </div>
                        <!-- Dropdown Arrow Icon -->
                        <div
                            class="h-4 aspect-square shrink-0 bg-light dark:bg-mode-base text-xxxs flex items-center justify-center rounded text-secondary-light dark:text-white cursor-pointer"
                        >
                            <i
                                :class="[
                                    'xcloud xc-angle_down transition-transform duration-300 text-[#A8ACBC]',
                                    isDropdownOpen ? 'rotate-180' : 'rotate-0',
                                ]"
                            ></i>
                        </div>
                    </div>

           <!-- Dropdown Menu -->
              <div
               :class="[
                'absolute w-52 left-4 z-20 top-9 bg-light dark:bg-mode-base rounded-[4px] shadow-lg',
                'overflow-y-scroll max-h-[200px] custom-scrollbar',
                'transition-all duration-300 ease-in-out transform origin-top',
               isDropdownOpen
                ? 'opacity-100 scale-y-100 translate-y-3'
               : 'opacity-0 scale-y-95 -translate-y-5 pointer-events-none'
                     ]"
             style="box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);"
>
            <div class="py-2">
              <div
                       v-for="language in languages"
                       :key="language?.value"
                       @click="selectLanguage(language)"
                       class="flex items-center gap-3 px-3 py-1 leading-tight text-secondary-full  text-sm hover:text-white hover:bg-success-full cursor-pointer"
              >
                <!-- Flag in Dropdown for each language -->
                       <RegionFlag
                  v-if="language?.region"
                  :region="language?.region"
                  :showRegion="false"
                  image-container="fi-round"
                  classList=" rounded-[10px]"
                  :disableTooltip="true"
                />
                <span>{{ language?.name }}</span>
              </div>
           </div>
          </div>

        </div>
            </div>
            <div
                class="bg-white dark:bg-mode-focus-dark border-t-1 border-light dark:border-mode-base divide-y-1 divide-light dark:divide-mode-base rounded-b-md"
            >
                <template v-if="!$page?.props?.current_white_label">
                <label
                    class="text-mode-secondary-light dark:text-mode-secondary-light flex justify-center items-center text-sm text-center py-2"
                >
                    Default Team
                </label>
                <button
                    v-if="navigation.getPersonalTeam(user)"
                    type="button"
                    @click.prevent="
                        switchToTeam(navigation.getPersonalTeam(user))
                    "
                    class="flex w-full items-center justify-start px-5 py-2 shadow-none focus:outline-none text-sm font-normal text-dark dark:text-white relative"
                >
                    <div
                        class="w-7 aspect-square shrink-0 rounded-full mr-2 flex items-center justify-center relative"
                    >
                        <img
                            class="w-full aspect-square shrink-0 bg-light dark:bg-mode-base rounded-full flex items-center justify-center object-cover"
                            :src="
                                navigation.getPersonalTeam(user).team_photo_url
                            "
                            alt=""
                        />
                    </div>
                    <tooltip :title="navigation.getPersonalTeam(user).name">
                    <span class="max-w-36 truncate">{{ navigation.getPersonalTeam(user).name }}</span>
                    </tooltip>
                    <span
                        class="absolute right-20px top-1/2 -translate-y-1/2 h-15px w-15px shrink-0 rounded-full border-1 border-secondary-light bg-transparent text-xxxs rotate-0 origin-center text-white flex justify-center items-center"
                    ><i class="xcloud xc-done hidden"></i
                    ></span>
                    <span
                        v-if="
                            navigation.current_team.id ===
                            navigation.getPersonalTeam(user).id
                        "
                        class="absolute right-5 top-1/2 -translate-y-1/2 h-4 aspect-square shrink-0 rounded-full border-1 border-success-full bg-success-full text-xxxs text-white flex justify-center items-center"
                    >
                        <i class="xcloud xc-done"></i
                    ></span>
                </button>

                <label
                    v-if="
                        navigation.teams.find(
                            (t) => !navigation.isPersonalTeam(t, user)
                        )
                    "
                    class="text-mode-secondary-light dark:text-mode-secondary-light flex justify-center items-center text-sm text-center py-2">
                    Other Teams
                </label>
                <button
                    v-if="
                        navigation.current_team &&
                        !navigation.isPersonalTeam(
                            navigation.current_team,
                            user
                        )
                    "
                    type="button"
                    class="flex w-full items-center justify-start px-5 py-2 shadow-none focus:outline-none text-sm font-normal text-dark dark:text-white relative"
                >
                    <div
                        class="w-7 aspect-square shrink-0 rounded-full mr-2 flex items-center justify-center relative"
                    >
                        <img
                            class="w-full aspect-square shrink-0 bg-light dark:bg-mode-base rounded-full flex items-center justify-center object-cover"
                            :src="navigation.current_team.team_photo_url"
                            alt=""
                        />
                    </div>
                    <tooltip :title="navigation.current_team.name">
                    <span class="max-w-36 truncate">{{ navigation.current_team.name }}</span>
                    </tooltip>
                    <span
                        class="absolute right-5 top-1/2 -translate-y-1/2 h-4 aspect-square shrink-0 rounded-full border-1 border-success-full bg-success-full text-xxxs text-white flex justify-center items-center"
                        ><i class="xcloud xc-done"></i
                    ></span>
                </button>
                <div>
                    <div class="flex flex-col">
                        <button
                            type="button"
                            @click.prevent="switchToTeam(team)"
                            v-for="team in navigation.teams.filter(
                                (t) =>
                                    t.id !== navigation.current_team.id &&
                                    !navigation.isPersonalTeam(t, user)
                            )"
                            :key="team.id"
                            class="flex w-full items-center justify-start px-5 py-2 shadow-none focus:outline-none text-sm font-normal text-dark dark:text-white relative"
                        >
                            <div
                                class="w-7 aspect-square shrink-0 rounded-full mr-2 flex items-center justify-center relative"
                            >
                                <img
                                    class="w-full aspect-square shrink-0 bg-light dark:bg-mode-base rounded-full flex items-center justify-center object-cover"
                                    :src="team.team_photo_url"
                                    alt=""
                                />
                            </div>
                            <tooltip :title="team.name">
                            <span class="max-w-36 truncate">{{ team.name }}</span>
                            </tooltip>
                            <!--<span
                            class="absolute right-5 top-1/2 -translate-y-1/2 h-4 aspect-square shrink-0 rounded-full border-1 border-success-full bg-success-full text-xxxs text-white flex justify-center items-center"
                                ><i class="xcloud xc-done hidden"></i
                            ></span>-->
                            <span
                                class="absolute right-20px top-1/2 -translate-y-1/2 h-15px w-15px shrink-0 rounded-full border-1 border-secondary-light bg-transparent text-xxxs rotate-0 origin-center text-white flex justify-center items-center"
                            ><i class="xcloud xc-done hidden"></i
                            ></span>
                        </button>
                    </div>
                    <Link
                        :href="route('user.team.create')"
                        @click="
                            navigation.toggleProfile(navigation.headers.profile)
                        "
                        class="flex w-36 items-center justify-center px-2 py-1 mx-auto rounded-md border-1 border-primary-light dark:border-mode-base shadow-none focus:outline-none bg-transparent dark:bg-dark focus:bg-primary-light text-sm font-normal text-primary-light dark:text-white focus:text-white my-2"
                    >
                        <div class="text-xxxs mr-2 flex">
                            <i class="xcloud xc-add"></i>
                        </div>
                        <span>New Team</span>
                    </Link>
                </div>
                </template>
                <div
                    class="flex flex-col divide-y-1 divide-white dark:divide-mode-base"
                >
                    <slot name="nightmode"></slot>
                    <button v-if="!$page.props.impersonating"
                        type="button"
                        @click="logout"
                        class="flex w-full items-center justify-center bg-light dark:bg-dark px-5 py-2.5 shadow-none focus:outline-none focus:border-none text-sm font-normal text-secondary-full dark:text-mode-secondary-light hover:text-dark dark:hover:text-white rounded-b-lg"
                    >
                        <span>{{ $t('Logout') }}</span>
                    </button>
                    <button v-else
                        type="button"
                        @click="stopImpersonating"
                        class="flex w-full items-center justify-center bg-light dark:bg-dark px-5 py-2.5 shadow-none focus:outline-none focus:border-none text-xs font-normal text-secondary-full dark:text-mode-secondary-light hover:text-dark dark:hover:text-white rounded-b-lg"
                    >
                        <span>Stop Impersonating and return to {{ $page.props.impersonating_from }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {Inertia} from "@inertiajs/inertia";
import {useNavigationStore} from "@/stores/NavigationStore.js";
import {onMounted, onUnmounted, ref} from "vue";
import {userClickOutside} from "@/Shared/Events/userClickOutside.js";
import {Link, usePage} from "@inertiajs/inertia-vue3";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";
import {languagesData} from "@/Composables/LanguagesWithCountryName.js";
import {setWhiteLabelLocale} from "@/stores/i18n";
import Tooltip from "@/Shared/Tooltip.vue";


let navigation = useNavigationStore();

// States for language dropdown
const isDropdownOpen = ref(false);
const languages = ref(languagesData);
const userLanguage = usePage().props.value?.user_language || usePage().props.value?.current_white_label?.branding?.language || 'en';
const getSelectedLanguageRegion = (lang) => {
    return languagesData.find((l) => l.value === lang);
};

const selectedLanguage = ref(getSelectedLanguageRegion(userLanguage));

// Functions for language dropdown
const toggleDropdown = () => {
    isDropdownOpen.value = !isDropdownOpen.value;
};

const selectLanguage = (language) => {
    navigation.updateLanguage(language.value);
    selectedLanguage.value = language;
    axios.post(route('user.language'), {
        lang: language.value
    }).then(() => {
        window.location.reload();
    }).catch(error => {

    });
    isDropdownOpen.value = false;
};

const profile = ref(null);
userClickOutside(profile, () => {
    navigation.toggleProfile(true);
    isDropdownOpen.value = false;
});

const teams = ref([]);
const { user } = usePage().props.value;

/*
    Load user teams
 */
onMounted(() => {
    if (user) {
        navigation.getTeams();
        navigation.setCurrentTeam(user.current_team);

        if (window.Echo) {
            console.log(
                "joining channel",
                `App.Models.User.${user.id}`,
                "and event UserSwitchTeam and UserLeaveTeam for events"
            );
            window.Echo.private(`App.Models.User.${user.id}`)
                .listen("UserSwitchTeam", (e) => {
                    // add team to store
                    if (!navigation.teams.find((t) => t.id === e.team.id)) {
                        navigation.teams.push(e.team);
                    }
                    navigation.setCurrentTeam(e.team);
                })
                .listen("UserLeaveTeam", (e) => {
                    // remove team from store
                    navigation.setTeams(
                        navigation.teams.filter((t) => t.id !== e.team.id)
                    );
                });
        }
    }
});

onUnmounted(() => {
    if (window.Echo) {
        window.Echo.leave(`App.Models.User.${user.id}`);
    }
});

const switchToTeam = (team) => {

    isDropdownOpen.value = false;

    if (team.id === navigation.current_team.id) {
        return;
    }
    Inertia.put(
        route("current-team.update"),
        {
            team_id: team.id,
        },
        {
            preserveState: true,
            onSuccess: () => {
                user.current_team_id = team.id;
                navigation.setCurrentTeam(team);
                navigation.toggleProfile(navigation.headers.profile);

                setWhiteLabelLocale();
            },
        }
    );
};

function logout() {
    Inertia.post(
        route("logout"),
        {},
        {
            preserveState: true,
            onSuccess: () => {
                navigation.setCurrentTeam(null);
                navigation.setTeams([]);
                navigation.toggleProfile(navigation.headers.profile);
            },
        }
    );
}
function stopImpersonating() {
    Inertia.get(
        route("impersonation.stop")
    );
}

function getActiveClass(component) {
    return Inertia.page.component === component
        ? "text-white bg-success-full"
        : "text-dark dark:text-white hover:text-white hover:bg-success-full";
}
</script>

<style>
@import "flag-icons/css/flag-icons.min.css";
</style>
