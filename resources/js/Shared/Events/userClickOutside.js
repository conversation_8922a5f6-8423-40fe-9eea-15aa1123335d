import {onBeforeUnmount, onMounted} from "vue";

export function userClickOutside(elementRef, callback) {
    if (!elementRef) return;

    let listener = (e) => {
        if (e.target === elementRef.value || e.composedPath().includes(elementRef.value)) return;

        if (typeof callback === 'function') {
            callback();
        }
    }

    onMounted(() => {
        window.addEventListener('click', listener);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('click', listener);
    });

    return {
        listener
    }
}
