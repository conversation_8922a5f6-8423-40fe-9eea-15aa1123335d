<template>
    <div class="bg-white dark:bg-mode-light rounded-10px w-full">
        <div class="flex w-full">
            <div
                v-for="(tab, index) in tabs"
                :class="getParentDivClasses(index, active_tab, tab)"
                class="grow border-b-3 wide-mobile:border-b-light dark:wide-mobile:border-b-mode-base flex items-center
                        justify-center flex-col pt-30px pb-25px wide-mobile:pt-25px wide-mobile:pb-20px">
                <div
                    :class="getChildDivClasses(index, active_tab, tab)"
                    class="w-full p-3 wide-mobile:py-0 flex justify-center wide-mobile:z-1 wide-mobile:after:-z-1">
                    <span
                        :class="getSpanClasses(index, active_tab, tab)"
                        class="flex justify-center items-center h-50px w-50px wide-mobile:h-40px wide-mobile:w-40px
                            mobile:h-15px mobile:w-15px shrink-0 rounded-10px wide-mobile:rounded-full text-lg
                            wide-mobile:text-base mobile:-indent-20 overflow-hidden">
                        <i :class="getIcon(index, active_tab, tab)" class="xcloud"></i>
                    </span>
                </div>
                <span
                    class="mt-15px text-base text-dark dark:text-white font-normal leading-tight wide-mobile:hidden">
                    {{ tab.name }}
                </span>
            </div>
        </div>
        <div class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>

import { ref } from 'vue'

const props = defineProps({
    tabs : Object,
    previous_tab : String,
    active_tab : String,
    next_tab : String
})

const tab_style = {
    parent_div_completed_steps: 'border-b-primary-light',
    parent_div_active_step: 'border-b-primary-light',
    parent_div_pending_steps: 'border-b-light dark:border-b-mode-focus-light',
    child_div_step_arrow: 'relative after:absolute after:font-xc multi_step_arrow_content ' +
        'after:left-full wide-mobile:after:left-1/2 after:top-1/2 after:-translate-x-1/2 wide-mobile:after:translate-x-0 ' +
        'after:-translate-y-1/2 wide-mobile:after:w-full wide-mobile:after:h-0.5 after:text-xxs',
    child_div_arrow_active_and_pending_step: 'wide-mobile:after:bg-secondary-light/75 dark:wide-mobile:after:bg-mode-focus-light ' +
        'after:text-secondary-light',
    child_div_arrow_completed_steps: 'wide-mobile:after:bg-primary-light after:text-primary-light',
    parent_span_completed: 'bg-success-full text-white',
    parent_span_active: 'bg-primary-light text-white',
    parent_span_pending: 'bg-light dark:bg-mode-focus-light text-primary-light dark:text-white'
}

function isCompletedStep(index, active_tab, tab) {
    return index !== active_tab && tab.step_no < props.tabs[active_tab].step_no;
}

function isCurrentStep(index, active_tab, tab) {
    return index === active_tab;
}

function isPendingStep(index, active_tab, tab) {
    return index !== active_tab && tab.step_no > props.tabs[active_tab].step_no;
}

function isFinalStep(index, active_tab, tab) {
    return tab.step_no === tab.total_steps;
}

function getParentDivClasses(index, active_tab, tab) {
    if (isCompletedStep(index, active_tab, tab)) {
        return tab_style.parent_div_completed_steps;
    } else if (isCurrentStep(index, active_tab, tab)) {
        return tab_style.parent_div_active_step;
    } else if (isPendingStep(index, active_tab, tab)) {
        return tab_style.parent_div_pending_steps;
    }
}

function getChildDivClasses(index, active_tab, tab) {
    if (isCompletedStep(index, active_tab, tab)) {
        return tab_style.child_div_step_arrow + ' ' + tab_style.child_div_arrow_completed_steps;
    } else if (isFinalStep(index, active_tab, tab)) {
        return '';
    } else if (isCurrentStep(index, active_tab, tab)) {
        return tab_style.child_div_step_arrow + ' ' + tab_style.child_div_arrow_active_and_pending_step;
    } else if (isPendingStep(index, active_tab, tab)) {
        return tab_style.child_div_step_arrow + ' ' + tab_style.child_div_arrow_active_and_pending_step;
    }
}

function getSpanClasses(index, active_tab, tab) {
    if (isCompletedStep(index, active_tab, tab)) {
        return tab_style.parent_span_completed;
    } else if (isCurrentStep(index, active_tab, tab)) {
        return tab_style.parent_span_active;
    } else if (isPendingStep(index, active_tab, tab)) {
        return tab_style.parent_span_pending;
    }
}

function getIcon(index, active_tab, tab) {

    if (isCompletedStep(index, active_tab, tab)) {
        return 'xc-done';
    }

    return tab.icon;
}

</script>
