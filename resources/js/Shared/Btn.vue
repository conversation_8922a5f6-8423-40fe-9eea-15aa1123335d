<template>
    <button
        class="inline-flex items-center justify-center min-h-20 py-2 px-5 mobile:mt-4 rounded-md shadow-none text-base text-center text-white font-normal  hover:text-white ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30 disabled:cursor-not-allowed"
        :class="classes"
        :disabled="disabled"
        :type="type"
    >
        <slot name="before"></slot>
        <i class="xcloud xc-spinner mr-2.5 animate-spin" v-if="loading"></i>
        <i :class="icon" v-else-if="icon" class="mr-2.5"></i>
        <span class="drop-shadow-button"> <slot></slot> </span>
        <slot name="after"></slot>
    </button>
</template>
<script setup>
const props = defineProps({
  type: {
    type: String,
    default: 'button',
  },
  icon: {
    type: String,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  classes: {
    type: String,
    default: 'bg-success-full focus:outline-none hover:bg-success-full',
  }
})

</script>
