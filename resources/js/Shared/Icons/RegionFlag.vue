<template>
    <span v-if="region"
          class="cursor-pointer inline-flex">
        <Tooltip
            :title="region.country"
            align="top"
            :disabled="disableTooltip" 
        >
            <span :class="'self-center border border-solid ' + imageContainer + ' fi-' + region.country_code + ' ' + classList"></span>

            <template v-if="showRegion">
                {{ region.country_code?.toUpperCase() }}
            </template>
        </Tooltip>
    </span>
</template>

<script setup>

import Tooltip from "@/Shared/Tooltip.vue";

const props = defineProps({
    region: {
        type: Object,
        required: true,
    },
    showRegion: {
        type: Boolean,
        required: false,
        default: true,
    },
    classList: {
        type: String,
        required: false,
        default: 'mr-2',
    },
    imageContainer: {
        type: String,
        required: false,
        default: 'fi', // fi-round
    },
    disableTooltip: {  
        type: Boolean,
        required: false,
        default: false,
    }
});
</script>
