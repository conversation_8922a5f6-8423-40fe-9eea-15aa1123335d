<template>
    <MultiSelectCheckbox
        :options="keys"
        :selected="selected"
        :error="error"
        :emptyText="emptyText"
        :filterEmptyText="$t('No SSH Keys found.')"
        :label="$t('Choose SSH Keys')"
        :placeholder="$t('Search SSH Keys')"
    >
        <button type="button" v-if="can_create_ssh_key"
                @click="showSshModal = true"
                class="inline-flex items-center justify-center min-h-50px py-2 px-20px rounded-md shadow-none text-base text-center text-primary-light font-normal bg-primary-light/10 focus:outline-none hover:bg-primary-light hover:text-white ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-light/30">
            <span class="mr-2 text-xs inline-flex"><i class="xcloud xc-add"></i></span>
            <span class="drop-shadow-button">{{ $t('Add SSH Key') }}</span>
        </button>

        <Modal
            @footerClick="addSshKey"
            @close="showSshModal = false"
            :widthClass="'max-w-850px'"
            :show="showSshModal"
            :footer-button-title="$t('Add SSH Key')"
            :footer-button="true"
            :title="$t('Add SSH Key')">
            <div>
                <form @submit.prevent="addSshKey">

                    <input type="submit" class="hidden">

                    <text-input v-model="addKeyForm.key_name"
                                class="mb-5"
                                :error="addKeyForm.errors.key_name"
                                type="text"
                                :label="$t('Name')"
                                :placeholder="`${$page.props.auth_user.name}'s MacBook Pro`"/>

                    <textarea-input v-model="addKeyForm.public_key"
                                     :error="addKeyForm.errors.public_key"
                                     :placeholder="`ssh-rsa AAAAB3Nza....name@${$page.props.auth_user.name}s MacBook Pro`"
                                     :label="$t('Public Key')"/>
                </form>
            </div>
        </Modal>
    </MultiSelectCheckbox>
</template>
<script setup>
import Modal from "@/Shared/Modal.vue"
import TextInput from '@/Shared/TextInput.vue'
import TextareaInput from '@/Shared/TextareaInput.vue'
import MultiSelectCheckbox from "@/Shared/MultiSelectCheckbox.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import {ref} from "vue";

let emit = defineEmits(['newKeyAdded'])

let addKeyForm = useForm({
    key_name: '',
    public_key: '',
});

let props = defineProps({
    keys: Object,
    selected: Object,
    error: String,
    emptyText: String,
    can_create_ssh_key: {
        type: Boolean,
        default: true
    }
});

const showSshModal = ref(false);
const addSshKey = () => {
    addKeyForm.post(route('api.team.key.store'), {
        onSuccess(response) {
            showSshModal.value = false;
            if (response?.props?.jetstream?.flash?.data?.id) {
                emit('newKeyAdded', response?.props?.jetstream?.flash?.data)
            }

            addKeyForm.reset();
        }
    })
}

</script>
