<template>
    <label class="inline-flex grow cursor-pointer" :class="{'cursor-not-allowed' : disabled}" >
        <input
            type="radio"
            class="hidden peer disabled:cursor-not-allowed"
            :value="value"
            :checked="modelValue === value"
            @change="$emit('update:modelValue', $event.target.value)"
            :disabled="disabled"/>
        <span
            class="flex before:w-5 before:shrink-0 before:h-5 before:mr-2 before:mt-0.5 before:bg-transparent before:border before:border-secondary-light before:rounded-full before:inline-flex before:items-center before:justify-center before:outline-none peer-checked:before:bg-primary-light peer-checked:before:border-primary-light peer-checked:before:shadow-[0_0_0_0.25rem_#ffffff_inset] dark:peer-checked:before:shadow-[0_0_0_0.25rem_#1D2239_inset] peer-disabled:cursor-not-allowed text-dark peer-disabled:text-secondary-full dark:text-white dark:peer-disabled:text-secondary-full">
            <span class="text-base font-normal">
                <slot></slot>
            </span>
        </span>
    </label>
</template>

<script setup>
const props = defineProps({
    disabled: {
        type: <PERSON><PERSON>an,
        default: false,
    },
    modelValue: {
        default: ""
    },
    value: {
        type: [String,Boolean],
        default: undefined
    }
})

</script>
