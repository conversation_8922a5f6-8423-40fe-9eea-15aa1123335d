<template>
    <div class="relative" :class="{ 'w-full': isFull }">
        <button
            type="button"
            @click="showDropDown = !showDropDown"
            ref="dropDown"
            :class="getClasses()"
            class="inline-flex items-center justify-between rounded-10px border-light dark:border-mode-light shadow-none min-h-50px wide-mobile:min-h-40px bg-white dark:bg-mode-light text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 px-20px wide-mobile:px-15px gap-20px"
        >
            <span>{{ selectedItem }}</span>
            <span class="inline-flex text-xs text-secondary-light">
                <i
                    class="xcloud xc-angle_down"
                    :class="{ 'rotate-180': showDropDown }"
                ></i>
            </span>
        </button>
        <div
            class="absolute right-1/2 translate-x-1/2 top-full rounded-10px bg-white dark:bg-mode-focus-light focus:outline-none pb-0px shadow-dropdown shadow-dark/10 z-10"
            :class="`${customClass} ${!showDropDown ? 'hidden' : ''} ${isFull ? 'w-full' : 'w-40'}`"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="menu-button"
            tabindex="-1"
        >
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { ref } from "vue";
import { userClickOutside } from "@/Shared/Events/userClickOutside.js";

const props = defineProps({
    selectedItem: String,
    customStyle: {
        type: Boolean,
        default: false,
    },
    position: {
        type: String,
        default: "center",
    },
    customClass: String,
    isFull: {
        type: Boolean,
        default: false,
    },
});

const showDropDown = ref(false);
const dropDown = ref(null);

const getClasses = () => {
    return `${props.customClass ?? ''} ${
        props.customStyle
            ? "!border-1 !border-solid !border-light dark:!border-mode-base"
            : ""
    } ${
        props.isFull ? "w-full" : 'w-40'
    }`;
};

userClickOutside(dropDown, () => {
    showDropDown.value = false;
});
</script>
