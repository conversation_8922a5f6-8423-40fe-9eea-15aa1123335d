<template>
  <footer class="bg-light dark:bg-mode-base" v-if="$page.props.auth_user?.name">
    <div class="xc-container">
        <div
        class="flex items-center justify-end gap-2 min-h-14 py-2 border-t border-focused dark:border-mode-focus-light"
        >
        <div>
            <p class="text-sm text-secondary-full dark:text-mode-secondary-light">
                <span v-if="currentWhiteLabel && currentWhiteLabel?.branding?.brand_name">{{ currentWhiteLabel?.branding?.brand_name }}</span>
                <span v-else>xCloud</span>

                <a v-if="!$page?.props?.current_white_label" class="text-primary-light dark:text-white hover:underline ml-1" target="_blank" href="https://xcloud.host/changelog/">
                    {{ page?.props?.version }}
                </a>
            </p>
        </div>
        <div>
            <p class="text-sm text-secondary-full dark:text-mode-secondary-light">
            {{ $t('Copyright') }} © 2025 |
            <span v-if="currentWhiteLabel" class="text-primary-light dark:text-white">{{ currentWhiteLabel?.branding?.copyright_name }}</span>
            <span v-else class="text-primary-light dark:text-white">xCloud Hosting LLC.</span>
            {{ $t('All rights reserved.') }}
            </p>
        </div>
        </div>
    </div>
  </footer>
</template>


<script setup>
import {ref} from "vue";
import {usePage} from "@inertiajs/inertia-vue3";

const props = defineProps({
  page: Object,
});

const currentWhiteLabel = ref(usePage().props.value?.current_white_label);
</script>
