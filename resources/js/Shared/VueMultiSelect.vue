<template>
    <div class="add-member-multiselect">
        <Multiselect
            v-model="selected"
            mode="tags"
            :close-on-select="false"
            :searchable="true"
            :create-option="false"
            placeholder="Select here..."
            :options="options"
            @select="onTagSelect"
        />
    </div>
</template>

<script setup>
import Multiselect from "@vueform/multiselect";

const props = defineProps({
    options: {
        type: Array,
        default: () => [],
    },
    selected: {
        type: Array,
        default: () => [],
    },
});
const onTagSelect = (selected) => {
    console.log(selected);
};
</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
.multiselect {
    margin-left: 0;
}
</style>
