<template>
    <footer class="xc-container">
        <div class="flex items-center justify-end gap-8 min-h-50px py-4 border-t border-light dark:border-mode-focus-light">
            <div>
                <p class="text-base text-secondary-full dark:text-[#919DB9]">xCloud <a class="text-primary-light dark:text-white hover:underline" href="#">v1.2.2</a></p>
            </div>
            <div>
                <p class="text-base text-secondary-full dark:text-[#919DB9]">Copyright © 2024 | <span class="text-primary-light dark:text-white">xCloud Hosting LLC.</span> All rights reserved.</p>
            </div>
        </div>
    </footer>
</template>


<script setup>

    const props = defineProps({

    })

</script>
