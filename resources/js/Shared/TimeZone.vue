<template>
    <div>
        <label
            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-dark leading-none">
            {{ $t('Time Zone') }}
        </label>
        <div class="group">
            <select :disabled="disabled" name="name" :value="modelValue" @input="$emit('update:modelValue', $event.target.value)"
                    class="bg-white dark:bg-dark text-base font-normal text-dark dark:text-white min-h-60px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light group-focus-within:border-success-full w-full focus:outline-none min-w-0 rounded-md autofill:bg-light dark:autofill:bg-mode-base">
                <option v-for="timezone in timezone_list" :value="timezone.zone">{{ timezone.zone }}
                    {{ timezone.diff_from_GMT }}
                </option>
            </select>
            <div class="text-red-500" v-if="error">{{ error }}</div>
        </div>
    </div>
</template>

<script setup>
import Tooltip from "@/Shared/Tooltip.vue";

defineProps({
    'timezone_list': Array,
    'modelValue':{
        type: String,
        default: 'Africa/Abidjan'
    },
    'error': String,
    'disabled': Boolean
})

defineEmits(['update:modelValue'])



</script>
