<template>
    <div
        class="text-secondary-light dark:text-secondary-full  hover:text-primary-dark dark:hover:text-white basis-60px text-xs flex items-center justify-center cursor-pointer"
        @click="showDropDown = !showDropDown"
        ref="dropDown"
    >
        <div class="relative flex">
            <button type="button"
                    v-if="title"
                    ref="dropDown"
                    class="inline-flex items-center rounded-10px border-light dark:border-mode-light shadow-none min-h-50px wide-mobile:min-h-40px bg-light dark:bg-mode-base text-base wide-mobile:text-sm text-dark dark:text-white focus:outline-0 px-20px wide-mobile:px-15px gap-20px">
                <span class="capitalize">{{title}}</span>
                <span class="inline-flex text-xs text-secondary-light">
                <i class="xcloud xc-angle_down"
                   :class="{'rotate-180': showDropDown}"
                ></i>
            </span>
            </button>

            <button
                v-else
                type="button" class="inline-flex items-center justify-center text-xl">
                 <i :class="icon"></i>
            </button>
            <div
                class="absolute z-[999] top-[calc(theme(width.full)+theme(spacing.10px))] w-52 rounded-b-10px bg-white py-10px rounded-10px dark:bg-mode-focus-light focus:outline-none shadow-dropdown shadow-dark/10"
                :class="{'hidden': !showDropDown,'translate-x-1/2 right-1/2': position==='center','-left-10px': position==='left','-right-10px': position==='right'}"
                role="menu" aria-orientation="vertical" aria-labelledby="menu-button"
                tabindex="-1">
                <slot name="items"></slot>
            </div>
        </div>
    </div>
</template>

<script setup>

import {ref, watch} from "vue";
import {userClickOutside} from "@/Shared/Events/userClickOutside.js";

const props = defineProps({
    position: {
        type: String,
        default: 'center'
    },
    icon : {
        type: String,
        default: 'xcloud xc-menu-vertical'
    },
    title: String
});
const emit = defineEmits(['onChangeDropDown']);
const showDropDown = ref(false);
const dropDown = ref(null);

userClickOutside(dropDown, () => {
    showDropDown.value = false;
});

watch(showDropDown, (value) => {
   emit('onChangeDropDown', value);
});

</script>
