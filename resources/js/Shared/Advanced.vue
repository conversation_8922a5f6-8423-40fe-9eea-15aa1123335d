<template>
    <div class="flex flex-col items-start mt-30px mobile:mt-20px">
        <button
            id="accordion_button"
            @click.prevent="showingAdvance = !showingAdvance"
            :class="{
                'rounded-tl-md rounded-tr-md': showingAdvance,
                rounded: !showingAdvance,
                'accordion_button': accordion_button,
            }"
            class="inline-flex items-center justify-center border-1 border-primary-light dark:border-dark
                   shadow-none min-h-40px pl-20px pr-15px bg-primary-light dark:bg-dark text-base font-normal text-white
                   focus:outline-none transition duration-200">
              {{ title }}
            <span
                class="text-xxs text-white flex ml-15px transition duration-200"
                :class="{ '-rotate-180': showingAdvance }">
                <i class="xcloud xc-angle_down"></i>
            </span>
        </button>
        <div
            class="w-full overflow-hidden origin-top-right transition-all duration-100 ease-in-out"
            :style="setOffsetHeight">
            <div
                class="p-30px mobile:p-20px rounded-md rounded-tl-none bg-white dark:bg-mode-base border-1 border-primary-light
                       dark:border-mode-base w-full"
                ref="contentHeight">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, onUpdated, ref, watch } from "vue";
const emit = defineEmits(["showAdvance"]);
let showingAdvance = ref(false);
let refresh = ref(false);
let contentHeight = ref(null);

const props = defineProps({
    title: {
      type: String,
      default: 'More Advanced Settings'
    },
    accordion_button: {
        type: Boolean,
        default: true,
    },
})

//watching showingAdvance
watch(showingAdvance, (value) => {
    emit("showAdvance", value);
});

onUpdated(() => {
    refresh.value = showingAdvance.value &&
        parseInt(setOffsetHeight.value.height) !==
        contentHeight?.value?.offsetHeight + 2;
});

const setOffsetHeight = computed(() => {
    let height;
    if (showingAdvance.value && refresh.value) {
        height = { height: contentHeight?.value?.offsetHeight + 2 + "px" };
    } else height = { height: 0 + "px" };
    refresh.value = false;
    return height;
});
</script>
