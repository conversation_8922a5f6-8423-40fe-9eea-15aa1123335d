<template>
    <div class="inline-flex items-center gap-0.5 text-sm">
        <template v-for="i in 5">
            <i class="xcloud text-pro"
               :class="classes(i)"
            ></i>
        </template>
    </div>
</template>

<script setup>

const {rating} = defineProps({
    rating: {
        type: Number,
        default: 0
    }
})
//3.5
const classes = (index) => {
   if (rating >= index) {
       return 'xc-star-full'
   } else if (rating < index && rating > index - 1) {
       return 'xc-star-half'
   } else {
       return 'xc-star-line'
   }
}

</script>
