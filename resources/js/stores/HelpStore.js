import {defineStore} from 'pinia'

export const useHelpStore = defineStore({
    id: 'helper',
    state: () => ({
        isOpen: JSON.parse(localStorage.getItem('isOpen')) ?? false,
        hasHelper: false,
        title: null,
        content: '',
        active_tag: null, //accordions: [{title: 'title', content: 'content'}],
        accordions: [],
        video: null, //documentations: [{title: 'title', url: 'url'}],
        documentations: [],
    }),
    getters: {},
    actions: {
        toggleHelpBar(state) {
            // this.isOpen = !state;
            this.isOpen = !state;
            localStorage.setItem('isOpen', this.isOpen);
            // console.log('toggle helper value: ' + this.isOpen)
        }, setAccordions(accordions, filterTags = []) {
            this.hasHelper = true;
            this.accordions = accordions.filter((accordion) => {
                return !filterTags.includes(accordion.tag);
            });
        }, toggleAccordion(tag) {
            if (tag === this.active_tag) {
                this.active_tag = null;
            } else {
                this.active_tag = tag;
            }
        }, toggleAccordions(tags = [],open_withs =[]) {
            if (this.accordions.length > 0) {
                const accordions = this.accordions.map((accordion) => {
                    if (tags.length > 0) {
                        return tags.includes(accordion.tag) ? {...accordion, depends: !accordion.depends} : accordion;
                    }
                    if ( accordion.hasOwnProperty('depends') && !accordion.hasOwnProperty('open_with')){
                        return {...accordion, depends: !accordion.depends};
                    }else if ( accordion.hasOwnProperty('depends') && accordion.hasOwnProperty('open_with')){
                        return {...accordion, depends: !open_withs.includes(accordion.open_with)};
                    }
                    return accordion;
                });
                this.setAccordions(accordions);
            }
        }, toggleOpenWithAccordions(tags = [],open_withs =[]) {
            if (this.accordions.length > 0) {
                const accordions = this.accordions.map((accordion) => {
                   if (accordion.hasOwnProperty('open_with')){
                        return {...accordion, depends: !open_withs.includes(accordion.open_with)};
                    }
                    return accordion;
                });
                this.setAccordions(accordions);
            }
        }, setVideo(video) {
            this.hasHelper = true;
            this.video = video;
        }, setDocumentations(documentations) {
            this.hasHelper = true;
            this.documentations = documentations;
        }, setHelpBarContent(title, content) {
            // console.log('set content called')
            // this.isOpen = true;
            this.hasHelper = true;
            this.title = title;
            this.content = content;
        }, setHelpers(helpers, doOpen = true, forget = false) {
            // this.isOpen = !this.isOpen;
            this.hasHelper = true;
            this.accordions = helpers?.accordions ?? forget ? [] : this.accordions;
            this.video = helpers?.video ?? null ?? forget ? null : this.video;
        }, resetHelpers() {
            this.hasHelper = false;
            this.title = null;
            this.content = '';
            this.accordions = [];
            this.video = null;
            this.documentations = null;
            // this.toggleHelpBar(true);
        }
    }
})
