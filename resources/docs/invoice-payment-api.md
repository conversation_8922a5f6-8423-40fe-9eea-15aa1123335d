# Invoice Payment API Documentation

This document outlines how to use the Invoice Payment API for developers.

## Pay Invoice Endpoint

### Request

- **URL**: `/api/invoice/{invoice}/pay`
- **Method**: `POST`
- **Authorization**: <PERSON><PERSON> required
- **Content-Type**: `application/json`

### Request Body

```json
{
  "paymentMethodId": "payment_method_id_here"
}
```

### Response Examples

#### 1. Success Response

```json
{
  "message": "Payment successfully processed.",
  "type": "success",
  "data": {
    // Invoice data
  }
}
```

#### 2. Already Paid Response

```json
{
  "message": "Invoice already paid!",
  "type": "success",
  "data": {
    // Invoice data
  }
}
```

#### 3. Manually Paid Response

```json
{
  "message": "Invoice already paid manually. Please contact support if you need any assistance.",
  "type": "success",
  "data": {
    // Invoice data
  }
}
```

#### 4. 3D Secure Authentication Required

```json
{
  "message": "Payment requires 3D secure authentication",
  "type": "warning",
  "data": {
    // Invoice data
  },
  "redirect": "https://example.com/stripe/checkout/requires3dsecure?paymentIntentId=..."
}
```

#### 5. Payment Failed Response

```json
{
  "message": "Error message from payment processor. Payment failed, please try again with a different payment method.",
  "type": "error",
  "data": {
    // Invoice data
  }
}
```

#### 6. Minimum Amount Error

```json
{
  "message": "Invoice amount is less than $0.50. Please contact support if you need any assistance.",
  "type": "error",
  "data": {
    // Invoice data
  }
}
```

## Implementation Notes

1. Always check the `type` field in the response to determine the outcome
2. For 3D secure payments, redirect the user to the URL provided in the `redirect` field
3. The `data` field contains the updated invoice information