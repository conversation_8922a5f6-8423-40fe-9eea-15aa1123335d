# Invoice Payment Redirect Implementation

## Overview
How to redirect users to the invoice payment page and handle return redirects with parameters.

## Basic Usage

1. Redirect to invoice payment URL:
```
/invoice/{invoice_number}/pay
```

2. With redirect URL:
```
/invoice/{invoice_number}/pay?redirect=/dashboard
```

## Passing Additional Parameters

Any additional query parameters will be preserved and passed to the redirect URL:

```
/invoice/{invoice_number}/pay?redirect=/dashboard&status=success
```

After payment, user will be redirected to:
```
/dashboard?status=success
```

## Multiple Parameters Example

```
/invoice/INV-12345/pay?redirect=/orders/789&ref=order-completion&source=checkout
```

After payment, user will be redirected to:
```
/orders/789?ref=order-completion&source=checkout
```

## Implementation Details

The payment system handles:
- Capturing the redirect URL from the `redirect` parameter
- Preserving other query parameters
- Passing all parameters to the final redirect URL after payment