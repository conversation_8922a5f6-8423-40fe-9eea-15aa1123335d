@if($file)
#make storage directory
mkdir -p /var/www/{{$site->name}}/bootstrap/cache
mkdir -p /var/www/{{$site->name}}/storage/logs \
mkdir -p /var/www/{{$site->name}}/storage/framework/cache \
mkdir -p /var/www/{{$site->name}}/storage/framework/sessions \
mkdir -p /var/www/{{$site->name}}/storage/framework/views \
# Run after backup restore
if [ ! -f /var/www/{{ $site->name }}/.env ]; then
    @include('scripts.laravel.updateEnv',[ 'site' => $site, 'createConfig' => true ])
fi

# Set proper ownership
chown -R {{ $site->site_user }}:{{ $site->site_user }} /var/www/{{ $site->name }}

# Set proper permissions
chmod -R 775 /storage /bootstrap/cache
@endif
@includeWhen($site->shouldRunDeployScript(), 'scripts.site.deployScript', ['site' => $site, 'script' => $site->getMeta('deploy_script') ])
