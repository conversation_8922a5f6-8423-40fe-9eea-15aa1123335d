# -----------
# Update Nginx Conf
# -----------

if grep client_max_body_size /etc/nginx/nginx.conf; then
    sudo sed -i '/client_max_body_size/d' /etc/nginx/nginx.conf
fi

if grep fastcgi_read_timeout /etc/nginx/nginx.conf; then
    sudo sed -i '/fastcgi_read_timeout/d' /etc/nginx/nginx.conf
fi

largest_filesize=0
largest_filesize_file=''
largest_input_time=-1
largest_input_time_file=''

# Loop through each PHP version directory
for php_dir in /etc/php/*; do
    php_version=$(basename "$php_dir")
    php_ini="${php_dir}/fpm/php.ini"

    if [[ -f "$php_ini" ]]; then
        # Extract values
        upload_max_filesize=$(grep -Po '(?<=upload_max_filesize = )\d+' "$php_ini")
        post_max_size=$(grep -Po '(?<=post_max_size = )\d+' "$php_ini")
        max_input_time=$(grep -Po '(?<=max_input_time = )-?\d+' "$php_ini") # -1 is a valid value, meaning no limit
        max_execution_time=$(grep -Po '(?<=max_execution_time = )-?\d+' "$php_ini") # -1 is a valid value, meaning no limit

        # Determine the largest filesize between upload_max_filesize and post_max_size
        current_largest_filesize=$upload_max_filesize
        if [[ "$post_max_size" != "" ]] && (( post_max_size > upload_max_filesize )); then
            current_largest_filesize=$post_max_size
        fi

        # Check and update largest filesize
        if (( current_largest_filesize > largest_filesize )); then
          largest_filesize=$current_largest_filesize
          largest_filesize_file=$php_ini
        fi

        # Determine the largest time between max_input_time and max_execution_time
        current_largest_time=$max_input_time
        if [[ "$max_execution_time" != "" ]] && (( max_execution_time > max_input_time )); then
            current_largest_time=$max_execution_time
        fi

        # Check and update largest input time
        if (( current_largest_time > largest_input_time )); then
          largest_input_time=$current_largest_time
          largest_input_time_file=$php_ini
        fi
    fi

    # Check pool configuration files
    pool_dir="${php_dir}/fpm/pool.d"
    if [[ -d "$pool_dir" ]]; then
        for pool_file in "$pool_dir"/*.conf; do
            # echo "Checking pool file: $pool_file"
            if [[ -f "$pool_file" ]]; then
                # Extract time values from pool files, if defined
                pool_input_time=$(grep -Po '(?<=php_admin_value\[max_input_time\]=)-?\d+' "$pool_file")
                pool_execution_time=$(grep -Po '(?<=php_admin_value\[max_execution_time\]=)-?\d+' "$pool_file")

                # Determine the largest time between pool_input_time and pool_execution_time
                pool_largest_time=$pool_input_time
                if [[ "$pool_execution_time" != "" ]] && [[ "$pool_input_time" != "" ]] && (( pool_execution_time > pool_input_time )); then
                    pool_largest_time=$pool_execution_time
                elif [[ "$pool_execution_time" != "" ]] && [[ "$pool_input_time" == "" ]]; then
                    pool_largest_time=$pool_execution_time
                fi

                if [[ "$pool_largest_time" != "" ]] && (( pool_largest_time > largest_input_time )); then
                    largest_input_time=$pool_largest_time
                    largest_input_time_file=$pool_file
                fi

                # Extract filesize values from pool files, if defined
                pool_upload_filesize=$(grep -Po '(?<=php_admin_value\[upload_max_filesize\]=)\d+M' "$pool_file" | grep -Po '\d+')
                pool_post_size=$(grep -Po '(?<=php_admin_value\[post_max_size\]=)\d+M' "$pool_file" | grep -Po '\d+')

                # Determine the largest filesize between pool_upload_filesize and pool_post_size
                pool_largest_filesize=$pool_upload_filesize
                if [[ "$pool_post_size" != "" ]] && [[ "$pool_upload_filesize" != "" ]] && (( pool_post_size > pool_upload_filesize )); then
                    pool_largest_filesize=$pool_post_size
                elif [[ "$pool_post_size" != "" ]] && [[ "$pool_upload_filesize" == "" ]]; then
                    pool_largest_filesize=$pool_post_size
                fi

                if [[ "$pool_largest_filesize" != "" ]] && (( pool_largest_filesize > largest_filesize )); then
                    largest_filesize=$pool_largest_filesize
                    largest_filesize_file=$pool_file
                fi
            fi
        done
    fi
done

echo "Largest filesize (upload_max_filesize/post_max_size): ${largest_filesize}M in ${largest_filesize_file}"
echo "Largest time (max_input_time/max_execution_time): ${largest_input_time} in ${largest_input_time_file}"

echo "client_max_body_size ${largest_filesize}M;" > /etc/nginx/conf.d/uploads.conf
echo "fastcgi_read_timeout $largest_input_time;" > /etc/nginx/conf.d/timeout.conf

echo "client_max_body_size ${largest_filesize}M in /etc/nginx/conf.d/uploads.conf"
echo "fastcgi_read_timeout $largest_input_time in /etc/nginx/conf.d/timeout.conf"

for php_dir in /etc/php/*; do
    php_version=$(basename "$php_dir")
    # Check pool configuration files
    pool_dir="${php_dir}/fpm/pool.d"
    if [[ -d "$pool_dir" ]]; then
        for pool_file in "$pool_dir"/*.conf; do
            if [[ -f "$pool_file" ]]; then
                echo "Updating Largest request_terminate_timeout=${largest_input_time} in pool file: $pool_file"
                sudo sed -i "s/request_terminate_timeout\( *\)=.*/request_terminate_timeout = $largest_input_time/" "$pool_file"
            fi
        done
    fi
done
