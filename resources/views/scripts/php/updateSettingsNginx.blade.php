# -----------
# Update Nginx Conf
# -----------

if grep client_max_body_size /etc/nginx/nginx.conf; then
    sudo sed -i '/client_max_body_size/d' /etc/nginx/nginx.conf
fi

if grep fastcgi_read_timeout /etc/nginx/nginx.conf; then
    sudo sed -i '/fastcgi_read_timeout/d' /etc/nginx/nginx.conf
fi

largest_filesize=0
largest_filesize_file=''
largest_input_time=-1
largest_input_time_file=''

# Loop through each PHP version directory
for php_dir in /etc/php/*; do
    php_version=$(basename "$php_dir")
    php_ini="${php_dir}/fpm/php.ini"

    if [[ -f "$php_ini" ]]; then
        # Extract values
        upload_max_filesize=$(grep -Po '(?<=upload_max_filesize = )\d+' "$php_ini")
        max_input_time=$(grep -Po '(?<=max_input_time = )-?\d+' "$php_ini") # -1 is a valid value, meaning no limit

        # Check and update largest filesize
        if (( upload_max_filesize > largest_filesize )); then
          largest_filesize=$upload_max_filesize
          largest_filesize_file=$php_ini
        fi

        # Check and update largest input time
        if (( max_input_time > largest_input_time )); then
          largest_input_time=$max_input_time
          largest_input_time_file=$php_ini
        fi
    fi

    # Check pool configuration files
    pool_dir="${php_dir}/fpm/pool.d"
    if [[ -d "$pool_dir" ]]; then
        for pool_file in "$pool_dir"/*.conf; do
            # echo "Checking pool file: $pool_file"
            if [[ -f "$pool_file" ]]; then
                # Extract max_input_time from pool files, if defined
                pool_input_time=$(grep -Po '(?<=php_admin_value\[max_input_time\]=)-?\d+' "$pool_file")
                if [[ "$pool_input_time" != "" ]] && (( pool_input_time > largest_input_time )); then
                    largest_input_time=$pool_input_time
                    largest_input_time_file=$php_ini
                fi

                # Extract upload_max_filesize from pool files, if defined
                pool_filesize=$(grep -Po '(?<=php_admin_value\[upload_max_filesize\]=)\d+M' "$pool_file" | grep -Po '\d+')
                if [[ "$pool_filesize" != "" ]] && (( pool_filesize > largest_filesize )); then
                    largest_filesize=$pool_filesize
                    largest_filesize_file=$pool_file
                fi
            fi
        done
    fi
done

echo "Largest upload_max_filesize: ${largest_filesize}M in ${largest_filesize_file}"
echo "Largest max_input_time: ${largest_input_time} in ${largest_input_time_file}"

echo "client_max_body_size ${largest_filesize}M;" > /etc/nginx/conf.d/uploads.conf
echo "fastcgi_read_timeout $largest_input_time;" > /etc/nginx/conf.d/timeout.conf

echo "client_max_body_size ${largest_filesize}M in /etc/nginx/conf.d/uploads.conf"
echo "fastcgi_read_timeout $largest_input_time in /etc/nginx/conf.d/timeout.conf"

for php_dir in /etc/php/*; do
    php_version=$(basename "$php_dir")
    # Check pool configuration files
    pool_dir="${php_dir}/fpm/pool.d"
    if [[ -d "$pool_dir" ]]; then
        for pool_file in "$pool_dir"/*.conf; do
            if [[ -f "$pool_file" ]]; then
                echo "Updating Largest request_terminate_timeout=${largest_input_time} in pool file: $pool_file"
                sudo sed -i "s/request_terminate_timeout\( *\)=.*/request_terminate_timeout = $largest_input_time/" "$pool_file"
            fi
        done
    fi
done
