@if($server->stack->isNginx())
SITE_WWW_CONF_PATH="/etc/php/{{ $site->php_version }}/fpm/pool.d/www-{{$site->site_user}}.conf"
if [ -f $SITE_WWW_CONF_PATH ]; then
@foreach($envVars as $envVar => $value)
    # if starts with pm.
    @if(str_starts_with($envVar, 'pm.'))
        if grep -qE "^\s*;?\s*{{$envVar}}\s*=" $SITE_WWW_CONF_PATH; then
            sudo sed -i -E "s/^\s*;?\s*{{$envVar}}\s*=\s*.*/{{$envVar}}={{$value}}/" $SITE_WWW_CONF_PATH
        else
            echo "{{$envVar}}={{$value}}" >> $SITE_WWW_CONF_PATH
        fi
    @else
        if grep -qE "^\s*;?\s*php_admin_value\[{{$envVar}}\]\s*=" $SITE_WWW_CONF_PATH; then
            sudo sed -i -E "s/^\s*;?\s*php_admin_value\[{{$envVar}}\]\s*=\s*.*/php_admin_value[{{$envVar}}]={{$value}}/" $SITE_WWW_CONF_PATH
        else
            echo "php_admin_value[{{$envVar}}]={{$value}}" >> $SITE_WWW_CONF_PATH
        fi
    @endif
@endforeach
    echo "Updated PHP settings for {{$site->site_url}}"
else
    echo "File not found"
fi

@include('scripts.php.updateSettingsNginx')

sudo service php{{ $site->php_version }}-fpm restart

NGINX=$(ps aux | grep nginx | grep -v grep)

if [[ -z $NGINX ]]; then
    service nginx start
    echo "Started Nginx"
else
    service nginx reload
    echo "Reloaded Nginx"
fi
@endif

@if($server->stack->isOpenLiteSpeed())
php_ini_file="/home/<USER>/lsphp/{{ $site->name }}/php.ini"
if [ -f "$php_ini_file" ]; then
@foreach($envVars as $envVar => $value)
    @if(str_starts_with($envVar, 'pm.'))
        @continue
    @endif
    #check if the variable is already exists
    if grep -qE "^\s*;?\s*{{$envVar}}\s*=" $php_ini_file; then
        sudo sed -i -E "s/^\s*;?\s*{{$envVar}}\s*=\s*.*/{{$envVar}}={{$value}}/" $php_ini_file
    else
        echo "{{$envVar}}={{$value}}" >> $php_ini_file
    fi
@endforeach
fi
echo "Updated PHP settings for {{$site->site_url}}"
killall -9 lsphp
@endif
