chown -R xcloud:xcloud /tmp/lshttpd/swap
chown -R xcloud:xcloud /tmp/lshttpd/.rtreport*

# Check if the directory /var/www/default exists
if [ -d /var/www/default ]; then
    # Check if the owner of the directory is 'root'
    if [ "$(stat -c "%U" /var/www/default)" == "root" ]; then
        # If the current owner is root, change ownership to xcloud:xcloud
        chown -R xcloud:xcloud /var/www/default
        echo "Ownership of /var/www/default changed to xcloud:xcloud."
    else
        echo "No change needed. Current owner is not root."
    fi
else
    echo "/var/www/default does not exist."
fi

echo "Permissions for /tmp/lshttpd/swap, /tmp/lshttpd/.rtreport* and /var/www/default have been updated."
