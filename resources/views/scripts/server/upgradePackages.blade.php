#!/bin/bash
# Update unattended-upgrades configuration
echo "Updating unattended-upgrades configuration..."

if ! apt-get install unattended-upgrades apt-listchanges -y > install.log 2>&1; then
    cat install.log
fi

unattended-upgrades

# Remove unnecessary packages
echo "Removing unnecessary packages..."
sudo apt-get autoremove -y

# remove partially installed or cached .deb files
sudo apt-get autoclean -y

# Check if a reboot is required after upgrades
if [ -f /var/run/reboot-required ]; then
echo "A reboot is required to complete the upgrade. Rebooting now..."
reboot
fi
