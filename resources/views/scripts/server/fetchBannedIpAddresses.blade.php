# Check if fail2ban is running and enable it if needed
if ! sudo systemctl is-active --quiet fail2ban; then
    echo "Fail2Ban is not running. Enabling Fail2Ban..."
    sudo systemctl restart fail2ban
    sudo systemctl enable fail2ban
fi

# Get list of all jails
jails=$(sudo fail2ban-client status | grep "Jail list" | sed -E 's/^[^:]+:[ \t]+//' | sed 's/,//g')

# Initialize JSON array
echo "{"
echo "  \"banned_ips\": ["

first=true
# Loop through each jail and get banned IPs
for jail in $jails; do
    # Get banned IPs for this jail
    banned_ips=$(sudo fail2ban-client status $jail | grep "Banned IP list" | sed -E 's/^[^:]+:[ \t]+//' | sed 's/,//g')
    
    # If there are banned IPs, add them to the JSON
    if [ ! -z "$banned_ips" ]; then
        for ip in $banned_ips; do
            if [ "$first" = true ]; then
                first=false
            else
                echo ","
            fi
            echo "    {\"ip\": \"$ip\", \"jail\": \"$jail\"}"
        done
    fi
done

echo "  ]"
echo "}"
