#!/bin/bash

sudo -i -u {{$site->site_user}} bash << 'EOF'
# Initialize JSON output
echo "{"

# Check maintenance mode status
echo "  \"maintenance_mode\": {"
cd {{ $site->manager()->siteBasePath() }}
if [ -f storage/framework/down ]; then
    echo "    \"status\": true"
else
    echo "    \"status\": false"
fi
echo "  },"

# Check debug mode status
echo "  \"debug_mode\": {"
DEBUG_MODE=$(grep -E "^APP_DEBUG=" .env | cut -d= -f2)
if [[ "$DEBUG_MODE" == "true" ]]; then
    echo "    \"status\": true"
else
    echo "    \"status\": false"
fi
echo "  },"

# Get application environment
echo "  \"app_env\": {"
APP_ENV=$(grep -E "^APP_ENV=" .env | cut -d= -f2)
echo "    \"value\": \"$APP_ENV\""
echo "  },"

# Check Horizon status
echo "  \"horizon\": {"
COMPOSER_JSON_PATH="{{ $site->manager()->siteBasePath() }}/composer.json"
if [ -f "$COMPOSER_JSON_PATH" ] && grep -q "laravel/horizon" "$COMPOSER_JSON_PATH"; then
    echo "    \"installed\": true,"

    # Check if Horizon is running
    cd {{ $site->manager()->siteBasePath() }}
    HORIZON_STATUS=$({{ $site->php }} artisan horizon:status --no-interaction 2>&1 || echo 'not running')

    if [[ "$HORIZON_STATUS" == *"not running"* ]] || [[ "$HORIZON_STATUS" == *"Horizon is inactive"* ]]; then
        echo "    \"running\": false"
    else
        echo "    \"running\": true"
    fi
else
    echo "    \"installed\": false,"
    echo "    \"running\": false"
fi
echo "  },"

# Check Scheduler status
echo "  \"scheduler\": {"

# We can't check the cron job directly from the script
# This will be handled in the controller by checking the database
echo "    \"status\": \"unknown\""
echo "  },"

# Check Queue Connection
echo "  \"queue_connection\": {"
QUEUE_CONNECTION=$(grep -E "^QUEUE_CONNECTION=" .env | cut -d= -f2)
echo "    \"value\": \"$QUEUE_CONNECTION\""
echo "  }"

echo "}"
EOF
