get_wordpress_dirs() {
    local nginx_path="$1"
    local site_prefix="$2"

    local processed_dirs=()

    grep -R -E 'root ' "$nginx_path" | awk -F 'root ' '{print $2}' | awk -F ';' '{print $1}' | grep -v '/\.' | grep -v '\./' | while read -r dir; do
        # Remove surrounding quotes if present
        dir=$(echo "$dir" | sed 's/^"//;s/"$//')
        if [ -n "$site_prefix" ]; then
            dir="$site_prefix$dir"
        fi
        #check if directory exists
        if [ ! -d "$dir" ]; then
            continue
        fi

        local wp_config_file=$(get_wp_config_file "$dir")
        if [ -f "$wp_config_file" ] && ! echo "${processed_dirs[@]}" | grep -wq "$dir"; then
            processed_dirs+=("$dir")
            echo "$dir"
        fi
    done
}
