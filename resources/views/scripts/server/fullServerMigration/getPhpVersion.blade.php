get_php_version() {
    local nginx_conf="$1"
    local fast_cgi=$(grep -o 'fastcgi_pass[^;]*;' "$nginx_conf")
    local php_version=$(echo "$fast_cgi" | grep -o '/php/php[^-]*' | sed 's/\/php\/php//')

    # If PHP version is empty, set it from php -v
    if [ -z "$php_version" ]; then
        php_version=$(php -v | grep -o 'PHP [0-9]\.[0-9]\.[0-9]' | sed 's/PHP //' | cut -d '.' -f 1,2)
    fi

    # If PHP version is still empty, set it to 8.1
    if [ -z "$php_version" ]; then
        php_version="8.1"
    fi

    echo "$php_version"
}
