get_wp_config_file() {
    local dir="$1"
    local wp_config_file="$dir/wp-config.php"
    # Search for wp-config.php file in parent directories only 1 level up
    if [ ! -f "$wp_config_file" ]; then
        dir=$(dirname "$dir")
        wp_config_file="$dir/wp-config.php"
    fi

    local dir2="$1"

    if [ ! -f "$wp_config_file" ]; then
        # If not found in parent directories, recursively search in subdirectories
        wp_config_file=$(find "$dir2" -type f -name 'wp-config.php' -print -quit)
        if [ -z "$wp_config_file" ]; then
            echo "Error: wp-config.php file not found for directory $1 and its subdirectories"
            exit 1
        fi
    fi
    echo "$wp_config_file"
}
