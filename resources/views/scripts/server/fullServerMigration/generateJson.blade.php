generate_json_array() {
    local nginx_paths=("$@")  # Accept multiple nginx paths as arguments
    local first="true"
    local json_array="["
    for nginx_path in "${nginx_paths[@]}"; do
        if [ ! -d "$nginx_path" ]; then
            continue  # Skip if the nginx path doesn't exist
        fi
        while read -r dir; do
            if [ "$first" = "false" ]; then
                json_array="$json_array, "
            fi
            local wp_config_file=$(get_wp_config_file "$dir")
            local db_name=$(get_db_name "$wp_config_file")
            local db_user=$(get_db_user "$wp_config_file")
            local db_password=$(get_db_password "$wp_config_file")
            local db_host=$(get_db_host "$wp_config_file")
            local db_port=$(get_db_port "$wp_config_file")
            #get the nginx path of the site using the directory
            local nginx_conf=$(grep -r "$dir" "$nginx_path"* | cut -d ":" -f 1 | tail -n 1)
            local domain_name=$(grep -m 1 -oP "(?<=server_name\s)[^;]+" "$nginx_conf" | sed -e 's/^\s*//' -e 's/\s*$//')

            #if domain name is empty, set it to directory name after last slash
            if [ -z "$domain_name" ]; then
                domain_name=$(echo "$dir" | rev | cut -d '/' -f 1 | rev)
            fi
            local site_name=$(echo "$domain_name" | cut -d '.' -f 1)

            local php_version=$(get_php_version "$nginx_conf")

            # Calculate storage usage in GB
            local storage=$(du -sb "$dir" | awk '{usage = $1 / 1024 / 1024 / 1024; printf "%.2f", usage}')

            # Get the database size
            local db_size=$(get_database_size "$db_name" "$db_host" "$db_port" "$db_user" "$db_password")

            # Calculate total size
            local total_size=$(awk "BEGIN { total = $db_size + $storage; printf \"%.2f\", total }")

            json_array="$json_array{\"site_name\":\"$site_name\", \"domain_name\":\"$domain_name\", \"directory\":\"$dir\", \"wp_config_file\":\"$wp_config_file\", \"php_version\":\"$php_version\", \"database\":{\"name\":\"$db_name\", \"user\":\"$db_user\", \"password\":\"$db_password\",\"host\":\"$db_host\", \"port\":\"$db_port\", \"size\":\"$db_size\"}, \"storage\":\"$storage\", \"total_size\":\"$total_size\"}"
            first="false"
        done <<< "$(get_wordpress_dirs "$nginx_path")"
    done
    json_array="$json_array]"
    echo "$json_array"
}
