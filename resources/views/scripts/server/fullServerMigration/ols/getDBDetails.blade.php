get_db_name() {
    local wp_config_file="$1"
    local db_name=$(grep -m 1 -E "^[[:space:]]*define[[:space:]]*\([[:space:]]*'DB_NAME',[[:space:]]*'[^']*'" "$wp_config_file" | awk -F "'" '{print $4}')
    echo "$db_name"
}

get_db_user() {
    local wp_config_file="$1"
    local db_user=$(grep -m 1 -E "^[[:space:]]*define[[:space:]]*\([[:space:]]*'DB_USER',[[:space:]]*'[^']*'" "$wp_config_file" | awk -F "'" '{print $4}')
    echo "$db_user"
}

get_db_password() {
    local wp_config_file="$1"
    local db_password=$(grep -m 1 -E "^[[:space:]]*define[[:space:]]*\([[:space:]]*'DB_PASSWORD',[[:space:]]*'[^']*'" "$wp_config_file" | awk -F "'" '{print $4}')
    echo "$db_password"
}

get_db_host_port() {
    local wp_config_file="$1"
    local db_host_port=$(grep -m 1 -E "^[[:space:]]*define[[:space:]]*\([[:space:]]*'DB_HOST',[[:space:]]*'[^']*'" "$wp_config_file" | awk -F "'" '{print $4}')
    echo "$db_host_port"
}

get_db_host(){
    local db_host_port=$(get_db_host_port "$1")
    echo "$db_host_port" | cut -d ':' -f 1
}

get_db_port(){
    local db_host_port=$(get_db_host_port "$1")
    local db_port=3306
    if [[ "$db_host_port" == *":"* ]]; then
        db_port=$(echo "$db_host_port" | cut -d ':' -f 2)
    fi
    echo "$db_port"
}

get_database_size() {
    local db_name="$1"
    local db_host="$2"
    local db_port="$3"
    local db_user="$4"
    local db_password="$5"
    local db_size=$(mysql -u "$db_user" -p"$db_password" -h "$db_host" -P "$db_port" -e "SELECT table_schema AS 'Database', SUM(data_length + index_length) / (1024 * 1024 * 1024) AS 'Size (GB)' FROM information_schema.TABLES WHERE table_schema = '$db_name' GROUP BY table_schema;" 2>/dev/null | awk '{if (NR!=1) {print $2}}')
    echo "$db_size"
}
