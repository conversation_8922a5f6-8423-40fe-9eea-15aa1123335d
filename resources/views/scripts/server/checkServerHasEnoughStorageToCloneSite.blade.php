#
# REQUIRES:
#   - site (the site instance)
#   - sourceSiteStorage (in MB)
#

set -e

## step 1: get total server size

AVAILABLE_SERVER_STORAGE=$(df -BM --output=avail / | awk 'NR==2{print $1}' | tr -d 'M') {{-- getting the result in MB and remove M from last--}}

STORAGE_IN_USE={{$sourceSiteStorage}}

# step2: Check if the server has atleast 1GB(1024M) more storage than the old site.
if [[ $AVAILABLE_SERVER_STORAGE -ge $((STORAGE_IN_USE + 1024)) ]];
then
    echo "Server has enough storage to clone site"
else
    echo "Server does not have enough space to clone site. Available Disk Space : $AVAILABLE_SERVER_STORAGE MB , storage required for cloning: $((STORAGE_IN_USE + 1024)) MB"
    exit 1
fi