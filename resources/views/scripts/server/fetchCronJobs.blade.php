#!/bin/bash

# Initialize an empty JSON object
cronJobs="{\"cron_jobs\": ["

# Function to add cron jobs to the JSON object
add_cron_jobs() {
    local source="$1"
    local user="$2"
    local jobs="$3"
    if [ -n "$jobs" ]; then
        cronJobs+="{\"source\": \"$source\", \"user\": \"$user\", \"jobs\": ["
        while IFS= read -r line; do
            # Skip empty lines and comments
            if [[ -n "$line" && ! "$line" =~ ^# ]]; then
                # Escape double quotes and backslashes in the line
                line=$(echo "$line" | sed 's/\\/\\\\/g; s/\"/\\"/g')
                cronJobs+="\"$line\", "
            fi
        done <<< "$jobs"
        # Remove trailing comma and space
        cronJobs=${cronJobs%, }
        cronJobs+="]}, "
    fi
}

# Get all system users
users=$(cut -d: -f1 /etc/passwd)

# Process user crontabs
for user in $users; do
    cron_jobs=$(crontab -u "$user" -l 2>/dev/null)
    add_cron_jobs "user_crontab" "$user" "$cron_jobs"
done

# Process /etc/cron.d directory
for file in /etc/cron.d/*; do
    if [ -f "$file" ]; then
        cron_jobs=$(cat "$file" | grep -vE '^(#|$)')
        add_cron_jobs "cron.d_file" "$(basename "$file")" "$cron_jobs"
    fi
done

# Remove trailing comma and space, close JSON array and object
cronJobs=${cronJobs%, }
cronJobs+="]}"

# Print the JSON object
echo -e "$cronJobs"
