#!/bin/bash

# Initialize the JSON string
cronJobs="{\"cron_jobs\": ["

# Escape string for JSON manually
escape_json_string() {
    echo "$1" | sed -e 's/\\/\\\\/g' \
                    -e 's/"/\\"/g' \
                    -e 's/\t/\\t/g' \
                    -e 's/\r/\\r/g' \
                    -e 's/\n/\\n/g'
}

# Function to add cron jobs
add_cron_jobs() {
    local source="$1"
    local user="$2"
    local jobs="$3"
    local job_lines=()

    while IFS= read -r line; do
        [[ -z "$line" || "$line" =~ ^# ]] && continue
        escaped_line=$(escape_json_string "$line")
        job_lines+=("\"$escaped_line\"")
    done <<< "$jobs"

    # Only add section if there are jobs
    if [ ${#job_lines[@]} -gt 0 ]; then
        cronJobs+="{\"source\": \"$source\", \"user\": \"$user\", \"jobs\": ["
        cronJobs+=$(IFS=,; echo "${job_lines[*]}")
        cronJobs+="]}, "
    fi
}

# Read system users
users=$(cut -d: -f1 /etc/passwd)

# Check user crontabs
for user in $users; do
    cron_output=$(crontab -u "$user" -l 2>/dev/null)
    [ -n "$cron_output" ] && add_cron_jobs "user_crontab" "$user" "$cron_output"
done

# Check /etc/cron.d files
for file in /etc/cron.d/*; do
    [ -f "$file" ] || continue
    cron_output=$(grep -vE '^(#|$)' "$file")
    [ -n "$cron_output" ] && add_cron_jobs "cron.d_file" "$(basename "$file")" "$cron_output"
done

# Trim trailing comma and close JSON
cronJobs="${cronJobs%, }"
cronJobs+="]}"

# Output final JSON
echo -e "$cronJobs"
