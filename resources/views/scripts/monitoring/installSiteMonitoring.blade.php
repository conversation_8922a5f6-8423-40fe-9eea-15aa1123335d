@include('scripts.monitoring.site.GenerateScript', ['site' => $site])
@includeWhen($site->isWordpress(),'scripts.monitoring.site.GenerateWPUpdateScript', ['site' => $site])

@include('scripts.monitoring.site.EncryptScript', ['site' => $site])
@includeWhen($site->isWordpress(),'scripts.monitoring.site.EncryptUpdateScript', ['site' => $site])

@include('scripts.monitoring.site.Ping', ['site' => $site])

@include('scripts.monitoring.site.GenerateCorn', ['minute' => 60, 'site' => $site])
