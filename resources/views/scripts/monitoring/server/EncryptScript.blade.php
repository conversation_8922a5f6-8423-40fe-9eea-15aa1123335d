@include('scripts.monitoring.server.Print', ['message' => 'Encrypting Monitoring Script...'])
chmod +x /home/<USER>/.xcloud-monitoring/monitor.sh
@include('scripts.apt.apt-wait')
apt install gcc -y
apt install shc -y
shc -f /home/<USER>/.xcloud-monitoring/monitor.sh -o /home/<USER>/.xcloud-monitoring/monitor.xc
rm /home/<USER>/.xcloud-monitoring/monitor.sh.x.c
rm /home/<USER>/.xcloud-monitoring/monitor.sh
