## REQUIRES:#   - site (The Site Instance)#

@if($site->server->stack->isOpenLiteSpeed())
touch {{ $site->site_path }}/.htaccess
cp {{ $site->site_path }}/.htaccess {{ $site->site_path }}/.htaccess.7g.bak

sed -i '/# START_XCLOUD_AI_BOT_BLOCKER/,/# END_XCLOUD_AI_BOT_BLOCKER/d' "{{ $site->site_path }}/.htaccess"

cat >> {{ $site->site_path }}/.htaccess << 'EOF_XCLOUD_AI_BOT_BLOCKER_EOF'

# START_XCLOUD_AI_BOT_BLOCKER
@include('scripts.security.ai-bot-blocker.ai-bot-blocker-ols')
# END_XCLOUD_AI_BOT_BLOCKER
EOF_XCLOUD_AI_BOT_BLOCKER_EOF

chown {{ $site->site_user }}:{{ $site->site_user }} {{ $site->site_path }}/.htaccess
@endif
