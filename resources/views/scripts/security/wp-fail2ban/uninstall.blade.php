sudo -i -u {{$site->site_user}} bash << 'EOF'
cd /var/www/{{ $site->name }}

# Deactivate and remove wp-fail2ban plugin
{{ $site->wp_cli }} plugin deactivate wp-fail2ban --skip-plugins --skip-themes


# Remove include line from wp-config.php if it exists
if grep -q "wp-fail2ban-config.php" wp-config.php; then
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' '/wp-fail2ban-config.php/d' wp-config.php
    else
        sed -i '/wp-fail2ban-config.php/d' wp-config.php
    fi
fi


# Remove wp-fail2ban config file if it exists
if [ -f wp-content/wp-fail2ban-config.php ]; then
    rm wp-content/wp-fail2ban-config.php
fi

# Remove the plugin 
{{ $site->wp_cli }} plugin delete wp-fail2ban --skip-plugins --skip-themes

echo "WP Fail2Ban has been uninstalled successfully"

EOF