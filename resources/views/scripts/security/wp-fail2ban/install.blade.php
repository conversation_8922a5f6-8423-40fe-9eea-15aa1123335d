echo "Installing WP Fail2Ban plugin..."

sudo -i -u {{$site->site_user}} bash << 'EOF'
cd /var/www/{{ $site->name }}

# Install wp-fail2ban plugin
{{ $site->wp_cli }} plugin install wp-fail2ban --activate --skip-plugins --skip-themes

# Copy default config file if it doesn't exist
if [ ! -f wp-content/wp-fail2ban-config.php ]; then
    cp wp-content/plugins/wp-fail2ban/conf.d/default-all.php wp-content/wp-fail2ban-config.php
fi

# Check if wp-config.php already includes the wp-fail2ban config
if ! grep -q "wp-fail2ban-config.php" wp-config.php; then
    # Find the line "/* That's all, stop editing! Happy publishing. */"
    STOP_EDITING_LINE=$(grep -n "That's all, stop editing! Happy publishing" wp-config.php | cut -d: -f1)

    if [ ! -z "$STOP_EDITING_LINE" ]; then
        # Insert include before the stop editing line
        sed -i "${STOP_EDITING_LINE}i\include __DIR__.'/wp-content/wp-fail2ban-config.php';\n" wp-config.php
    else
        # If stop editing line not found, append to end of file
        echo "include __DIR__.'/wp-content/wp-fail2ban-config.php';" >> wp-config.php
    fi
fi

# Enable purge for the plugin

set_wp_fail2ban_constant() {
    local config_file="wp-content/wp-fail2ban-config.php"
    local constant_name="$1"
    local constant_value="$2"
}

set_wp_fail2ban_constants() {
    local config_file="wp-content/wp-fail2ban-config.php"
    declare -n constants=$1  # Reference associative array

    # Ensure the file exists
    if [ ! -f "$config_file" ]; then
        echo "❌ Config file $config_file not found"
        return 1
    fi

    for constant_name in "${!constants[@]}"; do
        local constant_value="${constants[$constant_name]}"

        # Escape for grep
        local escaped_name
        escaped_name=$(printf '%s\n' "$constant_name" | sed 's/[][\.^$*]/\\&/g')

        # Remove existing define line(s)
        if grep -q "define(['\"]$constant_name['\"]" "$config_file"; then
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS version of sed
                sed -i '' "/define(['\"]$escaped_name['\"]/,/);/d" "$config_file"
            else
                sed -i "/define(['\"]$escaped_name['\"]/,/);/d" "$config_file"
            fi
            echo "🔁 Removed existing $constant_name definition"
        fi

        # Append new line
        echo -e "\ndefine('$constant_name', $constant_value);" >> "$config_file"
        echo "✅ Added $constant_name with value $constant_value"
    done

    # Remove extra newlines (more than 2 consecutive)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS version of sed
        sed -i '' '/^[[:space:]]*$/N;/^\n[[:space:]]*$/D' "$config_file"
    else
        sed -i '/^[[:space:]]*$/N;/^\n[[:space:]]*$/D' "$config_file"
    fi
    echo "✅ Removed extra newlines from config file"
}

handle_deprecated_constants() {
    local config_file="wp-content/wp-fail2ban-config.php"

    # Check for and remove deprecated WP_FAIL2BAN_LOG_COMMENTS_EXTRA constant
    if grep -q "define(['\"]WP_FAIL2BAN_LOG_COMMENTS_EXTRA['\"]" "$config_file"; then
        echo "⚠️ Found deprecated WP_FAIL2BAN_LOG_COMMENTS_EXTRA constant"
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS version of sed
            sed -i '' "/define(['\"]WP_FAIL2BAN_LOG_COMMENTS_EXTRA['\"]/,/);/d" "$config_file"
        else
            sed -i "/define(['\"]WP_FAIL2BAN_LOG_COMMENTS_EXTRA['\"]/,/);/d" "$config_file"
        fi
        echo "✅ Removed deprecated WP_FAIL2BAN_LOG_COMMENTS_EXTRA constant"
    fi
}

declare -A wp_fail2ban_options=(
    [WP_FAIL2BAN_PLUGIN_LOG_AUTH]="{{ $wpfail2ban['log_failed_logins'] ? 'true' : 'false' }}"
    [WP_FAIL2BAN_BLOCKED_USERS]="{!! $wpfail2ban['block_common_usernames'] ? "['admin','Admin','user','User','user1','User1','Administrator','administrator','demo','Demo']" : "[]" !!}"
    [WP_FAIL2BAN_BLOCK_USER_ENUMERATION]="{{ $wpfail2ban['block_user_enumeration'] ? 'true' : 'false' }}"
    [WP_FAIL2BAN_LOG_COMMENTS]="{{ $wpfail2ban['protect_comments'] ? 'true' : 'false' }}"
    [WP_FAIL2BAN_LOG_COMMENT_ATTEMPTS]="{{ $wpfail2ban['protect_comments'] ? 'true' : 'false' }}"
    [WP_FAIL2BAN_LOG_SPAM]="{{ $wpfail2ban['block_spam'] ? 'true' : 'false' }}"
    [WP_FAIL2BAN_LOG_PASSWORD_REQUEST]="{{ $wpfail2ban['guard_password_resets'] ? 'true' : 'false' }}"
    [WP_FAIL2BAN_LOG_PINGBACKS]="{{ $wpfail2ban['guard_pingbacks'] ? 'true' : 'false' }}"
)

set_wp_fail2ban_constants wp_fail2ban_options


@if($site->ssl_provider == \App\Models\SslCertificate::PROVIDER_CLOUDFLARE || $site->ssl_provider == \App\Models\SslCertificate::PROVIDER_STAGING)
    declare -A wp_fail2ban_options=(
        [WP_FAIL2BAN_PROXIES]="['173.245.48.0/20', '103.21.244.0/22', '103.22.200.0/22', '103.31.4.0/22', '141.101.64.0/18', '108.162.192.0/18', '190.93.240.0/20', '188.114.96.0/20', '197.234.240.0/22', '198.41.128.0/17', '162.158.0.0/15', '104.16.0.0/12', '172.64.0.0/13', '131.0.72.0/22']"
    )
@else
    declare -A wp_fail2ban_options=(
        [WP_FAIL2BAN_PROXIES]="[]"
    )
@endif

set_wp_fail2ban_constants wp_fail2ban_options

handle_deprecated_constants

EOF

# Enable wp-fail2ban jail and fileter if not configured already

echo "Configuring fail2ban for WP Fail2Ban plugin..."

# Check if wp-fail2ban filters exist in fail2ban
if [ ! -f "/etc/fail2ban/filter.d/wordpress-hard.conf" ] || [ ! -f "/etc/fail2ban/filter.d/wordpress-soft.conf" ]; then
    echo "⌛ Installing wp-fail2ban filters..."

    # Get plugin path
    PLUGIN_PATH="/var/www/{{ $site->name }}/wp-content/plugins/wp-fail2ban"

    if [ -d "$PLUGIN_PATH" ]; then
        # Copy filter files from plugin directory
        cp "$PLUGIN_PATH/filters.d/"*.conf /etc/fail2ban/filter.d/
        echo "✅ Copied wp-fail2ban filters to /etc/fail2ban/filter.d/"
        # Restart fail2ban to apply changes
        systemctl restart fail2ban
        echo "✅ Restarted fail2ban service"
    else
        echo "❌ WP Fail2Ban plugin directory not found at $PLUGIN_PATH"
        exit 1
    fi
else
    echo "✅ WP Fail2Ban filters already configured"
fi

# Create wordpress jail config if it doesn't exist
if [ ! -f "/etc/fail2ban/jail.d/wordpress.conf" ]; then
    echo "⌛ Creating wordpress jail configuration..."
    cat > "/etc/fail2ban/jail.d/wordpress.conf" << 'JAILCONF'
[wordpress-hard]
enabled = true
filter = wordpress-hard
logpath = /var/log/auth.log
maxretry = 1
port = http,https

[wordpress-soft]
enabled = true
filter = wordpress-soft
logpath = /var/log/auth.log
maxretry = 3
port = http,https
JAILCONF
    echo "✅ Created wordpress jail configuration"

    # Restart fail2ban to apply changes
    systemctl restart fail2ban
    echo "✅ Restarted fail2ban service"
else
    echo "✅ WordPress jail configuration already exists"
fi

echo "✅ WP Fail2Ban has been installed successfully"

