sudo -i -u {{$site->site_user}} bash << 'WP_UPDATE_CHECK'
# CD to the site directory
cd /var/www/{{ $site->name }}

# Update plugins
update_output=$({{ $site->wp_cli }} --skip-themes plugin  {!! $action !!} {!! implode(' ', $plugins) !!} 2>&1)

# Check if update was successful
if echo "$update_output" | grep -q "Success"; then
# Set update_status to "success"
action_status="success"
# Perform any additional actions for successful update
else
# Set update_status to "failed"
action_status="$(echo "$update_output" | tail -n 1)"
# Perform any additional actions for failed update
fi

# Create data payload for callback
DATA='{
"plugins": {!! json_encode($plugins)  !!},
"site": "'{{ $site->name }}'",
"action": "'{{ $action }}'",
"action_status": "'"${action_status}"'"
}'

echo $DATA

WP_UPDATE_CHECK
