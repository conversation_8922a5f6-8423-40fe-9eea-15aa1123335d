#!/bin/bash

# Initialize variables
BACKUP_PATH=$(eval echo "{{$site->backupFilesPath()}}")
SITE_NAME="{{$site->name}}"
SITE_PATH="/var/www/{{$site->name}}"
BACKUP_DIR_NAME="{{$site->backupDirName()}}"
DATETIME=$(date +"%Y%m%d%H%M%S")
LOG_FILE="${BACKUP_PATH}/backup.log"
CALLBACK_URL="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
IS_REMOTE_BACKUP={{$isRemoteBackup ? "true" : "false"}}
BACKUP_SETTINGS_ID="{{$backupSetting->id}}"
STORAGE_PROVIDER_ID="{{$backupSetting->storage_provider_id}}"
BACKUP_FILES={{$backupSetting->files ? "true" : "false"}}
BACKUP_DATABASE={{$backupSetting->database ? "true" : "false"}}
DATABASE_TYPE={{$site->isSqlite() ? "sqlite" : "mysql"}}
USER_ID=${1:-0}  # Set user_id to $1 if provided, otherwise default to 0

# Create backup directory and log file
mkdir -p ${BACKUP_PATH}
touch ${LOG_FILE}

# Function to log messages
log_message() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") - $1" >> ${LOG_FILE}
}

# Function to send error callback and exit
send_error_callback() {
    local ERROR_MESSAGE="$1"
    local DATA="{\"error\":\"${ERROR_MESSAGE}\",\"server_datetime\":\"${DATETIME}\",\"storage_provider_id\":\"${STORAGE_PROVIDER_ID}\",\"backup_settings_id\":\"${BACKUP_SETTINGS_ID}\""

    # Add user_id if provided
    if [ ${USER_ID} -ne 0 ]; then
        DATA="${DATA}, \"user_id\": \"${USER_ID}\""
    fi

    DATA="${DATA}}"

    log_message "ERROR: ${ERROR_MESSAGE}"
    curl -s -X POST -H 'Content-type: application/json' -d "${DATA}" --insecure "${CALLBACK_URL}" >/dev/null
    echo ${DATA}
    exit 1
}

# Function to calculate storage requirements
calculate_storage() {
    log_message "Calculating storage requirements"

    # Calculate site size if backing up files
    if [ "${BACKUP_FILES}" = "true" ]; then
        DIR_SIZE=$(($(du -s @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude="${SITE_PATH}/{{$excludesPath}}" @endforeach ${SITE_PATH} | awk '{print $1}') * 1024 / 1000000))
        log_message "Site size: ${DIR_SIZE} MB"
    else
        DIR_SIZE=0
    fi
    @if($backupSetting->database)
    # Calculate database size if backing up database
    if [ "${BACKUP_DATABASE}" = "true" ]; then
        if [ "${DATABASE_TYPE}" = "sqlite" ]; then
            DATABASE_SIZE=$(stat -c %s {{ $site->sqlFileFullPath() }} | awk '{print $1/1024/1024}')
        else
            DATABASE_SIZE=$(mysql -u {{$site->database_user}} -p{{ $site->database_password }} -e \
            "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) \
            FROM information_schema.tables WHERE table_schema = '{{ $site->database_name }}';" 2>/dev/null | tail -n 1)
            DATABASE_SIZE=${DATABASE_SIZE:-0}  # Fallback to 0 if empty
        fi
        log_message "Database size: ${DATABASE_SIZE} MB"
    else
        DATABASE_SIZE=0
    fi
    @else
        DATABASE_SIZE=0
    @endif

    # Calculate free storage
    FREE_STORAGE_MB=$(df -m /var/www | awk 'NR==2 {print $4}')
    TOTAL_USED=$(echo "${DIR_SIZE} + ${DATABASE_SIZE}" | bc)
    FREE_STORAGE=$(echo "${FREE_STORAGE_MB} - ${TOTAL_USED}" | bc)

    # Check if we have enough space
    if [ "$(echo "${FREE_STORAGE} < {{\App\Models\BackupSetting::NEED_EXTERNAL_SPACE}}" | bc)" -eq 1 ]; then
        TOTAL_FREE_GB=$(echo "${FREE_STORAGE_MB} / 1024" | bc)
        TOTAL_REQUIRED_GB=$(echo "({{\App\Models\BackupSetting::NEED_EXTERNAL_SPACE}} + ${TOTAL_USED}) / 1024" | bc)
        send_error_callback "Not enough space in the Server for backup. Required: ${TOTAL_REQUIRED_GB} GB | Available: ${TOTAL_FREE_GB} GB"
    fi

    log_message "Storage check passed. Free space: ${FREE_STORAGE} MB"
}
@if($isRemoteBackup)
# Function to setup AWS CLI for remote backups
setup_aws_cli() {
    log_message "Setting up AWS CLI"

    # Install AWS CLI if not already installed
    @include('scripts.site.backup.installAWSCLI')

    #check aws cli configuration is invalid
    @include('scripts.site.backup.s3Config',[
        'profile' => $site->backupDirName(),
        'access_key_id' => $storageProvider->access_key_id,
        'secret_key' => $storageProvider->secret_key,
        'region' => $storageProvider->region,
        'endpoint' => $storageProvider->getEndPoint()
    ])

    # Test AWS CLI connection
    log_message "Testing AWS CLI connection"
    /usr/local/bin/aws --profile ${BACKUP_DIR_NAME} s3 ls s3://{{ $storageProvider->bucket }} --endpoint-url={{ $storageProvider->getEndPoint() }} >/dev/null
    if [ $? -ne 0 ]; then
        send_error_callback "S3 client cannot connect to S3 bucket"
    fi

    log_message "AWS CLI setup completed successfully"
}
@endif
@if($backupSetting->database)
# Function to backup database
backup_database() {
    if [ "${BACKUP_DATABASE}" != "true" ]; then
        return
    fi

    log_message "Starting database backup"

    # Set database filename
@if($isRemoteBackup)
    if [ "${DATABASE_TYPE}" = "sqlite" ]; then
        DB_FILENAME="${SITE_NAME}_s_${DATABASE_SIZE}_${DATETIME}.sqlite"
    else
        DB_FILENAME="${SITE_NAME}_s_${DATABASE_SIZE}_${DATETIME}.sql"
    fi
@else
    if [ "${DATABASE_TYPE}" = "sqlite" ]; then
        DB_FILENAME="${SITE_NAME}_local_s_${DATABASE_SIZE}_${DATETIME}.sqlite"
    else
        DB_FILENAME="${SITE_NAME}_local_s_${DATABASE_SIZE}_${DATETIME}.sql"
    fi
@endif
    if [ "${DATABASE_TYPE}" = "sqlite" ]; then
        cp {{ $site->sqlFileFullPath() }} ${BACKUP_PATH}/${DB_FILENAME}
        if [ $? -ne 0 ]; then
            send_error_callback "Sqlite Database backup failed"
        fi
    else
        # Perform database backup
        mysqldump --single-transaction --skip-lock-tables --quick -u root -p{{ $server->database_password }} {{ $site->database_name }} > ${BACKUP_PATH}/${DB_FILENAME} 2>/dev/null
        if [ $? -ne 0 ]; then
            send_error_callback "Database backup failed"
        fi
    fi

    # Get database file size
    DB_FILE_SIZE=$(stat -c %s ${BACKUP_PATH}/${DB_FILENAME} | awk '{print $1/1024}')
    log_message "Database backup completed: ${DB_FILENAME} (${DB_FILE_SIZE} KB)"
}
@endif

# Function to backup site files
backup_site_files() {
    if [ "${BACKUP_FILES}" != "true" ]; then
        return
    fi

    log_message "Starting site files backup"

    # Set file backup filename
    @if($isRemoteBackup)
        FILE_BACKUP_NAME="${SITE_NAME}_s_${DIR_SIZE}_${DATETIME}.tar.gz"
    @else
        FILE_BACKUP_NAME="${SITE_NAME}_local_s_${DIR_SIZE}_${DATETIME}.tar.gz"
    @endif

    # Create tar archive
    tar -czvf ${BACKUP_PATH}/${FILE_BACKUP_NAME} -C /var/www \
        --exclude=${SITE_NAME}/adminer*.php \
        --exclude=${SITE_NAME}/file-manager-*.php \
        @if($site->isSqlite())
        --exclude=${SITE_NAME}/{{ $site->sqlFilePath() }} \
        @endif
        --warning=no-file-changed \
        @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='{{$site->name}}/{{$excludesPath}}' @endforeach \
        ${SITE_NAME} > /dev/null

    # Check if tar file was created
    if [ ! -f ${BACKUP_PATH}/${FILE_BACKUP_NAME} ]; then
        # Clean up database backup if it exists
        if [ "${BACKUP_DATABASE}" = "true" ]; then
            rm -rf ${BACKUP_PATH}/${DB_FILENAME}
        fi
        send_error_callback "Site backup failed"
    fi

    # Get file size
    FILE_SIZE=$(stat -c %s ${BACKUP_PATH}/${FILE_BACKUP_NAME} | awk '{print $1/1024}')
    log_message "Site files backup completed: ${FILE_BACKUP_NAME} (${FILE_SIZE} KB)"
}
@if($isRemoteBackup)
# Function to upload backups to S3
upload_to_s3() {
    if [ "${IS_REMOTE_BACKUP}" != "true" ]; then
        return
    fi

    log_message "Starting upload to S3"

    # Upload site files backup
    if [ "${BACKUP_FILES}" = "true" ]; then
        log_message "Uploading site files to S3"
        FILE_RESPONSE=$(/usr/local/bin/aws s3 cp ${BACKUP_PATH}/${FILE_BACKUP_NAME} \
            s3://{{ $storageProvider->bucket }}/${BACKUP_DIR_NAME}/${FILE_BACKUP_NAME} \
            --metadata '{"SiteID": "{{$site->id}}","ServerID": "{{$site->server_id}}"}' \
            --profile ${BACKUP_DIR_NAME} \
            --endpoint-url {{ $storageProvider->getEndPoint() }})

        if [ $? -ne 0 ]; then
            # Clean up local files
            rm -rf ${BACKUP_PATH}/${FILE_BACKUP_NAME}
            if [ "${BACKUP_DATABASE}" = "true" ]; then
                rm -rf ${BACKUP_PATH}/${DB_FILENAME}
            fi
            send_error_callback "File upload failed"
        fi

        log_message "File upload response: ${FILE_RESPONSE}"
    fi
     @if($backupSetting->database)
    # Upload database backup
    if [ "${BACKUP_DATABASE}" = "true" ]; then
        log_message "Uploading database to S3"
        SQL_RESPONSE=$(/usr/local/bin/aws s3 cp ${BACKUP_PATH}/${DB_FILENAME} \
            s3://{{ $storageProvider->bucket }}/${BACKUP_DIR_NAME}/${DB_FILENAME} \
            --metadata '{"SiteID": "{{$site->id}}","ServerID": "{{$site->server_id}}"}' \
            --profile ${BACKUP_DIR_NAME} \
            --endpoint-url {{ $storageProvider->getEndPoint() }})

        if [ $? -ne 0 ]; then
            # Clean up local files
            if [ "${BACKUP_FILES}" = "true" ]; then
                rm -rf ${BACKUP_PATH}/${FILE_BACKUP_NAME}
            fi
            rm -rf ${BACKUP_PATH}/${DB_FILENAME}
            send_error_callback "SQL upload failed"
        fi

        log_message "SQL upload response: ${SQL_RESPONSE}"
    fi
    if [ "${BACKUP_DATABASE}" = "true" ]; then
        rm -rf ${BACKUP_PATH}/${DB_FILENAME}
    fi
    @endif
    # Clean up local files after successful upload
    if [ "${BACKUP_FILES}" = "true" ]; then
        rm -rf ${BACKUP_PATH}/${FILE_BACKUP_NAME}
    fi

    log_message "S3 upload completed successfully"
}
@endif
# Function to send success callback
send_success_callback() {
    log_message "Preparing success callback"

    # Initialize data object
    if [ "${IS_REMOTE_BACKUP}" = "true" ]; then
        BACKUP_LOCATION="${BACKUP_DIR_NAME}"
    else
        BACKUP_LOCATION="${BACKUP_PATH}"
    fi

    # Build the data object based on what was backed up
    if [ "${BACKUP_FILES}" = "true" ] && [ "${BACKUP_DATABASE}" = "true" ]; then
        DATA="{\"file\":\"${BACKUP_LOCATION}\",\"storage_provider_id\":\"${STORAGE_PROVIDER_ID}\",\"backup_settings_id\":\"${BACKUP_SETTINGS_ID}\",\"file_size\":\"${FILE_SIZE}\",\"taken_size\":\"${DIR_SIZE}\",\"server_datetime\":\"${DATETIME}\",\"db_file_size\":\"${DB_FILE_SIZE}\",\"is_remote\":\"${IS_REMOTE_BACKUP}\",\"file_name\":\"${FILE_BACKUP_NAME}\",\"database\":\"${BACKUP_LOCATION}\",\"database_name\":\"${DB_FILENAME}\"}"
    elif [ "${BACKUP_FILES}" = "true" ]; then
        DATA="{\"file\":\"${BACKUP_LOCATION}\",\"storage_provider_id\":\"${STORAGE_PROVIDER_ID}\",\"backup_settings_id\":\"${BACKUP_SETTINGS_ID}\",\"file_size\":\"${FILE_SIZE}\",\"taken_size\":\"${DIR_SIZE}\",\"server_datetime\":\"${DATETIME}\",\"is_remote\":\"${IS_REMOTE_BACKUP}\",\"file_name\":\"${FILE_BACKUP_NAME}\"}"
    elif [ "${BACKUP_DATABASE}" = "true" ]; then
        DATA="{\"database\":\"${BACKUP_LOCATION}\",\"storage_provider_id\":\"${STORAGE_PROVIDER_ID}\",\"backup_settings_id\":\"${BACKUP_SETTINGS_ID}\",\"db_file_size\":\"${DB_FILE_SIZE}\",\"server_datetime\":\"${DATETIME}\",\"is_remote\":\"${IS_REMOTE_BACKUP}\",\"database_name\":\"${DB_FILENAME}\"}"
    fi

    # Add user_id if provided
    if [ ${USER_ID} -ne 0 ]; then
        DATA="${DATA%?}, \"user_id\": \"${USER_ID}\"}"
    fi

    # Send the callback
    log_message "Sending success callback: ${DATA}"
    curl -s -X POST -H 'Content-type: application/json' -d "${DATA}" --insecure "${CALLBACK_URL}" >/dev/null
    log_message "Backup process completed successfully"
}

# Main execution flow
log_message "Starting backup process"
log_message "Backup type: $([ "${IS_REMOTE_BACKUP}" = "true" ] && echo "Remote" || echo "Local")"
log_message "Backup files: $([ "${BACKUP_FILES}" = "true" ] && echo "Yes" || echo "No")"
log_message "Backup database: $([ "${BACKUP_DATABASE}" = "true" ] && echo "Yes" || echo "No")"

# Step 1: Calculate storage requirements
calculate_storage

# Step 2: Setup AWS CLI for remote backups
@if($isRemoteBackup)
setup_aws_cli
@endif

# Step 3: Backup database
@if($backupSetting->database)
backup_database
@endif
# Step 4: Backup site files
backup_site_files
@if($isRemoteBackup)
# Step 5: Upload to S3 if remote backup
upload_to_s3
@endif
# Step 6: Send success callback
send_success_callback

exit 0
