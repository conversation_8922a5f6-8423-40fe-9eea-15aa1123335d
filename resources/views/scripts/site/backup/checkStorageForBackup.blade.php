## Step 1: Calculate storage in use
@if($backupSetting->files)
    STORAGE_IN_USE=$(($(du -s  @foreach($site->backupExcludesPaths() as $excludesPath) --exclude=/var/www/{{$site->name}}/{{$excludesPath}}  @endforeach /var/www/{{ $site->name }} | awk '{print $1}') * 1024 / 1000000))
@endif

@if($backupSetting->database)
    DATABASE_SIZE=$(mysql -u {{$site->database_user}} -p{{ $site->database_password }} -e \
    "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) \
    FROM information_schema.tables WHERE table_schema = '{{ $site->database_name }}';" 2>/dev/null | tail -n 1)
    DATABASE_SIZE=${DATABASE_SIZE:-0}  # Fallback to 0 if empty
@endif

## Step 2: Calculate free storage
FREE_STORAGE_MB=$(df -m /root | awk 'NR==2 {print $4}')
TOTAL_USED=0

@if($backupSetting->files)
    TOTAL_USED=$(echo "$TOTAL_USED + $STORAGE_IN_USE" | bc)
@endif
@if($backupSetting->database)
    TOTAL_USED=$(echo "$TOTAL_USED + $DATABASE_SIZE" | bc)
@endif

FREE_STORAGE=$(echo "$FREE_STORAGE_MB - $TOTAL_USED" | bc)

## Step 3: Compare with required space threshold
if [ "$(echo "$FREE_STORAGE < {{\App\Models\BackupSetting::NEED_EXTERNAL_SPACE}}" | bc)" -eq 1 ]; then
TOTAL_FREE_GB=$(echo "$FREE_STORAGE_MB / 1024" | bc)
TOTAL_REQUIRED_GB=$(echo "({{\App\Models\BackupSetting::NEED_EXTERNAL_SPACE}} + $TOTAL_USED) / 1024" | bc)
ERROR_MESSAGE="Not enough space in the Server for backup. Required: $TOTAL_REQUIRED_GB GB | Available: $TOTAL_FREE_GB GB"
url="{!! callback_url('/api/callback/monitoring/server/'.hashid_encode($server->id).'/site/'.hashid_encode($site->id)).'/backup' !!}"

if [ -z "$datetime" ]; then
datetime=$(date +"%Y%m%d%H%M%S")
fi
DATA="{\"error\":\"$ERROR_MESSAGE\",\"server_datetime\":\"$datetime\",\"storage_in_use\":\"$STORAGE_IN_USE\",\"storage_provider_id\":\"{{$backupSetting->storage_provider_id}}\",\"backup_settings_id\":\"{{$backupSetting->id}}\",\"database_size\":\"$DATABASE_SIZE\",\"free_storage\":\"$FREE_STORAGE\"}"

user_id=${1:-0}  # Set user_id to $1 if provided, otherwise default to 0.
#check if user_id is not 0
if [ $user_id -ne 0 ]; then
DATA="${DATA%?}, \"user_id\": \"$user_id\"}"
fi
curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure "$url" >/dev/null

echo "$ERROR_MESSAGE"
exit 1
fi
