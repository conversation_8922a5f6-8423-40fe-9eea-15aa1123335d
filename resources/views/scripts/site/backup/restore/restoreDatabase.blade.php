#Take current backup file
mkdir -p {{$old_path}}
mysqldump --single-transaction --skip-lock-tables --quick -u root -p{{ $server->database_password }} {{ $site->database_name }} > {{$old_path}}/{{ $site->database_name }}_${datetime}.sql 2>/dev/null
if [ $? -ne 0 ]; then
    @if($should_delete)
    echo "delete downloaded backup" >> "$log_file"
    rm -rf {{$full_path}}
    @endif
    #send call monitoring callback
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Backup extract fail for restore"}' --insecure "$url" >/dev/null
    exit 1
fi
#drop database
echo "drop database" >> "$log_file"
mysql -u root -p{{ $server->database_password }} -e "DROP DATABASE IF EXISTS {{ $site->database_name }}" 2>/dev/null
#create database
mysql -u root -p{{ $server->database_password }} -e "CREATE DATABASE IF NOT EXISTS {{ $site->database_name }}" 2>/dev/null
#restore database
echo "restore database" >> "$log_file"
mysql -u root -p{{ $server->database_password }} {{ $site->database_name }} < {{$full_path}} 2>/dev/null
if [ $? -eq 0 ]; then
    #delete old database
    echo "delete backup-old database" >> "$log_file"
    rm -rf {{$old_path}}/{{ $site->database_name }}_${datetime}.sql
    echo "delete download backup" >> "$log_file"
    @if($should_delete)
    echo "delete downloaded backup" >> "$log_file"
    rm -rf {{$full_path}}
   @endif
else
    #delete old backup
    @if($should_delete)
    echo "delete downloaded backup" >> "$log_file"
    rm -rf {{$full_path}}
    @endif
    #restore old backup
    echo "restore old backup" >> "$log_file"
    mysql -u root -p{{ $server->database_password }} {{ $site->database_name }} < {{$old_path}}/{{ $site->database_name }}_${datetime}.sql 2>/dev/null
    rm -rf {{$old_path}}/{{ $site->database_name }}_${datetime}.sql
    #send call monitoring callback
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{"error": "SQL Backup import fail for restore"}' --insecure "$url" >/dev/null
    exit 1
fi
