 #extract file
echo "extract file" >> "$log_file"
tar -xzvf {{$full_path}} -C /var/www > /dev/null
if [ $? -eq 0 ]; then
    @if($should_delete)
        #delete downloaded backup file
        echo "delete downloaded backup file" >> "$log_file"
        rm -rf {{$full_path}}
    @endif
    @if($site->name !== $backupDomain)
        #rename backupDomain to site name
        echo "rename backupDomain to site name" >> "$log_file"
        cp -r /var/www/{{$backupDomain}}/* /var/www/{{ $site->name}}/
        rm -r /var/www/{{$backupDomain}}
        chown -R {{$site->site_user}}:{{$site->site_user}} /var/www/{{ $site->name}}
sudo -i -u {{ $site->site_user }} bash << 'UPDATE_URL'
    cd /var/www/{{ $site->name }}
    if [ "{{$backupDomain}}" != "{{$site->name}}" ]; then
        @if($server->stack->isNginx())
            if {{ $site->wp_cli }} config --skip-plugins --skip-themes has WP_REDIS_PREFIX; then
            {{ $site->wp_cli }} config set WP_REDIS_PREFIX '{{ $site->redis_object_cache_key }}' --skip-plugins --skip-themes > /dev/null 2>&1 || echo "The redis prefix could not be set. Continuing..."
            fi
        @endif
        @if($server->stack->isOpenLiteSpeed())
            if {{ $site->wp_cli }} config --skip-plugins --skip-themes has LSOC_PREFIX; then
            {{ $site->wp_cli }} config set LSOC_PREFIX '{{ $site->redis_object_cache_key }}' --skip-plugins --skip-themes > /dev/null 2>&1 || echo "The redis prefix could not be set. Continuing..."
            fi
        @endif
    fi
UPDATE_URL
        echo "search and replace url" >> "$log_file"
        @include('scripts.site.searchReplaceUrl', [
            'site' => $site,
            'newDomain' => $site->name,
            'oldDomain' =>$backupDomain,
            'new_url' => $site->site_url
        ])
    @else
        chown -R {{$site->site_user}}:{{$site->site_user}} /var/www/{{ $site->name}}
    @endif
else
    #restore old backup
    echo "restore old backup" >> "$log_file"
    cp -r {{$site->backupFilesPath()}}/old/{{ $site->name}}_${datetime}/* /var/www/{{ $site->name}}/
    rm -r {{$site->backupFilesPath()}}/old/{{ $site->name}}_${datetime}

    #remove  file-manager*.php file from the site if exists
    if [ -f /var/www/{{ $site->name }}/file-manager-*.php ]; then
        echo "remove file-manager*.php file from the site" >> "$log_file"
        rm -f /var/www/{{ $site->name }}/file-manager-*.php
    fi
    #remove  adminer*.php file from the site if exists
    if [ -f /var/www/{{ $site->name }}/adminer*.php ]; then
        echo "remove adminer*.php file from the site" >> "$log_file"
        rm -f /var/www/{{ $site->name }}/adminer*.php
    fi
    @if($site->name !== $backupDomain)
        #rename backupDomain to site name
        echo "rename backupDomain to site name" >> "$log_file"
        cp -r /var/www/{{$backupDomain}}/* /var/www/{{ $site->name}}/
        rm -r /var/www/{{$backupDomain}}
    @else
        #rename backupDomain to site name
        echo "rename backupDomain to site name" >> "$log_file"
        cp -r /var/www/{{ $site->name}}/{{ $site->name}}/* /var/www/{{ $site->name}}/
        rm -r /var/www/{{ $site->name}}/{{ $site->name}}
    @endif
     @if($should_delete)
         rm -rf {{$full_path}}
     @endif

    #send call monitoring callback
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Backup extract fail for restore"}' --insecure "$url" >/dev/null
    exit 1
fi
