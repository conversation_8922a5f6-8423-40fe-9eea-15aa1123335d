#making backup directory
mkdir -p {{$site->backupFilesPath()}}/backup-remote/{{$site->backupDirName()}}
mkdir -p {{$site->backupFilesPath()}}/old
#server time
datetime=$(date +"%Y%m%d%H%M%S")

log_file={{$site->backupFilesPath()}}/backup.log

#check it has enough space to make backup
echo "check it has enough space to make backup" >> "$log_file"
required_space_in_mb={{$required_size}}
available_space_in_server=$(df -m / | tail -1 | awk '{print $4}')

#check required_space_in_mb is less than available_space_in_server or not
if [ $required_space_in_mb -gt $available_space_in_server ]; then
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Not enough space to restore backup"}' --insecure "$url" >/dev/null
    echo "Not enough space to make backup" >> {{$site->backupFilesPath()}}/backup.log
    exit 1
fi

@if(!$is_local)
    @include('scripts.site.backup.installAWSCLI',['site' => $site])
    @includeWhen(isset($storageProvider),'scripts.site.backup.s3Config',[
        'profile' => $s3Profile,
        'access_key_id' => $storageProvider->getAccessKeyId(),
        'secret_key' => $storageProvider->getSecretKey(),
        'region' => $storageProvider->getRegion(),
        'endpoint' => $storageProvider->getEndPoint()
    ])
    @if($file)
    #check if file exist
    /usr/local/bin/aws s3 ls --endpoint-url {{ $storageProvider->getEndPoint() }} s3://{{ $storageProvider->getBucket() }}/{{$s3Profile}}/{{ $file }} >/dev/null
    if [ $? -ne 0 ]; then
        url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
        curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Backup File not found"}' --insecure "$url" >/dev/null
        echo "File not found" >> {{$site->backupFilesPath()}}/backup.log
        exit 1
    fi
    echo "download tar from s3" >> "$log_file"
    #download Files from s3 using aws cli
    /usr/local/bin/aws s3api get-object --bucket {{ $storageProvider->getBucket() }} --key {{$s3Profile}}/{{$file}} {{$site->backupFileFullPath(path: $site->backupFilesPath(),file: $file,is_local: $is_local)}}  --endpoint-url {{ $storageProvider->getEndPoint() }} 2>/dev/null
    if [ $? -ne 0 ]; then
        url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
        curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Backup file download failed"}' --insecure "$url" >/dev/null
        echo "Backup file download failed" >> {{$site->backupFilesPath()}}/backup.log
        exit 1
    fi
    @endif
    @if($sqlFile)
        #check if sql file exist
        /usr/local/bin/aws s3 ls --endpoint-url {{ $storageProvider->getEndPoint() }} --profile {{$s3Profile}} s3://{{ $storageProvider->bucket }}/{{$s3Profile}}/{{ $sqlFile }} >/dev/null
        if [ $? -ne 0 ]; then
            url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
            curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Database file not found"}' --insecure "$url" >/dev/null
            echo "Database file not found" >> {{$site->backupFilesPath()}}/backup.log
            exit 1
        fi
        echo "download sql file from s3" >> "$log_file"
        #download sql from s3 using aws cli
        /usr/local/bin/aws s3api get-object --bucket {{ $storageProvider->getBucket() }} --key {{$s3Profile}}/{{$sqlFile}} {{$site->backupFileFullPath(path: $site->backupFilesPath(),file: $sqlFile,is_local: $is_local)}}  --endpoint-url {{ $storageProvider->getEndPoint() }} 2>/dev/null
        if [ $? -ne 0 ]; then
            url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
            curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Database file download failed"}' --insecure "$url" >/dev/null
            echo "Database file download failed" >> {{$site->backupFilesPath()}}/backup.log
            exit 1
        fi
    @endif
@endif

@if($sqlFile)
@include('scripts.site.backup.restore.restoreDatabase',[
    'server' => $server,
    'site' => $site,
    'full_path' => $site->backupFileFullPath(path: $site->backupFilesPath($backupDomain ?? null),file: $sqlFile, is_local: $is_local),
    'should_delete' => !$is_local,
    'old_path' => $site->backupFilesPath().'/old'

])
@endif
@if($file)
echo "backup and delete old site" >> "$log_file"
mv /var/www/{{ $site->name}} {{$site->backupFilesPath()}}/old/{{ $site->name}}_${datetime}
mkdir -p /var/www/{{ $site->name}}
chown -R {{$site->site_user}}:{{$site->site_user}} /var/www/{{ $site->name}}
@include('scripts.site.backup.restore.restoreFiles',[
    'site' => $site,
    'full_path' => $site->backupFileFullPath(path: $site->backupFilesPath($backupDomain),file: $file, is_local: $is_local),
    'should_delete' => !$is_local,
    'backupDomain' =>$backupDomain
])
@endif
#delete old backup directory
echo "delete old backup directory" >> "$log_file"
rm -rf {{$site->backupFilesPath()}}/old

#send restore time to monitoring
url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
curl -s -X POST -H 'Content-type: application/json' -d '{"restore_time": "'$datetime'"}' --insecure "$url" >/dev/null


@includeWhen($site->isWordpress() && $site->hasFullPageCaching(), 'scripts.site.cache.purgeObjectCache', ['site' => $site])
@includeWhen($site->isWordpress() && $site->hasRedisObjectCaching(), 'scripts.site.cache.purgeRedisObjectCache', ['site' => $site,'server' => $server])
@includeWhen($site->isLaravel(), 'scripts.laravel.postBackupRestore', ['site' => $site,'server' => $server, 'file' => $file])
