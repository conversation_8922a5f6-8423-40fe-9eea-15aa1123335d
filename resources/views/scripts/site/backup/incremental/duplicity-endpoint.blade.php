# Get the installed Duplicity version
duplicity_version=$(duplicity --version | awk '{print $2}')

# Compare the Duplicity version directly with 2.1.4
if dpkg --compare-versions "$duplicity_version" ge "0.9"; then
# If version is 2.1.4, execute this endpoint
endpoint="{{ $backupSetting->getIncrementalBackupFullPathWithS3Flag() }}"
else
# Otherwise, execute this endpoint
endpoint="{{ $backupSetting->getIncrementalBackupFullPath() }}"
fi
