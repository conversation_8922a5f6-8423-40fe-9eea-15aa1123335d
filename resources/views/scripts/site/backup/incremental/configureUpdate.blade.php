#making backup directory
mkdir -p {{$site->incrementalPath()}}
#log file
touch {{$site->incrementalPath()}}/backup.log
#server time
datetime=$(date +"%Y%m%d%H%M%S")
@include('scripts.site.backup.checkStorageForBackup',[ 'backupSetting' => $backupSetting, 'site' => $site, 'server' => $server])

#Configure duplicity
@include('scripts.site.backup.incremental.installDuplicity')

@if($isRemoteBackup)
@if($storageProvider->isBackBlaze())
#instll backblaze b2 client if storage provider is backblaze
#check b2sdk is installed or not

dpkg -l | grep python3-b2sdk >/dev/null
if [ $? -ne 0 ]; then
sudo apt-get install python3-b2sdk -y
pip install b2sdk
fi

@endif

# Check if boto3 is installed
python3 -c "import boto" 2>/dev/null
# Capture the exit code of the previous command
if [ $? -ne 0 ]; then
# Install boto3 using pip if not installed
pip3 install boto
fi

@if($storageProvider->isCloudflareR2())
# Check if boto3 is installed
python3 -c "import boto3" 2>/dev/null

# Capture the exit code of the previous command
if [ $? -ne 0 ]; then
    # Install boto3 using pip if not installed
    pip3 install boto3
fi
@endif
@include('scripts.site.backup.installAWSCLI')
#check aws cli configuration is invalid
@include('scripts.site.backup.s3Config',[
    'profile' => $site->backupDirName(),
    'access_key_id' => $storageProvider->getAccessKeyId(),
    'secret_key' => $storageProvider->getSecretKey(),
    'region' => $storageProvider->getRegion(),
    'endpoint' => $storageProvider->getEndPoint()
])

#check if s3 client work or not
/usr/local/bin/aws s3 ls s3://{{ $storageProvider->getBucket() }}  --endpoint-url={{ $storageProvider->getEndPoint() }} >/dev/null 2>&1
if [ $? -ne 0 ]; then
url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
curl -s -X POST -H 'Content-type: application/json' -d '{ "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "type": "'{{$type}}'",  "server_datetime": "'"$datetime"'", "error": "S3 client can not connect to s3 bucket"}' --insecure "$url" >/dev/null
echo "S3 client not working" >> {{$site->incrementalPath()}}/backup.log
exit 1
fi

@endif
