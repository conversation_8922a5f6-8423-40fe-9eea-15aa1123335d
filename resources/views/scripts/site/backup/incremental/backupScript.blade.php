#set datetime if already not set
if [ -z "$datetime" ]; then
datetime=$(date +"%Y%m%d%H%M%S")
fi
type={{ $type }}
#check log file is present or not
if [ ! -f {{$site->incrementalPath()}}/backup.log ]; then
    touch {{$site->incrementalPath()}}/backup.log
fi
@include('scripts.site.backup.incremental.configureUpdate',[
    'site' => $site,
    'backupSetting' => $backupSetting,
    'server' => $server,
    'isRemoteBackup' => $isRemoteBackup,
    'type' => $type
])
@include('scripts.site.backup.incremental.duplicity-endpoint',[
    'backupSetting' => $backupSetting
])

#function for write log
function write_log() {
    echo "$(date) $1" >> {{$site->incrementalPath()}}/backup.log
}

#function for send error callback
function send_error_callback() {
    write_log "$1"
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{ "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "type": "'{{$type}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "server_datetime": "'"$datetime"'", "error": "'"$1"'"}' --insecure "$url" >/dev/null
}

@if($type ==  \App\Enums\BackupType::INCREMENTAL)
#check already full backup is taken or not
has_no_full_backup=$(duplicity collection-status $endpoint | grep 'No backup chains with active signatures found')
if [ -n "$has_no_full_backup" ]; then
    write_log "Full backup is not taken yet"
    #change type to full
    type={{ \App\Enums\BackupType::FULL }}
fi
@endif

#check mysql is up and running
if ! mysqladmin -u {{$site->database_user}} -p{{ $site->database_password }} ping 2>/dev/null; then
    send_error_callback "MySQL service is not running"
    exit 1
fi

#check mysql is enabled and database is present
if [ -z "$(mysql -u {{$site->database_user}} -p{{ $site->database_password }} -e 'SHOW DATABASES;' 2>/dev/null | grep {{ $site->database_name }})" ]; then
    send_error_callback "Database: {{ $site->database_name }} is not present"
    exit 1
fi

#take mysql backup if database is enabled
DATABASE_SIZE=$(mysql -u {{$site->database_user}} -p{{ $site->database_password }} -e "SELECT table_schema AS DatabaseName, ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS SizeMB FROM information_schema.tables WHERE table_schema = '{{ $site->database_name }}' GROUP BY table_schema;" 2>/dev/null | awk '/{{ $site->database_name }}/ {print $2}')
MYSQL_DUMP={{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql
mysqldump --single-transaction --skip-lock-tables --quick -u root -p{{ $server->database_password }} {{ $site->database_name }} > {{$site->incrementalPath()}}/$MYSQL_DUMP 2>/dev/null
if [ $? -ne 0 ]; then
    rm -rf {{$site->incrementalPath()}}/$MYSQL_DUMP
    send_error_callback "Database backup failed"
    exit 1
fi

MYSQL_FILE_SIZE=$(stat -c %s {{$site->incrementalPath()}}/$MYSQL_DUMP | awk '{print $1/1024}')

backup_size=$(/usr/bin/duplicity $type -v2 --no-encryption  --allow-source-mismatch --exclude=/var/www/{{ $site->name }}/adminer*.php --exclude=/var/www/{{ $site->name }}/file-manager-*.php  @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='/var/www/{{ $site->name }}/{{$excludesPath}}' @endforeach /var/www/{{ $site->name }} $endpoint | grep "TotalDestinationSizeChange" | awk '{
    size = $2;
    if (size >= 1024*1024*1024) {
        printf("%.2f GB\n", size / (1024*1024*1024));
    } else if (size >= 1024*1024) {
        printf("%.2f MB\n", size / (1024*1024));
    } else if (size >= 1024) {
        printf("%.2f KB\n", size / 1024);
    } else {
        printf("%d Bytes\n", size);
    }
}')

if [ $? -ne 0 ]; then
    #Remove mysql dump file
    rm -rf {{$site->incrementalPath()}}/$MYSQL_DUMP
    send_error_callback "Backup failed"
    exit 1
fi

#check backup size is not empty
if [ -z "$backup_size" ]; then
    #Remove mysql dump file
    rm -rf {{$site->incrementalPath()}}/$MYSQL_DUMP
    send_error_callback "Backup failed"
    exit 1
fi

#remove duplicity cache files
rm -rf /root/.cache/duplicity/*

@if($isRemoteBackup)
manifest_file=$(/usr/local/bin/aws s3api list-objects-v2 --bucket {{ $storageProvider->getBucket() }} --prefix {{$site->backupDirName()}} --endpoint-url={{ $storageProvider->getEndPoint() }} --query "Contents[?contains(Key, '.manifest')].[Key, LastModified]" --output text | sort -k2 | tail -n 1 | awk '{print $1}')
#upload mysql dump to s3
/usr/local/bin/aws s3 cp {{$site->incrementalPath()}}/$MYSQL_DUMP s3://{{ $storageProvider->getBucket() }}/{{$site->backupDirName()}}/$MYSQL_DUMP --metadata '{"SiteID": "{{$site->id}}","ServerID": "{{$site->server_id}}"}' --endpoint-url {{ $storageProvider->getEndPoint() }} >/dev/null
#Remove mysql dump file
rm -rf {{$site->incrementalPath()}}/$MYSQL_DUMP
@else
manifest_file=$(ls -t  {{$site->incrementalPath()}}/duplicity-*.manifest | head -n 1)
@endif
DATA='{ "backup_settings_id": "{{$backupSetting->id}}", "type": "'"$type"'", "file_path": "{{$backupSetting->getIncrementalFilePath()}}", "mysql_path": "{{$backupSetting->getIncrementalMySqlPath()}}", "manifest_file": "'"$manifest_file"'", "backup_size": "'"$backup_size"'", "mysql_file": "'"$MYSQL_DUMP"'", "mysql_size": "'"$MYSQL_FILE_SIZE"'",  "server_datetime": "'"$datetime"'" }'

user_id=${1:-0}  # Set user_id to $1 if provided, otherwise default to 0.
#check if user_id is not 0
if [ $user_id -ne 0 ]; then
    DATA="${DATA%?}, \"user_id\": \"$user_id\"}"
fi

#Send Callback After Taking Backup
url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/store-incremental-backups') }}"

curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure "$url" >/dev/null

