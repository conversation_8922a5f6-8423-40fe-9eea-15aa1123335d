cat > {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh << 'SITE_BACKUP'
#!/bin/bash

mkdir -p {{$site->incrementalPath()}}
#create log file if not exist
if [ ! -f {{$site->incrementalPath()}}/backup.log ]; then
touch {{$site->incrementalPath()}}/backup.log
fi
# Get the installed Duplicity version
duplicity_version=$(duplicity --version | awk '{print $2}')

# Compare the Duplicity version directly with 2.1.4
if dpkg --compare-versions "$duplicity_version" ge "0.9"; then
    # If version is 2.1.4, execute this endpoint
    endpoint="{{ $backupSetting->getIncrementalBackupFullPathWithS3Flag() }}"
else
    # Otherwise, execute this endpoint
    endpoint="{{ $backupSetting->getIncrementalBackupFullPath() }}"
fi
@if($isRemoteBackup)
   #check aws cli configuration is invalid
   @include('scripts.site.backup.s3Config',[
        'profile' => $site->backupDirName(),
        'access_key_id' => $storageProvider->getAccessKeyId(),
        'secret_key' => $storageProvider->getSecretKey(),
        'region' => $storageProvider->getRegion(),
        'endpoint' => $storageProvider->getEndPoint()
    ])
    #Those are the environment variables that are used by the AWS CLI to authenticate with AWS S3
    export PASSPHRASE="{{ hashid_encode($server->id) }}"
    export AWS_REQUEST_CHECKSUM_CALCULATION=when_required
    export AWS_RESPONSE_CHECKSUM_VALIDATION=when_required

    HAS_NO_BACKUP=$(duplicity remove-older-than {{$backupSetting->delete_after_days}}D --dry-run $endpoint | grep "No old backup sets found, nothing deleted.")
    if [ -n "$HAS_NO_BACKUP" ]; then
        echo "$(date) No old backup sets found, nothing deleted." >> {{$site->incrementalPath()}}/backup.log
        exit 0
    fi
    older_timestamp=$(duplicity remove-older-than {{$backupSetting->delete_after_days}}D --dry-run $endpoint | tail -n 2 | head -n 1)
    DAYS_AGO=$(date -d "$older_timestamp" +"%Y-%m-%dT%H:%M:%SZ")
    last_full_backup_date=$(/usr/local/bin/aws s3api list-objects --bucket {{$storageProvider->getBucket()}} --prefix "{{ $site->backupDirName() }}/duplicity-full." --endpoint-url {{ $storageProvider->endpoint }} --query "Contents[?LastModified>'$DAYS_AGO'].[Key, LastModified]" --output text | sort -r | head -n 1 | awk '{print $2}')

    files=$(/usr/local/bin/aws s3api list-objects --bucket {{$storageProvider->getBucket()}} --prefix "{{ $site->backupDirName() }}" --query "Contents[?LastModified<'$last_full_backup_date'].[Key, LastModified]" --endpoint-url {{ $storageProvider->endpoint }} --output json)
    echo $files >> {{$site->incrementalPath()}}/backup.log
    # Use grep and sed to extract the "Key" values and format them as a Bash array
    keys_array=$(echo $files | sed -e 's/[][]//g' -e 's/"//g' | tr ',' '\n' | grep {{ $site->backupDirName() }} | awk '{print $1}')
    file_array=$(echo $files | sed -e 's/[][]//g' -e 's/"//g' | tr ',' '\n' | grep {{ $site->backupDirName() }} | awk '{print "\"" $1 "\""}' | paste -sd, -)

    # Print the array elements
    for key in $keys_array; do
    /usr/local/bin/aws s3api delete-object --bucket {{$storageProvider->getBucket()}} --key "$key" --endpoint-url {{ $storageProvider->endpoint }}
    done
    DATA='{"files": ['$file_array'],"backup_settings": "{{$backupSetting->id}}"}'
@else
    # Get the modification time of the latest duplicity file
    HAS_NO_BACKUP=$(duplicity remove-older-than {{$backupSetting->delete_after_days}}D --dry-run {{$site->incrementalFilesPath()}} | grep "No old backup sets found, nothing deleted.")
    if [ -n "$HAS_NO_BACKUP" ]; then
        echo "$(date) No old backup sets found, nothing deleted." >> {{$site->incrementalPath()}}/backup.log
        exit 0
    fi
    older_timestamp=$(duplicity remove-older-than {{$backupSetting->delete_after_days}}D --dry-run {{$site->incrementalFilesPath()}} | tail -n 2 | head -n 1)
    older_mtime_human=$(date -d "$older_timestamp" +"%Y-%m-%d %H:%M:%S")
    latest_file_path=$(find {{$site->incrementalPath()}} -type f -name "duplicity-full*" -newermt "$older_mtime_human" -printf '%T@ %p\n' | sort -n | cut -d ' ' -f 2- | head -1)
    latest_file_mtime=$(stat -c %Y "$latest_file_path")
    if [ -z "$latest_file_mtime" ]; then
        echo "$(date) Invalid latest file time format." >> {{$site->incrementalPath()}}/backup.log
        exit 0
    fi
    latest_file_mtime_human=$(date -d "@$latest_file_mtime" +"%Y-%m-%d %H:%M:%S")
    # Find files  older than latest_file_mtime_human
    files=$(find {{$site->incrementalPath()}} -type f ! -newermt "$latest_file_mtime_human" | sort -nr | awk '{printf "\"%s\",", $0}' | sed 's/,$/\n/')
    echo "$(date) Files to delete: $files" >> {{$site->incrementalPath()}}/backup.log
    # Find and delete the files
    find {{$site->incrementalPath()}} -type f ! -newermt "$latest_file_mtime_human" | sort -nr | tr '\n' '\0' | xargs -0 -I {} sh -c 'rm -v "{}"'
    existing_files=$(find {{$site->incrementalPath()}} -type f \( -name "{*.gz" -o -name "{{$site->name}}*.sql" \) -exec basename {} \; | sed 's/'\''/\\&/g' | sed 's/.*/"&"/' | paste -sd,)
    DATA='{"files": ['$files'],"existing_files": ['$existing_files'],"storage_provider_id": "'{{$backupSetting->storage_provider_id}}'","backup_settings": "{{$backupSetting->id}}"}'
@endif

url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/deleteBackup') }}"
curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure "$url" >/dev/null

SITE_BACKUP

chmod +x {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh

rm -f {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.xc
# Encrypt Script
shc -f {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh -o  {{$site->backupScriptsPath()}}/dailyDelete{{$isRemoteBackup? 'Remote': 'Local'}}Backup.xc
rm  {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh.x.c
rm  {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh
