mkdir -p {{$site->backupScriptsPath()}}

cat > {{$site->backupScriptsPath()}}/siteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh << 'SITE_BACKUP'
#!/bin/bash
@include('scripts.site.backup.incremental.backupScript', [
    'site' => $site,
    'backupSetting' => $backupSetting,
    'storageProvider'=>$storageProvider,
    'server'=>$server,
    'isRemoteBackup'=>$isRemoteBackup,
    'type' => \App\Enums\BackupType::FULL //'full'
 ])
SITE_BACKUP

cat > {{$site->backupScriptsPath()}}/siteIncBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh << 'SITE_BACKUP'
#!/bin/bash
@include('scripts.site.backup.incremental.backupScript', [
    'site' => $site,
    'backupSetting' => $backupSetting,
    'storageProvider'=>$storageProvider,
    'server'=>$server,
    'isRemoteBackup'=>$isRemoteBackup,
    'type' => \App\Enums\BackupType::INCREMENTAL //'incremental'
 ])
SITE_BACKUP

@if($isRemoteBackup)
    rm -rf {{$site->backupScriptsPath()}}/*Remote*.xc
    rm -rf {{$site->backupScriptsPath()}}/*remote*.xc
@else
    rm -rf {{$site->backupScriptsPath()}}/*Local*.xc
    rm -rf {{$site->backupScriptsPath()}}/*local*.xc
@endif

chmod +x {{$site->backupScriptsPath()}}/siteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh
chmod +x {{$site->backupScriptsPath()}}/siteIncBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh

rm -f {{$site->backupScriptsPath()}}/{{$backupSetting->getFrequencyCronFile()}}
rm -f {{$site->backupScriptsPath()}}/{{$backupSetting->getIncrementalFrequencyCronFile()}}

# Encrypt Script
shc -f {{$site->backupScriptsPath()}}/siteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh -o  {{$site->backupScriptsPath()}}/{{$backupSetting->getFrequencyCronFile()}}
shc -f {{$site->backupScriptsPath()}}/siteIncBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh -o  {{$site->backupScriptsPath()}}/{{$backupSetting->getIncrementalFrequencyCronFile()}}

rm  {{$site->backupScriptsPath()}}/siteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh.x.c
rm  {{$site->backupScriptsPath()}}/siteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh
rm  {{$site->backupScriptsPath()}}/siteIncBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh.x.c
rm  {{$site->backupScriptsPath()}}/siteIncBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh
