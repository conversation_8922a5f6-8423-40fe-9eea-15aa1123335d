#making backup directory
mkdir -p {{$site->incrementalPath()}}/backup-remote/{{$site->backupDirName()}}
mkdir -p {{$site->incrementalPath()}}/old
#server time
datetime=$(date +"%Y%m%d%H%M%S")

log_file={{$site->incrementalPath()}}/backup.log
@if(!$is_local)
#check aws cli configuration is invalid
@include('scripts.site.backup.s3Config',[
    'profile' => $site->backupDirName(),
    'access_key_id' => $storageProvider->getAccessKeyId(),
    'secret_key' => $storageProvider->getSecretKey(),
    'region' => $storageProvider->getRegion(),
    'endpoint' => $storageProvider->getEndPoint()
])
# Get the installed Duplicity version
duplicity_version=$(duplicity --version | awk '{print $2}')

# Compare the Duplicity version directly with 2.1.4
if dpkg --compare-versions "$duplicity_version" ge "0.9"; then
# If version is higher than 0.9, execute this endpoint
endpoint="{{ $storageProvider->getEndPointWithS3Flag().DIRECTORY_SEPARATOR.$site->backupDirName() }}"
else
# Otherwise, execute this endpoint
endpoint="{{ $storageProvider->getS3EndPoint().DIRECTORY_SEPARATOR.$site->backupDirName() }}"
fi
@endif
@if($file && $restoreTime)
    rm -rf /var/www/{{$site->name}}
    duplicity restore -t {{$restoreTime}} --no-encryption @if($is_local) {{ str($filePath)->remove('~/')->prepend('file:///root/') }} @else $endpoint @endif /var/www/{{$site->name}}
    chown -R {{$site->site_user}}:{{$site->site_user}} /var/www/{{$site->name}}
@endif
@if($sqlFile)
    @if(!$is_local)
        /usr/local/bin/aws s3api get-object --bucket {{ $storageProvider->getBucket() }} --key {{$site->backupDirName()}}/{{$sqlFile}} {{$site->incrementalPath()}}/backup-remote/{{$sqlFile}} --endpoint-url {{ $storageProvider->getEndPoint() }}
        if [ $? -ne 0 ]; then
            url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
            curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Backup Database download failed"}' --insecure "$url" >/dev/null
            echo "Database download failed"
            exit 1
        fi
        @include('scripts.site.backup.restore.restoreDatabase',[
            'server' => $server,
            'site' => $site,
            'full_path' => $site->incrementalPath().'/backup-remote/'.$sqlFile,
            'old_path' => $site->incrementalPath().'/old',
            'should_delete' => !$is_local
        ])
    @else
        @include('scripts.site.backup.restore.restoreDatabase',[
           'server' => $server,
           'site' => $site,
           'full_path' => $filePath.DIRECTORY_SEPARATOR.$sqlFile,
           'old_path' => $site->incrementalPath().'/old',
           'should_delete' => !$is_local
       ])
    @endif
@endif

#remove  adminer*.php file from the site if exists
if [ -f {{$site->manager()->siteDocumentRoot()}}/adminer*.php ]; then
    echo "Remove adminer*.php file from the site"
    rm -f {{$site->manager()->siteDocumentRoot()}}/adminer*.php
fi
#remove  file-manager*.php file from the site if exists
if [ -f {{$site->manager()->siteDocumentRoot()}}/file-manager-*.php ]; then
    echo "Remove file-manager*.php file from the site"
    rm -f {{$site->manager()->siteDocumentRoot()}}/file-manager-*.php
fi

@if($site->isWordpress())
sudo -i -u {{ $site->site_user }} bash << 'UPDATE_URL'
# cd to the directory
cd {{$site->manager()->siteDocumentRoot()}}
oldDomain={{ $site->wp_cli }} option get siteurl --skip-plugins --skip-themes | sed 's/^http[s]\?:\/\///'
if [ "$oldDomain" != "{{$site->name}}" ]; then
    {{ $site->wp_cli }} search-replace "$oldDomain" {{ $site->name }} --all-tables-with-prefix --verbose --skip-plugins --skip-themes > /dev/null 2>&1 || true
    {{ $site->wp_cli }} option update HOME "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    {{ $site->wp_cli }} option update SITEURL "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1

    {{ $site->wp_cli }} config set WP_HOME "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    {{ $site->wp_cli }} config set WP_SITEURL "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    @if($site->isMultiSite())
        {{ $site->wp_cli }} config set DOMAIN_CURRENT_SITE "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    @endif

    @if($server->stack->isNginx())
    if {{ $site->wp_cli }} config --skip-plugins --skip-themes has WP_REDIS_PREFIX; then
    {{ $site->wp_cli }} config set WP_REDIS_PREFIX '{{ $site->redis_object_cache_key }}' --skip-plugins --skip-themes > /dev/null 2>&1 || echo "The redis prefix could not be set. Continuing..."
    fi
    @endif
    @if($server->stack->isOpenLiteSpeed())
    if {{ $site->wp_cli }} config --skip-plugins --skip-themes has LSOC_PREFIX; then
    {{ $site->wp_cli }} config set LSOC_PREFIX '{{ $site->redis_object_cache_key }}' --skip-plugins --skip-themes > /dev/null 2>&1 || echo "The redis prefix could not be set. Continuing..."
    fi
    @endif
fi
UPDATE_URL
if [ -f /var/www/{{$site->name}}/wp-content/object-cache.php ]; then
    @include('scripts.site.cache.installRedisObjectCache', ['site' => $site])
fi
@includeWhen($site->hasFullPageCaching(), 'scripts.site.cache.purgeObjectCache', ['site' => $site])
@includeWhen($site->hasRedisObjectCaching(), 'scripts.site.cache.purgeRedisObjectCache', ['site' => $site,'server' => $server])
@endif

@includeWhen($site->isLaravel(), 'scripts.laravel.postBackupRestore', ['site' => $site,'server' => $server, 'file' => $file])
