/usr/local/bin/aws configure set profile.{{ $profile }}.s3.signature_version s3
/usr/local/bin/aws configure set profile.{{ $profile }}.region {{ $region }}
/usr/local/bin/aws configure set profile.{{ $profile }}.endpoint_url {{ $endpoint }}
/usr/local/bin/aws configure set profile.{{ $profile }}.response_checksum_validation when_required
/usr/local/bin/aws configure set profile.{{ $profile }}.request_checksum_calculation when_required
/usr/local/bin/aws configure set aws_access_key_id {{ $access_key_id }} --profile {{ $profile }}
/usr/local/bin/aws configure set aws_secret_access_key {{ $secret_key }} --profile {{ $profile }}
