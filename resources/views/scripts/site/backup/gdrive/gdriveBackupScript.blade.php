#!/bin/bash

# Constants
BACKUP_PATH={{$site->backupFilesPath()}}
LOG_FILE="$BACKUP_PATH/backup.log"
DATETIME=$(date +"%Y%m%d%H%M%S")
CALLBACK_URL="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"

# Logging function
log_message() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") $1" >> $LOG_FILE
}

@include('scripts.site.backup.gdrive.gdriveConfig',[
    'backupSetting' => $backupSetting
])

# Send error callback
send_error_callback() {
    local error_message=$1
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{"storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "server_datetime": "'"$DATETIME"'", "backup_settings_id": "'{{$backupSetting->id}}'", "error": "'"$error_message"'"}' --insecure "$url" >/dev/null
    # Remove the backup files if the backup fails
    rm -rf $FULL_FILE_PATH $FULL_SQL_FILE_PATH
}

# Backup Database
backup_database() {
    # $1 is the full path of the sql file
    log_message "Database backup started"
    mysqldump -u root -p{{ $server->database_password }} --single-transaction --skip-lock-tables {{ $site->database_name }} > $1 2>/dev/null
    if [ $? -ne 0 ]; then
        log_message "Database backup failed"
        send_error_callback "Database backup failed"
        exit 1
    fi
}

# Backup Site Files
backup_files() {
    # $1 is the full path of the tar file
    log_message "Site backup started"
    tar -czvf $1 -C /var/www --exclude={{$site->name}}/adminer*.php --exclude={{$site->name}}/file-manager-*.php --warning=no-file-changed @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='{{$site->name}}/{{$excludesPath}}'  @endforeach {{$site->name}} > /dev/null
    if [ ! -f $1 ]; then
        log_message "tar file not created"
        send_error_callback "Site backup failed"
        exit 1
    fi
}

# Check if Google Drive has enough space
check_drive_space() {
    #$1 is the access token
    #$2 is the required space
    storage_info=$(curl -s -X GET -H "Authorization: Bearer $1" "https://www.googleapis.com/drive/v3/about?fields=storageQuota")
    limit=$(echo "$storage_info" | grep -o '"limit": *"[0-9]*"' | sed -E 's/.*"limit": *"([0-9]+)".*/\1/')
    usage=$(echo "$storage_info" | grep -o '"usage": *"[0-9]*"' | sed -E 's/.*"usage": *"([0-9]+)".*/\1/')
    available_space=$((limit - usage))
    log_message "Available space: $available_space KB"

    if [ "$(echo "$available_space < $2" | bc -l)" -eq 1 ]; then
        log_message "Google Drive has insufficient space"
        send_error_callback "Google Drive has insufficient space"
        exit 1
    fi
}

# Upload file to Google Drive
upload_to_drive() {
    mime_type=$(file --mime-type -b $1)
    file_name=$(basename $1)
    response=$(curl -X POST -H "Authorization: Bearer $2" \
                -F "metadata={name : '$file_name', parents: ['$3']};type=application/json;charset=UTF-8" \
                -F "file=@$1;type=$mime_type" \
                -o response.json \
                -w "%{http_code}" \
                "https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart")

    if [ "$response" -ne 200 ]; then
        #remove the file if the upload fails
        rm -rf $1
        log_message "Failed to upload: $response"
        send_error_callback "Failed to upload $4"
        exit 1
    fi
}

# Main script
log_message "Backup started"
mkdir -p $BACKUP_PATH
touch $LOG_FILE

@include('scripts.site.backup.checkStorageForBackup', [ 'backupSetting' => $backupSetting, 'site' => $site, 'server' => $server])
dir_size=$(($(du -s @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='/var/www/{{$site->name}}/{{$excludesPath}}'  @endforeach /var/www/{{ $site->name }} | awk '{print $1}') * 1024 / 1000000))
FULL_SQL_FILE_PATH="$BACKUP_PATH/{{$site->name}}_s_${DATABASE_SIZE}_${DATETIME}.sql"
FULL_FILE_PATH="$BACKUP_PATH/{{$site->name}}_s_${dir_size}_${DATETIME}.tar.gz"

ACCESS_TOKEN=$(refresh_access_token $CALLBACK_URL)
if [ -z "$ACCESS_TOKEN" ]; then
    send_error_callback "Failed to fetch access token."
    exit 1
fi
FOLDER_ID=$(get_folder_id $ACCESS_TOKEN {{$storageProvider->endpoint}} {{$site->backupDirName()}})
if [ -z "$FOLDER_ID" ]; then
    send_error_callback "Failed to fetch folder id."
    exit 1
fi

@if($backupSetting->files)
backup_files $FULL_FILE_PATH
@endif

@if($backupSetting->database)
backup_database $FULL_SQL_FILE_PATH
@endif

#check if the files are created successfully
if [ ! -f $FULL_FILE_PATH ] && [ ! -f $FULL_SQL_FILE_PATH ]; then
    log_message "Backup failed"
    send_error_callback "Backup failed"
    rm -rf $FULL_FILE_PATH $FULL_SQL_FILE_PATH
    exit 1
fi

# Get the size of each file in bytes
file_size=$(stat -c %s "$FULL_FILE_PATH")
sql_file_size=$(stat -c %s "$FULL_SQL_FILE_PATH")

# Sum the sizes
total_size=$((file_size + sql_file_size))

check_drive_space $ACCESS_TOKEN $total_size

upload_to_drive $FULL_FILE_PATH  $ACCESS_TOKEN $FOLDER_ID 'File'
rm -rf $FULL_FILE_PATH

#Refresh the access token again
ACCESS_TOKEN=$(refresh_access_token $CALLBACK_URL)
upload_to_drive $FULL_SQL_FILE_PATH $ACCESS_TOKEN $FOLDER_ID 'Database'
rm -rf $FULL_SQL_FILE_PATH

#File Size in KB
file_size=$(($file_size / 1024))
sql_file_size=$(($sql_file_size / 1024))

file_name=$(basename $FULL_FILE_PATH)
database_name=$(basename $FULL_SQL_FILE_PATH)

# Send callback with data
DATA='{"file": "'"$FOLDER_ID"'", "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'",  "file_size": "'"$file_size"'","taken_size": "'"$dir_size"'", "server_datetime": "'"$DATETIME"'", "db_file_size": "'"$sql_file_size"'", "is_remote": "true", "file_name" : "'"$file_name"'", "database": "'"$FOLDER_ID"'", "database_name" :  "'"$database_name"'"}'

user_id=${1:-0}  # Set user_id to $1 if provided, otherwise default to 0.
#check if user_id is not 0
if [ $user_id -ne 0 ]; then
    DATA="${DATA%?}, \"user_id\": \"$user_id\"}"
fi

url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure "$url" >/dev/null

log_message "Backup completed"
exit 0
