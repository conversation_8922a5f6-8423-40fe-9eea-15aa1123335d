#install aws cli if not installed
if ! [ -x "$(command -v /usr/local/bin/aws)" ]; then
    #clear old /root/.aws/credentials
    echo "" > /root/.aws/credentials
    echo "" > /root/.aws/config
    sudo sed -i "s/#precedence ::ffff:0:0\/96  100/precedence ::ffff:0:0\/96  100/" /etc/gai.conf

    # Install Python Httpie
    pip3 install httpie  ## skip for now

    #install aws cli
    #check is it ARM Server or not
    if [ "$(uname -m)" = "aarch64" ]; then
        curl -s -o awscliv2.zip "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" 2>/dev/null
    else
        curl -s -o awscliv2.zip "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" 2>/dev/null
    fi
    if [ $? -ne 0 ]; then
        echo "Failed to download AWS CLI installer"
        pip3 install awscliv2
    else
        #unzip the installer without showing progress output
        unzip -qq awscliv2.zip
        sudo ./aws/install --update > /dev/null
        rm -rf awscliv2.zip
        rm -rf ./aws
    fi

fi
# add break-system-packages = true to /etc/pip.conf if not exists
# Define the path to the pip.conf file
PIP_CONF="/etc/pip.conf"

# Check if the file already exists
if [ -f "$PIP_CONF" ]; then
    # Append the line if it doesn't already exist
    if ! grep -q "break-system-packages" "$PIP_CONF"; then
        echo -e "\n[global]\nbreak-system-packages = true" >> "$PIP_CONF"
    fi
else
    # Create the file and add the line
    echo -e "[global]\nbreak-system-packages = true" | sudo tee "$PIP_CONF" > /dev/null
fi
