cat > {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh << 'SITE_BACKUP'
#!/bin/bash
mkdir -p {{$site->backupScriptsPath()}}
#create log file if not exist
if [ ! -f {{$site->backupScriptsPath()}}/backup.log ]; then
touch {{$site->backupScriptsPath()}}/backup.log
fi
@if($isRemoteBackup)
    @include('scripts.site.backup.installAWSCLI')
    #check aws cli configuration is invalid
    @include('scripts.site.backup.s3Config',[
        'profile' => $site->backupDirName(),
        'access_key_id' => $storageProvider->getAccessKeyId(),
        'secret_key' => $storageProvider->getSecretKey(),
        'region' => $storageProvider->getRegion(),
        'endpoint' => $storageProvider->getEndPoint()
    ])

    #check if s3 client work or not
    /usr/local/bin/aws s3 ls s3://{{ $storageProvider->getBucket() }}  --endpoint-url={{ $storageProvider->getEndPoint() }} >/dev/null
    if [ $? -ne 0 ]; then
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/deleteBackup') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{"storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings": "'{{$backupSetting->id}}'","error": "The S3 client is unable to establish a connection."}' --insecure "$url" >/dev/null
    echo "S3 client not working on delete script" >> {{$site->backupFilesPath()}}/backup.log
    exit 1
    fi
    echo "Deleting remote backup files older than {{$backupSetting->delete_after_days}} days" >> {{$site->backupFilesPath()}}/backup.log
    #print the command
    files=$(/usr/local/bin/aws s3api list-objects --bucket {{$storageProvider->getBucket()}} --prefix "{{ $site->backupDirName() }}" --query "Contents[?LastModified<='$(date -d '{{$backupSetting->delete_after_days}} days ago' --utc '+%Y-%m-%dT%H:%M:%S.%NZ')']" --endpoint-url {{ $storageProvider->getEndPoint() }} --output json)
    echo $files >> {{$site->backupFilesPath()}}/backup.log
    # Use grep and sed to extract the "Key" values and format them as a Bash array
    keys_array=($(echo "$files" | grep -oP '"Key": "\K[^"]+' | sed 'N;s/\n/ /'))

    # Print the array elements
    for key in "${keys_array[@]}"; do
    /usr/local/bin/aws s3api delete-object --bucket {{$storageProvider->getBucket()}} --key "$key" --endpoint-url {{ $storageProvider->endpoint }}
    done
    DATA='{"files": ['$files'],"backup_settings": "{{$backupSetting->id}}"}'
@else
    echo "Deleting local backup files older than {{$backupSetting->delete_after_days}} days" >> {{$site->backupFilesPath()}}/backup.log
    files=$(find {{$site->backupFilesPath()}} -type f \( -name "{{$site->name}}*.tar.gz" -o -name "{{$site->name}}*.sql" \) -ctime +{{$backupSetting->deleteAfterDaysInLocal()}} -exec basename {} \; | sed 's/'\''/\\&/g' | sed 's/.*/"&"/' | paste -sd,)
    echo $files >> {{$site->backupFilesPath()}}/backup.log
    find {{$site->backupFilesPath()}} -type f \( -name "{{$site->name}}*.tar.gz" -o -name "{{$site->name}}*.sql" \) -ctime +{{$backupSetting->deleteAfterDaysInLocal()}} -exec rm {} +
    existing_files=$(find {{$site->backupFilesPath()}} -type f \( -name "{{$site->name}}*.tar.gz" -o -name "{{$site->name}}*.sql" \) -exec basename {} \; | sed 's/'\''/\\&/g' | sed 's/.*/"&"/' | paste -sd,)
    DATA='{"files": ['$files'],"existing_files": ['$existing_files'],"storage_provider_id": "'{{$backupSetting->storage_provider_id}}'","backup_settings": "{{$backupSetting->id}}"}'
@endif

url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/deleteBackup') }}"
curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure "$url" >/dev/null

SITE_BACKUP

chmod +x {{$site->backupScriptsPath()}}/deleteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh


