#!/bin/bash

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null
then
  echo "gcloud CLI not found. Installing Google Cloud SDK silently..."

  # Update and install necessary packages
  sudo apt update -y && sudo apt-get install apt-transport-https ca-certificates gnupg curl -y

  # Import the Google Cloud public key
  curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg

  # Update and install Google Cloud SDK
echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list

  echo "Google Cloud SDK installed successfully."

  # Authenticate and set a project if needed (optional step)
  # gcloud auth activate-service-account --key-file=/path/to/service-account.json

else
  echo "gcloud CLI is already installed."
  gcloud --version
fi
