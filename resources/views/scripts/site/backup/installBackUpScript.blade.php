@include('scripts.site.backup.GenerateSiteBackupScript', [
    'site' => $site,
    'backupSetting' => $backupSetting,
     'storageProvider'=>$storageProvider,
     'server'=>$server,
     'isRemoteBackup'=>$isRemoteBackup
])

@includeWhen($backupSetting->auto_delete && $backupSetting?->isGDrive(),'scripts.site.backup.gdrive.gdriveDeleteBackupScript', [
    'site' => $site,
    'backupSetting' => $backupSetting,
    'isRemoteBackup'=>$isRemoteBackup,
    'storageProvider'=>$storageProvider,
])

@includeWhen(!$backupSetting?->isGDrive() && $backupSetting->auto_delete,'scripts.site.backup.GenerateDeleteBackupScript', [
    'site' => $site,
    'backupSetting' => $backupSetting,
    'isRemoteBackup'=>$isRemoteBackup,
    'storageProvider'=>$storageProvider,
])

@include('scripts.site.backup.encryptSiteBackupScript', ['site' => $site,'isRemoteBackup'=>$isRemoteBackup, 'auto_delete' => $backupSetting->auto_delete])

@include('scripts.site.backup.GenerateCorn', [
    'site' => $site,
    'backupSetting'=>$backupSetting,
    'isRemoteBackup'=>$isRemoteBackup,
    'site_name' => str_replace('.', '_', $site->name)
])

@if($take_backup)
    # Run Backup Script Now
    cd {{$site->backupScriptsPath()}}/ && ./{{$backupSetting->getFileName()}} > /dev/null 2>&1
@endif
