mkdir -p {{$site->backupScriptsPath()}}

cat > {{$site->backupScriptsPath()}}/siteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh << 'SITE_BACKUP'
#!/bin/bash
@includeWhen(!$backupSetting->isGDrive(),'scripts.site.backup.backupScript', [
    'site' => $site,
    'backupSetting' => $backupSetting,
     'storageProvider'=>$storageProvider,
     'server'=>$server,
     'isRemoteBackup'=>$isRemoteBackup
 ])
@includeWhen($backupSetting->isGDrive(),'scripts.site.backup.gdrive.gdriveBackupScript', [
    'site' => $site,
    'backupSetting' => $backupSetting,
     'storageProvider'=>$storageProvider,
     'server'=>$server,
     'isRemoteBackup'=>$isRemoteBackup
 ])
SITE_BACKUP

chmod +x {{$site->backupScriptsPath()}}/siteBackup_{{$isRemoteBackup? 'remote': 'local'}}.sh
