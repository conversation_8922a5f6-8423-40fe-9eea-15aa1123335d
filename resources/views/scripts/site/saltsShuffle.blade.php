#
# REQUIRES:
#   - site
#
# Run the command with site user
sudo -i -u {{ $site->site_user }} bash << 'EOF'
# check if wp-config.php file exists
if [ ! -f /var/www/{{ $site->name }}/wp-config.php ]; then
    echo "wp-config.php file not found"
    exit 1
fi

# check if wp-config.php has "put your unique phrase here" text
if ! grep -q "put your unique phrase here" /var/www/{{ $site->name }}/wp-config.php; then
    echo "wp-config.php file does not contain 'put your unique phrase here' text"
    exit 1
fi

{{ $site->wp_cli }} config shuffle-salts --path=/var/www/{{ $site->name }}
echo "Salts shuffled successfully"
EOF
