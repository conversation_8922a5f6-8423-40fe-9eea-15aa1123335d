#
# REQUIRES:
#   - clonedSite (the site instance)
#   - fromSite (the site instance)
#


set -e

### Step 1: Copy everything from old site directory to new site directory

rsync -av /var/www/{{ $fromSite->name }}/ /var/www/{{ $clonedSite->name }}/ --exclude='wp-content/mu-plugins/xcloud-auto-login.php'

# remove old wp-content/object-cache.php file if exists
if [ -f /var/www/{{$clonedSite->name}}/wp-content/object-cache.php ]; then
rm -f /var/www/{{$clonedSite->name}}/wp-content/object-cache.php
fi

if [ -f /var/www/{{$clonedSite->name}}/wp-content/advanced-cache.php ]; then
rm -f /var/www/{{$clonedSite->name}}/wp-content/advanced-cache.php
fi

if [ -f /var/www/{{$clonedSite->name}}/wp-content/.litespeed_conf.dat ]; then
rm -f /var/www/{{$clonedSite->name}}/wp-content/.litespeed_conf.dat
fi

if [ -d /var/www/{{$clonedSite->name}}/wp-content/plugins/redis-cache ]; then
rm -rf /var/www/{{$clonedSite->name}}/wp-content/plugins/redis-cache
fi

if [ -d /var/www/{{$clonedSite->name}}/wp-content/plugins/litespeed-cache ]; then
rm -rf /var/www/{{$clonedSite->name}}/wp-content/plugins/litespeed-cache
fi

### Step 2: Export old site database
@if($fromSite->hasDatabase())
OLD_SITE_DB_NAME=$({{ $fromSite->wp_cli }} config get DB_NAME --path=/var/www/{{ $fromSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
OLD_SITE_DB_USER=$({{ $fromSite->wp_cli }} config get DB_USER --path=/var/www/{{ $fromSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
OLD_SITE_DB_PASSWORD=$({{ $fromSite->wp_cli }} config get DB_PASSWORD --path=/var/www/{{ $fromSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
OLD_SITE_DB_HOST=$({{ $fromSite->wp_cli }} config get DB_HOST --path=/var/www/{{ $fromSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)

# Split DB_HOST into host and port
OLD_SITE_DB_PORT=${OLD_SITE_DB_HOST##*:} {{-- i.e extract port from localhost:3306 --}}
OLD_SITE_DB_HOST=${OLD_SITE_DB_HOST%:*} {{-- i.e extract host name from localhost:3306 --}}

# Set default port if not specified (i.e localhost:3306)
if [ -z "$OLD_SITE_DB_PORT" ]; then
OLD_SITE_DB_PORT=3306
fi

# if port value is empty (i.e localhost)
if [ "$OLD_SITE_DB_PORT" == "$OLD_SITE_DB_HOST" ]; then
OLD_SITE_DB_PORT=3306
fi

# if the port is not specified, use the host as the port
if [ "$OLD_SITE_DB_PORT" == "$OLD_SITE_DB_HOST" ]; then
OLD_SITE_DB_PORT=3306
fi

echo $OLD_SITE_DB_NAME
echo $OLD_SITE_DB_USER
echo $OLD_SITE_DB_PASSWORD
echo $OLD_SITE_DB_HOST
echo $OLD_SITE_DB_PORT

# Dump the database into a temporary location
TMP_DIR="/tmp"
DATE=$(date +%Y-%m-%d-%H-%M-%S)
DB_DUMP_FILE="$TMP_DIR/$OLD_SITE_DB_NAME-$DATE.sql"
echo "dumping db: $DB_DUMP_FILE"
mysqldump -h $OLD_SITE_DB_HOST -u $OLD_SITE_DB_USER --password=$OLD_SITE_DB_PASSWORD $OLD_SITE_DB_NAME {{ $fromSite->server->database_type === \App\Enums\DatabaseType::MYSQL_8 ? '--no-tablespaces' : '--single-transaction' }} > $DB_DUMP_FILE

### Step 3: Update wp-config.php according to new site

{{ $clonedSite->wp_cli }} config set DB_NAME {{ $clonedSite->database_name }} --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
{{ $clonedSite->wp_cli }} config set DB_USER {{ $clonedSite->database_user }} --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
{{ $clonedSite->wp_cli }} config set DB_PREFIX {{  $clonedSite->prefix ?: "wp{$clonedSite->id}_" }} --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
{{ $clonedSite->wp_cli }} config set DB_PASSWORD {{ $clonedSite->database_password }} --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
{{ $clonedSite->wp_cli }} config set DB_HOST {{ $clonedSite->database_host ?: 'localhost' }}{{ $clonedSite->database_port ? ":{$clonedSite->database_port}" : '' }} --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet

### Step 4: Import the old site database into new site database

NEW_SITE_DB_NAME=$({{ $clonedSite->wp_cli }} config get DB_NAME --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
NEW_SITE_DB_USER=$({{ $clonedSite->wp_cli }} config get DB_USER --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
NEW_SITE_DB_PASSWORD=$({{ $clonedSite->wp_cli }} config get DB_PASSWORD --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
NEW_SITE_DB_HOST=$({{ $clonedSite->wp_cli }} config get DB_HOST --path=/var/www/{{ $clonedSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)

# Split NEW_SITE_DB_HOST into host and port
NEW_SITE_DB_PORT=${NEW_SITE_DB_HOST##*:} {{-- i.e extract port from localhost:3306 --}}
NEW_SITE_DB_HOST=${NEW_SITE_DB_HOST%:*} {{-- i.e extract host name from localhost:3306 --}}

# Set default port if not specified (i.e localhost:3306)
if [ -z "$NEW_SITE_DB_PORT" ]; then
NEW_SITE_DB_PORT=3306
fi

# if port value is empty (i.e localhost)
if [ "$NEW_SITE_DB_PORT" == "$NEW_SITE_DB_HOST" ]; then
NEW_SITE_DB_PORT=3306
fi

echo "$NEW_SITE_DB_NAME"
echo $NEW_SITE_DB_USER
echo $NEW_SITE_DB_PASSWORD
echo $NEW_SITE_DB_HOST

mysql -f -h $NEW_SITE_DB_HOST -u $NEW_SITE_DB_USER --password="$NEW_SITE_DB_PASSWORD" $NEW_SITE_DB_NAME < $DB_DUMP_FILE

echo "Database dump imported into $NEW_SITE_DB_NAME database"


chown -R {{ $clonedSite->site_user }}:{{ $clonedSite->site_user }} /var/www/{{ $clonedSite->name }}

### Step 4: Remove the tmp database (cleaning up)
echo "deleting database dump: $DB_DUMP_FILE"
sudo rm $DB_DUMP_FILE
@endif

#Remove old cache plugins configurations
sudo -i -u {{ $clonedSite->site_user }} bash << 'SITE_CACHE'
# cd to the directory
cd /var/www/{{ $clonedSite->name }}
#remove previous cache plugins
{{ $clonedSite->wp_cli }} plugin delete redis-cache --skip-plugins --skip-themes --skip-packages --quiet
{{ $clonedSite->wp_cli }} plugin delete litespeed-cache --skip-plugins --skip-themes --skip-packages --quiet
if {{ $clonedSite->wp_cli }} config has WP_REDIS_PREFIX; then
{{ $clonedSite->wp_cli }} config delete WP_REDIS_PREFIX --skip-plugins --skip-themes --skip-packages --quiet
fi
if {{ $clonedSite->wp_cli }} config has LSOC_PREFIX; then
{{ $clonedSite->wp_cli }} config delete LSOC_PREFIX --skip-plugins --skip-themes --skip-packages --quiet
fi
if {{ $clonedSite->wp_cli }} config has WP_REDIS_PASSWORD; then
{{ $clonedSite->wp_cli }} config delete WP_REDIS_PASSWORD --skip-plugins --skip-themes --skip-packages --quiet
fi
SITE_CACHE

@if($clonedSite->server->stack->isNginx() && $clonedSite->server->stack->isNginx())
## clone custom nginx configurations
mkdir -p /etc/nginx/xcloud-conf/{{ $clonedSite->name }}

if [ -d /etc/nginx/xcloud-conf/{{ $fromSite->name }} ]; then
    cp -r /etc/nginx/xcloud-conf/{{ $fromSite->name }}/* /etc/nginx/xcloud-conf/{{ $clonedSite->name }}/
fi
@endif

@includeWhen($fromSite->hasDatabase(),'scripts.site.searchReplaceUrl', [
    'site' => $clonedSite,
    'newDomain' => $clonedSite->siteClone->domain_name,
    'oldDomain' => get_domain($clonedSite->siteClone->existing_site_url),
    'new_url' => $clonedSite->site_url
])
