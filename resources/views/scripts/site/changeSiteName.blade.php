## Not tested..

# Move Site Diretory

mv /var/www/{{ $oldName }} mv /var/www/{{ $newName }}

# Change Nginx Config File Names

mv /etc/nginx/sites-available/{{ $oldName }} mv /etc/nginx/sites-available/{{ $newName }}

# Change Nginx Links
rm /etc/nginx/sites-enabled/{{ $oldName }}
ln -s /etc/nginx/sites-available/{{ $newName }} /etc/nginx/sites-enabled/{{ $newName }}

# SSL Certificates
# How should we handle?
## @TODO
