sudo -i -u {{$site->site_user}} bash << 'WP_UPDATE_CHECK'
# CD to the site directory
cd /var/www/{{ $site->name }}

# Check if WP_DEBUG is true
WP_DEBUG=$({{ $site->wp_cli }} config get WP_DEBUG --quiet --skip-plugins --skip-themes)

# Normalize the output to lowercase for comparison
WP_DEBUG_LOWER=$(echo "$WP_DEBUG" | tr '[:upper:]' '[:lower:]')

# Check if WP_DEBUG is true
if [[ "$WP_DEBUG_LOWER" == "true" ]] || [[ "$WP_DEBUG_LOWER" == "1" ]] || [[ -n "$WP_DEBUG_LOWER" && "$WP_DEBUG_LOWER" != "false" ]]; then
    echo "true"
else
    echo "false"
fi

WP_UPDATE_CHECK
