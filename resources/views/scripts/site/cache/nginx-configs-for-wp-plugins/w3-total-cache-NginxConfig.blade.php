    # W3 TOTAL CACHE
    set $cache_uri $request_uri;

    # POST requests and urls with a query string should always go to PHP
    if ($request_method = POST) {
        set $cache_uri 'null cache';
    }

    if ($query_string != "") {
        set $cache_uri 'null cache';
    }

    # Don't cache uris containing the following segments
    if ($request_uri ~* "(/wp-admin/|/xmlrpc.php|/wp-(app|cron|login|register|mail).php|wp-.*.php|/feed/|index.php|wp-comments-popup.php|wp-links-opml.php|wp-locations.php|sitemap(index)?.xml|[a-z0-9_-]+-sitemap([0-9]+)?.xml)") {
        set $cache_uri 'null cache';
    }

    # Don't use the cache for logged in users or recent commenters
    if ($http_cookie ~* "comment_author|wordpress_[a-f0-9]+|wp-postpass|wordpress_logged_in") {
        set $cache_uri 'null cache';
    }

@if($site->isMultiSiteDirectory())
    if ( $request_uri ~* "^/([_0-9a-zA-Z-]+)/.*" ){
        set $blog $1;
    }

    set $blog "${blog}.";

    if ( $blog = "blog." ){
        set $blog "";
    }

    location / {
        try_files /wp-content/w3tc-$blog$host/pgcache$cache_uri/index.html $uri $uri/ /index.php?$args ;
    }
@elseif($site->isMultiSiteSubdomain())
    location / {
        try_files /wp-content/w3tc-$host/pgcache/$cache_uri/index.html $uri $uri/ /index.php?$args;
    }
@else
    location / {
        try_files /wp-content/w3tc/pgcache$cache_uri/index.html $uri $uri/ /index.php?$args ;
    }
@endif
    # END W3 TOTAL CACHE
