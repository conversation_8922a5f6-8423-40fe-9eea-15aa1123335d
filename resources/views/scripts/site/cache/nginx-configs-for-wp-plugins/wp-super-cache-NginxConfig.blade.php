    # This file is used to generate the Nginx configuration for the WP Super Cache plugin
    set $cache_uri $request_uri;
    set $cache_file "/wp-content/cache/supercache/$host$cache_uri/index.html";
    set $cache_status 'HIT';

    if ($scheme = https) {
        set $cache_file "/wp-content/cache/supercache/$host$cache_uri/index-https.html";
    }

    set $TestingThisFix 'Not our WordPress'; # debug

    # Request from WordPress should always go to PHP, otherwise preload will not work
    if ($http_user_agent ~* ^WordPress.*\ {{add_backslash_before_last_dot($site->name)}}$) {
        set $TestingThisFix 'Our WordPress'; # debug
    }

    if ($http_cookie ~* "{{\App\Services\WordPress\FullPageCaching::DEFAULT_COOKIE}}") {
        set $cache_status 'BYPASS';
        set $cache_file 'skipped';
        set $TestingThisFix 'Our WordPress - Logged In'; # debug
    }

    # Don't cache uris containing the following segments
    if ($request_uri ~* "(/wp-admin/|/xmlrpc.php|/wp-(app|cron|login|register|mail).php
    |wp-.*.php|/feed/|index.php|wp-comments-popup.php
    |wp-links-opml.php|wp-locations.php |sitemap(_index)?.xml
    |[a-z0-9_-]+-sitemap([0-9]+)?.xml)") {
        set $cache_status 'BYPASS';
        set $cache_file 'skipped';
    }

    # Don't use the cache for logged-in users or recent commenters
    if ($http_cookie ~* "comment_author|wordpress_[a-f0-9]+
    |wp-postpass|wordpress_logged_in") {
        set $cache_status 'BYPASS';
        set $cache_file 'skipped';
    }

    # BEGIN GZIP
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    # END GZIP

    # POST requests and URLs with a query string should always go to PHP
    if ($request_method = POST) {
        set $cache_status 'BYPASS';
        set $cache_file 'skipped';
    }

    if ($query_string != "") {
        set $cache_status 'BYPASS';
        set $cache_file 'skipped';
    }

    # Don't cache the following URLs
    if ($request_uri ~* "/wp-admin/|/xmlrpc.php|wp-.*.php|index.php|/feed/|sitemap(_index)?.xml") {
        set $cache_status 'BYPASS';
        set $cache_file 'skipped';{{--    # Set X-WP-Super-Cache header based on cache existence--}}
    }

    add_header X-Cache-WP-Super-Cache $cache_status;

    # Try in the following order: (1) cachefile, (2) normal url, (3) php
    location / {
        gzip_static on; # Enable Nginx's gzip static module
        try_files $cache_file $uri $uri/ /index.php;
    }

    # End of WP Super Cache configuration
