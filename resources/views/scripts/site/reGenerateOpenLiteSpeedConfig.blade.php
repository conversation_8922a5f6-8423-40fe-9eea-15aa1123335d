mkdir -p /usr/local/lsws/conf/vhosts/{{ $site->name }}/
mkdir -p /etc/lsws/conf.d/
mkdir -p /home/<USER>/lsphp/{{ $site->name }}
touch /home/<USER>/lsphp/{{ $site->name }}/php.ini
{{--mkdir -p /home/<USER>/.xcloud/lscaches/{{ $site->name }}--}}

chown -R lsadm:xcloud /usr/local/lsws/conf/vhosts/{{ $site->name }}/
chown -R lsadm:xcloud /etc/lsws/conf.d/
{{--chown -R {{ $site->site_user }}:{{ $site->site_user }} /home/<USER>/.xcloud/lscaches/--}}

cat > /etc/lsws/conf.d/{{ $site->name }}.conf << 'EOF'
@if($site->type === \App\Enums\SiteType::N8N || $site->type === \App\Enums\SiteType::UPTIME_KUMA)
    @include('scripts.oneclick.n8n.openLiteSpeedConfig', ['site' => $site])
@elseif($site->type === \App\Enums\SiteType::MAUTIC)
    @include('scripts.oneclick.mautic.openLiteSpeedConfig', ['site' => $site])
@else
    @include('scripts.wordpress.openLiteSpeedConfig', ['site' => $site])
@endif
EOF

@if($site->isDisable())
cat > /usr/local/lsws/conf/vhosts/{{ $site->name }}/vhconf.conf << 'EOF'
@include('scripts.wordpress.openLiteSpeedVHDisableConfig', ['site' => $site])
EOF
@else
cat > /usr/local/lsws/conf/vhosts/{{ $site->name }}/vhconf.conf << 'EOF'
@if($site->type === \App\Enums\SiteType::N8N || $site->type === \App\Enums\SiteType::UPTIME_KUMA)
    @include('scripts.oneclick.n8n.openLiteSpeedVHConfig', ['site' => $site])
@elseif($site->type === \App\Enums\SiteType::MAUTIC)
    @include('scripts.oneclick.mautic.openLiteSpeedVHConfig', ['site' => $site])
@else
    @include('scripts.wordpress.openLiteSpeedVHConfig', ['site' => $site])
@endif
EOF
@endif
/usr/local/lsws/bin/openlitespeed -t
systemctl restart lsws || echo "Failed to restart OpenLiteSpeed"
killall -9 lsphp || echo "No lsphp processes running"
