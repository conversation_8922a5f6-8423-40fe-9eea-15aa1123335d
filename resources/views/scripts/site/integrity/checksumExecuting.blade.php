timestamp=$(date +%s)
checksum_file="/home/<USER>/.xcloud/{{ $site->id }}/verify-checksums.json"

# Ensure the parent directory exists
mkdir -p "$(dirname "$checksum_file")"

# If file does not exist, create it with initial timestamp
echo '{"checksums_timestamp":'$timestamp'}' > "$checksum_file"

sudo -i -u {{ $site->site_user }} bash << 'EOF'
    SITE_DIR="{{ $site->manager()->siteDocumentRoot() }}"

    # Ensure directory exists
    if [ -d "$SITE_DIR" ]; then
        cd "$SITE_DIR" || exit 1

        # Check if WordPress is installed
        if [ -f wp-config.php ]; then

            #plugin checksums
            verify_plugins() {
                # Run the WordPress CLI command and capture output
                plugin_output=$({{ $site->wp_cli }} plugin verify-checksums --all --skip-plugins --skip-themes --skip-packages --format=json 2>&1)

                # Extract the JSON array part
                plugins=$(echo "$plugin_output" | sed -n 's/^\(\[.*\]\)[[:space:]]*\(Error:\|Warning:\|Success:\).*/\1/p')

                #external warnings
                warnings=$(echo "$plugin_output" | grep "Warning:" | awk '{print "\""$0"\","}' | sed '$s/},/}/')

                #check warnings is not empty then add to plugins
                if [ -n "$warnings" ]; then
                    #remove last comma
                    warnings=$(echo "$warnings" | sed '$s/,$//')
                    plugins=$(echo "$plugins" | sed '$s/]$//')",$warnings]"
                fi
                if [[ "$plugins" =~ ^, ]]; then
                    plugins="[$(echo "$plugins" | sed 's/^,//')]"
                fi

                #if no plugins are installed than assign empty array
                if [ -z "$plugins" ]; then
                    plugins="[]"
                fi

                # Extract the status message (Error, Warning, or Success)
                status_message=$(echo "$plugin_output" | grep -o 'Error: .*' || echo "$plugin_output" | grep -o 'Success: .*' || echo "$plugin_output" | grep -o 'Warning: .*')
                # Construct the final JSON format
                echo '{"plugins":'$plugins',"output":"'$status_message'"}'
            }

            # Verify WordPress core checksums
            core_checksums=$({{ $site->wp_cli }} core verify-checksums --skip-plugins --skip-themes --skip-packages 2>&1 | awk 'BEGIN {print "["} {lines[NR] = "  \"" $0 "\","} END {for (i=1; i<NR; i++) print lines[i]; print substr(lines[NR], 1, length(lines[NR])-1); print "]"}')

            # Verify WordPress plugin checksums by calling the function verify_plugins
            plugin_checksums=$(verify_plugins)

            # Print values so they can be captured outside heredoc
            echo '{"core_checksums":'$core_checksums',"plugin_checksums":'$plugin_checksums'}'
        fi
    fi
EOF
