timestamp=$(date +%s)
checksum_file="/home/<USER>/.xcloud/{{ $site->id }}/verify-checksums.json"

# Ensure the parent directory exists
mkdir -p "$(dirname "$checksum_file")"

# If file does not exist, create it with initial timestamp
if [ ! -f "$checksum_file" ]; then
    echo '{"checksums_timestamp":'$timestamp'}' > "$checksum_file"
fi

# Ensure checksums_timestamp exists in the file
if ! grep -q '"checksums_timestamp"' "$checksum_file"; then
    echo '{"checksums_timestamp":'$timestamp'}' > "$checksum_file"
fi

# Extract the timestamp value using grep and awk (more reliable than sed)
checksums_timestamp=$(grep -o '"checksums_timestamp":[0-9]*' "$checksum_file" | awk -F: '{print $2}')

# Validate if extraction was successful
if [[ -z "$checksums_timestamp" || ! "$checksums_timestamp" =~ ^[0-9]+$ ]]; then
    checksums_timestamp=$timestamp
    echo '{"checksums_timestamp":'$timestamp'}' > "$checksum_file"
fi

# If the timestamp is 3 days old in second or exactly the same, run verification
if [ $((timestamp - checksums_timestamp)) -ge {{ \App\Models\Site::CHECKSUM_MONITORING_INTERVAL }} ] || [ $((timestamp - checksums_timestamp)) -eq 0 ]; then

# Run checksum verification as site user
site_integrity=$(@include('scripts.site.integrity.checksumExecuting'))

fi
