#
# REQUIRES:
#   - site (the site instance)
#

set -e

## step 1: Find out how much storage the old site is using
STORAGE_IN_USE=$(($(du -s @if($exclude) @foreach($site->backupExcludesPaths() as $excludesPath) --exclude=/var/www/{{$site->name}}/{{$excludesPath}}  @endforeach @endif /var/www/{{ $site->name }} | awk '{print $1}') * 1024 / 1000000)) {{-- getting the result in MB --}}

echo $STORAGE_IN_USE
