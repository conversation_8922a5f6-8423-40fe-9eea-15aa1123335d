sudo -i -u {{ $site->site_user }} bash << 'PATCHSTACK_SCRIPT'
# Navigate to the site's root directory
cd /var/www/{{ $site->name }} || exit 1

# Check if the Patchstack plugin is installed
if ! wp plugin is-installed patchstack; then
echo "Patchstack plugin not found. Installing..."
wp plugin install patchstack --activate
if [ $? -ne 0 ]; then
echo "not_connected"
exit 1
fi
else
echo "Patchstack plugin already installed. Activating..."
wp plugin activate patchstack
fi

# Connected Patchstack App & Plugin with site ID and API key
if wp patchstack activate {{ $patchstack_vulnerability->patchstack_site_id }} {{ $patchstack_vulnerability->site_api_key }}; then
echo "connected"
exit 0
else
echo "not_connected"
exit 1
fi

PATCHSTACK_SCRIPT
