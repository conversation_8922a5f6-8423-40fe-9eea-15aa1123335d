@php
    use App\Services\Deleting\SiteDeleting;
@endphp

@include('scripts.site.deleteSite.init', ['site' => $site])

@include('scripts.site.deleteSite.ping', ['step' => SiteDeleting::DELETING_DATABASE])

@if($site->database_name)
# delete site database
mysql -u {{$site->database_user}} -p{{ $site->database_password }} -e "DROP DATABASE IF EXISTS {{ $site->database_name }};"
@endif

# delete site user
@includeWhen($site->database_user, 'scripts.database.deleteDatabaseUser', ['server' => $site->server, 'databaseUser' => $site->database_user])

#purge redis object cache
@includeWhen($site->getMeta('has_redis_object_caching',false),'scripts.site.cache.purgeRedisObjectCache', ['site' => $site])
