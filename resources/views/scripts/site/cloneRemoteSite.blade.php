#
# REQUIRES:
#   - site (source Site instance)
#   - site (destination Site instance)
#   - private key path on the destination site server
#


{{--set -e--}}

### Step 1: Copy everything from source server site directory to destination server site directory
rsync -avze "ssh -p {{ $sourceSite->port()  }} -o StrictHostKeyChecking=no -i {{ $privateKeyPath }}" {{"root@".$sourceSite->server->public_ip}}:/var/www/{{$sourceSite->name}}/ /var/www/{{$destinationSite->name}}/ --exclude 'wp-content/mu-plugins/xcloud-auto-login.php'

# remove old wp-content/object-cache.php file if exists
if [ -f /var/www/{{$destinationSite->name}}/wp-content/object-cache.php ]; then
    rm -f /var/www/{{$destinationSite->name}}/wp-content/object-cache.php
fi

if [ -f /var/www/{{$destinationSite->name}}/wp-content/.litespeed_conf.dat ]; then
    rm -f /var/www/{{$destinationSite->name}}/wp-content/.litespeed_conf.dat
fi

if [ -d /var/www/{{$destinationSite->name}}/wp-content/plugins/redis-cache ]; then
    rm -rf /var/www/{{$destinationSite->name}}/wp-content/plugins/redis-cache
fi

if [ -d /var/www/{{$destinationSite->name}}/wp-content/plugins/litespeed-cache ]; then
    rm -rf /var/www/{{$destinationSite->name}}/wp-content/plugins/litespeed-cache
fi

### Step 2: Export old site database
OLD_SITE_DB_NAME=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_NAME --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
OLD_SITE_DB_USER=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_USER --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
OLD_SITE_DB_PASSWORD=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_PASSWORD --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
OLD_SITE_DB_HOST=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_HOST --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)


# Split DB_HOST into host and port
OLD_SITE_DB_PORT=${OLD_SITE_DB_HOST##*:}  # i.e extract port from localhost:3306
OLD_SITE_DB_HOST=${OLD_SITE_DB_HOST%:*}  # i.e extract host name from localhost:3306

# Set default port if not specified (i.e localhost:3306)
if [ -z "$OLD_SITE_DB_PORT" ]; then
OLD_SITE_DB_PORT=3306
fi

# if port value is empty (i.e localhost)
if [ "$OLD_SITE_DB_PORT" == "$OLD_SITE_DB_HOST" ]; then
OLD_SITE_DB_PORT=3306
fi

echo $OLD_SITE_DB_NAME
echo $OLD_SITE_DB_USER
echo $OLD_SITE_DB_PASSWORD
echo $OLD_SITE_DB_HOST
echo $OLD_SITE_DB_PORT


# Dump the database into a temporary location
TMP_DIR="/tmp"
DATE=$(date +%Y-%m-%d-%H-%M-%S)
DB_DUMP_FILE="$TMP_DIR/$OLD_SITE_DB_NAME-$DATE.sql"
echo "dumping db: $DB_DUMP_FILE"


# dump the source database on source site server
ssh -p {{ $sourceSite->port() }} -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -i {{ $privateKeyPath }} {{"root@".$sourceSite->server->public_ip}} bash -s << EOF
mysqldump --verbose -h $OLD_SITE_DB_HOST -u $OLD_SITE_DB_USER --password=$OLD_SITE_DB_PASSWORD $OLD_SITE_DB_NAME {{ $sourceSite->server->database_type === \App\Enums\DatabaseType::MYSQL_8 ? '--no-tablespaces' : '--single-transaction' }} > $DB_DUMP_FILE
EOF

# bring the database from source to destination server
rsync -avze "ssh -p {{ $sourceSite->port() }} -o StrictHostKeyChecking=no -i {{ $privateKeyPath }}" {{"root@".$sourceSite->server->public_ip}}:$DB_DUMP_FILE  /tmp/{{$destinationSite->name}}.sql

### Step 3: Update wp-config.php according to new site

/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config set DB_NAME {{ $destinationSite->database_name }} --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config set DB_USER {{ $destinationSite->database_user }} --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config set DB_PREFIX {{  $destinationSite->prefix ?: "wp{$destinationSite->id}_" }} --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config set DB_PASSWORD {{ $destinationSite->database_password }} --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config set DB_HOST {{ $destinationSite->database_host ?: 'localhost' }}{{ $destinationSite->database_port ? ":{$destinationSite->database_port}" : '' }} --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet

### Step 4: Import the old site database into new site database

NEW_SITE_DB_NAME=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_NAME --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
NEW_SITE_DB_USER=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_USER --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
NEW_SITE_DB_PASSWORD=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_PASSWORD --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)
NEW_SITE_DB_HOST=$(/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config get DB_HOST --path=/var/www/{{ $destinationSite->name }} --allow-root --skip-plugins --skip-themes --skip-packages --quiet)

# Split NEW_SITE_DB_HOST into host and port
NEW_SITE_DB_PORT=${NEW_SITE_DB_HOST##*:}  # i.e extract port from localhost:3306
NEW_SITE_DB_HOST=${NEW_SITE_DB_HOST%:*}  # i.e extract host name from localhost:3306

# Set default port if not specified (i.e localhost:3306)
if [ -z "$NEW_SITE_DB_PORT" ]; then
NEW_SITE_DB_PORT=3306
fi

# if port value is empty (i.e localhost)
if [ "$NEW_SITE_DB_PORT" == "$NEW_SITE_DB_HOST" ]; then
NEW_SITE_DB_PORT=3306
fi

echo $NEW_SITE_DB_NAME
echo $NEW_SITE_DB_USER
echo $NEW_SITE_DB_PASSWORD
echo $NEW_SITE_DB_HOST

mysql -f -h $NEW_SITE_DB_HOST -u $NEW_SITE_DB_USER --password="$NEW_SITE_DB_PASSWORD" $NEW_SITE_DB_NAME < /tmp/{{$destinationSite->name}}.sql

echo "Database dump imported into $NEW_SITE_DB_NAME database"


chown -R {{ $destinationSite->site_user }}:{{ $destinationSite->site_user }} /var/www/{{ $destinationSite->name }}

### Step 4: Remove the tmp database (cleaning up)
echo "deleting database dump from /tmp/{{$destinationSite->name}}.sql"
sudo rm /tmp/{{$destinationSite->name}}.sql

@if($sourceSite->server->stack->isNginx() && $destinationSite->server->stack->isNginx())
## clone custom nginx configurations from source site
mkdir -p /etc/nginx/xcloud-conf/{{ $destinationSite->name }}

if [ -d /etc/nginx/xcloud-conf/{{ $destinationSite->name }} ]; then
    rsync -avze "ssh -p {{ $sourceSite->port() }} -o StrictHostKeyChecking=no -i {{ $privateKeyPath }}" {{"root@".$sourceSite->server->public_ip}}:/etc/nginx/xcloud-conf/{{ $sourceSite->name }}/* /etc/nginx/xcloud-conf/{{ $destinationSite->name }}/
fi
@endif
#Remove old cache plugins configurations
sudo -i -u {{ $destinationSite->site_user }} bash << 'SITE_CACHE'
# cd to the directory
cd /var/www/{{ $destinationSite->name }}
#remove previous cache plugins
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) plugin delete redis-cache --skip-plugins --skip-themes --skip-packages --quiet
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) plugin delete litespeed-cache --skip-plugins --skip-themes --skip-packages --quiet
if /usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config has WP_REDIS_PREFIX; then
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config delete WP_REDIS_PREFIX --skip-plugins --skip-themes --skip-packages --quiet
fi
if /usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config has LSOC_PREFIX; then
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config delete LSOC_PREFIX --skip-plugins --skip-themes --skip-packages --quiet
fi
if /usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config has WP_REDIS_PASSWORD; then
/usr/bin/php{{$destinationSite->php_version}} -d error_reporting=0 -d display_errors=0 $(which wp) config delete WP_REDIS_PASSWORD --skip-plugins --skip-themes --skip-packages --quiet
fi
SITE_CACHE

@include('scripts.site.searchReplaceUrl', [
    'site' => $destinationSite,
    'newDomain' => $destinationSite->siteClone->domain_name,
    'oldDomain' => get_domain($destinationSite->siteClone->existing_site_url),
    'new_url' => $destinationSite->site_url
])
