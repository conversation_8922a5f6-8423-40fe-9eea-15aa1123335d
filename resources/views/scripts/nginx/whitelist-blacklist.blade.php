@foreach($ipAddresses->where('type',\App\Enums\IPAddressType::WHITELIST) as $whiteIp)
    allow {{ $whiteIp->ip_address }};
@endforeach

@foreach($ipAddresses->where('type',\App\Enums\IPAddressType::BLACKLIST) as $blackIp)
    deny {{ $blackIp->ip_address }};
@endforeach

@if($ipAddresses->where('type',\App\Enums\IPAddressType::BLACKLIST)->isNotEmpty())
    # Block multiple IPs from HTTP_X_FORWARDED_FOR
    set $block_ip 0;
@foreach($ipAddresses->where('type',\App\Enums\IPAddressType::BLACKLIST) as $blackIp)
    if ($http_x_forwarded_for ~* "^{{ str_replace('.', '\.', $blackIp->ip_address) }}$") {
        set $block_ip 1;
    }
@endforeach
    # Return 403 for blocked IPs
    if ($block_ip) {
        return 403;
    }
@endif
