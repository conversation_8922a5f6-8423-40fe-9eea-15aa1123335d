# Check if Node.js version {{ $nodeVersion }} is installed

# Check system Node.js
if command -v node &> /dev/null; then
    SYSTEM_NODE_VERSION=$(node -v | sed 's/v//' | cut -d'.' -f1)
    if [[ "$SYSTEM_NODE_VERSION" == "{{ $nodeVersion }}" ]]; then
        echo "Node.js version {{ $nodeVersion }} is installed: $(node -v)"
        exit 0
    else
        echo "Node.js is installed but version $SYSTEM_NODE_VERSION does not match required version {{ $nodeVersion }}"
        exit 1
    fi
else
    echo "Node.js is not installed"
    exit 1
fi
