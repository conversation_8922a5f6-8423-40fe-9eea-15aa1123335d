# Install Node.js version {{ $nodeVersion }}

# Check if Node.js is already installed
if command -v node &> /dev/null; then
    CURRENT_VERSION=$(node -v | sed 's/v//' | cut -d'.' -f1)
    if [[ "$CURRENT_VERSION" == "{{ $nodeVersion }}" ]]; then
        echo "Node.js version {{ $nodeVersion }} is already installed"
    else
        echo "Installing Node.js version {{ $nodeVersion }}..."

        # Remove existing Node.js if it exists
        if dpkg -l | grep -q nodejs; then
            apt-get remove -y nodejs
            apt-get autoremove -y
        fi

        # Install the specified Node.js version
        @include('scripts.apt.apt-wait')
        curl --silent --location https://deb.nodesource.com/setup_{{ $nodeVersion }}.x | bash -

        @include('scripts.apt.apt-gpg-key-update')

        apt-get install -y nodejs

        if [ $? -ne 0 ]; then
            echo "Failed to install Node.js version {{ $nodeVersion }}"
            exit 1
        fi

        echo "Node.js version {{ $nodeVersion }} installed successfully"
    fi
else
    echo "Installing Node.js version {{ $nodeVersion }}..."

    # Install the specified Node.js version
    @include('scripts.apt.apt-wait')
    curl --silent --location https://deb.nodesource.com/setup_{{ $nodeVersion }}.x | bash -

    @include('scripts.apt.apt-gpg-key-update')

    apt-get install -y nodejs

    if [ $? -ne 0 ]; then
        echo "Failed to install Node.js version {{ $nodeVersion }}"
        exit 1
    fi

    echo "Node.js version {{ $nodeVersion }} installed successfully"
fi

# Install global packages
echo "Installing global packages for Node.js {{ $nodeVersion }}..."
npm install -g yarn pm2

echo "Node.js {{ $nodeVersion }} setup completed"
