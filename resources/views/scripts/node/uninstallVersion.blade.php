# Uninstall Node.js version {{ $nodeVersion }}

# Check if Node.js is installed
if command -v node &> /dev/null; then
    CURRENT_VERSION=$(node -v | sed 's/v//' | cut -d'.' -f1)
    if [[ "$CURRENT_VERSION" == "{{ $nodeVersion }}" ]]; then
        echo "Cannot uninstall Node.js version {{ $nodeVersion }} as it is currently the only installed version"
        echo "To change versions, install a new version first"
        exit 1
    else
        echo "Current Node.js version is $CURRENT_VERSION, not {{ $nodeVersion }}"

        # Check if the version to uninstall was previously installed
        if dpkg -l | grep -q nodejs; then
            echo "Uninstalling Node.js version {{ $nodeVersion }}..."

            # Remove the Node.js packages
            apt-get remove -y nodejs
            apt-get autoremove -y

            echo "Node.js version {{ $nodeVersion }} has been uninstalled"
            exit 0
        else
            echo "Node.js version {{ $nodeVersion }} is not installed"
            exit 0
        fi
    fi
else
    echo "Node.js is not installed"
    exit 0
fi
