# Set default Node.js version to {{ $nodeVersion }}

# Check if Node.js is installed
if command -v node &> /dev/null; then
    CURRENT_VERSION=$(node -v | sed 's/v//' | cut -d'.' -f1)
    if [[ "$CURRENT_VERSION" == "{{ $nodeVersion }}" ]]; then
        echo "Node.js version {{ $nodeVersion }} is already set as default"
    else
        echo "Current Node.js version $CURRENT_VERSION does not match required version {{ $nodeVersion }}"
        echo "Installing Node.js version {{ $nodeVersion }}..."

        # Install the specified Node.js version
        @include('scripts.node.installVersion', ['nodeVersion' => $nodeVersion])
    fi
else
    echo "Node.js is not installed"
    echo "Installing Node.js version {{ $nodeVersion }}..."

    # Install the specified Node.js version
    @include('scripts.node.installVersion', ['nodeVersion' => $nodeVersion])
fi

# Ensure global packages are installed
if ! command -v yarn &> /dev/null; then
    npm install -g yarn
fi

if ! command -v pm2 &> /dev/null; then
    npm install -g pm2
fi

echo "Node.js version {{ $nodeVersion }} is set as default successfully"
