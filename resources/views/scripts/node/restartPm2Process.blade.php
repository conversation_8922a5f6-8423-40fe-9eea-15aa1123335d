# Restart n8n PM2 process

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "PM2 is not installed, installing now..."
    npm install -g pm2
fi

sudo -i -u {{$site->site_user}} bash << 'PM2_RESTART'

    cd /var/www/{{ $site->name }}

    # Check if the n8n process exists for this site
    if pm2 list | grep -q "{{ $site->pm2ProcessName() }}"; then
        echo "Restarting n8n PM2 process for {{ $site->name }}"
        # Restart the process
        pm2 restart "{{ $site->pm2ProcessName() }}"
        # Save the PM2 process list
        pm2 save
        echo "Successfully restarted n8n PM2 process for {{ $site->name }}"
    else
        echo "No n8n PM2 process found for {{ $site->name }}, starting new process..."

        # Check if the site directory exists
        if [ -d "/var/www/{{ $site->name }}" ]; then
            cd /var/www/{{ $site->name }}

            # Check if ecosystem.config.js exists
            if [ -f "ecosystem.config.js" ]; then
                # Start the process using PM2
                pm2 start ecosystem.config.js
                pm2 save
                echo "Successfully started n8n PM2 process for {{ $site->name }}"
            else
                echo "Error: ecosystem.config.js not found in /var/www/{{ $site->name }}"
                exit 1
            fi
        else
            echo "Error: Site directory /var/www/{{ $site->name }} not found"
            exit 1
        fi
    fi
PM2_RESTART
