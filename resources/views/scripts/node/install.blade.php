# Install NodeJS {{ $nodeVersion ?? \App\Models\NodeVersion::DEFAULT }}

@include('scripts.apt.apt-wait')

# Use the specified Node.js version or default to {{ \App\Models\NodeVersion::DEFAULT }}
NODE_VERSION="{{ $nodeVersion ?? \App\Models\NodeVersion::DEFAULT }}"

curl --silent --location https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -

@include('scripts.apt.apt-gpg-key-update')

apt-get install -y nodejs

# Install global packages
npm install -g yarn pm2
