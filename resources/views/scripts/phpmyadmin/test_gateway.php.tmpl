# Create a test script to verify the gateway works
cat > test_gateway.php << 'EOFTEST'
<?php
// Include the debug_log function
function debug_log($message, $level = 'info') {
    $log_file = '/tmp/phpmyadmin_gateway.log';
    $timestamp = date('Y-m-d H:i:s');
    $formatted_message = "[$timestamp] [$level] $message\n";
    error_log($formatted_message, 3, $log_file);

    // Also log to PHP error log
    error_log("phpMyAdmin Gateway Test: $message");
}

session_name('phpmyadmin_sso');
session_start();

debug_log("Test gateway script started");

echo "<h1>phpMyAdmin Gateway Test</h1>";
echo "<p>This script tests the phpMyAdmin gateway.</p>";

echo "<h2>Authentication Status</h2>";
echo "<p>";

// Check for authentication cookies
$site_auth = isset($_COOKIE['pma_site_auth_complete']) && $_COOKIE['pma_site_auth_complete'] == '1';
$server_auth = isset($_COOKIE['pma_server_auth_complete']) && $_COOKIE['pma_server_auth_complete'] == '1';
$legacy_auth = isset($_COOKIE['pma_auth_complete']) && $_COOKIE['pma_auth_complete'] == '1';

if ($site_auth) {
    echo "<strong style='color:green'>Site-specific authentication is active</strong><br>";
} else {
    echo "<strong style='color:red'>Site-specific authentication is not active</strong><br>";
}

if ($server_auth) {
    echo "<strong style='color:green'>Server-level authentication is active</strong><br>";
} else {
    echo "<strong style='color:red'>Server-level authentication is not active</strong><br>";
}

if ($legacy_auth) {
    echo "<strong style='color:orange'>Legacy authentication is active</strong><br>";
}

echo "</p>";

echo "<h2>Session Information</h2>";
echo "<pre>";
echo "Session name: " . session_name() . "<br>";
echo "Session ID: " . session_id() . "<br>";
echo "Session data: ";
print_r($_SESSION);
echo "</pre>";

// Display the URL parameters
echo "<h2>URL Parameters</h2>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

// Display server information
echo "<h2>Server Information</h2>";
echo "<pre>";
print_r($_SERVER);
echo "</pre>";

// Add buttons for various actions
echo "<h2>Actions</h2>";
echo "<div style='display: flex; gap: 10px;'>";

// Clear cookies button
echo "<form method='get'>";
echo "<input type='hidden' name='clear_cookies' value='1'>";
echo "<button type='submit' style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; border-radius: 4px;'>Clear All Cookies</button>";
echo "</form>";

// Logout button
echo "<a href='logout.php' style='padding: 10px; background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; border-radius: 4px; text-decoration: none; display: inline-block;'>Test Logout</a>";

// Link back to phpMyAdmin
echo "<a href='index.php' style='padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; border-radius: 4px; text-decoration: none; display: inline-block;'>Go to phpMyAdmin</a>";

echo "</div>";

// Handle cookie clearing
if (isset($_GET['clear_cookies']) && $_GET['clear_cookies'] == '1') {
    setcookie('pma_site_auth_complete', '', time() - 3600, '/', '', true, true);
    setcookie('pma_server_auth_complete', '', time() - 3600, '/', '', true, true);
    setcookie('pma_auth_complete', '', time() - 3600, '/', '', true, true);
    echo "<p style='color: green; font-weight: bold;'>All cookies have been cleared. Refresh the page to see the changes.</p>";
}
EOFTEST

chmod 644 test_gateway.php
chown SITE_USER:SITE_USER test_gateway.php
