<?php
// Helper function for debugging
function debug_log($message, $level = 'info') {
    $log_file = '/tmp/phpmyadmin_gateway.log';
    $timestamp = date('Y-m-d H:i:s');
    $formatted_message = "[$timestamp] [$level] $message\n";
    error_log($formatted_message, 3, $log_file);

    // Also log to PHP error log
    error_log("phpMyAdmin Gateway: $message");
}

// Helper function to clear all authentication cookies and session data
function clear_all_auth_cookies() {
    // Clear cookies
    setcookie('pma_site_auth_complete', '', time() - 3600, '/', '', true, true);
    setcookie('pma_server_auth_complete', '', time() - 3600, '/', '', true, true);
    setcookie('pma_auth_complete', '', time() - 3600, '/', '', true, true);

    // Clear session data
    $_SESSION = array();

    debug_log('Cleared all authentication cookies and session data');
}

// Helper function to check if we have valid phpMyAdmin session data
function has_valid_session_data() {
    // Check if all required session variables are present and not empty
    $required_vars = ['PMA_single_signon_user', 'PMA_single_signon_password', 'PMA_single_signon_host'];

    foreach ($required_vars as $var) {
        if (!isset($_SESSION[$var]) || empty($_SESSION[$var])) {
            debug_log("Missing or empty session variable: $var");
            return false;
        }
    }

    debug_log('Valid session data found');
    return true;
}

// Helper function to check for redirect loops
function is_redirect_loop() {
    // Check if we've been redirected too many times
    if (!isset($_SESSION['pma_redirect_count'])) {
        $_SESSION['pma_redirect_count'] = 0;
    }

    $_SESSION['pma_redirect_count']++;

    if ($_SESSION['pma_redirect_count'] > 3) {
        debug_log('Redirect loop detected, count: ' . $_SESSION['pma_redirect_count'], 'warning');
        return true;
    }

    return false;
}

// Start session
session_name('phpmyadmin_sso');
session_start();

// Log the start of the script
debug_log('Gateway script started');

// If we're starting a new authentication process, clear all existing cookies and session data
if (isset($_GET['xcloud-token']) && !empty($_GET['xcloud-token'])) {
    // Regenerate session ID to prevent session fixation
    session_regenerate_id(true);
    clear_all_auth_cookies();
    debug_log('New authentication process detected, cleared all cookies and session data');
    // Reset redirect count for new authentication
    $_SESSION['pma_redirect_count'] = 0;
}

// Check if we're coming from a direct link (to avoid redirect loops)
if (isset($_GET['direct']) && $_GET['direct'] == '1') {
    debug_log('Direct access detected, redirecting to index.php');
    // Reset redirect count when coming from direct link
    $_SESSION['pma_redirect_count'] = 0;
    header('Location: index.php');
    exit;
}

// Check for redirect loops first
if (is_redirect_loop()) {
    debug_log('Redirect loop detected, clearing cookies and showing error', 'error');
    clear_all_auth_cookies();
    // Show error page for redirect loop
    echo "<!DOCTYPE html>";
    echo "<html>";
    echo "<head>";
    echo "<title>phpMyAdmin Session Error</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
    echo "h1 { color: #2c3e50; }";
    echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
    echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    echo "<h1>phpMyAdmin Session Error</h1>";
    echo "<div class='error'>";
    echo "<strong>Session Error</strong><br>";
    echo "Your session has expired or become invalid. Please try accessing phpMyAdmin again from xCloud.";
    echo "</div>";
    echo "<div class='info'>";
    echo "<strong>How to access phpMyAdmin:</strong><br>";
    echo "1. Go to your xCloud dashboard<br>";
    echo "2. Navigate to your server or site<br>";
    echo "3. Click on the 'Open phpMyAdmin' button";
    echo "</div>";
    echo "</body>";
    echo "</html>";
    exit;
}

// Check if we already have a valid authentication cookie
// Check both types of cookies (site-specific and server-level)
if ((isset($_COOKIE['pma_site_auth_complete']) && $_COOKIE['pma_site_auth_complete'] == '1') ||
    (isset($_COOKIE['pma_server_auth_complete']) && $_COOKIE['pma_server_auth_complete'] == '1') ||
    (isset($_COOKIE['pma_auth_complete']) && $_COOKIE['pma_auth_complete'] == '1')) {

    // Determine which type of cookie we have
    $cookie_type = isset($_COOKIE['pma_site_auth_complete']) ? 'site-specific' :
                  (isset($_COOKIE['pma_server_auth_complete']) ? 'server-level' : 'legacy');

    // Check if we have a new token - if so, we need to re-authenticate even with valid cookies
    if (isset($_GET['xcloud-token']) && !empty($_GET['xcloud-token'])) {
        debug_log('Authentication cookie found but new token detected - will re-authenticate');
        // Continue to token processing below
    } else {
        // Check if we have valid session data to go with the cookie
        if (has_valid_session_data()) {
            // Both cookie and session data are valid, redirect to phpMyAdmin
            debug_log('Authentication cookie found (' . $cookie_type . ') with valid session data, redirecting to index.php');
            // Reset redirect count on successful validation
            $_SESSION['pma_redirect_count'] = 0;
            header('Location: index.php');
            exit;
        } else {
            // Cookie exists but session data is invalid/missing
            debug_log('Authentication cookie found (' . $cookie_type . ') but session data is invalid, clearing cookies', 'warning');
            clear_all_auth_cookies();
            // Show error message instead of redirecting to avoid loop
            echo "<!DOCTYPE html>";
            echo "<html>";
            echo "<head>";
            echo "<title>phpMyAdmin Session Expired</title>";
            echo "<style>";
            echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
            echo "h1 { color: #2c3e50; }";
            echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
            echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
            echo "</style>";
            echo "</head>";
            echo "<body>";
            echo "<h1>phpMyAdmin Session Expired</h1>";
            echo "<div class='error'>";
            echo "<strong>Session Expired</strong><br>";
            echo "Your phpMyAdmin session has expired. Please access phpMyAdmin again from xCloud.";
            echo "</div>";
            echo "<div class='info'>";
            echo "<strong>How to access phpMyAdmin:</strong><br>";
            echo "1. Go to your xCloud dashboard<br>";
            echo "2. Navigate to your server or site<br>";
            echo "3. Click on the 'Open phpMyAdmin' button";
            echo "</div>";
            echo "</body>";
            echo "</html>";
            exit;
        }
    }
}

// Check if we have a token in the URL
if (isset($_GET['xcloud-token']) && !empty($_GET['xcloud-token'])) {
    $token = $_GET['xcloud-token'];

    // We can't use Laravel's decrypt() function here, so we'll use the auth_type parameter
    // to determine if this is a site-specific or server-level authentication
    $is_site_specific = false;
    $auth_type = 'unknown';

    // Check the auth_type parameter
    if (isset($_GET['auth_type'])) {
        $auth_type = $_GET['auth_type'];
        $is_site_specific = ($auth_type === 'site');
        debug_log("Auth type from parameter: " . $auth_type);
    }
    // Fallback to site_specific parameter if auth_type is not set
    else if (isset($_GET['site_specific'])) {
        $is_site_specific = ($_GET['site_specific'] === '1');
        $auth_type = $is_site_specific ? 'site' : 'server';
        debug_log("Auth type from site_specific parameter: " . $auth_type);
    }

    debug_log("Authentication type: " . $auth_type . ", is_site_specific: " . ($is_site_specific ? 'true' : 'false'));

    // Use the appropriate callback URL based on token type
    $callback_url = $is_site_specific ? "SITE_CALLBACK_URL" : "CALLBACK_URL";

    // Log the token and callback URL for debugging
    debug_log("Token: " . $token);
    debug_log("Callback URL: " . $callback_url);
    debug_log("Site-specific: " . ($is_site_specific ? 'Yes' : 'No'));

    // Make a request to the xCloud API to verify the token
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $callback_url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['token' => $token]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Log the response for debugging
    debug_log("Response HTTP Code: " . $http_code);
    debug_log("Response: " . $response);

    // Check for HTTP errors
    if ($http_code >= 400) {
        debug_log("HTTP Error: " . $http_code, 'error');

        // Create a more user-friendly error page
        echo "<!DOCTYPE html>";
        echo "<html>";
        echo "<head>";
        echo "<title>phpMyAdmin Server Error</title>";
        echo "<style>";
        echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
        echo "h1 { color: #2c3e50; }";
        echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo "</style>";
        echo "</head>";
        echo "<body>";
        echo "<h1>phpMyAdmin Server Error</h1>";
        echo "<div class='error'>";
        echo "<strong>Error communicating with authentication server</strong><br>";
        echo "HTTP Status Code: " . $http_code . "<br>";
        echo "Response: " . htmlspecialchars($response);
        echo "</div>";
        echo "<div class='info'>";
        echo "<strong>What to do:</strong><br>";
        echo "1. Try again later<br>";
        echo "2. If the problem persists, contact support";
        echo "</div>";
        echo "</body>";
        echo "</html>";
        exit;
    }

    if ($curl_error) {
        debug_log("cURL Error: " . $curl_error, 'error');

        // Create a more user-friendly error page
        echo "<!DOCTYPE html>";
        echo "<html>";
        echo "<head>";
        echo "<title>phpMyAdmin Connection Error</title>";
        echo "<style>";
        echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
        echo "h1 { color: #2c3e50; }";
        echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo "</style>";
        echo "</head>";
        echo "<body>";
        echo "<h1>phpMyAdmin Connection Error</h1>";
        echo "<div class='error'>";
        echo "<strong>Error connecting to authentication server</strong><br>";
        echo "Technical details: " . htmlspecialchars($curl_error);
        echo "</div>";
        echo "<div class='info'>";
        echo "<strong>What to do:</strong><br>";
        echo "1. Check your internet connection<br>";
        echo "2. Try again later<br>";
        echo "3. If the problem persists, contact support";
        echo "</div>";
        echo "</body>";
        echo "</html>";
        exit;
    }

    $data = json_decode($response, true);
    $json_error = json_last_error();

    if ($json_error !== JSON_ERROR_NONE) {
        $error_message = 'JSON decode error: ' . json_last_error_msg();
        debug_log($error_message, 'error');
        debug_log("Response that failed to decode: " . $response, 'error');

        // Create a more user-friendly error page
        echo "<!DOCTYPE html>";
        echo "<html>";
        echo "<head>";
        echo "<title>phpMyAdmin Response Error</title>";
        echo "<style>";
        echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
        echo "h1 { color: #2c3e50; }";
        echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo "</style>";
        echo "</head>";
        echo "<body>";
        echo "<h1>phpMyAdmin Response Error</h1>";
        echo "<div class='error'>";
        echo "<strong>Error processing server response</strong><br>";
        echo "Technical details: " . htmlspecialchars($error_message);
        echo "</div>";
        echo "<div class='info'>";
        echo "<strong>What to do:</strong><br>";
        echo "1. Try again later<br>";
        echo "2. If the problem persists, contact support";
        echo "</div>";
        echo "</body>";
        echo "</html>";
        exit;
    }

    if (isset($data['success']) && $data['success'] === true) {
        // Set the authentication variables
        $_SESSION['PMA_single_signon_user'] = $data['data']['username'];
        $_SESSION['PMA_single_signon_password'] = $data['data']['password'];
        $_SESSION['PMA_single_signon_host'] = $data['data']['host'];

        // Store the authentication type in the session
        $_SESSION['xcloud_auth_type'] = $auth_type;

        // If site-specific database is provided, select it automatically
        if (isset($data['data']['database'])) {
            $_SESSION['PMA_single_signon_database'] = $data['data']['database'];
            debug_log("Using site-specific database: " . $data['data']['database']);
        }

        // Log successful authentication
        debug_log("Authentication successful for user: " . $data['data']['username']);

        // Store authentication in a cookie to prevent redirect loops
        // Use different cookies for server-level and site-specific access
        if ($is_site_specific) {
            setcookie('pma_site_auth_complete', '1', time() + 3600, '/', '', true, true);
            // Clear any server-level auth cookie to avoid conflicts
            setcookie('pma_server_auth_complete', '', time() - 3600, '/', '', true, true);
            debug_log("Set site-specific auth cookie");
        } else {
            setcookie('pma_server_auth_complete', '1', time() + 3600, '/', '', true, true);
            // Clear any site-specific auth cookie to avoid conflicts
            setcookie('pma_site_auth_complete', '', time() - 3600, '/', '', true, true);
            debug_log("Set server-level auth cookie");
        }

        // Also clear the old cookie to avoid conflicts
        setcookie('pma_auth_complete', '', time() - 3600, '/', '', true, true);

        // Reset redirect count on successful authentication
        $_SESSION['pma_redirect_count'] = 0;

        // Redirect to the main phpMyAdmin page with a direct parameter to avoid loops
        header('Location: index.php?direct=1');
        exit;
    } else {
        $error_message = isset($data['message']) ? $data['message'] : 'Unknown error';
        debug_log("Authentication failed: " . $error_message, 'error');

        // Create a more user-friendly error page
        echo "<!DOCTYPE html>";
        echo "<html>";
        echo "<head>";
        echo "<title>phpMyAdmin Authentication Failed</title>";
        echo "<style>";
        echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
        echo "h1 { color: #2c3e50; }";
        echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
        echo "</style>";
        echo "</head>";
        echo "<body>";
        echo "<h1>phpMyAdmin Authentication Failed</h1>";
        echo "<div class='error'>";
        echo "<strong>Error:</strong> " . htmlspecialchars($error_message) . "<br>";
        echo "</div>";
        echo "<div class='info'>";
        echo "<strong>What to do:</strong><br>";
        echo "1. Go back to xCloud and try again<br>";
        echo "2. If the problem persists, contact support";
        echo "</div>";
        echo "</body>";
        echo "</html>";
        exit;
    }
}

// If no token or authentication failed, show a more informative error
debug_log("No token provided or token invalid", 'warning');

// Create a more user-friendly error page
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>phpMyAdmin Access</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
echo "h1 { color: #2c3e50; }";
echo ".error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<h1>phpMyAdmin Access</h1>";
echo "<div class='error'>";
echo "<strong>Access Denied</strong><br>";
echo "You must access phpMyAdmin through the xCloud interface.";
echo "</div>";
echo "<div class='info'>";
echo "<strong>How to access phpMyAdmin:</strong><br>";
echo "1. Go to your xCloud dashboard<br>";
echo "2. Navigate to your server or site<br>";
echo "3. Click on the 'Open phpMyAdmin' button";
echo "</div>";
echo "</body>";
echo "</html>";
exit;
