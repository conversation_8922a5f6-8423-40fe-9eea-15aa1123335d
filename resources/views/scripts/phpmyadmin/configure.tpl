#!/bin/bash

# Navigate to the phpMyAdmin directory
cd /var/www/SITE_NAME

# Create a simple info file
cat > xcloud_info.php << 'EOF'
<?php
/**
 * Information file for xCloud phpMyAdmin integration
 */

echo "<h1>xCloud phpMyAdmin Integration</h1>";
echo "<p>This phpMyAdmin installation is managed by xCloud.</p>";
echo "<p>Please access phpMyAdmin through the xCloud interface.</p>";
EOF

# Update the phpMyAdmin configuration to use our custom authentication
if grep -q "SignonSession" config.inc.php; then
    echo "Signon authentication already configured"
else
    # Create a backup of the original config file
    cp config.inc.php config.inc.php.bak

    # Create a completely new config file
    cat > new_config.inc.php << 'EOFNEW'
NEW_CONFIG_CONTENT_HERE
EOFNEW

    # Replace the original with our new file
    mv new_config.inc.php config.inc.php

    # Verify the config file is valid PHP
    php -l config.inc.php > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Error: config.inc.php contains syntax errors. Restoring backup."
        cp config.inc.php.bak config.inc.php
    else
        echo "config.inc.php successfully updated and validated."
    fi
fi

# Set proper permissions
chmod 644 xcloud_info.php
chown SITE_USER:SITE_USER xcloud_info.php
chown SITE_USER:SITE_USER config.inc.php
chown SITE_USER:SITE_USER new_config.inc.php

# Create the gateway script for phpMyAdmin signon authentication
cat > pma-gateway.php << 'EOFGATEWAY'
PMA_GATEWAY_CONTENT_HERE
EOFGATEWAY

# Create a logout script
cat > logout.php << 'EOFLOGOUT'
LOGOUT_FILE_CONTENT_HERE
EOFLOGOUT

chmod 644 logout.php
chown SITE_USER:SITE_USER logout.php

chmod 644 pma-gateway.php
chown SITE_USER:SITE_USER pma-gateway.php

# Create log file with proper permissions
touch /tmp/phpmyadmin_gateway.log
chmod 666 /tmp/phpmyadmin_gateway.log

# Test the gateway script
php -l pma-gateway.php > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Error: pma-gateway.php contains syntax errors."
else
    echo "pma-gateway.php successfully validated."
fi

echo "phpMyAdmin authentication configured successfully"
