<?php
// Helper function for debugging
function debug_log($message, $level = 'info') {
    $log_file = '/tmp/phpmyadmin_gateway.log';
    $timestamp = date('Y-m-d H:i:s');
    $formatted_message = "[$timestamp] [$level] $message\n";
    error_log($formatted_message, 3, $log_file);

    // Also log to PHP error log
    error_log("phpMyAdmin Logout: $message");
}

// Start session
session_name('phpmyadmin_sso');
session_start();

debug_log("Logout script started");

// Clear all session data
$_SESSION = array();

// If it's desired to kill the session, also delete the session cookie.
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Finally, destroy the session
session_destroy();

// Clear all authentication cookies
setcookie('pma_site_auth_complete', '', time() - 3600, '/', '', true, true);
setcookie('pma_server_auth_complete', '', time() - 3600, '/', '', true, true);
setcookie('pma_auth_complete', '', time() - 3600, '/', '', true, true);

debug_log("User logged out successfully");

// Create a user-friendly logout page
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>phpMyAdmin Logout</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }";
echo "h1 { color: #2c3e50; }";
echo ".success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
echo ".info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 20px; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<h1>phpMyAdmin Logout</h1>";
echo "<div class='success'>";
echo "<strong>Successfully Logged Out</strong><br>";
echo "You have been successfully logged out of phpMyAdmin.";
echo "</div>";
echo "<div class='info'>";
echo "<strong>How to access phpMyAdmin again:</strong><br>";
echo "1. Go to your xCloud dashboard<br>";
echo "2. Navigate to your server or site<br>";
echo "3. Click on the 'Open phpMyAdmin' button";
echo "</div>";
echo "</body>";
echo "</html>";
