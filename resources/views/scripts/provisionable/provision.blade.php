@php
    use App\Services\Provisioning\ServerProvisioning;
@endphp

@include('scripts.provisionable.init')
@include('scripts.apt.apt-wait')
@include('scripts.provisionable.ping', ['step' => ServerProvisioning::CONNECTED])

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::CREATING_SWAP])
@include('scripts.provisionable.swapfile')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::UPGRADING_SYSTEM])
@include('scripts.provisionable.upgrade')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_BASE])
@include('scripts.provisionable.base')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::AUTHENTICATION_METHOD])
@include('scripts.provisionable.disablePass')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::UPDATING_HOSTNAME])
@include('scripts.provisionable.hostname')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::UPDATING_TIMEZONE])
@include('scripts.provisionable.setTimezone')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::XCLOUD_USER])
@include('scripts.provisionable.setupUser')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETUP_SSH])
@include('scripts.provisionable.ssh')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETTING_UP_GIT])
@include('scripts.provisionable.gitConfig')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETTING_UP_CLEANING_SCRIPT])
@include('scripts.provisionable.cleanupCorn')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETUP_FIREWALL])
@include('scripts.provisionable.firewall', ['server' => $server])

@include('scripts.provisionable.installWebServer', ['server' => $server])

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_NODE])
@include('scripts.node.install')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_REDIS])
@include('scripts.redis.install',[
        'phpVersion' => $server->php_version ?: App\Models\PhpVersion::DEFAULT,
        'isRedisSeven' => $server->isRedisSeven(),
        'defaultPassword' => $server->redis_password
    ])

# check if user has selected mysql 8 or not
@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_DATABASE])
@include('scripts.database.install', ['server' => $server])
@include('scripts.database.installRetry', ['server' => $server])

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_WP_CLI])
@include('scripts.wordpress.install')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::SETTING_SSH_PERMISSIONS])
@include('scripts.tools.chown')
@include('scripts.tools.cleanUp')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALL_MONITORING_SCRIPT])
@include('scripts.monitoring.installServerMonitoring', ['server' => $server, 'interval' => 60])

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::READY_TO_DO_MAGIC])

