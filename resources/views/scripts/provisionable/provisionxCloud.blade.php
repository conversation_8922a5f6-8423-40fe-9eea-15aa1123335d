@php
    use App\Services\Provisioning\ServerProvisioning;
@endphp

@include('scripts.provisionable.init')
@include('scripts.apt.apt-wait')
@include('scripts.apt.apt-gpg-key-update')
@include('scripts.provisionable.ping', ['step' => ServerProvisioning::CONNECTED])

# this is a temporary fix for the issue with the xcloud image and OpenSSL, can be removed after rebuilding the image
@include('scripts.provisionable.ping', ['step' => ServerProvisioning::UPGRADING_SYSTEM])
@include('scripts.provisionable.upgrade')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::UPDATING_HOSTNAME])
@include('scripts.provisionable.hostname')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::XCLOUD_USER])
@include('scripts.provisionable.setupUser')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETUP_SSH])
@include('scripts.provisionable.ssh')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETTING_UP_GIT])
@include('scripts.provisionable.gitConfig')

@include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETTING_UP_CLEANING_SCRIPT])
@include('scripts.provisionable.cleanupCorn')

# We are reinstalling the PHP, as the built-in xcloud image is missing imagick extension
# This can be removed after rebuilding the image
@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_PHP])

# We removed this part and replaced with the 'installWebServer' script below, as we do not want to make this complicated.
# Our stacks are growing so it's difficult to create different images for all of them
{{--@includeWhen($server->stack->isOpenLiteSpeed(), 'scripts.lsphp.install', ['phpVersion' => $server->php_version ?: App\Models\PhpVersion::DEFAULT, 'server' => $server])--}}
{{--keeping this here. we can remove this once we build a new image for xcloud managed--}}
{{--@includeWhen($server->stack->isOpenLiteSpeed(), 'scripts.openlitespeed.installHtaccessWatcher')--}}

{{--# Update PHP Default Settings for nginx--}}
{{--@includeWhen($server->stack->isNginx(), 'scripts.php.updateDefaultSettings', ['phpVersion' => $server->php_version, 'server' => $server])--}}
@include('scripts.provisionable.installWebServer', ['server' => $server])

# check if user has selected mysql 8 or not
@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_DATABASE])

# Replaced that with 'database.install' script below. We are installing database so no need to update the password
{{--@include('scripts.database.forceUpdateRootPassword', ['server' => $server])--}}
@include('scripts.database.install', ['server' => $server])
@include('scripts.database.installRetry', ['server' => $server])

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::SETTING_SSH_PERMISSIONS])
@include('scripts.tools.chown')
@include('scripts.tools.cleanUp')
@include('scripts.provisionable.disablePass')

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALL_MONITORING_SCRIPT])
@include('scripts.monitoring.installServerMonitoring', ['server' => $server, 'interval' => 60])

@include('scripts.provisionable.ping', ['step' => ServerProvisioning::READY_TO_DO_MAGIC])

