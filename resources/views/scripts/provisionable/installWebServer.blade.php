@php
    use App\Services\Provisioning\ServerProvisioning;
@endphp

@include('scripts.apt.apt-wait')

# Installing necessary things for Nginx
@if($server->stack->isNginx())
    {{-- // should be nginx only // start--}}
    @include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS])
    @include('scripts.provisionable.sudoAdjustment')

    @include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_PHP])
    @include('scripts.php.install', ['phpVersion' => $server->php_version ?: App\Models\PhpVersion::DEFAULT, 'server' => $server, 'skiUpdateDefaultSettings' => true])

    @include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_WEBSERVER])
    @include('scripts.nginx.install', ['phpVersion' => $server->php_version ?: App\Models\PhpVersion::DEFAULT, 'server' => $server ])

    @include('scripts.php.updateDefaultSettings', ['phpVersion' => $server->php_version ?: App\Models\PhpVersion::DEFAULT, 'server' => $server])
    {{-- // should be nginx only // end --}}
@endif

# Installing necessary things for OLS
@if($server->stack->isOpenLiteSpeed())
    {{-- // should be ols only // start--}}
    @include('scripts.provisionable.ping', ['step' =>  ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS])
    {{--@include('scripts.provisionable.sudoAdjustment')--}}
    @include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_PHP])
    @include('scripts.openlitespeed.install', ['phpVersion' => $server->php_version ?: App\Models\PhpVersion::DEFAULT, 'server' => $server ])

    @include('scripts.provisionable.ping', ['step' => ServerProvisioning::INSTALLING_WEBSERVER])
    @include('scripts.lsphp.install', ['phpVersion' => $server->php_version ?: App\Models\PhpVersion::DEFAULT, 'server' => $server ])
    {{-- // should be ols only // end --}}
@endif
