# create site user
# add user to sudo group
# check permissions

if id -u {{$site->site_user}} > /dev/null 2>&1; then
    echo "Site User {{$site->site_user}} user exists..."
else
    adduser --disabled-password --gecos "" {{$site->site_user}}
fi

if [[ $(cat /etc/group) =~ "isolated" ]];
then
    echo "Isolated group exists..."
else
    addgroup isolated
fi

usermod -aG www-data,isolated {{$site->site_user}}

# Configure ACL To Remove Isolated Group Access To The New User
setfacl -m g:isolated:000 /home/<USER>

PASSWORD=$(mkpasswd -m sha-512 {!! escapeshellarg($site->server->sudo_password) !!})
usermod --password $PASSWORD {{$site->site_user}}

mkdir -p /home/<USER>/.ssh
mkdir -p /home/<USER>/.xcloud
mkdir -p /home/<USER>/.wp-cli

@if($site->server->stack->isOpenLiteSpeed())
cat > /home/<USER>/.wp-cli/config.yml << 'EOF'
apache_modules:
- mod_rewrite
EOF
@endif

@include('scripts.site.cdIntoSiteAutomatically')

# reset permissions
chown -R {{$site->site_user}}:{{$site->site_user}} /home/<USER>
chmod -R 750 /home/<USER>
chmod 700 /home/<USER>/.ssh > /dev/null 2>&1
chmod 600 /home/<USER>/.ssh/* > /dev/null 2>&1
chmod 600 /home/<USER>/.ssh/id_rsa* > /dev/null 2>&1
chmod 640 /home/<USER>/.ssh/id_rsa*.pub > /dev/null 2>&1

@if($exisingPhpVersion ?? null)
    echo "PHP Version {{$exisingPhpVersion}} found. Deleting configuration."
    rm -f "/etc/php/{{ $exisingPhpVersion }}/fpm/pool.d/www-{{$site->site_user}}.conf"
@endif

@if($site->server->stack->isNginx())
SITE_WWW_CONF_PATH="/etc/php/{{ $site->php_version }}/fpm/pool.d/www-{{$site->site_user}}.conf"

cp /etc/php/{{ $site->php_version }}/fpm/pool.d/www.conf $SITE_WWW_CONF_PATH

sed -i "s/\[www\]/\[{{$site->site_user}}\]/" $SITE_WWW_CONF_PATH
sed -i "s/^user = xcloud/user = {{$site->site_user}}/" $SITE_WWW_CONF_PATH
sed -i "s/^group = xcloud/group = {{$site->site_user}}/" $SITE_WWW_CONF_PATH
sed -i "s/^listen = .*/listen = \/run\/php\/php{{ $site->php_version }}-fpm-{{$site->site_user}}.sock/" $SITE_WWW_CONF_PATH
sed -i "s/^;\?listen\.owner.*/listen.owner = {{$site->site_user}}/" $SITE_WWW_CONF_PATH
sed -i "s/^;\?listen\.group.*/listen.group = xcloud/" $SITE_WWW_CONF_PATH
sed -i "s/^;\?listen\.mode.*/listen.mode = 0660/" $SITE_WWW_CONF_PATH
sed -i "s/^;\?request_terminate_timeout\( *\)=.*/request_terminate_timeout = 60/" "$SITE_WWW_CONF_PATH"

echo "Configuration updated at $SITE_WWW_CONF_PATH"
@endif

# Remove Permissions To The xCloud Directory For The Isolated Group
setfacl -m g:isolated:000 /home/<USER>

# Configure ACL To Remove Isolated Group Access to site root, But keep traverse access so user can still access the site
setfacl -m g:isolated:--x /var/www

echo "Site User {{$site->site_user}} created and configured..."
