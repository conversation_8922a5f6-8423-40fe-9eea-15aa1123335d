# Configure DNS resolver to use Cloudflare DNS

CONFIG_FILE="/etc/systemd/resolved.conf"
BACKUP_FILE="/etc/systemd/resolved.conf.bak"

echo "🔧 Configuring systemd-resolved to use Cloudflare DNS..."

# 1. Backup original config (once)
if [ ! -f "$BACKUP_FILE" ]; then
  cp "$CONFIG_FILE" "$BACKUP_FILE"
  echo "✅ Backup created at $BACKUP_FILE"
else
  echo "ℹ️  Backup already exists at $BACKUP_FILE"
fi

# 2. Ensure [Resolve] section exists
if ! grep -q "^\[Resolve\]" "$CONFIG_FILE"; then
  echo -e "\n[Resolve]" >> "$CONFIG_FILE"
fi

# 3. Remove any existing DNS= or FallbackDNS= lines
sed -i '/^#*DNS=/d; /^#*FallbackDNS=/d' "$CONFIG_FILE"

# 4. Add Cloudflare + Google fallback entries
sed -i '/^\[Resolve\]/a DNS=1.1.1.1 1.0.0.1\nFallbackDNS=8.8.8.8 8.8.4.4' "$CONFIG_FILE"

# 5. Restart the resolver service
systemctl restart systemd-resolved

# 6. Re-point /etc/resolv.conf to the stub resolver
ln -sf /run/systemd/resolve/stub-resolv.conf /etc/resolv.conf

# 7. Verify
echo "✅ DNS Servers now in use:"
resolvectl status | grep "DNS Servers"