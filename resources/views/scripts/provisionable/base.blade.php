apt_wait

# Add A Few PPAs To Stay Current

apt-get install -y --force-yes software-properties-common
@if($server->stack->isNginx())
apt-add-repository ppa:ondrej/nginx -y
apt-add-repository ppa:ondrej/php -y
@endif
add-apt-repository universe -y

apt_wait

apt-get update -y

# Base Packages

apt_wait

apt-get install -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold" -y --force-yes build-essential curl pkg-config fail2ban gcc g++ git libmcrypt4 libpcre3-dev \
make python3 python3-pip supervisor ufw zip unzip zsh ncdu uuid-runtime acl whois libpng-dev libmagickwand-dev gpg lsb-release

# Add Redis apt-repository key https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/install-redis-on-linux/
curl -fsSL https://packages.redis.io/gpg | sudo gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg
sudo chmod 644 /usr/share/keyrings/redis-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/redis.list
