# Install PHPMyAdmin Site

@include('scripts.provisionable.isolatedUser', ['site' => $site])

# check if $site->name is not empty, if empty exit the script with an error message
if [ -z "{{ $site->name }}" ]; then
    echo "Error: Domain name is missing, please provide a domain name for the site to install the site."
    exit 1
fi

mkdir -p /var/www/{{ $site->name }}
@include('scripts.site.updatePermission', ['site' => $site])

# site site
sudo -i -u {{$site->site_user}} bash << 'PHPMYADMIN_INSTALL'

cd /var/www/{{ $site->name }}

# Download latest PHPMyAdmin
if ! wget https://www.phpmyadmin.net/downloads/phpMyAdmin-latest-all-languages.zip -O phpmyadmin.zip; then
    echo "Error: Failed to download PHPMyAdmin. Please check your internet connection and try again."
    exit 1
fi

if ! unzip phpmyadmin.zip; then
    echo "Error: Failed to extract PHPMyAdmin archive. The downloaded file may be corrupted."
    exit 1
fi

if ! mv phpMyAdmin-*-all-languages/* .; then
    echo "Error: Failed to move PHPMyAdmin files. The extraction may have failed."
    exit 1
fi

rm -rf phpMyAdmin-*-all-languages phpmyadmin.zip

# Create config file
cp config.sample.inc.php config.inc.php

# Generate blowfish secret
BLOWFISH_SECRET=$(openssl rand -base64 32 | tr -d '\n')
sed -i "s#\$cfg\['blowfish_secret'\] = '.*';#\$cfg['blowfish_secret'] = '$BLOWFISH_SECRET';#" config.inc.php

# Configure authentication
sed -i "s/\$cfg\['Servers'\]\[\$i\]\['auth_type'\] = 'cookie'/\$cfg\['Servers'\]\[\$i\]\['auth_type'\] = 'http'/" config.inc.php

# Create temp directory
mkdir -p tmp
chmod 777 tmp

PHPMYADMIN_INSTALL

# Configure ACL To Remove Isolated Group Access To The New Site
setfacl -m g:isolated:000 /var/www/{{ $site->name }}
