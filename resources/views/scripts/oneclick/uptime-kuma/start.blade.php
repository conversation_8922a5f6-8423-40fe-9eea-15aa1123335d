# Install Uptime Kuma
sudo -i -u {{$site->site_user}} bash << 'UPTIME_KUMA_INSTALL'

cd /var/www/{{ $site->name }}

# Clone Uptime Kuma repository
git clone https://github.com/louislam/uptime-kuma.git .

# Create .env file for database configuration
cat > .env << EOF
UPTIME_KUMA_DB_TYPE=mariadb
UPTIME_KUMA_DB_HOSTNAME={{ $site->database_host }}
UPTIME_KUMA_DB_PORT={{ $site->database_port }}
UPTIME_KUMA_DB_NAME={{ $site->database_name }}
UPTIME_KUMA_DB_USERNAME={{ $site->database_user }}
UPTIME_KUMA_DB_PASSWORD={{ $site->database_password }}
PORT={{ $site->port ?? 3001 }}
EOF

# Install dependencies
export NODE_OPTIONS="--max-old-space-size=4096"
npm install
npm run build

# Create a startup script
cat > start.sh << EOF
#!/bin/bash
cd "\$(dirname "\$0")"
export NODE_ENV=production
export PORT={{ $site->port ?? 3001 }}

# Database configuration
export UPTIME_KUMA_DB_TYPE=mariadb
export UPTIME_KUMA_DB_HOSTNAME={{ $site->database_host }}
export UPTIME_KUMA_DB_PORT={{ $site->database_port }}
export UPTIME_KUMA_DB_NAME={{ $site->database_name }}
export UPTIME_KUMA_DB_USERNAME={{ $site->database_user }}
export UPTIME_KUMA_DB_PASSWORD={{ $site->database_password }}

node server/server.js
EOF

chmod +x start.sh

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: "{{ $site->pm2ProcessName() }}",
    script: "./start.sh",
    env: {
      PORT: {{ $site->port ?? 3001 }},
      NODE_ENV: "production",

      // Database configuration
      UPTIME_KUMA_DB_TYPE: "mariadb",
      UPTIME_KUMA_DB_HOSTNAME: "{{ $site->database_host }}",
      UPTIME_KUMA_DB_PORT: {{ $site->database_port }},
      UPTIME_KUMA_DB_NAME: "{{ $site->database_name }}",
      UPTIME_KUMA_DB_USERNAME: "{{ $site->database_user }}",
      UPTIME_KUMA_DB_PASSWORD: "{{ $site->database_password }}"
    },
    watch: false,
    autorestart: true
  }]
};
EOF

# Start uptime-kuma with PM2 and save the process list
pm2 start ecosystem.config.js

# Save PM2 process list so it starts on reboot
pm2 save

UPTIME_KUMA_INSTALL

@include('scripts.oneclick.n8n.startup', [ 'site' => $site ])
