# Stop and remove n8n PM2 process

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "PM2 is not installed, nothing to stop"
    exit 0
fi

# Check if the n8n process exists for this site
if pm2 list | grep -q "{{ $site->pm2ProcessName() }}"; then
    echo "Stopping n8n PM2 process for {{ $site->name }}"
    # Stop the process
    pm2 stop "{{ $site->pm2ProcessName() }}"
    # Delete the process from PM2
    pm2 delete "{{ $site->pm2ProcessName() }}"
    # Save the PM2 process list
    pm2 save
    echo "Successfully stopped and removed n8n PM2 process for {{ $site->name }}"
else
    echo "No n8n PM2 process found for {{ $site->name }}"
fi

# Check if there's a systemd service for this user and disable it
if systemctl list-unit-files | grep -q "pm2-{{$site->site_user}}"; then
    echo "Disabling and stopping pm2-{{$site->site_user}} service"
    systemctl stop pm2-{{$site->site_user}}
    systemctl disable pm2-{{$site->site_user}}

    # Remove the PM2 startup script for this user if it exists
    PM2_STARTUP_PATH=$(systemctl cat pm2-{{$site->site_user}} 2>/dev/null | grep "ExecStart=" | sed 's/ExecStart=//')
    if [ -n "$PM2_STARTUP_PATH" ] && [ -f "$PM2_STARTUP_PATH" ]; then
        echo "Removing PM2 startup script: $PM2_STARTUP_PATH"
        rm -f "$PM2_STARTUP_PATH"
    fi

    echo "Successfully disabled and stopped pm2-{{$site->site_user}} service"
fi
