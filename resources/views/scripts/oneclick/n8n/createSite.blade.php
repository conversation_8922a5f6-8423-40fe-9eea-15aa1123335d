# Install n8n Site

@include('scripts.provisionable.isolatedUser', ['site' => $site])

# check if $site->name is not empty, if empty exit the script with an error message
if [ -z "{{ $site->name }}" ]; then
    echo "Error: Domain name is missing, please provide a domain name for the site to install the site."
    exit 1
fi

mkdir -p /var/www/{{ $site->name }}
@include('scripts.site.updatePermission', ['site' => $site])

# Install Node.js if not already installed
if ! command -v node &> /dev/null; then
    curl --silent --location https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y --force-yes nodejs
    npm install -g yarn
fi

# Install PM2 if not already installed
if ! command -v pm2 &> /dev/null; then
    echo "Installing PM2..."
    npm install -g pm2
fi

# Install n8n if not already installed
if ! command -v n8n &> /dev/null; then
    echo "Installing n8n..."
    npm install -g n8n
fi

@include('scripts.oneclick.n8n.start')

# Configure ACL To Remove Isolated Group Access To The New Site
setfacl -m g:isolated:000 /var/www/{{ $site->name }}
