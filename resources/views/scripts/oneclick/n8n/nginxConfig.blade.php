# XCLOUD CONFIG BEFORE
include xcloud-conf/{{ $site->name }}/before/*;

server {
    # Ports to listen on
@if($site->hasSuccessfulSslCertificate())
    @include('scripts.nginx.listenSsl', ['site' => $site])
@else
    listen 80;
    listen [::]:80;
@endif

    # Server name to listen for
    server_name {{ $site->manager()->siteNginxServerName() }};

    # Path to document root
    root {{ $site->manager()->siteDocumentRoot() }};

    @includeWhen($site->ipAddresses->isNotEmpty(),'scripts.nginx.whitelist-blacklist', ['ipAddresses' => $site->ipAddresses])

@if($site->getMeta('enable_ai_bot_blocker'))
    if ($block_ai_bot = 1) {
        return 403;
    }
@endif

@if($site->hasSuccessfulSslCertificate())
    # Paths to certificate files.
    ssl_certificate {{ $site->sslCertificate->getCertificatePath() }};
    ssl_certificate_key {{ $site->sslCertificate->getPrivateKeyPath() }};
@endif

@if($site->hasBasicAuthEnabled())
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/xcloud-conf/{{ $site->name }}/.htpasswd;
@endif
    server_tokens off;
    add_header X-Frame-Options "{{$site->getNginxXFrameOption()}}";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    # XCLOUD CONFIG SERVER
    include xcloud-conf/{{ $site->name }}/server/*;

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    # Proxy all requests to n8n running on port {{ $site->port ?? 5678 }}
    location / {
        proxy_pass http://127.0.0.1:{{ $site->port ?? 5678 }};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_buffering off;
        proxy_read_timeout 86400;
    }

    location ~* /\.(?!well-known\/) {
        deny all;
    }

    location ~\.(ini|log|conf)$ {
        deny all;
    }

    # Overrides logs defined in nginx.conf, allows per site logs.
    access_log /var/log/nginx/{{ $site->name }}-access.log;
    error_log /var/log/nginx/{{ $site->name }}-error.log;

@foreach($site->redirections as $redirection)
    # xCloud Redirection #{{ $redirection->id }}
    rewrite {{ $redirection->from }} {{ $redirection->to }} {{ $redirection->nginx_redirect_type }};
@endforeach

@if($site->has7gFirewallEnabled())
    include /etc/nginx/xcloud-conf/7g.conf;
@endif
}

@includeWhen($site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsSSL', ['site' => $site])
@includeWhen(!$site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsNonSSL', ['site' => $site])
@include('scripts.wordpress.nginxRedirectDomains', ['site' => $site])

# XCLOUD CONFIG AFTER
include xcloud-conf/{{ $site->name }}/after/*;
