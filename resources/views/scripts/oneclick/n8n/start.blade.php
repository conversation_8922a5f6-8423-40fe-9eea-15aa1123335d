# Start n8n
sudo -i -u {{$site->site_user}} bash << 'N8N_INSTALL'

cd /var/www/{{ $site->name }}

# Create a basic package.json file
cat > package.json << 'EOF'
{
  "name": "n8n-instance",
  "version": "1.0.0",
  "description": "n8n workflow automation",
  "scripts": {
    "start": "n8n start"
  },
  "dependencies": {
    "n8n": "latest"
  }
}
EOF

# Create a startup script
cat > start.sh << EOF
#!/bin/bash
cd "\$(dirname "\$0")"
export N8N_PORT={{ $site->port ?? 5678 }}
export N8N_PROTOCOL=https
export N8N_HOST={{ $site->name }}
export N8N_PATH=/
export NODE_ENV=production

# Database configuration
export DB_TYPE=mysqldb
export DB_MYSQLDB_HOST={{ $site->database_host }}
export DB_MYSQLDB_PORT={{ $site->database_port }}
export DB_MYSQLDB_DATABASE={{ $site->database_name }}
export DB_MYSQLDB_USER={{ $site->database_user }}
export DB_MYSQLDB_PASSWORD={{ $site->database_password }}

# Start n8n
n8n start
EOF

chmod +x start.sh

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: "{{ $site->pm2ProcessName() }}",
    script: "./start.sh",
    env: {
      N8N_PORT: {{ $site->port ?? 5678 }},
      N8N_PROTOCOL: "https",
      N8N_HOST: "{{ $site->name }}",
      N8N_PATH: "/",
      NODE_ENV: "production",

      // Database configuration
      DB_TYPE: "mysqldb",
      DB_MYSQLDB_HOST: "{{ $site->database_host }}",
      DB_MYSQLDB_PORT: {{ $site->database_port }},
      DB_MYSQLDB_DATABASE: "{{ $site->database_name }}",
      DB_MYSQLDB_USER: "{{ $site->database_user }}",
      DB_MYSQLDB_PASSWORD: "{{ $site->database_password }}",
    },
    watch: false,
    autorestart: true
  }]
};
EOF

# Start n8n with PM2 and save the process list
pm2 start ecosystem.config.js

# Save PM2 process list so it starts on reboot
pm2 save

N8N_INSTALL

@include('scripts.oneclick.n8n.startup', [ 'site' => $site ])
