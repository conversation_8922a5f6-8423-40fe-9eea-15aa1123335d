docRoot                   $VH_ROOT
vhDomain                  {{ $site->name }} {{ ($site->isMultiSite() && $site->isMultiSiteSubdomain()) ? ',*.' . $site->name : null }}
@php
    $aliases = $site->getDomainNames()->reject(fn($value) => $value == $site->name);
@endphp
@if($aliases->count() > 0)
vhAliases                 {{ $aliases->implode(',') }}
@endif
enableGzip                1
enableIpGeo               1

index {
  useServer               0
  indexFiles              index.js
  autoIndex               1
}

@if($site->ipAddresses->isNotEmpty())
rewrite  {
    enable                1
@foreach($site->ipAddresses->where('type',\App\Enums\IPAddressType::WHITELIST) as $whiteIp)
allow {{ $whiteIp->ip_address }}
@endforeach
@if($site->ipAddresses->where('type',\App\Enums\IPAddressType::BLACKLIST)->isNotEmpty())
rewriteCond %{HTTP:X-Forwarded-For} ^({{str($site->ipAddresses->where('type',\App\Enums\IPAddressType::BLACKLIST)->pluck('ip_address')->join('|'))->replace('.','\.')}})$
rewriteRule .* - [F,L]
@endif
}
@endif

@if($site->hasBasicAuthEnabled())
realm ProtectedSite {
  userDB  {
    location              $SERVER_ROOT/conf/vhosts/$VH_NAME/htpasswd
  }
  groupDB  {
    location              $SERVER_ROOT/conf/vhosts/$VH_NAME/htgroup
  }
}
@endif

rewrite {
  enable                  1
  autoLoadHtaccess        1
@foreach($site->redirections as $redirection)
  # xCloud Redirection #{{ $redirection->id }}
  rewriteRule {{ $redirection->from }} {{ $redirection->to }} {!! $redirection->ols_redirect_type !!}
@endforeach
  # Deny access to any files with a .ini, .log, .conf extension
  rewriteRule ^(.*)\.(ini|log|conf)$ - [F,L]
  # Proxy all requests to the Node.js application
  rewriteRule ^/(.*) HTTP://{{ $site->port ?? 5678 }}//$1 [P,L,E=PROXY-HOST:{{ $site->name }}]
}

websocket / {
  address                 127.0.0.1:{{ $site->port ?? 5678 }}
}

extprocessor {{ $site->port ?? 5678 }} {
  type                    proxy
  address                 127.0.0.1:{{ $site->port ?? 5678 }}
  maxConns                5
  initTimeout             20
  retryTimeout            20
  respBuffer              0
}

# Only include these contexts if the files/directories exist
# For Node.js applications, these are typically not needed as they're handled by the Node.js app

@if($site->hasSuccessfulSslCertificate())
vhssl  {
    keyFile                 {{ $site->sslCertificate->getPrivateKeyPath() }}
    certFile                {{ $site->sslCertificate->getCertificatePath() }}
    certChain               1
    sslProtocol             30
    enableSpdy              15
}
@endif

errorlog /var/log/lsws/{{ $site->name }}.err {
    useServer               0
    logLevel                ERROR
    rollingSize             10M
}

accesslog /var/log/lsws/{{ $site->name }}-access.log {
    useServer               0
    logFormat               %a %l %u %t "%r" %>s %O "%{Referer}i" "%{User-Agent}i"
    logHeaders              5
    rollingSize             10M
    keepDays                30
    compressArchive         1
}

errorPage 403 {
    url                     /usr/local/lsws/DEFAULT/html/403.html
}

errorPage 404 {
    url                     /usr/local/lsws/DEFAULT/html/404.html
}

errorPage 500 {
    url                     /usr/local/lsws/DEFAULT/html/500.html
}

module cache {
    ls_enabled {{ $site->hasFullPageCaching() ? '1' : '0' }}
}

include /usr/local/lsws/conf/vhosts/{{ $site->name }}/vhconf.server.conf
