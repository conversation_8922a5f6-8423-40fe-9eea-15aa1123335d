virtualhost {{ $site->name }} {
    listeners               http, http6, https, https6
    vhRoot                  {{ $site->manager()->siteDocumentRoot() }}
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

@foreach($site->getRedirectDomains() as $redirectDomain)
virtualhost {{ $redirectDomain }} {
    listeners               http, http6, https, https6
    vhDomain                {{ $redirectDomain }}
    vhRoot                  {{ $site->manager()->siteDocumentRoot() }}
    docRoot                 $VH_ROOT
    enableScript            1

    rewrite  {
        enable                  1
        autoLoadHtaccess        1
    }

    context / {
      type                    static
      allowBrowse             1
      indexFiles              index.html
      addDefaultCharset       off
      rewrite  {
        enable                1
        rewriteRule ^(.*)$ {{ $site->site_url }}/$1 [R=301,L]
      }
    }
}
@endforeach

include /usr/local/lsws/conf/vhosts/{{ $site->name }}/vhconf.after.conf
