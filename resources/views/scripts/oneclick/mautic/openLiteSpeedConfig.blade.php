virtualhost {{ $site->name }} {
    listeners               http, http6, https, https6
    vhRoot                  {{ $site->manager()->siteDocumentRoot() }}
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

extprocessor lsphp{{ $site->ls_php_version }}-{{$site->site_user}} {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp{{ $site->ls_php_version }}-{{$site->site_user}}.sock
  maxConns                30
  env                     PHP_LSAPI_CHILDREN=30
  env                     PHP_LSAPI_MAX_REQUESTS=5000
  env                     LSAPI_AVOID_FORK=200M
  env                     LSPHP_ENABLE_USER_INI=on
  initTimeout             60
  retryTimeout            0
  respBuffer              0
  autoStart               2
  path                    /usr/local/lsws/lsphp{{ $site->ls_php_version }}/bin/lsphp
  env                     PHP_INI_SCAN_DIR=/home/<USER>/lsphp/{{ $site->name }}:/usr/local/lsws/lsphp{{ $site->ls_php_version }}/etc/php/{{ $site->php_version }}/mods-available
  extUser                 {{ $site->site_user }}
  extGroup                {{ $site->site_user }}
}

@foreach($site->getRedirectDomains() as $redirectDomain)
virtualhost {{ $redirectDomain }} {
    listeners               http, http6, https, https6
    vhDomain                {{ $redirectDomain }}
    vhRoot                  {{ $site->manager()->siteDocumentRoot() }}
    docRoot                 $VH_ROOT
    enableScript            1

    rewrite  {
        enable                  1
        autoLoadHtaccess        1
    }

    context / {
      type                    static
      allowBrowse             1
      indexFiles              index.html
      addDefaultCharset       off
      rewrite  {
        enable                1
        rewriteRule ^(.*)$ {{ $site->site_url }}/$1 [R=301,L]
      }
    }
}
@endforeach

include /usr/local/lsws/conf/vhosts/{{ $site->name }}/vhconf.after.conf
