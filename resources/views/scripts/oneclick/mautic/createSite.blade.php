# Install Mautic Site

@include('scripts.provisionable.isolatedUser', ['site' => $site])

# check if $site->name is not empty, if empty exit the script with an error message
if [ -z "{{ $site->name }}" ]; then
    echo "Error: Domain name is missing, please provide a domain name for the site to install the site."
    exit 1
fi

mkdir -p /var/www/{{ $site->name }}

@include('scripts.site.updatePermission', ['site' => $site])

@if($site->server->stack->isOpenLiteSpeed())
# Set date.timezone to UTC
# This is a workaround to fix the issue with the date.timezone not being set in the php.ini file
sed -i 's/;date.timezone =/date.timezone = UTC/' /usr/local/lsws/lsphp{{  $site->ls_php_version }}/etc/php/{{  $site->php_version }}/litespeed/php.ini
@endif

# Set PHP settings for Mautic
@php
$envVars = [
    'max_execution_time' => 240,
    'memory_limit' => '512M',
    'upload_max_filesize' => '100M',
    'post_max_size' => '100M',
    'date.timezone' => 'UTC'
];
@endphp
@include('scripts.php.updateSitePhpSettings', ['site' => $site, 'server' => $site->server, 'envVars' => $envVars])

# Install Mautic
sudo -i -u {{$site->site_user}} bash << 'MAUTIC_INSTALL'

cd /var/www/{{ $site->name }}

# Download latest Mautic
LATEST_URL=$(
  curl -s https://api.github.com/repos/mautic/mautic/releases/latest \
    | grep '"browser_download_url":' \
    | grep -v 'update' \
    | head -n1 \
    | cut -d '"' -f4
)

wget -q "$LATEST_URL" -O mautic.zip

if [ $? -ne 0 ] || [ ! -f mautic.zip ] || [ ! -s mautic.zip ]; then
  echo "Failed to download Mautic or downloaded file is empty"
  exit 1
fi

unzip -q mautic.zip
# Extract files directly to current directory without moving
# This avoids the "same file" errors from mv command
rm -rf mautic.zip

# Create required directories with proper permissions
mkdir -p var/logs
mkdir -p var/cache
mkdir -p var/spool
mkdir -p var/tmp
mkdir -p media/files
mkdir -p config

# Clear cache
{{$site->php}} bin/console cache:clear

# Run the command line installer with parameters
{{$site->php}} bin/console mautic:install \
  --db_driver=pdo_mysql \
  --db_host={!! escapeshellarg($site->database_host) !!} \
  --db_port={!! escapeshellarg($site->database_port) !!} \
  --db_name={!! escapeshellarg($site->database_name) !!} \
  --db_user={!! escapeshellarg($site->database_user) !!} \
  --db_password={!! escapeshellarg($site->database_password) !!} \
  --db_backup_tables=false \
  --admin_firstname='Admin' \
  --admin_lastname='User' \
  --admin_username={!! escapeshellarg($site->admin_user ?: 'admin') !!} \
  --admin_email={!! escapeshellarg($site->getMeta('admin_email', 'admin@'.$site->name)) !!} \
  --admin_password={!! escapeshellarg($site->admin_password) !!} \
  {!! escapeshellarg($site->site_url) !!}

# Clear cache
{{$site->php}} bin/console cache:clear

MAUTIC_INSTALL


# Configure ACL To Remove Isolated Group Access To The New Site
setfacl -m g:isolated:000 /var/www/{{ $site->name }}
