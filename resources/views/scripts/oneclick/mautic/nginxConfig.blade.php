# XCLOUD CONFIG BEFORE
include xcloud-conf/{{ $site->name }}/before/*;

server {
    # Ports to listen on
@if($site->hasSuccessfulSslCertificate())
    @include('scripts.nginx.listenSsl', ['site' => $site])
@else
    listen 80;
    listen [::]:80;
@endif

    # Server name to listen for
    server_name {{ $site->manager()->siteNginxServerName() }};

    # Path to document root
    root {{ $site->manager()->siteDocumentRoot() }};

    @includeWhen($site->ipAddresses->isNotEmpty(),'scripts.nginx.whitelist-blacklist', ['ipAddresses' => $site->ipAddresses])

@if($site->getMeta('enable_ai_bot_blocker'))
    if ($block_ai_bot = 1) {
        return 403;
    }
@endif

@if($site->hasSuccessfulSslCertificate())
    # Paths to certificate files.
    ssl_certificate {{ $site->sslCertificate->getCertificatePath() }};
    ssl_certificate_key {{ $site->sslCertificate->getPrivateKeyPath() }};
@endif

@if($site->hasBasicAuthEnabled())
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/xcloud-conf/{{ $site->name }}/.htpasswd;
@endif
    server_tokens off;
    add_header X-Frame-Options "{{$site->getNginxXFrameOption()}}";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    # XCLOUD CONFIG SERVER
    include xcloud-conf/{{ $site->name }}/server/*;

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    # Mautic specific configuration
    index index.php index.html;

    # Deny access to sensitive files
    location ~ /\.(git|env|htaccess|config) {
        deny all;
        return 404;
    }

    # Deny access to certain Mautic directories
    location ~ ^/(vendor|translations|bin)/ {
        deny all;
        return 404;
    }

    # Deny access to dot files
    location ~ /\. {
        deny all;
        return 404;
    }

    location ~* (^|/)\.(env|git|htaccess)(\.|$)|\.(json|lock|ini|log|conf|sql)(\.|$) {
        deny all;
    }

    # Handle PHP files
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php{{ $site->php_version }}-fpm-{{$site->site_user}}.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_param HTTPS $https if_not_empty;
        fastcgi_param HTTP_PROXY "";
        fastcgi_intercept_errors off;
        fastcgi_buffer_size 16k;
        fastcgi_buffers 4 16k;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_read_timeout 300;
    }

    # Handle other requests
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

@foreach($site->redirections as $redirection)
    # xCloud Redirection #{{ $redirection->id }}
    rewrite {{ $redirection->from }} {{ $redirection->to }} {{ $redirection->nginx_redirect_type }};
@endforeach

@if($site->has7gFirewallEnabled())
    include /etc/nginx/xcloud-conf/7g.conf;
@endif
}

@includeWhen($site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsSSL', ['site' => $site])
@includeWhen(!$site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsNonSSL', ['site' => $site])
@include('scripts.wordpress.nginxRedirectDomains', ['site' => $site])

# XCLOUD CONFIG AFTER
include xcloud-conf/{{ $site->name }}/after/*;
