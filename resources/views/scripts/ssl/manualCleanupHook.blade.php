
# upload cleanup hook script
cat > /etc/letsencrypt/xcloud/domain-challenge/cleanup.sh << 'EOF'
#!/bin/bash

{{--base_url="{{ env('APP_URL') }}/api/callback/certbot/domain-challenge"--}}
{{--hash_id="{{ hashid_encode($domainChallenge->id) }}"  # Replace your_hash_id_here with your actual hash ID--}}
{{--url="${base_url}/${hash_id}/acme-challenge-done"--}}

url="{!! callback_url('/api/callback/certbot/domain-challenge/'.hashid_encode($domainChallenge->id).'/acme-challenge-done') !!}"

{{--# Making HTTP request and capturing the status code--}}
status_code=$(curl -o /dev/null -s -w "%{http_code}\n" "$url")

# remove the domain challenge script and cleanup script
sudo rm -f /etc/letsencrypt/xcloud/domain-challenge/domain.log

EOF