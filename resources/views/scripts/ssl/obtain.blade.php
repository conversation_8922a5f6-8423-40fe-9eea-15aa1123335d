export DEBIAN_FRONTEND=noninteractive

sudo apt install software-properties-common -y
sudo add-apt-repository universe -y
@include('scripts.apt.apt-gpg-key-update')
sudo apt install certbot @if($site->server->stack->isNginx()) python3-certbot-nginx @endif -y

@if($site->server->stack->isNginx())
    sudo certbot --nginx certonly --cert-name={{ $site->name }} -d {{ $site->getAllDomainNames()->join(',') }} -m {{ $email }} --agree-tos -n
@else
    sudo certbot --webroot certonly -w {{ $site->manager()->siteDocumentRoot() }} --cert-name={{ $site->name }} -d {{ $site->getAllDomainNames()->join(',') }} -m {{ $email }} --agree-tos -n
@endif

sudo certbot certificates --cert-name={{ $site->name }}

# throw error if certificate not found
if [ $? -ne 0 ]; then
    echo "Certificate not found"
    exit 1
fi

# throw err if certificate file not found
if [ ! -f {{ $certificate->getCertificatePath() }} ]; then
    echo "Certificate file not found"
    exit 1
fi

if [ ! -f {{ $certificate->getPrivateKeyPath() }} ]; then
    echo "Certificate privatekey file not found"
    exit 1
fi
