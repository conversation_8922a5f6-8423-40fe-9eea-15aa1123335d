# XCLOUD CONFIG BEFORE
include xcloud-conf/{{ $site->name }}/before/*;

@if($site->hasFullPageCaching())
# Fastcgi cache path
fastcgi_cache_path /etc/nginx/cache/{{ $site->name }} levels=1:2 keys_zone={{ $site->name }}:100m inactive={{ $site->fullPageCachingDuration() }};
# End of Fastcgi cache path
@endif
@if($site->isMultiSiteDirectory())
# WordPress multisite subdirectory rules.
map $uri $blogname{
    ~^/(?<blogpath>[^/]+/)files/(.*)       $blogpath ;
}

map $blogname $blogid{
    default -999;
    #Ref: https://wordpress.org/extend/plugins/nginx-helper/
    #include /var/www/wordpress/wp-content/plugins/nginx-helper/map.conf ;
}
@endif

server {
    # Ports to listen on
@if($site->hasSuccessfulSslCertificate())
    @include('scripts.nginx.listenSsl', ['site' => $site])
@else
    listen 80;
    listen [::]:80;
@endif

    # Server name to listen for
    server_name {{ $site->manager()->siteNginxServerName() }};

    # Path to document root
    root {{ $site->manager()->siteDocumentRoot() }};

    @includeWhen($site->ipAddresses->isNotEmpty(),'scripts.nginx.whitelist-blacklist', ['ipAddresses' => $site->ipAddresses])

@if($site->getMeta('enable_ai_bot_blocker'))
    if ($block_ai_bot = 1) {
        return 403;
    }
@endif

@if($site->hasSuccessfulSslCertificate())
    # Paths to certificate files.
    ssl_certificate {{ $site->sslCertificate->getCertificatePath() }};
    ssl_certificate_key {{ $site->sslCertificate->getPrivateKeyPath() }};
@endif

@if($site->hasBasicAuthEnabled())
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/xcloud-conf/{{ $site->name }}/.htpasswd;
@endif
    server_tokens off;
    {{-- Remove X-Frame-Options, X-Content-Type-Options and X-XSS-Protection for phpmyadmin because it already has it's own security measures --}}
@if(!$site->type->isPhpMyAdmin())
    add_header X-Frame-Options "{{$site->getNginxXFrameOption()}}";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
@endif
    @includeWhen($site->hasCachePlugin(), 'scripts.site.cache.nginx-configs-for-wp-plugins.'.$site->getActiveCachePlugin().'-NginxConfig',['site' => $site])

@if($site->hasCachePluginThatHandleOwnDirectory() || !$site->hasCachePlugin())
    location / {
        try_files $uri $uri/ /index.php?$args;
    }
@endif
    # File to be used as index
    index index.php index.html;

    # XCLOUD CONFIG SERVER
    include xcloud-conf/{{ $site->name }}/server/*;

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    location ~* ^.+\.(ogg|ogv|svg|svgz|eot|otf|woff|woff2|mp4|ttf|ttc|rss|atom|jpg|jpeg|gif|png|ico|webp|zip|tgz|gz|rar|bz2|doc|xls|exe|ppt|tar|mid|midi|wav|bmp|rtf|css|js)$ {
        access_log off; log_not_found off; expires max; add_header Cache-Control "public";
    }

    location ~* /\.(?!well-known\/) {
        deny all;
    }

    location ~\.(ini|log|conf)$ {
        deny all;
    }

@if($site->isOneClickApp())
    location ~* (^|/)\.(env|git|htaccess)(\.|$)|\.(json|lock|ini|log|conf|sql)(\.|$) {
        deny all;
    }
@endif

@if(!$site->enablePhpExecutionOnUploadDirectory())
    # Deny access to any files with a .php extension in the uploads directory
    location ~* /(?:uploads|files)/.*\.php$ {
        deny all;
    }
@endif

@if(!$site->enableXmlRpc())
    location = /xmlrpc.php {
        deny all;
    }
@endif

    # Overrides logs defined in nginx.conf, allows per site logs.
    access_log /var/log/nginx/{{ $site->name }}-access.log;
    error_log /var/log/nginx/{{ $site->name }}-error.log;
@includeWhen($site->hasFullPageCaching(), 'scripts.wordpress.fullPageCache', ['site' => $site])
@foreach($site->redirections as $redirection)
    # xCloud Redirection #{{ $redirection->id }}
    rewrite {{ $redirection->from }} {{ $redirection->to }} {{ $redirection->nginx_redirect_type }};
@endforeach
@if($site->isLaravel())
    error_page 404 /index.php;
@endif
@if($site->isMultiSiteDirectory())
    location ~ ^(/[^/]+/)?files/(.+) {
        try_files /wp-content/blogs.dir/$blogid/files/$2 /wp-includes/ms-files.php?file=$2 ;
        access_log off;     log_not_found off; expires max;
    }

    #avoid php readfile()
    location ^~ /blogs.dir {
        internal;
        alias /var/www/{{ $site->name }}/wp-content/blogs.dir ;
        access_log off;     log_not_found off; expires max;
    }

    if (!-e $request_filename) {
        rewrite /wp-admin$ $scheme://$host$request_uri/ permanent;
        rewrite ^(/[^/]+)?(/wp-.*) $2 last;
        rewrite ^(/[^/]+)?(/.*\.php) $2 last;
    }
@endif

    location ~ \.php$ {
@if($site->isWordPress())
        try_files $uri = 404;
@endif
        fastcgi_buffer_size 128k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_buffers 4 256k;

        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php{{ $site->php_version }}-fpm-{{$site->site_user}}.sock;
@if($site->hasFullPageCaching())
        fastcgi_cache_bypass $skip_cache;
        fastcgi_no_cache $skip_cache;
        fastcgi_cache {{ $site->name }};
        fastcgi_cache_valid {{ $site->fullPageCachingDuration() }};
        add_header X-Cache $upstream_cache_status;
        add_header X-Frame-Options "{{$site->getNginxXFrameOption()}}";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
@endif
        fastcgi_index index.php;
        include fastcgi_params;
@if($site->isWordPress())
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
@else
        fastcgi_param   SCRIPT_FILENAME     $request_filename;
@endif
    }

@unless($site->getMeta('enable_custom_robots_txt', false))
    # Rewrite robots.txt
    rewrite ^/robots.txt$ /index.php last;
@endunless

@if($site->has7gFirewallEnabled())
    include /etc/nginx/xcloud-conf/7g.conf;
@endif
}

@includeWhen($site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsSSL', ['site' => $site])
@includeWhen(!$site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsNonSSL', ['site' => $site])
@include('scripts.wordpress.nginxRedirectDomains', ['site' => $site])

# XCLOUD CONFIG AFTER
include xcloud-conf/{{ $site->name }}/after/*;
