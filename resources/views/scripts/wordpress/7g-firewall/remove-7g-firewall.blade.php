#
# REQUIRES:
#   - site (The Site Instance)
#

# search for include /etc/nginx/xcloud-conf/7g.conf; in the /etc/nginx/sites-available/{{ $site->name }} file and remove that line
@if($site->server->stack->isNginx())
sed -i '/include \/etc\/nginx\/xcloud-conf\/7g.conf;/d' /etc/nginx/sites-available/{{ $site->name }}
@else
sed -i '/# START_XCLOUD_7G_FIREWALL/,/# END_XCLOUD_7G_FIREWALL/d' "{{ $site->site_path }}/.htaccess"
@endif
