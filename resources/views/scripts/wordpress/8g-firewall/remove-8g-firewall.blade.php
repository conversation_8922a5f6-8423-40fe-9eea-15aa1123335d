#
# REQUIRES:
#   - site (The Site Instance)
#

# Remove 8G Firewall configuration
@if($site->server->stack->isNginx())
sed -i '/include \/etc\/nginx\/xcloud-conf\/8g.conf;/d' /etc/nginx/sites-available/{{ $site->name }}

# Reload Nginx to apply the changes
nginx -t && systemctl reload nginx
@else
sed -i '/# START_XCLOUD_8G_FIREWALL/,/# END_XCLOUD_8G_FIREWALL/d' "{{ $site->site_path }}/.htaccess"
@endif
