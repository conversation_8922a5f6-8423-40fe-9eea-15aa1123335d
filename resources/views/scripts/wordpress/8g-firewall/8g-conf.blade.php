cat > /etc/nginx/xcloud-conf/8g.conf << 'EOF'
# 8G FIREWALL - NGINX v1.3
# @ https://perishablepress.com/8g-firewall/

set $8g_reason "";
set $8g_drop_bad_bot 0;
set $8g_drop_bad_referer 0;
set $8g_drop_bad_query_string 0;
set $8g_drop_bad_request 0;
set $8g_drop_bad_method 0;
set $8g_drop_bad_cookie 0;
set $8g_drop_bad_host 0;
set $8g_drop 0;

if ($bad_bot_8g) {

set $8g_reason "${8g_reason}:bad_bot_${bad_bot_8g}:";
set $8g_drop_bad_bot 1;

}

if ($bad_referer_8g) {

set $8g_reason "${8g_reason}:bad_referer_${bad_referer_8g}:";
set $8g_drop_bad_referer 1;

}

if ($bad_querystring_8g) {

set $8g_reason "${8g_reason}:bad_querystring_${bad_querystring_8g}:";
set $8g_drop_bad_query_string 1;

}

if ($bad_request_8g) {

set $8g_reason "${8g_reason}:bad_request_${bad_request_8g}:";
set $8g_drop_bad_request 1;

}

if ($not_allowed_method_8g) {

set $8g_reason "${8g_reason}:bad_method_${not_allowed_method_8g}:";
set $8g_drop_bad_method 1;

}

if ($bad_cookie_8g) {

set $8g_reason "${8g_reason}:bad_cookie_${bad_cookie_8g}:";
set $8g_drop_bad_cookie 1;

}

if ($bad_host_8g) {

set $8g_reason "${8g_reason}:bad_host_${bad_host_8g}:";
set $8g_drop_bad_host 1;

}

if ($8g_drop_bad_bot) {
set $8g_drop 1;
}

if ($8g_drop_bad_referer) {
set $8g_drop 1;
}

if ($8g_drop_bad_query_string) {
set $8g_drop 1;
}

if ($8g_drop_bad_request) {
set $8g_drop 1;
}

if ($8g_drop_bad_method) {
set $8g_drop 1;
}

if ($8g_drop_bad_cookie) {
set $8g_drop 1;
}

if ($8g_drop_bad_host) {
set $8g_drop 1;
}

if ($8g_drop) {
return 403;
}
EOF
