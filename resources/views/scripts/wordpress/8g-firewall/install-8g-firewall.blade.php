#
# REQUIRES:
#   - site (The Site Instance)
#

@if($site->server->stack->isNginx())
# Create the 8G firewall configuration files
@include('scripts.wordpress.8g-firewall.8g-firewall')
@include('scripts.wordpress.8g-firewall.8g-conf')

# Add the include directive to the site's Nginx configuration
if ! grep -q "include /etc/nginx/xcloud-conf/8g.conf;" /etc/nginx/sites-available/{{ $site->name }}; then
    # Find the line with "server_tokens off;" and add the include directive after it
    sed -i '/server_tokens off;/a \    include /etc/nginx/xcloud-conf/8g.conf;' /etc/nginx/sites-available/{{ $site->name }}
fi

# Reload Nginx to apply the changes
nginx -t && systemctl reload nginx
@else
touch {{ $site->site_path }}/.htaccess
cp {{ $site->site_path }}/.htaccess {{ $site->site_path }}/.htaccess.8g.bak

sed -i '/# START_XCLOUD_8G_FIREWALL/,/# END_XCLOUD_8G_FIREWALL/d' "{{ $site->site_path }}/.htaccess"

cat >> {{ $site->site_path }}/.htaccess << 'EOF'

# START_XCLOUD_8G_FIREWALL
@include('scripts.wordpress.7g-firewall.8g-firewall-ols')
# END_XCLOUD_8G_FIREWALL
EOF

chown {{ $site->site_user }}:{{ $site->site_user }} {{ $site->site_path }}/.htaccess
@endif
