server {
# Ports to listen on
@if($site->hasSuccessfulSslCertificate())
    @include('scripts.nginx.listenSsl', ['site' => $site])
@else
    listen 80;
    listen [::]:80;
@endif

# Server name to listen for
server_name {{ $site->getDomainNames()->implode(' ') }};

# Path to document root
root {{ $site->manager()->siteDocumentRoot() }};

@if($site->hasSuccessfulSslCertificate())
    # Paths to certificate files.
    ssl_certificate {{ $site->sslCertificate->getCertificatePath() }};
    ssl_certificate_key {{ $site->sslCertificate->getPrivateKeyPath() }};
@endif


location = /favicon.ico { access_log off; log_not_found off; }
location = /robots.txt  { access_log off; log_not_found off; }


# Overrides logs defined in nginx.conf, allows per site logs.
access_log /var/log/nginx/{{ $site->name }}-access.log;
error_log /var/log/nginx/{{ $site->name }}-error.log;

location / {
    return 503;
}

error_page 503 /errors/503_{{$site->id}}_error.html;

location /errors {
alias /var/www/html/errors;
}



# Rewrite robots.txt
rewrite ^/robots.txt$ /index.php last;


}

@includeWhen($site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsSSL', ['site' => $site])
@includeWhen(!$site->hasSuccessfulSslCertificate(), 'scripts.wordpress.nginxRedirectionsNonSSL', ['site' => $site])
@include('scripts.wordpress.nginxRedirectDomains', ['site' => $site])

