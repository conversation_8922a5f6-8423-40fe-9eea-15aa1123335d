@if($siteMigration->isDatabaseZip())
    #Download the database zip file from the remote server
    wget -q -O /home/<USER>/.xcloud/migration-uploads/{{$site->name}}/db/{{$site->name}}_db.zip {{$siteMigration->getMeta('migration_database_url')}}
    #if file is zip unzip it or if it is sql file just copy it
    unzip -q /home/<USER>/.xcloud/migration-uploads/{{$site->name}}/db/{{$site->name}}_db.zip -d /home/<USER>/.xcloud/migration-uploads/{{$site->name}}/db
@else
    #Download the database sql file from the remote server
    wget -q -O /home/<USER>/.xcloud/migration-uploads/{{$site->name}}/db/{{$site->name}}_db.sql {{$siteMigration->getMeta('migration_database_url')}}
@endif

cd /home/<USER>/.xcloud/migration-uploads/{{$site->name}}/db

# Search for the .sql file in the current directory and subdirectories
file=$(find . -name "*.sql" -type f -not -path '*/\.*')

echo $file

# Check if the file was found
if [ -z "$file" ]; then
    echo "SQL file not found in current directory or subdirectories"
else
    #get the db prefix from the sql file
    prefix=$(sed -n "s/^CREATE TABLE \`\([^_]*\).*/\1/p" "$file" | head -n 1)
    echo $prefix

    # Import the file into the database
    mysql -f -u {{ $site->database_user }} -p{{ $site->database_password }} {{ $site->database_name }} < "$file"
    echo "SQL file imported into the database"

    mysql -f -u {{ $site->database_user }} -p{{ $site->database_password }} -e "USE  {{ $site->database_name }};UPDATE wp_options SET option_value='{{$site->site_url}}' WHERE option_name='siteurl'; UPDATE wp_options SET option_value='{{$site->site_url}}' WHERE option_name='home';"
fi
@if($site->isWordpress())
    #then create the wp-config.php file
sudo -i -u {{$site->site_user}} bash << EOF

cd /var/www/{{ $site->name }}

{{ $site->wp_cli }} config create \
    --dbname={{ $site->database_name }} \
    --dbuser={{ $site->database_user }} \
    --dbprefix="$prefix"_ \
    --dbpass={{ $site->database_password }} \
    --dbhost={{ $site->database_host ?: 'localhost' }}{{ $site->database_port ? ":{$site->database_port}" : '' }}

EOF
@endif

#finally remove the db folder
rm -rf /home/<USER>/.xcloud/migration-uploads/{{$site->name}}/db

# search and replace
@includeWhen($site->isWordpress(),'scripts.site.searchReplaceUrl', [
    'site' => $site,
    'newDomain' => $site->siteMigration->domain_name,
    'oldDomain' => get_domain($site->siteMigration->existing_site_url),
    'new_url' => $site->site_url
])
