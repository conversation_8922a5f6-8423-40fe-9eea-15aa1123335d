docRoot                   $VH_ROOT
vhDomain                  {{ $site->name }} {{ ($site->isMultiSite() && $site->isMultiSiteSubdomain()) ? ',*.' . $site->name : null }}
@php
    $aliases = $site->getDomainNames()->reject(fn($value) => $value == $site->name);
@endphp
@if($site->ipAddresses->isNotEmpty())
rewrite  {
    enable                1
@foreach($site->ipAddresses->where('type',\App\Enums\IPAddressType::WHITELIST) as $whiteIp)
allow {{ $whiteIp->ip_address }}
@endforeach
@if($site->ipAddresses->where('type',\App\Enums\IPAddressType::BLACKLIST)->isNotEmpty())
rewriteCond %{HTTP:X-Forwarded-For} ^({{str($site->ipAddresses->where('type',\App\Enums\IPAddressType::BLACKLIST)->pluck('ip_address')->join('|'))->replace('.','\.')}})$
rewriteRule .* - [F,L]
# Handle 404 errors due to missing rewrite rules
rewriteCond %{REQUEST_FILENAME} !-f
rewriteCond %{REQUEST_FILENAME} !-d
rewriteRule . /index.php [L]
@endif
}
@endif
@if($aliases->count() > 0)
vhAliases                 {{ $aliases->implode(',') }}
@endif

scripthandler  {
    add                  lsapi:lsphp{{ $site->ls_php_version }}-{{$site->site_user}} php
}

index  {
    useServer               0
    indexFiles index.php,index.html
}

{{--this also doesnt work--}}
@if($site->hasBasicAuthEnabled())
realm ProtectedSite {
  userDB  {
    location              $SERVER_ROOT/conf/vhosts/$VH_NAME/htpasswd
  }
  groupDB  {
    location              $SERVER_ROOT/conf/vhosts/$VH_NAME/htgroup
  }
}
@endif

context / {
  allowBrowse             1
@if($site->hasBasicAuthEnabled())
  realm                   ProtectedSite
  authName                Restricted
@endif
  rewrite  {
    enable                  1
@foreach($site->redirections as $redirection)
    # xCloud Redirection #{{ $redirection->id }}
    rewriteRule {{ $redirection->from }} {{ $redirection->to }} {!! $redirection->ols_redirect_type !!}
@endforeach

    # Deny access to any files with a .ini, .log, .conf extension
    rewriteRule ^(.*)\.(ini|log|conf)$ - [F,L]
  }
  addDefaultCharset       off

  phpIniOverride  {

  }

extraHeaders {!! '<<<' !!}END_extraHeaders
    set X-Frame-Options "{{$site->getNginxXFrameOption()}}";
    set X-XSS-Protection "1; mode=block";
    set X-Content-Type-Options "nosniff";
END_extraHeaders
}


@if($site->isWordpress() && !$site->enablePhpExecutionOnUploadDirectory())
context /wp-content/uploads {
  type                    static
  location                $VH_ROOT/wp-content/uploads
  allowBrowse             1
  indexFiles              index.html

  # Optional - Deny access to PHP files
  addDefaultCharset       off
  rewrite  {
    enable                1
    rewriteCond %{REQUEST_FILENAME} -f
    rewriteRule ^(.*)\.php$ - [F,L]
  }
}
@endif

@if($site->isWordpress() && !$site->enableXmlRpc())
context /xmlrpc.php {
  type                    static
  location                $VH_ROOT/wp-content/uploads
  allowBrowse             1
  indexFiles              index.html

  # Optional - Deny access to PHP files
  addDefaultCharset       off
  rewrite  {
    enable                1
    rewriteCond %{REQUEST_FILENAME} -f
    rewriteRule ^(.*)\.php$ - [F,L]
  }
}
@endif

@if($site->hasSuccessfulSslCertificate())
vhssl  {
    keyFile                 {{ $site->sslCertificate->getPrivateKeyPath() }}
    certFile                {{ $site->sslCertificate->getCertificatePath() }}
    certChain               1
}
@endif

rewrite  {
    enable                  1
    autoLoadHtaccess        1

@if($site->hasSuccessfulSslCertificate())
    # Redirect HTTP to HTTPS
    rewriteCond %{HTTPS} !on
    rewriteCond %{HTTP:X-Forwarded-Proto} !https
    rewriteRule ^(.*)$ https://%{SERVER_NAME}%{REQUEST_URI} [R=301,L]
@endif

@if(!$site->enableXmlRpc())
    # Remove access to xmlrpc.php
    rewriteRule ^/xmlrpc\.php$ - [F,L]
@endif

@if($site->isOneClickApp())
    rewriteRule ^(.*)?\.(env.*|git.*|htaccess.*|json|lock|ini|log|conf|sql|sql.gz|twig)$ - [F,L]
@endif

    # Deny access to any files with a .ini, .log, .conf extension
    rewriteRule ^(.*)\.(ini|log|conf)$ - [F,L]

    include /usr/local/lsws/conf/vhosts/{{ $site->name }}/vhconf.rewrite.conf
}

errorlog /var/log/lsws/{{ $site->name }}-error.log {
  useServer               0
  logLevel                ERROR
  rollingSize             10M
}

accesslog /var/log/lsws/{{ $site->name }}-access.log {
    useServer               0
    logFormat               %a %l %u %t "%r" %>s %O "%{Referer}i" "%{User-Agent}i"
    logHeaders              5
    rollingSize             10M
    keepDays                30
    compressArchive         1
}

module cache {
    ls_enabled {{ $site->hasFullPageCaching() ? '1' : '0' }}
}

include /usr/local/lsws/conf/vhosts/{{ $site->name }}/vhconf.server.conf
