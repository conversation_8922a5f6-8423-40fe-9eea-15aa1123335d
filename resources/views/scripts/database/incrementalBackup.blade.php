#check /etc/mysql/my.cnf has log_bin = mysql-bin
if ! grep -q "log_bin = mysql-bin" /etc/mysql/my.cnf; then
echo "log_bin = mysql-bin" >> /etc/mysql/my.cnf
sudo service mysql restart
fi
serverTime=$(date +%d-%m-%Y_%H-%M-%S)
datetime=$(date +"%Y%m%d%H%M%S")
dumpName=${serverTime}-inc.zip

#path to directory with binary log files
binlogs_path=/var/lib/mysql/

#start writing to new binary log file
sudo mysql -uroot -p{{ $server->database_password }} -E --execute='USE {{ $site->database_name }}; FLUSH BINARY LOGS;' mysql


#get list of binary log files
binlogs=$(sudo mysql -uroot -p{{ $server->database_password }} -E --execute='USE {{ $site->database_name }}; SHOW BINARY LOGS;' mysql | grep Log_name | sed -e 's/Log_name://g' -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')


#get list of binary log for backup (all but the last one)
binlogs_without_Last=`echo "${binlogs}" | head -n -1`

#get the last active binary log file (which you do not have to copy)
binlog_Last=`echo "${binlogs}" | tail -n -1`

#form full path to binary log files
binlogs_fullPath=`echo "${binlogs_without_Last}" | xargs -I % echo $binlogs_path%`

#compress binary logs into archive
zip {{$site->incrementalPath()}}/$dumpName $binlogs_fullPath

#delete saved binary log files
echo $binlog_Last | xargs -I % sudo mysql -uroot -p{{ $server->database_password }} -E --execute='USE {{ $site->database_name }}; PURGE BINARY LOGS TO "%";' mysql

fileSize=$(du -h {{$site->incrementalPath()}}/$dumpName | awk '{print $1}')

@if($isRemoteBackup)
    /usr/local/bin/aws s3 cp {{$site->incrementalPath()}}/$dumpName s3://{{ $storageProvider->getBucket() }}/{{$site->backupDirName()}}/$dumpName --metadata '{"SiteID": "{{$site->id}}","ServerID": "{{$site->server_id}}"}' --endpoint-url {{ $storageProvider->getEndPoint() }} 2>/dev/null
    POST_DATA="{ \"backup_settings_id\": \"{{$backupSetting->id}}\", \"storage_provider_id\": \"{{$backupSetting->storage_provider_id}}\", \"path\": \"s3://{{ $storageProvider->getBucket() }}/{{$site->backupDirName()}}\", \"type\": \"{{\App\Models\BackupFile::INCREMENTAL_BACKUP}}\" , \"fileName\": \"$dumpName\" ,\"serverTime\": \"$datetime\" , \"fileSize\": \"$fileSize\", \"is_remote\": \"true\" }"
@else
    POST_DATA="{ \"backup_settings_id\": \"{{$backupSetting->id}}\", \"path\": \"{{$site->incrementalPath()}}\", \"type\": \"{{\App\Models\BackupFile::INCREMENTAL_BACKUP}}\" , \"fileName\": \"$dumpName\" ,\"serverTime\": \"$datetime\" , \"fileSize\": \"$fileSize\", \"is_remote\": \"false\" }"
@endif


url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/database-backup') }}"

curl -s -X POST -H 'Content-type: application/json' -d "$POST_DATA" --insecure "$url" >/dev/null
#end of incremental backup
