if [ -z "$datetime" ]; then
datetime=$(date +"%Y%m%d%H%M%S")
fi
DATABASE_SIZE=$(mysql -u {{$site->database_user}} -p{{ $site->database_password }} -e "SELECT table_schema AS DatabaseName, ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS SizeMB FROM information_schema.tables WHERE table_schema = '{{ $site->database_name }}' GROUP BY table_schema;" 2>/dev/null | awk '/{{ $site->database_name }}/ {print $2}')
dumpName={{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql

mysqldump --single-transaction --skip-lock-tables --quick -u root -p{{ $server->database_password }} {{ $site->database_name }} > {{$site->incrementalPath()}}/$dumpName 2>/dev/null

fileSize=$(stat -c %s {{$site->incrementalPath()}}/$dumpName | awk '{print $1/1024}')
@if($isRemoteBackup)
    /usr/local/bin/aws s3 cp {{$site->incrementalPath()}}/$dumpName s3://{{ $storageProvider->bucket }}/{{$site->backupDirName()}}/$dumpName --metadata '{"SiteID": "{{$site->id}}","ServerID": "{{$site->server_id}}"}' --profile {{ $site->backupDirName() }} --endpoint-url {{ $storageProvider->getEndPoint() }} >/dev/null
    POST_DATA='{ "backup_settings_id": "{{$backupSetting->id}}", "type": "{{$type}}", "is_remote": "1", "server_datetime": "'"$datetime"'", "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "path": "s3://{{ $storageProvider->bucket }}/{{$site->backupDirName()}}/'$dumpName'", "fileSize": "'"$fileSize"'", "fileName": "'"$dumpName"'"}'
    rm -rf {{$site->incrementalPath()}}/$dumpName
@else
    POST_DATA='{ "backup_settings_id": "{{$backupSetting->id}}", "type": "{{$type}}", "is_remote": "0", "server_datetime": "'"$datetime"'", "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "path": "{{$site->incrementalPath()}}", "fileSize": "'"$fileSize"'", "fileName": "'"$dumpName"'"}'
@endif
url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/database-backup') }}"
curl -s -X POST -H 'Content-type: application/json' -d "$POST_DATA" --insecure "$url" >/dev/null
