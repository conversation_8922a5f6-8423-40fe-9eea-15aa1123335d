#
# REQUIRES:
#       - server (the server instance)
#       - database (the database instance)
#       - database user (the database user(optional))
#       - database user password (the database user password(optional))
#

set -e

# Create The MySQL Database
mysql --user="root" --password='{{ $server->database_password }}' -e "CREATE DATABASE IF NOT EXISTS \`{{ $databaseName }}\` CHARACTER SET utf8 COLLATE utf8_unicode_ci;"

@if($databaseUser)
    # Add MySQL User
    mysql --user="root" --password='{{ $server->database_password }}' -e "CREATE USER IF NOT EXISTS '{{ $databaseUser }}'@'{{ $server->public_ip }}' IDENTIFIED BY '{{ $databaseUserPassword }}';"
    mysql --user="root" --password='{{ $server->database_password }}' -e "CREATE USER IF NOT EXISTS '{{ $databaseUser }}'@'localhost' IDENTIFIED BY '{{ $databaseUserPassword }}';"
    mysql --user="root" --password='{{ $server->database_password }}' -e "CREATE USER IF NOT EXISTS '{{ $databaseUser }}'@'%' IDENTIFIED BY '{{ $databaseUserPassword }}';"

    mysql --user="root" --password='{{ $server->database_password }}' -e "GRANT ALL ON {{ $databaseName }}.* TO '{{ $databaseUser }}'@'{{ $server->public_ip }}' WITH GRANT OPTION;"
    mysql --user="root" --password='{{ $server->database_password }}' -e "GRANT ALL ON {{ $databaseName }}.* TO '{{ $databaseUser }}'@'localhost' WITH GRANT OPTION;"
    mysql --user="root" --password='{{ $server->database_password }}' -e "GRANT ALL ON {{ $databaseName }}.* TO '{{ $databaseUser }}'@'%' WITH GRANT OPTION;"

    mysql --user="root" --password='{{ $server->database_password }}' -e "FLUSH PRIVILEGES;"
@endif
