<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Invoice</title>
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@500&display=swap" rel="stylesheet">

    <style>
        .clearfix:after {
            content: "";
            display: table;
            clear: both;
        }

        a {
            color: #167AFF;
            text-decoration: none;
        }

        .container {
            padding-left: 12px;
            padding-right: 12px;
        }

        body {
            position: relative;
            width: 21cm;
            height: 29.7cm;
            margin: 0 auto;
            color: #2B2748;
            background: #FFFFFF;
            font-size: 14px;
            font-family: 'DM Sans';
        }

        .payment-status {
            position: absolute;
            top: -45px;
            right: 45px;
        }

        header {
            width: 88%;
            margin-bottom: 4px;
        }

        #logo {
            position: absolute;
        }

        #companyLogo {
            height: 46px;
            width: auto;
        }

        #invoice-intro {
            margin-top: 44px;
            font-size: 15px;
            font-weight: 400;
            letter-spacing: 1px;
            line-height: 1.2;
            color: #505060;
            margin-bottom: 8px;
        }

        #invoice {
            width: 96%;
            border: 1px solid #DFE4EA;
            border-radius: 4px;
            background: #F7FBFF;
            box-sizing: border-box;
            padding: 10px 2% 4px;
            margin-bottom: 8px;
        }

        #invoice-id {
            float: left;
            display: inline-block;
            margin: 0;
        }

        #invoice-date {
            float: right;
            display: inline-block;
            margin: 0;
        }

        #invoice .label {
            display: inline-block;
            font-size: 13px;
            font-weight: 400;
            line-height: 1.2;
            color: #717894;
            margin: 0 4px 0 0 ;
        }

        #invoice .data {
            display: inline-block;
            font-size: 13px;
            font-weight: 500!important;
            line-height: 1.2;
            margin: 0;
            color: #2B2748;
        }

        #client {
            float: left;
            width: 45%;
        }

        #client .to {
            color: #717894;
            font-size: 11px;
            font-weight: 500;
            line-height: 1;
            margin-bottom: 2px;
        }

        #client .name {
            font-size: 16px;
            font-weight: 500;
            line-height: 1;
            color: #2B2748;
            margin: 0;
        }

        #client .email {
            font-size: 14px;
            line-height: 1;
            margin: 0 0 2px 0;
            font-weight: 400;
        }

        #client .custom-information {
            display: inline-block;
            margin: 0 0 2px 0;
        }

        #client .custom-information .label {
            display: inline;
            font-size: 14px;
            font-weight: 400;
            line-height: 1;
            color: #717894;
            margin: 0;
        }

        #client .custom-information .data {
            display: inline;
            font-size: 14px;
            font-weight: 400;
            line-height: 1;
            margin: 0;
            color: #505060;
        }

        #company {
            float: right;
            width: 45%;
            text-align: right;
        }

        #company .to {
            color: #717894;
            font-size: 11px;
            line-height: 1;
            font-weight: 500;
            margin-bottom: 2px;
        }

        #company .name {
            font-size: 16px;
            font-weight: 500;
            line-height: 1;
            color: #2B2748;
            margin: 0;
        }

        #company .email {
            font-size: 14px;
            margin: 0 0 2px 0;
            font-weight: 400;
            line-height: 1;
        }

        #company .custom-information {
            display: inline-block;
            margin: 0 0 2px 0;
        }

        #company .custom-information .label {
            display: inline;
            font-size: 14px;
            line-height: 1;
            color: #717894;
            margin: 0;
            font-weight: 400;
        }

        #company .custom-information .data {
            display: inline;
            font-size: 14px;
            line-height: 1;
            margin: 0;
            color: #505060;
            font-weight: 400;
        }

        #main-content {
            width: 88%;
            margin-bottom: 10px;
        }

        #invoice-summary-wrapper {
            width: 100%;
        }

        #invoice-summary-title {
            display: block;
            font-size: 18px;
            font-weight: 500;
            line-height: 1;
            border-bottom: 1px solid #363D46;
            padding-left: 12px;
            padding-right: 12px;
            padding-bottom: 8px;
            color: #2B2748;
        }

        #invoice-summary {
            width: 100%;
            margin-top: 4px;
        }

        #invoice-summary tbody {
            width: 100%;
            border-bottom: 1px solid #363D46;
            margin-top: 8px;
        }

        #invoice-summary tbody td {
            padding: 2px 12px;
            font-size: 15px;
            color: #505060;
            font-weight: 500;
            line-height: 1;
        }

        #invoice-summary tfoot td {
            padding: 6px 6px 0;
            font-size: 16px;
            color: #147AFF;
            font-weight: 600;
            line-height: 1;
        }

        #invoice-breakdown-wrapper {
            margin-bottom: 0;
            margin-top: 8px;
        }

        #invoice-breakdown-title {
            display: block;
            font-size: 18px;
            font-weight: 500;
            line-height: 1;
            font-family: 'Inter';
            color: #2B2748;
        }

        #invoice-breakdown-text {
            display: block;
            color: #717894;
            font-size: 12px;
            font-weight: 400;
            line-height: 1;
            margin: 6px 0;
        }

        .invoice-details {
            width: 100%;
            margin-bottom: 12px;
        }

        .invoice-details .invoice-details-heading {
            border-bottom: 1px solid #363D46;
            width: 100%;
        }

        .invoice-details .invoice-details-heading td {
            padding: 4px 12px 10px 12px;
            font-size: 14px;
            color: #2B2748;
            font-weight: 600;
            line-height: 1;
        }

        .invoice-details .invoice-details-data {
            border-bottom: 1px solid #DFE4EA;
            width: 100%;
            color: #505060;
        }

        .invoice-details .invoice-details-data.borderless {
            border-bottom: none;
        }

        .invoice-details .invoice-details-data td {
            color: #717894;
            padding: 6px 12px 6px 12px;
            font-weight: 500;
            line-height: 1;
        }

        .invoice-details .invoice-details-data td .invoice-details-data-inner {
            display: block;
            margin: 0;
            padding: 0;
            font-size: 14px;
            font-weight: 500;
        }

        .invoice-details .invoice-details-data td .title {
            color: #505060;
            display: inline;
        }

        .invoice-details .invoice-details-data td .type {
            color: #717894;
            display: inline;
        }

        .invoice-details .invoice-details-data td .label {
            color: #717894;
            display: inline;
            font-weight: 400;
        }

        .invoice-details .invoice-details-data td .ip {
            color: #505060;
            display: inline;
        }

        footer {
            width: 88%;
            margin-top: 10px;
        }

        #footer-inner {
            width: 100%;
        }

        #payment-info {
            float: left;
        }

        #payment-info .payment-info-title {
            color: #717894;
            font-size: 11px;
            font-weight: 500;
            line-height: 1;
            margin-bottom: 5px;
            display: inline-block;
        }

        #payment-info .payment-info-data {
            font-size: 14px;
            margin-bottom: 3px;
            line-height: 1;
        }

        #payment-info .payment-info-data .label {
            color: #717894;
            font-weight: 400;
        }

        #payment-info .payment-info-data .data {
            color: #505060;
            font-weight: 500;
        }

        #support-wrapper {
            float: right;
            text-align: right;
        }

        #support-wrapper .notice {
            font-size: 14px;
            font-weight: 400;
            color: #2B2748;
            margin-bottom: 3px;
            margin-top: 0;
            line-height: 1;
        }

        #support-wrapper .notice a {
            font-weight: 500;
        }

        .thank_you {
            font-family: 'Caveat';
            font-size: 40px;
            font-weight: 600;
            margin-top: -20px;
            margin-bottom: 0;
            color: #505060;
        }
    </style>
</head>
<body>
<header class="clearfix">
    @if($invoice->status->getSealImagePath())
        <img class="payment-status" height="135" width="170" src="data:image/png+xml;base64,{{ base64_encode(File::get($invoice->status->getSealImagePath())) }}" alt="{{ $invoice->status->value }}" />
    @else
        <img class="payment-status" height="135" width="170" src="data:image/png+xml;base64,{{ base64_encode(file_get_contents(resource_path('images/pending.png'))) }}" alt="payment-status-pending" />
    @endif
    <!-- <img class="payment-status" height="135" width="170" src="data:image/png+xml;base64,{{ base64_encode(file_get_contents(resource_path('images/failed.png'))) }}" alt="payment-status-failed" />
        <img class="payment-status" height="135" width="170" src="data:image/png+xml;base64,{{ base64_encode(file_get_contents(resource_path('images/pending.png'))) }}" alt="payment-status-pending" /> -->
    <div class="container">
        <div id="logo-wrapper">
            <div id="logo">
                <img alt="xCloud" id="companyLogo" src="data:image/png+xml;base64,{{ base64_encode(file_get_contents(resource_path('images/xCloud-logo.png'))) }}">
            </div>
            <p id="invoice-intro">Final invoice for your recent purchase</p>
        </div>
    </div>
    @if($invoice->created_at?->format('d/m/Y'))
        <div id="invoice" class="clearfix">
            <div id="invoice-id"><label class='label'>INVOICE ID: </label><p class="data">{{ $invoice->number }}</p></div>
            <div id="invoice-date"><label class='label'>INVOICE DATE:</label><p class="data">{{ $invoice->created_at?->format('d/m/Y') }}</p></div>
        </div>
    @endif
    <div class="container">
        <div class="clearfix">
            <div id="client">
                <div class="to">INVOICE TO:</div>
                <h2 class="name">{{ $invoice->team->name }}</h2>
                <div class="email">
                    <a href="mailto:{{ $invoice->team->email }}">
                        {{ $invoice->team->email }}
                    </a>
                </div>
                @if(isset($userMeta['customer_name']) && !blank($userMeta['customer_name']))
                    <div class="custom-information">
                        <label class="label">Customer Name:</label>
                        <p class="data">{{ $userMeta['customer_name'] }}</p>
                    </div>
                @endif
                @if(isset($userMeta['address']) && !blank($userMeta['address']))
                    <div class="custom-information">
                        <label class="label">Billing Information:</label>
                        <p class="data">{!! $userMeta['address'] !!}</p>
                    </div>
                @endif
            </div>
            <div id="company">
                <div class="to">INVOICE FROM:</div>
                <h2 class="name">xCloud Hosting LLC</h2>
                <div class="email">
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="custom-information">
                    <p class="data">124 Broadkill Road, Suit 599, Milton, Delaware, 19968</p>
                </div>
            </div>
        </div>
    </div>
</header>
<main id="main-content" class="clearfix">
    <div id="invoice-summary-wrapper">
        <label id="invoice-summary-title">Invoice Summary</label>
        <table id="invoice-summary">
            <tbody>
            <tr>
                <td class="" style="text-align: left">
                    Sub Total
                </td>
                <td class="" style="text-align: right">
                    ${{ $billableAmount }}
                </td>
            </tr>
            @if(!blank($invoice->discount))
                <tr>
                    <td class="" style="text-align: left">
                        Coupon Discount
                    </td>
                    <td class="" style="text-align: right">
                        (-) ${{ $invoice->discount }}
                    </td>
                </tr>
            @endif
            </tbody>
            <tfoot>
            <tr>
                <td></td>
                <td>
                    <table style="width: 100%;">
                        <tfoot>
                        <tr>
                            <td class="" style="text-align: left">
                                Total Paid:
                            </td>
                            <td class="" style="text-align: right">
                                ${{ !blank($invoice->discount) ? $invoice->amount_received : $invoice->amount }}
                            </td>
                        </tr>
                        </tfoot>
                    </table>
                </td>
            </tr>
            </tfoot>
        </table>
    </div>
</main>
<footer>
    <div>
        <div class="container">
            <div id="footer-inner" class="clearfix">
                <div id="payment-info">
                    <span class="payment-info-title">PAYMENT INFO:</span>
                    <div class="payment-info-data">
                        <label class="label">Payment Method:</label>
                        <span class="data" style="text-transform: capitalize;">{{ $paymentMethod?->payment_gateway }}</span>
                    </div>
                    <div class="payment-info-data">
                        <label class="label">Card Holder Name:</label>
                        <span class="data">{{ team()?->name }}</span>
                    </div>
                    <div class="payment-info-data">
                        <label class="label">Card Number:</label>
                        <span class="data">{{ $paymentMethod?->card_no }}</span>
                    </div>
                </div>
                <div id="support-wrapper">
                    <p class="thank_you">Thank You!</p>
                    <!--<img class="appreciation" height="35px" width="150" src="data:image/png+xml;base64,{{ base64_encode(file_get_contents(resource_path('images/Thank You!.png'))) }}" alt="appreciation" />-->
                    <p class="notice">Questions about your Invoice?</p>
                    <p class="notice">Please Contact with us at
                        <a href="mailto:<EMAIL>">
                            <EMAIL>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</footer>
</body>
</html>
