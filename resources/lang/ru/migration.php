<?php

use App\Enums\SiteStatus;
use App\Services\Migration\SiteMigrating;

return [
    'init' => [
        [
            'stage' => 'Проверка и Подтверждение',
            'tasks' => [
                SiteMigrating::CHECKING_STATUS => 'Проверка хранилища сервера и соединения',
            ]
        ],
        [
            'stage' => 'Установка Приложений',
            'tasks' => [
                SiteMigrating::INSTALLING_PHP => 'Установка PHP :php_version',
                SiteMigrating::INSTALLING_DATABASE => 'Установка Базы Данных',
                SiteMigrating::INSTALLING_SITE => 'Настройка нового веб-сайта',
                SiteMigrating::UPDATING_PERMISSIONS => 'Обновление разрешений',
            ]
        ],
    ],
    'no_db_init' => [
        [
            'stage' => 'Проверка и Подтверждение',
            'tasks' => [
                SiteMigrating::CHECKING_STATUS => 'Проверка хранилища сервера и соединения',
            ]
        ],
        [
            'stage' => 'Установка Приложений',
            'tasks' => [
                SiteMigrating::INSTALLING_PHP => 'Установка PHP :php_version',
                SiteMigrating::INSTALLING_SITE => 'Настройка нового веб-сайта',
                SiteMigrating::UPDATING_PERMISSIONS => 'Обновление разрешений',
            ]
        ],
    ],
    'manual_init' => [
        [
            'stage' => 'Проверка и Подтверждение',
            'tasks' => [
                SiteMigrating::CHECKING_STATUS => 'Проверка хранилища сервера и соединения',
            ]
        ],
        [
            'stage' => 'Установка Приложений',
            'tasks' => [
                SiteMigrating::INSTALLING_PHP => 'Установка PHP :php_version',
                SiteMigrating::INSTALLING_DATABASE => 'Установка Базы Данных',
                SiteMigrating::CREATE_SITE_DIRECTORY => 'Создание каталога сайта',
                SiteMigrating::MAKING_SITE_DIRECTORY_READY => 'Подготовка каталога сайта',
                SiteMigrating::MAKING_DATABASE_READY => 'Подготовка базы данных',
                SiteMigrating::FETCHING_SITE_URL => 'Получение URL сайта',
                SiteMigrating::UPDATING_PERMISSIONS => 'Обновление разрешений',
            ]
        ],
    ],
    'files' => [
        [
            'stage' => 'Миграция Файлов',
            'tasks' => [
                SiteMigrating::SCANING_FILESYSTEM => 'Сканирование файлов',
                SiteMigrating::FOUND_FILES => 'Найдено :found_file_count Файлов :found_file_size',
                SiteMigrating::MIGRATING_FILES => 'Миграция файлов :file_migration_percentage',
                // SiteMigrating::RE_VERIFYING_FILES => 'Повторная проверка файлов :re_verified_file_count',
                // SiteMigrating::RETRYING_FAILED_FILES => 'Повторная попытка неудачных файлов :file_retry_percentage',
                SiteMigrating::FILE_MIGRATION_FINISHED => 'Миграция файлов завершена',
            ]
        ]
    ],
    'db' => [
        [
            'stage' => 'Миграция Базы Данных',
            'tasks' => [
                SiteMigrating::SCANING_DB => 'Сканирование баз данных',
                SiteMigrating::FOUND_DB => 'Найдено :found_table_count Таблиц и :found_row_count Строк',
                SiteMigrating::MIGRATING_DB => 'Миграция БД :db_migration_percentage',
                SiteMigrating::DB_MIGRATION_FINISHED => 'Миграция базы данных завершена',
            ]
        ],
    ],
    'config' => [
        [
            'stage' => 'Настройка',
            'tasks' => [
                SiteMigrating::VERIFYING_DNS => 'Проверка DNS для вашего сайта',
                SiteMigrating::CONFIGURING_HTTPS => 'Настройка HTTPS',
                SiteMigrating::CONFIGURING_FULL_PAGE_CACHE => 'Настройка :cache',
                SiteMigrating::CONFIGURING_REDIS_CACHE => 'Настройка кэша объектов Redis',
                SiteMigrating::CONFIGURING_NGINX => 'Настройка :stack',
                SiteMigrating::INSTALLING_MONITORING => 'Установка Мониторинга',
                SiteMigrating::FINISHING_UP => 'Завершение',
            ]
        ]
    ],
    'no_db_config' => [
        [
            'stage' => 'Настройка',
            'tasks' => [
                SiteMigrating::VERIFYING_DNS => 'Проверка DNS для вашего сайта',
                SiteMigrating::CONFIGURING_HTTPS => 'Настройка HTTPS',
                SiteMigrating::CONFIGURING_NGINX => 'Настройка Nginx',
                SiteMigrating::INSTALLING_MONITORING => 'Установка Мониторинга',
                SiteMigrating::FINISHING_UP => 'Завершение',
            ]
        ]
    ]
];
