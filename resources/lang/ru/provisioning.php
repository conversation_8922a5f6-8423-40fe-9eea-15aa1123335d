<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'Проверка карты и обработка платежа',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'Проверка карты и обработка платежа на сумму $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'Создание сервера',
            'tasks' => [
                ServerProvisioning::INIT => 'ИнициализацияProvisioning сервера',
                ServerProvisioning::CREATING_SERVER => 'Создание сервера на :provider',
                ServerProvisioning::SERVER_CREATED => 'Сервер создан :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'Ожидание запуска сервера',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'Подключение к серверу',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'Подключение к SSH',
                ServerProvisioning::CONNECTED => 'Подключение установлено',
            ]
        ],
        [
            'stage' => 'Конфигурация сервера',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'Конфигурация swap-файла',
                ServerProvisioning::UPGRADING_SYSTEM => 'Обновление системы',
                ServerProvisioning::INSTALLING_BASE => 'Установка базовых зависимостей',
                ServerProvisioning::AUTHENTICATION_METHOD => 'Обновление метода аутентификации',
                ServerProvisioning::UPDATING_HOSTNAME => 'Обновление имени хоста',
                ServerProvisioning::UPDATING_TIMEZONE => 'Обновление часового пояса',
                ServerProvisioning::XCLOUD_USER => 'Настройка пользователей',

                ServerProvisioning::SETUP_SSH => 'Настройка SSH',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Настройка прав Sudo',
                ServerProvisioning::SETTING_UP_GIT => 'Настройка Git',
                ServerProvisioning::SETUP_FIREWALL => 'Настройка брандмауэра',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'Настройка скрипта очистки',
            ]
        ],
        [
            'stage' => 'Установка приложений',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'Установка PHP :php_version',
                ServerProvisioning::INSTALLING_WEBSERVER => 'Установка веб-сервера :stack',
                ServerProvisioning::INSTALLING_NODE => 'Установка Node',
                ServerProvisioning::INSTALLING_REDIS => 'Установка Redis',
                ServerProvisioning::INSTALLING_DATABASE => 'Установка базы данных :database_type',
                ServerProvisioning::INSTALLING_WP_CLI => 'Установка WP CLI',
            ]
        ],
        [
            'stage' => 'Завершение',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'Настройка прав SSH',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => 'Установка скрипта мониторинга',
                ServerProvisioning::READY_TO_DO_MAGIC => 'Готово к магии!',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => 'Проверка и верификация',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'Проверка хранилища сервера и подключения',
                SiteProvisioning::VERIFYING_DNS => 'Проверка DNS для вашего сайта',
            ]
        ],
        [
            'stage' => 'Установка приложений',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => 'Установка PHP :php_version',
                SiteProvisioning::INSTALLING_DATABASE => 'Установка базы данных',
                SiteProvisioning::INSTALLING_WORDPRESS => 'Настройка WordPress',
            ]
        ],
        [
            'stage' => 'Конфигурация',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'Настройка SSL',
                SiteProvisioning::CONFIGURING_HTTPS => 'Настройка HTTPS',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => 'Настройка кеша :cache',
                SiteProvisioning::CONFIGURING_NGINX => 'Настройка :stack',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Настройка кеша объектов Redis',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Установка Blueprint',
                SiteProvisioning::DEPLOY_SCRIPT => 'Скрипт развертывания',
                SiteProvisioning::INSTALL_MONITORING => 'Установка мониторинга',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'Установка WP Cron Job',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'Настройка управляемого почтового сервиса xCloud',
                SiteProvisioning::HANDLE_INDEXING => 'Обработка индексации сайта',
                SiteProvisioning::FINISHING_UP => 'Завершение',
            ]
        ]
    ],
];
