<?php

use App\Enums\SiteStatus;
use App\Services\Clone\SiteCloning;

return [
    'init' => [
        [
            'stage' => 'Memeriksa dan <PERSON>',
            'tasks' => [
                SiteCloning::CHECKING_STATUS => 'Memeriksa Penyimpanan & Koneksi Server',
            ]
        ],
        [
            'stage' => 'Menginstal Aplikasi',
            'tasks' => [
                SiteCloning::INSTALLING_PHP => 'Menginstal PHP: :php_version',
                SiteCloning::SYNCING_PHP_SETTINGS => 'Menyelaraskan Pengaturan PHP',
                SiteCloning::INSTALLING_DATABASE => 'Menginstal Basis Data',
            ]
        ],
    ],
    'config' => [
        [
            'stage' => 'Mengonfigurasi',
            'tasks' => [
                SiteCloning::CLONING_SITE => 'Mengkloning Situs',
                SiteCloning::CONFIGURING_SSL => 'Mengonfigurasi SSL untuk situs yang dikloning',
                SiteCloning::CONFIGURING_HTTPS => 'Mengonfigurasi HTTPS untuk situs yang dikloning',
                SiteCloning::CONFIGURING_FULL_PAGE_CACHE => 'Mengonfigurasi Full Page Cache untuk situs yang dikloning',
                SiteCloning::CONFIGURING_REDIS_CACHE => 'Mengonfigurasi Redis Object Cache untuk situs yang dikloning',
                SiteCloning::CONFIGURING_NGINX => 'Mengonfigurasi Nginx untuk situs yang dikloning',
                SiteCloning::INSTALLING_MONITORING => 'Menginstal Monitoring untuk situs yang dikloning',
                SiteCloning::FINISHING_UP => 'Menyelesaikan',
            ]
        ]
    ]
];
