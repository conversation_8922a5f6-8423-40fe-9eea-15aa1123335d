<?php

return [
    'billing_failed' => [
        'subject' => 'Billing Failed',
        'line' => 'Unfortunately, we could not process your payment for your xCloud subscription. Please check if your billing information is accurate, and update the information if necessary.',
        'action' => 'Update Billing Information'
    ],
    'billing_success' => [
        'subject' => 'Billing Successful',
        'line' => 'Thank you for being a valued user of xCloud! We have received your payment and have attached your latest invoice below.',
        'action' => 'Check Bills And Payment'
    ],
    'domain_update_failed' => [
        'subject' => 'Domain Update Failed',
        'line' => "This message is to notify you that your request for updating domain from :previousDomain to :newDomain has failed!.",
        'action' => 'Go to Site'
    ],
    'drop_action' => [
        'line' => 'This is to inform you that your :model :name has been :action as per your instructions.',
        'action' => 'Go to :model'
    ],
    'invoice' => [
        'subject' => ":app_name - New Invoice for :team",
        'line' => ':app_name - New Invoice for :team_name. We are pleased to inform you that the payment for your :purchase_title invoice for :team has been successfully processed.',
        'action' => 'Check Bills And Payment'
    ],
    'invoice_failed' => [
        'subject' => ':service_name - Invoice Payment Failed',
        'line' => ':service_name - Invoice Payment Failed. We regret to inform you that the payment for your invoice (:invoice_number) has failed.',
        'action' => 'Check Bills And Payment'
    ],
    'manual_invoice' => [
        'subject' => ":app_name - New Invoice for :team",
        'line' => ':app_name - New Invoice for :team_name. We are pleased to inform you that the payment for your :purchase_title invoice for :team has been successfully processed.',
        'action' => 'Check Bills And Payment'
    ],
    'manual_invoice_paid' => [
        'subject' => ":app_name - Your :current_month Invoice for :team has been paid",
        'line' => ":app_name - Your :current_month Invoice for :team_name has been paid. We are pleased to inform you that the payment for your :current_month invoice for :team has been successfully processed using :payment_method.",
        'action' => 'Check Bills And Payment'
    ],
    'monthly_invoice' => [
        'subject' => ":app_name - Your :current_month Invoice for :team is available",
        'line' => "Your :app_name invoice for :current_month is paid. We are pleased to inform you that the payment for your :current_month invoice for :team has been successfully processed. The balance was automatically charged to your card, so you don't need further action.",
        'action' => 'Check Bills And Payment'
    ],
    'monthly_invoice_payment_failed' => [
        'subject' => ":app_name - Payment Failed for Your :current_month Invoice",
        'line' => "We regret to inform you that the payment for your :current_month invoice for :team has failed.",
        'line_2' => "To avoid any disruption in services, please review the payment details and attempt the payment again at your earliest convenience.",
        'invoice_no' => "Invoice Number: :invoice_reference",
        'invoice_date' => "Issue Date: :issue_date",
        'due_date' => "Due Date: :due_date",
        'amount_due' => "Amount Due: $:amount_due",
        'action' => 'Retry Payment',
        'solution' => "If you believe this is an error or have any questions, please contact our support team.",
    ],
    'monthly_manual_invoice' => [
        'subject' => ":app_name - Your :current_month Invoice for :team is available",
        'line' => "Your :app_name invoice for :current_month is ready. Please note that payment is not processed automatically. Kindly proceed with manual payment at your earliest convenience. Thank you for choosing :app_name. We appreciate your business. If you have already made the payment for your :current_month invoice for :team, kindly disregard this message.",
        'action' => 'Check Bills And Payment'
    ],
    'invoice_refund' => [
        'subject' => ":app_name - you received refund for :team",
        'line' => ":app_name - you received refund for :team_name. We are pleased to inform you that the refund for your :purchase_title invoice for :team has been successfully processed. Please find the invoice in attachment. The balance was automatically charged to your card, so you don't need further action.",
        'action' => 'Check Bills And Payment'
    ],
    'server_auto_heal' => [
        'action' => 'Sign In To xCloud',
    ],
    'server_provisioning_success' => [
        'subject' => 'Server Setup Completed',
        'line' => 'Congratulations! Server Setup Is Successful. Your new server, :name has been set up successfully.',
        'action' => 'View Server in xCloud'
    ],
    'server_disconnected' => [
        'subject' => 'Unable to connect to server :serverName',
        'line' => "Unable to connect to server :serverName. This message is to notify you that we were unable to connect to your server :serverName. You may view the error details from your xCloud account.",
        'action' => 'View Server in xCloud',
    ],
    'server_disk_space_low' => [
        'subject' => 'Disk Space Low',
        'line' => 'Your server, :name is running low on disk space. Please take necessary action to avoid any downtime.',
        'action' => 'View Server in xCloud'
    ],
    'resource_used_high' => [
        'subject' => 'High resource usage detected',
        'line' => "We have detected high resource usage on load in your Server :serverName (:serverIP). If the issue is not resolved, we will send this notification again in :intervalInHour hour.",
        'action' => 'View Server in xCloud'
    ],
    'resource_used_normal' => [
        'subject' => 'Server resources back to normal usage',
        'line' => 'Your Server :serverName (:serverIP) resources are back to normal usage.',
        'action' => 'View Server in xCloud'
    ],
    'server_provisioning_failed' => [
        'subject' => 'Server Setup Failed',
        'line' => "Server setup failed. This email is to notify you that there were some problems while setting up new server, :name.",
        'action' => 'View Server in xCloud'
    ],
    'reboot_required' => [
        'subject' => 'Reboot Required',
        'line' => 'New security updates have been automatically installed on your server, :name. For the changes to take effect, you must reboot the server. If you need assistance, check out our guide here on how to reboot your server.',
        'action' => 'Reboot Server',
    ],
    'server_resize_failed' => [
        'subject' => 'Server Resize Failed',
        'line' => "Server resize failed. This message is to notify you that there were some problems while upgrading your server :serverName. You may view the error details from your xCloud account.",
        'action' => 'View Server in xCloud',
    ],
    'server_resize_success' => [
        'subject' => 'Update: Server Resize Successful',
        'line' => "This message is to notify you that your server xCloud-Project has been upgraded successfully.",
        'action' => 'View Server in xCloud',
    ],
    'site_backup_delete_failed' => [
        'subject' => 'Backup Delete Failed',
        'line' => 'Unfortunately we were unable to delete backup for site :name. You can check the cause of errors here. Error: :error',
        'error' => 'Error: :error',
        'action' => 'View Site in xCloud',
    ],
    'site_backup_failed' => [
        'subject' => 'Backup Failed',
        'line' => 'Unfortunately we were unable to backup site :name. You can check the cause of errors below. Error: :error',
        'error' => 'Error: :error',
        'action' => 'View Site in xCloud',
    ],
    'site_restore_success' => [
        'subject' => 'Restore Successful',
        'line' => 'Your site, :name has been restored successfully. Restore Time: :restore_time',
        'restore_time' => 'Restore Time: :restore_time',
        'action' => 'View Site in xCloud',
    ],
    'site_migrate_failed' => [
        'subject' => 'Migration Failed',
        'line' => 'Unfortunately we were unable to migrate site :name. You can check the cause of errors below.',
        'action' => 'View Site in xCloud',
    ],
    'site_migrate_progress' => [
        'subject' => 'Migrate Site - Setup In Progress',
        'line' => 'This is to notify you that your website migration for :name is currently underway. You will receive an email once the migration is complete. In the meantime, feel free to check in on the progress of your site migration by logging into xCloud.',
        'action' => 'View Site in xCloud',
    ],
    'site_migrate_success' => [
        'subject' => 'Migration Success',
        'line' => "Site :site has been successfully migrated!",
        'action' => 'View Site in xCloud',
    ],
    'site_provisioning_success' => [
        'line' => 'Your new site, :name has been set up successfully.',
        'action' => 'View Site in xCloud',
    ],
    'cpanel_backup_success' => [
        'subject' => 'CPanel Backup Using SCP Successful',
        'line' => 'Full Cpanel backup is now available for migration, you can continue with the migration process.',
        'action' => 'Continue CPanel Migration',
    ],

    'site_provisioning_failed' => [
        'subject' => 'Site Setup Failed',
        'line' => "This email is to notify you that there were some problems while setting up new site, :name.",
        'action' => 'View Site in xCloud',
    ],
    'site_restore_failed' => [
        'subject' => 'Restore Failed',
        'line' => 'Unfortunately we were unable to restore site :name. You can check the cause of errors here. Error: :error',
        'error' => 'Error: :error',
        'action' => 'View Site in xCloud',
    ],
    'ssl_renewed_failure' => [
        'subject' => 'SSL Renewal Failed',
        'line' => "This is to notify you that we were unable to renew SSL for :name. If the certificate is not renewed, your website may become inaccessible. You may view the error details from your xCloud account. There are some common reasons why SSL certificate renewal may fail, please check out this detailed documentation for help.",
        'action' => 'View Site in xCloud',
    ],
    'ssl_renewed_successor' => [
        'subject' => 'SSL Renewed Success',
        'line' => "Congratulations! SSL has been successfully renewed for :name. View SSL certificate by logging in to your xCloud account.",
        'action' => 'Go To Dashboard',
    ],
    'vulnerability' => [
        'line' => 'We found a security vulnerability in your website: :name. Please take necessary action to avoid any security breach.',
        'action' => 'View Site in xCloud',
    ],
];
