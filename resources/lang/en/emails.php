<?php

return [

    'billing_failed' => [
        'subject' => 'Billing Failed',
        'line_1' => 'Unfortunately, we could not process your payment for your :brand_name subscription. Please check if your billing information is accurate, and update the information if necessary.',
        'action' => 'Update Billing Information',
        'line_2' => 'If you have enabled auto renewal for your subscription, we will charge the credit/debit card you have added to your :brand_name account four times, after which your subscription will expire.',
        'line_3' => 'Once your subscription expires, you will no longer be able to perform any action on your server.',
        'line_4' => 'In addition, :brand_name will no longer carry out the following actions:',
    ],

    'billing_success' => [
        'subject' => 'Billing Successful',
        'greeting' => 'Billing Successful',
        'line_1' => 'Thank you for being a valued user of :brand_name! We have received your payment and have attached your latest invoice below.',
        'table_column_1' => 'Name: :name <br> Phone: :phone <br> Payment Amount: :amount <br> Date: :date',
        'table_column_2' => 'Have questions? <br><br> Contact our support team here.',
    ],
    'drop_action' => [
        'greeting' => 'Update: Your :model Has Been :action',
        'line' => 'This is to inform you that your :model :name has been :action as per your instructions.',
        'salutation' => 'If you believe this was a mistake, please contact our support team.',
    ],
    'reset_password' => [
        'subject' => 'Reset Password',
        'greeting' => 'Hi!',
        'line_1' => 'You are receiving this email because someone requested a password change for your account. If this was you, simply click on the button below and set your new password',
        'action' => 'Reset Password',
        'line_2' => 'If you did not request this change, simply ignore this message and contact our support team for further assistance. To keep your account secure, please do not share this email or the link with anyone.',
    ],
    'server_provisioning_success' => [
        'subject' => 'Server Setup Completed Successfully',
        'greeting' => 'Congratulations! Server Setup Is Successful',
        'line' => 'Your new server, :name has been set up successfully. Credentials are given below. Make sure to save these credentials as we will not be able to reset them.',
        'salutation' => 'Log in to :brand_name account to view more details.',
        'notification' => "Server :name setup completed successfully",
        'server_ip' => 'Server IP',
        'user' => 'User',
        'database_name' => 'Database Name',
        'database_username' => 'Database Username',
        'sudo_password' => 'Sudo Password',
        'database_password' => 'Database Password',
    ],
    'server_disk_space_low' => [
        'subject' => 'Disk Space Low',
        'greeting' => 'Update: Disk Space Low',
        'line' => 'Your server, :name is running low on disk space. Please take necessary action to avoid any downtime.',
        'salutation' => 'Log in to :brand_name account to view more details.',
        'notification' => "Server :name disk space is low",
    ],
    'server_provisioning_failed' => [
        'subject' => 'Server Setup Failed',
        'greeting' => "Update: New Server Setup Failed",
        'line' => "This email is to notify you that there were some problems while setting up new server, :name.",
        'notification' => "Server :name setup failed",
    ],
    'reboot_required' => [
        'subject' => 'Reboot Required',
        'line' => 'Security Update - :name requires a reboot.',
        'auto_reboot_line' => 'Security Update - :name will automatically update and reboot on :scheduled_time :time_zone. You can also reboot now.',
        'action' => 'View Details',
    ],
    'site_migrate_success' => [
        'subject' => 'Migration Successful',
        'greeting' => "Congratulations! Website migrations is complete",
        'line' => "Site :site has been successfully migrated!",
        'details' => "For more details, log into your :brand_name account by clicking the link below.",
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'site_migrate_failed' => [
        'subject' => 'Migration Failed',
        'greeting' => 'Update: Website Migrations Failed',
        'line' => 'Unfortunately we were unable to migrate site :name. You can check the cause of errors below.',
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'site_migrate_progress' => [
        'subject' => 'Migrate Site - Setup In Progress',
        'line' => 'This is to notify you that your website migration for :name is currently underway. You will receive an email once the migration is complete.',
        'line_2' => 'In the meantime, feel free to check in on the progress of your site migration by logging into :brand_name.',
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'site_dns' => [
        'error_subject' => 'DNS Error',
        'error_line' => 'We have detected a DNS error on your site :name. You can check the cause of errors below.',
        'success_subject' => 'DNS Update Successful',
        'success_greeting' => 'Congratulations! DNS Update Successful',
        'success_line' => 'DNS for site :name has been updated successfully.',
        'salutation' => 'If you have any questions, please contact us at',
        'action' => 'Go to Site',
    ],
    'site_ssl' => [
        'error_subject' => 'SSL Error',
        'error_line' => 'We have detected an SSL error on your site :name. You can check the cause of errors below.',
        'success_subject' => 'SSL Update Successful',
        'success_greeting' => 'Congratulations! SSL Update Successful',
        'success_line' => 'SSL for site :name has been updated successfully.',
        'salutation' => 'If you have any questions, please contact us at',
        'action' => 'Go to Site',
    ],
    'cpanel_backup_success' => [
        'subject' => 'CPanel Backup Using SCP Successful',
        'greeting' => 'Congratulations! Full Cpanel backup is now available for migration',
        'line' => 'Full Cpanel backup is now available for migration, you can continue with the migration process.',
        'salutation' => 'If you have any questions, please contact us at',
        'details' => 'Log in to your :brand_name account to view more details.',
        'action' => 'Continue CPanel Migration',
        'notification' => 'CPanel backup using SCP successful',
    ],

    'site_provisioning_success' => [
        'subject' => 'Site Setup Completed Successfully',
        'greeting' => 'Congratulations! Site :name setup is successful',
        'line' => 'Your new site, :name has been set up successfully. Your credentials are given below. Make sure to store these credentials somewhere safe.',
        'custom_action' => 'Login to Site',
        'action' => 'View Site in :brand_name',
        'details' => 'Log in to your :brand_name account to view more details.',
        'salutation' => 'If you have any questions, please contact us at',
        'notification' => 'Site :name provisioned successfully.',
        'url' => 'URL',
        'user' => 'User',
        'https' => 'HTTPS',
        'page_cache' => 'Page Cache',
        'php_version' => 'PHP Version',
        'site_user' => 'Site User',
        'password' => 'Password',
    ],
    'site_provisioning_failed' => [
        'subject' => 'Site Setup Failed',
        'greeting' => "Update: New Site Setup Failed",
        'line' => "This email is to notify you that there were some problems while setting up new site, :name.",
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'ssl_expire_soon' => [
        'subject' => 'SSL Certificate Expiry Alert',
        'line' => 'This is to notify you that the SSL certificate for :name will expire on :expiry_date. Please renew the certificate to avoid any downtime.',
        'salutation' => 'If you have any questions, please contact us at',
        'action' => 'Renew SSL Certificate',
    ],
    'ssl_renewed_failure' => [
        'subject' => 'SSL Renewal Failed',
        'line' => "This is to notify you that we were unable to renew SSL for :name",
        'line_2' => 'If the certificate is not renewed, your website may become inaccessible.',
        'line_3' => "You may view the error details from your :brand_name account.",
        'line_4' => "There are some common reasons why SSL certificate renewal may fail, please check out this detailed documentation for help.",
        'salutation' => 'If you need further assistance, feel free to reach out to our support team.',
    ],
    'ssl_renewed_successor' => [
        'subject' => 'SSL Renewed Successfully',
        'line' => "Congratulations! SSL has been successfully renewed for :name. View SSL certificate by logging in to your :brand_name account.",
        'salutation' => "If you have any questions, please contact us at",
    ],
    'greeting' => 'Hi :name,', // :name is a placeholder for the name of the user
    'salutation' => 'Thank you for choosing :brand_name! We appreciate your business. If you believe this was a mistake, please contact our support team.',
    'action' => 'Sign In To :brand_name',
    'line' => 'Log in to your :brand_name account to view more details.',
    'address' => 'xCloud INC, 124 Broadkill Road, Suit 599, Milton, Delaware, 19968',
    'server_progress_text' => 'It might take around 10-15 minutes to create your server.',
    'site_progress_text' => 'This may take a few moments to create your site.',
    'server_title_text' => 'Setting Up Your Server',
    'site_title_text' => 'Setting Up Your Site',
    'domain_update_failed' => [
        'subject' => 'Domain Update Failed',
        'line' => "This email is to notify you that your request for updating domain from :previousDomain to :newDomain has failed!.",
        'line_2' => "You may view the error details from your :brand_name account.",
        'solution' => 'If you need further assistance, feel free to reach out to our support team at',
        'action' => 'Go to Site'

    ],
    'server_resize_success' => [
        'subject' => 'Update: Server Resize Successful',
        'line' => "This email is to notify you that your server :brand_name-Project has been upgraded successfully.",
        'line_2' => "Your server configuration has been updated from :previousServerSize to :newServerSize.",
        'details' => 'Log in to your :brand_name account to view more details.',
        'action' => 'Sign into :brand_name',
        'solution' => 'If you believe this was a mistake, please contact our support team.',
    ],
    'server_resize_failed' => [
        'subject' => 'Server Resize Failed',
        'line' => "This email is to notify you that there were some problems while upgrading your server :serverName",
        'line_2' => "You may view the error details from your :brand_name account.",
        'details' => 'Log in to your :brand_name account to view more details.',
        'action' => 'Sign into :brand_name',
        'solution' => 'If you believe this was a mistake, please contact our support team.',
    ],
    'server_disconnected' => [
        'subject' => 'Unable to connect to server :serverName',
        'line' => "This email is to notify you that we were unable to connect to your server :serverName",
        'line_2' => "You may view the error details from your :brand_name account.",
        'details' => 'Log in to your :brand_name account to view more details.',
        'action' => 'Sign into :brand_name',
        'solution' => 'If you believe this was a mistake, please contact our support team.',
    ],
    'resource_used_high' => [
        'subject' => 'High resource usage detected',
        'line' => "We have detected high resource usage on load in your Server <b> :serverName </b> (:serverIP). If the issue is not resolved, we will send this notification again in :intervalInHour hour.",
        'solution' => 'If you believe this was a mistake, please contact our support team.',
        'high_resource_usage' => 'High resource usage:',
        'load_average' => 'Load average',
        'threshold' => "threshold:"
    ],
    'resource_used_normal' => [
        'subject' => 'Server resources back to normal usage',
        'line' => 'Your Server <b> :serverName </b> (:serverIP) resources are back to normal usage.',
        'solution' => 'If you believe this was a mistake, please contact our support team.',
    ],
    'monthly_invoice_due' => [
        'subject' => ":app_name - Your :current_month invoice for :team is available",
        'line' => "Your monthly :app_name invoice is ready for review",
        'line_2' => "We kindly remind you that your invoice for the :current_month team hosting services is due for payment.
                    Please review the details below and proceed with the payment at your earliest convenience.",
        'invoice_no' => "Invoice Number: :invoice_reference",
        'invoice_date' => "Issue Date: :issue_date",
        'payable_amount' => "Payable Amount: $:payable_amount",
        'due_date' => 'Due Date: :due_date',
        'action' => 'Check Bills And Payment'
    ],
    'monthly_invoice' => [
        'subject' => ":app_name - Your :current_month Invoice for :team is available",
        'line' => "Your :app_name invoice for :current_month is paid.",
        'line_2' => "We are pleased to inform you that the payment for your :current_month invoice for :team has been successfully processed.
                     The balance was automatically charged to your card, so you don't need further action.",
        'invoice_no' => "Invoice Number: :invoice_number",
        'invoice_date' => "Issue Date: :issue_date",
        'paid_amount' => "Paid Amount: $:paid_amount",
        'due_date' => "Due Date: :due_date",
        'action' => 'Check Bills And Payment'
    ],
    'monthly_invoice_payment_failed' => [
        'subject' => ":app_name - Payment Failed for Your :current_month Invoice",
        'line' => "We regret to inform you that the payment for your :current_month invoice for :team has failed.",
        'line_2' => "To avoid any disruption in services, please review the payment details and attempt the payment again at your earliest convenience.",
        'invoice_no' => "Invoice Number: :invoice_number",
        'invoice_date' => "Issue Date: :issue_date",
        'due_date' => "Due Date: :due_date",
        'amount_due' => "Amount Due: $:amount_due",
        'action' => 'Retry Payment',
        'solution' => "If you believe this is an error or have any questions, please contact our support team.",
    ],
    'monthly_manual_invoice' => [
        'subject' => ":app_name - Your :current_month Invoice for :team is available",
        'line' => "Your :app_name invoice for :current_month is ready. Please note that payment is not processed automatically. Kindly proceed with manual payment at your earliest convenience.",
        'line_2' => "Thank you for choosing :app_name. We appreciate your business. If you have already made the payment for your :current_month invoice for :team, kindly disregard this message.",
        'invoice_no' => "Invoice Number: :invoice_reference",
        'invoice_date' => "Issue Date: :issue_date",
        'due_date' => "Due Date: :due_date",
        'paid_amount' => "Paid Amount: $:paid_amount",
        'action' => 'Check Bills And Payment'
    ],
    'manual_invoice_paid' => [
        'subject' => ":app_name - Your :current_month Invoice for :team has been paid",
        'line' => "We are pleased to inform you that the payment for your :current_month invoice for :team has been successfully processed using :payment_method.",
        'invoice_no' => "Invoice Number: :invoice_number",
        'invoice_date' => "Issue Date: :issue_date",
        'bill_amount' => "Bill Amount: $:bill_amount",
        'discount_amount' => "Discount: $:discount_amount",
        'paid_amount' => "Paid Amount: :paid_amount",
        'amount_received_in' => "Currency: :amount_received_currency",
        'updated_by' => "Action Taken By: :updated_by",
        'action' => 'Check Bills And Payment'
    ],
    'invoice' => [
        'subject' => ":app_name - New Invoice for :team",
        'line' => 'We are pleased to inform you that the payment for your :purchase_title invoice for :team has been successfully processed.',
        'line_2' => "Please find the invoice in attachment. The balance was automatically charged to your card, so you don't need further action.",
        'invoice_no' => ":invoice_number",
        'invoice_date' => ":invoice_date",
        'paid_amount' => "$:paid_amount",
        'action' => 'Check Bills And Payment'
    ],
    'invoice_refund' => [
        'subject' => ":app_name - you received refund for :team",
        'line' => "We are pleased to inform you that the refund for your :purchase_title invoice for :team has been successfully processed.",
        'line_2' => "Please find the invoice in attachment. The balance was automatically charged to your card, so you don't need further action.",
        'invoice_no' => ":invoice_number",
        'invoice_date' => ":invoice_date",
        'refund_amount' => "$:refund_amount",
        'action' => 'Check Bills And Payment'
    ],
    'manual_invoice' => [
        'subject' => ":app_name - New Invoice for :team",
        'line' => "We are pleased to inform you that the invoice :purchase_title for team :team has been successfully processed.",
        'line_2' => "Please find the invoice in attachment. Kindly proceed with manual payment at your earliest convenience.",
        'invoice_no' => ":invoice_number",
        'invoice_date' => ":invoice_date",
        'due_date' => ":due_date",
        'paid_amount' => "$:paid_amount",
        'action' => 'Check Bills And Payment'
    ],
    'invoice_failed' => [
        'subject' => ':service_name - Invoice Payment Failed',
        'greeting' => 'Update: Invoice Payment Failed',
        'line_1' => 'We regret to inform you that the payment for your invoice (:invoice_number) has failed.',
        'line_2' => 'Please pay your invoice manually by completing the verification process.',
        'line_3' => 'If you believe this is an error or have any questions, please contact our support team immediately.',
        'salutation' => 'Thank you for choosing :app_name.',
        'paid_amount' => "Paid Amount: $:paid_amount",
        'action' => 'Complete Payment Verification',
    ],
    'site_backup_failed' => [
        'subject' => 'Backup Failed',
        'greeting' => 'Update: Site Backup Failed',
        'line' => 'Unfortunately we were unable to backup site :name. You can check the cause of errors below.',
        'error' => 'Error: :error',
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'site_backup_delete_failed' => [
        'subject' => 'Backup Delete Failed',
        'greeting' => 'Update: Site Backup Delete Failed',
        'line' => 'Unfortunately we were unable to delete backup for site :name. You can check the cause of errors below.',
        'error' => 'Error: :error',
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'site_restore_failed' => [
        'subject' => 'Restore Failed',
        'greeting' => 'Update: Site Restore Failed',
        'line' => 'Unfortunately we were unable to restore site :name. You can check the cause of errors below.',
        'error' => 'Error: :error',
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'site_restore_success' => [
        'subject' => 'Restore Successful',
        'greeting' => 'Congratulations! Site :name restore is successful',
        'line' => 'Your site, :name has been restored successfully.',
        'restore_time' => 'Restore Time: :restore_time',
        'salutation' => 'If you have any questions, please contact us at',
    ],
    'site_delete_success' => [
        'subject' => 'Site Deleted Successfully',
        'greeting' => 'Update: Site Deleted Successfully',
        'line' => 'This is to inform you that your site :name has been deleted successfully.',
        'salutation' => 'If you believe this was a mistake, please contact our support team.',
    ],
    'user_welcome' => [
        'subject' => '🙌Welcome To xCloud: Your Gateway to Effortless Hosting for WordPress Sites!',
        'greeting' => 'Hey There 👋',
        'welcome_to_xcloud' => "Welcome to the <a href='https://xcloud.host/'>xCloud</a> family! 🎉 We're thrilled to have you on board and can't wait to assist you in simplifying your hosting for WordPress journey. Our mission is to make your web development experience as seamless as possible, so you can focus on creating remarkable websites without the hassle of managing server complexities.",
        'what_xcloud_can_do' => "<h1>🔧 What xCloud Can Do For You?</h1>",
        'automated_server_management' => "Our platform takes care of the nitty-gritty details, like configuring servers, installing WordPress, setting up SSL certificates, monitoring server performance, resizing servers, and even keeping your WordPress plugins up to date.",
        'simplified_dashboard' => "<h1>🖥️ Simplified Dashboard For You</h1>",
        'centralized_site_management' => "Now, let's talk about your dashboard – your central hub for managing your WordPress sites. Your dashboard is your command center, where you can effortlessly oversee all your sites, servers, and more. It's the heart of your xCloud experience.",
        'seamless_integrations' => "<h1>⛓️Seamless Integrations</h1>",
        'flexible_integration_support' => "We understand that every project is unique, and you might have specific needs. That's why xCloud supports a wide range of integrations like Vultr, AWS, GCP, Digital Ocean, and more ensuring that you can tailor your hosting environment to match your exact requirements.",
        'vultr_partnership' => "<h1>🎁 Vultr Partnership</h1>",
        'vultr_partnership_benefits' => "Our <a href='https://xcloud.host/docs/how-to-get-free-hosting-with-vultr/'>Vultr and xCloud partnership</a> offers Free hosting for Six months for Vultr servers. By simply signing up for Vultr using the code <strong>“XCLOUD”</strong>, you’ll receive <strong>$100</strong> in free credits. Also, new signups in xCloud allow users to create 1 server and 1 site for free using the Bring Your Own Server service.",
        'free_migrate' => "<h1>✅ Migrate For Free</h1>",
        'free_seamless_migrations' => "<a href='https://xcloud.host/docs/perform-a-full-server-migration-with-xcloud/'>Migrate multiple WordPress websites</a> seamlessly to xCloud for free, with our Full Server Migration Feature and guided, step-by-step Migration Plugin. Our WordPress experts ensure swift, live-site-friendly migrations.",
        'team_collaboration' => "🪁 Team Collaboration</h1>",
        'auto_team_create' => 'When you join xCloud, an account <a href="https://xcloud.host/docs/team-roles-permission-for-xcloud/">team</a> is created automatically. These teams are vital for collaborating with those managing your servers and websites. You can make multiple teams, add team members, and adjust their access and roles in the "Members" section of Team Settings.',
        'dedicated_support' =>  "Your journey with xCloud has just begun, and we're here to assist you every step of the way. If you ever have questions or need support, our dedicated team is ready to help. Feel free to reach out to us at  <a href='mailto:<EMAIL>'><EMAIL></a> anytime.",
        'action' => 'Get Started Now',
        'salutation' => "Welcome aboard! <br> Warm regards,<br> xCloud Team",
    ],
    'vulnerability' => [
        'subject' => 'Immediate Action Required: We found a security vulnerability in your website',
        'greeting' => 'Vulnerability Detected',
        'line' => 'We have discovered a security vulnerability in the :plugin_or_theme that you are using on the following website: :site',
        'details' => 'The plugin has a vulnerability that makes it possible for unauthorized actions.',
        'version_details' => 'This vulnerability affects versions before :version. We recommend immediately updating this plugin to the latest version in both live and staging environments.',
        'datetime_details' => 'We detected this vulnerability on :datetime. You can ignore this message if you have already updated :plugin_or_theme to the latest version or have it set to update automatically.',
        'dedicated_support' => 'If you have any questions, do not hesitate to contact our support team.',
        'salutation' => 'Thank you for being a :brand_name user!',
        'id' => 'ID: :id',
        'notification' => 'Vulnerability detected in :name',
    ],
];
