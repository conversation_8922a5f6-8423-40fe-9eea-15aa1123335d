<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => '验证卡片 & 处理支付',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => '验证卡片并处理支付 $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => '创建服务器',
            'tasks' => [
                ServerProvisioning::INIT => '初始化服务器配置',
                ServerProvisioning::CREATING_SERVER => '在 :provider 上创建服务器',
                ServerProvisioning::SERVER_CREATED => '服务器已创建 :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => '等待服务器启动',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => '连接到服务器',
            'tasks' => [
                ServerProvisioning::CONNECTING => '连接到 SSH',
                ServerProvisioning::CONNECTED => '连接已建立',
            ]
        ],
        [
            'stage' => '配置服务器',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => '配置交换文件',
                ServerProvisioning::UPGRADING_SYSTEM => '升级系统',
                ServerProvisioning::INSTALLING_BASE => '安装基础依赖',
                ServerProvisioning::AUTHENTICATION_METHOD => '更新身份验证方式',
                ServerProvisioning::UPDATING_HOSTNAME => '更新主机名',
                ServerProvisioning::UPDATING_TIMEZONE => '更新时区',
                ServerProvisioning::XCLOUD_USER => '设置用户',

                ServerProvisioning::SETUP_SSH => '设置 SSH',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => '设置 Sudo 权限',
                ServerProvisioning::SETTING_UP_GIT => '设置 Git',
                ServerProvisioning::SETUP_FIREWALL => '设置防火墙',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => '设置清理脚本',
            ]
        ],
        [
            'stage' => '安装应用',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => '安装 PHP :php_version',
                ServerProvisioning::INSTALLING_WEBSERVER => '安装 :stack 网络服务器',
                ServerProvisioning::INSTALLING_NODE => '安装 Node',
                ServerProvisioning::INSTALLING_REDIS => '安装 Redis',
                ServerProvisioning::INSTALLING_DATABASE => '安装 :database_type 数据库',
                ServerProvisioning::INSTALLING_WP_CLI => '安装 WP CLI',
            ]
        ],
        [
            'stage' => '完成',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => '设置 SSH 权限',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => '安装监控脚本',
                ServerProvisioning::READY_TO_DO_MAGIC => '准备开始操作！',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => '检查和验证',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => '检查服务器存储和连接',
                SiteProvisioning::VERIFYING_DNS => '验证您的站点 DNS',
            ]
        ],
        [
            'stage' => '安装应用',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => '正在安装 :site_stack :site_stack_version',
                SiteProvisioning::INSTALLING_DATABASE => '正在安装数据库',
                SiteProvisioning::INSTALLING_WORDPRESS => '正在设置 :type',
            ]
        ],
        [
            'stage' => '配置',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => '配置 SSL',
                SiteProvisioning::CONFIGURING_HTTPS => '配置 HTTPS',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => '配置 :cache 缓存',
                SiteProvisioning::CONFIGURING_NGINX => '配置 :stack',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => '配置 Redis 对象缓存',
                SiteProvisioning::INSTALL_BLUEPRINT => '安装 Blueprint',
                SiteProvisioning::DEPLOY_SCRIPT => '部署脚本',
                SiteProvisioning::INSTALL_MONITORING => '安装监控',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => '安装 WP Cron 作业',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => '设置 xCloud 管理邮件服务',
                SiteProvisioning::HANDLE_INDEXING => '处理站点索引',
                SiteProvisioning::FINISHING_UP => '完成',
            ]
        ]
    ],
];
