{"xCloud": "xCloud", "xcloud": "xcloud", "Log In To": "Log In To", "Email": "Email", "Password": "Password", "forgot_password": "Forgot password", "Remember me": "Remember me", "Log In": "Log In", "Not Registered Yet? Please": "Not Registered Yet? Please", "Sign Up": "Sign Up", "Email Address..": "Email Address..", "Sign Up For": "Sign Up For", "Enter necessary informations for creating a new account": "Enter necessary informations for creating a new account.", "Name": "Name", "Use 8 or more characters with a mix of letters, numbers & symbols": "Use 8 or more characters with a mix of letters, numbers & symbols", "Password Confirmation": "Password Confirmation", "I agree to the": "I agree to the", "Terms": "Terms", "and": "and", "Privacy Policy": "Privacy Policy", "Already have an account? Please": "Already have an account? Please", "Forgot Password": "Forgot Password", "Please enter your email address to search for your account": "Please enter your email address to search for your account", "Reset Password": "Reset Password", "This is a secure area of the application. Please confirm your password before continuing.": "This is a secure area of the application. Please confirm your password before continuing.", "Confirm": "Confirm", "Confirm Password": "Confirm Password", "Two-factor Confirmation": "Two-factor Confirmation", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "Please confirm access to your account by entering the authentication code provided by your authenticator application.", "Please confirm access to your account by entering one of your emergency recovery codes.": "Please confirm access to your account by entering one of your emergency recovery codes.", "Code": "Code", "Recovery Code": "Recovery Code", "Use a recovery code": "Use a recovery code", "Use an authentication code": "Use an authentication code", "Verify Email Address": "Verify Em<PERSON> Address", "To continue using": "To continue using", "please click on the link in the verification email sent to your email.": "please click on the link in the verification email sent to your email.", "A new verification link has been sent to the email address you provided during registration.": "A new verification link has been sent to the email address you provided during registration.", "Please wait": "Please wait", "seconds to resend email.": "seconds to resend email.", "Resend Verification Email": "Resend Verification Email", "Logout": "Logout", "Overview": "Overview", "New Server": "New Server", "New Site": "New Site", "Active Servers": "Active Servers", "Total Sites": "Total Sites", "No Active Billing": "No Active Billing", "Plan": "Plan", "Vulnerable Sites": "Vulnerable Sites", "New Team Invitations": "New Team Invitations", "Server Setup In Progress": "Server Setup In Progress", "Site Setup In Progress": "Site Setup In Progress", "View All": "View All", "You can create 1 server and 10 sites with our Free Plan.": "You can create 1 server and 10 sites with our Free Plan.", "Two-Factor Authentication": "Two-Factor Authentication", "You have not enabled two-factor authentication, which is highly recommended for your account security.": "You have not enabled two-factor authentication, which is highly recommended for your account security.", "Skip": "<PERSON><PERSON>", "Setup Now": "Setup Now", "Please check your": "Please check your", "invoices": "invoices", "has asked you to join as a": "has asked you to join as a", "to this team": "to this team", "Decline": "Decline", "Accept": "Accept", "To decline the invitation please visit": "To decline the invitation please visit", "team page": "team page", "Server Disk Space Low": "Server Disk Space Low", "Warning": "Warning", "Your Server": "Your Server", "disk space is low. Please upgrade your plan to avoid any downtime.": "disk space is low. Please upgrade your plan to avoid any downtime.", "Reboot Required": "Reboot Required", "Security Update": "Security Update", "will automatically update and reboot on": "will automatically update and reboot on", "You can also reboot now.": "You can also reboot now.", "Security Update - Server": "Security Update - Server", "requires a reboot.": "requires a reboot.", "Reboot Now": "Reboot Now", "Security updates were automatically installed on your server, but a reboot is required to complete the installation. The following packages were upgraded:": "Security updates were automatically installed on your server, but a reboot is required to complete the installation. The following packages were upgraded:", "Be sure to test the sites on your server after rebooting.": "Be sure to test the sites on your server after rebooting.", "Server Size": "Server Size", "PHP": "PHP", "View Details": "View Details", "Server": "Server", "Continue Setup": "Continue Setup", "View Site": "View Site", "Open options": "Open options", "Bill Calculator": "<PERSON>", "Billing Amount": "Billing Amount", "Select Renewal Period": "Select Renewal Period", "Bill From": "<PERSON>", "Bill Before": "<PERSON>", "Bill To": "Bill <PERSON>", "Processing payment... Please do not cancel or refresh the page.": "Processing payment... Please do not cancel or refresh the page.", "My Blueprints": "My Blueprints", "Create New Blueprint": "Create New Blueprint", "Created": "Created", "Active Plugins": "Active Plugins", "Active Theme": "Active Theme", "Edit Blueprint": "Edit Blueprint", "Create Blueprint": "Create Blueprint", "By": "By", "Active Installations": "Active Installations", "Update": "Update", "Create": "Create", "Staging Url": "Staging Url", "Confirm Details": "Confirm Details", "Edit": "Edit", "Old Domain": "Old Domain", "HTTPS": "HTTPS", "Enabled": "Enabled", "Disabled": "Disabled", "PHP Version": "PHP Version", "Database Type": "Database Type", "Database Name": "Database Name", "Database User": "Database User", "No Database": "No Database", "Site User": "Site User", "Configuration & Database Connection": "Configuration & Database Connection", "Create Database In Server": "Create Database In Server", "Select Destination Server To Clone Your Site": "Select Destination Server To Clone Your Site", "Choose Server": "<PERSON>ose <PERSON>", "DNS & SSL For": "DNS & SSL For", "Demo Site": "Demo Site", "Create a demo site with our test domain and customize before going live.": "Create a demo site with our test domain and customize before going live.", "Migrate into a New Domain": "Migrate into a New Domain", "Get your site up and running for the world to see by simply pointing your domain to the server.": "Get your site up and running for the world to see by simply pointing your domain to the server.", "Add DNS and SSL Certificate on Cloudflare": "Add DNS and SSL Certificate on Cloudflare", "Integrate Cloudflare for Automatic DNS and SSL management.": "Integrate Cloudflare for Automatic DNS and SSL management.", "DNS Setup": "DNS Setup", "Settings & Configurations": "Settings & Configurations", "Deploy Script (Optional)": "De<PERSON><PERSON> (Optional)", "wp plugin install woocommerce": "wp plugin install woocommerce", "Verifying": "Verifying", "Uploading....": "Uploading....", "Upload/Drop Your Zipped sql or only .sql File": "Upload/Drop Your Zipped sql or only .sql File", "Maximum File Size: 500 MB": "Maximum File Size: 500 MB", "Use These Credentials To Transfer Database": "Use These Credentials To Transfer Database", "Server Connection Url": "Server Connection Url", "Server Address": "Server Address", "Server SSH Host": "Server SSH Host", "Server Username": "Server Username", "Server database name": "Server database name", "Database Password": "Database Password", "Verify Upload": "Verify Upload", "Upload/Drop Your Zipped/Tar File": "Upload/Drop Your Zipped/Tar File", "Use These Credentials To Upload Your WordPress Website": "Use These Credentials To Upload Your WordPress Website", "22": "22", "Port": "Port", "Remote Path": "Remote Path", "Upload Migration Files": "Upload Migration Files", "Choose a method from below to install the": "Choose a method from below to install the", "Migration Plugin": "<PERSON><PERSON> Plugin", "Optional": "Optional", "Authentication Token": "Authentication Token", "Plugin Page URL": "Plugin Page URL", "I have added the authentication token to my site": "I have added the authentication token to my site", "Please agree that you have added the authentication token to your site": "Please agree that you have added the authentication token to your site", "Download Plugin ZIP File": "Download Plugin ZIP File", "Plugin Link": "Plugin <PERSON>", "WP CLI": "WP CLI", "Show Previous cPanel Migrations": "Show Previous cPanel Migrations", "Username of the cPanel account:": "Userna<PERSON> of the cPanel account:", "Enter your cPanel username": "Enter your cPanel username", "cPanel Api Key:": "cPanel Api Key:", "Enter your cPanel API Key": "Enter your cPanel API Key", "cPanel Host:": "cPanel Host:", "Fetch Existing Backups": "Fetch Existing Backups", "Select a backup to import from the list below": "Select a backup to import from the list below", "Backup in Progress ...": "Backup in Progress ...", "No Backups Found": "No Backups Found", "You need to create a backup in your cPanel account first": "You need to create a backup in your cPanel account first", "Generate Backup": "Generate Backup", "Migration File": "Migration File", "Created At": "Created At", "Actions": "Actions", "Continue Migration": "Continue Migration", "No cPanel Migrations Found": "No cPanel Migrations Found", "You can create a new cPanel Migration from the cPanel Migration tab": "You can create a new cPanel Migration from the cPanel Migration tab", "Upload Completed": "Upload Completed", "Upload/Drop Your Zipped File": "Upload/Drop Your Zipped File", "Regenerate cPanel User": "Regenerate cPanel User", "I have started the backup": "I have started the backup", "Waiting for the backup to complete": "Waiting for the backup to complete", "Currently transferring this file from generated backup for cpanel migration": "Currently transferring this file from generated backup for cpanel migration", "New cPanel Migration using SCP found, click here to continue": "New cPanel Migration using SCP found, click here to continue", "Live Site": "Live Site", "Domain Setup": "Domain Setup", "Domain Name": "Domain Name", "Auto DNS management by Cloudflare is disabled for Full Server Migration.": "Auto DNS management by Cloudflare is disabled for Full Server Migration.", "Coming soon...": "Coming soon...", "Your DNS setup and SSL Certificate will be done by Cloudflare and managed by xCloud.": "Your DNS setup and SSL Certificate will be done by Cloudflare and managed by xCloud.", "Confirm Migration Details": "Confirm Migration Details", "Source site": "Source site", "Destination site": "Destination site", "Select Websites to Migrate": "Select Websites to Migrate", "to": "to", "Previously migrated sites from": "Previously migrated sites from", "Available Sites": "Available Sites", "Select All": "Select All", "Fetch Websites": "Fetch Websites", "Fetching...": "Fetching...", "Wordpress Sites To Migrate": "Wordpress Sites To Migrate", "Type": "Type", "Directory": "Directory", "Non-Wordpress Sites": "Non-Wordpress Sites", "Fetching sites...": "Fetching sites...", "Add Websites to Migrate": "Add Websites to Migrate", "Add the domains you want to migrate from your Cpanel backup, we will search these domains and migrate to the new server": "Add the domains you want to migrate from your Cpanel backup, we will search these domains and migrate to the new server", "Add Website": "Add Website", "Connect Your Source Server": "Connect Your Source Server", "Submit": "Submit", "Select your Hosting Provider": "Select your Hosting Provider", "cPanel Migration Method": "cPanel Migration Method", "Set domain for migration": "Set domain for migration", "Full Server Migration In Progress from": "Full Server Migration In Progress from", "Migration completed successfully from": "Migration completed successfully from", "Successfully Migrated Sites": "Successfully Migrated Sites", "Migration In Progress": "Migration In Progress", "Migration In Queue": "Migration In Queue", "Migration Failed": "Migration Failed", "IP Address": "IP Address", "SSH Port": "SSH Port", "SSH Username": "SSH Username", "root": "root", "SSH Authentication": "SSH Authentication", "SSH Password": "SSH Password", "Size": "Size", "GB": "GB", "Existing Site": "Existing Site", "Full Page Cache": "Full Page Cache", "Redis Object Cache": "Redis Object Cache", "Default Database In Server": "Default Database In Server", "Database": "Database", "Admin User Name": "Admin User Name", "Files and Database Migration": "Files and Database Migration", "Select Databases & File Systems To Migrate": "Select Databases & File Systems To Migrate", "Migrate The Following Content": "Migrate The Following Content", "All Database Tables & Corresponding File System": "All Database Tables & Corresponding File System", "Only File System But NOT Database Tables": "Only File System But NOT Database Tables", "Select Destination Server To Migrate Your Site": "Select Destination Server To Migrate Your Site", "Existing WordPress Site URL": "Existing WordPress Site URL", "Existing Site URL": "Existing Site URL", "Git Repository": "Git Repository", "master": "master", "Git Branch": "Git Branch", "Enable push to deploy": "Enable push to deploy", "Deployment URL": "Deployment URL", "npm run deploy": "npm run deploy", "Deploy Script": "De<PERSON><PERSON>", "Run this script after every site deployment": "Run this script after every site deployment", "Select Databases To Migrate": "Select Databases To Migrate", "Add Your Existing Database": "Add Your Existing Database", "Without Database": "Without Database", "Install Migration Plugin": "Install Migration Plugin", "Buy Now": "Buy Now", "Packages": "Packages", "Products": "Products", "Bills & Payment": "Bills & Payment", "Free plan which includes 1 server and 10 website with Self Hosting.": "Free plan which includes 1 server and 10 website with Self Hosting.", "You’re on the": "You’re on the", "Activate your": "Activate your", "team by adding payment method today.": "team by adding payment method today.", "team by adding payment method.": "team by adding payment method.", "Estimated Cost": "Estimated Cost", "This is an estimate of the amount based on your current month-to-date": "This is an estimate of the amount based on your current month-to-date", "Cost This Month": "Cost This Month", "Cost Next Month": "Cost Next Month", "Overused Amount After 28th": "Overused Amount After 28th", "Billing Period Monthly": "Billing Period Monthly", "renews 29": "renews 29", "Read how billing works.": "Read how billing works.", "Subscriptions": "Subscriptions", "xCloud Managed Email Provider": "xCloud Managed Email Provider", "Services": "Services", "Payment Methods": "Payment Methods", "You can add Credit/Debit Card as your payment method": "You can add Credit/Debit Card as your payment method", "Expires at": "Expires at", "Default": "<PERSON><PERSON><PERSON>", "Set As Default": "<PERSON> As De<PERSON>ult", "Add A Payment Method": "Add A Payment Method", "Billing History": "Billing History", "Description": "Description", "Date": "Date", "Service Status": "Service Status", "Amount": "Amount", "Payment Status": "Payment Status", "Next Billing": "Next Billing", "Renewal Period": "Renewal Period", "Link a Credit/Debit Card": "Link a Credit/Debit Card", "We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)": "We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)", "Service": "Service", "Amount Payable": "Amount Payable", "discontinued on": "discontinued on", "to lifetime": "to lifetime", "I want to convert this bill to monthly and unlock full features": "I want to convert this bill to monthly and unlock full features", "Please add a": "Please add a", "payment": "payment", "method unlock more features.": "method unlock more features.", "Amount Paid": "Amount <PERSON>", "Whitelabel Subscriptions": "Whitelabel Subscriptions", "Current Plan": "Current Plan", "Sell Up to": "Sell Up to", "Servers": "Servers", "Subscription Date": "Subscription Date", "Expires on": "Expires on", "Your subscription is about to expire please renew it": "Your subscription is about to expire please renew it", "Your subscription is about to expire please renew it for uninterrupted service.": "Your subscription is about to expire please renew it for uninterrupted service.", "Pay Now": "Pay Now", "No active subscription found": "No active subscription found", "Claim Free with LTD": "Claim Free with LTD", "Server Limit": "Server Limit", "As an existing LTD customer, you are eligible to claim this plan for free.": "As an existing LTD customer, you are eligible to claim this plan for free.", "Switch Free with LTD": "Switch Free with LTD", "Switch to this plan for": "Switch to this plan for", "Choose a plan": "Choose a plan", "SMTP Username": "SMTP Username", "SMTP Password": "SMTP Password", "Domain": "Domain", "Sendgrid Username": "<PERSON><PERSON><PERSON>", "Sendgrid Api Key": "Sendgrid Api Key", "SMTP Host": "SMTP Host", "SMTP Port": "SMTP Port", "Label": "Label", "Get help from our": "Get help from our", "Configure SMTP Provider": "Configure SMTP Provider", "documentation": "documentation", "From Email": "From Email", "To Email": "To Email", "Email Subscriptions": "Email Subscriptions", "With xCloud you get 100 free emails per month in each team. You can purchase additional email more from": "With xCloud you get 100 free emails per month in each team. You can purchase additional email more from", "here.": "here.", "xCloud Email Balance": "xCloud Email Balance", "This is a summary of your xCloud Email Balance.": "This is a summary of your xCloud Email Balance.", "Total Emails": "Total Emails", "Emails Available": "Emails Available", "Elastic Email API Key": "Elastic Email API Key", "Invoice": "Invoice", "Details": "Details", "You will pay for following bills": "You will pay for following bills", "which are under": "which are under", "Next Billing Amount": "Next Billing Amount", "Amount Adjusted": "Amount Adjusted", "Add New Member": "Add New Member", "Send Invitation": "Send Invitation", "Server Access": "Server Access", "Access to all server": "Access to all server", "Choose specific server": "Choose specific server", "Select here...": "Select here...", "Site Access": "Site Access", "Access to all site": "Access to all site", "Choose specific site": "Choose specific site", "Role Permission": "Role Permission", "Create New Team": "Create New Team", "Save Changes": "Save Changes", "Add this URL as a webhook in your Git repository settings to enable automated deployments": "Add this URL as a webhook in your Git repository settings to enable automated deployments", "Laravel Debug Log": "Laravel Debug Log", "Laravel": "<PERSON><PERSON>", "Environment": "Environment", "Update Environment": "Update Environment", "Deploy Now": "Deploy Now", "Deploying...": "Deploying...", "Your Laravel site deployment has been initiated. Please wait while the changes are being deployed.": "Your Laravel site deployment has been initiated. Please wait while the changes are being deployed.", "Failed to deploy Laravel site. Please try again.": "Failed to deploy Laravel site. Please try again.", "Upload Your Avatar": "Upload Your Avatar", "Add a profile picture or icon for your account": "Add a profile picture or icon for your account", "Upload Image": "Upload Image", "Team Name": "Team Name", "Team Email": "Team Email", "Tags": "Tags", "Select or create tags": "Select or create tags", "Edit Team": "Edit Team", "Update Member": "Update Member", "Update Invitation": "Update Invitation", "Email ID": "Email ID", "User Role": "User Role", "Delete Team": "Delete Team", "Leave Team": "Leave Team", "Team Members": "Team Members", "Role": "Role", "Status": "Status", "Server Count": "Server Count", "Action": "Action", "Change Role": "Change Role", "Delete": "Delete", "Resend Email": "<PERSON><PERSON><PERSON>", "Site": "Site", "Switch to Team": "Switch to Team", "Add Member": "Add Member", "Check Details": "Check Details", "All Sites": "All Sites", "Refresh": "Refresh", "Archive Servers": "Archive Servers", "List of Archive Servers": "List of Archive Servers", "Find all the archive servers associated with your team here.": "Find all the archive servers associated with your team here.", "Provider": "Provider", "If you proceed, this will permanently remove this service and you will not be able to access or retrieve it again.": "If you proceed, this will permanently remove this service and you will not be able to access or retrieve it again.", "Authentication": "Authentication", "Please enter the two-factor authentication code sent to you below to verify:": "Please enter the two-factor authentication code sent to you below to verify:", "Billing Details": "Billing Details", "Saved Cards": "Saved Cards", "Add your payment methods for billing.": "Add your payment methods for billing.", "Set as Default": "Set as <PERSON><PERSON><PERSON>", "No card available": "No card available", "Add Payment Method": "Add Payment Method", "Bills": "Bills", "Find complete, downloadable receipts of your regular payments": "Find complete, downloadable receipts of your regular payments", "Title": "Title", "Amount To Pay": "Amount To Pay", "Due On": "Due On", "Next Billing Date": "Next Billing Date", "Paid On": "<PERSON><PERSON>", "Manage Active Browser Sessions": "Manage Active Browser Sessions", "Check which sessions are still active from different devices & browsers, and manage as needed.": "Check which sessions are still active from different devices & browsers, and manage as needed.", "Note: You can log out of all your active sessions; it will also log you out of this session and you will have to log back in to continue using": "Note: You can log out of all your active sessions; it will also log you out of this session and you will have to log back in to continue using", "Log Out Of All Sessions": "Log Out Of All Sessions", "Browser Name": "Browser Name", "Last Session": "Last Session", "This device": "This device", "Last active": "Last active", "Cloudflare Integration": "Cloudflare Integration", "List of your Cloudflare Integrations": "List of your Cloudflare Integrations", "Find all the cloudflare integrations associated with your account here.": "Find all the cloudflare integrations associated with your account here.", "New Cloudflare Integration": "New Cloudflare Integration", "Account Email": "Account <PERSON><PERSON>", "API Token": "API Token", "Global API Key": "Global API Key", "Origin CA Key": "Origin CA Key", "Add New Cloudflare Integration": "Add New Cloudflare Integration", "Name of the integration": "Name of the integration", "Your Cloudflare Account Email": "Your Cloudflare Account Email", "Create a new API Token from your Cloudflare account and paste it here. (With Edit zone DNS permissions)": "Create a new API Token from your Cloudflare account and paste it here. (With Edit zone DNS permissions)", "Go to your Cloudflare Profile > API Tokens > Global API Key > View": "Go to your Cloudflare Profile > API Tokens > Global API Key > View", "Go to your Cloudflare Profile > API Tokens > Origin CA Key > View": "Go to your Cloudflare Profile > API Tokens > Origin CA Key > View", "Integrate Cloudflare For DNS Management": "Integrate Cloudflare For DNS Management", "Account email": "Account email", "Find complete, downloadable receipts of your regular payments.": "Find complete, downloadable receipts of your regular payments.", "All Invoices": "All Invoices", "This Month": "This Month", "Last Month": "Last Month", "Last 6 Months": "Last 6 Months", "Last 1 Year": "Last 1 Year", "Paid Invoices": "Paid Invoices", "Unpaid Invoices": "Unpaid Invoices", "Failed Invoices": "Failed Invoices", "Invoice No": "Invoice No", "Download": "Download", "Cancel": "Cancel", "Email Provider": "Email Provider", "List of Email Providers": "List of Email Providers", "Find all the email providers associated with your account here.": "Find all the email providers associated with your account here.", "Add New Provider": "Add New Provider", "Username/Domain": "Username/Domain", "Invitation List": "Invitation List", "Invite Users": "Invite Users", "Share your invitation code with others. You have": "Share your invitation code with others. You have", "invitation": "invitation", "remaining": "remaining", "Invited Users": "Invited Users", "You have invited following persons.": "You have invited following persons.", "Accepted": "Accepted", "Notifications": "Notifications", "List of Notifications": "List of Notifications", "Integrate your notification platform. You can customize your": "Integrate your notification platform. You can customize your", "notification settings": "notification settings", "from": "from", "here": "here", "Add New Notification": "Add New Notification", "Disconnect": "Disconnect", "Reconnect": "Reconnect", "Add Notification": "Add Notification", "Being with Country Code (e.g., ****** XXX XXXX for US)": "Being with Country Code (e.g., ****** XXX XXXX for US)", "WhatsApp Phone Number": "WhatsApp Phone Number", "Server Notifications": "Server Notifications", "For server reboots, unavailable servers, and available upgrades are included": "For server reboots, unavailable servers, and available upgrades are included", "Telegram": "Telegram", "WhatsApp": "WhatsApp", "Slack": "<PERSON><PERSON>ck", "Newly Provisioned Servers": "Newly Provisioned Servers", "Site Notifications": "Site Notifications", "For site upgrades, SSL certificate issues, and deployment errors": "For site upgrades, SSL certificate issues, and deployment errors", "Other Notifications": "Other Notifications", "Get notified about team accounts and actions": "Get notified about team accounts and actions", "Do Not Send Sensitive Information": "Do Not Send Sensitive Information", "This option will disable sending sensitive options like, sudo password, database password, wp-admin password over email and notification channels": "This option will disable sending sensitive options like, sudo password, database password, wp-admin password over email and notification channels", "Vulnerability Notifications": "Vulnerability Notifications", "Enable this option to receive notifications about vulnerabilities via Email": "Enable this option to receive notifications about vulnerabilities via Email", "Stay informed and take timely action to secure your systems": "Stay informed and take timely action to secure your systems", "Team Packages": "Team Packages", "Renewal type": "Renewal type", "Price Per Server": "Price Per Server", "Total Price": "Total Price", "Change Password": "Change Password", "Enter a strong password to keep your profile locked": "Enter a strong password to keep your profile locked", "Current Password": "Current Password", "New Password": "New Password", "Pay Bills": "Pay Bills", "Choose Your Payment Method": "Choose Your Payment Method", "You are paying for following invoice": "You are paying for following invoice", "Total Amount": "Total Amount", "Team Products": "Team Products", "Service Type": "Service Type", "Price": "Price", "Role Management": "Role Management", "Server Provider": "Server Provider", "List of Server Providers": "List of Server Providers", "Find all the server providers associated with your account here.": "Find all the server providers associated with your account here.", "Servers Count": "Servers Count", "Select Server Provider": "Select Server Provider", "Vultr API Key": "Vultr API Key", "API Key": "API Key", "Hetzner API Key": "Hetzner API Key", "Hetzner Label": "Hetzner Label", "Upload JSON": "Upload JSON", "Label for AWS Credential": "Label for AWS Credential", "AWS Access Key": "AWS Access Key", "Access Key": "Access Key", "AWS Secret Key": "AWS Secret Key", "Secret Key": "Secret Key", "Setup A Digital Ocean Server In xCloud": "Setup A Digital Ocean Server In xCloud", "Setup A Vultr Server In xCloud": "Setup A Vultr Server In xCloud", "Setup A GCP Server In xCloud": "Setup A GCP Server In xCloud", "SSH Keys": "SSH Keys", "Add New SSH Key": "Add New SSH Key", "ID": "ID", "Fingerprint": "Fingerprint", "Auto Provision": "Auto Provision", "Used By": "Used By", "A name to recognize this public key": "A name to recognize this public key", "Key Name": "Key Name", "Public Key": "Public Key", "Don't have a key?": "Don't have a key?", "Learn how to": "Learn how to", "generate an SSH Key": "generate an SSH Key", "Already have a key?": "Already have a key?", "Copy and paste your key here with": "Copy and paste your key here with", "Always provision to new servers": "Always provision to new servers", "Select servers to push this key to": "Select servers to push this key to", "Default Sudo User": "Default <PERSON>do User", "Default Sudo Password": "Default <PERSON> Password", "If you proceed, this will permanently remove this SSH Key and you will not be able to access or retrieve it again.": "If you proceed, this will permanently remove this SSH Key and you will not be able to access or retrieve it again.", "Following sudo users will be deleted": "Following sudo users will be deleted", "SSH key will be removed from the following sites": "SSH key will be removed from the following sites", "Storage Provider": "Storage Provider", "List of Storage Providers": "List of Storage Providers", "Bucket Name": "Bucket Name", "Region": "Region", "Site Count": "Site Count", "bucket": "bucket", "Enter the name of the bucket you have in your storage provider.": "Enter the name of the bucket you have in your storage provider.", "Select the data location": "Select the data location", "Select Region": "Select Region", "Enter the access key id here.": "Enter the access key id here.", "Access Key Id": "Access Key Id", "Enter the secret key here.": "Enter the secret key here.", "Endpoint": "Endpoint", "Enter the endpoint url here and make sure to add https:// in url.": "Enter the endpoint url here and make sure to add https:// in url.", "Site Backup": "Site Backup", "Enter the region here.": "Enter the region here.", "Team Invitations": "Team Invitations", "Team Management": "Team Management", "Add New Team": "Add New Team", "It looks like you haven’t created any team yet. Create one now.": "It looks like you haven’t created any team yet. Create one now.", "Profile": "Profile", "General Information": "General Information", "Set up your profile by providing the following information": "Set up your profile by providing the following information", "Contact Number": "Contact Number", "Extra Billing Information": "Extra Billing Information", "If you require the inclusion of specific contact or tax details on your receipts, such as your VAT identification number, or registered address, you may add it here.": "If you require the inclusion of specific contact or tax details on your receipts, such as your VAT identification number, or registered address, you may add it here.", "Name on Invoice": "Name on Invoice", "Billing Emails": "Billing Emails", "If": "If", "Send billing invoices only to the team email address": "Send billing invoices only to the team email address", "is unchecked, billing invoices will be sent to the your given email addresses, separated by commas. Example:": "is unchecked, billing invoices will be sent to the your given email addresses, separated by commas. Example:", "remaining.": "remaining.", "You have invited the following persons.": "You have invited the following persons.", "Note": "Note", "You can again enable this service.": "You can again enable this service.", "Execute": "Execute", "Proceeding will permanently delete all data stored on this server and all WordPress sites under it, which can’t be recovered later. Full Server Backups created for this server will also be deleted.": "Proceeding will permanently delete all data stored on this server and all WordPress sites under it, which can’t be recovered later. Full Server Backups created for this server will also be deleted.", "Delete From Provider": "Delete From Provider", "Enable this to delete this server from the provider also.": "Enable this to delete this server from the provider also.", "Type server name to confirm": "Type server name to confirm", "Delete DNS records from your Cloudflare account": "Delete DNS records from your Cloudflare account", "Your DNS records for the sites on this server will be deleted from your Cloudflare account.": "Your DNS records for the sites on this server will be deleted from your Cloudflare account.", "Add site": "Add site", "sites": "sites", "Storage": "Storage", "Ram": "Ram", "No sites": "No sites", "Add New Site": "Add New Site", "View Sites": "View Sites", "Restart Server": "Restart Server", "Hard Reboot Server": "Hard Reboot Server", "Restart Nginx": "<PERSON><PERSON>", "Restart LiteSpeed": "Restart Lite<PERSON>ed", "Restart MySQL": "Restart MySQL", "Restart MariaDB": "Restart <PERSON>", "Archive Server": "Archive Server", "Clone Server": "Clone Server", "Delete Server": "Delete Server", "View Server": "View Server", "Individual Site Details": "Individual Site Details", "Site Name": "Site Name", "CPU": "CPU", "RAM": "RAM", "DISK": "DISK", "LAST UPDATED": "LAST UPDATED", "EC2": "EC2", "Flexible, scalable virtual servers for all workloads.": "Flexible, scalable virtual servers for all workloads.", "Lightsail": "Lightsail", "Easy, budget-friendly servers for small projects.": "Easy, budget-friendly servers for small projects.", "Set Up Your Server With": "Set Up Your Server With", "Connect xCloud with your AWS Account": "Connect xCloud with your AWS Account", "Connect New Account": "Connect New Account", "Verified": "Verified", "Next": "Next", "Provider Label": "Provider Label", "Connected": "Connected", "Select AWS Service": "Select AWS Service", "Please choose an AWS service to create an instance.": "Please choose an AWS service to create an instance.", "Choose region": "Choose region", "Choose Region": "Choose Region", "Choose Zone": "Choose Zone", "Loading zones...": "Loading zones...", "Choose Server Size": "<PERSON><PERSON>", "Loading server types...": "Loading server types...", "Select Database Server": "Select Database Server", "Select Tags": "Select Tags", "I have understood that the billing of this server will be handled on my server provider account.": "I have understood that the billing of this server will be handled on my server provider account.", "Demo Server for Billing Plan": "Demo Server for Billing Plan", "Connect xCloud with your Digital Ocean account": "Connect xCloud with your Digital Ocean account", "Existing DigitalOcean Credential": "Existing DigitalOcean Credential", "Choose Credential": "<PERSON>ose Credential", "Add new credential": "Add new credential", "Authorize on Digital Ocean": "Authorize on Digital Ocean", "You do not have permission to add new provider": "You do not have permission to add new provider", "Enable Digital Ocean": "Enable Digital Ocean", "Auto Backups": "Auto Backups", "Connect xCloud with your Google Cloud Platform account": "Connect xCloud with your Google Cloud Platform account", "Select Existing or Connect New": "Select Existing or Connect New", "Finish": "Finish", "Choose your project": "Choose your project", "Choose Project": "Choose Project", "Connect xCloud with your Hetzner Account": "Connect xCloud with your <PERSON><PERSON>ner Account", "Choose Account": "<PERSON><PERSON> Account", "Hetzner API Token": "Hetzner API Token", "Connect xCloud with your Linode (Akamai) account": "Connect xCloud with your Linode (Akamai) account", "Existing Linode Credential": "Existing Linode Credential", "Authorize on Linode (Akamai)": "Authorize on Linode (Akamai)", "Connect xCloud with your Linode Account": "Connect xCloud with your Linode Account", "Vultr Label": "Vultr Label", "Connect xCloud with your Vultr Account": "Connect xCloud with your Vultr Account", "Enable Vultr": "<PERSON><PERSON>", "I am sure that I've read the": "I am sure that I've read the", "and added Any": "and added Any", "and Any": "and Any", "both under Access Control.": "both under Access Control.", "Fill in the details below to get your server set up with xCloud": "Fill in the details below to get your server set up with xCloud", "Note down the generated database root password above and secure it as it will not be displayed again.": "Note down the generated database root password above and secure it as it will not be displayed again.", "Server Details": "Server Details", "Server Tag(Optional)": "Server Tag(Optional)", "Server Type": "Server Type", "General": "General", "Cost-effective servers powered by Intel CPUs and regular SSDs.": "Cost-effective servers powered by Intel CPUs and regular SSDs.", "Premium": "Premium", "Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.": "Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.", "Billed Monthly": "Billed Monthly", "SSD": "SSD", "vCPU": "vCPU", "Bandwidth": "Bandwidth", "Recommended Option": "Recommended Option", "Backup": "Backup", "Enable xCloud Auto Backups": "Enable xCloud Auto Backups,", "Total Cost": "Total Cost", "Coupon Code": "Coupon Code", "Apply": "Apply", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks.": "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks.", "Disable Auto Backup": "Disable Auto Backup", "Choose Your Continent": "Choose Your Continent", "Enter Code Here...": "Enter Code Here...", "Fill in the details below to get your server set up with": "Fill in the details below to get your server set up with", "Connect Your Existing Server": "Connect Your Existing Server", "Fill in the details below to connect xCloud with your existing fresh Ubuntu server from any cloud provider.": "Fill in the details below to connect xCloud with your existing fresh Ubuntu server from any cloud provider.", "You will need to add the xCloud public key to allow server setup access. Please SSH to the server using the default/root account and use the following command to add the public key.": "You will need to add the xCloud public key to allow server setup access. Please SSH to the server using the default/root account and use the following command to add the public key.", "I have created a new server on my provider": "I have created a new server on my provider", "Choose Your Server Provider": "Choose Your Server Provider", "Choose Hosting Managed by": "Choose Hosting Managed by", "Everything You Need to Create a Website": "Everything You Need to Create a Website", "Bring and Manage Your Own Server": "Bring and Manage Your Own Server", "Integrate Your Own Provider.": "Integrate Your Own Provider.", "Manage": "Manage", "First Server": "First Server", "10 Sites": "10 Sites", "without any cost.": "without any cost.", "Create More, Pay Less upto": "Create More, Pay Less upto", "Start": "Start", "LTD Server Created": "LTD Server Created", "Create from package": "Create from package", "You’re on the xCloud Free plan, which includes 1 server and 10 websites with self-hosting. Activate your": "You’re on the xCloud Free plan, which includes 1 server and 10 websites with self-hosting. Activate your", "team by adding a": "team by adding a", "payment method": "payment method", "today.": "today.", "team by adding": "team by adding", "payment method.": "payment method.", "Choose Provider": "<PERSON><PERSON> Provider", "Only Available for LTD Users": "Only Available for LTD Users", "is pending for setup.": "is pending for setup.", "Automatic Reboot": "Automatic Reboot", "Enable Backup": "Enable Backup", "Disable Backup": "Disable Backup", "This setting is to enable/disable the backup settings of cloud provider.": "This setting is to enable/disable the backup settings of cloud provider.", "The charge will be": "The charge will be", "for this server.": "for this server.", "You will be charged based on the provider policy.": "You will be charged based on the provider policy.", "To prevent system abuse, the backup feature cannot be repeatedly turned on and off. Contact our support if you need any help.": "To prevent system abuse, the backup feature cannot be repeatedly turned on and off. Contact our support if you need any help.", "Next Backup Schedule": "Next Backup Schedule", "Select Backup Time (UTC)": "Select Backup Time (UTC)", "Backup List on Cloud Provider": "Backup List on Cloud Provider", "No backup found.": "No backup found.", "If you enable backup then you will be charged": "If you enable backup then you will be charged", "for this.": "for this.", "based on the provider policy.": "based on the provider policy.", "If you disable backup then you will not be able to restore the backup for the server. However, you can enable it anytime.": "If you disable backup then you will not be able to restore the backup for the server. However, you can enable it anytime.", "If you proceed, this will restore the backup for the server. This operation is irreversible.": "If you proceed, this will restore the backup for the server. This operation is irreversible.", "Do you want to set the backup schedule for the server": "Do you want to set the backup schedule for the server", "Backup Schedule Details": "Backup Schedule Details", "Some features may not work due to a payment issue.": "Some features may not work due to a payment issue.", "Payment failed": "Payment failed", "Please update your": "Please update your", "to retry billing.": "to retry billing.", "Note: Your billing period has been extended until": "Note: Your billing period has been extended until", "Please pay your outstanding invoices": "Please pay your outstanding invoices", "to avoid any service interruptions.": "to avoid any service interruptions.", "Add Cron Job": "<PERSON>d <PERSON>", "Command": "Command", "You can add wget and curl commands here. But if you want to add wp-cli commands then you need to specify the site location with cd /var/www/sitename && wp command.": "You can add wget and curl commands here. But if you want to add wp-cli commands then you need to specify the site location with cd /var/www/sitename && wp command.", "User": "User", "Frequency": "Frequency", "Custom Schedule": "Custom Schedule", "Save": "Save", "Add Sudo User": "Add Sudo User", "Sudo User Name": "Sudo User Name", "Sudo Password": "Sudo Password", "Add SSH Key": "Add SSH Key", "No SSH Keys are available. Please add SSH Key.": "No SSH Keys are available. Please add SSH Key.", "Cron Job": "<PERSON><PERSON>", "System Cron Jobs": "System Cron Jobs", "Scroll to end": "Scroll to end", "Scroll to top": "Scroll to top", "Add Supervisor Process": "Add Supervisor Process", "Edit Supervisor Process": "Edit Supervisor Process", "Update Supervisor Process": "Update Supervisor Process", "Supervisor Processes": "Supervisor Processes", "Check Process Status": "Check Process Status", "Supervisor": "Supervisor", "Process Management": "Process Management", "Background Processes": "Background Processes", "Daemon": "Daemon", "Supervisor Process Output": "Supervisor Process Output", "Processes": "Processes", "View Logs": "View Logs", "Restart": "<PERSON><PERSON>", "No supervisor processes found": "No supervisor processes found", "Delete Supervisor Process": "Delete Supervisor Process", "Are you sure you want to delete this supervisor process? This action cannot be undone.": "Are you sure you want to delete this supervisor process? This action cannot be undone.", "This will remove the supervisor process from the server.": "This will remove the supervisor process from the server.", "Yes, Remove!": "Yes, Remove!", "Inactive": "Inactive", "Running": "Running", "Stopped": "Stopped", "Fatal": "Fatal", "Backoff": "Backoff", "Starting": "Starting", "Stopping": "Stopping", "Exited": "Exited", "Unknown": "Unknown", "Are you sure?": "Are you sure?", "Processing": "Processing", "You can run any executable command here. For Python, Node.js, or PHP scripts, specify the full command (e.g., python3 script.py).": "You can run any executable command here. For Python, Node.js, or PHP scripts, specify the full command (e.g., python3 script.py).", "Optional. Specify the working directory for the process.": "Optional. Specify the working directory for the process.", "By default, you can use root. To run as a different user, enter the username here.": "By default, you can use root. To run as a different user, enter the username here.", "Number of Processes": "Number of Processes", "Number of process instances to keep running.": "Number of process instances to keep running.", "Start Seconds (Optional)": "Start Seconds (Optional)", "Supervisor will consider the process started after this many seconds.": "Supervisor will consider the process started after this many seconds.", "Stop Seconds (Optional)": "Stop Seconds (Optional)", "Supervisor will wait this many seconds before force stopping the process.": "Supervisor will wait this many seconds before force stopping the process.", "Stop Signal (Optional)": "Stop Signal (Optional)", "Supervisor sends this signal to stop the process. Leave empty for default (TERM).": "Supervisor sends this signal to stop the process. Leave empty for default (TERM).", "Manually Upload Website": "Manually Upload Website", "Upload a zipped file of your existing website (max size: 500 MB)": "Upload a zipped file of your existing website (max size: 500 MB)", "Last checked": "Last checked", "Add Database": "Add Database", "Loading...": "Loading...", "No associated site": "No associated site", "No database found!": "No database found!", "Database Users": "Database Users", "No database user found!": "No database user found!", "xcloud_db": "xcloud_db", "Database User Name": "Database User Name", "User (Optional)": "User (Optional)", "Password (Required with user)": "Password (Required with user)", "Add Database User": "Add Database User", "Can Access": "Can Access", "Keep empty to keep the same password": "Keep empty to keep the same password", "If you proceed, this is permanently removed and you will not be able to access or retrieve it again.": "If you proceed, this is permanently removed and you will not be able to access or retrieve it again.", "Update Cron Job": "Update <PERSON><PERSON> Job", "Update Sudo User": "Update Sudo User", "Reset Sudo Password": "Reset Sudo Password", "Add New Rule": "Add New Rule", "Protocol": "Protocol", "Traffic": "Traffic", "Active": "Active", "Fail2Ban Management": "Fail2Ban Management", "Ban New IP Address": "Ban New IP Address", "Banned IP Addresses": "Banned IP Addresses", "No banned IP addresses.": "No banned IP addresses.", "SSH": "SSH", "port": "port", "You can use multiple ports separated by comma. Leave empty to allow all ports. Also you can use a range of ports by using colon(i.e 6000:7000).": "You can use multiple ports separated by comma. Leave empty to allow all ports. Also you can use a range of ports by using colon(i.e 6000:7000).", "IP Address (Optional)": "IP Address (Optional)", "Valid IP address": "Valid IP address", "You can use multiple IP addresses separated by comma. Leave empty to allow all IP addresses. Also you can use a subnet. (i.e *************, *************, *************/24)": "You can use multiple IP addresses separated by comma. Leave empty to allow all IP addresses. Also you can use a subnet. (i.e *************, *************, *************/24)", "All": "All", "TCP": "TCP", "UDP": "UDP", "Allow": "Allow", "Deny": "<PERSON><PERSON>", "Adding Rule...": "Adding Rule...", "Add Rule": "Add Rule", "Enter the IP address you want to ban. You can use comma to separate multiple IP addresses (e.g xx.xx.xx.xx, xx.xx.xx.xx)": "Enter the IP address you want to ban. You can use comma to separate multiple IP addresses (e.g xx.xx.xx.xx, xx.xx.xx.xx)", "Banning IP...": "Banning IP...", "Ban IP": "Ban IP", "All Servers": "All Servers", "All Web Servers": "All Web Servers", "Nginx": "<PERSON><PERSON><PERSON>", "OpenLiteSpeed": "OpenLiteSpeed", "Provisioned": "Provisioned", "Provisioning": "Provisioning", "Upgrade Server": "Upgrade Server", "Server Name": "Server Name", "Choose Your Server Size": "Choose Your Server Size", "Be cautious when upgrading to a larger hosting plan since you can’t switch back. Also, it’s crucial to back up important data before trying to resize partitions, which can be risky.": "Be cautious when upgrading to a larger hosting plan since you can’t switch back. Also, it’s crucial to back up important data before trying to resize partitions, which can be risky.", "Can not perform server resize action on disconnected server.": "Can not perform server resize action on disconnected server.", "Current Status": "Current Status", "After Upgrading": "After Upgrading", "RAM Size": "RAM Size", "SSD Space": "SSD Space", "Server Utilities": "Server Utilities", "Delete your Server": "Delete your Server", "This action is irreversible, and will also remove the server from your provider, and all data will be erased. We recommend that you keep a backup before you proceed.": "This action is irreversible, and will also remove the server from your provider, and all data will be erased. We recommend that you keep a backup before you proceed.", "Warning: Before you upgrade": "Warning: Before you upgrade", "This will require a HARD restart and the process can take up to 15 min. It is recommended to take a full server backup of your server before proceeding.": "This will require a HARD restart and the process can take up to 15 min. It is recommended to take a full server backup of your server before proceeding.", "New Plan": "New Plan", "If you upgrade now, you’ll be charged": "If you upgrade now, you’ll be charged", "Provider Name": "Provider Name", "Ubuntu Version": "Ubuntu Version", "MySQL Version": "MySQL Version", "Full Server Migrations": "Full Server Migrations", "Initiate Full Server Migration": "Initiate Full Server Migration", "Public Ip": "Public Ip", "Updated": "Updated", "In Use": "In Use", "Available": "Available", "Utilization": "Utilization", "Cores": "Cores", "Speed/Core": "Speed/Core", "Threads": "Threads", "Hard Disk Usage": "Hard Disk Usage", "Total": "Total", "Used": "Used", "Uptime Overview": "Uptime Overview", "Select your site's PHP version to update php settings on the sites.": "Select your site's PHP version to update php settings on the sites.", "Max Execution Time": "Max Execution Time", "The maximum time in seconds a script is allowed to run before it is terminated by the parser. We recommend 60 seconds.": "The maximum time in seconds a script is allowed to run before it is terminated by the parser. We recommend 60 seconds.", "Max Input Time": "Max Input Time", "The maximum time in seconds a script is allowed to parse input data, like POST and GET. We recommend 60 seconds.": "The maximum time in seconds a script is allowed to parse input data, like POST and GET. We recommend 60 seconds.", "Max Input Vars": "Max Input Vars", "The maximum number of input variables allowed per request. We recommend 1000 vars.": "The maximum number of input variables allowed per request. We recommend 1000 vars.", "Memory Limit": "Memory Limit", "The maximum amount of memory a script may consume. We recommend 256MB.": "The maximum amount of memory a script may consume. We recommend 256MB.", "Post Max Size": "Post Max Si<PERSON>", "The maximum size of POST data that PHP will accept. We recommend 128MB.": "The maximum size of POST data that PHP will accept. We recommend 128MB.", "Max File Upload Size": "Max File Upload Size", "The maximum size of an uploaded file. We recommend 128MB.": "The maximum size of an uploaded file. We recommend 128MB.", "Session GC Maxlifetime": "Session GC Maxlifetime", "The maximum time in seconds a session is allowed to be idle before it is terminated by the session garbage collector. We recommend 1440 seconds.": "The maximum time in seconds a session is allowed to be idle before it is terminated by the session garbage collector. We recommend 1440 seconds.", "Important Note": "Important Note", "Enabling PHP OPCache will highly improve performance by storing precompiled scripts (PHP Code) in shared memory.": "Enabling PHP OPCache will highly improve performance by storing precompiled scripts (PHP Code) in shared memory.", "If you allow optimizing your OPCache, you need to make sure that your deployment parse code reloads the PHP FPM services at the end of each development.": "If you allow optimizing your OPCache, you need to make sure that your deployment parse code reloads the PHP FPM services at the end of each development.", "Disable OPCache": "Disable O<PERSON><PERSON>", "Enable OPCache": "Enable <PERSON><PERSON>", "Server Statistics": "Server Statistics", "Disable it to turn off magic login feature on all sites under this server.": "Disable it to turn off magic login feature on all sites under this server.", "Back To Servers": "Back To Servers", "Team": "Team", "WEB SERVER": "WEB SERVER", "Disk Usage": "Disk Usage", "Checked": "Checked", "Check Again": "Check Again", "Your disk space is low. Please upgrade your plan to avoid any downtime.": "Your disk space is low. Please upgrade your plan to avoid any downtime.", "Close": "Close", "No sites on this server": "No sites on this server", "Get started by creating a new site!": "Get started by creating a new site!", "Sudo Users": "Sudo Users", "Enable Vulnerability Scan": "Enable Vulnerability Scan", "Enable Auto Update": "Enable Auto Update", "If vulnerability found we will update in 24 hours": "If vulnerability found we will update in 24 hours", "Enable Auto Backup": "Enable Auto Backup", "Select All Sites": "Select All Sites", "Choose a Server to Clone Sites from": "Choose a Server to Clone Sites from", "Team Details": "Team Details", "Create a new team to collaborate with others on projects.": "Create a new team to collaborate with others on projects.", "If you proceed, this will remove all the services and sites along with other data which cannot be recovered.": "If you proceed, this will remove all the services and sites along with other data which cannot be recovered.", "I have understood and would like to proceed.": "I have understood and would like to proceed.", "Permanently delete this team.": "Permanently delete this team.", "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.": "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.", "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.": "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.", "Add Team Member": "Add Team Member", "Add a new team member to your team, allowing them to collaborate with you.": "Add a new team member to your team, allowing them to collaborate with you.", "Please provide the email address of the person you would like to add to this team.": "Please provide the email address of the person you would like to add to this team.", "Added.": "Added.", "Add": "Add", "Pending Team Invitations": "Pending Team Invitations", "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.": "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.", "All of the people that are part of this team.": "All of the people that are part of this team.", "Leave": "Leave", "Remove": "Remove", "Manage Role": "Manage Role", "Are you sure you would like to leave this team?": "Are you sure you would like to leave this team?", "Remove Team Member": "Remove Team Member", "Are you sure you would like to remove this person from the team?": "Are you sure you would like to remove this person from the team?", "The team's name and owner information.": "The team's name and owner information.", "Saved.": "Saved.", "Create Team": "Create Team", "Team Settings": "Team Settings", "Join the Exclusive Waitlist!": "Join the Exclusive Waitlist!", "Your Email Address..": "Your Email Address..", "Join Waitlist": "Join <PERSON>", "Already have been invited?": "Already have been invited?", "Login to xCloud": "Login to xCloud", "When you cancel migration, already migrated data will be removed from the server.": "When you cancel migration, already migrated data will be removed from the server.", "Cancel Migration": "Cancel Migration", "Auth": "<PERSON><PERSON>", "Cache": "<PERSON><PERSON>", "Git": "Git", "Proceeding will permanently delete this Site and all of its data.": "Proceeding will permanently delete this Site and all of its data.", "Delete All Files and Configurations.": "Delete All Files and Configurations.", "Do You Want To Delete Files?": "Do You Want To Delete Files?", "Delete Database": "Delete Database", "Do You Want To Delete Database?": "Do You Want To Delete Database?", "Delete Site User": "Delete Site User", "Do You Want To Delete User?": "Do You Want To Delete User?", "Delete Local Backups": "Delete Local Backups", "Do You Want To Delete Local Backups?": "Do You Want To Delete Local Backups?", "Type site name to confirm": "Type site name to confirm", "Delete DNS record from your Cloudflare account": "Delete DNS record from your Cloudflare account", "Your DNS record for the site on this server will be deleted from your Cloudflare account.": "Your DNS record for the site on this server will be deleted from your Cloudflare account.", "Proceeding will disable this Site.": "Proceeding will disable this Site.", "Disable Site": "Disable Site", "Also Disable Cron For This Site": "Also Disable Cron For This Site", "Disable HTML Markup": "Disable HTML Markup", "After turning on Disable Site, you won’t be able to use this site on the web anymore along with losing access through SFTP/SSH. Plus, all the scheduled tasks for the site will be paused once you turn on Disable Cron For This Site.": "After turning on Disable Site, you won’t be able to use this site on the web anymore along with losing access through SFTP/SSH. Plus, all the scheduled tasks for the site will be paused once you turn on Disable Cron For This Site.", "Disable": "Disable", "Site is currently disabled.": "Site is currently disabled.", "Click here": "Click here", "to enable this site": "to enable this site", "The maximum amount of memory a script may consume. we recommend 128MB.": "The maximum amount of memory a script may consume. we recommend 128MB.", "The number of seconds after which data will be seen as 'garbage' and potentially cleaned up. we recommend 1440 seconds.": "The number of seconds after which data will be seen as 'garbage' and potentially cleaned up. we recommend 1440 seconds.", "Max PHP Workers": "Max PHP Workers", "The number of PHP workers (pm.max_children) that can be spawned to handle requests.": "The number of PHP workers (pm.max_children) that can be spawned to handle requests.", "Redirect Type": "Redirect Type", "Choose Redirect Type": "Choose Redirect Type", "From": "From", "To": "To", "View": "View", "Magic Login": "Magic Login", "Site Logs": "Site Logs", "Site Events": "Site Events", "Purge Cache": "<PERSON><PERSON>", "Clone Site": "Clone Site", "Delete Site": "Delete Site", "Create New Database": "Create New Database", "Database Cluster Name": "Database Cluster Name", "Cluster Name": "Cluster Name", "Select Existing Database Cluster": "Select Existing Database Cluster", "Additional Domain Name": "Additional Domain Name", "Additional Domain (Optional)": "Additional Domain (Optional)", "Add Domain": "Add Domain", "Admin Password": "Admin Password", "Admin Email Address": "Admin Email Address", "WordPress Version": "WordPress Version", "Prefix": "Prefix", "wp_": "wp_", "Full Page Caching": "Full Page Caching", "Redis Object Caching": "Redis Object Caching", "Could not connect to the database server. Please check your database credentials and try again.": "Could not connect to the database server. Please check your database credentials and try again.", "Database Username": "Database Username", "Database Host": "Database Host", "Database Port": "Database Port", "Connection URL": "Connection URL", "Please use the database connection URL provided to establish a connection between your database client and the database. We suggest using TablePlus as your database client.": "Please use the database connection URL provided to establish a connection between your database client and the database. We suggest using TablePlus as your database client.", "Add the following record to the DNS for your domain. This required when when you want to get a free SSL certificate issued & managed by": "Add the following record to the DNS for your domain. This required when when you want to get a free SSL certificate issued & managed by", "If you have manually added DNS record with Cloudflare proxy then the verify option will not work": "If you have manually added DNS record with Cloudflare proxy then the verify option will not work", "Record": "Record", "A": "A", "Verify My DNS": "Verify My DNS", "In your domain settings, you need to add the following records for configuring your site email.": "In your domain settings, you need to add the following records for configuring your site email.", "Record(SPF)": "Record(SPF)", "TXT": "TXT", "Value": "Value", "CNAME": "CNAME", "Record(DKIM)": "Record(DKIM)", "Record(DMARC)": "Record(DMARC)", "Verify Record": "Verify Record", "Use free SSL certificate issued & managed by": "Use free SSL certificate issued & managed by", "I will provide the certificate and manage it myself.": "I will provide the certificate and manage it myself.", "Certificate": "Certificate", "Paste Your Certificate Here": "Paste Your Certificate Here", "Private Key": "Private Key", "Paste Your Private Key Here": "Paste Your Private Key Here", "Enabling HTTPS makes your website more secure.": "Enabling HTTPS makes your website more secure.", "Learn more": "Learn more", "Beta": "Beta", "Install New WordPress Website": "Install New WordPress Website", "Select this option if you want to a create a fresh new WordPress website": "Select this option if you want to a create a fresh new WordPress website", "Clone a Git Repository": "Clone a Git Repository", "Clone a git repository to create a new website": "Clone a git repository to create a new website", "Migrate An Existing WordPress Website": "Migrate An Existing WordPress Website", "Have an existing website already? Select this option to migrate it with ": "Have an existing website already? Select this option to migrate it with ", "Upload a zipped file of your existing website": "Upload a zipped file of your existing website", "Migrate Full Server": "Migrate Full Server", "Migrate all WordPress sites from Ubuntu servers with a few clicks": "Migrate all WordPress sites from Ubuntu servers with a few clicks", "Certificate Issue": "Certificate Issue", "Expires On": "Expires On", "Renew Date": "Renew Date", "Add Your New Site Into": "Add Your New Site Into", "This server created under xCloud Free plan which includes 1 server and": "This server created under xCloud Free plan which includes 1 server and", "website with Self Hosting. To unlock full features, please consider upgrading the server bill from free to paid plan.": "website with Self Hosting. To unlock full features, please consider upgrading the server bill from free to paid plan.", "Your": "Your", "is low. Please upgrade your plan to avoid any downtime.": "is low. Please upgrade your plan to avoid any downtime.", "Choose a Server to add Site": "Choose a Server to add Site", "Site Title": "Site Title", "New Site Title": "New Site Title", "Add Tag (optional)": "Add Tag (optional)", "Go Live": "Go Live", "(Optional)": "(Optional)", "Integrate Cloudflare forAutomatic DNS and SSL management.": "Integrate Cloudflare forAutomatic DNS and SSL management.", "If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.": "If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.", "WordPress Multisite": "WordPress Multisite", "Enable Multisite": "Enable Multisite", "Select Multisite Type": "Select Multisite Type", "You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.": "You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.", "Enhancing Website Performance": "Enhancing Website Performance", "Speed up your website by using smart caching!": "Speed up your website by using smart caching!", "Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "Combine Full Page and Redis to make your site work faster and give your visitors a better experience.", "Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.": "Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.", "LiteSpeed Cache": "LiteSpeed Cache", "Email Provider Configuration": "Email Provider Configuration", "Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.": "Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.", "Read our documentation": "Read our documentation", "Blueprints": "Blueprints", "Choose a blueprint to install WordPress with pre-configured plugins and themes.": "Choose a blueprint to install WordPress with pre-configured plugins and themes.", "All Blueprints": "All Blueprints", "Manage your all blueprints as you like, you can edit, delete or create new from here": "Manage your all blueprints as you like, you can edit, delete or create new from here", "OK": "OK", "Create Your New Site Into": "Create Your New Site Into", "xCloud Playground": "xCloud Playground", "Playground Environment": "Playground Environment", "This demo site will expire 24 hours after creation.": "This demo site will expire 24 hours after creation.", "This will be auto-generated according to your site title": "This will be auto-generated according to your site title", "Speed up your website by using smart caching! Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "Speed up your website by using smart caching! Combine Full Page and Redis to make your site work faster and give your visitors a better experience.", "Step 1 of 3": "Step 1 of 3", "Hey you are trying to access the playground site": "Hey you are trying to access the playground site", "but you are not in the": "but you are not in the", "We request you to check your email and accept the invitation to join and edit the site.": "We request you to check your email and accept the invitation to join and edit the site.", "Enable Adminer": "Enable Adminer", "Adminer can enhance your database management experience. Activate it only when necessary to ensure optimal security measures.": "Adminer can enhance your database management experience. Activate it only when necessary to ensure optimal security measures.", "Database Manager": "Database Manager", "Manage your databases with Adminer, an opensource database management tool.": "Manage your databases with Adminer, an opensource database management tool.", "Launch Adminer": "Launch Adminer", "Basic Authentication": "Basic Authentication", "Protect Your Site": "Protect Your Site", "Turn on to enable basic authentication for your site by adding username and password.": "Turn on to enable basic authentication for your site by adding username and password.", "Previous Remote Backups": "Previous Remote Backups", "Backup Now": "Backup Now", "File": "File", "Previous Local Backups": "Previous Local Backups", "Remote Backup Settings": "Remote Backup Settings", "Backup Type": "Backup Type", "Select Bucket": "Select Bucket", "Server Bucket": "Server Bucket", "Manage Your Storage Providers": "Manage Your Storage Providers", "Backup Items": "Backup Items", "Database Backup": "Database Backup", "will be backed up.": "will be backed up.", "Files Backup": "Files Backup", "Exclude Paths": "Exclude Paths", "Automatic Backup": "Automatic Backup", "Automatic Full Backup": "Automatic Full Backup", "Select Backup Frequency": "Select Backup Frequency", "Select Full Backup Frequency": "Select Full Backup Frequency", "Automatic Incremental Backup": "Automatic Incremental Backup", "Automatic Delete": "Automatic Delete", "Delete After Days": "Delete After Days", "30": "30", "Local Backup Settings": "Local Backup Settings", "Select Incremental Backup Frequency": "Select Incremental Backup Frequency", "Change": "Change", "Additional Domains": "Additional Domains", "Add new": "Add new", "After changing the domain, you need to install SSL for Multisite Subdomain from the site SSL/HTTPS page.": "After changing the domain, you need to install SSL for Multisite Subdomain from the site SSL/HTTPS page.", "Connect New Provider": "Connect New Provider", "Use xCloud Domain": "Use xCloud Domain", "Use Your Own Domain": "Use Your Own Domain", "Requires Verification": "Requires Verification", "From Name": "From Name", "This is the name that will appear in the recipient's inbox.": "This is the name that will appear in the recipient's inbox.", "To use your own domain, please verify your domain with xCloud by following the DNS setup below.": "To use your own domain, please verify your domain with xCloud by following the DNS setup below.", "Clear Provider": "Clear Provider", "We only use the Fluent SMTP plugin to configure your given SMTP credentials on the sites, ": "We only use the Fluent SMTP plugin to configure your given SMTP credentials on the sites, ", "so that you do not have to do anything manually. If you are not getting emails then please ": "so that you do not have to do anything manually. If you are not getting emails then please ", "check your email logs from your email provider. To test your email integration, please send a test email from your fluentsmtp plugin.": "check your email logs from your email provider. To test your email integration, please send a test email from your fluentsmtp plugin.", "Enable File Manager": "Enable File Manager", "Tiny File Manager": "Tiny File Manager", "Activate it only when necessary to ensure optimal security measures.": "Activate it only when necessary to ensure optimal security measures.", "File Manager": "File Manager", "Manage your files, an opensource tool.": "Manage your files, an opensource tool.", "Launch File Manager": "Launch File Manager", "Page Caching": "Page Caching", "FastCGI Nginx": "FastCGI Nginx", "Cache Duration": "<PERSON><PERSON>", "Unit": "Unit", "Cache Exclusion HTTP URL Rules": "Cache Exclusion HTTP URL Rules", "Cache Exclusion Cookie Rules": "<PERSON><PERSON> Exclusion Cookie Rules", "Clear Page Cache": "Clear Page Cache", "This will slow down your site until the caches are rebuilt.": "This will slow down your site until the caches are rebuilt.", "Purging Cache...": "Purging <PERSON>...", "Action will be available once the Object Cache operation is finished": "Action will be available once the Object Cache operation is finished", "Object Cache": "O<PERSON>", "Redis User": "Redis User", "Redis Password": "Redis Password", "Redis Object Cache Key": "Redis Object Cache Key", "Redis object cache optimize performance, reduces database load, and enhances response times for a seamless browsing experience.": "Redis object cache optimize performance, reduces database load, and enhances response times for a seamless browsing experience.", "Stores the results of queries to the site’s database.": "Stores the results of queries to the site’s database.", "Clear Object Cache": "Clear Object Cache", "Git Settings": "<PERSON><PERSON>", "Pull and deploy now": "Pull and deploy now", "Updated At": "Updated At", "Production": "Production", "Staging": "Staging", "Demo": "Demo", "Vulnerable": "Vulnerable", "Migrating": "Migrating", "Add IP Address": "Add IP Address", "No IP addresses added yet.": "No IP addresses added yet.", "IP Addresses": "IP Addresses", "Updating...": "Updating...", "Adding...": "Adding...", "LiteSpeed Cache for WordPress (LSCWP) is an all-in-one site acceleration plugin, featuring an exclusive server-level cache and a collection of optimization features.": "LiteSpeed Cache for WordPress (LSCWP) is an all-in-one site acceleration plugin, featuring an exclusive server-level cache and a collection of optimization features.", "Clear LiteSpeed Cache": "Clear LiteSpeed Cache", "Showing logs from": "Showing logs from", "Loading..": "Loading..", "WordPress Debug Log": "WordPress Debug Log", "Reload": "Reload", "Clear": "Clear", "Clearing..": "Clearing..", "Your Site Has Been Successfully Migrated": "Your Site Has Been Successfully Migrated", "Staging URL": "Staging URL", "Setup Email for this site": "Setup Email for this site", "Monitoring Stats": "Monitoring Stats", "CPU Usages": "CPU Usages", "SSL Overview": "SSL Overview", "DNS Status": "DNS Status", "SSL Status": "SSL Status", "Expiry": "Expiry", "WordPress Logs": "WordPress Logs", "Updates": "Updates", "Update Available": "Update Available", "Up To Date": "Up To Date", "Plugins": "Plugins", "Themes": "Themes", "Custom Nginx Config": "Custom Nginx Config", "Fetching Nginx..": "Fetching Nginx..", "Preview Nginx": "Preview Nginx", "Add a New Config": "Add a New Config", "Select Config Type": "Select Config Type", "Config File Name": "Config File Name", "Config Content": "Config Content", "Preview Content": "Preview Content", "Running...": "Running...", "Run & Debug": "Run & Debug", "Nginx Config File": "Nginx Config File", "Save Settings": "Save Settings", "Nginx & Security": "Nginx & Security", "Security": "Security", "7G Firewall": "7G Firewall", "8G Firewall": "8G Firewall", "Disable Nginx File Regeneration": "Disable Nginx File Regeneration", "PHP Execution on Upload Directory": "PHP Execution on Upload Directory", "Enable XML-RPC": "Enable XML-RPC", "Edit X-Frame-Options": "Edit X-Frame-Options", "Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com": "Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com", "With xCloud you get 100 emails free per month in each team. Manage email for this site from": "With xCloud you get 100 emails free per month in each team. Manage email for this site from", "You can purchase more or add your own email providers from": "You can purchase more or add your own email providers from", "Learn more from our": "Learn more from our", "Primary Domain": "Primary Domain", "Add New": "Add New", "Others": "Others", "Page Cache": "<PERSON>", "on": "on", "off": "off", "SSL/HTTPS": "SSL/HTTPS", "Basic Auth": "Basic Auth", "No Updates": "No Updates", "Most Recent Event": "Most Recent Event", "View All Events": "View All Events", "Redirection": "Redirection", "Add Redirection": "Add Redirection", "Redirections": "Redirections", "Edit Redirection": "Edit Redirection", "WP-Cron": "WP-Cron", "WP-Cron manages time-based tasks in WordPress, relying on site visits.": "WP-Cron manages time-based tasks in WordPress, relying on site visits.", "If you want to also add additional custom cronjob then you can configure on your server from": "If you want to also add additional custom cronjob then you can configure on your server from", "Edit Site Tags": "Edit Site Tags", "WP Debug": "WP Debug", "Enable WP Debug": "Enable WP Debug", "Enable this to view WordPress debug logs on the": "Enable this to view WordPress debug logs on the", "site's logs page": "site's logs page", "Delete Site Confirmation": "Delete Site Confirmation", "This action is irreversible. Delete sites cannot be restored.": "This action is irreversible. Delete sites cannot be restored.", "Rescue Site": "Rescue Site", "Run Now": "Run Now", "Repair Site User": "Repair Site User", "Update Directory Permissions": "Update Directory Permissions", "Repair PHP": "Repair PHP", "Regenerate": "Regenerate", "It is recommended to keep the above options turned on before running the rescue action.": "It is recommended to keep the above options turned on before running the rescue action.", "Back To Sites": "Back To Sites", "staging": "staging", "Add Staging environment": "Add Staging environment", "Visit Site": "Visit Site", "Deploy Staging": "Deploy Staging", "WordPress": "WordPress", "Archive": "Archive", "Are You Sure You Want To Deploy Staging for  ": "Are You Sure You Want To Deploy Staging for  ", "A staging site is an identical copy of your production website, created for testing purposes. This isolated environment lets you test any plugin, or theme, or make any other changes before going live. This removes the risk of damaging your production website. Later, you can pull/push updates and apply to your production.": "A staging site is an identical copy of your production website, created for testing purposes. This isolated environment lets you test any plugin, or theme, or make any other changes before going live. This removes the risk of damaging your production website. Later, you can pull/push updates and apply to your production.", "Staging Site Domain": "Staging Site Domain", "Preparing Deployment..": "Preparing Deployment..", "Site SSH/sFTP Access": "Site SSH/sFTP Access", "Site Username": "Site Username", "Site Path": "Site Path", "SSH String": "SSH String", "Database URL Connection": "Database URL Connection", "DNS Setup For Multisite SSL": "DNS Setup For Multisite SSL", "Provide your own certificate & manage it yourself": "Provide your own certificate & manage it yourself", "Use Cloudflare managed SSL Certificate.": "Use Cloudflare managed SSL Certificate.", "Demo Environment": "Demo Environment", "Staging Environment": "Staging Environment", "Demo Site Domain": "Demo Site Domain", "Staging Domain": "Staging Domain", "If you’re using different additional domains then you need to switch to xCloud SSL. With Cloudflare this is not supported.": "If you’re using different additional domains then you need to switch to xCloud SSL. With Cloudflare this is not supported.", "Staging Management": "Staging Management", "Pull Data from Production to Staging": "Pull Data from Production to Staging", "Copy the changes from the live site to staging": "Copy the changes from the live site to staging", "Pulling Data...": "Pulling Data...", "Pull Data": "Pull Data", "Push Data from Staging to Production": "Push Data from Staging to Production", "Copy the changes from the staging to live": "<PERSON>py the changes from the staging to live", "Pushing Data...": "Pushing Data...", "Push Data": "<PERSON><PERSON> Data", "Deployment Logs": "Deployment Logs", "View All Logs": "View All Logs", "Pull data from ": "Pull data from ", "Files": "Files", "Overwrite": "Overwrite", "Incremental": "Incremental", "Full": "Full", "Selected Tables": "Selected Tables", "fetching tables...": "fetching tables...", "Fetching tables...": "Fetching tables...", "Initiating Pulling...": "Initiating Pulling...", "Push data from this Staging Site to the Production site(": "Push data from this Staging Site to the Production site(", "Push to Production": "Push to Production", "Your staging site data will be pushed to production. The production site may be temporarily unavailable for a while.": "Your staging site data will be pushed to production. The production site may be temporarily unavailable for a while.", "Select Tables": "Select Tables", "Take backup of your production site before pushing changes(Recommended).": "Take backup of your production site before pushing changes(Recommended).,", "Initiating Pushing...": "Initiating Pushing...", "Source Site": "Source Site", "Destination Site": "Destination Site", "Initiated By": "Initiated By", "WordPress Core": "WordPress Core", "Your current version is": "Your current version is", "Updating to": "Updating to", "Your WordPress": "Your WordPress", "is up to date.": "is up to date.", "Version": "Version", "Changelog": "Changelog", "Activating...": "Activating...", "Activate": "Activate", "Activated": "Activated", "Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or": "Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or", "click here": "click here", "Insecure": "Insecure", "Secure": "Secure", "Last Scan": "Last Scan", "An Updated Version of WordPress is Available": "An Updated Version of WordPress is Available", "CVSS Score": "CVSS Score", "Ignored": "Ignored", "Ignore": "Ignore", "Patched": "Patched", "Fixed in": "Fixed in", "Remediation": "Remediation", "Direct URL": "Direct URL", "First Published": "First Published", "Last Update": "Last Update", "Update Config": "Update Config", "WP Config": "WP Config", "Invoices of Clients": "Invoices of Clients", "Here you can check all your previous invoices": "Here you can check all your previous invoices", "Copy": "Copy", "View Nova": "View Nova", "Total Clients": "Total Clients", "Total Products": "Total Products", "Total Servers": "Total Servers", "You don’t have any server yet": "You don’t have any server yet", "Sites": "Sites", "You don’t have any site yet": "You don’t have any site yet", "Clients Overview": "Clients Overview", "Total Active Clients": "Total Active Clients", "Total Inactive Clients": "Total Inactive Clients", "Total Invoices": "Total Invoices", "You don’t have any client yet": "You don’t have any client yet", "Access Dashboard & Website": "Access Dashboard & Website", "Domain Settings": "Domain Settings", "Visit Landing Page": "Visit Landing Page", "Dashboard": "Dashboard", "Brand Setup": "Brand Setup", "Brand Profile": "Brand Profile", "Add Your Logo": "Add Your Logo", "Upload a logo that represent your brand profile.": "Upload a logo that represent your brand profile.", "Please upload a logo with a minimum size of 212x40 pixels in PNG, JPEG, or JPG format (max 5MB) for best quality.": "Please upload a logo with a minimum size of 212x40 pixels in PNG, JPEG, or JPG format (max 5MB) for best quality.", "Upload Logo": "Upload Logo", "Cloud Hosting": "Cloud Hosting", "Brand Name": "Brand Name", "<EMAIL>": "<EMAIL>", "Support Email": "Support Email", "579 Spruce Court, Dallas, TX 75201": "579 Spruce Court, Dallas, TX 75201", "Address": "Address", "Copyright Name": "Copyright Name", "Processing...": "Processing...", "Proceed to Checkout": "Proceed to Checkout", "Sell up to": "Sell up to", "Checkout": "Checkout", "Your Order Summary": "Your Order Summary", "Sub Total": "Sub Total", "Processing Payment...": "Processing Payment...", "Processing Offer...": "Processing Offer...", "Claim Free": "<PERSON><PERSON><PERSON>", "Create Product": "Create Product", "Create Hosting Plan": "Create Hosting Plan", "Plan Name": "Plan Name", "Renewal Type": "Renewal Type", "SKU": "SKU", "Setup Products & Start Selling": "Setup Products & Start Selling", "Add Your Domain Name": "Add Your Domain Name", "example.com": "example.com", "Add the following record to the DNS manager for your domain": "Add the following record to the DNS manager for your domain", "Skip this step, if you are not ready to point your domain server.": "Skip this step, if you are not ready to point your domain server.", "Payment Setup": "Payment Setup", "Integrate with Stripe Connect": "Integrate with Stripe Connect", "Connect to your existing Stripe account or create a new account to start processing payments via Stripe.": "Connect to your existing Stripe account or create a new account to start processing payments via Stripe.", "Connect Now": "Connect Now", "Stripe Account": "Stripe Account", "If your Stripe account is set up correctly, it will automatically connect within 1–2 minutes.": "If your Stripe account is set up correctly, it will automatically connect within 1–2 minutes.", "If you’ve just created a new Stripe business account, it may take up to 3 days for Stripe to verify it.Once verified, your account should connect automatically.": "If you’ve just created a new Stripe business account, it may take up to 3 days for <PERSON><PERSON> to verify it.Once verified, your account should connect automatically.", "To check the status of your": "To check the status of your", "account": "account", "you can visit your": "you can visit your", "Stripe dashboard.": "Stripe dashboard.", "Billing Account": "Billing Account", "Stripe Account Name": "Stripe Account Name", "Stripe Account ID": "Stripe Account ID", "Connected On": "Connected On", "Billing Currency": "Billing <PERSON><PERSON><PERSON>cy", "3D Secure Card Compatibility for Your Clients (Optional)": "3D Secure Card Compatibility for Your Clients (Optional)", "Stripe Publishable Key": "Stripe Publishable Key", "If your client prefers to pay using a 3D Secure card, you need to add your": "If your client prefers to pay using a 3D Secure card, you need to add your", "Stripe Publishable Key.": "Stripe Publishable Key.", "Get your publishable key": "Get your publishable key", "from here.": "from here.", "Syncing Account..": "Syncing Account..", "Having Trouble?": "Having Trouble?", "Read our comprehensive documentation & learn to manage your hosting easily.": "Read our comprehensive documentation & learn to manage your hosting easily.", "Read Documentation": "Read Documentation", "Start Your Hosting Business: Resell & Earn Revenue": "Start Your Hosting Business: Resell & Earn Revenue", "Launch a cloud hosting business with xCloud Managed Servers. Resell our fully managed web hosting under your own brand or domain to maximize your revenue ensuring a reliable performance.": "Launch a cloud hosting business with xCloud Managed Servers. Resell our fully managed web hosting under your own brand or domain to maximize your revenue ensuring a reliable performance.", "Complete control for your personal branding": "Complete control for your personal branding", "Manage your client billings with Stripe Connect": "Manage your client billings with Stripe Connect", "Customize hosting packages & sell at your own price": "Customize hosting packages & sell at your own price", "Get access to powerful features of xCloud": "Get access to powerful features of xCloud", "By ticking this box, you are confirming that you have read, understood, and agree to our": "By ticking this box, you are confirming that you have read, understood, and agree to our", "Please check this box to confirm that you accept the terms and conditions and want to proceed.": "Please check this box to confirm that you accept the terms and conditions and want to proceed.", "Before proceeding, ensure you have an active Stripe account, as all transactions are managed via": "Before proceeding, ensure you have an active Stripe account, as all transactions are managed via", "Stripe Connect": "Stripe Connect", "Please check this box to confirm that you have a Stripe account and want to proceed.": "Please check this box to confirm that you have a Stripe account and want to proceed.", "Start Your Hosting Business Now": "Start Your Hosting Business Now", "Product Details": "Product Details", "Product Information": "Product Information", "Some basic information is shared over here": "Some basic information is shared over here", "Edit Product": "Edit Product", "Checkout URL": "Checkout URL", "Invoices of Product": "Invoices of Product", "Source Product": "Source Product", "Includes Up to RAM": "Includes Up to RAM", "Customize Package": "Customize Package", "My Server": "My Server", "Price/Month": "Price/Month", "XCPU110": "XCPU110", "Stripe": "Stripe", "will deduct a 3%-7% fee per sale. Your approximate profit for this sale is": "will deduct a 3%-7% fee per sale. Your approximate profit for this sale is", "Active Package": "Active Package", "Preview Plan": "Preview Plan", "Save and Publish": "Save and Publish", "Add Product": "Add Product", "Buying Price": "Buying Price", "Selling Price": "Selling <PERSON>", "Create New Product": "Create New Product", "Select Server Plan at xCloud": "Select Server Plan at xCloud", "Select Server Size": "Select Server Size", "Change Plan": "Change Plan", "Duplicate Product": "Duplicate Product", "Duplicate": "Duplicate", "Favicon": "Favicon", "Add Your Favicon": "Add Your Favicon", "Please upload a favicon with a minimum size of 16x16 pixels in PNG or ICO format (max 1MB) for best quality.": "Please upload a favicon with a minimum size of 16x16 pixels in PNG or ICO format (max 1MB) for best quality.", "Upload Favicon": "Upload Favicon", "Custom Domain Setup": "Custom Domain Setup", "Your Domain Name": "Your Domain Name", "Landing Page Settings": "<PERSON> Page Settings", "Landing Page": "<PERSON>", "Enable it to use and customize the ready landing page settings": "Enable it to use and customize the ready landing page settings", "Navbar Logo": "<PERSON><PERSON><PERSON>", "Update your logo for navigation bar": "Update your logo for navigation bar", "Upload a logo or icon that represent your brand profile.": "Upload a logo or icon that represent your brand profile.", "Hero Section": "Hero Section", "Fast, Secure & Reliable Cloud Hosting": "Fast, Secure & Reliable Cloud Hosting", "Heading": "Heading", "Some more information that uou can add here": "Some more information that <PERSON><PERSON> can add here", "Sub Heading": "Sub Heading", "Create Now": "Create Now", "Button Text": "Button Text", "Button URL": "Button URL", "https://www.example.com": "https://www.example.com", "CTA Section": "CTA Section", "Social Media Links": "Social Media Links", "https://facebook.com/xcloud": "https://facebook.com/xcloud", "Facebook": "Facebook", "https://instagram.com/xcloud": "https://instagram.com/xcloud", "Instagram": "Instagram", "https://x.com/xcloud": "https://x.com/xcloud", "X.com": "X.com", "https://linkedin.com/xcloud": "https://linkedin.com/xcloud", "Linkedin": "Linkedin", "https://youtube.com/xcloud": "https://youtube.com/xcloud", "Youtube": "Youtube", "Preview": "Preview", "Saving...": "Saving...", "Connect to Stripe": "Connect to Stripe", "Account Name": "Account Name", "Stripe 3DS Card Setup": "Stripe 3DS Card Setup", "Privacy Policy Settings": "Privacy Policy Settings", "Default Privacy Policy": "Default Privacy Policy", "Use your own Privacy Policy": "Use your own Privacy Policy", "SMTP Settings": "SMTP Settings", "Use Custom SMTP": "Use Custom SMTP", "Use my custom smtp credentials for email sending.": "Use my custom smtp credentials for email sending.", "SMTP Credentials": "SMTP Credentials", "smtp.mailgun.org": "smtp.mailgun.org", "Host": "Host", "587": "587", "SMTP username": "SMTP username", "Username": "Username", "Encryption": "Encryption", "ssl": "ssl", "Select Encryption": "Select Encryption", "Terms & Services Settings": "Terms & Services Settings", "Default TOS": "Default TOS", "Use your own TOS": "Use your own TOS", "Use your own TOS URL": "Use your own TOS URL", "https://example.com/tos": "https://example.com/tos", "Enter the URL of your own Terms & Services page and make sure to add https:// in url": "Enter the URL of your own Terms & Services page and make sure to add https:// in url", "Use your own Privacy Policy URL": "Use your own Privacy Policy URL", "https://example.com/privacy-policy": "https://example.com/privacy-policy", "Enter the URL of your own Privacy Policy page and make sure to add https:// in url": "Enter the URL of your own Privacy Policy page and make sure to add https:// in url", "All Clients": "All Clients", "Client Name": "Client Name", "Press / to search": "Press / to search", "You don’t have any Product yet": "You don’t have any Product yet", "Client Details": "Client Details", "Client Information": "Client Information", "Edit Information": "Edit Information", "Billing Address": "Billing Address", "Account Status": "Account Status", "Payment Information": "Payment Information", "Here you can find all cards added by your clients": "Here you can find all cards added by your clients", "No payment methods found.": "No payment methods found.", "Invoices": "Invoices", "Here you can check all your client's previous payments": "Here you can check all your client's previous payments", "Edit Client Information": "Edit Client Information", "Experience Effortless Hosting With Powerful Features": "Experience Effortless Hosting With Powerful Features", "Enjoy the lightning performance backed by powerful features and experience hassle-free hosting": "Enjoy the lightning performance backed by powerful features and experience hassle-free hosting", "Effortless Server Operations": "Effortless Server Operations", "Manage your server effortlessly with our intuitive dashboard. Enjoy high performance and reliability without worrying about downtime, regardless of your technical skill level.": "Manage your server effortlessly with our intuitive dashboard. Enjoy high performance and reliability without worrying about downtime, regardless of your technical skill level.", "Easy Website Management": "Easy Website Management", "We offer a comprehensive platform for managing WordPress sites with ease. With our intuitive dashboard and advanced features, you can manage your sites without any hassle.": "We offer a comprehensive platform for managing WordPress sites with ease. With our intuitive dashboard and advanced features, you can manage your sites without any hassle.", "Powerful Security Measures": "Powerful Security Measures", "Our advanced security features ensure the safety of your data through advanced protective measures. Also, automated regular updates keep everything safe from threats and remains protected.": "Our advanced security features ensure the safety of your data through advanced protective measures. Also, automated regular updates keep everything safe from threats and remains protected.", "Real-time Resources Monitoring": "Real-time Resources Monitoring", "Monitoring your site and server is essential for ensuring optimal performance and reliability. With real-time measures, you can quickly identify issues provide a seamless experience to your customers.": "Monitoring your site and server is essential for ensuring optimal performance and reliability. With real-time measures, you can quickly identify issues provide a seamless experience to your customers.", "Transparent & Flexible Pricing for Everyone": "Transparent & Flexible Pricing for Everyone", "Explore our range of plans designed to meet every needs of every web creator": "Explore our range of plans designed to meet every needs of every web creator", "Includes": "Includes", "All rights reserved.": "All rights reserved.", "Terms of Service": "Terms of Service", "Terms & Services": "Terms & Services", "Just One Click Away from Completing Your Order": "Just One Click Away from Completing Your Order", "Already have an account?": "Already have an account?", "Sign In": "Sign In", "Integrate any cloud provider to manage server and sites in xCloud": "Integrate any cloud provider to manage server and sites in xCloud", "Choose Your Plan": "Choose Your Plan", "Cost": "Cost", "Applied Coupon": "Applied Coupon", "Purchase Limit Reached!": "Purchase Limit Reached!", "Only one purchase remaining!": "Only one purchase remaining!", "Split Pay": "Split Pay", "Ready to confirm your purchase?": "Ready to confirm your purchase?", "By clicking 'Confirm', a charge of": "By clicking 'Confirm', a charge of", "will be applied to your saved credit card.": "will be applied to your saved credit card.", "Please switch to your team to checkout": "Please switch to your team to checkout", "You are currently in the Playground Team. You can't checkout from here": "You are currently in the Playground Team. You can't checkout from here", "Total Purchases": "Total Purchases", "Only": "Only", "purchase remaining": "purchase remaining", "Database Root Password": "Database Root Password", "Do you want to turn off Auto Backups?": "Do you want to turn off Auto Backups?", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks": "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks", "Create Server": "Create Server", "Report an Issue": "Report an Issue", "Something went wrong": "Something went wrong", "Please try again or report an issue to support": "Please try again or report an issue to support", "Retry": "Retry", "You may exit this window and navigate away": "You may exit this window and navigate away", "Check Connection": "Check Connection", "Add Site": "Add Site", "Install Stack": "Install Stack", "SSH User": "SSH User", "SSH Key": "SSH Key", "Log": "Log", "Install stack on the server": "Install stack on the server", "It will install these following services on the server": "It will install these following services on the server", "PHP 7 .4": "PHP 7 .4", "MySQL 8 .0": "MySQL 8 .0", "Redis": "Redis", "and more. .": "and more. .", "Install": "Install", "Log in": "Log in", "Register": "Register", "White Label": "White Label", "Playground": "Playground", "Email not verified": "Email not verified", "Please check your email to verify your account. Verify your email": "Please check your email to verify your account. Verify your email", "Do you want to switch from": "Do you want to switch from", "You can create free demo sites under Playground Team which will be removed after 24 hours. From Team Settings page you can switch back to your default team again.": "You can create free demo sites under Playground Team which will be removed after 24 hours. From Team Settings page you can switch back to your default team again.", "Info": "Info", "You can switch to any team you belong by clicking on the team name in the top right header.": "You can switch to any team you belong by clicking on the team name in the top right header.", "Switch to": "Switch to", "Yes": "Yes", "No": "No", "Try xCloud Playground": "Try xCloud Playground", "Do you want to Switch your team?": "Do you want to Switch your team?", "Please accept or check your email to switch to": "Please accept or check your email to switch to", "team. You can also visit": "team. You can also visit", "to manage your teams.": "to manage your teams.", "Please accept or check your email to switch to your team. You can also visit": "Please accept or check your email to switch to your team. You can also visit", "My Profile": "My Profile", "Support": "Support", "Documentation": "Documentation", "Affiliates": "Affiliates", "Admin Panel": "Admin Panel", "Reports": "Reports", "Horizon": "Horizon", "Telescope": "Telescope", "Vapor UI": "Vapor UI", "Documents": "Documents", "Find Servers or Sites": "Find Servers or Sites", "Search Results": "Search Results", "of": "of", "Check our ": "Check our ", " to get free hosting for 6 months with new Vultr signups.": "to get free hosting for 6 months with new Vultr signups.", " to get a quick start.": " to get a quick start.", "Check out our ": "Check out our ", "Quick Start.": "Quick Start.", "No vulnerabilities were found on your sites. To setup vulnerability scanner please check this": "No vulnerabilities were found on your sites. To setup vulnerability scanner please check this", "Hey there! You have no ": "Hey there! You have no ", " yet.": " yet.", "Quick Start Documentation": "Quick Start Documentation", "This feature is not available in Playground.": "This feature is not available in Playground.", "No custom Nginx configurations found!": "No custom Nginx configurations found!", "Hey there! You have no": "Hey there! You have no", "plugins.": "plugins.", "themes.": "themes.", "to claim": "to claim", "$20 free credits": "$20 free credits", "if you are a new user on Hetzner.": "if you are a new user on <PERSON><PERSON>ner.", " to claim ": " to claim ", "Read our ": "Read our ", " on Heztner API integration.": " on Heztner API integration.", "Choose AWS Services": "Choose AWS Services", "Select one or more AWS services for which you want to use this credentials.": "Select one or more AWS services for which you want to use this credentials.", "Verify": "Verify", "$300 free credits": "$300 free credits", "if you are a new user on Google Cloud Platform.": "if you are a new user on Google Cloud Platform.", "Back": "Back", "$100 free credits": "$100 free credits", "if you are a new user on Linode.": "if you are a new user on Linode.", "You can connect a fresh": "You can connect a fresh", "Ubuntu 24.04 LTS x64": "Ubuntu 24.04 LTS x64", "server from any provider if you have root access.": "server from any provider if you have root access.", "$200 free credits": "$200 free credits", "if you are a new user on DigitalOcean.": "if you are a new user on DigitalOcean.", "to collect your API key. Read our": "to collect your API key. Read our", "and Use": "and Use", "XCLOUD": "XCLOUD", "coupon code to claim": "coupon code to claim", "$100": "$100", "free credits on new vultr singups for 180 days.": "free credits on new vultr singups for 180 days.", "Database Info": "Database Info", "Refreshing...": "Refreshing...", "By default you can use": "By default you can use", "user. But if you want to do it for specific site enter the specific site user name here.": "user. But if you want to do it for specific site enter the specific site user name here.", "Update PHP Configuration": "Update PHP Configuration", "Default PHP Version": "Default PHP Version", "Default Server PHP Version": "Default Server PHP Version", "This will set the default PHP version for the CLI and for new site installations. However, it won't affect the PHP versions of any existing sites, and the default version for new sites can still be changed during installation.": "This will set the default PHP version for the CLI and for new site installations. However, it won't affect the PHP versions of any existing sites, and the default version for new sites can still be changed during installation.", "Run Custom Command": "Run Custom Command", "Most Recent Commands": "Most Recent Commands", "This command will be executed on the server. Please make sure you are running the correct command. Running incorrect commands can break your server.": "This command will be executed on the server. Please make sure you are running the correct command. Running incorrect commands can break your server.", "If you want to do it for specific site enter the specific site user name here.": "If you want to do it for specific site enter the specific site user name here.", "Run Command": "Run Command", "Run New Command": "Run New Command", "Server Health": "Server Health", "Filter": "Filter", "Event": "Event", "Date & Time": "Date & Time", "Firewall Management": "Firewall Management", "Vulnerability Scan": "Vulnerability Scan", "Your server is set to automatically update security patches. A reboot is required to complete the updates. You can configure the server to reboot automatically at a preferred time or choose to do it manually.": "Your server is set to automatically update security patches. A reboot is required to complete the updates. You can configure the server to reboot automatically at a preferred time or choose to do it manually.", "Provider Backup Setting": "Provider Backup Setting", "Information": "Information", "Notes": "Notes", "Connection Settings": "Connection Settings", "Server Settings": "Server Settings", "Update Timezone": "Update Timezone", "Magic Login Settings": "Magic Login Settings", "Time Zone": "Time Zone", "Turn off Indexing": "Turn off Indexing", "Turning off this setting will prevent search engines from indexing your staging site.": "Turning off this setting will prevent search engines from indexing your staging site.", "Search Engine Visibility": "Search Engine Visibility", "Discourage search engines from indexing this site": "Discourage search engines from indexing this site", "It is up to search engines to honor this request.": "It is up to search engines to honor this request.", " offers a temporary test domain that allows you to quickly deploy your site. ": " offers a temporary test domain that allows you to quickly deploy your site. ", "This temporary domain enables you to share your work in progress with teammates or clients for review ": "This temporary domain enables you to share your work in progress with teammates or clients for review ", "and input before you finalize and launch it with your own custom domain for public access.": "and input before you finalize and launch it with your own custom domain for public access.", "HTTPS Is Enabled": "HTTPS Is Enabled", "Do You Want To Enable HTTPS?": "Do You Want To Enable HTTPS?", "Please make sure that the database connection configuration is correct and if you are creating a new database you may want to store the information in a secure place. Storing the wp-config.php file in Git is not recommended and if the wp-config.php file is absent from your Git repository, ": "Please make sure that the database connection configuration is correct and if you are creating a new database you may want to store the information in a secure place. Storing the wp-config.php file in Git is not recommended and if the wp-config.php file is absent from your Git repository, ", " will automatically generate it from wp-config-sample.php, adding database credentials. Additional credentials can be added later on the WP Config page in the ": " will automatically generate it from wp-config-sample.php, adding database credentials. Additional credentials can be added later on the WP Config page in the ", " site dashboard. If wp-config.php is already in the repository, ": " site dashboard. If wp-config.php is already in the repository, ", " won't make any changes.": " won't make any changes.", "Database Management": "Database Management", "Your source server must be": "Your source server must be", "or": "or", "OLS": "OLS", "server with": "server with", "Ubuntu 20.04 or 22.04 LTS x64": "Ubuntu 20.04 or 22.04 LTS x64", "and should have": "and should have", "access.": "access.", "month": "month", "Email Address": "Email Address", "By default, emails are sent from": "By default, emails are sent from", "To send emails from your domain, enter your custom SMTP credentials (e.g., Mailgun, Elastic Email)": "To send emails from your domain, enter your custom SMTP credentials (e.g., Mailgun, Elastic Email)", "Two Factor Authentication": "Two Factor Authentication", "You have enabled two factor authentication.": "You have enabled two factor authentication.", "Finish enabling two factor authentication.": "Finish enabling two factor authentication.", "You have not enabled two factor authentication.": "You have not enabled two factor authentication.", "Enable": "Enable", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application.": "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application.", "Two factor authentication is now enabled.": "Two factor authentication is now enabled.", "Enter the text code below instead if you can't use the barcode.": "Enter the text code below instead if you can't use the barcode.", "Or, Scan the": "Or, Scan the", "QR provided": "QR provided", "with your phone's two-factor authentication app.": "with your phone's two-factor authentication app.", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.", "Setup Key": "Setup Key", "Regenerate Recovery Codes": "Regenerate Recovery Codes", "Show Recovery Codes": "Show Recovery Codes", "Create SSH Key": "Create SSH Key", "Create A New SSH Key": "Create A New SSH Key", "Verify & Save for": "Verify & Save for", "Buy & Add": "Buy & Add", "Make sure you have active billing plan to use this feature.": "Make sure you have active billing plan to use this feature.", "This label will be used to identify this SMTP provider in": "This label will be used to identify this SMTP provider in", "Optional if you're using global API key of Mailgun": "Optional if you're using global API key of Mailgun", ". i.e. Mailgun, Sendgrid, etc.": ". i.e. Mailgun, Sendgrid, etc.", "This label will be used to identify this SMTP provider in xCloud. i.e. Mailgun, Sendgrid, etc.": "This label will be used to identify this SMTP provider in xCloud. i.e. Mailgun, Sendgrid, etc.", "Authorize on WhatsApp": "Authorize on WhatsApp", "Authorize on": "Authorize on", "Please go to Telegram and start talking with": "Please go to Telegram and start talking with", "To authenticate your chat, send this command to xCloud Notification Bot.": "To authenticate your chat, send this command to xCloud Notification Bot.", "Due Date": "Due Date", "Recent Events": "Recent Events", "Currently No Event Available": "Currently No Event Available", "Mark all as read": "Mark all as read", "No Notifications Available!": "No Notifications Available!", "provides": "provides", "Environment to do all the development to your site with a temporary domain.": "Environment to do all the development to your site with a temporary domain.", "When you are ready, simply select the GO LIVE option and add your own domain to make the site available to your user/visitor.": "When you are ready, simply select the GO LIVE option and add your own domain to make the site available to your user/visitor.", "Demo Site Management": "Demo Site Management", "Verifying DNS..": "Verifying DNS..", "Your staging SSL is being managed by": "Your staging SSL is being managed by", "Your SSL is being managed by Cloudflare.": "Your SSL is being managed by Cloudflare.", "If you turn on this option then": "If you turn on this option then", "will automatically enable NGINX to directly serve previously": "will automatically enable NGINX to directly serve previously", "cached files without calling WordPress or any PHP. It also adds headers to cached CSS, JS, and images via": "cached files without calling WordPress or any PHP. It also adds headers to cached CSS, JS, and images via", "the browser cache. As a result, your website will be much faster. If someone visits your website again,": "the browser cache. As a result, your website will be much faster. If someone visits your website again,", "their browser can load the cached files directly from their own computer instead of making a request to": "their browser can load the cached files directly from their own computer instead of making a request to", "your web server. This reduces the number of requests to your server and further speeds up the loading of your website.": "your web server. This reduces the number of requests to your server and further speeds up the loading of your website.", "You can read more from our documentation": "You can read more from our documentation", "Saving Changes...": "Saving Changes...", "Make sure to add your SSH key on the SSH Authentication section.": "Make sure to add your SSH key on the SSH Authentication section.", "Enter the configuration file name": "Enter the configuration file name", "Save Config": "Save Config", "Create Custom Nginx Configuration for": "Create Custom Nginx Configuration for", "Select Template": "Select Template", "IP Whitelist/Blacklist": "IP Whitelist/Blacklist", "WP-Cron and xCloud-Cron": "WP-Cron and xCloud-Cron", "Update PHP Settings": "Update PHP Settings", "All existing files and data on this site will be deleted": "All existing files and data on this site will be deleted", "Create a new backup before restoring?": "Create a new backup before restoring?", "Restore": "Rest<PERSON>", "Take Backup": "Take Backup", "Full Backup": "Full Backup", "Are you sure you want to take a full backup? This will create a complete backup of all your data. Future incremental backups will be based on this full backup.": "Are you sure you want to take a full backup? This will create a complete backup of all your data. Future incremental backups will be based on this full backup.", "Incremental Backup": "Incremental Backup", "This will add to your previous backups by only saving the changes since the last backup.": "This will add to your previous backups by only saving the changes since the last backup.", "Site List": "Site List", "Management": "Management", "Settings": "Settings", "Custom Cron Jobs": "Custom Cron Jobs", "PHP Configuration": "PHP Configuration", "Commands": "Commands", "Monitoring": "Monitoring", "Logs": "Logs", "Events": "Events", "Firewall management": "Firewall management", "Vulnerability Settings": "Vulnerability Settings", "Full Server migration": "Full Server migration", "Metadata": "<PERSON><PERSON><PERSON>", "Site Overview": "Site Overview", "Caching": "Caching", "Email Configuration": "Email Configuration", "Previous Backups": "Previous Backups", "Backup Settings": "Backup Settings", "Site Monitoring": "Site Monitoring", "Access Data": "Access Data", "SSH/sFTP": "SSH/sFTP", "Tools": "Tools", "Nginx and Security": "Nginx and Security", "Nginx Customization": "Nginx Customization", "IP Management": "IP Management", "Site Settings": "Site Settings", "User Profile": "User Profile", "Browser Sessions": "Browser Sessions", "Integrations": "Integrations", "Cloudflare": "Cloudflare", "Notification": "Notification", "Billing": "Billing", "Manual Invoices": "Manual Invoices", "Whitelabel": "Whitelabel", "All Events": "All Events", "Payment": "Payment", "Brand": "Brand", "SMTP": "SMTP", "Create Products": "Create Products", "Setup your brand to get started": "Setup your brand to get started", "Setup your payment method": "Setup your payment method", "Create customized plans": "Create customized plans", "Setup your domain easily": "Setup your domain easily", "Search sites...": "Search sites...", "Create Account": "Create Account", "Password must be at least  8 characters and should contain uppercase, number and special character": "Password must be at least  8 characters and should contain uppercase, number and special character", "Filter by Date": "Filter by Date", "Name your blueprint": "Name your blueprint", "Search": "Search", "Are You Sure You Want To Delete Server": "Are You Sure You Want To Delete Server", "to confirm": "to confirm", "Are You Sure You Want To Delete Site": "Are You Sure You Want To Delete Site", "Edit Server Provider": "Edit Server Provider", "Add Server Provider": "Add Server Provider", "check connection": "check connection", "Clone Blueprint": "Clone Blueprint", "Delete Blueprint": "Delete Blueprint", "Are you sure you want to delete this blueprint?": "Are you sure you want to delete this blueprint?", "Are you sure you want to clone this blueprint?": "Are you sure you want to clone this blueprint?", "Clone": "<PERSON><PERSON>", "Set as Default Blueprint": "Set as Default Blueprint", "Are you sure you want to set this blueprint as default?": "Are you sure you want to set this blueprint as default?", "You won't be able to revert this!": "You won't be able to revert this!", "Are you sure you want to update WordPress?": "Are you sure you want to update WordPress?", "Update WordPress Core": "Update WordPress Core", "Are you sure you want to active this theme?": "Are you sure you want to active this theme?", "Activate Theme": "Activate Theme", "Are you sure you want to update those plugin?": "Are you sure you want to update those plugin?", "You have selected": "You have selected", "plugins": "plugins", "Are you sure you want to update those plugins?": "Are you sure you want to update those plugins?", "Are you sure you want to update these themes?": "Are you sure you want to update these themes?", "themes": "themes", "Do you want to deactivate Adminer?": "Do you want to deactivate Ad<PERSON><PERSON>?", "Do you want to activate Adminer?": "Do you want to activate Adminer?", "We recommend deactivating it when not required.": "We recommend deactivating it when not required.", "Yes, restore it!": "Yes, restore it!", "Are you sure you want to restore this server?": "Are you sure you want to restore this server?", "Are you sure you want to delete this card?": "Are you sure you want to delete this card?", "You can add another card.": "You can add another card.", "Yes, log out!": "Yes, log out!", "Are you sure you want to delete this integration?": "Are you sure you want to delete this integration?", "You want to disconnect": "You want to disconnect", "Yes, Disconnect!": "Yes, Disconnect!", "You want to reconnect": "You want to reconnect", "Yes, Reconnect!": "Yes, Reconnect!", "Yes, disable it!": "Yes, disable it!", "Are you sure you want to disable HTTPS?": "Are you sure you want to disable HTTPS?", "Yes, Leave Team!": "Yes, Leave Team!", "Edit Storage Provider": "Edit Storage Provider", "Add Storage Provider": "Add Storage Provider", "Yes, leave team!": "Yes, leave team!", "This will remove the cron job from the server.": "This will remove the cron job from the server.", "Are you sure you want to disable this firewall rule?": "Are you sure you want to disable this firewall rule?", "You can enable it later.": "You can enable it later.", "Yes, Disable": "Yes, Disable", "Are you sure you want to enable this firewall rule?": "Are you sure you want to enable this firewall rule?", "You can disable it later.": "You can disable it later.", "Yes, Enable": "Yes, Enable", "Are you sure you want to delete this firewall rule?": "Are you sure you want to delete this firewall rule?", "Deleting firewall rule will remove it permanently.": "Deleting firewall rule will remove it permanently.", "Yes, Delete": "Yes, Delete", "Are you sure you want to unban": "Are you sure you want to unban", "This will remove the IP address from the banned list.": "This will remove the IP address from the banned list.", "Yes, Unban": "Yes, Unban", "This will remove the sudo user from the server.": "This will remove the sudo user from the server.", "Authentication will be disabled for this site": "Authentication will be disabled for this site", "Yes, Remove": "Yes, Remove", "Are you sure you want to delete this backup?": "Are you sure you want to delete this backup?", "This action cannot be undone.": "This action cannot be undone.", "Are you sure you want to remove this failed backup?": "Are you sure you want to remove this failed backup?", "Do you want to deactivate Tiny File Manager?": "Do you want to deactivate Tiny File Manager?", "Do you want to activate Tiny File Manager?": "Do you want to activate Tiny File Manager?", "Are you sure you want to disable": "Are you sure you want to disable", "caching?": "caching?", "Are you sure you want to disable redis object caching?": "Are you sure you want to disable redis object caching?", "Yes, switch it!": "Yes, switch it!", "Are you sure you want to switch to": "Are you sure you want to switch to", "plugin?": "plugin?", "Are you sure you want to disable full page caching?": "Are you sure you want to disable full page caching?", "Yes, Remove it!": "Yes, Remove it!", "Are you sure you want to remove this IP address?": "Are you sure you want to remove this IP address?", "Are you sure you want to disable LiteSpeed Cache?": "Are you sure you want to disable Lite<PERSON>pe<PERSON> Cache?", "Are you sure you want to delete this Nginx Configuration?": "Are you sure you want to delete this Nginx Configuration?", "It will be permanently deleted.": "It will be permanently deleted.", "Enable Nginx File Regeneration": "Enable Nginx File Regeneration", "This will prevent xCloud from regenerating nginx file on any changes made to the site. You will have to manually regenerate the nginx file.": "This will prevent xCloud from regenerating nginx file on any changes made to the site. You will have to manually regenerate the nginx file.", "This will allow xCloud to regenerate nginx file on any changes made to the site. You will not have to manually regenerate the nginx file.": "This will allow xCloud to regenerate nginx file on any changes made to the site. You will not have to manually regenerate the nginx file.", "Enable Site": "Enable Site", "Disabling the site will make it inaccessible. Are you sure you want to disable it?": "Disabling the site will make it inaccessible. Are you sure you want to disable it?", "Enabling the site will make it accessible. Are you sure you want to enable it?": "Enabling the site will make it accessible. Are you sure you want to enable it?", "This action will run the rescue process on the site. Are you sure you want to run it?": "This action will run the rescue process on the site. Are you sure you want to run it?", "Yes, Run Now": "Yes, Run Now", "Are you sure you want to update the plugin?": "Are you sure you want to update the plugin?", "Plugin will be updated to": "Plugin will be updated to", "Are you sure you want to not ignore this vulnerability?": "Are you sure you want to not ignore this vulnerability?", "Are you sure you want to ignore this vulnerability?": "Are you sure you want to ignore this vulnerability?", "Are you sure you want to update the theme?": "Are you sure you want to update the theme?", "You have select": "You have select", "theme": "theme", "Are You Sure You Want To Delete this SSH Key:": "Are You Sure You Want To Delete this SSH Key:", "Generate Invoice": "Generate Invoice", "Add A New Payment Method": "Add A New Payment Method", "Log Out Other Browser Sessions": "Log Out Other Browser Sessions", "Log Out": "Log Out", "Log Out of All Sessions": "Log Out of All Sessions", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.", "You won't be able to revert this to your current device.": "You won't be able to revert this to your current device.", "Are you sure you want to delete": "Are you sure you want to delete", "You cannot delete this team": "You cannot delete this team", "You cannot delete your personal team": "You cannot delete your personal team", "You cannot delete your current team": "You cannot delete your current team", "Admin Username": "<PERSON><PERSON>", "SIZE": "SIZE", "REGION": "REGION", "UBUNTU": "UBUNTU", "Deleting..": "Deleting..", "Delete SSH Key": "Delete SSH Key", "Add a new database to your server": "Add a new database to your server", "Add a new user to your database": "Add a new user to your database", "Edit database user:": "Edit database user:", "Are You Sure You Want To Delete this database:": "Are You Sure You Want To Delete this database:", "Are You Sure You Want To Delete this database user:": "Are You Sure You Want To Delete this database user:", "Cron Job Output": "Cron Job Output", "Add new firewall rule for": "Add new firewall rule for", "Ban an IP Address on": "Ban an IP Address on", "Reboot Time": "Reboot Time", "Your server is set to automatically reboot at": "Your server is set to automatically reboot at", "You can change the reboot time if you'd like.": "You can change the reboot time if you'd like.", "CPU Usage": "CPU Usage", "Reboot": "Reboot", "Are You Sure You Want To": "Are You Sure You Want To", "Service?": "Service?", "Are You Sure You Want To Restore Backup For The Server": "Are You Sure You Want To Restore Backup For The Server", "Are You Sure You Want To Resize Server": "Are You Sure You Want To Resize Server", "Resize": "Resize", "Initiating Resizing...": "Initiating Resizing...", "Manage Database": "Manage Database", "Last Updated": "Last Updated", "Deactivate": "Deactivate", "Last Checked": "Last Checked", "Current Version": "Current Version", "RAM Usages": "RAM Usages", "Disk Usages": "Disk Usages", "Choose SSH Keys": "Choose <PERSON>H Keys", "No SSH Keys found.": "No SSH Keys found.", "Search SSH Keys": "Search SSH Keys", "Are You Sure You Want To Disable Site": "Are You Sure You Want To Disable Site", "Add Your Own Database": "Add Your Own Database", "Please provide your new database information": "Please provide your new database information", "Cron Interval for Server": "<PERSON><PERSON>val for Server", "Add this public key in your Git repository as deploy key. This is necessary to enable": "Add this public key in your Git repository as deploy key. This is necessary to enable", "to clone the repository, ensuring a secure and authorized access for automated processes such as cloning.": "to clone the repository, ensuring a secure and authorized access for automated processes such as cloning.", "Click On The Plugin Download Link": "Click On The Plugin Download Link", "Upload the zipped plugin file to WordPress under 'Add New' in the 'Plugins' tab": "Upload the zipped plugin file to WordPress under 'Add New' in the 'Plugins' tab", "Click 'Activate' to install the plugin": "Click 'Activate' to install the plugin", "Copy the authentication token and paste it into the plugin page to complete the setup": "Copy the authentication token and paste it into the plugin page to complete the setup", "Upload The Plugin Zipped File On WordPress": "Upload The Plugin Zipped File On WordPress", "Click On The ‘Activate’ Button To Install Plugin": "Click On The ‘Activate’ Button To Install Plugin", "Copy The Authentication Token And Paste It Into The Plugin Page": "Copy The Authentication Token And Paste It Into The Plugin Page", "Export Zipped File From Your Existing Site": "Export Zipped File From Your Existing Site", "Upload The Exported Zip or Tar File Here": "Upload The Exported Zip or Tar File Here", "You must have root access to perform Full Server Migration": "You must have root access to perform Full Server Migration", "Migrate multiple cPanel sites from shared hosting to xCloud easily for faster, scalable and secure hosting.": "Migrate multiple cPanel sites from shared hosting to xCloud easily for faster, scalable and secure hosting.", "If the fetched domain is incorrect, you can edit it here, the domain name needs to be accurate for successful migration": "If the fetched domain is incorrect, you can edit it here, the domain name needs to be accurate for successful migration", "Total migrated": "Total migrated", "Verifying ...": "Verifying ...", "Choose Web Server": "Choose Web Server", "Updating Backup Schedule...": "Updating Backup Schedule...", "Update Backup Schedule": "Update Backup Schedule", "Set Schedule": "Set Schedule", "Are You Sure?": "Are You Sure?", "Restoring...": "Restoring...", "Enabling Backup...": "Enabling Backup...", "Disabling Backup...": "Disabling Backup...", "Backup?": "Backup?", "Provider Backup Schedule": "Provider Backup Schedule", "Update Schedule": "Update Schedule", "Select Day of Week": "Select Day of Week", "Select Day of Month": "Select Day of Month", "View all": "View all", "You are now managing": "You are now managing", "site on": "site on", "server.": "server.", "You can not use your own domain with xCloud Free Email Service..": "You can not use your own domain with xCloud Free Email Service..", "For example, you can send <NAME_EMAIL> or <EMAIL>. It is up to you!": "For example, you can send <NAME_EMAIL> or <EMAIL>. It is up to you!", "Are you sure you want to restore this backup?": "Are you sure you want to restore this backup?", "X-Frame-Options": "X-Frame-Options", "SAMEORIGIN": "SAMEORIGIN", "Update Tags": "Update Tags", "Zone": "Zone", "Choose Zones": "Choose Zones", "Choose Regions": "Choose Regions", "Choose Sizes": "<PERSON><PERSON>", "site": "site", "No Vulnerabilities Found": "No Vulnerabilities Found", "Vulnerability Scan Not Enabled": "Vulnerability Scan Not Enabled", "Updates Available": "Updates Available", "sudo user": "sudo user", "IP": "IP", "Package": "Package", "Can't find what you're looking for?": "Can't find what you're looking for?", "Contact Support": "Contact Support", "Nginx Options": "Nginx Options", "OpenLiteSpeed Options": "OpenLiteSpeed Options", "Link Google Drive Account": "Link Google Drive Account", "Recreate Site from Backup": "Recreate Site from Backup", "Restore site backup from local or remote storage easily to create a site": "Restore site backup from local or remote storage easily to create a site", "Language Settings": "Language Settings", "Copyright": "Copyright", "Taking Payment...": "Taking Payment...", "Bill": "Bill", "Pay": "Pay", "Additional Information": "Additional Information", "Name of Invoice": "Name of Invoice", "Notification Language Settings": "Notification Language Settings", "Turning on this setting will prevent search engines from indexing your staging site.": "Turning on this setting will prevent search engines from indexing your staging site.", "Adminer and File Manager won't work with 7G/8G firewall. We recommend SFTP if you're familiar with it.": "Adminer and File Manager won't work with 7G/8G firewall. We recommend SFTP if you're familiar with it.", "For example, you can send emails from": "For example, you can send emails from", "It is up to you!": "It is up to you!", "Demo Site Setup": "Demo Site Setup", "others": "others", "Items": "Items", "List of Items": "List of Items", "Integrate New Item": "Integrate New Item", "Add New Item": "Add New Item", "License": "License", "Are you sure you want to delete?": "Are you sure you want to delete?", "Item deleted successfully": "Item deleted successfully", "Failed to delete Item": "Failed to delete Item", "Select Integrated Plugin": "Select Integrated Plugin", "Boost your WordPress site’s performance with Object Cache Pro by caching frequently accessed data in Redis. This reduces database queries, enhances speed, and is especially beneficial for dynamic, content-heavy websites.": "Boost your WordPress site’s performance with Object Cache Pro by caching frequently accessed data in Redis. This reduces database queries, enhances speed, and is especially beneficial for dynamic, content-heavy websites.", "Debug Mode": "Debug Mode", "Object Cache Pro": "Object Cache Pro", "Select Plugin": "Select Plugin", "Update Item": "Update Item", "Edit Item": "<PERSON>em", "Add Item": "Add Item", "Enter your license label": "Enter your license label", "Enter your license key": "Enter your license key", "Customization": "Customization", "Access File Manager": "Access File Manager", "Always Enabled": "Always Enabled", "Keep the File Manager accessible at all times.": "Keep the File Manager accessible at all times.", "Disable File Manager": "Disable File Manager", "Specify how long the File Manager should remain enabled before it’s automatically disabled.": "Specify how long the File Manager should remain enabled before it’s automatically disabled.", "Set Auto Disable Duration": "Set Auto Disable Duration", "Choose to keep the File Manager active for always or schedule it to disable after a certain time.": "Choose to keep the File Manager active for always or schedule it to disable after a certain time.", "Access Adminer": "Access Adminer", "Keep the Adminer accessible at all times.": "Keep the Adminer accessible at all times.", "Disable Adminer": "Disable Adminer", "Specify how long the Adminer should remain enabled before it’s automatically disabled.": "Specify how long the <PERSON><PERSON>r should remain enabled before it’s automatically disabled.", "Set Auto Deactivate Duration": "Set Auto Deactivate Duration", "Choose to keep the Adminer active for always or schedule it to disable after a certain time.": "Choose to keep the <PERSON><PERSON>r active for always or schedule it to disable after a certain time.", "The File Manager will be disable within": "The File Manager will be disable within", "The Adminer will be disable within": "The Adminer will be disable within", "Custom PHP": "Custom PHP", "laravel": "laravel", "Coming Soon": "Coming Soon", "Update Web Root": "Update Web Root", "Web Root": "Web Root", "Login": "<PERSON><PERSON>", "Login Options": "Login Options", "Log in using xCloud email account or the first admin account.": "Log in using xCloud email account or the first admin account.", "Use a different email and, if your login URL is custom, enter it here to log in.": "Use a different email and, if your login URL is custom, enter it here to log in.", "Custom": "Custom", "Something went wrong, please try again.": "Something went wrong, please try again.", "Use a different email or username to log in.": "Use a different email or username to log in.", "Enter your email": "Enter your email", "Haven’t registered for your free account yet?": "Haven’t registered for your free account yet?", "Sign up now": "Sign up now", "Glad To Have You Back!": "Glad To Have You Back!", "Get Started For Free": "Get Started For Free", "Access 1 server and 10 sites at $0 cost. No credit card required.": "Access 1 server and 10 sites at $0 cost. No credit card required.", "Enter your name": "Enter your name", "Please choose a strong password. Use at least 8 characters, including a mix of letters, numbers and symbols.": "Please choose a strong password. Use at least 8 characters, including a mix of letters, numbers and symbols.", "Retype Password": "Retype Password", "The passwords you entered do not match. Please ensure both fields contain the same password.": "The passwords you entered do not match. Please ensure both fields contain the same password.", "By creating an account, you agree to our": "By creating an account, you agree to our", "Create Your Account": "Create Your Account", "Enter your credentials to sign up.": "Enter your credentials to sign up.", "We have discovered a security vulnerability in the": "We have discovered a security vulnerability in the", "that you are using on the following website:": "that you are using on the following website:", "View Vulnerabilities": "View Vulnerabilities", "Immediate Action Required: We found a security vulnerability in your Website": "Immediate Action Required: We found a security vulnerability in your Website", "Community": "Community", "Want To Create A Staging Site For": "Want To Create A Staging Site For", "A staging site is a secure clone of your live website for testing and development. It lets you experiment with plugins, themes, and changes risk-free before deploying them to live.": "A staging site is a secure clone of your live website for testing and development. It lets you experiment with plugins, themes, and changes risk-free before deploying them to live.", "Test Domain": "Test Domain", "Create a demo site with our test domains and customize it before going live.": "Create a demo site with our test domains and customize it before going live.", "Custom Domain": "Custom Domain", "Enter your custom domain by simply pointing your domain to the server.": "Enter your custom domain by simply pointing your domain to the server.", "Deploy Staging Site": "Deploy Staging Site", "Manage Staging": "Manage Staging", "Integrity Monitor": "Integrity Monitor", "Scan Now": "Scan Now", "Last scan": "Last scan", "Items found": "Items found", "Message": "Message", "Plugin": "Plugin", "This website has found some checksum errors. This could be due to a corrupted file or a malicious attack. Please review the files and database of your site.": "This website has found some checksum errors. This could be due to a corrupted file or a malicious attack. Please review the files and database of your site.", "Check": "Check", "Security Settings": "Security Settings", "AI Bot Blocker": "AI <PERSON><PERSON>er", "Disable Nginx Config Regeneration": "Disable Nginx Config Regeneration", "Disable OpenLiteSpeed Config Regeneration": "Disable OpenLiteSpeed Config Regeneration", "WP Fail2Ban": "WP Fail2Ban", "Block Failed Login Attempts": "Block Failed Login Attempts", "Block Common Usernames": "Block Common Usernames", "Block User Enumeration": "Block User Enumeration", "Protect Comments": "Protect Comments", "Block Spam": "Block Spam", "Guard Password Resets": "Guard Password Resets", "Guard Pingbacks": "Guard Pingbacks", "Enable OpenLiteSpeed Config Regeneration": "Enable OpenLiteSpeed Config Regeneration", "Config file regeneration has been updated successfully": "Config file regeneration has been updated successfully", "This will allow xCloud to regenerate OpenLiteSpeed config on any changes made to the site. You will not have to manually regenerate the OpenLiteSpeed config.": "This will allow xCloud to regenerate OpenLiteSpeed config on any changes made to the site. You will not have to manually regenerate the OpenLiteSpeed config.", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config.": "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config.", "This will prevent xCloud from regenerating nginx config on any changes made to the site. You will have to manually regenerate the nginx config.": "This will prevent xCloud from regenerating nginx config on any changes made to the site. You will have to manually regenerate the nginx config.", "This will allow xCloud to regenerate nginx config on any changes made to the site. You will not have to manually regenerate the nginx config.": "This will allow xCloud to regenerate nginx config on any changes made to the site. You will not have to manually regenerate the nginx config.", "Serve robots.txt from file system": "Serve robots.txt from file system", "Failed to load nginx options": "Failed to load nginx options", "Plugins Found": "Plugins Found", "Please configure Cloudflare SSL on this site to enable edge cache.": "Please configure Cloudflare SSL on this site to enable edge cache.", "Cloudflare Edge Cache is not available for staging sites. Please switch to production with Cloudflare SSL for this site to enable edge cache.": "Cloudflare Edge Cache is not available for staging sites. Please switch to production with Cloudflare SSL for this site to enable edge cache.", "You need to set up Cloudflare integration first to enable edge cache.": "You need to set up Cloudflare integration first to enable edge cache.", "Domain is not available on Cloudflare. Please add your domain to Cloudflare first to enable edge cache.": "Domain is not available on Cloudflare. Please add your domain to Cloudflare first to enable edge cache.", "Cloudflare Edge Cache has been enabled successfully.": "Cloudflare Edge Cache has been enabled successfully.", "Cloudflare Edge Cache has been disabled successfully.": "<PERSON><PERSON>lar<PERSON> has been disabled successfully.", "Failed to update Cloudflare Edge Cache settings.": "Failed to update Cloudflare Edge Cache settings.", "Cloudflare Edge Cache purged successfully.": "Cloudflare Edge <PERSON>ache purged successfully.", "Failed to purge Cloudflare Edge Cache.": "Failed to purge <PERSON><PERSON><PERSON><PERSON>.", "Cloudflare Edge Cache": "<PERSON><PERSON><PERSON><PERSON>", "Boost your website's performance with Cloudflare Edge Cache by caching content at Cloudflare's global edge network. This reduces server load, enhances speed, and improves user experience worldwide.": "Boost your website's performance with Cloudflare Edge Cache by caching content at Cloudflare's global edge network. This reduces server load, enhances speed, and improves user experience worldwide.", "Clear Edge Cache": "Clear Edge Cache", "This will purge all cached content from Cloudflare's edge network.": "This will purge all cached content from Cloudflare's edge network.", "Failed to update Cloudflare Edge Cache settings": "Failed to update Cloudflare Edge Cache settings", "Are you sure you want to disable Cloudflare Edge Cache?": "Are you sure you want to disable <PERSON><PERSON>lare <PERSON> Cache?", "Failed to disable Cloudflare Edge Cache": "Failed to disable <PERSON><PERSON><PERSON><PERSON>", "Cloudflare Edge Cache purged successfully": "<PERSON>flare <PERSON> purged successfully", "Failed to update config file regeneration": "Failed to update config file regeneration", "Laravel Horizon is not installed": "Laravel Horizon is not installed", "Horizon is not detected in your composer.json. To use Horizon, you need to install it first:": "Horizon is not detected in your composer.json. To use Horizon, you need to install it first:", "Laravel Application": "Laravel Application", "Enable Debug Mode": "Enable Debug Mode", "When debug mode is enabled, detailed error messages will be displayed. This should be disabled in production.": "When debug mode is enabled, detailed error messages will be displayed. This should be disabled in production.", "Enable Maintenance Mode": "Enable Maintenance Mode", "When maintenance mode is enabled, a maintenance screen will be displayed for all requests to your application.": "When maintenance mode is enabled, a maintenance screen will be displayed for all requests to your application.", "Application Environment": "Application Environment", "Application": "Application", "The application environment affects various Laravel behaviors. This setting updates APP_ENV in your .env file": "The application environment affects various Laravel behaviors. This setting updates APP_ENV in your .env file", "Clear Application Cache": "Clear Application Cache", "Clears all Laravel caches by running the optimize:clear command.": "Clears all Laravel caches by running the optimize:clear command.", "Clearing...": "Clearing...", "Clear Cache": "<PERSON>ache", "Laravel Horizon": "<PERSON>vel <PERSON>", "Update Process": "Update Process", "Start Horizon": "Start Horizon", "Not Configured": "Not Configured", "Horizon is running": "Horizon is running", "Horizon is not running": "Horizon is not running", "Stop": "Stop", "Horizon provides a beautiful dashboard and code-driven configuration for your Laravel powered Redis queues.": "Horizon provides a beautiful dashboard and code-driven configuration for your Laravel powered Redis queues.", "Laravel Scheduler": "<PERSON><PERSON>", "Start Scheduler": "Start Scheduler", "Your scheduler is running properly": "Your scheduler is running properly", "Scheduler needs to be configured": "Scheduler needs to be configured", "Scheduler Frequency": "Scheduler Frequency", "The Laravel scheduler allows you to fluently and expressively define your command schedule within Laravel itself.": "The Laravel scheduler allows you to fluently and expressively define your command schedule within Laravel itself.", "Local": "Local", "Testing": "Testing", "Every Minute": "Every Minute", "Every Five Minutes": "Every Five Minutes", "Every Ten Minutes": "Every Ten Minutes", "Every Fifteen Minutes": "Every Fifteen Minutes", "Every Thirty Minutes": "Every Thirty Minutes", "Hourly": "Hourly", "Server is not connected": "Server is not connected", "Failed to load Laravel application status": "Failed to load Laravel application status", "Laravel application settings updated successfully": "Laravel application settings updated successfully", "Failed to update Laravel application settings": "Failed to update Laravel application settings", "Application cache cleared successfully": "Application cache cleared successfully", "Failed to clear application cache": "Failed to clear application cache", "Horizon started successfully": "Horizon started successfully", "Failed to start Horizon": "Failed to start Horizon", "Horizon stopped successfully": "Horizon stopped successfully", "Failed to stop Horizon": "Failed to stop Horizon", "Horizon restarted successfully": "Horizon restarted successfully", "Failed to restart Horizon": "Failed to restart Horizon", "Scheduler setup successfully": "Scheduler setup successfully", "Failed to setup scheduler": "Failed to setup scheduler", "Scheduler stopped successfully": "Scheduler stopped successfully", "Failed to stop scheduler": "Failed to stop scheduler", "Laravel Queue": "<PERSON><PERSON>", "Add Queue Worker": "Add Queue Worker", "No queue workers are running": "No queue workers are running", "Connection": "Connection", "Queue": "Queue", "Timeout": "Timeout", "Memory": "Memory", "Refresh Status": "Refresh Status", "Edit Process": "Edit Process", "Restart Process": "Restart Process", "View Output": "View Output", "Queue workers process jobs in the background. They are useful for handling time-consuming tasks like sending emails, processing files, or any other task that should not block the main application.": "Queue workers process jobs in the background. They are useful for handling time-consuming tasks like sending emails, processing files, or any other task that should not block the main application.", "Update Queue Worker": "Update Queue Worker", "redis": "redis", "default": "default", "Maximum Seconds Per Job": "Maximum Seconds Per Job", "0 = No Timeout": "0 = No Timeout", "256": "256", "Maximum Memory (Optional)": "Maximum Memory (Optional)", "Memory limit in MB": "Memory limit in MB", "1": "1", "Number of worker processes to run": "Number of worker processes to run", "Queue Worker Output": "Queue Worker Output", "Queue worker updated successfully": "Queue worker updated successfully", "Queue worker added successfully": "Queue worker added successfully", "Are you sure you want to stop this queue worker?": "Are you sure you want to stop this queue worker?", "This will remove the queue worker process.": "This will remove the queue worker process.", "Yes, Stop": "Yes, Stop", "Queue worker stopped successfully": "Queue worker stopped successfully", "Failed to stop queue worker": "Failed to stop queue worker", "Queue worker restarted successfully": "Queue worker restarted successfully", "Failed to restart queue worker": "Failed to restart queue worker", "Error loading output. Please try again.": "Error loading output. Please try again.", "Failed to refresh worker status": "Failed to refresh worker status", "Deployment has been initiated.": "Deployment has been initiated.", "A self-hosted monitoring tool like \"Uptime Robot\"": "A self-hosted monitoring tool like \"Uptime Robot\"", "A web interface for managing MySQL databases": "A web interface for managing MySQL databases", "Admin User": "Admin User", "Default Node.js Version": "Default Node.js Version", "Default Server Node.js Version": "Default Server Node.js Version", "Disable phpMyAdmin": "Disable phpMyAdmin", "Disabling...": "Disabling...", "Enable phpMyAdmin": "Enable phpMyAdmin", "Enabling...": "Enabling...", "Install One Click App": "Install One Click App", "Install One Click App Into": "Install One Click App Into", "Mautic": "Mautic", "n8n": "n8n", "One Click App": "One Click App", "One Click Apps": "One Click Apps", "Open phpMyAdmin": "Open phpMyAdmin", "Open-source marketing automation platform": "Open-source marketing automation platform", "PHPMyAdmin": "PHPMyAdmin", "Please make sure to copy your admin password. You will not be able to see it again after installation.": "Please make sure to copy your admin password. You will not be able to see it again after installation.", "Provisioning...": "Provisioning...", "Repair Node": "Repair <PERSON>", "Repair PM2 Process & ecosystem.config.js": "Repair PM2 Process & ecosystem.config.js", "Restart PM2 Process": "Restart PM2 Process", "Select": "Select", "Select an Application": "Select an Application", "Selected": "Selected", "Site Details": "Site Details", "This will set the default Node.js version for the server.": "This will set the default Node.js version for the server.", "Uptime Kuma": "Uptime Kuma", "Workflow automation tool with a visual editor": "Workflow automation tool with a visual editor", "phpMyAdmin": "phpMyAdmin", "phpmyadmin_description_before": "phpMyAdmin is a free tool that lets you manage MySQL and MariaDB databases through your web browser.", "phpmyadmin_description_provisioning": "phpMyAdmin is enabled but not yet fully set up. Please wait while the provisioning process completes.", "phpmyadmin_description_after": "phpMyAdmin is enabled on this server. Click on the open button to access your databases.", "phpmyadmin_description": "A web interface for managing MySQL databases", "n8n_description": "Workflow automation tool with a visual editor", "uptime_kuma_description": "A self-hosted monitoring tool like \"Uptime Robot\"", "mautic_description": "Open-source marketing automation platform", "Patchstack Subscriptions": "Patchstack Subscriptions", "List of Patchstack Subscriptions": "List of Patchstack Subscriptions", "Find all the patchstack subscriptions associated with your account here.": "Find all the patchstack subscriptions associated with your account here.", "Vulnerability Shield Pro is active!": "Vulnerability Shield Pro is active!", "Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.": "Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.", "WordPress Core.": "WordPress Core.", "Get faster security alerts with Vulnerability Shield Pro powered by": "Get faster security alerts with Vulnerability Shield Pro powered by", "Patchstack - only $4/month.": "Patchstack - only $4/month.", "Upgrade to PRO": "Upgrade to PRO", "Canceling...": "Canceling...", "Vulnerability Shield Pro": "Vulnerability Shield Pro", "Powered by Patchstack": "Powered by <PERSON><PERSON><PERSON>", "Stay protected with continuous vulnerability scans and advanced threat alerts to secure your WordPress website like a pro.": "Stay protected with continuous vulnerability scans and advanced threat alerts to secure your WordPress website like a pro.", "mo": "mo", "Upgrade to Pro NOW": "Upgrade to Pro NOW", "Remove Subscription": "Remove Subscription", "Removing...": "Removing...", "Are you sure you want to remove the subscription for this site?": "Are you sure you want to remove the subscription for this site?", "Once confirm, this cannot be undone.": "Once confirm, this cannot be undone.", "Patchstack - only": "Patchstack - only", "/month.": "/month.", "Subscription Status": "Subscription Status", "Export Sites Data": "Export Sites Data", "Export Servers Data": "Export Servers Data", "Export": "Export", "Export site data in your preferred format by selecting the desired export type, format, and data columns.": "Export site data in your preferred format by selecting the desired export type, format, and data columns.", "Export server data in your preferred format by selecting the desired export type, format, and data columns.": "Export server data in your preferred format by selecting the desired export type, format, and data columns.", "Select Export Type": "Select Export Type", "Choose what to export": "Choose what to export", "Excel (XLSX)": "Excel (XLSX)", "CSV": "CSV", "Select Columns": "Select Columns", "Exporting...": "Exporting...", "Export Format": "Export Format", "Issues": "Issues", "No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.": "No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.", "Excellent! Your WordPress Core is fully secure!": "Excellent! Your WordPress Core is fully secure!", "Excellent! Your plugins are fully secure!": "Excellent! Your plugins are fully secure!", "Excellent! Your theme is fully secure!": "Excellent! Your theme is fully secure!", "Issue": "Issue", "No issues were detected by Vulnerability Scanner. You’ll be notified immediately if any issues are found.": "No issues were detected by Vulnerability Scanner. You’ll be notified immediately if any issues are found.", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $5/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $5/mo for advanced vulnerability detection and automated patching powered by Patchstack.", "Go PRO for": "Go PRO for", "Upgrade to Vulnerability Shield Pro for automated vulnerability detection & virtual patching powered by Patchstack": "Upgrade to Vulnerability Shield Pro for automated vulnerability detection & virtual patching powered by Patchstack", "Output": "Output", "Shield Enabled": "Shield Enabled", "Enable Shield": "Enable Shield", "Vulnerability Shield": "Vulnerability Shield", "Pro": "Pro", "Enable Vulnerability Shield Pro to secure this site with automated virtual patching and faster notification alerts.": "Enable Vulnerability Shield Pro to secure this site with automated virtual patching and faster notification alerts.", "Pay now for": "Pay now for", "Continue free plan": "Continue free plan", "I am not interested at this moment. Please, do not show this message again.": "I am not interested at this moment. Please, do not show this message again.", "The plugin has a vulnerability that makes it possible for unauthorized actions.": "The plugin has a vulnerability that makes it possible for unauthorized actions.", "This vulnerability affects": "This vulnerability affects", "We have discovered security vulnerabilities in the": "We have discovered security vulnerabilities in the", "These": "These", "have vulnerabilities that make it possible for unauthorized actions.": "have vulnerabilities that make it possible for unauthorized actions.", "The plugins have vulnerabilities that make it possible for unauthorized actions.": "The plugins have vulnerabilities that make it possible for unauthorized actions.", "We have discovered security vulnerabilities in": "We have discovered security vulnerabilities in", "and a few more plugins that you are using on the following website:": "and a few more plugins that you are using on the following website:", "We detected this vulnerability on": "We detected this vulnerability on", "UTC. You can ignore this message if you have already taken care of it.": "UTC. You can ignore this message if you have already taken care of it.", "Thank you for being a": "Thank you for being a", "user!": "user!", "If you have any questions, don't hesitate to contact our": "If you have any questions, don't hesitate to contact our", "support team": "support team", "Thank you for being a xCloud user!": "Thank you for being a xCloud user!", "We found a security vulnerability in your Website": "We found a security vulnerability in your Website", "Scanning Now": "Scanning Now", "Last Scanned": "Last Scanned", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $": "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $", "/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "/mo for advanced vulnerability detection and automated patching powered by Patchstack.", "Node Configuration": "Node Configuration"}