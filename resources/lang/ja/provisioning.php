<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'カードの確認と支払いの受け取り',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'カードの確認と支払いの受け取り $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'サーバーの作成',
            'tasks' => [
                ServerProvisioning::INIT => 'サーバープロビジョニングの初期化',
                ServerProvisioning::CREATING_SERVER => ':provider にサーバーを作成中',
                ServerProvisioning::SERVER_CREATED => 'サーバーが作成されました :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'サーバーの起動を待機中',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'サーバーに接続中',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'SSHに接続中',
                ServerProvisioning::CONNECTED => '接続が確立しました',
            ]
        ],
        [
            'stage' => 'サーバーの設定',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'スワップファイルの設定',
                ServerProvisioning::UPGRADING_SYSTEM => 'システムのアップグレード',
                ServerProvisioning::INSTALLING_BASE => '基本依存関係のインストール',
                ServerProvisioning::AUTHENTICATION_METHOD => '認証方法の更新',
                ServerProvisioning::UPDATING_HOSTNAME => 'ホスト名の更新',
                ServerProvisioning::UPDATING_TIMEZONE => 'タイムゾーンの更新',
                ServerProvisioning::XCLOUD_USER => 'ユーザーの設定',

                ServerProvisioning::SETUP_SSH => 'SSHの設定',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Sudo権限の設定',
                ServerProvisioning::SETTING_UP_GIT => 'Gitの設定',
                ServerProvisioning::SETUP_FIREWALL => 'ファイアウォールの設定',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'クリーンスクリプトの設定',
            ]
        ],
        [
            'stage' => 'アプリのインストール',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'PHP :php_version のインストール',
                ServerProvisioning::INSTALLING_WEBSERVER => ':stack のWebサーバーをインストール',
                ServerProvisioning::INSTALLING_NODE => 'Nodeのインストール',
                ServerProvisioning::INSTALLING_REDIS => 'Redisのインストール',
                ServerProvisioning::INSTALLING_DATABASE => ':database_type データベースのインストール',
                ServerProvisioning::INSTALLING_WP_CLI => 'WP CLIのインストール',
            ]
        ],
        [
            'stage' => '完了',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'SSH権限の設定',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => '監視スクリプトのインストール',
                ServerProvisioning::READY_TO_DO_MAGIC => 'マジックの準備完了！',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => '確認と検証',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'サーバーのストレージと接続を確認中',
                SiteProvisioning::VERIFYING_DNS => 'サイトのDNSを確認中',
            ]
        ],
        [
            'stage' => 'アプリのインストール',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => ':site_stack :site_stack_version をインストールしています',
                SiteProvisioning::INSTALLING_DATABASE => 'データベースをインストールしています',
                SiteProvisioning::INSTALLING_WORDPRESS => ':type をセットアップしています',
            ]
        ],
        [
            'stage' => '設定',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'SSLの設定',
                SiteProvisioning::CONFIGURING_HTTPS => 'HTTPSの設定',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => ':cache の設定',
                SiteProvisioning::CONFIGURING_NGINX => ':stack の設定',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Redisオブジェクトキャッシュの設定',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Blueprintのインストール',
                SiteProvisioning::DEPLOY_SCRIPT => 'デプロイスクリプト',
                SiteProvisioning::INSTALL_MONITORING => '監視のインストール',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'WP Cronジョブのインストール',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'xCloud管理メールサービスの設定',
                SiteProvisioning::HANDLE_INDEXING => 'サイトのインデックス処理',
                SiteProvisioning::FINISHING_UP => '完了',
            ]
        ]
    ],
];
