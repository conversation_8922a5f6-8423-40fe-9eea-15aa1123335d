<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'Verifi<PERSON><PERSON> van <PERSON> & Betaling verwerken',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'Veri<PERSON><PERSON><PERSON> van de ka<PERSON> en het verwerken van de betaling van $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'Server aanmaken',
            'tasks' => [
                ServerProvisioning::INIT => 'Initialisatie van server provisioning',
                ServerProvisioning::CREATING_SERVER => 'Server aanmaken op :provider',
                ServerProvisioning::SERVER_CREATED => 'Server aangemaakt :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'Wachten tot de server start',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'Verbinden met de server',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'Verbinden met SSH',
                ServerProvisioning::CONNECTED => 'Verbinding tot stand gebracht',
            ]
        ],
        [
            'stage' => 'Server configureren',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'Swapfile configureren',
                ServerProvisioning::UPGRADING_SYSTEM => 'Systeem upgraden',
                ServerProvisioning::INSTALLING_BASE => 'Basisafhankelijkheden installeren',
                ServerProvisioning::AUTHENTICATION_METHOD => 'Authenticatie methode bijwerken',
                ServerProvisioning::UPDATING_HOSTNAME => 'Hostname bijwerken',
                ServerProvisioning::UPDATING_TIMEZONE => 'Tijdzone bijwerken',
                ServerProvisioning::XCLOUD_USER => 'Gebruikers instellen',

                ServerProvisioning::SETUP_SSH => 'SSH instellen',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Sudo rechten instellen',
                ServerProvisioning::SETTING_UP_GIT => 'Git instellen',
                ServerProvisioning::SETUP_FIREWALL => 'Firewall instellen',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'Cleaning script instellen',
            ]
        ],
        [
            'stage' => 'Apps installeren',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'PHP :php_version installeren',
                ServerProvisioning::INSTALLING_WEBSERVER => ':stack webserver installeren',
                ServerProvisioning::INSTALLING_NODE => 'Node installeren',
                ServerProvisioning::INSTALLING_REDIS => 'Redis installeren',
                ServerProvisioning::INSTALLING_DATABASE => ':database_type database installeren',
                ServerProvisioning::INSTALLING_WP_CLI => 'WP CLI installeren',
            ]
        ],
        [
            'stage' => 'Afwerken',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'SSH rechten instellen',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => 'Monitoring script installeren',
                ServerProvisioning::READY_TO_DO_MAGIC => 'Klaar om magie te doen!',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => 'Controleren en verifiëren',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'Server opslag en verbinding controleren',
                SiteProvisioning::VERIFYING_DNS => 'DNS voor je site verifiëren',
            ]
        ],
        [
            'stage' => 'Apps installeren',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => 'PHP :php_version installeren',
                SiteProvisioning::INSTALLING_DATABASE => 'Database installeren',
                SiteProvisioning::INSTALLING_WORDPRESS => 'WordPress instellen',
            ]
        ],
        [
            'stage' => 'Configureren',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'SSL configureren',
                SiteProvisioning::CONFIGURING_HTTPS => 'HTTPS configureren',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => ':cache configureren',
                SiteProvisioning::CONFIGURING_NGINX => ':stack configureren',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Redis object cache configureren',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Blueprint installeren',
                SiteProvisioning::DEPLOY_SCRIPT => 'Deploy script',
                SiteProvisioning::INSTALL_MONITORING => 'Monitoring installeren',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'WP Cron job installeren',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'xCloud beheerde e-mailservice instellen',
                SiteProvisioning::HANDLE_INDEXING => 'Site-indexering beheren',
                SiteProvisioning::FINISHING_UP => 'Afwerken',
            ]
        ]
    ],
];
