<?php

use App\Enums\SiteStatus;
use App\Services\Migration\SiteMigrating;

return [
    'init' => [
        [
            'stage' => 'Controleren en Verifiëren',
            'tasks' => [
                SiteMigrating::CHECKING_STATUS => 'Serveropslag & verbinding controleren',
            ]
        ],
        [
            'stage' => 'Apps Installeren',
            'tasks' => [
                SiteMigrating::INSTALLING_PHP => 'PHP installeren :php_version',
                SiteMigrating::INSTALLING_DATABASE => 'Database installeren',
                SiteMigrating::INSTALLING_SITE => 'Nieuwe website instellen',
                SiteMigrating::UPDATING_PERMISSIONS => 'Rechten bijwerken',
            ]
        ],
    ],
    'no_db_init' => [
        [
            'stage' => 'Controleren en Verifiëren',
            'tasks' => [
                SiteMigrating::CHECKING_STATUS => 'Serveropslag & verbinding controleren',
            ]
        ],
        [
            'stage' => 'Apps Installeren',
            'tasks' => [
                SiteMigrating::INSTALLING_PHP => 'PHP installeren :php_version',
                SiteMigrating::INSTALLING_SITE => 'Nieuwe website instellen',
                SiteMigrating::UPDATING_PERMISSIONS => 'Rechten bijwerken',
            ]
        ],
    ],
    'manual_init' => [
        [
            'stage' => 'Controleren en Verifiëren',
            'tasks' => [
                SiteMigrating::CHECKING_STATUS => 'Serveropslag & verbinding controleren',
            ]
        ],
        [
            'stage' => 'Apps Installeren',
            'tasks' => [
                SiteMigrating::INSTALLING_PHP => 'PHP installeren :php_version',
                SiteMigrating::INSTALLING_DATABASE => 'Database installeren',
                SiteMigrating::CREATE_SITE_DIRECTORY => 'Site directory aanmaken',
                SiteMigrating::MAKING_SITE_DIRECTORY_READY => 'Site directory gereedmaken',
                SiteMigrating::MAKING_DATABASE_READY => 'Database gereedmaken',
                SiteMigrating::FETCHING_SITE_URL => 'Site URL ophalen',
                SiteMigrating::UPDATING_PERMISSIONS => 'Rechten bijwerken',
            ]
        ],
    ],
    'files' => [
        [
            'stage' => 'Bestanden Migreren',
            'tasks' => [
                SiteMigrating::SCANING_FILESYSTEM => 'Scannen naar bestanden',
                SiteMigrating::FOUND_FILES => ':found_file_count Bestanden gevonden :found_file_size',
                SiteMigrating::MIGRATING_FILES => 'Bestanden migreren :file_migration_percentage',
                //SiteMigrating::RE_VERIFYING_FILES => 'Bestanden opnieuw verifiëren :re_verified_file_count',
                //SiteMigrating::RETRYING_FAILED_FILES => 'Mislukte bestanden opnieuw proberen :file_retry_percentage',
                SiteMigrating::FILE_MIGRATION_FINISHED => 'Bestandsmigratie voltooid',
            ]
        ]
    ],
    'db' => [
        [
            'stage' => 'Database Migreren',
            'tasks' => [
                SiteMigrating::SCANING_DB => 'Scannen naar databases',
                SiteMigrating::FOUND_DB => ':found_table_count Tabellen & :found_row_count Rijen gevonden',
                SiteMigrating::MIGRATING_DB => 'DB migreren :db_migration_percentage',
                SiteMigrating::DB_MIGRATION_FINISHED => 'Database migratie voltooid',
            ]
        ],
    ],
    'config' => [
        [
            'stage' => 'Configureren',
            'tasks' => [
                SiteMigrating::VERIFYING_DNS => 'DNS verifiëren voor uw site',
                SiteMigrating::CONFIGURING_HTTPS => 'HTTPS configureren',
                SiteMigrating::CONFIGURING_FULL_PAGE_CACHE => 'Configureren :cache',
                SiteMigrating::CONFIGURING_REDIS_CACHE => 'Redis Object Cache configureren',
                SiteMigrating::CONFIGURING_NGINX => 'Configureren :stack',
                SiteMigrating::INSTALLING_MONITORING => 'Monitoring installeren',
                SiteMigrating::FINISHING_UP => 'Afronden',
            ]
        ]
    ],
    'no_db_config' => [
        [
            'stage' => 'Configureren',
            'tasks' => [
                SiteMigrating::VERIFYING_DNS => 'DNS verifiëren voor uw site',
                SiteMigrating::CONFIGURING_HTTPS => 'HTTPS configureren',
                SiteMigrating::CONFIGURING_NGINX => 'Nginx configureren',
                SiteMigrating::INSTALLING_MONITORING => 'Monitoring installeren',
                SiteMigrating::FINISHING_UP => 'Afronden',
            ]
        ]
    ]
];
