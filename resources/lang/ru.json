{"xCloud": "xCloud", "xcloud": "xCloud", "Log In To": "Войти в", "Email": "Электронная почта", "Password": "Пароль", "forgot_password": "Забыли пароль", "Remember me": "Запомнить меня", "Log In": "Войти", "Not Registered Yet? Please": "Ещё не зарегистрированы? Пожалуйста,", "Sign Up": "Регистрация", "Email Address..": "Адрес электронной почты", "Sign Up For": "Зарегистрироваться для", "Enter necessary informations for creating a new account": "Введите необходимые данные для создания новой учетной записи.", "Name": "Имя", "Use 8 or more characters with a mix of letters, numbers & symbols": "Используйте 8 или более символов, включая буквы, цифры и специальные знаки.", "Password Confirmation": "Подтверждение пароля", "I agree to the": "Я согласен с", "Terms": "Условия", "and": "и", "Privacy Policy": "Политика конфиденциальности", "Already have an account? Please": "Уже есть аккаунт? Пожалуйста,", "Forgot Password": "Забыли пароль", "Please enter your email address to search for your account": "Введите адрес электронной почты, чтобы найти ваш аккаунт", "Reset Password": "Сбросить пароль", "This is a secure area of the application. Please confirm your password before continuing.": "Это защищенная область приложения. Пожалуйста, подтвердите свой пароль, прежде чем продолжить.", "Confirm": "Подтвердить", "Confirm Password": "Подтвердите пароль", "Two-factor Confirmation": "Двухфакторное подтверждение", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "Пожалуйста, подтвердите доступ к вашему аккаунту, введя код аутентификации, предоставленный вашим приложением-аутентификатором.", "Please confirm access to your account by entering one of your emergency recovery codes.": "Пожалуйста, подтвердите доступ к вашему аккаунту, введя один из ваших аварийных кодов восстановления.", "Code": "<PERSON>од", "Recovery Code": "Код восстановления", "Use a recovery code": "Использовать код восстановления", "Use an authentication code": "Используйте код аутентификации", "Verify Email Address": "Подтвердите адрес электронной почты", "To continue using": "Чтобы продолжить использование", "please click on the link in the verification email sent to your email.": "Пожалуйста, нажмите на ссылку в письме для подтверждения, отправленном на вашу электронную почту.", "A new verification link has been sent to the email address you provided during registration.": "На указанный вами при регистрации адрес электронной почты отправлена новая ссылка для подтверждения.", "Please wait": "Пожалуйста, подождите", "seconds to resend email.": "секунд до повторной отправки письма", "Resend Verification Email": "Отправить письмо для подтверждения повторно", "Logout": "Выход", "Overview": "Обзор", "New Server": "Новый сервер", "New Site": "Новый сайт", "Active Servers": "Активные серверы", "Total Sites": "Всего сайтов", "No Active Billing": "Нет активных платежей", "Plan": "<PERSON><PERSON><PERSON><PERSON>", "Vulnerable Sites": "Уязвимые сайты", "New Team Invitations": "Приглашения в новую команду", "Server Setup In Progress": "Настройка сервера в процессе", "Site Setup In Progress": "Настройка сайта в процессе", "View All": "Просмотреть все", "You can create 1 server and 10 sites with our Free Plan.": "С бесплатным планом вы можете создать 1 сервер и 10 сайтов.", "Two-Factor Authentication": "Двухфакторная аутентификация", "You have not enabled two-factor authentication, which is highly recommended for your account security.": "Вы не включили двухфакторную аутентификацию, что настоятельно рекомендуется для безопасности вашей учетной записи.", "Skip": "Пропустить", "Setup Now": "Настроить сейчас", "Please check your": "Пожалуйста, проверьте ваш", "invoices": "Счета", "has asked you to join as a": "пригла<PERSON>ил(а) вас присоединиться в качестве", "to this team": "этой команде", "Decline": "Отклонить", "Accept": "Принять", "To decline the invitation please visit": "Чтобы отклонить приглашение, пожалуйста, посетите", "team page": "Страница команды", "Server Disk Space Low": "Мало места на диске сервера", "Warning": "Предупреждение", "Your Server": "Ваш сервер", "disk space is low. Please upgrade your plan to avoid any downtime.": "Мало места на диске. Пожалуйста, обновите план, чтобы избежать простоев.", "Reboot Required": "Требуется перезагрузка", "Security Update": "Обновление безопасности", "will automatically update and reboot on": "Автоматически обновится и перезагрузится в", "You can also reboot now.": "Вы также можете перезагрузить сейчас.", "Security Update - Server": "Обновление безопасности - Сервер", "requires a reboot.": "Требуется перезагрузка.", "Reboot Now": "Перезагрузить сейчас", "Security updates were automatically installed on your server, but a reboot is required to complete the installation. The following packages were upgraded:": "На ваш сервер автоматически установлены обновления безопасности, но для завершения установки требуется перезагрузка. Были обновлены следующие пакеты:", "Be sure to test the sites on your server after rebooting.": "После перезагрузки сервера обязательно протестируйте сайты.", "Server Size": "Размер сервера", "PHP": "PHP", "View Details": "Просмотр деталей", "Server": "Сервер", "Continue Setup": "Продолжить настройку", "View Site": "Просмотр сайта", "Open options": "Открыть настройки", "Bill Calculator": "Калькуля<PERSON><PERSON><PERSON> счетов", "Billing Amount": "Сумма к оплате", "Select Renewal Period": "Выберите период продления", "Bill From": "Счет от", "Bill Before": "Счет до", "Bill To": "Плательщик", "Processing payment... Please do not cancel or refresh the page.": "Обработка платежа... Пожалуйста, не отменяйте и не обновляйте страницу.", "My Blueprints": "Мои чертежи", "Create New Blueprint": "Создать новый чертёж", "Created": "Создано", "Active Plugins": "Активные плагины", "Active Theme": "Активная тема", "Edit Blueprint": "Редактировать шаблон", "Create Blueprint": "Создать чертёж", "By": "От", "Active Installations": "Активные установки", "Update": "Обновить", "Create": "Создать", "Staging Url": "URL для тестирования", "Confirm Details": "Подтвердить данные", "Edit": "Редактировать", "Old Domain": "Старый домен", "HTTPS": "HTTPS", "Enabled": "Включено", "Disabled": "Отключено", "PHP Version": "Версия PHP", "Database Type": "Тип базы данных", "Database Name": "Имя базы данных", "Database User": "Пользователь базы данных", "No Database": "Нет базы данных", "Site User": "Пользователь сайта", "Configuration & Database Connection": "Конфигурация и Подключение к Базе Данных", "Create Database In Server": "Создать базу данных на сервере", "Select Destination Server To Clone Your Site": "Выберите сервер назначения для клонирования вашего сайта", "Choose Server": "Выберите сервер", "DNS & SSL For": "DNS и SSL для", "Demo Site": "Демонстрационный сайт", "Create a demo site with our test domain and customize before going live.": "Создайте демонстрационный сайт с нашим тестовым доменом и настройте его перед запуском.", "Migrate into a New Domain": "Перейти на новый домен", "Get your site up and running for the world to see by simply pointing your domain to the server.": "Запустите свой сайт, просто направив домен на сервер, чтобы его увидел весь мир.", "Add DNS and SSL Certificate on Cloudflare": "Добавить DNS и SSL-сертификат на Cloudflare", "Integrate Cloudflare for Automatic DNS and SSL management.": "Интегрируйте Cloudflare для автоматического управления DNS и SSL.", "DNS Setup": "Настройка DNS", "Settings & Configurations": "Настройки и Конфигурации", "Deploy Script (Optional)": "Развернуть скрипт (необязательно)", "wp plugin install woocommerce": "Установить плагин WooCommerce", "Verifying": "Проверка", "Uploading....": "Загрузка...", "Upload/Drop Your Zipped sql or only .sql File": "Загрузите или перетащите ваш архив .zip или файл .sql", "Maximum File Size: 500 MB": "Максимальный размер файла: 500 МБ", "Use These Credentials To Transfer Database": "Используйте эти учетные данные для передачи базы данных", "Server Connection Url": "URL подключения к серверу", "Server Address": "Адрес сервера", "Server SSH Host": "Сервер SSH Хост", "Server Username": "Имя пользователя сервера", "Server database name": "Имя базы данных сервера", "Database Password": "Пароль базы данных", "Verify Upload": "Проверить загрузку", "Upload/Drop Your Zipped/Tar File": "Загрузите/Перетащите ваш файл в формате ZIP/TAR", "Use These Credentials To Upload Your WordPress Website": "Используйте эти учетные данные для загрузки вашего сайта WordPress", "22": "22", "Port": "Порт", "Remote Path": "Удалённый путь", "Upload Migration Files": "Загрузить файлы миграции", "Choose a method from below to install the": "Выберите метод установки из предложенных ниже", "Migration Plugin": "Плагин миграции", "Optional": "Необязательно", "Authentication Token": "То<PERSON><PERSON>н аутентификации", "Plugin Page URL": "URL страницы плагина", "I have added the authentication token to my site": "Я добавил токен аутентификации на свой сайт", "Please agree that you have added the authentication token to your site": "Пожалуйста, подтвердите, что вы добавили токен аутентификации на ваш сайт.", "Download Plugin ZIP File": "Скачать ZIP-файл плагина", "Plugin Link": "Ссылка на плагин", "WP CLI": "WP CLI", "Show Previous cPanel Migrations": "Показать предыдущие миграции cPanel", "Username of the cPanel account:": "Имя пользователя учетной записи cPanel:", "Enter your cPanel username": "Введите имя пользователя cPanel", "cPanel Api Key:": "Ключ API cPanel:", "Enter your cPanel API Key": "Введите ключ API для cPanel", "cPanel Host:": "Хост cPanel:", "Fetch Existing Backups": "Получить существующие резервные копии", "Select a backup to import from the list below": "Выберите резервную копию для импорта из списка ниже", "Backup in Progress ...": "Резервное копирование...", "No Backups Found": "Резервные копии не найдены", "You need to create a backup in your cPanel account first": "Сначала создайте резервную копию в вашем аккаунте cPanel.", "Generate Backup": "Создать резервную копию", "Migration File": "Файл миграции", "Created At": "Создано", "Actions": "Действия", "Continue Migration": "Продолжить миграцию", "No cPanel Migrations Found": "Миграции cPanel не найдены", "You can create a new cPanel Migration from the cPanel Migration tab": "Вы можете создать новую миграцию cPanel на вкладке «Миграция cPanel».", "Upload Completed": "Загрузка завершена", "Upload/Drop Your Zipped File": "Загрузите/Перетащите ваш ZIP-файл", "Regenerate cPanel User": "Восстановить пользователя cPanel", "I have started the backup": "Я начал резервное копирование", "Waiting for the backup to complete": "Ожидание завершения резервного копирования", "Currently transferring this file from generated backup for cpanel migration": "Выполняется перенос файла из созданной резервной копии для миграции cPanel", "New cPanel Migration using SCP found, click here to continue": "Обнаружена новая миграция cPanel с использованием SCP, нажмите здесь, чтобы продолжить", "Live Site": "Рабочий сайт", "Domain Setup": "Настройка домена", "Domain Name": "Имя домена", "Auto DNS management by Cloudflare is disabled for Full Server Migration.": "Автоматическое управление DNS от Cloudflare отключено для полной миграции сервера.", "Coming soon...": "Скоро будет...", "Your DNS setup and SSL Certificate will be done by Cloudflare and managed by xCloud.": "Настройка DNS и SSL-сертификат будут выполнены Cloudflare и управляться xCloud.", "Confirm Migration Details": "Подтвердите детали миграции", "Source site": "Исходный сайт", "Destination site": "Целевой сайт", "Select Websites to Migrate": "Выберите сайты для переноса", "to": "в", "Previously migrated sites from": "Ранее перенесённые сайты из", "Available Sites": "Доступные сайты", "Select All": "Выбрать все", "Fetch Websites": "Получить сайты", "Fetching...": "Получение...", "Wordpress Sites To Migrate": "Сайты WordPress для миграции", "Type": "Тип", "Directory": "Каталог", "Non-Wordpress Sites": "Сайты не на WordPress", "Fetching sites...": "Загрузка сайтов...", "Add Websites to Migrate": "Добавить сайты для миграции", "Add the domains you want to migrate from your Cpanel backup, we will search these domains and migrate to the new server": "Добавьте домены, которые хотите перенести из резервной копии Cpanel. Мы найдем эти домены и перенесем их на новый сервер.", "Add Website": "Добавить сайт", "Connect Your Source Server": "Подключите сервер источника", "Submit": "Отправить", "Select your Hosting Provider": "Выберите вашего хостинг-провайдера", "cPanel Migration Method": "Метод миграции cPanel", "Set domain for migration": "Установить домен для миграции", "Full Server Migration In Progress from": "Полная миграция сервера в процессе", "Migration completed successfully from": "Миграция успешно завершена из", "Successfully Migrated Sites": "Успешно перенесённые сайты", "Migration In Progress": "Миграция в процессе", "Migration In Queue": "Миграция в очереди", "Migration Failed": "Миграция не удалась", "IP Address": "IP-адрес", "SSH Port": "Порт SSH", "SSH Username": "Имя пользователя SSH", "root": "Корень", "SSH Authentication": "Аутентификация SSH", "SSH Password": "Пароль SSH", "Size": "Размер", "GB": "Великобритания", "Existing Site": "Существующий сайт", "Full Page Cache": "Кэширование всей страницы", "Redis Object Cache": "Кэш объектов Redis", "Default Database In Server": "База данных по умолчанию на сервере", "Database": "База данных", "Admin User Name": "Имя пользователя администратора", "Files and Database Migration": "Миграция файлов и базы данных", "Select Databases & File Systems To Migrate": "Выберите базы данных и файловые системы для миграции", "Migrate The Following Content": "Перенести следующий контент", "All Database Tables & Corresponding File System": "Все таблицы базы данных и соответствующая файловая система", "Only File System But NOT Database Tables": "Только файловая система, но НЕ таблицы базы данных", "Select Destination Server To Migrate Your Site": "Выберите сервер назначения для миграции вашего сайта", "Existing WordPress Site URL": "URL существующего сайта WordPress", "Existing Site URL": "URL существующего сайта", "Git Repository": "Git-репозиторий", "master": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Git Branch": "Ветка Git", "Enable push to deploy": "Включить push для развертывания", "Deployment URL": "URL развертывания", "npm run deploy": "npm run deploy", "Deploy Script": "Развернуть скрипт", "Run this script after every site deployment": "Запустите этот скрипт после каждого развертывания сайта", "Select Databases To Migrate": "Выберите базы данных для миграции", "Add Your Existing Database": "Добавить существующую базу данных", "Without Database": "Без базы данных", "Install Migration Plugin": "Установить плагин миграции", "Buy Now": "Купить сейчас", "Packages": "Пакеты", "Products": "Продукты", "Bills & Payment": "Счета и Оплата", "Free plan which includes 1 server and 10 website with Self Hosting.": "Бесплатный план, включающий 1 сервер и 10 сайтов с самостоятельным хостингом.", "You’re on the": "Вы на", "Activate your": "Актив<PERSON><PERSON><PERSON><PERSON><PERSON>е ваш", "team by adding payment method today.": "Добавьте способ оплаты в команду сегодня.", "team by adding payment method.": "Добавьте способ оплаты для команды.", "Estimated Cost": "Ориентировочная стоимость", "This is an estimate of the amount based on your current month-to-date": "Это оценка суммы на основе текущих данных за месяц", "Cost This Month": "Стоимость в этом месяце", "Cost Next Month": "Стоимость в следующем месяце", "Overused Amount After 28th": "Превышение после 28-го", "Billing Period Monthly": "Платежный период: Ежем<PERSON>сячно", "renews 29": "Обновляется 29", "Read how billing works.": "Узнайте, как работает выставление счетов.", "Subscriptions": "Подписки", "xCloud Managed Email Provider": "xCloud Управляемый Провайдер Электронной Почты", "Services": "Услуги", "Payment Methods": "Способы оплаты", "You can add Credit/Debit Card as your payment method": "Вы можете добавить кредитную/дебетовую карту в качестве способа оплаты", "Expires at": "Срок действия истекает:", "Default": "По умолчанию", "Set As Default": "Установить по умолчанию", "Add A Payment Method": "Добавить способ оплаты", "Billing History": "История выставления счетов", "Description": "Описание", "Date": "Дата", "Service Status": "Статус сервиса", "Amount": "Сумма", "Payment Status": "Статус платежа", "Next Billing": "Следующий счет", "Renewal Period": "Период продления", "Link a Credit/Debit Card": "Привязать кредитную/дебетовую карту", "We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)": "Мы принимаем Visa, Mastercard, American Express, Discover, Diners Club, а также China UnionPay (CUP) и Japan Credit Bureau (JCB).", "Service": "Сервис", "Amount Payable": "Сумма к оплате", "discontinued on": "Снято с производства", "to lifetime": "На весь срок службы", "I want to convert this bill to monthly and unlock full features": "Я хочу перевести этот счет на ежемесячный и разблокировать все функции", "Please add a": "Пожалуйста, добавьте", "payment": "Оплата", "method unlock more features.": "Метод разблокировки дополнительных функций.", "Amount Paid": "Сумма оплаты", "Whitelabel Subscriptions": "Белые Метки Подписки", "Current Plan": "Текущий план", "Sell Up to": "Продать до", "Servers": "Серверы", "Subscription Date": "Дата подписки", "Expires on": "Срок действия истекает:", "Your subscription is about to expire please renew it": "Срок вашей подписки истекает, пожалуйста, продлите её.", "Your subscription is about to expire please renew it for uninterrupted service.": "Срок вашей подписки скоро истекает. Пожалуйста, продлите её для бесперебойного обслуживания.", "Pay Now": "Оплатить сейчас", "No active subscription found": "Активная подписка не найдена", "Claim Free with LTD": "Получите бесплатно с LTD", "Server Limit": "Лимит сервера", "As an existing LTD customer, you are eligible to claim this plan for free.": "Как существующий клиент LTD, вы можете получить этот план бесплатно.", "Switch Free with LTD": "Переключиться на бесплатную версию с LTD", "Switch to this plan for": "Переключиться на этот план за", "Choose a plan": "Выберите план", "SMTP Username": "Имя пользователя SMTP", "SMTP Password": "Пароль SMTP", "Domain": "До<PERSON><PERSON>н", "Sendgrid Username": "Имя пользователя Sendgrid", "Sendgrid Api Key": "Ключ API Sendgrid", "SMTP Host": "SMTP хост", "SMTP Port": "Порт SMTP", "Label": "Метка", "Get help from our": "Получите помощь от нашего", "Configure SMTP Provider": "Настроить SMTP-провайдер", "documentation": "Документация", "From Email": "От кого (Email)", "To Email": "На электронную почту", "Email Subscriptions": "Подписки на электронную почту", "With xCloud you get 100 free emails per month in each team. You can purchase additional email more from": "С помощью xCloud вы получаете 100 бесплатных писем в месяц для каждой команды. Вы можете приобрести дополнительные письма у", "here.": "здесь", "xCloud Email Balance": "Баланс электронной почты xCloud", "This is a summary of your xCloud Email Balance.": "Это сводка вашего баланса xCloud Email.", "Total Emails": "Всего писем", "Emails Available": "Доступные электронные письма", "Elastic Email API Key": "Ключ API Elastic Email", "Invoice": "Счет-фактура", "Details": "Детали", "You will pay for following bills": "Вы оплатите следующие счета", "which are under": "которые находятся под", "Next Billing Amount": "Сумма следующего счета", "Amount Adjusted": "Сумма скорректирована", "Add New Member": "Добавить нового участника", "Send Invitation": "Отправить приглашение", "Server Access": "Доступ к серверу", "Access to all server": "Доступ ко всем серверам", "Choose specific server": "Выберите конкретный сервер", "Select here...": "Выберите здесь...", "Site Access": "Доступ к сайту", "Access to all site": "Доступ ко всему сайту", "Choose specific site": "Выберите конкретный сайт", "Role Permission": "Разрешение роли", "Create New Team": "Создать новую команду", "Save Changes": "Сохранить изменения", "Upload Your Avatar": "Загрузите аватар", "Add a profile picture or icon for your account": "Добавьте фото профиля или значок для вашего аккаунта", "Upload Image": "Загрузить изображение", "Team Name": "Название команды", "Team Email": "Командная почта", "Tags": "Теги", "Select or create tags": "Выберите или создайте теги", "Edit Team": "Редактировать команду", "Update Member": "Обновить участника", "Update Invitation": "Приглашение на обновление", "Email ID": "Адрес электронной почты", "User Role": "Роль пользователя", "Delete Team": "Удалить команду", "Leave Team": "Покинуть команду", "Team Members": "Участники команды", "Role": "Роль", "Status": "Статус", "Server Count": "Количество серверов", "Action": "Действие", "Change Role": "Изменить роль", "Delete": "Удалить", "Resend Email": "Отправить письмо повторно", "Site": "Сайт", "Switch to Team": "Переключиться на команду", "Add Member": "Добавить участника", "Check Details": "Проверить детали", "All Sites": "Все сайты", "Refresh": "Обновить", "Archive Servers": "Архивные серверы", "List of Archive Servers": "Список архивных серверов", "Find all the archive servers associated with your team here.": "Найдите все архивные серверы, связанные с вашей командой, здесь.", "Provider": "Поставщик", "If you proceed, this will permanently remove this service and you will not be able to access or retrieve it again.": "Если вы продолжите, эта услуга будет удалена навсегда, и вы не сможете получить к ней доступ или восстановить её.", "Authentication": "Аутентификация", "Please enter the two-factor authentication code sent to you below to verify:": "Введите код двухфакторной аутентификации, отправленный вам, для проверки:", "Billing Details": "Платёжные данные", "Saved Cards": "Сохранённые карты", "Add your payment methods for billing.": "Добавьте способы оплаты для выставления счетов.", "Set as Default": "Установить по умолчанию", "No card available": "Карта недоступна", "Add Payment Method": "Добавить способ оплаты", "Bills": "Счета", "Find complete, downloadable receipts of your regular payments": "Найдите полные, загружаемые квитанции ваших регулярных платежей", "Title": "Заголовок", "Amount To Pay": "Сумма к оплате", "Due On": "Срок сдачи", "Next Billing Date": "Следующая дата выставления счета", "Paid On": "Оплачено", "Manage Active Browser Sessions": "Управление активными сеансами браузера", "Check which sessions are still active from different devices & browsers, and manage as needed.": "Проверьте, какие сеансы все еще активны на разных устройствах и в браузерах, и управляйте ими при необходимости.", "Note: You can log out of all your active sessions; it will also log you out of this session and you will have to log back in to continue using": "Примечание: Вы можете выйти из всех активных сеансов; это также завершит текущий сеанс, и вам нужно будет войти снова для продолжения использования.", "Log Out Of All Sessions": "Выйти из всех сеансов", "Browser Name": "Имя браузера", "Last Session": "Последняя сессия", "This device": "Это устройство", "Last active": "Последняя активность", "Cloudflare Integration": "Интеграция с Cloudflare", "List of your Cloudflare Integrations": "Список ваших интеграций с Cloudflare", "Find all the cloudflare integrations associated with your account here.": "Найдите все интеграции Cloudflare, связанные с вашей учетной записью, здесь.", "New Cloudflare Integration": "Новая интеграция с Cloudflare", "Account Email": "Электронная почта аккаунта", "API Token": "Токен API", "Global API Key": "Глобальный API-ключ", "Origin CA Key": "Ключ CA Origin", "Add New Cloudflare Integration": "Добавить новую интеграцию с Cloudflare", "Name of the integration": "Название интеграции", "Your Cloudflare Account Email": "Электронная почта вашего аккаунта Cloudflare", "Create a new API Token from your Cloudflare account and paste it here. (With Edit zone DNS permissions)": "Создайте новый API-токен в своем аккаунте Cloudflare и вставьте его здесь. (с разрешениями на редактирование зоны)", "Go to your Cloudflare Profile > API Tokens > Global API Key > View": "Перейдите в профиль Cloudflare > API токены > Глобальный API ключ > Просмотр", "Go to your Cloudflare Profile > API Tokens > Origin CA Key > View": "Перейдите в профиль Cloudflare > Токены API > Ключ Origin CA > Просмотр", "Integrate Cloudflare For DNS Management": "Интегрируйте Cloudflare для управления DNS", "Account email": "Электронная почта аккаунта", "Find complete, downloadable receipts of your regular payments.": "Найдите полные загружаемые квитанции ваших регулярных платежей.", "All Invoices": "Все счета", "This Month": "Этот месяц", "Last Month": "Прошлый месяц", "Last 6 Months": "Последние 6 месяцев", "Last 1 Year": "Последний год", "Paid Invoices": "Оплаченные счета", "Unpaid Invoices": "Неоплаченные счета", "Failed Invoices": "Неудачные счета", "Invoice No": "Номер счета", "Download": "Скачать", "Cancel": "Отмена", "Email Provider": "Поставщик электронной почты", "List of Email Providers": "Список почтовых провайдеров", "Find all the email providers associated with your account here.": "Найдите всех почтовых провайдеров, связанных с вашей учетной записью, здесь.", "Add New Provider": "Добавить нового поставщика", "Username/Domain": "Имя пользователя/Домен", "Invitation List": "Список приглашений", "Invite Users": "Пригласить пользователей", "Share your invitation code with others. You have": "Поделитесь своим кодом приглашения с другими. У вас есть", "invitation": "Приглашение", "remaining": "Осталось", "Invited Users": "Приглашённые пользователи", "You have invited following persons.": "Вы пригласили следующих пользователей.", "Accepted": "Принято", "Notifications": "Уведомления", "List of Notifications": "Список уведомлений", "Integrate your notification platform. You can customize your": "Интегрируйте вашу платформу уведомлений. Вы можете настроить ваши", "notification settings": "Настройки уведомлений", "from": "от", "here": "здесь", "Add New Notification": "Добавить новое уведомление", "Disconnect": "Отключить", "Reconnect": "Повторное подключение", "Add Notification": "Добавить уведомление", "Being with Country Code (e.g., ****** XXX XXXX for US)": "Начните с кода страны (например, ****** XXX XXXX для США)", "WhatsApp Phone Number": "Номер телефона WhatsApp", "Server Notifications": "Уведомления сервера", "For server reboots, unavailable servers, and available upgrades are included": "Для перезагрузки сервера включены недоступные серверы и доступные обновления", "Telegram": "Телеграм", "WhatsApp": "WhatsApp", "Slack": "Слэк", "Newly Provisioned Servers": "Новые настроенные серверы", "Site Notifications": "Уведомления сайта", "For site upgrades, SSL certificate issues, and deployment errors": "Для обновлений сайта, проблем с SSL-сертификатом и ошибок развертывания", "Other Notifications": "Другие уведомления", "Get notified about team accounts and actions": "Получайте уведомления о командах и действиях", "Do Not Send Sensitive Information": "Не отправляйте конфиденциальную информацию", "This option will disable sending sensitive options like, sudo password, database password, wp-admin password over email and notification channels": "Эта опция отключит отправку конфиденциальных данных, таких как пароль sudo, пароль базы данных, пароль wp-admin, по электронной почте и через каналы уведомлений.", "Vulnerability Notifications": "Уведомления о уязвимостях", "Enable this option to receive notifications about vulnerabilities via Email": "Включите эту опцию, чтобы получать уведомления о уязвимостях по электронной почте", "Stay informed and take timely action to secure your systems": "Будьте в курсе и принимайте своевременные меры для защиты ваших систем", "Team Packages": "Командные пакеты", "Renewal type": "Тип обновления", "Price Per Server": "Цена за сервер", "Total Price": "Общая стоимость", "Change Password": "Изменить пароль", "Enter a strong password to keep your profile locked": "Введите надежный пароль, чтобы защитить ваш профиль", "Current Password": "Текущий пароль", "New Password": "Новый пароль", "Pay Bills": "Оплатить счета", "Choose Your Payment Method": "Выберите способ оплаты", "You are paying for following invoice": "Вы оплачиваете следующий счет", "Total Amount": "Общая сумма", "Team Products": "Продукты команды", "Service Type": "Тип услуги", "Price": "Цена", "Role Management": "Управление ролями", "Server Provider": "Поставщик сервера", "List of Server Providers": "Список поставщиков серверов", "Find all the server providers associated with your account here.": "Найдите всех поставщиков серверов, связанных с вашим аккаунтом, здесь.", "Servers Count": "Количество серверов", "Select Server Provider": "Выберите поставщика сервера", "Vultr API Key": "Ключ API Vultr", "API Key": "Ключ API", "Hetzner API Key": "Ключ <PERSON> Hetzner", "Hetzner Label": "Мет<PERSON><PERSON>", "Upload JSON": "Загрузить JSON", "Label for AWS Credential": "Метка для учетных данных AWS", "AWS Access Key": "Ключ доступа AWS", "Access Key": "Ключ доступа", "AWS Secret Key": "Секретный ключ AWS", "Secret Key": "Секретный ключ", "Setup A Digital Ocean Server In xCloud": "Настройка сервера Digital Ocean в xCloud", "Setup A Vultr Server In xCloud": "Настройка сервера Vultr в xCloud", "Setup A GCP Server In xCloud": "Настройка сервера GCP в xCloud", "SSH Keys": "SSH ключи", "Add New SSH Key": "Добавить новый SSH-ключ", "ID": "ID", "Fingerprint": "Отпечаток пальца", "Auto Provision": "Автонастройка", "Used By": "Используется в", "A name to recognize this public key": "Имя для идентификации этого открытого ключа", "Key Name": "Имя ключа", "Public Key": "Открытый ключ", "Don't have a key?": "Нет ключа?", "Learn how to": "Узнайте, как", "generate an SSH Key": "Создать SSH-ключ", "Already have a key?": "Уже есть ключ?", "Copy and paste your key here with": "Скопируйте и вставьте ваш ключ здесь с", "Always provision to new servers": "Всегда подключайтесь к новым серверам", "Select servers to push this key to": "Выберите серверы для отправки этого ключа", "Default Sudo User": "Пользователь Sudo по умолчанию", "Default Sudo Password": "Пароль Sudo по умолчанию", "If you proceed, this will permanently remove this SSH Key and you will not be able to access or retrieve it again.": "Если вы продолжите, этот SSH-ключ будет удалён навсегда, и вы не сможете получить к нему доступ или восстановить его.", "Following sudo users will be deleted": "Следующие пользователи sudo будут удалены", "SSH key will be removed from the following sites": "SSH-ключ будет удален с следующих сайтов", "Storage Provider": "Поставщик хранилища", "List of Storage Providers": "Список поставщиков хранилища", "Bucket Name": "Имя корзины", "Region": "Регион", "Site Count": "Количество сайтов", "bucket": "Ведро", "Enter the name of the bucket you have in your storage provider.": "Введите имя корзины, которую вы используете у своего поставщика хранилища.", "Select the data location": "Выберите местоположение данных", "Select Region": "Выберите регион", "Enter the access key id here.": "Введите здесь идентификатор ключа доступа.", "Access Key Id": "Идентификатор ключа доступа", "Enter the secret key here.": "Введите секретный ключ здесь.", "Endpoint": "Конечная точка", "Enter the endpoint url here and make sure to add https:// in url.": "Введите URL конечной точки здесь и убедитесь, что добавили https:// в URL.", "Site Backup": "Резервное копирование сайта", "Enter the region here.": "Введите регион здесь.", "Team Invitations": "Приглашения в команду", "Team Management": "Управление командой", "Add New Team": "Добавить новую команду", "It looks like you haven’t created any team yet. Create one now.": "Похоже, вы еще не создали команду. Создайте её сейчас.", "Profile": "Профиль", "General Information": "Общая информация", "Set up your profile by providing the following information": "Настройте свой профиль, указав следующую информацию", "Contact Number": "Контактный номер", "Extra Billing Information": "Дополнительная информация о счете", "If you require the inclusion of specific contact or tax details on your receipts, such as your VAT identification number, or registered address, you may add it here.": "Если вам необходимо включить в чеки определенные контактные данные или налоговую информацию, такие как ваш идентификационный номер НДС или зарегистрированный адрес, вы можете добавить их здесь.", "Name on Invoice": "Имя в счете", "Billing Emails": "Платёжные письма", "If": "Если", "Send billing invoices only to the team email address": "Отправляйте счета только на командный адрес электронной почты", "is unchecked, billing invoices will be sent to the your given email addresses, separated by commas. Example:": "если не отмечено, счета будут отправлены на указанные вами адреса электронной почты, разделенные запятыми. Пример:", "remaining.": "Осталось", "You have invited the following persons.": "Вы пригласили следующих пользователей.", "Note": "Заметка", "You can again enable this service.": "Вы можете снова включить эту услугу.", "Execute": "Выполнить", "Proceeding will permanently delete all data stored on this server and all WordPress sites under it, which can’t be recovered later. Full Server Backups created for this server will also be deleted.": "Продолжение приведет к окончательному удалению всех данных, хранящихся на этом сервере, и всех сайтов WordPress под ним, которые не могут быть восстановлены. Полные резервные копии сервера, созданные для этого сервера, также будут удалены.", "Delete From Provider": "Удалить у поставщика", "Enable this to delete this server from the provider also.": "Включите это, чтобы также удалить сервер у провайдера.", "Type server name to confirm": "Введите имя сервера для подтверждения", "Delete DNS records from your Cloudflare account": "Удалить DNS-записи из вашего аккаунта Cloudflare", "Your DNS records for the sites on this server will be deleted from your Cloudflare account.": "Ваши DNS-записи для сайтов на этом сервере будут удалены из вашего аккаунта Cloudflare.", "Add site": "Добавить сайт", "sites": "Сайты", "Storage": "Храни<PERSON><PERSON><PERSON>е", "Ram": "ОЗУ", "No sites": "Нет сайтов", "Add New Site": "Добавить новый сайт", "View Sites": "Просмотр сайтов", "Restart Server": "Перезапустить сервер", "Hard Reboot Server": "Перезагрузить сервер принудительно", "Restart Nginx": "Перезапустить Nginx", "Restart LiteSpeed": "Перезапустить LiteSpeed", "Restart MySQL": "Перезапустить MySQL", "Restart MariaDB": "Перезапустить MariaDB", "Archive Server": "Архивный сервер", "Clone Server": "Клонировать сервер", "Delete Server": "Удалить сервер", "View Server": "Просмотр сервера", "Individual Site Details": "Детали отдельного сайта", "Site Name": "Название сайта", "CPU": "ЦП (Центральный процессор)", "RAM": "ОЗУ", "DISK": "ДИСК", "LAST UPDATED": "Последнее обновление", "EC2": "EC2", "Flexible, scalable virtual servers for all workloads.": "Гибкие, масштабируемые виртуальные серверы для любых задач.", "Lightsail": "Лай<PERSON><PERSON>ейл", "Easy, budget-friendly servers for small projects.": "Простые и недорогие серверы для небольших проектов.", "Set Up Your Server With": "Настройте ваш сервер с помощью", "Connect xCloud with your AWS Account": "Подключите xCloud к вашему аккаунту AWS", "Connect New Account": "Подключить новый аккаунт", "Verified": "Проверено", "Next": "Далее", "Provider Label": "Метка поставщика", "Connected": "Подключено", "Select AWS Service": "Выберите сервис AWS", "Please choose an AWS service to create an instance.": "Пожалуйста, выберите сервис AWS для создания экземпляра.", "Choose region": "Выберите регион", "Choose Region": "Выберите регион", "Choose Zone": "Выберите зону", "Loading zones...": "Зоны загрузки...", "Choose Server Size": "Выберите размер сервера", "Loading server types...": "Загрузка типов серверов...", "Select Database Server": "Выберите сервер базы данных", "Select Tags": "Выберите теги", "I have understood that the billing of this server will be handled on my server provider account.": "Я понимаю, что оплата этого сервера будет осуществляться через мой аккаунт у провайдера.", "Demo Server for Billing Plan": "Демо-сервер для тарифного плана", "Connect xCloud with your Digital Ocean account": "Подключите xCloud к вашему аккаунту Digital Ocean", "Existing DigitalOcean Credential": "Существующие учетные данные DigitalOcean", "Choose Credential": "Выберите учетные данные", "Add new credential": "Добавить новые учетные данные", "Authorize on Digital Ocean": "Авторизоваться в Digital Ocean", "You do not have permission to add new provider": "У вас нет разрешения на добавление нового поставщика", "Enable Digital Ocean": "Включить Digital Ocean", "Auto Backups": "Автоматическое резервное копирование", "Connect xCloud with your Google Cloud Platform account": "Подключите xCloud к вашему аккаунту Google Cloud Platform", "Select Existing or Connect New": "Выбрать существующее или подключить новое", "Finish": "Завершить", "Choose your project": "Выберите проект", "Choose Project": "Выберите проект", "Connect xCloud with your Hetzner Account": "Подключите xCloud к вашему аккау<PERSON><PERSON><PERSON>", "Choose Account": "Выберите аккаунт", "Hetzner API Token": "Токен <PERSON>", "Connect xCloud with your Linode (Akamai) account": "Подключите xCloud к вашему аккаунту Linode (Akamai)", "Existing Linode Credential": "Существующие учетные данные Linode", "Authorize on Linode (Akamai)": "Авторизоваться на Linode (Akamai)", "Connect xCloud with your Linode Account": "Подключите xCloud к вашему аккаунту Linode", "Vultr Label": "Метка Vultr", "Connect xCloud with your Vultr Account": "Подключите xCloud к вашему аккаунту Vultr", "Enable Vultr": "В<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "I am sure that I've read the": "Уверен, что я прочитал", "and added Any": "и добавлено Любое", "and Any": "и Любой", "both under Access Control.": "Оба под управлением контроля доступа.", "Fill in the details below to get your server set up with xCloud": "Заполните данные ниже, чтобы настроить ваш сервер с xCloud", "Note down the generated database root password above and secure it as it will not be displayed again.": "Запишите сгенерированный корневой пароль базы данных и сохраните его, так как он больше не будет отображаться.", "Server Details": "Детали сервера", "Server Tag(Optional)": "Тег сервера (необязательно)", "Server Type": "Тип сервера", "General": "Общие", "Cost-effective servers powered by Intel CPUs and regular SSDs.": "Экономичные серверы на базе процессоров Intel и обычных SSD.", "Premium": "Премиум", "Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.": "Сверхбыстрые серверы с процессорами Intel Xeon 3 ГГц+, быстрой памятью и NVMe-хранилищем.", "Billed Monthly": "Ежемесячная оплата", "SSD": "SSD", "vCPU": "vCPU", "Bandwidth": "Пропускная способность", "Recommended Option": "Рекомендуемый вариант", "Backup": "Резервное копирование", "Enable xCloud Auto Backups": "Включить автоматическое резервное копирование xCloud", "Total Cost": "Общая стоимость", "Coupon Code": "Промокод", "Apply": "Применить", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks.": "Отключение автоматического резервного копирования означает потерю легкого восстановления данных, мобильности резервных копий и защиты от сбоев. Нажмите, чтобы продолжить, если вы понимаете риски.", "Disable Auto Backup": "Отключить автозагрузку", "Choose Your Continent": "Выберите ваш континент", "Enter Code Here...": "Введите код здесь...", "Fill in the details below to get your server set up with": "Заполните данные ниже, чтобы настроить ваш сервер с", "Connect Your Existing Server": "Подключить существующий сервер", "Fill in the details below to connect xCloud with your existing fresh Ubuntu server from any cloud provider.": "Заполните данные ниже, чтобы подключить xCloud к вашему существующему серверу Ubuntu от любого облачного провайдера.", "You will need to add the xCloud public key to allow server setup access. Please SSH to the server using the default/root account and use the following command to add the public key.": "Вам необходимо добавить открытый ключ xCloud для предоставления доступа к настройке сервера. Пожалуйста, выполните SSH-подключение к серверу, используя учетную запись по умолчанию/root, и выполните следующую команду для добавления открытого ключа.", "I have created a new server on my provider": "Я создал новый сервер у своего провайдера", "Choose Your Server Provider": "Выберите поставщика сервера", "Choose Hosting Managed by": "Выберите управляемый хостинг", "Everything You Need to Create a Website": "В<PERSON>ё, что нужно для создания сайта", "Bring and Manage Your Own Server": "Подключите и управляйте своим сервером", "Integrate Your Own Provider.": "Интегрируйте собственного провайдера.", "Manage": "Управление", "First Server": "Первый сервер", "10 Sites": "10 сайтов", "without any cost.": "Бесплатно", "Create More, Pay Less upto": "Создавайте больше, платите меньше до", "Start": "Начать", "LTD Server Created": "Сервер LTD создан", "Create from package": "Создать из пакета", "You’re on the xCloud Free plan, which includes 1 server and 10 websites with self-hosting. Activate your": "Вы используете план xCloud Free, который включает 1 сервер и 10 веб-сайтов с самостоятельным хостингом. Активируйте ваш", "team by adding a": "Команда, добавив участника", "payment method": "Способ оплаты", "today.": "Сегодня", "team by adding": "Добавить в команду", "payment method.": "Способ оплаты", "Choose Provider": "Выберите поставщика", "Only Available for LTD Users": "Доступно только для пользователей LTD", "is pending for setup.": "ожидает настройки", "Automatic Reboot": "Автоматическая перезагрузка", "Enable Backup": "Включить резервное копирование", "Disable Backup": "Отключить резервное копирование", "This setting is to enable/disable the backup settings of cloud provider.": "Этот параметр позволяет включить или отключить настройки резервного копирования облачного провайдера.", "The charge will be": "Плата составит", "for this server.": "для этого сервера", "You will be charged based on the provider policy.": "С вас будет взиматься плата в соответствии с политикой поставщика.", "To prevent system abuse, the backup feature cannot be repeatedly turned on and off. Contact our support if you need any help.": "Чтобы предотвратить злоупотребление системой, функция резервного копирования не может быть многократно включена и выключена. Обратитесь в службу поддержки, если вам нужна помощь.", "Next Backup Schedule": "Следующее расписание резервного копирования", "Select Backup Time (UTC)": "Выберите время резервного копирования (UTC)", "Backup List on Cloud Provider": "Список резервных копий у облачного провайдера", "No backup found.": "Резервная копия не найдена.", "If you enable backup then you will be charged": "Если вы включите резервное копирование, с вас будет взиматься плата", "for this.": "для этого", "based on the provider policy.": "в соответствии с политикой поставщика", "If you disable backup then you will not be able to restore the backup for the server. However, you can enable it anytime.": "Если вы отключите резервное копирование, то не сможете восстановить резервную копию для сервера. Однако вы можете включить его в любое время.", "If you proceed, this will restore the backup for the server. This operation is irreversible.": "Если вы продолжите, это восстановит резервную копию сервера. Эта операция необратима.", "Do you want to set the backup schedule for the server": "Хотите установить расписание резервного копирования для сервера?", "Backup Schedule Details": "Детали расписания резервного копирования", "Some features may not work due to a payment issue.": "Некоторые функции могут не работать из-за проблемы с оплатой.", "Payment failed": "Платёж не выполнен", "Please update your": "Пожалуйста, обновите ваш", "to retry billing.": "Повторить попытку выставления счета.", "Note: Your billing period has been extended until": "Примечание: Ваш расчетный период продлен до", "Please pay your outstanding invoices": "Пожалуйста, оплатите ваши неоплаченные счета", "to avoid any service interruptions.": "чтобы избежать перебоев в обслуживании.", "Add Cron Job": "Добавить задание Cron", "Command": "Команда", "You can add wget and curl commands here. But if you want to add wp-cli commands then you need to specify the site location with cd /var/www/sitename && wp command.": "Вы можете добавить команды wget и curl здесь. Но если вы хотите добавить команды wp-cli, то вам нужно указать расположение сайта с помощью cd /var/www/sitename && wp command.", "User": "Пользователь", "Frequency": "Частота", "Custom Schedule": "Пользовательское расписание", "Save": "Сохранить", "Add Sudo User": "Добавить пользователя Sudo", "Sudo User Name": "Имя пользователя Sudo", "Sudo Password": "Пароль Sudo", "Add SSH Key": "Добавить SSH-ключ", "No SSH Keys are available. Please add SSH Key.": "SSH-ключи недоступны. Пожалуйста, добавьте SSH-ключ.", "Cron Job": "Задача Cron", "Scroll to end": "Прокрутите до конца", "Scroll to top": "Прокрутить вверх", "Last checked": "Последняя проверка", "Add Database": "Добавить базу данных", "Loading...": "Загрузка...", "No associated site": "Нет связанного сайта", "No database found!": "База данных не найдена!", "Database Users": "Пользователи базы данных", "No database user found!": "Пользователь базы данных не найден!", "xcloud_db": "xcloud_db", "Database User Name": "Имя пользователя базы данных", "User (Optional)": "Пользователь (Необязательно)", "Password (Required with user)": "Пароль (Обязательно для пользователя)", "Add Database User": "Добавить пользователя базы данных", "Can Access": "Имеет доступ", "Keep empty to keep the same password": "Оставьте пустым, чтобы сохранить тот же пароль", "If you proceed, this is permanently removed and you will not be able to access or retrieve it again.": "Если вы продолжите, это будет удалено безвозвратно, и вы не сможете получить к нему доступ или восстановить его.", "Update Cron Job": "Обновить задание Cron", "Update Sudo User": "Обновить пользователя Sudo", "Reset Sudo Password": "Сбросить пароль Sudo", "Add New Rule": "Добавить новое правило", "Protocol": "Протокол", "Traffic": "Трафик", "Active": "Активно", "Fail2Ban Management": "Управление Fail2Ban", "Ban New IP Address": "Заблокировать новый IP-адрес", "Banned IP Addresses": "Заблокированные IP-адреса", "No banned IP addresses.": "Нет заблокированных IP-адресов.", "SSH": "SSH", "port": "Порт", "You can use multiple ports separated by comma. Leave empty to allow all ports. Also you can use a range of ports by using colon(i.e 6000:7000).": "Вы можете использовать несколько портов, разделенных запятой. Оставьте поле пустым, чтобы разрешить все порты. Также можно использовать диапазон портов, указав его через двоеточие (например, 6000:7000).", "IP Address (Optional)": "IP-адрес (необязательно)", "Valid IP address": "Действительный IP-адрес", "You can use multiple IP addresses separated by comma. Leave empty to allow all IP addresses. Also you can use a subnet. (i.e *************, *************, *************/24)": "Вы можете использовать несколько IP-адресов, разделённых запятой. Оставьте поле пустым, чтобы разрешить все IP-адреса. Также можно использовать подсеть (например, *************, *************, *************/24).", "All": "Все", "TCP": "TCP", "UDP": "UDP", "Allow": "Разрешить", "Deny": "Отказать", "Adding Rule...": "Добавление правила...", "Add Rule": "Добавить правило", "Enter the IP address you want to ban. You can use comma to separate multiple IP addresses (e.g xx.xx.xx.xx, xx.xx.xx.xx)": "Введите IP-адре<PERSON>, который вы хотите заблокировать. Вы можете использовать запятую для разделения нескольких IP-адресов (например, xx.xx.xx.xx, xx.xx.xx.xx).", "Banning IP...": "Блокировка IP...", "Ban IP": "Заблокировать IP", "All Servers": "Все серверы", "All Web Servers": "Все веб-серверы", "Nginx": "<PERSON><PERSON><PERSON>", "OpenLiteSpeed": "OpenLiteSpeed", "Provisioned": "Подготовлено", "Provisioning": "Настройка", "Upgrade Server": "Обновить сервер", "Server Name": "Имя сервера", "Choose Your Server Size": "Выберите размер сервера", "Be cautious when upgrading to a larger hosting plan since you can’t switch back. Also, it’s crucial to back up important data before trying to resize partitions, which can be risky.": "Будьте осторожны при переходе на более крупный тарифный план хостинга, так как вернуться обратно невозможно. Также важно создать резервную копию важных данных перед изменением размера разделов, так как это может быть рискованно.", "Can not perform server resize action on disconnected server.": "Невозможно выполнить изменение размера отключенного сервера.", "Current Status": "Текущий статус", "After Upgrading": "После обновления", "RAM Size": "Размер ОЗУ", "SSD Space": "Место на SSD", "Server Utilities": "Серверные утилиты", "Delete your Server": "Удалить сервер", "This action is irreversible, and will also remove the server from your provider, and all data will be erased. We recommend that you keep a backup before you proceed.": "Это действие необратимо, и также удалит сервер у вашего провайдера, все данные будут стерты. Рекомендуем сделать резервную копию перед продолжением.", "Warning: Before you upgrade": "Предупреждение: Перед обновлением", "This will require a HARD restart and the process can take up to 15 min. It is recommended to take a full server backup of your server before proceeding.": "Это потребует ПОЛНОЙ перезагрузки, и процесс может занять до 15 минут. Рекомендуется сделать полную резервную копию сервера перед продолжением.", "New Plan": "Новый план", "If you upgrade now, you’ll be charged": "Если вы обновитесь сейчас, с вас будет взиматься плата", "Provider Name": "Имя поставщика", "Ubuntu Version": "Версия Ubuntu", "MySQL Version": "Версия MySQL", "Full Server Migrations": "Полные миграции сервера", "Initiate Full Server Migration": "Запустить полную миграцию сервера", "Public Ip": "Публичный IP", "Updated": "Обновлено", "In Use": "Используется", "Available": "Доступно", "Utilization": "Использование", "Cores": "Ядра", "Speed/Core": "Скорость/Ядро", "Threads": "Темы", "Hard Disk Usage": "Использование жесткого диска", "Total": "Итого", "Used": "Использовано", "Uptime Overview": "Обзор времени безотказной работы", "Select your site's PHP version to update php settings on the sites.": "Выберите версию PHP вашего сайта, чтобы обновить настройки PHP на сайтах.", "Max Execution Time": "Максимальное время выполнения", "The maximum time in seconds a script is allowed to run before it is terminated by the parser. We recommend 60 seconds.": "Максимальное время в секундах, в течение которого скрипт может выполняться до его завершения парсером. Рекомендуем 60 секунд.", "Max Input Time": "Макс. время ввода", "The maximum time in seconds a script is allowed to parse input data, like POST and GET. We recommend 60 seconds.": "Максимальное время в секундах, разрешенное для обработки входных данных скриптом, таких как POST и GET. Рекомендуем 60 секунд.", "Max Input Vars": "Макс. количество входных переменных", "The maximum number of input variables allowed per request. We recommend 1000 vars.": "Максимальное количество входных переменных на запрос. Рекомендуем 1000 переменных.", "Memory Limit": "Ограничение памяти", "The maximum amount of memory a script may consume. We recommend 256MB.": "Максимальный объем памяти, который может использовать скрипт. Рекомендуем 256 МБ.", "Post Max Size": "Максимальный размер сообщения", "The maximum size of POST data that PHP will accept. We recommend 128MB.": "Максимальный размер данных POST, который PHP примет. Рекомендуем 128 МБ.", "Max File Upload Size": "Максимальный размер загружаемого файла", "The maximum size of an uploaded file. We recommend 128MB.": "Максимальный размер загружаемого файла. Рекомендуем 128 МБ.", "Session GC Maxlifetime": "Максимальная продолжительность сеанса GC", "The maximum time in seconds a session is allowed to be idle before it is terminated by the session garbage collector. We recommend 1440 seconds.": "Максимальное время в секундах, в течение которого сессия может оставаться неактивной, прежде чем будет завершена сборщиком мусора сессий. Рекомендуемое значение: 1440 секунд.", "Important Note": "Важное примечание", "Enabling PHP OPCache will highly improve performance by storing precompiled scripts (PHP Code) in shared memory.": "Включение PHP OPCache значительно улучшит производительность, сохраняя предварительно скомпилированные скрипты (PHP-код) в общей памяти.", "If you allow optimizing your OPCache, you need to make sure that your deployment parse code reloads the PHP FPM services at the end of each development.": "Если вы разрешаете оптимизацию вашего OPCache, убедитесь, что при развертывании кода перезагружаются службы PHP FPM после каждого этапа разработки.", "Disable OPCache": "Отключить OPCache", "Enable OPCache": "Включить OPCache", "Server Statistics": "Статистика сервера", "Disable it to turn off magic login feature on all sites under this server.": "Отключите, чтобы деактивировать функцию магического входа на всех сайтах этого сервера.", "Back To Servers": "Назад к серверам", "Team": "Команда", "WEB SERVER": "ВЕБ-СЕРВЕР", "Disk Usage": "Использование диска", "Checked": "Проверено", "Check Again": "Проверить ещё раз", "Your disk space is low. Please upgrade your plan to avoid any downtime.": "Место на диске заканчивается. Пожалуйста, обновите план, чтобы избежать простоя.", "Close": "Закрыть", "No sites on this server": "На этом сервере нет сайтов", "Get started by creating a new site!": "Начните с создания нового сайта!", "Sudo Users": "Пользователи Sudo", "Enable Vulnerability Scan": "Включить сканирование уязвимостей", "Enable Auto Update": "Включить автообновление", "If vulnerability found we will update in 24 hours": "Если обнаружена уязвимость, мы обновим в течение 24 часов", "Enable Auto Backup": "Включить автоматическое резервное копирование", "Select All Sites": "Выбрать все сайты", "Choose a Server to Clone Sites from": "Выберите сервер для клонирования сайтов", "Team Details": "Детали команды", "Create a new team to collaborate with others on projects.": "Создайте новую команду для совместной работы над проектами.", "If you proceed, this will remove all the services and sites along with other data which cannot be recovered.": "Если вы продолжите, это удалит все сервисы и сайты вместе с другими данными, которые не могут быть восстановлены.", "I have understood and would like to proceed.": "Я понял и хочу продолжить.", "Permanently delete this team.": "Окончательно удалить эту команду.", "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.": "После удаления команды все её ресурсы и данные будут удалены навсегда. Перед удалением команды, пожалуйста, скачайте все данные или информацию, которую вы хотите сохранить.", "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.": "Вы уверены, что хотите удалить эту команду? После удаления команды все её ресурсы и данные будут удалены безвозвратно.", "Add Team Member": "Добавить участника команды", "Add a new team member to your team, allowing them to collaborate with you.": "Добавьте нового участника в вашу команду, чтобы он мог с вами сотрудничать.", "Please provide the email address of the person you would like to add to this team.": "Укажите адрес электронной почты человека, которого вы хотите добавить в эту команду.", "Added.": "Добавлено.", "Add": "Добавить", "Pending Team Invitations": "Ожидающие приглашения в команду", "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.": "Эти люди были приглашены в вашу команду, и им отправлено письмо с приглашением. Они могут присоединиться к команде, приняв приглашение по электронной почте.", "All of the people that are part of this team.": "Все участники этой команды.", "Leave": "Выйти", "Remove": "Удалить", "Manage Role": "Управление ролью", "Are you sure you would like to leave this team?": "Вы уверены, что хотите покинуть эту команду?", "Remove Team Member": "Удалить участника команды", "Are you sure you would like to remove this person from the team?": "Вы уверены, что хотите удалить этого человека из команды?", "The team's name and owner information.": "Название команды и информация о владельце.", "Saved.": "Сохранено.", "Create Team": "Создать команду", "Team Settings": "Настройки команды", "Join the Exclusive Waitlist!": "Присоединяйтесь к эксклюзивному списку ожидания!", "Your Email Address..": "Ваш адрес электронной почты", "Join Waitlist": "Присоединиться к списку ожидания", "Already have been invited?": "Уже получили приглашение?", "Login to xCloud": "Войти в xCloud", "When you cancel migration, already migrated data will be removed from the server.": "При отмене миграции уже перенесённые данные будут удалены с сервера.", "Cancel Migration": "Отменить миграцию", "Auth": "Аутентификация", "Cache": "Кэш", "Git": "<PERSON><PERSON><PERSON>", "Proceeding will permanently delete this Site and all of its data.": "Продолжение приведет к окончательному удалению этого сайта и всех его данных.", "Delete All Files and Configurations.": "Удалить все файлы и настройки.", "Do You Want To Delete Files?": "Удалить файлы?", "Delete Database": "Удалить базу данных", "Do You Want To Delete Database?": "Удалить базу данных?", "Delete Site User": "Удалить пользователя сайта", "Do You Want To Delete User?": "Удалить пользователя?", "Delete Local Backups": "Удалить локальные резервные копии", "Do You Want To Delete Local Backups?": "Удалить локальные резервные копии?", "Type site name to confirm": "Введите название сайта для подтверждения", "Delete DNS record from your Cloudflare account": "Удалить DNS-запись из вашего аккаунта Cloudflare", "Your DNS record for the site on this server will be deleted from your Cloudflare account.": "Запись DNS для сайта на этом сервере будет удалена из вашего аккаунта Cloudflare.", "Proceeding will disable this Site.": "Продолжение отключит этот сайт.", "Disable Site": "Отключить сайт", "Also Disable Cron For This Site": "Также отключить Cron для этого сайта", "Disable HTML Markup": "Отключить HTML-разметку", "After turning on Disable Site, you won’t be able to use this site on the web anymore along with losing access through SFTP/SSH. Plus, all the scheduled tasks for the site will be paused once you turn on Disable Cron For This Site.": "После включения функции «Отключить сайт» вы больше не сможете использовать этот сайт в интернете, а также потеряете доступ через SFTP/SSH. Кроме того, все запланированные задачи для сайта будут приостановлены, если вы включите «Отключить Cron для этого сайта».", "Disable": "Отключить", "Site is currently disabled.": "Сайт в настоящее время отключен.", "Click here": "Нажмите здесь", "to enable this site": "Включить этот сайт", "The maximum amount of memory a script may consume. we recommend 128MB.": "Максимальный объем памяти, который может использовать скрипт. Рекомендуем 128 МБ.", "The number of seconds after which data will be seen as 'garbage' and potentially cleaned up. we recommend 1440 seconds.": "Количество секунд, после которых данные будут считаться «мусором» и могут быть удалены. Рекомендуем 1440 секунд.", "Max PHP Workers": "Максимум PHP-процессов", "The number of PHP workers (pm.max_children) that can be spawned to handle requests.": "Количество PHP-проц<PERSON><PERSON><PERSON><PERSON> (pm.max_children), которые могут быть запущены для обработки запросов.", "Redirect Type": "Тип перенаправления", "Choose Redirect Type": "Выберите тип перенаправления", "From": "От", "To": "Кому", "View": "Просмотр", "Magic Login": "Магическая Авторизация", "Site Logs": "Жур<PERSON><PERSON>ы сайта", "Site Events": "События сайта", "Purge Cache": "Очистить кэш", "Clone Site": "Клонировать сайт", "Delete Site": "Удалить сайт", "Create New Database": "Создать новую базу данных", "Database Cluster Name": "Имя кластера базы данных", "Cluster Name": "Имя кластера", "Select Existing Database Cluster": "Выберите существующий кластер базы данных", "Additional Domain Name": "Дополнительное доменное имя", "Additional Domain (Optional)": "Дополнительный домен (необязательно)", "Add Domain": "Добавить домен", "Admin Password": "Пароль администратора", "Admin Email Address": "Адрес электронной почты администратора", "WordPress Version": "Версия WordPress", "Prefix": "Префикс", "wp_": "wp_", "Full Page Caching": "Кэширование всей страницы", "Redis Object Caching": "Кэширование объектов Redis", "Could not connect to the database server. Please check your database credentials and try again.": "Не удалось подключиться к серверу базы данных. Проверьте учетные данные базы данных и повторите попытку.", "Database Username": "Имя пользователя базы данных", "Database Host": "Хост базы данных", "Database Port": "Порт базы данных", "Connection URL": "URL подключения", "Please use the database connection URL provided to establish a connection between your database client and the database. We suggest using TablePlus as your database client.": "Пожалуйста, используйте предоставленный URL для подключения к базе данных, чтобы установить соединение между вашим клиентом базы данных и самой базой данных. Мы рекомендуем использовать TablePlus в качестве клиента базы данных.", "Add the following record to the DNS for your domain. This required when when you want to get a free SSL certificate issued & managed by": "Добавьте следующую запись в DNS для вашего домена. Это необходимо, если вы хотите получить бесплатный SSL-сертификат, выданный и управляемый", "If you have manually added DNS record with Cloudflare proxy then the verify option will not work": "Если вы вручную добавили DNS-запись с прокси Cloudflare, то функция проверки не будет работать.", "Record": "Запись", "A": "А", "Verify My DNS": "Проверить мой DNS", "In your domain settings, you need to add the following records for configuring your site email.": "В настройках домена необходимо добавить следующие записи для настройки электронной почты вашего сайта.", "Record(SPF)": "Запись (SPF)", "TXT": "Текст", "Value": "Значение", "CNAME": "CNAME", "Record(DKIM)": "Запись (DKIM)", "Record(DMARC)": "Запись(DMARC)", "Verify Record": "Проверить запись", "Use free SSL certificate issued & managed by": "Используйте бесплатный SSL-сертификат, выданный и управляемый", "I will provide the certificate and manage it myself.": "Я предоставлю сертификат и буду управлять им самостоятельно.", "Certificate": "Сертификат", "Paste Your Certificate Here": "Вставьте ваш сертификат здесь", "Private Key": "Приват<PERSON><PERSON>й ключ", "Paste Your Private Key Here": "Вставьте ваш приватный ключ здесь", "Enabling HTTPS makes your website more secure.": "Включение HTTPS делает ваш сайт более безопасным.", "Learn more": "Узнать больше", "Beta": "Бета", "Install New WordPress Website": "Установить новый сайт на WordPress", "Select this option if you want to a create a fresh new WordPress website": "Выберите этот вариант, если хотите создать новый сайт на WordPress.", "Clone a Git Repository": "Клонировать Git-репозиторий", "Clone a git repository to create a new website": "Клонировать git-репозиторий для создания нового сайта", "Migrate An Existing WordPress Website": "Перенести существующий сайт WordPress", "Have an existing website already? Select this option to migrate it with ": "Уже есть существующий сайт? Выберите этот вариант для его переноса с помощью", "Manually Upload WordPress Website": "Ручная загрузка сайта WordPress", "Upload a zipped file of your existing WordPress website": "Загрузите zip-файл вашего существующего сайта WordPress", "Migrate Full Server": "Перенос полного сервера", "Migrate all WordPress sites from Ubuntu servers with a few clicks": "Перенесите все сайты WordPress с серверов Ubuntu в несколько кликов", "Certificate Issue": "Выдача сертификата", "Expires On": "Срок действия истекает", "Renew Date": "Дата продления", "Add Your New Site Into": "Добавьте ваш новый сайт в", "This server created under xCloud Free plan which includes 1 server and": "Этот сервер создан по тарифному плану xCloud Free, который включает 1 сервер и", "website with Self Hosting. To unlock full features, please consider upgrading the server bill from free to paid plan.": "Веб-сайт с самостоятельным хостингом. Чтобы разблокировать все функции, рассмотрите возможность перехода с бесплатного на платный тарифный план.", "Your": "<PERSON><PERSON><PERSON>", "is low. Please upgrade your plan to avoid any downtime.": "Низкий уровень. Пожалуйста, обновите план, чтобы избежать простоев.", "Choose a Server to add Site": "Выберите сервер для добавления сайта", "Site Title": "Название сайта", "New Site Title": "Название нового сайта", "Add Tag (optional)": "Добавить тег (необязательно)", "Go Live": "Начать трансляцию", "(Optional)": "(Необязательно)", "Integrate Cloudflare forAutomatic DNS and SSL management.": "Интегрируйте Cloudflare для автоматического управления DNS и SSL.", "If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.": "Если вы используете дополнительные домены, которые не являются частью основного домена, вам необходимо переключиться на xCloud SSL.", "WordPress Multisite": "WordPress Мультисайт", "Enable Multisite": "Включить мультисайт", "Select Multisite Type": "Выберите тип мультис<PERSON>йта", "You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.": "После создания сайта необходимо установить SSL для поддомена мультисайта на странице SSL/HTTPS сайта.", "Enhancing Website Performance": "Улучшение производительности сайта", "Speed up your website by using smart caching!": "Ускорьте ваш сайт с помощью умного кэширования!", "Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "Объедините Full Page и Redis, чтобы ускорить работу вашего сайта и улучшить впечатления посетителей.", "Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.": "Включите LiteSpeed Cache, чтобы ускорить работу вашего сайта и улучшить впечатления посетителей.", "LiteSpeed Cache": "LiteSpeed Cache", "Email Provider Configuration": "Настройка почтового провайдера", "Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.": "Получите 100 бесплатных писем в месяц для старта вашего бизнеса. Отключите эту опцию, если хотите использовать свой собственный домен.", "Read our documentation": "Ознакомьтесь с нашей документацией", "Blueprints": "Черте<PERSON>и", "Choose a blueprint to install WordPress with pre-configured plugins and themes.": "Выберите шаблон для установки WordPress с предустановленными плагинами и темами.", "All Blueprints": "Все чертежи", "Manage your all blueprints as you like, you can edit, delete or create new from here": "Управляйте всеми чертежами по своему усмотрению: редактируйте, удаляйте или создавайте новые здесь.", "OK": "ОК", "Create Your New Site Into": "Создайте свой новый сайт", "xCloud Playground": "xCloud Песочница", "Playground Environment": "Среда для игр", "This demo site will expire 24 hours after creation.": "Этот демонстрационный сайт истечет через 24 часа после создания.", "Staging Domain Setup": "Настройка тестового домена", "This will be auto-generated according to your site title": "Это будет автоматически сгенерировано в соответствии с названием вашего сайта", "Speed up your website by using smart caching! Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "Ускорьте ваш сайт с помощью умного кэширования! Объедините полное кэширование страниц и Redis, чтобы сделать ваш сайт быстрее и улучшить впечатления посетителей.", "Step 1 of 3": "Шаг 1 из 3", "Hey you are trying to access the playground site": "Привет, вы пытаетесь получить доступ к сайту Playground", "but you are not in the": "но вы не в", "We request you to check your email and accept the invitation to join and edit the site.": "Пожалуйста, проверьте свою электронную почту и примите приглашение для присоединения и редактирования сайта.", "Enable Adminer": "Вклю<PERSON>и<PERSON><PERSON> Adminer", "Adminer can enhance your database management experience. Activate it only when necessary to ensure optimal security measures.": "Adminer может улучшить ваш опыт управления базой данных. Активируйте его только при необходимости для обеспечения оптимальной безопасности.", "Database Manager": "Менеджер базы данных", "Manage your databases with Adminer, an opensource database management tool.": "Управляйте своими базами данных с помощью Adminer, открытого инструмента для управления базами данных.", "Launch Adminer": "Запустить Adminer", "Basic Authentication": "Базовая аутентификация", "Protect Your Site": "Защитите свой сайт", "Turn on to enable basic authentication for your site by adding username and password.": "Включите, чтобы активировать базовую аутентификацию для вашего сайта, добавив имя пользователя и пароль.", "Previous Remote Backups": "Предыдущие удалённые резервные копии", "Backup Now": "Создать резервную копию сейчас", "File": "<PERSON>а<PERSON><PERSON>", "Previous Local Backups": "Предыдущие локальные резервные копии", "Remote Backup Settings": "Настройки удалённого резервного копирования", "Backup Type": "Тип резервного копирования", "Select Bucket": "Выбрать корзину", "Server Bucket": "Серверное Хранилище", "Manage Your Storage Providers": "Управление поставщиками хранилища", "Backup Items": "Резервное копирование элементов", "Database Backup": "Резервное копирование базы данных", "will be backed up.": "Будет сохранено.", "Files Backup": "Резервное копирование файлов", "Exclude Paths": "Исключить пути", "Automatic Backup": "Автоматическое резервное копирование", "Automatic Full Backup": "Автоматическое полное резервное копирование", "Select Backup Frequency": "Выберите частоту резервного копирования", "Select Full Backup Frequency": "Выберите частоту полного резервного копирования", "Automatic Incremental Backup": "Автоматическое инкрементное резервное копирование", "Automatic Delete": "Автоматическое удаление", "Delete After Days": "Удалить через дней", "30": "30", "Local Backup Settings": "Настройки локального резервного копирования", "Select Incremental Backup Frequency": "Выберите частоту инкрементного резервного копирования", "Change": "Изменить", "Additional Domains": "Дополнительные домены", "Add new": "Добавить новый", "After changing the domain, you need to install SSL for Multisite Subdomain from the site SSL/HTTPS page.": "После изменения домена необходимо установить SSL для многосайтового субдомена на странице SSL/HTTPS сайта.", "Connect New Provider": "Подключить нового провайдера", "Use xCloud Domain": "Использовать домен xCloud", "Use Your Own Domain": "Используйте свой домен", "Requires Verification": "Требуется проверка", "From Name": "От имени", "This is the name that will appear in the recipient's inbox.": "Это имя будет отображаться в почтовом ящике получателя.", "To use your own domain, please verify your domain with xCloud by following the DNS setup below.": "Чтобы использовать собственный домен, подтвердите его в xCloud, следуя приведенной ниже настройке DNS.", "Clear Provider": "Очистить провайдера", "We only use the Fluent SMTP plugin to configure your given SMTP credentials on the sites, ": "Мы используем только плагин Fluent SMTP для настройки ваших SMTP-учетных данных на сайтах.", "so that you do not have to do anything manually. If you are not getting emails then please ": "Чтобы вам не приходилось делать что-либо вручную. Если вы не получаете электронные письма, пожалуйста,", "check your email logs from your email provider. To test your email integration, please send a test email from your fluentsmtp plugin.": "Проверьте журналы электронной почты у вашего провайдера. Чтобы протестировать интеграцию электронной почты, отправьте тестовое письмо через плагин FluentSMTP.", "Enable File Manager": "Включить файловый менеджер", "Tiny File Manager": "Менеджер файлов Tiny", "Activate it only when necessary to ensure optimal security measures.": "Активируйте только при необходимости для обеспечения оптимальных мер безопасности.", "File Manager": "Файловый менеджер", "Manage your files, an opensource tool.": "Управляйте файлами, инструмент с открытым исходным кодом.", "Launch File Manager": "Запустить Файловый Менеджер", "Page Caching": "Кэширование страниц", "FastCGI Nginx": "FastCGI Nginx", "Cache Duration": "Длительность кэша", "Unit": "Единица", "Cache Exclusion HTTP URL Rules": "Правила Исключения HTTP URL из Кэша", "Cache Exclusion Cookie Rules": "Правила Исключения Куки для Кэша", "Clear Page Cache": "Очистить кэш страницы", "This will slow down your site until the caches are rebuilt.": "Это замедлит работу вашего сайта, пока кэши не будут перестроены.", "Purging Cache...": "Очистка кэша...", "Action will be available once the Object Cache operation is finished": "Действие будет доступно после завершения операции кэширования объекта", "Object Cache": "Кэш объектов", "Redis User": "Пользователь Redis", "Redis Password": "Пар<PERSON><PERSON><PERSON> Redis", "Redis Object Cache Key": "Ключ объекта кеша Redis", "Redis object cache optimize performance, reduces database load, and enhances response times for a seamless browsing experience.": "Кэширование объектов Redis оптимизирует производительность, снижает нагрузку на базу данных и улучшает время отклика для плавного просмотра.", "Stores the results of queries to the site’s database.": "Сохраняет результаты запросов к базе данных сайта.", "Clear Object Cache": "Очистить кэш объектов", "Git Settings": "Настройки Git", "Pull and deploy now": "Выполнить извлечение и развертывание сейчас", "Updated At": "Обновлено в", "Production": "Производство", "Staging": "Промежуточная версия", "Demo": "Демо", "Vulnerable": "Уязвимый", "Migrating": "Миграция", "Add IP Address": "Добавить IP-адрес", "No IP addresses added yet.": "Пока нет добавленных IP-адресов.", "IP Addresses": "IP-адреса", "Updating...": "Обновление...", "Adding...": "Добавление...", "LiteSpeed Cache for WordPress (LSCWP) is an all-in-one site acceleration plugin, featuring an exclusive server-level cache and a collection of optimization features.": "LiteSpeed Cache для WordPress (LSCWP) — это универсальный плагин для ускорения сайта, включающий эксклюзивный серверный кэш и набор функций оптимизации.", "Clear LiteSpeed Cache": "Очистить LiteSpeed Cache", "Showing logs from": "Показать журналы с", "Loading..": "Загрузка...", "WordPress Debug Log": "<PERSON><PERSON><PERSON><PERSON>л отладки WordPress", "Reload": "Перезагрузить", "Clear": "Очистить", "Clearing..": "Очистка...", "Your Site Has Been Successfully Migrated": "Ваш сайт успешно перенесён", "Staging URL": "URL для тестирования", "Setup Email for this site": "Настроить электронную почту для этого сайта", "Monitoring Stats": "Статистика мониторинга", "CPU Usages": "Использование ЦП", "SSL Overview": "Обзор SSL", "DNS Status": "Статус DNS", "SSL Status": "Статус SSL", "Expiry": "Срок действия", "WordPress Logs": "Журналы WordPress", "Updates": "Обновления", "Update Available": "Доступно обновление", "Up To Date": "Актуально", "Plugins": "Плагины", "Themes": "Темы", "Custom Nginx Config": "Пользовательская конфигурация Nginx", "Fetching Nginx..": "Получение Nginx...", "Preview Nginx": "Предпросмотр Nginx", "Add a New Config": "Добавить новую конфигурацию", "Select Config Type": "Выберите тип конфигурации", "Config File Name": "Имя файла конфигурации", "Config Content": "Содержимое конфигурации", "Preview Content": "Предварительный просмотр контента", "Running...": "Запуск...", "Run & Debug": "Запуск и Отладка", "Nginx Config File": "Файл конфигурации Nginx", "Save Settings": "Сохранить настройки", "Nginx & Security": "Nginx и безопасность", "Security": "Безопасность", "7G Firewall": "7G Брандмауэр", "8G Firewall": "8G Брандмауэр", "Disable Nginx File Regeneration": "Отключить регенерацию файлов Nginx", "PHP Execution on Upload Directory": "Выполнение PHP в каталоге загрузки", "Enable XML-RPC": "Включить XML-RPC", "Edit X-Frame-Options": "Редактировать X-Frame-Options", "Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com": "Пожалуйста, используйте одно из следующих: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com или ALLOW-FROM https://example2.com https://example3.com", "With xCloud you get 100 emails free per month in each team. Manage email for this site from": "С помощью xCloud вы получаете 100 бесплатных писем в месяц для каждой команды. Управляйте электронной почтой для этого сайта из", "You can purchase more or add your own email providers from": "Вы можете приобрести больше или добавить своих поставщиков электронной почты из", "Learn more from our": "Узнайте больше из нашего", "Primary Domain": "Основной домен", "Add New": "Добавить новый", "Others": "Прочие", "Page Cache": "Кэш страницы", "on": "<PERSON><PERSON><PERSON>.", "off": "Выкл.", "SSL/HTTPS": "SSL/HTTPS", "Basic Auth": "Базовая аутентификация", "No Updates": "Обновлений нет", "Most Recent Event": "Последнее событие", "View All Events": "Просмотреть все события", "Redirection": "Перенаправление", "Add Redirection": "Добавить перенаправление", "Redirections": "Перенаправления", "Edit Redirection": "Редактировать перенаправление", "WP-Cron": "WP-Cron", "WP-Cron manages time-based tasks in WordPress, relying on site visits.": "WP-Cron управляет задачами по времени в WordPress, полагаясь на посещения сайта.", "If you want to also add additional custom cronjob then you can configure on your server from": "Если вы хотите добавить дополнительное пользовательское задание cron, вы можете настроить его на своем сервере из", "Edit Site Tags": "Редактировать теги сайта", "WP Debug": "Отладка WP", "Enable WP Debug": "Включить отладку WP", "Enable this to view WordPress debug logs on the": "Включите это, чтобы просмотреть журналы отладки WordPress на", "site's logs page": "Страниц<PERSON> журналов сайта", "Delete Site Confirmation": "Подтверждение удаления сайта", "This action is irreversible. Delete sites cannot be restored.": "Это действие необратимо. Удаленные сайты не могут быть восстановлены.", "Rescue Site": "Спасательный участок", "Run Now": "Запустить сейчас", "Repair Site User": "Пользователь сайта ремонта", "Update Directory Permissions": "Обновить разрешения каталога", "Repair PHP": "Исправить PHP", "Regenerate": "Сгенерировать заново", "It is recommended to keep the above options turned on before running the rescue action.": "Рекомендуется оставить вышеуказанные параметры включенными перед запуском операции восстановления.", "Back To Sites": "Назад к сайтам", "staging": "Промежуточная версия", "Add Staging environment": "Добавить промежуточную среду", "Visit Site": "Перейти на сайт", "Deploy Staging": "Развернуть на промежуточном этапе", "WordPress": "WordPress", "Archive": "Архивировать", "Are You Sure You Want To Deploy Staging for  ": "Вы уверены, что хотите развернуть Staging?", "A staging site is an identical copy of your production website, created for testing purposes. This isolated environment lets you test any plugin, or theme, or make any other changes before going live. This removes the risk of damaging your production website. Later, you can pull/push updates and apply to your production.": "Сайт для тестирования — это идентичная копия вашего рабочего сайта, созданная для тестирования. Эта изолированная среда позволяет тестировать любые плагины, темы или вносить другие изменения перед публикацией. Это устраняет риск повреждения вашего рабочего сайта. Позже вы можете переносить обновления и применять их к рабочему сайту.", "Staging Site Domain": "Домен тестового сайта", "Preparing Deployment..": "Подготовка к развертыванию...", "Site SSH/sFTP Access": "Доступ к сайту через SSH/sFTP", "Site Username": "Имя пользователя сайта", "Site Path": "Путь сайта", "SSH String": "Строка SSH", "Database URL Connection": "URL подключения к базе данных", "DNS Setup For Multisite SSL": "Настройка DNS для многосайтового SSL", "Provide your own certificate & manage it yourself": "Предоставьте свой сертификат и управляйте им самостоятельно", "Use Cloudflare managed SSL Certificate.": "Используйте управляемый SSL-сертификат Cloudflare.", "Demo Environment": "Демонстрационная среда", "Staging Environment": "Тестовая среда", "Demo Site Domain": "Домен демонстрационного сайта", "Staging Domain": "Тестовый домен", "If you’re using different additional domains then you need to switch to xCloud SSL. With Cloudflare this is not supported.": "Если вы используете разные дополнительные домены, вам нужно переключиться на xCloud SSL. С Cloudflare это не поддерживается.", "Staging Management": "Управление Сценами", "Pull Data from Production to Staging": "Перенести данные из Production в Staging", "Copy the changes from the live site to staging": "Скопировать изменения с активного сайта на тестовый", "Pulling Data...": "Загрузка данных...", "Pull Data": "Получить данные", "Push Data from Staging to Production": "Отправить данные из промежуточной среды в рабочую среду", "Copy the changes from the staging to live": "Скопировать изменения из промежуточной версии в рабочую", "Pushing Data...": "Отправка данных...", "Push Data": "Отправить данные", "Deployment Logs": "Журналы Развертывания", "View All Logs": "Просмотреть все журналы", "Pull data from ": "Получить данные из", "Files": "Файлы", "Overwrite": "Перезаписать", "Incremental": "Пошаговый", "Full": "Полный", "Selected Tables": "Выбранные таблицы", "fetching tables...": "Загрузка таблиц...", "Fetching tables...": "Получение таблиц...", "Initiating Pulling...": "Инициализация извлечения...", "Push data from this Staging Site to the Production site(": "Отправить данные с этого промежуточного сайта на рабочий сайт", "Push to Production": "Выпустить в Продакшн", "Your staging site data will be pushed to production. The production site may be temporarily unavailable for a while.": "Данные вашего тестового сайта будут перенесены на рабочий сайт. Рабочий сайт может быть временно недоступен.", "Select Tables": "Выбрать таблицы", "Take backup of your production site before pushing changes(Recommended).": "Создайте резервную копию вашего рабочего сайта перед внесением изменений (рекомендуется).", "Initiating Pushing...": "Запуск отправки...", "Source Site": "Исходный сайт", "Destination Site": "Целевой сайт", "Initiated By": "Инициатор", "Inactive": "Неактивно", "WordPress Core": "Ядро WordPress", "Your current version is": "Ваша текущая версия:", "Updating to": "Обновление до", "Your WordPress": "Ваш WordPress", "is up to date.": "обновлено", "Version": "Версия", "Changelog": "<PERSON><PERSON><PERSON><PERSON>л изменений", "Activating...": "Активация...", "Activate": "Активир<PERSON>ать", "Activated": "Актив<PERSON><PERSON>овано", "Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or": "Обнаружение уязвимостей в настоящее время отключено для этого сайта. Чтобы включить его, перейдите в раздел Сервер>Настройки безопасности.", "click here": "Нажмите здесь", "Insecure": "Небезопасно", "Secure": "Безопасно", "Last Scan": "Последнее сканирование", "An Updated Version of WordPress is Available": "Доступна обновлённая версия WordPress", "CVSS Score": "Оценка CVSS", "Ignored": "Игнорируется", "Ignore": "Игнорировать", "Patched": "Обновлено", "Fixed in": "Исправлено в", "Remediation": "Устранение", "Direct URL": "Прямая ссылка", "First Published": "Опубликовано впервые", "Last Update": "Последнее обновление", "Update Config": "Обновить конфигурацию", "WP Config": "Конфигурация WP", "Invoices of Clients": "Счета клиентов", "Here you can check all your previous invoices": "Здесь вы можете просмотреть все ваши предыдущие счета", "Copy": "Копировать", "View Nova": "Просмотр Nova", "Total Clients": "Всего клиентов", "Total Products": "Всего товаров", "Total Servers": "Всего серверов", "You don’t have any server yet": "У вас пока нет сервера", "Sites": "Сайты", "You don’t have any site yet": "У вас пока нет сайта", "Clients Overview": "Обзор клиентов", "Total Active Clients": "Всего активных клиентов", "Total Inactive Clients": "Общее количество неактивных клиентов", "Total Invoices": "Всего счетов", "You don’t have any client yet": "У вас пока нет клиентов", "Access Dashboard & Website": "Доступ к панели управления и сайту", "Domain Settings": "Настройки домена", "Visit Landing Page": "Перейти на целевую страницу", "Dashboard": "Панель управления", "Brand Setup": "Настройка бренда", "Brand Profile": "Профиль бренда", "Add Your Logo": "Добавьте логотип", "Upload a logo that represent your brand profile.": "Загрузите логотип, представляющий ваш бренд.", "Please upload a logo with a minimum size of 212x40 pixels in PNG, JPEG, or JPG format (max 5MB) for best quality.": "Пожалуйста, загрузите логотип размером не менее 212x40 пикселей в формате PNG, JPEG или JPG (максимум 5 МБ) для лучшего качества.", "Upload Logo": "Загрузить логотип", "Cloud Hosting": "Облачный хостинг", "Brand Name": "Название бренда", "<EMAIL>": "<EMAIL>", "Support Email": "Поддержка по электронной почте", "579 Spruce Court, Dallas, TX 75201": "Спрус Корт, 579, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> 75201", "Address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Copyright Name": "Авторское право: Имя", "Processing...": "Обработка...", "Proceed to Checkout": "Перейти к оформлению заказа", "Sell up to": "Продать до", "Checkout": "Оформление заказа", "Your Order Summary": "Сводка вашего заказа", "Sub Total": "Промежуточный итог", "Processing Payment...": "Обработка платежа...", "Processing Offer...": "Обработка предложения...", "Claim Free": "Получить бесплатно", "Create Product": "Создать продукт", "Create Hosting Plan": "Создать тарифный план хостинга", "Plan Name": "Название плана", "Renewal Type": "Тип продления", "SKU": "Арти<PERSON><PERSON><PERSON>", "Setup Products & Start Selling": "Настройте товары и начните продавать", "Add Your Domain Name": "Добавьте ваше доменное имя", "example.com": "example.com", "Add the following record to the DNS manager for your domain": "Добавьте следующую запись в DNS-менеджер вашего домена", "Skip this step, if you are not ready to point your domain server.": "Пропустите этот шаг, если вы не готовы указать сервер домена.", "Payment Setup": "Настройка платежей", "Integrate with Stripe Connect": "Интеграция с Stripe Connect", "Connect to your existing Stripe account or create a new account to start processing payments via Stripe.": "Подключитесь к существующему аккаунту Stripe или создайте новый, чтобы начать обработку платежей через Stripe.", "Connect Now": "Подключиться сейчас", "Stripe Account": "Аккаунт Stripe", "If your Stripe account is set up correctly, it will automatically connect within 1–2 minutes.": "Если ваш аккаунт Stripe настроен правильно, он подключится автоматически в течение 1–2 минут.", "If you’ve just created a new Stripe business account, it may take up to 3 days for Stripe to verify it.Once verified, your account should connect automatically.": "Если вы только что создали новый бизнес-аккаунт в Stripe, его проверка может занять до 3 дней. После проверки ваш аккаунт должен подключиться автоматически.", "To check the status of your": "Чтобы проверить статус вашего", "account": "Аккаунт", "you can visit your": "Вы можете посетить ваш", "Stripe dashboard.": "Панель управления Stripe", "Billing Account": "Платёжный аккаунт", "Stripe Account Name": "Название учетной записи Stripe", "Stripe Account ID": "Идентифика<PERSON>ор аккаунта Stripe", "Connected On": "Подключено", "Billing Currency": "Валюта для выставления счетов", "3D Secure Card Compatibility for Your Clients (Optional)": "Совместимость с 3D Secure для карт ваших клиентов (необязательно)", "Stripe Publishable Key": "Публичный ключ Stripe", "If your client prefers to pay using a 3D Secure card, you need to add your": "Если ваш клиент предпочитает оплату с использованием карты с 3D Secure, вам необходимо добавить ваш", "Stripe Publishable Key.": "Публичный ключ Stripe", "Get your publishable key": "Получите ваш открытый ключ", "from here.": "Отсюда.", "Syncing Account..": "Синхронизация аккаунта...", "Having Trouble?": "Возникли проблемы?", "Read our comprehensive documentation & learn to manage your hosting easily.": "Ознакомьтесь с нашей подробной документацией и научитесь легко управлять хостингом.", "Read Documentation": "Читать документацию", "Start Your Hosting Business: Resell & Earn Revenue": "Начните свой хостинг-бизнес: перепродавайте и зарабатывайте", "Launch a cloud hosting business with xCloud Managed Servers. Resell our fully managed web hosting under your own brand or domain to maximize your revenue ensuring a reliable performance.": "Запустите бизнес облачного хостинга с xCloud Managed Servers. Продавайте наш полностью управляемый веб-хостинг под своим брендом или доменом, чтобы максимизировать доход и обеспечить надежную работу.", "Complete control for your personal branding": "Полный контроль над вашим личным брендом", "Manage your client billings with Stripe Connect": "Управляйте счетами клиентов с помощью Stripe Connect", "Customize hosting packages & sell at your own price": "Настройте пакеты хостинга и продавайте по своей цене", "Get access to powerful features of xCloud": "Получите доступ к мощным функциям xCloud", "By ticking this box, you are confirming that you have read, understood, and agree to our": "Установив этот флажок, вы подтверждаете, что прочитали, поняли и согласны с нашими", "Please check this box to confirm that you accept the terms and conditions and want to proceed.": "Пожалуйста, установите этот флажок, чтобы подтвердить, что вы принимаете условия и хотите продолжить.", "Before proceeding, ensure you have an active Stripe account, as all transactions are managed via": "Прежде чем продолжить, убедите<PERSON>ь, что у вас есть активная учетная запись Stripe, так как все транзакции обрабатываются через неё.", "Stripe Connect": "Stripe Connect", "Please check this box to confirm that you have a Stripe account and want to proceed.": "Пожалуйста, установите этот флажок, чтобы подтвердить, что у вас есть аккаунт Stripe и вы хотите продолжить.", "Start Your Hosting Business Now": "Начните свой хостинг-бизнес сейчас", "Product Details": "Детали продукта", "Product Information": "Информация о продукте", "Some basic information is shared over here": "Некоторая основная информация представлена здесь", "Edit Product": "Редактировать товар", "Checkout URL": "URL оформления заказа", "Invoices of Product": "Счета за продукт", "Source Product": "Исходный продукт", "Includes Up to RAM": "Включает до ОЗУ", "Customize Package": "Настроить пакет", "My Server": "Мой сервер", "Price/Month": "Цена/месяц", "XCPU110": "XCPU110", "Stripe": "Стр<PERSON><PERSON><PERSON>", "will deduct a 3%-7% fee per sale. Your approximate profit for this sale is": "Будет удержана комиссия 3%-7% за каждую продажу. Ваш приблизительный доход от этой продажи составляет", "Active Package": "Активный пакет", "Preview Plan": "Предварительный просмотр плана", "Save and Publish": "Сохранить и Опубликовать", "Add Product": "Добавить товар", "Buying Price": "Цена покупки", "Selling Price": "Цена продажи", "Create New Product": "Создать новый продукт", "Select Server Plan at xCloud": "Выберите тарифный план сервера в xCloud", "Select Server Size": "Выберите размер сервера", "Change Plan": "Изменить план", "Duplicate Product": "Дублировать продукт", "Duplicate": "Дублировать", "Favicon": "Фавиконка", "Add Your Favicon": "Добавьте свой фавикон", "Please upload a favicon with a minimum size of 16x16 pixels in PNG or ICO format (max 1MB) for best quality.": "Пожалуйста, загрузите фавикон размером не менее 16x16 пикселей в формате PNG или ICO (макс. 1 МБ) для лучшего качества.", "Upload Favicon": "Загрузить фавикон", "Custom Domain Setup": "Настройка пользовательского домена", "Your Domain Name": "Ваше доменное имя", "Landing Page Settings": "Настройки целевой страницы", "Landing Page": "Главная страница", "Enable it to use and customize the ready landing page settings": "Включите, чтобы использовать и настроить готовые настройки целевой страницы", "Navbar Logo": "Логотип навигации", "Update your logo for navigation bar": "Обновите логотип для панели навигации", "Upload a logo or icon that represent your brand profile.": "Загрузите логотип или значок, представляющий ваш бренд.", "Hero Section": "Герой Секции", "Fast, Secure & Reliable Cloud Hosting": "Быстрый, безопасный и надежный облачный хостинг", "Heading": "Заголовок", "Some more information that uou can add here": "Дополнительная информация, которую вы можете добавить здесь", "Sub Heading": "Подзаголовок", "Create Now": "Создать сейчас", "Button Text": "Текст кнопки", "Button URL": "URL кнопки", "https://www.example.com": "https://www.example.com", "CTA Section": "Раздел CTA", "Social Media Links": "Ссылки на социальные сети", "https://facebook.com/xcloud": "https://facebook.com/xcloud", "Facebook": "Фейсбук", "https://instagram.com/xcloud": "https://instagram.com/xcloud", "Instagram": "Инстаграм", "https://x.com/xcloud": "https://x.com/xcloud", "X.com": "X.com", "https://linkedin.com/xcloud": "https://linkedin.com/xcloud", "Linkedin": "LinkedIn", "https://youtube.com/xcloud": "https://youtube.com/xcloud", "Youtube": "Ю<PERSON><PERSON><PERSON>", "Preview": "Предпросмотр", "Saving...": "Сохранение...", "Connect to Stripe": "Подключиться к Stripe", "Account Name": "Имя аккаунта", "Stripe 3DS Card Setup": "Настройка карты Stripe 3DS", "Privacy Policy Settings": "Настройки Политики Конфиденциальности", "Default Privacy Policy": "Политика конфиденциальности по умолчанию", "Use your own Privacy Policy": "Используйте свою Политику конфиденциальности", "SMTP Settings": "Настройки SMTP", "Use Custom SMTP": "Использовать пользовательский SMTP", "Use my custom smtp credentials for email sending.": "Использовать мои пользовательские SMTP учетные данные для отправки электронной почты.", "SMTP Credentials": "Учетные данные SMTP", "smtp.mailgun.org": "smtp.mailgun.org", "Host": "Хо<PERSON>т", "587": "587", "SMTP username": "Имя пользователя SMTP", "Username": "Имя пользователя", "Encryption": "Шифрование", "ssl": "SSL", "Select Encryption": "Выбрать шифрование", "Terms & Services Settings": "Настройки Условий и Услуг", "Default TOS": "Условия использования по умолчанию", "Use your own TOS": "Используйте свои Условия использования", "Use your own TOS URL": "Используйте собственный URL для Условий использования", "https://example.com/tos": "https://example.com/tos", "Enter the URL of your own Terms & Services page and make sure to add https:// in url": "Введите URL вашей страницы с Условиями использования и убедитесь, что в URL добавлено https://", "Use your own Privacy Policy URL": "Используйте собственный URL политики конфиденциальности", "https://example.com/privacy-policy": "https://example.com/privacy-policy", "Enter the URL of your own Privacy Policy page and make sure to add https:// in url": "Введите URL вашей страницы с Политикой конфиденциальности и убедитесь, что добавили https:// в URL.", "All Clients": "Все клиенты", "Client Name": "Имя клиента", "Press / to search": "Нажмите / для поиска", "You don’t have any Product yet": "У вас пока нет товаров", "Client Details": "Данные клиента", "Client Information": "Информация о клиенте", "Edit Information": "Редактировать информацию", "Billing Address": "Платёжный адрес", "Account Status": "Статус аккаунта", "Payment Information": "Платежная информация", "Here you can find all cards added by your clients": "Здесь вы можете найти все карты, добавленные вашими клиентами", "No payment methods found.": "Способы оплаты не найдены.", "Invoices": "Счета", "Here you can check all your client's previous payments": "Здесь вы можете проверить все предыдущие платежи вашего клиента", "Edit Client Information": "Редактировать информацию о клиенте", "Experience Effortless Hosting With Powerful Features": "Испытайте Легкость Хостинга с Мощными Функциями", "Enjoy the lightning performance backed by powerful features and experience hassle-free hosting": "Наслаждайтесь молниеносной производительностью, поддерживаемой мощными функциями, и беззаботным хостингом.", "Effortless Server Operations": "Беспроблемная работа сервера", "Manage your server effortlessly with our intuitive dashboard. Enjoy high performance and reliability without worrying about downtime, regardless of your technical skill level.": "Управляйте сервером без усилий с помощью нашего интуитивно понятного интерфейса. Наслаждайтесь высокой производительностью и надежностью без простоев, независимо от уровня ваших технических навыков.", "Easy Website Management": "Управление сайтом с легкостью", "We offer a comprehensive platform for managing WordPress sites with ease. With our intuitive dashboard and advanced features, you can manage your sites without any hassle.": "Мы предлагаем комплексную платформу для легкого управления сайтами на WordPress. С нашим интуитивно понятным интерфейсом и расширенными функциями вы сможете управлять своими сайтами без лишних хлопот.", "Powerful Security Measures": "Мощные меры безопасности", "Our advanced security features ensure the safety of your data through advanced protective measures. Also, automated regular updates keep everything safe from threats and remains protected.": "Наши передовые функции безопасности обеспечивают защиту ваших данных с помощью современных мер защиты. Автоматические регулярные обновления защищают от угроз и поддерживают безопасность.", "Real-time Resources Monitoring": "Мониторинг ресурсов в реальном времени", "Monitoring your site and server is essential for ensuring optimal performance and reliability. With real-time measures, you can quickly identify issues provide a seamless experience to your customers.": "Мониторинг вашего сайта и сервера необходим для обеспечения оптимальной производительности и надежности. С помощью показателей в реальном времени вы можете быстро выявлять проблемы и обеспечивать бесперебойную работу для ваших клиентов.", "Transparent & Flexible Pricing for Everyone": "Прозрачные и гибкие цены для всех", "Explore our range of plans designed to meet every needs of every web creator": "Изучите наши тарифные планы, разработанные для удовлетворения потребностей каждого веб-разработчика.", "Includes": "Включает", "All rights reserved.": "Все права защищены.", "Terms of Service": "Условия использования", "Terms & Services": "Условия и услуги", "Just One Click Away from Completing Your Order": "Всего один клик до завершения заказа", "Already have an account?": "Уже есть аккаунт?", "Sign In": "Войти", "Integrate any cloud provider to manage server and sites in xCloud": "Интегрируйте любого облачного провайдера для управления серверами и сайтами в xCloud", "Choose Your Plan": "Выберите план", "Cost": "Стоимость", "Applied Coupon": "Применён купон", "Purchase Limit Reached!": "Достигнут лимит покупок!", "Only one purchase remaining!": "Осталась только одна покупка!", "Split Pay": "Разделить платёж", "Ready to confirm your purchase?": "Готовы подтвердить покупку?", "By clicking 'Confirm', a charge of": "Нажимая «Подтвердить», вы соглашаетесь с оплатой в размере", "will be applied to your saved credit card.": "будет применено к сохранённой кредитной карте.", "Please switch to your team to checkout": "Пожалуйста, переключитесь на свою команду для оформления заказа", "You are currently in the Playground Team. You can't checkout from here": "Вы находитесь в команде Playground. Вы не можете выйти отсюда.", "Total Purchases": "Общая сумма покупок", "Only": "Только", "purchase remaining": "Оставшаяся покупка", "Database Root Password": "Пароль администратора базы данных", "Do you want to turn off Auto Backups?": "Вы хотите отключить автоматическое резервное копирование?", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks": "Отключение автоматического резервного копирования означает потерю легкого восстановления данных, мобильности резервных копий и защиты от сбоев; нажмите, чтобы продолжить, если вы понимаете риски.", "Create Server": "Создать сервер", "Report an Issue": "Сообщить о проблеме", "Something went wrong": "Что-то пошло не так", "Please try again or report an issue to support": "Попробуйте снова или сообщите о проблеме в службу поддержки", "Retry": "Повторить попытку", "You may exit this window and navigate away": "Вы можете закрыть это окно и перейти на другую страницу", "Check Connection": "Проверить подключение", "Add Site": "Добавить сайт", "Install Stack": "Установить Stack", "SSH User": "Пользователь SSH", "SSH Key": "SS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install stack on the server": "Установить стек на сервере", "It will install these following services on the server": "На сервер будут установлены следующие службы", "PHP 7 .4": "PHP 7.4", "MySQL 8 .0": "MySQL 8.0", "Redis": "Редис", "and more. .": "и другое...", "Install": "Установить", "Log in": "Войти", "Register": "Регистрация", "White Label": "Белая Метка", "Playground": "Игровая площадка", "Email not verified": "Электронная почта не подтверждена", "Please check your email to verify your account. Verify your email": "Пожалуйста, проверьте свою электронную почту, чтобы подтвердить учетную запись. Подтвердите свою почту", "Do you want to switch from": "Хотите переключиться с", "You can create free demo sites under Playground Team which will be removed after 24 hours. From Team Settings page you can switch back to your default team again.": "Вы можете создавать бесплатные демонстрационные сайты в команде Playground, которые будут удалены через 24 часа. На странице настроек команды вы можете вернуться к своей команде по умолчанию.", "Info": "Инфо", "You can switch to any team you belong by clicking on the team name in the top right header.": "Вы можете переключиться на любую команду, к которой принадлежите, нажав на название команды в правом верхнем углу.", "Switch to": "Переключить на", "Yes": "Да", "No": "Нет", "Try xCloud Playground": "Попробуйте xCloud Playground", "Do you want to Switch your team?": "Хотите сменить команду?", "Please accept or check your email to switch to": "Пожалуйста, примите или проверьте свою электронную почту, чтобы переключиться на", "team. You can also visit": "Команда. Вы также можете посетить", "to manage your teams.": "Управление командами.", "Please accept or check your email to switch to your team. You can also visit": "Пожалуйста, примите или проверьте свою электронную почту, чтобы перейти к вашей команде. Вы также можете посетить", "My Profile": "Мой профиль", "Support": "Поддержка", "Documentation": "Документация", "Affiliates": "Пар<PERSON><PERSON>ё<PERSON>ы", "Admin Panel": "Панель администратора", "Reports": "От<PERSON>ёты", "Horizon": "Горизонт", "Telescope": "Телескоп", "Vapor UI": "Интерфейс Vapor", "Documents": "Документы", "Find Servers or Sites": "Найти серверы или сайты", "Search Results": "Результаты поиска", "of": "от", "Check our ": "Проверьте наши", " to get free hosting for 6 months with new Vultr signups.": "Получите бесплатный хостинг на 6 месяцев при новой регистрации в Vultr.", " to get a quick start.": "для быстрого начала", "Check out our ": "Ознакомьтесь с нашим", "Quick Start.": "Быстрый старт", "No vulnerabilities were found on your sites. To setup vulnerability scanner please check this": "Уязвимости на ваших сайтах не обнаружены. Чтобы настроить сканер уязвимостей, пожалуйста, проверьте это.", "Hey there! You have no ": "Привет! У вас нет", " yet.": "<PERSON><PERSON><PERSON>.", "Quick Start Documentation": "Документация для быстрого начала", "This feature is not available in Playground.": "Эта функция недоступна в Playground.", "No custom Nginx configurations found!": "Пользовательские конфигурации Nginx не найдены!", "Hey there! You have no": "Привет! У вас нет", "plugins.": "Плагины", "themes.": "Темы", "to claim": "Получить", "$20 free credits": "20 бесплатных кредитов", "if you are a new user on Hetzner.": "если вы новый пользователь на <PERSON>.", " to claim ": "Получить", "Read our ": "Прочитайте наш", " on Heztner API integration.": "Интеграция с <PERSON>ner", "Choose AWS Services": "Выберите сервисы AWS", "Select one or more AWS services for which you want to use this credentials.": "Выберите одну или несколько AWS-сервисов, для которых вы хотите использовать эти учетные данные.", "Verify": "Проверить", "$300 free credits": "300 $ бесплатных кредитов", "if you are a new user on Google Cloud Platform.": "Если вы новый пользователь Google Cloud Platform.", "Back": "Назад", "$100 free credits": "100 $ бесплатных кредитов", "if you are a new user on Linode.": "Если вы новый пользователь на Linode.", "You can connect a fresh": "Вы можете подключить новое", "Ubuntu 24.04 LTS x64": "Ubuntu 24.04 LTS x64", "server from any provider if you have root access.": "сервер от любого провайдера, если у вас есть root-доступ.", "$200 free credits": "200 долларов бесплатного кредита", "if you are a new user on DigitalOcean.": "если вы новый пользователь на DigitalOcean.", "to collect your API key. Read our": "Чтобы получить ваш API-ключ, прочитайте наше", "and Use": "Использовать", "XCLOUD": "ИКЛАУД", "coupon code to claim": "Код купона для активации", "$100": "100 долларов США", "free credits on new vultr singups for 180 days.": "Бесплатные кредиты на новые регистрации Vultr в течение 180 дней.", "Database Info": "Информация о базе данных", "Refreshing...": "Обновление...", "By default you can use": "По умолчанию вы можете использовать", "user. But if you want to do it for specific site enter the specific site user name here.": "Пользователь. Если вы хотите сделать это для конкретного сайта, введите здесь имя пользователя для этого сайта.", "Update PHP Configuration": "Обновить конфигурацию PHP", "Default PHP Version": "Версия PHP по умолчанию", "Default Server PHP Version": "Версия PHP сервера по умолчанию", "This will set the default PHP version for the CLI and for new site installations. However, it won't affect the PHP versions of any existing sites, and the default version for new sites can still be changed during installation.": "Это установит версию PHP по умолчанию для CLI и новых установок сайтов. Однако это не повлияет на версии PHP существующих сайтов, и версию по умолчанию для новых сайтов можно будет изменить во время установки.", "Run Custom Command": "Выполнить пользовательскую команду", "Most Recent Commands": "Последние команды", "This command will be executed on the server. Please make sure you are running the correct command. Running incorrect commands can break your server.": "Эта команда будет выполнена на сервере. Пожалуйста, убедитесь, что вы запускаете правильную команду. Неправильные команды могут повредить ваш сервер.", "If you want to do it for specific site enter the specific site user name here.": "Если вы хотите сделать это для конкретного сайта, введите здесь имя пользователя этого сайта.", "Run Command": "Выполнить команду", "Run New Command": "Выполнить новую команду", "Server Health": "Состояние сервера", "Filter": "Фильтр", "Event": "Событие", "Date & Time": "Дата и время", "Firewall Management": "Управление брандмауэром", "Vulnerability Scan": "Сканирование уязвимостей", "Your server is set to automatically update security patches. A reboot is required to complete the updates. You can configure the server to reboot automatically at a preferred time or choose to do it manually.": "Ваш сервер настроен на автоматическое обновление исправлений безопасности. Для завершения обновлений требуется перезагрузка. Вы можете настроить автоматическую перезагрузку сервера в удобное время или выполнить её вручную.", "Provider Backup Setting": "Настройка резервного копирования провайдера", "Information": "Информация", "Notes": "Заметки", "Connection Settings": "Настройки подключения", "Server Settings": "Настройки сервера", "Update Timezone": "Обновить часовой пояс", "Magic Login Settings": "Настройки Магического Входа", "Time Zone": "Часовой пояс", "Turn off Indexing": "Отключить индексацию", "Turning off this setting will prevent search engines from indexing your staging site.": "Отключение этой настройки предотвратит индексацию вашего тестового сайта поисковыми системами.", "Search Engine Visibility": "Видимость в поисковых системах", "Discourage search engines from indexing this site": "Запретить поисковым системам индексировать этот сайт", "It is up to search engines to honor this request.": "Уважение этого запроса зависит от поисковых систем.", " offers a temporary test domain that allows you to quickly deploy your site. ": "предоставляет временный тестовый домен, который позволяет быстро развернуть ваш сайт.", "This temporary domain enables you to share your work in progress with teammates or clients for review ": "Этот временный домен позволяет вам делиться текущей работой с коллегами или клиентами для проверки.", "and input before you finalize and launch it with your own custom domain for public access.": "и введите данные перед тем, как завершить и запустить с вашим собственным доменом для публичного доступа.", "HTTPS Is Enabled": "HTTPS включён", "Do You Want To Enable HTTPS?": "Включить HTTPS?", "Please make sure that the database connection configuration is correct and if you are creating a new database you may want to store the information in a secure place. Storing the wp-config.php file in Git is not recommended and if the wp-config.php file is absent from your Git repository, ": "Пожалуйста, убеди<PERSON>е<PERSON><PERSON>, что конфигурация подключения к базе данных правильная. Если вы создаете новую базу данных, рекомендуется хранить информацию в безопасном месте. Хранение файла wp-config.php в Git не рекомендуется, и если файл wp-config.php отсутствует в вашем репозитории Git,", " will automatically generate it from wp-config-sample.php, adding database credentials. Additional credentials can be added later on the WP Config page in the ": "Автоматически сгенерирует его из wp-config-sample.php, добавив учетные данные базы данных. Дополнительные учетные данные можно добавить позже на странице WP Config.", " site dashboard. If wp-config.php is already in the repository, ": "Панель управления сайтом. Если файл wp-config.php уже находится в репозитории,", " won't make any changes.": "Изменений не будет.", "Database Management": "Управление базой данных", "Your source server must be": "Ваш исходный сервер должен быть", "or": "или", "OLS": "ОЛС", "server with": "Сервер с", "Ubuntu 20.04 or 22.04 LTS x64": "Ubuntu 20.04 или 22.04 LTS x64", "and should have": "и должно иметь", "access.": "Доступ", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Email Address": "Адрес электронной почты", "By default, emails are sent from": "По умолчанию, электронные письма отправляются с", "To send emails from your domain, enter your custom SMTP credentials (e.g., Mailgun, Elastic Email)": "Чтобы отправлять электронные письма с вашего домена, введите ваши пользовательские SMTP-учетные данные (например, Mailgun, Elastic Email).", "Two Factor Authentication": "Двухфакторная аутентификация", "You have enabled two factor authentication.": "Вы включили двухфакторную аутентификацию.", "Finish enabling two factor authentication.": "Завершите включение двухфакторной аутентификации.", "You have not enabled two factor authentication.": "Вы не включили двухфакторную аутентификацию.", "Enable": "Включить", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application.": "Чтобы завершить включение двухфакторной аутентификации, отсканируйте следующий QR-код с помощью приложения-аутентификатора на вашем телефоне.", "Two factor authentication is now enabled.": "Двухфакторная аутентификация включена.", "Enter the text code below instead if you can't use the barcode.": "Введите текстовый код ниже, если не можете использовать штрих-код.", "Or, Scan the": "Или отсканируйте", "QR provided": "QR предоставлен", "with your phone's two-factor authentication app.": "с помощью приложения двухфакторной аутентификации на вашем телефоне.", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "Храните эти коды восстановления в надежном менеджере паролей. Они могут быть использованы для восстановления доступа к вашему аккаунту, если устройство двухфакторной аутентификации будет утеряно.", "Setup Key": "Ключ настройки", "Regenerate Recovery Codes": "Сгенерировать новые коды восстановления", "Show Recovery Codes": "Показать коды восстановления", "Create SSH Key": "Создать SSH-ключ", "Create A New SSH Key": "Создать новый SSH-ключ", "Verify & Save for": "Подтвердить и Сохранить для", "Buy & Add": "Купить и Добавить", "Make sure you have active billing plan to use this feature.": "Убедите<PERSON>ь, что у вас активен тарифный план для использования этой функции.", "This label will be used to identify this SMTP provider in": "Эта метка будет использоваться для идентификации этого SMTP-провайдера в", "Optional if you're using global API key of Mailgun": "Необязательно, если вы используете глобальный API-ключ Mailgun", ". i.e. Mailgun, Sendgrid, etc.": "<PERSON>gun, Sendgrid и т. д.", "This label will be used to identify this SMTP provider in xCloud. i.e. Mailgun, Sendgrid, etc.": "Эта метка будет использоваться для идентификации этого SMTP-провайдера в xCloud, например, Mailgun, Sendgrid и т.д.", "Authorize on WhatsApp": "Авторизоваться в WhatsApp", "Authorize on": "Авторизоваться в", "Please go to Telegram and start talking with": "Пожалуйста, перейдите в Telegram и начните общение с", "To authenticate your chat, send this command to xCloud Notification Bot.": "Чтобы подтвердить чат, отправьте эту команду боту уведомлений xCloud.", "Due Date": "Срок выполнения", "Recent Events": "Недавние события", "Currently No Event Available": "Событий нет", "Mark all as read": "Отметить все как прочитанные", "No Notifications Available!": "Уведомлений нет!", "provides": "предоставляет", "Environment to do all the development to your site with a temporary domain.": "Среда для разработки вашего сайта с использованием временного домена.", "When you are ready, simply select the GO LIVE option and add your own domain to make the site available to your user/visitor.": "Когда будете готовы, просто выберите опцию \"Запустить\" и добавьте свой домен, чтобы сделать сайт доступным для ваших пользователей/посетителей.", "Demo Site Management": "Управление демо-сайтом", "Verifying DNS..": "Проверка DNS...", "Your staging SSL is being managed by": "Ваш промежуточный SSL управляется", "Your SSL is being managed by Cloudflare.": "Ваш SSL управляется Cloudflare.", "If you turn on this option then": "Если вы включите эту опцию, то", "will automatically enable NGINX to directly serve previously": "Автоматически включит NGINX для прямой обработки ранее", "cached files without calling WordPress or any PHP. It also adds headers to cached CSS, JS, and images via": "Кэшированные файлы без вызова WordPress или PHP. Также добавляет заголовки кэшированным CSS, JS и изображениям через", "the browser cache. As a result, your website will be much faster. If someone visits your website again,": "кэш браузера. В результате ваш сайт будет работать намного быстрее. Если кто-то снова посетит ваш сайт,", "their browser can load the cached files directly from their own computer instead of making a request to": "Их браузер может загружать кэшированные файлы напрямую с их компьютера, вместо запроса к", "your web server. This reduces the number of requests to your server and further speeds up the loading of your website.": "ваш веб-сервер. Это уменьшает количество запросов к вашему серверу и ускоряет загрузку вашего сайта.", "You can read more from our documentation": "Вы можете узнать больше в нашей документации", "Saving Changes...": "Сохранение изменений...", "Make sure to add your SSH key on the SSH Authentication section.": "Убедитесь, что добавили свой SSH-ключ в разделе аутентификации SSH.", "Enter the configuration file name": "Введите имя файла конфигурации", "Save Config": "Сохранить настройки", "Create Custom Nginx Configuration for": "Создать пользовательскую конфигурацию Nginx для", "Select Template": "Выбрать шаблон", "IP Whitelist/Blacklist": "Белый список/Черный список IP", "WP-Cron and xCloud-Cron": "WP-Cron и xCloud-Cron", "Update PHP Settings": "Обновить настройки PHP", "All existing files and data on this site will be deleted": "Все существующие файлы и данные на этом сайте будут удалены", "Create a new backup before restoring?": "Создать новую резервную копию перед восстановлением?", "Restore": "Восстановить", "Take Backup": "Создать резервную копию", "Full Backup": "Полная резервная копия", "Are you sure you want to take a full backup? This will create a complete backup of all your data. Future incremental backups will be based on this full backup.": "Вы уверены, что хотите создать полную резервную копию? Это создаст полную копию всех ваших данных. Будущие инкрементные копии будут основываться на этой полной копии.", "Incremental Backup": "Инкрементное резервное копирование", "This will add to your previous backups by only saving the changes since the last backup.": "Это добавит к вашим предыдущим резервным копиям, сохраняя только изменения с последнего резервного копирования.", "Site List": "Список сайтов", "Management": "Управление", "Settings": "Настройки", "Custom Cron Jobs": "Пользовательские задания Cron", "PHP Configuration": "Конфигурация PHP", "Commands": "Команды", "Monitoring": "Мони<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Events": "События", "Firewall management": "Управление брандмауэром", "Vulnerability Settings": "Настройки уязвимостей", "Full Server migration": "Полная миграция сервера", "Metadata": "Метаданные", "Site Overview": "Обзор сайта", "Caching": "Кэширование", "Email Configuration": "Настройка электронной почты", "Previous Backups": "Предыдущие резервные копии", "Backup Settings": "Настройки резервного копирования", "Site Monitoring": "Монитор<PERSON>нг сайта", "Access Data": "Доступ к данным", "SSH/sFTP": "SSH/sFTP", "Tools": "Инструменты", "Nginx and Security": "Nginx и безопасность", "Nginx Customization": "Настройка Nginx", "IP Management": "Управление IP", "Site Settings": "Настройки сайта", "User Profile": "Профиль пользователя", "Browser Sessions": "Сеансы браузера", "Integrations": "Интеграции", "Cloudflare": "Cloudflare", "Notification": "Уведомление", "Billing": "Выставление счетов", "Manual Invoices": "Ручные счета", "Whitelabel": "Белая метка", "All Events": "Все события", "Payment": "Оплата", "Brand": "<PERSON>р<PERSON><PERSON>д", "SMTP": "SMTP", "Create Products": "Создать продукты", "Setup your brand to get started": "Настройте ваш бренд, чтобы начать", "Setup your payment method": "Настройте способ оплаты", "Create customized plans": "Создайте персонализированные планы", "Setup your domain easily": "Легко настройте ваш домен", "Search sites...": "Поиск сайтов...", "Create Account": "Создать аккаунт", "Password must be at least  8 characters and should contain uppercase, number and special character": "Пароль должен содержать не менее 8 символов, включая заглавную букву, цифру и специальный символ.", "Filter by Date": "Фильтр по дате", "Name your blueprint": "Назовите ваш шаблон", "Search": "Поиск", "Are You Sure You Want To Delete Server": "Вы уверены, что хотите удалить сервер?", "to confirm": "Подтвердить", "Are You Sure You Want To Delete Site": "Вы уверены, что хотите удалить сайт?", "Edit Server Provider": "Редактировать поставщика сервера", "Add Server Provider": "Добавить поставщика сервера", "check connection": "Проверить подключение", "Clone Blueprint": "Клонировать шаблон", "Delete Blueprint": "Удалить чертёж", "Are you sure you want to delete this blueprint?": "Вы уверены, что хотите удалить этот чертёж?", "Are you sure you want to clone this blueprint?": "Вы уверены, что хотите клонировать этот чертёж?", "Clone": "Клонировать", "Set as Default Blueprint": "Установить как шаблон по умолчанию", "Are you sure you want to set this blueprint as default?": "Вы уверены, что хотите установить этот шаблон по умолчанию?", "Are you sure?": "Вы уверены?", "You won't be able to revert this!": "Это действие необратимо!", "Yes, Remove!": "Да, удалить!", "Are you sure you want to update WordPress?": "Вы уверены, что хотите обновить WordPress?", "Update WordPress Core": "Обновить ядро WordPress", "Are you sure you want to active this theme?": "Вы уверены, что хотите активировать эту тему?", "Activate Theme": "Активировать тему", "Are you sure you want to update those plugin?": "Вы уверены, что хотите обновить эти плагины?", "You have selected": "Вы выбрали", "plugins": "Плагины", "Are you sure you want to update those plugins?": "Вы уверены, что хотите обновить эти плагины?", "Are you sure you want to update these themes?": "Вы уверены, что хотите обновить эти темы?", "themes": "Темы", "Do you want to deactivate Adminer?": "Вы хотите отключить Adminer?", "Do you want to activate Adminer?": "Хотите активировать Adminer?", "We recommend deactivating it when not required.": "Рекомендуем отключать, когда не требуется.", "Yes, restore it!": "Да, восстановить!", "Are you sure you want to restore this server?": "Вы уверены, что хотите восстановить этот сервер?", "Are you sure you want to delete this card?": "Вы уверены, что хотите удалить эту карточку?", "You can add another card.": "Вы можете добавить другую карту.", "Yes, log out!": "Да, выйти!", "Are you sure you want to delete this integration?": "Вы уверены, что хотите удалить эту интеграцию?", "You want to disconnect": "Отключиться?", "Yes, Disconnect!": "Да, отключить!", "You want to reconnect": "Вы хотите восстановить соединение", "Yes, Reconnect!": "Да, переподключиться!", "Yes, disable it!": "Да, отключить!", "Are you sure you want to disable HTTPS?": "Вы уверены, что хотите отключить HTTPS?", "Yes, Leave Team!": "Да, покинуть команду!", "Edit Storage Provider": "Редактировать поставщика хранилища", "Add Storage Provider": "Добавить поставщика хранилища", "Yes, leave team!": "Да, покинуть команду!", "This will remove the cron job from the server.": "Это удалит задание cron с сервера.", "Are you sure you want to disable this firewall rule?": "Вы уверены, что хотите отключить это правило брандмауэра?", "You can enable it later.": "Вы можете включить это позже.", "Yes, Disable": "Да, отключить", "Are you sure you want to enable this firewall rule?": "Вы уверены, что хотите включить это правило брандмауэра?", "You can disable it later.": "Вы можете отключить это позже.", "Yes, Enable": "Да, включить", "Are you sure you want to delete this firewall rule?": "Вы уверены, что хотите удалить это правило брандмауэра?", "Deleting firewall rule will remove it permanently.": "Удаление правила брандмауэра приведет к его окончательному удалению.", "Yes, Delete": "Да, удалить", "Are you sure you want to unban": "Вы уверены, что хотите снять блокировку?", "This will remove the IP address from the banned list.": "Это удалит IP-адрес из списка заблокированных.", "Yes, Unban": "Да, Разблокировать", "This will remove the sudo user from the server.": "Это удалит пользователя sudo с сервера.", "Authentication will be disabled for this site": "Аутентификация для этого сайта будет отключена", "Yes, Remove": "Да, удалить", "Are you sure you want to delete this backup?": "Вы уверены, что хотите удалить эту резервную копию?", "This action cannot be undone.": "Это действие нельзя отменить.", "Are you sure you want to remove this failed backup?": "Вы уверены, что хотите удалить эту неудавшуюся резервную копию?", "Do you want to deactivate Tiny File Manager?": "Вы хотите деактивировать Tiny File Manager?", "Do you want to activate Tiny File Manager?": "Хотите активировать Tiny File Manager?", "Are you sure you want to disable": "Вы уверены, что хотите отключить?", "caching?": "Кэширование?", "Are you sure you want to disable redis object caching?": "Вы уверены, что хотите отключить кэширование объектов Redis?", "Yes, switch it!": "Да, переключить!", "Are you sure you want to switch to": "Вы уверены, что хотите переключиться на", "plugin?": "Плагин?", "Are you sure you want to disable full page caching?": "Вы уверены, что хотите отключить кэширование всей страницы?", "Yes, Remove it!": "Да, удалить!", "Are you sure you want to remove this IP address?": "Вы уверены, что хотите удалить этот IP-адрес?", "Are you sure you want to disable LiteSpeed Cache?": "Вы уверены, что хотите отключить LiteSpeed Cache?", "Are you sure you want to delete this Nginx Configuration?": "Вы уверены, что хотите удалить эту конфигурацию Nginx?", "It will be permanently deleted.": "Это будет удалено навсегда.", "Enable Nginx File Regeneration": "Включить регенерацию файлов <PERSON>x", "This will prevent xCloud from regenerating nginx file on any changes made to the site. You will have to manually regenerate the nginx file.": "Это предотвратит автоматическую регенерацию файла nginx в xCloud при изменениях на сайте. Вам придется вручную регенерировать файл nginx.", "This will allow xCloud to regenerate nginx file on any changes made to the site. You will not have to manually regenerate the nginx file.": "Это позволит xCloud автоматически обновлять файл nginx при любых изменениях на сайте. Вам не нужно будет вручную обновлять файл nginx.", "Enable Site": "Включить сайт", "Disabling the site will make it inaccessible. Are you sure you want to disable it?": "Отключение сайта сделает его недоступным. Вы уверены, что хотите отключить его?", "Enabling the site will make it accessible. Are you sure you want to enable it?": "Включение сайта сделает его доступным. Вы уверены, что хотите включить его?", "This action will run the rescue process on the site. Are you sure you want to run it?": "Это действие запустит процесс восстановления на сайте. Вы уверены, что хотите его запустить?", "Yes, Run Now": "Да, запустить сейчас", "Are you sure you want to update the plugin?": "Вы уверены, что хотите обновить плагин?", "Plugin will be updated to": "Плагин будет обновлён до", "Are you sure you want to not ignore this vulnerability?": "Вы уверены, что хотите не игнорировать эту уязвимость?", "Are you sure you want to ignore this vulnerability?": "Вы уверены, что хотите игнорировать эту уязвимость?", "Are you sure you want to update the theme?": "Вы уверены, что хотите обновить тему?", "You have select": "Вы выбрали", "theme": "Тема", "Are You Sure You Want To Delete this SSH Key:": "Вы уверены, что хотите удалить этот SSH-ключ?", "Generate Invoice": "Создать счет", "Add A New Payment Method": "Добавить новый способ оплаты", "Log Out Other Browser Sessions": "Завершить сеансы в других браузерах", "Log Out": "Выйти", "Log Out of All Sessions": "Выйти из всех сеансов", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "Введите пароль, чтобы подтвердить выход из других сеансов браузера на всех ваших устройствах.", "You won't be able to revert this to your current device.": "Вы не сможете вернуть это на текущее устройство.", "Are you sure you want to delete": "Вы уверены, что хотите удалить?", "You cannot delete this team": "Вы не можете удалить эту команду", "You cannot delete your personal team": "Вы не можете удалить свою личную команду", "You cannot delete your current team": "Вы не можете удалить свою текущую команду", "Admin Username": "Имя пользователя администратора", "SIZE": "РАЗМЕР", "REGION": "Регион", "UBUNTU": "УБУНТУ", "Deleting..": "Удаление...", "Delete SSH Key": "Удалить SSH-ключ", "Add a new database to your server": "Добавить новую базу данных на ваш сервер", "Add a new user to your database": "Добавить нового пользователя в вашу базу данных", "Edit database user:": "Редактировать пользователя базы данных:", "Are You Sure You Want To Delete this database:": "Вы уверены, что хотите удалить эту базу данных?", "Are You Sure You Want To Delete this database user:": "Вы уверены, что хотите удалить этого пользователя базы данных?", "Cron Job Output": "Вывод задания Cron", "Add new firewall rule for": "Добавить новое правило брандмауэра для", "Ban an IP Address on": "Заблокировать IP-адрес на", "Reboot Time": "Время перезагрузки", "Your server is set to automatically reboot at": "Ваш сервер настроен на автоматическую перезагрузку в", "You can change the reboot time if you'd like.": "Вы можете изменить время перезагрузки, если хотите.", "CPU Usage": "Использование ЦП", "Reboot": "Перезагрузка", "Are You Sure You Want To": "Вы уверены, что хотите", "Service?": "Сервис?", "Are You Sure You Want To Restore Backup For The Server": "Вы уверены, что хотите восстановить резервную копию для сервера?", "Are You Sure You Want To Resize Server": "Вы уверены, что хотите изменить размер сервера?", "Resize": "Изменить размер", "Initiating Resizing...": "Запуск изменения размера...", "Manage Database": "Управление базой данных", "Last Updated": "Последнее обновление", "Deactivate": "Отключить", "Last Checked": "Последняя проверка", "Current Version": "Текущая версия", "RAM Usages": "Использование ОЗУ", "Disk Usages": "Использование диска", "Choose SSH Keys": "Выберите SSH-ключи", "No SSH Keys found.": "SSH-ключи не найдены.", "Search SSH Keys": "Поиск SSH ключей", "Are You Sure You Want To Disable Site": "Вы уверены, что хотите отключить сайт?", "Add Your Own Database": "Добавить собственную базу данных", "Please provide your new database information": "Пожалуйста, укажите данные новой базы данных", "Cron Interval for Server": "Интерв<PERSON><PERSON>ron для сервера", "Add this public key in your Git repository as deploy key. This is necessary to enable": "Добавьте этот открытый ключ в ваш репозиторий Git как ключ развертывания. Это необходимо для включения.", "to clone the repository, ensuring a secure and authorized access for automated processes such as cloning.": "Клонировать репозиторий, обеспечивая безопасный и авторизованный доступ для автоматизированных процессов, таких как клонирование.", "Click On The Plugin Download Link": "Щелкните по ссылке для загрузки плагина", "Upload the zipped plugin file to WordPress under 'Add New' in the 'Plugins' tab": "Загрузите zip-файл плагина в WordPress, выбрав «Добавить новый» на вкладке «Плагины».", "Click 'Activate' to install the plugin": "Нажмите «Активировать», чтобы установить плагин", "Copy the authentication token and paste it into the plugin page to complete the setup": "Скопируйте токен аутентификации и вставьте его на страницу плагина, чтобы завершить настройку.", "Upload The Plugin Zipped File On WordPress": "Загрузите ZIP-файл плагина в WordPress", "Click On The ‘Activate’ Button To Install Plugin": "Нажмите кнопку «Активировать», чтобы установить плагин", "Copy The Authentication Token And Paste It Into The Plugin Page": "Скопируйте токен аутентификации и вставьте его на страницу плагина", "Export Zipped File From Your Existing Site": "Экспортировать архив с вашего текущего сайта", "Upload The Exported Zip or Tar File Here": "Загрузите экспортированный файл в формате Zip или Tar здесь", "You must have root access to perform Full Server Migration": "Для выполнения полной миграции сервера необходимы права root-доступа", "Migrate multiple cPanel sites from shared hosting to xCloud easily for faster, scalable and secure hosting.": "Легко перенесите несколько сайтов cPanel с общего хостинга на xCloud для более быстрого, масштабируемого и безопасного хостинга.", "If the fetched domain is incorrect, you can edit it here, the domain name needs to be accurate for successful migration": "Если полученный домен неверен, вы можете отредактировать его здесь. Имя домена должно быть точным для успешной миграции.", "Total migrated": "Всего перенесено", "Verifying ...": "Проверка ...", "Choose Web Server": "Выберите веб-сервер", "Updating Backup Schedule...": "Обновление расписания резервного копирования...", "Update Backup Schedule": "Обновить расписание резервного копирования", "Set Schedule": "Установить расписание", "Are You Sure?": "Вы уверены?", "Restoring...": "Восстановление...", "Enabling Backup...": "Включение резервного копирования...", "Disabling Backup...": "Отключение резервного копирования...", "Backup?": "Резервное копирование?", "Provider Backup Schedule": "Расписание резервного копирования провайдера", "Update Schedule": "Расписание обновлений", "Select Day of Week": "Выберите день недели", "Select Day of Month": "Выберите день месяца", "View all": "Просмотреть все", "You are now managing": "Вы теперь управляете", "site on": "Сайт включен", "server.": "Сервер", "You can not use your own domain with xCloud Free Email Service..": "Вы не можете использовать собственный домен с бесплатным почтовым сервисом xCloud.", "For example, you can send <NAME_EMAIL> or <EMAIL>. It is up to you!": "Например, вы можете отправлять письма с адресов <EMAIL> или <EMAIL>. Выбор за вами!", "Are you sure you want to restore this backup?": "Вы уверены, что хотите восстановить эту резервную копию?", "X-Frame-Options": "X-Frame-Options", "SAMEORIGIN": "SAMEORIGIN", "Update Tags": "Обновить теги", "Zone": "Зона", "Choose Zones": "Выберите зоны", "Choose Regions": "Выберите регионы", "Choose Sizes": "Выберите размеры", "site": "Сайт", "No Vulnerabilities Found": "Уязвимости не обнаружены", "Vulnerability Scan Not Enabled": "Сканирование уязвимостей не включено", "Updates Available": "Доступны обновления", "sudo user": "sudo пользователь", "IP": "IP", "Package": "Пак<PERSON>т", "Can't find what you're looking for?": "Не можете найти то, что ищете?", "Contact Support": "Связаться с поддержкой", "Nginx Options": "Парамет<PERSON><PERSON> Nginx", "OpenLiteSpeed Options": "Параметры OpenLiteSpeed", "Link Google Drive Account": "Подключить аккаунт Google Диск", "Upload a zipped file of your existing WordPress website (max size: 500 MB)": "Загрузите zip-архив вашего существующего сайта WordPress (макс. размер: 500 МБ)", "Recreate Site from Backup": "Восстановить сайт из резервной копии", "Restore site backup from local or remote storage easily to create a site": "Восстановите резервную копию сайта из локального или удаленного хранилища для создания сайта.", "Language Settings": "Настройки языка", "Copyright": "Авторское право", "Taking Payment...": "Обработка платежа...", "Bill": "Счет", "Pay": "Оплатить", "Additional Information": "Дополнительная информация", "Name of Invoice": "Название счета", "Notification Language Settings": "Настройки языка уведомлений", "Turning on this setting will prevent search engines from indexing your staging site.": "Включение этой настройки предотвратит индексацию вашего тестового сайта поисковыми системами.", "For example, you can send emails from": "Например, вы можете отправлять электронные письма из", "It is up to you!": "Вам решать!", "Adminer and File Manager won't work with 7G/8G firewall. We recommend SFTP if you're familiar with it.": "Adminer и Файловый менеджер не будут работать с брандмауэром 7G/8G. Мы рекомендуем использовать SFTP, если вы с ним знакомы.", "Demo Site Setup": "Настройка демонстрационного сайта", "System Cron Jobs": "Системные задания Cron", "others": "Другие", "Items": "Элементы", "List of Items": "Список элементов", "Integrate New Item": "Добавить новый элемент", "Add New Item": "Добавить новый элемент", "License": "Лицензия", "Are you sure you want to delete?": "Вы уверены, что хотите удалить?", "Item deleted successfully": "Элемент успешно удалён", "Failed to delete Item": "Не удалось удалить элемент", "Select Integrated Plugin": "Выберите встроенный плагин", "Boost your WordPress site’s performance with Object Cache Pro by caching frequently accessed data in Redis. This reduces database queries, enhances speed, and is especially beneficial for dynamic, content-heavy websites.": "Улучшите производительность вашего сайта на WordPress с помощью Object Cache Pro, кэшируя часто запрашиваемые данные в Redis. Это снижает количество запросов к базе данных, увеличивает скорость и особенно полезно для динамичных сайтов с большим количеством контента.", "Debug Mode": "Режим отладки", "Object Cache Pro": "Object Cache Pro", "Select Plugin": "Выбрать плагин", "Update Item": "Обновить элемент", "Add Item": "Добавить элемент", "Enter your license label": "Введите метку лицензии", "Enter your license key": "Введите лицензионный ключ", "Edit Item": "Редактировать элемент", "Access File Manager": "Открыть Файловый Менеджер", "Always Enabled": "Всегда включено", "Keep the File Manager accessible at all times.": "Держите Диспетчер файлов всегда доступным.", "Disable File Manager": "Отключить Файловый Менеджер", "Specify how long the File Manager should remain enabled before it’s automatically disabled.": "Укажите, как долго Диспетчер файлов должен оставаться включенным, прежде чем будет автоматически отключен.", "Set Auto Disable Duration": "Установить длительность автоматического отключения", "Choose to keep the File Manager active for always or schedule it to disable after a certain time.": "Выберите, чтобы Файловый менеджер всегда оставался активным, или запланируйте его отключение через определенное время.", "Access Adminer": "Доступ к Adminer", "Keep the Adminer accessible at all times.": "Держите Adminer доступным в любое время.", "Disable Adminer": "Отключи<PERSON>ь Adminer", "Specify how long the Adminer should remain enabled before it’s automatically disabled.": "Укажите, как долго Adminer должен оставаться включенным, прежде чем будет автоматически отключен.", "Set Auto Deactivate Duration": "Установить длительность автоматического отключения", "Choose to keep the Adminer active for always or schedule it to disable after a certain time.": "Выберите, что<PERSON>ы Adminer оставался активным всегда, или запланируйте его отключение через определенное время.", "Customization": "Настройка", "The File Manager will be disable within": "Файловый менеджер будет отключен через", "The Adminer will be disable within": "Админер будет отключен через", "Manually Upload Website": "Ручная Загрузка Сайта", "Upload a zipped file of your existing website": "Загрузите zip-архив вашего существующего сайта", "Upload a zipped file of your existing website (max size: 500 MB)": "Загрузите архивированный файл вашего существующего сайта (максимальный размер: 500 МБ)", "Custom PHP": "Пользовательский PHP", "laravel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Laravel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Coming": "Скоро", "Update Web Root": "Обновить корневой каталог веб-сайта", "Web Root": "Корневая папка веб-сайта", "Coming Soon": "Скоро будет доступно", "Login": "Войти", "Login Options": "Варианты входа", "Log in using xCloud email account or the first admin account.": "Войдите, используя учетную запись электронной почты xCloud или первую учетную запись администратора.", "Use a different email and, if your login URL is custom, enter it here to log in.": "Используйте другой адрес электронной почты и, если у вас есть пользовательский URL для входа, введите его здесь для авторизации.", "Custom": "Пользовательский", "Something went wrong, please try again.": "Произошла ошибка, попробуйте ещё раз.", "Use a different email or username to log in.": "Используйте другой адрес электронной почты или имя пользователя для входа.", "Enter your email": "Введите адрес электронной почты", "Haven’t registered for your free account yet?": "Ещё не зарегистрировались для бесплатного аккаунта?", "Sign up now": "Зарегистрируйтесь сейчас", "Glad To Have You Back!": "Рады видеть вас снова!", "Get Started For Free": "Начать бесплатно", "Access 1 server and 10 sites at $0 cost. No credit card required.": "Доступ к 1 серверу и 10 сайтам бесплатно. Кредитная карта не требуется.", "Enter your name": "Введите ваше имя", "Please choose a strong password. Use at least 8 characters, including a mix of letters, numbers and symbols.": "Пожалуйста, выберите надежный пароль. Используйте не менее 8 символов, включая буквы, цифры и символы.", "Retype Password": "Повторите пароль", "The passwords you entered do not match. Please ensure both fields contain the same password.": "Введённые пароли не совпадают. Пожалуйста, убедитесь, что оба поля содержат одинаковый пароль.", "By creating an account, you agree to our": "Создавая учетную запись, вы соглашаетесь с нашими", "Create Your Account": "Создать аккаунт", "Enter your credentials to sign up.": "Введите свои данные для регистрации.", "We have discovered a security vulnerability in the": "Мы обнаружили уязвимость безопасности в", "that you are using on the following website:": "используемый вами на следующем веб-сайте:", "View Vulnerabilities": "Просмотр уязвимостей", "Immediate Action Required: We found a security vulnerability in your Website": "Требуется немедленное действие: обнаружена уязвимость безопасности на вашем сайте", "Community": "Сообщество", "Want To Create A Staging Site For": "Создать тестовый сайт для", "A staging site is a secure clone of your live website for testing and development. It lets you experiment with plugins, themes, and changes risk-free before deploying them to live.": "Тестовый сайт — это безопасная копия вашего рабочего веб-сайта для тестирования и разработки. Он позволяет экспериментировать с плагинами, темами и изменениями без риска перед их развертыванием на рабочем сайте.", "Test Domain": "Тестовый домен", "Create a demo site with our test domains and customize it before going live.": "Создайте демонстрационный сайт с нашими тестовыми доменами и настройте его перед запуском.", "Custom Domain": "Пользовательский домен", "Enter your custom domain by simply pointing your domain to the server.": "Введите свой пользовательский домен, просто направив его на сервер.", "Deploy Staging Site": "Развернуть тестовый сайт", "Manage Staging": "Управление промежуточной версией", "Integrity Monitor": "Мониторинг Целостности", "Scan Now": "Сканировать сейчас", "Last scan": "Последнее сканирование", "Items found": "Найденные элементы", "Message": "Сообщение", "Plugin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "This website has found some checksum errors. This could be due to a corrupted file or a malicious attack. Please review the files and database of your site.": "На этом сайте обнаружены ошибки контрольной суммы. Это может быть вызвано поврежденным файлом или злонамеренной атакой. Пожалуйста, проверьте файлы и базу данных вашего сайта.", "Check": "Проверить", "Security Settings": "Настройки безопасности", "AI Bot Blocker": "Блокировщик ИИ-ботов", "Disable Nginx Config Regeneration": "Отключить регенерацию конфигураци<PERSON>inx", "Disable OpenLiteSpeed Config Regeneration": "Отключить регенерацию конфигурации OpenLiteSpeed", "WP Fail2Ban": "WP Fail2Ban", "Block Failed Login Attempts": "Блокировать неудачные попытки входа", "Block Common Usernames": "Блокировать Общие Имена Пользователей", "Block User Enumeration": "Блокировать перечисление пользователей", "Protect Comments": "Защитить комментарии", "Block Spam": "Блокировать спам", "Guard Password Resets": "Защитить сброс пароля", "Guard Pingbacks": "Защита Пингбеков", "Enable OpenLiteSpeed Config Regeneration": "Включить регенерацию конфигурации OpenLiteSpeed", "Config file regeneration has been updated successfully": "Файл конфигурации успешно обновлён", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config": "Это предотвратит автоматическое обновление конфигурации OpenLiteSpeed в xCloud при изменениях на сайте. Вам потребуется вручную обновлять конфигурацию OpenLiteSpeed.", "This will allow xCloud to regenerate OpenLiteSpeed config on any changes made to the site. You will not have to manually regenerate the OpenLiteSpeed config.": "Это позволит xCloud автоматически обновлять конфигурацию OpenLiteSpeed при любых изменениях на сайте. Вам не нужно будет вручную обновлять конфигурацию OpenLiteSpeed.", "Serve robots.txt from file system": "Обслуживать robots.txt из файловой системы", "Failed to load nginx options": "Не удалось загрузить параметры nginx", "Failed to update config file regeneration": "Не удалось обновить восстановление конфигурационного файла", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config.": "Это предотвратит автоматическое обновление конфигурации OpenLiteSpeed в xCloud при изменениях на сайте. Вам потребуется вручную обновить конфигурацию OpenLiteSpeed.", "This will prevent xCloud from regenerating nginx config on any changes made to the site. You will have to manually regenerate the nginx config.": "Это предотвратит автоматическое обновление конфигурации nginx в xCloud при изменениях на сайте. Вам придется вручную обновлять конфигурацию nginx.", "This will allow xCloud to regenerate nginx config on any changes made to the site. You will not have to manually regenerate the nginx config.": "Это позволит xCloud автоматически обновлять конфигурацию nginx при любых изменениях на сайте. Вам не нужно будет вручную обновлять конфигурацию nginx.", "Add this URL as a webhook in your Git repository settings to enable automated deployments": "Добавьте этот URL в качестве вебхука в настройках вашего Git-репозитория для включения автоматических развертываний.", "Laravel Debug Log": "<PERSON><PERSON> журнал отлад<PERSON>и", "Environment": "Окружение", "Update Environment": "Обновить среду", "Deploy Now": "Развернуть сейчас", "Deploying...": "Развертывание...", "Your Laravel site deployment has been initiated. Please wait while the changes are being deployed.": "Развертывание вашего сайта на Laravel начато. Пожалуйста, подождите, пока изменения применяются.", "Failed to deploy Laravel site. Please try again.": "Не удалось развернуть сайт <PERSON>. Пожалуйста, попробуйте снова.", "Add Supervisor Process": "Добавить процесс супервизора", "Edit Supervisor Process": "Редактировать процесс супервайзера", "Update Supervisor Process": "Обновить процесс супервизора", "Supervisor Processes": "Процессы Супервизора", "Check Process Status": "Проверить статус процесса", "Supervisor": "Супервайзер", "Process Management": "Управление процессами", "Background Processes": "Фоновые процессы", "Daemon": "Демон", "Supervisor Process Output": "Вывод процесса супервизора", "Processes": "Процессы", "View Logs": "Просмот<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Restart": "Перезапуск", "No supervisor processes found": "Процессы супервизора не найдены", "Delete Supervisor Process": "Удалить процесс супервайзера", "Are you sure you want to delete this supervisor process? This action cannot be undone.": "Вы уверены, что хотите удалить этот процесс супервизора? Это действие нельзя отменить.", "This will remove the supervisor process from the server.": "Это удалит процесс супервизора с сервера.", "Running": "Запуск", "Stopped": "Остановлено", "Fatal": "Критическая ошибка", "Backoff": "Откат", "Starting": "Запуск", "Stopping": "Остановка", "Exited": "<PERSON>ы<PERSON><PERSON>л", "Unknown": "Неизвестно", "Processing": "Обработка", "You can run any executable command here. For Python, Node.js, or PHP scripts, specify the full command (e.g., python3 script.py).": "Вы можете выполнить любую исполняемую команду здесь. Для скриптов на Python, Node.js или PHP укажите полную команду (например, python3 script.py).", "Optional. Specify the working directory for the process.": "Необязательно. Укажите рабочий каталог для процесса.", "By default, you can use root. To run as a different user, enter the username here.": "По умолчанию вы можете использовать root. Чтобы запустить от имени другого пользователя, введите здесь имя пользователя.", "Number of Processes": "Количество процессов", "Number of process instances to keep running.": "Количество экземпляров процессов для поддержания работы.", "Start Seconds (Optional)": "Начальные секунды (необязательно)", "Supervisor will consider the process started after this many seconds.": "Супервайзер начнёт процесс через указанное количество секунд.", "Stop Seconds (Optional)": "Остановить секунды (необязательно)", "Supervisor will wait this many seconds before force stopping the process.": "Супервизор будет ждать столько секунд перед принудительной остановкой процесса.", "Stop Signal (Optional)": "Сигнал остановки (необязательно)", "Supervisor sends this signal to stop the process. Leave empty for default (TERM).": "Супервизор отправляет этот сигнал для остановки процесса. Оставьте пустым для значения по умолчанию (TERM).", "Laravel Horizon is not installed": "Laravel Horizon не установлен", "Horizon is not detected in your composer.json. To use Horizon, you need to install it first:": "Horizon не обнаружен в вашем composer.json. Чтобы использовать Horizon, сначала установите его:", "Laravel Application": "Прилож<PERSON><PERSON><PERSON><PERSON>", "Enable Debug Mode": "Включить режим отладки", "When debug mode is enabled, detailed error messages will be displayed. This should be disabled in production.": "При включенном режиме отладки будут отображаться подробные сообщения об ошибках. В рабочей среде это следует отключить.", "Enable Maintenance Mode": "Включить режим обслуживания", "When maintenance mode is enabled, a maintenance screen will be displayed for all requests to your application.": "Когда режим обслуживания включен, для всех запросов к вашему приложению будет отображаться экран обслуживания.", "Application Environment": "Среда приложения", "Application": "Приложение", "The application environment affects various Laravel behaviors. This setting updates APP_ENV in your .env file": "Среда приложения влияет на различные поведения Laravel. Этот параметр обновляет APP_ENV в вашем файле .env.", "Clear Application Cache": "Очистить кэш приложения", "Clears all Laravel caches by running the optimize:clear command.": "Очищает все кэш<PERSON>, выполняя команду optimize:clear.", "Clearing...": "Очистка...", "Clear Cache": "Очистить кэш", "Laravel Horizon": "<PERSON>vel <PERSON>", "Update Process": "Процесс обновления", "Start Horizon": "Запустить Horizon", "Not Configured": "Не настроено", "Horizon is running": "Горизонт запущен", "Horizon is not running": "Горизонт не запущен", "Stop": "Стоп", "Horizon provides a beautiful dashboard and code-driven configuration for your Laravel powered Redis queues.": "Horizon предлагает красивую панель управления и конфигурацию на основе кода для ваших очередей Redis в Laravel.", "Laravel Scheduler": "<PERSON>ла<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Start Scheduler": "Запустить планировщик", "Your scheduler is running properly": "Ваш планировщик работает исправно", "Scheduler needs to be configured": "Требуется настройка планировщика", "Scheduler Frequency": "Частота планировщика", "The Laravel scheduler allows you to fluently and expressively define your command schedule within Laravel itself.": "Планировщик Laravel позволяет легко и наглядно определять расписание команд прямо в Laravel.", "Local": "Локальный", "Testing": "Тестирование", "Every Minute": "Каждую минуту", "Every Five Minutes": "Каждые пять минут", "Every Ten Minutes": "Каждые 10 минут", "Every Fifteen Minutes": "Каждые 15 минут", "Every Thirty Minutes": "Каждые 30 минут", "Hourly": "Почасовой", "Server is not connected": "Сервер не подключен", "Failed to load Laravel application status": "Не удалось загрузить статус приложения Laravel", "Laravel application settings updated successfully": "Настройки приложения Laravel успешно обновлены", "Failed to update Laravel application settings": "Не удалось обновить настройки приложения Laravel", "Application cache cleared successfully": "Кэш приложения успешно очищен", "Failed to clear application cache": "Не удалось очистить кэш приложения", "Horizon started successfully": "Горизонт успешно запущен", "Failed to start Horizon": "Не удалось запустить Horizon", "Horizon stopped successfully": "Горизонт успешно остановлен", "Failed to stop Horizon": "Не удалось остановить Horizon", "Horizon restarted successfully": "Горизонт успешно перезапущен", "Failed to restart Horizon": "Не удалось перезапустить Horizon", "Scheduler setup successfully": "Настройка планировщика выполнена успешно", "Failed to setup scheduler": "Не удалось настроить планировщик", "Scheduler stopped successfully": "Планировщик успешно остановлен", "Failed to stop scheduler": "Не удалось остановить планировщик", "Laravel Queue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Queue Worker": "Добавить Рабочий Очереди", "No queue workers are running": "Нет активных рабочих очереди", "Connection": "Подключение", "Queue": "Очередь", "Timeout": "Тайм-аут", "Memory": "Память", "Refresh Status": "Обновить статус", "Edit Process": "Редактировать процесс", "Restart Process": "Перезапустить процесс", "View Output": "Просмотр результата", "Queue workers process jobs in the background. They are useful for handling time-consuming tasks like sending emails, processing files, or any other task that should not block the main application.": "Очередные работники обрабатывают задания в фоновом режиме. Они полезны для выполнения трудоемких задач, таких как отправка электронных писем, обработка файлов или любых других задач, которые не должны блокировать основное приложение.", "Update Queue Worker": "Обновить очередь обработчика", "redis": "Редис", "default": "По умолчанию", "0": "0", "Maximum Seconds Per Job": "Максимальное количество секунд на задание", "0 = No Timeout": "0 = Без тайм-аута", "256": "256", "Maximum Memory (Optional)": "Максимальная память (необязательно)", "Memory limit in MB": "Ограничение памяти в МБ", "1": "1", "Number of worker processes to run": "Количество рабочих процессов для запуска", "Queue Worker Output": "Вывод Рабочего Очереди", "Queue worker updated successfully": "Очередь обработчика успешно обновлена", "Queue worker added successfully": "Очередь обработчика успешно добавлена", "Are you sure you want to stop this queue worker?": "Вы уверены, что хотите остановить этого работника очереди?", "This will remove the queue worker process.": "Это удалит процесс обработки очереди.", "Yes, Stop": "Да, остановить", "Queue worker stopped successfully": "Очередь успешно остановлена", "Failed to stop queue worker": "Не удалось остановить обработчик очереди", "Queue worker restarted successfully": "Очередь обработчика успешно перезапущена", "Failed to restart queue worker": "Не удалось перезапустить обработчик очереди", "Error loading output. Please try again.": "Ошибка загрузки результата. Пожалуйста, попробуйте снова.", "Failed to refresh worker status": "Не удалось обновить статус работника", "Deployment has been initiated.": "Развертывание начато.", "Plugins Found": "Найдены плагины", "Please configure Cloudflare SSL on this site to enable edge cache.": "Пожалуйста, настройте SSL от Cloudflare на этом сайте для включения кэширования на границе.", "Cloudflare Edge Cache is not available for staging sites. Please switch to production with Cloudflare SSL for this site to enable edge cache.": "Кэширование на краю Cloudflare недоступно для тестовых сайтов. Пожалуйста, переключитесь на рабочую среду с Cloudflare SSL для этого сайта, чтобы включить кэширование на краю.", "You need to set up Cloudflare integration first to enable edge cache.": "Сначала настройте интеграцию с <PERSON>flare, чтобы включить кэширование на границе.", "Domain is not available on Cloudflare. Please add your domain to Cloudflare first to enable edge cache.": "Домен недоступен в Cloudflare. Пожалуйста, сначала добавьте ваш домен в Cloudflare, чтобы включить кэширование на границе.", "Cloudflare Edge Cache has been enabled successfully.": "Кэширование на краю сети Cloudflare успешно включено.", "Cloudflare Edge Cache has been disabled successfully.": "Кэ<PERSON> <PERSON> от Cloudflare успешно отключен.", "Failed to update Cloudflare Edge Cache settings.": "Не удалось обновить настройки кэша Cloudflare Edge.", "Cloudflare Edge Cache purged successfully.": "Очистка кэша на Edge-серверах Cloudflare выполнена успешно.", "Failed to purge Cloudflare Edge Cache.": "Не удалось очистить кэш Cloudflare Edge.", "Cloudflare Edge Cache": "Кэш Edge от Cloudflare", "Boost your website's performance with Cloudflare Edge Cache by caching content at Cloudflare's global edge network. This reduces server load, enhances speed, and improves user experience worldwide.": "Улучшите производительность вашего сайта с помощью Cloudflare Edge Cache, кэшируя контент в глобальной сети Cloudflare. Это снижает нагрузку на сервер, увеличивает скорость и улучшает пользовательский опыт по всему миру.", "Clear Edge Cache": "Очистить кэш Edge", "This will purge all cached content from Cloudflare's edge network.": "Это действие удалит весь кэшированный контент из сети Cloudflare.", "Failed to update Cloudflare Edge Cache settings": "Не удалось обновить настройки кэша Cloudflare Edge", "Are you sure you want to disable Cloudflare Edge Cache?": "Вы уверены, что хотите отключить кэширование на Edge-серверах Cloudflare?", "Failed to disable Cloudflare Edge Cache": "Не удалось отключить Cloudflare Edge Cache", "Cloudflare Edge Cache purged successfully": "Очистка кэша Cloudflare Edge выполнена успешно", "A self-hosted monitoring tool like \"Uptime Robot\"": "Инструмент мониторинга с самостоятельным размещением, такой как \"Uptime Robot\"", "A web interface for managing MySQL databases": "Веб-интерфейс для управления базами данных MySQL", "Admin User": "Администратор", "Default Node.js Version": "Версия Node.js по умолчанию", "Default Server Node.js Version": "Версия Node.js по умолчанию для сервера", "Disable phpMyAdmin": "Отключить phpMyAdmin", "Disabling...": "Отключение...", "Enable phpMyAdmin": "Включить phpMyAdmin", "Enabling...": "Включение...", "Install One Click App": "Установить приложение в один клик", "Install One Click App Into": "Установить приложение в один клик в", "Mautic": "Ма́<PERSON><PERSON><PERSON><PERSON>", "n8n": "n8n", "One Click App": "Приложение в один клик", "One Click Apps": "Приложения в один клик", "Open phpMyAdmin": "Открыть phpMyAdmin", "Open-source marketing automation platform": "Платформа автоматизации маркетинга с открытым исходным кодом", "PHPMyAdmin": "PHPMyAdmin", "Please make sure to copy your admin password. You will not be able to see it again after installation.": "Пожалуйста, скопируйте ваш пароль администратора. После установки вы не сможете его увидеть.", "Provisioning...": "Настройка...", "Repair Node": "Узел ремонта", "Repair PM2 Process & ecosystem.config.js": "Восстановить процесс PM2 и файл ecosystem.config.js", "Restart PM2 Process": "Перезапустить процесс PM2", "Select": "Выбрать", "Select an Application": "Выберите приложение", "Selected": "Выбрано", "Site Details": "Детали сайта", "This will set the default Node.js version for the server. Node.js is managed using nvm (Node Version Manager).": "Это установит версию Node.js по умолчанию для сервера. Node.js управляется с помощью nvm (Node Version Manager).", "Uptime Kuma": "Ап<PERSON><PERSON><PERSON><PERSON> Кума", "Workflow automation tool with a visual editor": "Инструмент автоматизации рабочих процессов с визуальным редактором", "phpMyAdmin": "phpMyAdmin", "phpMyAdmin is a free software tool written in PHP, intended to handle the administration of MySQL over the Web.": "phpMyAdmin — это бесплатный программный инструмент, написанный на PHP, предназначенный для управления MySQL через веб-интерфейс.", "phpMyAdmin is being provisioned. Please wait until the provisioning process completes.": "phpMyAdmin подготавливается. Пожалуйста, подождите, пока процесс подготовки завершится.", "phpMyAdmin is enabled but not yet fully provisioned. Please wait until it becomes available.": "php<PERSON><PERSON><PERSON><PERSON><PERSON> включен, но еще не полностью настроен. Пожалуйста, подождите, пока он станет доступен.", "phpMyAdmin is enabled on this server. Click the button above to open it in a new tab.": "phpMyAdmin включен на этом сервере. Нажмите кнопку выше, чтобы открыть его в новой вкладке.", "phpmyadmin_description": "Веб-интерфейс для управления базами данных MySQL", "n8n_description": "Инструмент автоматизации рабочих процессов с визуальным редактором", "uptime_kuma_description": "Инструмент мониторинга с самостоятельным размещением, такой как \"Uptime Robot\"", "mautic_description": "Платформа автоматизации маркетинга с открытым исходным кодом", "Patchstack Subscriptions": "Подписки Patchstack", "List of Patchstack Subscriptions": "Список подписок Patchstack", "Find all the patchstack subscriptions associated with your account here.": "Найдите все подписки Patchstack, связанные с вашей учетной записью, здесь.", "Vulnerability Shield Pro is active!": "Щит уязвимостей Pro активен!", "Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.": "Vulnerability Shield Pro теперь будет отправлять более быстрые уведомления, чтобы вы всегда были на шаг впереди угроз.", "WordPress Core.": "Ядро WordPress", "Get faster security alerts with Vulnerability Shield Pro powered by": "Получайте более быстрые уведомления о безопасности с помощью Vulnerability Shield Pro на базе", "Patchstack - only $4/month.": "Patchstack - всего $4/месяц.", "Upgrade to PRO": "Обновить до PRO", "Canceling...": "Отмена...", "Vulnerability Shield Pro": "Защита от уязвимостей Pro", "Powered by Patchstack": "Работает на базе Patchstack", "Stay protected with continuous vulnerability scans and advanced threat alerts to secure your WordPress website like a pro.": "Оставайтесь защищенными с помощью непрерывного сканирования уязвимостей и расширенных оповещений об угрозах, чтобы профессионально обезопасить ваш сайт на WordPress.", "mo": "\"мо\"", "Upgrade to Pro NOW": "Обновитесь до Pro СЕЙЧАС", "Remove Subscription": "Отменить подписку", "Removing...": "Удаление...", "Are you sure you want to remove the subscription for this site?": "Вы уверены, что хотите отменить подписку на этот сайт?", "Once confirm, this cannot be undone.": "После подтверждения это действие нельзя будет отменить.", "Patchstack - only": "Patchstack - только", "/month.": "/мес.", "Subscription Status": "Статус подписки", "Export Sites Data": "Экспорт данных сайтов", "Export Servers Data": "Экспорт данных серверов", "Export": "Экспортировать", "Export site data in your preferred format by selecting the desired export type, format, and data columns.": "Экспортируйте данные сайта в предпочитаемом формате, выбрав тип экспорта, формат и столбцы данных.", "Export server data in your preferred format by selecting the desired export type, format, and data columns.": "Экспортируйте данные сервера в предпочитаемом формате, выбрав тип экспорта, формат и столбцы данных.", "Select Export Type": "Выберите тип экспорта", "Choose what to export": "Выберите данные для экспорта", "Excel (XLSX)": "Excel (XLSX)", "CSV": "CSV", "Select Columns": "Выбрать столбцы", "Exporting...": "Экспорт...", "Export Format": "Формат экспорта", "Issues": "Проблемы", "No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.": "Уязвимостей не обнаружено в Vulnerability Shield Pro. Вы получите уведомление, если будут найдены проблемы.", "Excellent! Your WordPress Core is fully secure!": "Отлично! Ваш WordPress Core полностью защищён!", "Excellent! Your plugins are fully secure!": "Отлично! Ваши плагины полностью защищены!", "Excellent! Your theme is fully secure!": "Отлично! Ваша тема полностью защищена!", "Issue": "Проблема", "No issues were detected by Vulnerability Scanner. You’ll be notified immediately if any issues are found.": "Уязвимостей не обнаружено. Вы получите уведомление сразу, как только будут найдены проблемы.", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $5/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "Уязвимостей не обнаружено. Вы получите уведомление, если будут найдены проблемы. Обновитесь до версии Pro всего за $5/мес для расширенного обнаружения уязвимостей и автоматического исправления с помощью Patchstack.", "Go PRO for": "Перейдите на PRO, чтобы получить", "Upgrade to Vulnerability Shield Pro for automated vulnerability detection & virtual patching powered by Patchstack": "Обновитесь до Vulnerability Shield Pro для автоматического обнаружения уязвимостей и виртуального патчинга с поддержкой Patchstack.", "Output": "Вывод", "Shield Enabled": "Щит включен", "Enable Shield": "Включить защиту", "Vulnerability Shield": "Экран уязвимостей", "Pro": "Профи", "Enable Vulnerability Shield Pro to secure this site with automated virtual patching and faster notification alerts.": "Включите Vulnerability Shield Pro, чтобы защитить этот сайт с помощью автоматического виртуального патчинга и более быстрых уведомлений.", "Pay now for": "Оплатить сейчас для", "Continue free plan": "Продолжить бесплатный план", "I am not interested at this moment. Please, do not show this message again.": "Сейчас мне это не интересно. Пожалуйста, больше не показывайте это сообщение.", "The plugin has a vulnerability that makes it possible for unauthorized actions.": "Плагин имеет уязвимость, позволяющую выполнять несанкционированные действия.", "This vulnerability affects": "Эта уязвимость затрагивает", "We have discovered security vulnerabilities in the": "Мы обнаружили уязвимости в безопасности в", "These": "Эти", "have vulnerabilities that make it possible for unauthorized actions.": "имеют уязвимости, позволяющие несанкционированные действия.", "The plugins have vulnerabilities that make it possible for unauthorized actions.": "Плагины имеют уязвимости, которые позволяют несанкционированные действия.", "We have discovered security vulnerabilities in": "Мы обнаружили уязвимости в безопасности в", "and a few more plugins that you are using on the following website:": "и несколько других плагинов, которые вы используете на следующем веб-сайте:", "We detected this vulnerability on": "Мы обнаружили эту уязвимость на", "UTC. You can ignore this message if you have already taken care of it.": "UTC. Вы можете проигнорировать это сообщение, если уже позаботились об этом.", "Thank you for being a": "Спасибо, что вы с нами", "user!": "Пользователь!", "If you have any questions, don't hesitate to contact our": "Если у вас есть вопросы, свяжитесь с нашей", "support team": "Служба поддержки", "Thank you for being a xCloud user!": "Спасибо, что пользуетесь xCloud!", "We found a security vulnerability in your Website": "Мы обнаружили уязвимость безопасности на вашем сайте", "Scanning Now": "Сканирование...", "Last Scanned": "Последнее сканирование", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $": "Уязвимостей не обнаружено. Вы получите уведомление, если будут найдены проблемы. Обновитесь до версии Pro всего за $", "/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "/mo для расширенного обнаружения уязвимостей и автоматического исправления с поддержкой Patchstack."}