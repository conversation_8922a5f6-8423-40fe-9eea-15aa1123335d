{"xCloud": "云端", "xcloud": "云端", "Log In To": "登录到", "Email": "电子邮件", "Password": "密码", "forgot_password": "忘记密码", "Remember me": "记住我", "Log In": "登录", "Not Registered Yet? Please": "尚未注册？请", "Sign Up": "注册", "Email Address..": "电子邮箱地址", "Sign Up For": "注册", "Enter necessary informations for creating a new account": "输入创建新账户所需的信息。", "Name": "名称", "Use 8 or more characters with a mix of letters, numbers & symbols": "使用8个或以上字符，包含字母、数字和符号。", "Password Confirmation": "确认密码", "I agree to the": "我同意", "Terms": "条款", "and": "和", "Privacy Policy": "隐私政策", "Already have an account? Please": "已经有账户？请", "Forgot Password": "忘记密码", "Please enter your email address to search for your account": "请输入您的电子邮件地址以搜索您的账户", "Reset Password": "重置密码", "This is a secure area of the application. Please confirm your password before continuing.": "这是应用程序的安全区域。请在继续之前确认您的密码。", "Confirm": "确认", "Confirm Password": "确认密码", "Two-factor Confirmation": "双重验证", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "请通过输入您的身份验证应用程序提供的验证码来确认访问您的账户。", "Please confirm access to your account by entering one of your emergency recovery codes.": "请输入您的紧急恢复代码之一以确认访问您的账户。", "Code": "代码", "Recovery Code": "恢复代码", "Use a recovery code": "使用恢复代码", "Use an authentication code": "使用验证码", "Verify Email Address": "验证电子邮件地址", "To continue using": "继续使用", "please click on the link in the verification email sent to your email.": "请点击发送到您邮箱的验证邮件中的链接。", "A new verification link has been sent to the email address you provided during registration.": "我们已向您在注册时提供的电子邮箱发送了一条新的验证链接。", "Please wait": "请稍候", "seconds to resend email.": "秒后可重新发送邮件。", "Resend Verification Email": "重新发送验证邮件", "Logout": "登出", "Overview": "概览", "New Server": "新服务器", "New Site": "新站点", "Active Servers": "活动服务器", "Total Sites": "站点总数", "No Active Billing": "无活动账单", "Plan": "计划", "Vulnerable Sites": "易受攻击站点", "New Team Invitations": "新团队邀请", "Server Setup In Progress": "服务器设置中", "Site Setup In Progress": "站点设置进行中", "View All": "查看全部", "You can create 1 server and 10 sites with our Free Plan.": "您可以使用我们的免费计划创建1个服务器和10个站点。", "Two-Factor Authentication": "双重验证", "You have not enabled two-factor authentication, which is highly recommended for your account security.": "您尚未启用双重身份验证，强烈建议启用以确保账户安全。", "Skip": "跳过", "Setup Now": "立即设置", "Please check your": "请检查您的", "invoices": "发票", "has asked you to join as a": "邀请您加入为", "to this team": "给此团队", "Decline": "拒绝", "Accept": "接受", "To decline the invitation please visit": "要拒绝邀请，请访问", "team page": "团队页面", "Server Disk Space Low": "服务器磁盘空间不足", "Warning": "警告", "Your Server": "您的服务器", "disk space is low. Please upgrade your plan to avoid any downtime.": "磁盘空间不足。请升级您的计划以避免停机。", "Reboot Required": "需要重启", "Security Update": "安全更新", "will automatically update and reboot on": "将自动更新并在以下时间重启", "You can also reboot now.": "您也可以立即重启。", "Security Update - Server": "安全更新 - 服务器", "requires a reboot.": "需要重启。", "Reboot Now": "立即重启", "Security updates were automatically installed on your server, but a reboot is required to complete the installation. The following packages were upgraded:": "您的服务器已自动安装安全更新，但需要重启以完成安装。以下软件包已升级：", "Be sure to test the sites on your server after rebooting.": "请确保重启后在您的服务器上测试这些站点。", "Server Size": "服务器大小", "PHP": "PHP", "View Details": "查看详情", "Server": "服务器", "Continue Setup": "继续设置", "View Site": "查看网站", "Open options": "打开选项", "Bill Calculator": "账单计算器", "Billing Amount": "账单金额", "Select Renewal Period": "选择续订周期", "Bill From": "账单来自", "Bill Before": "账单预览", "Bill To": "账单抬头", "Processing payment... Please do not cancel or refresh the page.": "正在处理付款... 请勿取消或刷新页面。", "My Blueprints": "我的蓝图", "Create New Blueprint": "创建新蓝图", "Created": "已创建", "Active Plugins": "活动插件", "Active Theme": "活动主题", "Edit Blueprint": "编辑蓝图", "Create Blueprint": "创建蓝图", "By": "通过", "Active Installations": "活动安装数", "Update": "更新", "Create": "创建", "Staging Url": "暂存网址", "Confirm Details": "确认详情", "Edit": "编辑", "Old Domain": "旧域名", "HTTPS": "HTTPS", "Enabled": "启用", "Disabled": "禁用", "PHP Version": "PHP 版本", "Database Type": "数据库类型", "Database Name": "数据库名称", "Database User": "数据库用户", "No Database": "无数据库", "Site User": "网站用户", "Configuration & Database Connection": "配置与数据库连接", "Create Database In Server": "在服务器中创建数据库", "Select Destination Server To Clone Your Site": "选择目标服务器以克隆您的网站", "Choose Server": "选择服务器", "DNS & SSL For": "DNS 和 SSL 用于", "Demo Site": "演示站点", "Create a demo site with our test domain and customize before going live.": "使用我们的测试域名创建演示站点，并在上线前进行自定义。", "Migrate into a New Domain": "迁移到新域", "Get your site up and running for the world to see by simply pointing your domain to the server.": "只需将您的域名指向服务器，即可让您的网站上线，展示给全世界。", "Add DNS and SSL Certificate on Cloudflare": "在 Cloudflare 上添加 DNS 和 SSL 证书", "Integrate Cloudflare for Automatic DNS and SSL management.": "集成 Cloudflare 以实现自动 DNS 和 SSL 管理。", "DNS Setup": "DNS 设置", "Settings & Configurations": "设置与配置", "Deploy Script (Optional)": "部署脚本（可选）", "wp plugin install woocommerce": "安装 WooCommerce 插件", "Verifying": "验证中", "Uploading....": "上传中……", "Upload/Drop Your Zipped sql or only .sql File": "上传/拖放您的压缩 sql 文件或仅 .sql 文件", "Maximum File Size: 500 MB": "最大文件大小：500 MB", "Use These Credentials To Transfer Database": "使用这些凭据传输数据库", "Server Connection Url": "服务器连接 URL", "Server Address": "服务器地址", "Server SSH Host": "服务器 SSH 主机", "Server Username": "服务器用户名", "Server database name": "服务器数据库名称", "Database Password": "数据库密码", "Verify Upload": "验证上传", "Upload/Drop Your Zipped/Tar File": "上传/拖放您的压缩/Tar 文件", "Use These Credentials To Upload Your WordPress Website": "使用这些凭据上传您的WordPress网站", "22": "22", "Port": "端口", "Remote Path": "远程路径", "Upload Migration Files": "上传迁移文件", "Choose a method from below to install the": "选择以下方法进行安装", "Migration Plugin": "迁移插件", "Optional": "可选", "Authentication Token": "身份验证令牌", "Plugin Page URL": "插件页面网址", "I have added the authentication token to my site": "我已将认证令牌添加到我的网站", "Please agree that you have added the authentication token to your site": "请确认您已将身份验证令牌添加到您的网站", "Download Plugin ZIP File": "下载插件 ZIP 文件", "Plugin Link": "插件链接", "WP CLI": "WP CLI", "Show Previous cPanel Migrations": "显示以前的 cPanel 迁移", "Username of the cPanel account:": "cPanel账户的用户名：", "Enter your cPanel username": "输入您的cPanel用户名", "cPanel Api Key:": "cPanel API 密钥：", "Enter your cPanel API Key": "输入您的 cPanel API 密钥", "cPanel Host:": "cPanel 主机：", "Fetch Existing Backups": "获取现有备份", "Select a backup to import from the list below": "从下面的列表中选择要导入的备份", "Backup in Progress ...": "正在备份...", "No Backups Found": "未找到备份", "You need to create a backup in your cPanel account first": "您需要先在您的 cPanel 账户中创建备份", "Generate Backup": "生成备份", "Migration File": "迁移文件", "Created At": "创建时间", "Actions": "操作", "Continue Migration": "继续迁移", "No cPanel Migrations Found": "未找到 cPanel 迁移", "You can create a new cPanel Migration from the cPanel Migration tab": "您可以在 cPanel 迁移选项卡中创建新的 cPanel 迁移", "Upload Completed": "上传完成", "Upload/Drop Your Zipped File": "上传/拖放您的压缩文件", "Regenerate cPanel User": "重新生成 cPanel 用户", "I have started the backup": "我已开始备份", "Waiting for the backup to complete": "等待备份完成", "Currently transferring this file from generated backup for cpanel migration": "正在从生成的备份中传输此文件以进行 cPanel 迁移", "New cPanel Migration using SCP found, click here to continue": "发现新的 cPanel 迁移使用 SCP，点击此处继续", "Live Site": "在线网站", "Domain Setup": "域设置", "Domain Name": "域名", "Auto DNS management by Cloudflare is disabled for Full Server Migration.": "由于进行完整服务器迁移，Cloudflare 的自动 DNS 管理已禁用。", "Coming soon...": "即将推出...", "Your DNS setup and SSL Certificate will be done by Cloudflare and managed by xCloud.": "您的 DNS 设置和 SSL 证书将由 Cloudflare 完成，并由 xCloud 管理。", "Confirm Migration Details": "确认迁移详情", "Source site": "来源网站", "Destination site": "目标站点", "Select Websites to Migrate": "选择要迁移的网站", "to": "至", "Previously migrated sites from": "先前迁移的网站来自", "Available Sites": "可用站点", "Select All": "全选", "Fetch Websites": "获取网站", "Fetching...": "正在获取...", "Wordpress Sites To Migrate": "要迁移的WordPress站点", "Type": "输入", "Directory": "目录", "Non-Wordpress Sites": "非WordPress网站", "Fetching sites...": "正在获取站点...", "Add Websites to Migrate": "添加要迁移的网站", "Add the domains you want to migrate from your Cpanel backup, we will search these domains and migrate to the new server": "添加您想从Cpanel备份中迁移的域名，我们将搜索这些域名并迁移到新服务器。", "Add Website": "添加网站", "Connect Your Source Server": "连接您的源服务器", "Submit": "提交", "Select your Hosting Provider": "选择您的托管服务提供商", "cPanel Migration Method": "cPanel 迁移方法", "Set domain for migration": "设置迁移域", "Full Server Migration In Progress from": "服务器迁移进行中", "Migration completed successfully from": "迁移成功完成，来自", "Successfully Migrated Sites": "站点迁移成功", "Migration In Progress": "迁移进行中", "Migration In Queue": "迁移排队中", "Migration Failed": "迁移失败", "IP Address": "IP地址", "SSH Port": "SSH端口", "SSH Username": "SSH 用户名", "root": "根目录", "SSH Authentication": "SSH 认证", "SSH Password": "SSH 密码", "Size": "大小", "GB": "英镑", "Existing Site": "现有站点", "Full Page Cache": "全页缓存", "Redis Object Cache": "Redis 对象缓存", "Default Database In Server": "服务器中的默认数据库", "Database": "数据库", "Admin User Name": "管理员用户名", "Files and Database Migration": "文件和数据库迁移", "Select Databases & File Systems To Migrate": "选择要迁移的数据库和文件系统", "Migrate The Following Content": "迁移以下内容", "All Database Tables & Corresponding File System": "所有数据库表及对应文件系统", "Only File System But NOT Database Tables": "仅文件系统，不包括数据库表", "Select Destination Server To Migrate Your Site": "选择目标服务器以迁移您的站点", "Existing WordPress Site URL": "现有 WordPress 站点 URL", "Existing Site URL": "现有网站网址", "Git Repository": "Git 仓库", "master": "主控", "Git Branch": "Git 分支", "Enable push to deploy": "启用推送部署", "Deployment URL": "部署 URL", "npm run deploy": "npm run deploy", "Deploy Script": "部署脚本", "Run this script after every site deployment": "在每次站点部署后运行此脚本", "Select Databases To Migrate": "选择要迁移的数据库", "Add Your Existing Database": "添加现有数据库", "Without Database": "无数据库", "Install Migration Plugin": "安装迁移插件", "Buy Now": "立即购买", "Packages": "套餐", "Products": "产品", "Bills & Payment": "账单与支付", "Free plan which includes 1 server and 10 website with Self Hosting.": "免费计划，包括 1 台服务器和 10 个自托管网站。", "You’re on the": "您正在使用", "Activate your": "激活您的", "team by adding payment method today.": "立即添加付款方式以加入团队。", "team by adding payment method.": "通过添加付款方式加入团队。", "Estimated Cost": "预估费用", "This is an estimate of the amount based on your current month-to-date": "这是基于您本月截至目前的金额估算", "Cost This Month": "本月费用", "Cost Next Month": "下月费用", "Overused Amount After 28th": "28日后超出金额", "Billing Period Monthly": "计费周期：每月", "renews 29": "续订 29", "Read how billing works.": "了解账单运作方式。", "Subscriptions": "订阅", "xCloud Managed Email Provider": "xCloud 管理邮箱服务商", "Services": "服务", "Payment Methods": "付款方式", "You can add Credit/Debit Card as your payment method": "您可以添加信用卡/借记卡作为您的支付方式", "Expires at": "到期时间", "Default": "默认", "Set As Default": "设为默认", "Add A Payment Method": "添加付款方式", "Billing History": "账单历史记录", "Description": "描述", "Date": "日期", "Service Status": "服务状态", "Amount": "金额", "Payment Status": "支付状态", "Next Billing": "下次账单", "Renewal Period": "续订周期", "Link a Credit/Debit Card": "绑定信用卡/借记卡", "We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)": "我们接受Visa、Mastercard、American Express、Discover、Diners Club，以及中国银联（CUP）、日本信用卡公司（JCB）。", "Service": "服务", "Amount Payable": "应付金额", "discontinued on": "已停产", "to lifetime": "终身", "I want to convert this bill to monthly and unlock full features": "我想将此账单转换为每月并解锁全部功能", "Please add a": "请添加一个", "payment": "付款", "method unlock more features.": "解锁更多功能的方法。", "Amount Paid": "付款金额", "Whitelabel Subscriptions": "白标订阅", "Current Plan": "当前计划", "Sell Up to": "出售高达", "Servers": "服务器", "Subscription Date": "订阅日期", "Expires on": "到期日期", "Your subscription is about to expire please renew it": "您的订阅即将到期，请续订", "Your subscription is about to expire please renew it for uninterrupted service.": "您的订阅即将到期，请续订以确保服务不中断。", "Pay Now": "立即支付", "No active subscription found": "未找到有效订阅", "Claim Free with LTD": "使用LTD免费领取", "Server Limit": "服务器限制", "As an existing LTD customer, you are eligible to claim this plan for free.": "作为现有的LTD客户，您有资格免费领取此计划。", "Switch Free with LTD": "使用LTD免费切换", "Switch to this plan for": "切换到此计划以获取", "Choose a plan": "选择方案", "SMTP Username": "SMTP 用户名", "SMTP Password": "SMTP 密码", "Domain": "域名", "Sendgrid Username": "Sendgrid 用户名", "Sendgrid Api Key": "Sendgrid API 密钥", "SMTP Host": "SMTP 主机", "SMTP Port": "SMTP端口", "Label": "标签", "Get help from our": "获取我们的帮助", "Configure SMTP Provider": "配置 SMTP 提供商", "documentation": "文档", "From Email": "发件邮箱", "To Email": "发送至邮箱", "Email Subscriptions": "电子邮件订阅", "With xCloud you get 100 free emails per month in each team. You can purchase additional email more from": "使用 xCloud，每个团队每月可获得 100 封免费邮件。您可以购买更多额外邮件。", "here.": "这里", "xCloud Email Balance": "xCloud 邮件余额", "This is a summary of your xCloud Email Balance.": "这是您的 xCloud 邮件余额摘要。", "Total Emails": "总邮件数量", "Emails Available": "可用电子邮件", "Elastic Email API Key": "弹性邮件 API 密钥", "Invoice": "发票", "Details": "详情", "You will pay for following bills": "您将支付以下账单", "which are under": "在以下内容中", "Next Billing Amount": "下次账单金额", "Amount Adjusted": "调整金额", "Add New Member": "添加新成员", "Send Invitation": "发送邀请", "Server Access": "服务器访问", "Access to all server": "访问所有服务器", "Choose specific server": "选择特定服务器", "Select here...": "选择此处…", "Site Access": "站点访问", "Access to all site": "访问所有网站", "Choose specific site": "选择特定站点", "Role Permission": "角色权限", "Create New Team": "创建新团队", "Save Changes": "保存更改", "Upload Your Avatar": "上传头像", "Add a profile picture or icon for your account": "为您的账户添加个人资料图片或图标", "Upload Image": "上传图片", "Team Name": "团队名称", "Team Email": "团队邮箱", "Tags": "标签", "Select or create tags": "选择或创建标签", "Edit Team": "编辑团队", "Update Member": "更新成员", "Update Invitation": "更新邀请", "Email ID": "电子邮件ID", "User Role": "用户角色", "Delete Team": "删除团队", "Leave Team": "离开团队", "Team Members": "团队成员", "Role": "角色", "Status": "状态", "Server Count": "服务器数量", "Action": "操作", "Change Role": "更改角色", "Delete": "删除", "Resend Email": "重发邮件", "Site": "站点", "Switch to Team": "切换到团队", "Add Member": "添加成员", "Check Details": "查看详情", "All Sites": "所有网站", "Refresh": "刷新", "Archive Servers": "归档服务器", "List of Archive Servers": "存档服务器列表", "Find all the archive servers associated with your team here.": "在此查找与您的团队相关的所有归档服务器。", "Provider": "提供者", "If you proceed, this will permanently remove this service and you will not be able to access or retrieve it again.": "如果继续，此操作将永久删除该服务，您将无法再次访问或恢复。", "Authentication": "身份验证", "Please enter the two-factor authentication code sent to you below to verify:": "请输入发送给您的双重验证代码以进行验证：", "Billing Details": "账单详情", "Saved Cards": "已保存的卡片", "Add your payment methods for billing.": "添加您的付款方式以进行结算。", "Set as Default": "设为默认", "No card available": "无可用卡片", "Add Payment Method": "添加支付方式", "Bills": "账单", "Find complete, downloadable receipts of your regular payments": "查找您定期付款的完整可下载收据", "Title": "标题", "Amount To Pay": "支付金额", "Due On": "到期日", "Next Billing Date": "下次账单日期", "Paid On": "已支付", "Manage Active Browser Sessions": "管理活动浏览器会话", "Check which sessions are still active from different devices & browsers, and manage as needed.": "检查哪些会话仍在不同设备和浏览器上处于活动状态，并根据需要进行管理。", "Note: You can log out of all your active sessions; it will also log you out of this session and you will have to log back in to continue using": "注意：您可以注销所有活跃会话；这也会注销当前会话，您需要重新登录才能继续使用。", "Log Out Of All Sessions": "注销所有会话", "Browser Name": "浏览器名称", "Last Session": "上次会话", "This device": "此设备", "Last active": "上次活跃", "Cloudflare Integration": "Cloudflare 集成", "List of your Cloudflare Integrations": "您的 Cloudflare 集成列表", "Find all the cloudflare integrations associated with your account here.": "在此处查找与您的账户关联的所有 Cloudflare 集成。", "New Cloudflare Integration": "新的 Cloudflare 集成", "Account Email": "账户邮箱", "API Token": "API 令牌", "Global API Key": "全局 API 密钥", "Origin CA Key": "来源 CA 密钥", "Add New Cloudflare Integration": "添加新的Cloudflare集成", "Name of the integration": "集成名称", "Your Cloudflare Account Email": "您的 Cloudflare 帐户邮箱", "Create a new API Token from your Cloudflare account and paste it here. (With Edit zone DNS permissions)": "从您的 Cloudflare 账户创建一个新的 API 令牌，并在此粘贴。（具有编辑区域权限）", "Go to your Cloudflare Profile > API Tokens > Global API Key > View": "前往 Cloudflare 个人资料 > API 令牌 > 全局 API 密钥 > 查看", "Go to your Cloudflare Profile > API Tokens > Origin CA Key > View": "前往 Cloudflare 个人资料 > API 令牌 > 原始 CA 密钥 > 查看", "Integrate Cloudflare For DNS Management": "集成 Cloudflare 进行 DNS 管理", "Account email": "账户邮箱", "Find complete, downloadable receipts of your regular payments.": "查找您定期付款的完整可下载收据。", "All Invoices": "所有发票", "This Month": "本月", "Last Month": "上个月", "Last 6 Months": "最近6个月", "Last 1 Year": "过去一年", "Paid Invoices": "已支付发票", "Unpaid Invoices": "未支付发票", "Failed Invoices": "失败的发票", "Invoice No": "发票号", "Download": "下载", "Cancel": "取消", "Email Provider": "电子邮件提供商", "List of Email Providers": "电子邮件提供商列表", "Find all the email providers associated with your account here.": "在此查找与您的账户关联的所有电子邮件提供商。", "Add New Provider": "添加新供应商", "Username/Domain": "用户名/域", "Invitation List": "邀请名单", "Invite Users": "邀请用户", "Share your invitation code with others. You have": "与他人分享您的邀请码。您有", "invitation": "邀请", "remaining": "剩余", "Invited Users": "受邀用户", "You have invited following persons.": "您已邀请以下人员。", "Accepted": "已接受", "Notifications": "通知", "List of Notifications": "通知列表", "Integrate your notification platform. You can customize your": "集成您的通知平台。您可以自定义您的", "notification settings": "通知设置", "from": "从", "here": "这里", "Add New Notification": "添加新通知", "Disconnect": "断开连接", "Reconnect": "重新连接", "Add Notification": "添加通知", "Being with Country Code (e.g., ****** XXX XXXX for US)": "以国家代码开头（例如，美国为 ****** XXX XXXX）", "WhatsApp Phone Number": "WhatsApp 电话号码", "Server Notifications": "服务器通知", "For server reboots, unavailable servers, and available upgrades are included": "服务器重启、服务器不可用和可用升级已包含在内", "Telegram": "电报", "WhatsApp": "WhatsApp", "Slack": "<PERSON><PERSON>ck", "Newly Provisioned Servers": "新配置的服务器", "Site Notifications": "网站通知", "For site upgrades, SSL certificate issues, and deployment errors": "网站升级、SSL证书问题和部署错误", "Other Notifications": "其他通知", "Get notified about team accounts and actions": "接收团队账户和操作通知", "Do Not Send Sensitive Information": "请勿发送敏感信息", "This option will disable sending sensitive options like, sudo password, database password, wp-admin password over email and notification channels": "此选项将禁用通过电子邮件和通知渠道发送敏感信息，如 sudo 密码、数据库密码、wp-admin 密码。", "Vulnerability Notifications": "漏洞通知", "Enable this option to receive notifications about vulnerabilities via Email": "启用此选项以通过电子邮件接收漏洞通知", "Stay informed and take timely action to secure your systems": "保持信息更新，及时采取行动以保护您的系统。", "Team Packages": "团队套餐", "Renewal type": "续订类型", "Price Per Server": "每台服务器价格", "Total Price": "总价", "Change Password": "更改密码", "Enter a strong password to keep your profile locked": "输入强密码以锁定您的个人资料", "Current Password": "当前密码", "New Password": "新密码", "Pay Bills": "缴费", "Choose Your Payment Method": "选择支付方式", "You are paying for following invoice": "您正在支付以下发票", "Total Amount": "总金额", "Team Products": "团队产品", "Service Type": "服务类型", "Price": "价格", "Role Management": "角色管理", "Server Provider": "服务器提供商", "List of Server Providers": "服务器提供商列表", "Find all the server providers associated with your account here.": "在此处查找与您的帐户关联的所有服务器提供商。", "Servers Count": "服务器数量", "Select Server Provider": "选择服务器提供商", "Vultr API Key": "Vultr API 密钥", "API Key": "API 密钥", "Hetzner API Key": "Hetzner API 密钥", "Hetzner Label": "赫兹纳标签", "Upload JSON": "上传 JSON", "Label for AWS Credential": "AWS凭证标签", "AWS Access Key": "AWS 访问密钥", "Access Key": "访问密钥", "AWS Secret Key": "AWS 密钥", "Secret Key": "密钥", "Setup A Digital Ocean Server In xCloud": "在 xCloud 中设置 Digital Ocean 服务器", "Setup A Vultr Server In xCloud": "在 xCloud 中设置 Vultr 服务器", "Setup A GCP Server In xCloud": "在 xCloud 中设置 GCP 服务器", "SSH Keys": "SSH 密钥", "Add New SSH Key": "添加新的 SSH 密钥", "ID": "ID", "Fingerprint": "指纹", "Auto Provision": "自动配置", "Used By": "使用者", "A name to recognize this public key": "用于识别此公钥的名称", "Key Name": "键名", "Public Key": "公钥", "Don't have a key?": "没有钥匙？", "Learn how to": "了解如何", "generate an SSH Key": "生成 SSH 密钥", "Already have a key?": "已有密钥？", "Copy and paste your key here with": "在此处复制并粘贴您的密钥", "Always provision to new servers": "始终配置到新服务器", "Select servers to push this key to": "选择服务器以推送此密钥", "Default Sudo User": "默认 Sudo 用户", "Default Sudo Password": "默认 Sudo 密码", "If you proceed, this will permanently remove this SSH Key and you will not be able to access or retrieve it again.": "如果继续，此操作将永久删除此 SSH 密钥，您将无法再次访问或检索它。", "Following sudo users will be deleted": "以下 sudo 用户将被删除", "SSH key will be removed from the following sites": "SSH 密钥将从以下站点移除", "Storage Provider": "存储提供商", "List of Storage Providers": "存储提供商列表", "Bucket Name": "存储桶名称", "Region": "区域", "Site Count": "站点计数", "bucket": "桶", "Enter the name of the bucket you have in your storage provider.": "请输入您在存储提供商中的桶名称。", "Select the data location": "选择数据位置", "Select Region": "选择地区", "Enter the access key id here.": "在此输入访问密钥 ID。", "Access Key Id": "访问密钥 ID", "Enter the secret key here.": "在此输入密钥。", "Endpoint": "端点", "Enter the endpoint url here and make sure to add https:// in url.": "在此输入端点 URL，并确保在 URL 中添加 https://。", "Site Backup": "网站备份", "Enter the region here.": "在此输入地区。", "Team Invitations": "团队邀请", "Team Management": "团队管理", "Add New Team": "添加新团队", "It looks like you haven’t created any team yet. Create one now.": "您似乎还没有创建任何团队。立即创建一个吧。", "Profile": "个人资料", "General Information": "常规信息", "Set up your profile by providing the following information": "通过提供以下信息来设置您的个人资料", "Contact Number": "联系电话", "Extra Billing Information": "额外账单信息", "If you require the inclusion of specific contact or tax details on your receipts, such as your VAT identification number, or registered address, you may add it here.": "如果您需要在收据上包含特定的联系或税务信息，例如您的增值税识别号或注册地址，您可以在此添加。", "Name on Invoice": "发票上的姓名", "Billing Emails": "账单邮件", "If": "如果", "Send billing invoices only to the team email address": "仅将账单发票发送至团队电子邮件地址", "is unchecked, billing invoices will be sent to the your given email addresses, separated by commas. Example:": "未选中时，账单发票将发送到您提供的电子邮件地址，并用逗号分隔。例如：", "remaining.": "剩余", "You have invited the following persons.": "您已邀请以下人员。", "Note": "备注", "You can again enable this service.": "您可以再次启用此服务。", "Execute": "执行", "Proceeding will permanently delete all data stored on this server and all WordPress sites under it, which can’t be recovered later. Full Server Backups created for this server will also be deleted.": "继续操作将永久删除存储在此服务器上的所有数据及其下的所有 WordPress 站点，无法恢复。为此服务器创建的完整服务器备份也将被删除。", "Delete From Provider": "从提供者删除", "Enable this to delete this server from the provider also.": "启用此选项以同时从提供商删除此服务器。", "Type server name to confirm": "输入服务器名称以确认", "Delete DNS records from your Cloudflare account": "从您的 Cloudflare 帐户中删除 DNS 记录", "Your DNS records for the sites on this server will be deleted from your Cloudflare account.": "您在此服务器上的站点的 DNS 记录将从您的 Cloudflare 帐户中删除。", "Add site": "添加网站", "sites": "站点", "Storage": "存储", "Ram": "内存", "No sites": "无站点", "Add New Site": "添加新站点", "View Sites": "查看网站", "Restart Server": "重启服务器", "Hard Reboot Server": "硬重启服务器", "Restart Nginx": "重启 Nginx", "Restart LiteSpeed": "重启 LiteSpeed", "Restart MySQL": "重启 MySQL", "Restart MariaDB": "重启 MariaDB", "Archive Server": "归档服务器", "Clone Server": "克隆服务器", "Delete Server": "删除服务器", "View Server": "查看服务器", "Individual Site Details": "单个站点详情", "Site Name": "站点名称", "CPU": "中央处理器", "RAM": "内存", "DISK": "磁盘", "LAST UPDATED": "最近更新", "EC2": "EC2", "Flexible, scalable virtual servers for all workloads.": "灵活、可扩展的虚拟服务器，适用于所有工作负载。", "Lightsail": "Lightsail", "Easy, budget-friendly servers for small projects.": "适合小型项目的简单经济型服务器。", "Set Up Your Server With": "使用以下工具设置您的服务器", "Connect xCloud with your AWS Account": "将 xCloud 连接到您的 AWS 账户", "Connect New Account": "连接新账户", "Verified": "已验证", "Next": "下一步", "Provider Label": "提供者标签", "Connected": "已连接", "Select AWS Service": "选择 AWS 服务", "Please choose an AWS service to create an instance.": "请选择一个 AWS 服务来创建实例。", "Choose region": "选择地区", "Choose Region": "选择地区", "Choose Zone": "选择区域", "Loading zones...": "加载区域...", "Choose Server Size": "选择服务器大小", "Loading server types...": "加载服务器类型...", "Select Database Server": "选择数据库服务器", "Select Tags": "选择标签", "I have understood that the billing of this server will be handled on my server provider account.": "我已了解此服务器的账单将通过我的服务器提供商账户处理。", "Demo Server for Billing Plan": "计费计划演示服务器", "Connect xCloud with your Digital Ocean account": "将 xCloud 连接到您的 Digital Ocean 帐户", "Existing DigitalOcean Credential": "现有的DigitalOcean凭证", "Choose Credential": "选择凭证", "Add new credential": "添加新凭证", "Authorize on Digital Ocean": "在 Digital Ocean 上授权", "You do not have permission to add new provider": "您没有权限添加新供应商", "Enable Digital Ocean": "启用 Digital Ocean", "Auto Backups": "自动备份", "Connect xCloud with your Google Cloud Platform account": "将 xCloud 连接到您的 Google Cloud Platform 帐户", "Select Existing or Connect New": "选择现有或连接新建", "Finish": "完成", "Choose your project": "选择您的项目", "Choose Project": "选择项目", "Connect xCloud with your Hetzner Account": "将 xCloud 连接到您的 <PERSON><PERSON><PERSON> 账户", "Choose Account": "选择账户", "Hetzner API Token": "Hetzner API 令牌", "Connect xCloud with your Linode (Akamai) account": "将 xCloud 连接到您的 Linode (Akamai) 账户", "Existing Linode Credential": "现有 Linode 凭证", "Authorize on Linode (Akamai)": "在 <PERSON><PERSON> (Akamai) 上授权", "Connect xCloud with your Linode Account": "将 xCloud 连接到您的 Linode 帐户", "Vultr Label": "Vultr 标签", "Connect xCloud with your Vultr Account": "将 xCloud 连接到您的 Vultr 帐户", "Enable Vultr": "启用 Vultr", "I am sure that I've read the": "我确定我已经阅读了", "and added Any": "并添加任何", "and Any": "任何", "both under Access Control.": "访问控制下的两者。", "Fill in the details below to get your server set up with xCloud": "请填写以下详细信息以设置您的 xCloud 服务器", "Note down the generated database root password above and secure it as it will not be displayed again.": "请记录上方生成的数据库根密码并妥善保管，因为它不会再次显示。", "Server Details": "服务器详情", "Server Tag(Optional)": "服务器标签（可选）", "Server Type": "服务器类型", "General": "常规", "Cost-effective servers powered by Intel CPUs and regular SSDs.": "由英特尔CPU和普通SSD驱动的高性价比服务器。", "Premium": "高级版", "Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.": "3GHz+ Intel Xeon CPU、快速内存和NVMe存储的超高速服务器。", "Billed Monthly": "按月计费", "SSD": "固态硬盘", "vCPU": "虚拟CPU", "Bandwidth": "带宽", "Recommended Option": "推荐选项", "Backup": "备份", "Enable xCloud Auto Backups": "启用 xCloud 自动备份", "Total Cost": "总费用", "Coupon Code": "优惠码", "Apply": "应用", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks.": "禁用自动备份意味着失去便捷的数据恢复、备份移动性和灾难恢复；如果您了解风险，请点击继续。", "Disable Auto Backup": "禁用自动备份", "Choose Your Continent": "选择您的洲", "Enter Code Here...": "在此输入代码...", "Fill in the details below to get your server set up with": "请填写以下详细信息以设置您的服务器", "Connect Your Existing Server": "连接您的现有服务器", "Fill in the details below to connect xCloud with your existing fresh Ubuntu server from any cloud provider.": "请填写以下信息，以将 xCloud 连接到您现有的任何云服务商的 Ubuntu 服务器。", "You will need to add the xCloud public key to allow server setup access. Please SSH to the server using the default/root account and use the following command to add the public key.": "您需要添加 xCloud 公钥以允许服务器设置访问。请使用默认/根账户通过 SSH 连接到服务器，并使用以下命令添加公钥。", "I have created a new server on my provider": "我已在我的服务商上创建了一个新服务器", "Choose Your Server Provider": "选择服务器提供商", "Choose Hosting Managed by": "选择托管服务由", "Everything You Need to Create a Website": "创建网站所需的一切", "Bring and Manage Your Own Server": "自带并管理您的服务器", "Integrate Your Own Provider.": "集成您自己的提供商", "Manage": "管理", "First Server": "首台服务器", "10 Sites": "10 个站点", "without any cost.": "免费", "Create More, Pay Less upto": "创建更多，支付更少，最高可达", "Start": "开始", "LTD Server Created": "已创建LTD服务器", "Create from package": "从包创建", "You’re on the xCloud Free plan, which includes 1 server and 10 websites with self-hosting. Activate your": "您正在使用 xCloud 免费计划，其中包括 1 台服务器和 10 个自托管网站。激活您的", "team by adding a": "通过添加一个团队", "payment method": "付款方式", "today.": "今天", "team by adding": "通过添加团队", "payment method.": "付款方式", "Choose Provider": "选择提供商", "Only Available for LTD Users": "仅限LTD用户使用", "is pending for setup.": "待设置。", "Automatic Reboot": "自动重启", "Enable Backup": "启用备份", "Disable Backup": "禁用备份", "This setting is to enable/disable the backup settings of cloud provider.": "此设置用于启用/禁用云提供商的备份设置。", "The charge will be": "费用将为", "for this server.": "用于此服务器。", "You will be charged based on the provider policy.": "根据供应商政策收费。", "To prevent system abuse, the backup feature cannot be repeatedly turned on and off. Contact our support if you need any help.": "为防止系统滥用，备份功能无法反复开启和关闭。如需帮助，请联系我们的支持团队。", "Next Backup Schedule": "下次备份计划", "Select Backup Time (UTC)": "选择备份时间（UTC）", "Backup List on Cloud Provider": "云提供商备份列表", "No backup found.": "未找到备份。", "If you enable backup then you will be charged": "启用备份将产生费用", "for this.": "对此。", "based on the provider policy.": "根据提供商政策", "If you disable backup then you will not be able to restore the backup for the server. However, you can enable it anytime.": "如果禁用备份，则无法恢复服务器的备份。不过，您可以随时启用。", "If you proceed, this will restore the backup for the server. This operation is irreversible.": "如果继续，此操作将恢复服务器的备份。此操作不可逆。", "Do you want to set the backup schedule for the server": "您要设置服务器的备份计划吗？", "Backup Schedule Details": "备份计划详情", "Some features may not work due to a payment issue.": "由于支付问题，某些功能可能无法使用。", "Payment failed": "支付失败", "Please update your": "请更新您的", "to retry billing.": "重试账单", "Note: Your billing period has been extended until": "注意：您的账单周期已延长至", "Please pay your outstanding invoices": "请支付您的未结发票", "to avoid any service interruptions.": "避免任何服务中断。", "Add Cron Job": "添加定时任务", "Command": "命令", "You can add wget and curl commands here. But if you want to add wp-cli commands then you need to specify the site location with cd /var/www/sitename && wp command.": "您可以在此添加 wget 和 curl 命令。但如果您想添加 wp-cli 命令，则需要使用 cd /var/www/sitename && wp 命令指定站点位置。", "User": "用户", "Frequency": "频率", "Custom Schedule": "自定义日程", "Save": "保存", "Add Sudo User": "添加 Sudo 用户", "Sudo User Name": "超级用户名称", "Sudo Password": "管理员密码", "Add SSH Key": "添加 SSH 密钥", "No SSH Keys are available. Please add SSH Key.": "没有可用的 SSH 密钥。请添加 SSH 密钥。", "Cron Job": "计划任务", "Scroll to end": "滚动到底部", "Scroll to top": "回到顶部", "Last checked": "上次检查", "Add Database": "添加数据库", "Loading...": "加载中...", "No associated site": "无关联站点", "No database found!": "未找到数据库！", "Database Users": "数据库用户", "No database user found!": "未找到数据库用户！", "xcloud_db": "云数据库", "Database User Name": "数据库用户名", "User (Optional)": "用户（可选）", "Password (Required with user)": "密码（用户必填）", "Add Database User": "添加数据库用户", "Can Access": "可访问", "Keep empty to keep the same password": "保持为空以保留相同密码", "If you proceed, this is permanently removed and you will not be able to access or retrieve it again.": "如果继续，此项将被永久删除，您将无法访问或恢复。", "Update Cron Job": "更新定时任务", "Update Sudo User": "更新 Sudo 用户", "Reset Sudo Password": "重置Sudo密码", "Add New Rule": "添加新规则", "Protocol": "协议", "Traffic": "交通", "Active": "激活", "Fail2Ban Management": "Fail2Ban 管理", "Ban New IP Address": "禁止新的 IP 地址", "Banned IP Addresses": "被禁IP地址", "No banned IP addresses.": "无被禁IP地址。", "SSH": "SSH", "port": "端口", "You can use multiple ports separated by comma. Leave empty to allow all ports. Also you can use a range of ports by using colon(i.e 6000:7000).": "您可以使用逗号分隔多个端口。留空以允许所有端口。您还可以使用冒号指定端口范围（例如 6000:7000）。", "IP Address (Optional)": "IP地址（可选）", "Valid IP address": "有效的 IP 地址", "You can use multiple IP addresses separated by comma. Leave empty to allow all IP addresses. Also you can use a subnet. (i.e *************, *************, *************/24)": "您可以使用逗号分隔多个IP地址。留空以允许所有IP地址。您也可以使用子网。（例如：*************, *************, *************/24）", "All": "全部", "TCP": "TCP", "UDP": "UDP", "Allow": "允许", "Deny": "拒绝", "Adding Rule...": "添加规则…", "Add Rule": "添加规则", "Enter the IP address you want to ban. You can use comma to separate multiple IP addresses (e.g xx.xx.xx.xx, xx.xx.xx.xx)": "请输入您要封禁的IP地址。您可以使用逗号分隔多个IP地址（例如：xx.xx.xx.xx, xx.xx.xx.xx）。", "Banning IP...": "禁止 IP...", "Ban IP": "禁止 IP", "All Servers": "所有服务器", "All Web Servers": "所有网络服务器", "Nginx": "<PERSON><PERSON><PERSON>", "OpenLiteSpeed": "OpenLiteSpeed", "Provisioned": "已配置", "Provisioning": "配置", "Upgrade Server": "升级服务器", "Server Name": "服务器名称", "Choose Your Server Size": "选择服务器大小", "Be cautious when upgrading to a larger hosting plan since you can’t switch back. Also, it’s crucial to back up important data before trying to resize partitions, which can be risky.": "升级到更大的托管计划时请谨慎，因为无法切换回去。此外，在尝试调整分区大小之前，务必备份重要数据，因为这可能存在风险。", "Can not perform server resize action on disconnected server.": "无法对断开连接的服务器执行服务器调整操作。", "Current Status": "当前状态", "After Upgrading": "升级后", "RAM Size": "内存大小", "SSD Space": "SSD 空间", "Server Utilities": "服务器工具", "Delete your Server": "删除服务器", "This action is irreversible, and will also remove the server from your provider, and all data will be erased. We recommend that you keep a backup before you proceed.": "此操作不可逆，将从您的提供商中移除服务器，并删除所有数据。建议您在继续之前备份数据。", "Warning: Before you upgrade": "警告：升级前", "This will require a HARD restart and the process can take up to 15 min. It is recommended to take a full server backup of your server before proceeding.": "这将需要进行强制重启，过程可能需要长达15分钟。建议在继续操作前对服务器进行完整备份。", "New Plan": "新计划", "If you upgrade now, you’ll be charged": "立即升级将会收费", "Provider Name": "提供者名称", "Ubuntu Version": "Ubuntu 版本", "MySQL Version": "MySQL 版本", "Full Server Migrations": "完整服务器迁移", "Initiate Full Server Migration": "启动完整服务器迁移", "Public Ip": "公网 IP", "Updated": "已更新", "In Use": "使用中", "Available": "可用", "Utilization": "使用率", "Cores": "核心", "Speed/Core": "速度/核心", "Threads": "线程", "Hard Disk Usage": "硬盘使用情况", "Total": "总计", "Used": "已使用", "Uptime Overview": "运行时间概览", "Select your site's PHP version to update php settings on the sites.": "选择您的站点 PHP 版本以更新站点的 PHP 设置。", "Max Execution Time": "最大执行时间", "The maximum time in seconds a script is allowed to run before it is terminated by the parser. We recommend 60 seconds.": "脚本允许运行的最长时间（秒），超时将被解析器终止。建议设置为60秒。", "Max Input Time": "最大输入时间", "The maximum time in seconds a script is allowed to parse input data, like POST and GET. We recommend 60 seconds.": "脚本解析输入数据（如 POST 和 GET）的最大时间（秒）。建议设置为 60 秒。", "Max Input Vars": "最大输入变量", "The maximum number of input variables allowed per request. We recommend 1000 vars.": "每个请求允许的最大输入变量数。建议使用1000个变量。", "Memory Limit": "内存限制", "The maximum amount of memory a script may consume. We recommend 256MB.": "脚本可使用的最大内存量。我们建议 256MB。", "Post Max Size": "上传大小限制", "The maximum size of POST data that PHP will accept. We recommend 128MB.": "PHP可接受的POST数据的最大大小。我们建议设置为128MB。", "Max File Upload Size": "最大文件上传大小", "The maximum size of an uploaded file. We recommend 128MB.": "上传文件的最大大小。建议使用128MB。", "Session GC Maxlifetime": "会话 GC 最大生存时间", "The maximum time in seconds a session is allowed to be idle before it is terminated by the session garbage collector. We recommend 1440 seconds.": "会话在被会话垃圾回收器终止前允许空闲的最长时间（秒）。我们建议设置为1440秒。", "Important Note": "重要提示", "Enabling PHP OPCache will highly improve performance by storing precompiled scripts (PHP Code) in shared memory.": "启用 PHP OPCache 将通过在共享内存中存储预编译脚本（PHP 代码）大幅提升性能。", "If you allow optimizing your OPCache, you need to make sure that your deployment parse code reloads the PHP FPM services at the end of each development.": "如果您允许优化您的 OPCache，请确保在每次开发结束时，您的部署解析代码会重新加载 PHP FPM 服务。", "Disable OPCache": "禁用 OPCache", "Enable OPCache": "启用OPCache", "Server Statistics": "服务器统计信息", "Disable it to turn off magic login feature on all sites under this server.": "禁用此功能以关闭此服务器上所有网站的魔法登录功能。", "Back To Servers": "返回服务器", "Team": "团队", "WEB SERVER": "Web服务器", "Disk Usage": "磁盘使用情况", "Checked": "已检查", "Check Again": "重新检查", "Your disk space is low. Please upgrade your plan to avoid any downtime.": "您的磁盘空间不足。请升级您的计划以避免停机。", "Close": "关闭", "No sites on this server": "此服务器上没有站点", "Get started by creating a new site!": "开始创建新站点！", "Sudo Users": "超级用户", "Enable Vulnerability Scan": "启用漏洞扫描", "Enable Auto Update": "启用自动更新", "If vulnerability found we will update in 24 hours": "如发现漏洞，我们将在24小时内更新。", "Enable Auto Backup": "启用自动备份", "Select All Sites": "选择所有站点", "Choose a Server to Clone Sites from": "选择服务器以克隆站点", "Team Details": "团队详情", "Create a new team to collaborate with others on projects.": "创建新团队以与他人协作项目。", "If you proceed, this will remove all the services and sites along with other data which cannot be recovered.": "如果继续，此操作将删除所有服务和站点以及其他无法恢复的数据。", "I have understood and would like to proceed.": "我已理解并愿意继续。", "Permanently delete this team.": "永久删除此团队。", "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.": "删除团队后，其所有资源和数据将被永久删除。在删除此团队之前，请下载您希望保留的任何数据或信息。", "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.": "您确定要删除此团队吗？一旦删除，团队的所有资源和数据将被永久删除。", "Add Team Member": "添加团队成员", "Add a new team member to your team, allowing them to collaborate with you.": "添加新团队成员，协作更高效。", "Please provide the email address of the person you would like to add to this team.": "请提供您想添加到此团队的人员的电子邮件地址。", "Added.": "已添加。", "Add": "添加", "Pending Team Invitations": "待处理的团队邀请", "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.": "这些人已被邀请加入您的团队，并已收到邀请邮件。他们可以通过接受邮件邀请来加入团队。", "All of the people that are part of this team.": "团队中的所有成员。", "Leave": "离开", "Remove": "移除", "Manage Role": "管理角色", "Are you sure you would like to leave this team?": "您确定要离开这个团队吗？", "Remove Team Member": "移除团队成员", "Are you sure you would like to remove this person from the team?": "您确定要将此人从团队中移除吗？", "The team's name and owner information.": "团队名称和所有者信息。", "Saved.": "已保存。", "Create Team": "创建团队", "Team Settings": "团队设置", "Join the Exclusive Waitlist!": "加入专属候补名单！", "Your Email Address..": "您的电子邮箱地址", "Join Waitlist": "加入候补名单", "Already have been invited?": "已收到邀请？", "Login to xCloud": "登录 xCloud", "When you cancel migration, already migrated data will be removed from the server.": "取消迁移时，已迁移的数据将从服务器中删除。", "Cancel Migration": "取消迁移", "Auth": "认证", "Cache": "缓存", "Git": "Git", "Proceeding will permanently delete this Site and all of its data.": "继续操作将永久删除此站点及其所有数据。", "Delete All Files and Configurations.": "删除所有文件和配置", "Do You Want To Delete Files?": "是否要删除文件？", "Delete Database": "删除数据库", "Do You Want To Delete Database?": "是否要删除数据库？", "Delete Site User": "删除站点用户", "Do You Want To Delete User?": "是否要删除用户？", "Delete Local Backups": "删除本地备份", "Do You Want To Delete Local Backups?": "是否要删除本地备份？", "Type site name to confirm": "输入网站名称以确认", "Delete DNS record from your Cloudflare account": "从您的 Cloudflare 账户中删除 DNS 记录", "Your DNS record for the site on this server will be deleted from your Cloudflare account.": "您在此服务器上的站点的 DNS 记录将从您的 Cloudflare 帐户中删除。", "Proceeding will disable this Site.": "继续操作将禁用此站点。", "Disable Site": "禁用网站", "Also Disable Cron For This Site": "同时禁用此站点的 Cron", "Disable HTML Markup": "禁用HTML标记", "After turning on Disable Site, you won’t be able to use this site on the web anymore along with losing access through SFTP/SSH. Plus, all the scheduled tasks for the site will be paused once you turn on Disable Cron For This Site.": "启用“禁用站点”后，您将无法再通过网络使用此站点，并且无法通过 SFTP/SSH 访问。此外，一旦启用“禁用此站点的 Cron”，该站点的所有计划任务将会暂停。", "Disable": "禁用", "Site is currently disabled.": "站点当前已禁用。", "Click here": "点击此处", "to enable this site": "启用此网站", "The maximum amount of memory a script may consume. we recommend 128MB.": "脚本可使用的最大内存量。我们建议设置为128MB。", "The number of seconds after which data will be seen as 'garbage' and potentially cleaned up. we recommend 1440 seconds.": "数据在经过多少秒后将被视为“垃圾”并可能被清理。我们建议设置为1440秒。", "Max PHP Workers": "最大 PHP 工作线程数", "The number of PHP workers (pm.max_children) that can be spawned to handle requests.": "可以生成以处理请求的 PHP 工作进程数量 (pm.max_children)。", "Redirect Type": "重定向类型", "Choose Redirect Type": "选择重定向类型", "From": "从", "To": "至", "View": "查看", "Magic Login": "魔法登录", "Site Logs": "站点日志", "Site Events": "站点事件", "Purge Cache": "清除缓存", "Clone Site": "克隆站点", "Delete Site": "删除站点", "Create New Database": "创建新数据库", "Database Cluster Name": "数据库集群名称", "Cluster Name": "集群名称", "Select Existing Database Cluster": "选择现有数据库集群", "Additional Domain Name": "附加域名", "Additional Domain (Optional)": "附加域（可选）", "Add Domain": "添加域名", "Admin Password": "管理员密码", "Admin Email Address": "管理员电子邮箱地址", "WordPress Version": "WordPress 版本", "Prefix": "前缀", "wp_": "wp_", "Full Page Caching": "全页缓存", "Redis Object Caching": "Redis 对象缓存", "Could not connect to the database server. Please check your database credentials and try again.": "无法连接到数据库服务器。请检查您的数据库凭据并重试。", "Database Username": "数据库用户名", "Database Host": "数据库主机", "Database Port": "数据库端口", "Connection URL": "连接 URL", "Please use the database connection URL provided to establish a connection between your database client and the database. We suggest using TablePlus as your database client.": "请使用提供的数据库连接URL在您的数据库客户端和数据库之间建立连接。我们建议使用TablePlus作为您的数据库客户端。", "Add the following record to the DNS for your domain. This required when when you want to get a free SSL certificate issued & managed by": "将以下记录添加到您的域名DNS中。这是获取由...颁发和管理的免费SSL证书所必需的。", "If you have manually added DNS record with Cloudflare proxy then the verify option will not work": "如果您手动添加了带有 Cloudflare 代理的 DNS 记录，则验证选项将无法使用。", "Record": "记录", "A": "A", "Verify My DNS": "验证我的DNS", "In your domain settings, you need to add the following records for configuring your site email.": "在您的域设置中，您需要添加以下记录以配置您的网站邮箱。", "Record(SPF)": "记录 (SPF)", "TXT": "文本", "Value": "值", "CNAME": "CNAME", "Record(DKIM)": "记录（DKIM）", "Record(DMARC)": "记录（DMARC）", "Verify Record": "验证记录", "Use free SSL certificate issued & managed by": "使用由...颁发和管理的免费SSL证书", "I will provide the certificate and manage it myself.": "我将自行提供并管理证书。", "Certificate": "证书", "Paste Your Certificate Here": "在此粘贴您的证书", "Private Key": "私钥", "Paste Your Private Key Here": "在此粘贴您的私钥", "Enabling HTTPS makes your website more secure.": "启用 HTTPS 可提高您网站的安全性。", "Learn more": "了解更多", "Beta": "测试版", "Install New WordPress Website": "安装新的 WordPress 网站", "Select this option if you want to a create a fresh new WordPress website": "如果您想创建一个全新的 WordPress 网站，请选择此选项", "Clone a Git Repository": "克隆 Git 仓库", "Clone a git repository to create a new website": "克隆 Git 仓库以创建新网站", "Migrate An Existing WordPress Website": "迁移现有的WordPress网站", "Have an existing website already? Select this option to migrate it with ": "已经有现有网站？选择此选项进行迁移", "Manually Upload WordPress Website": "手动上传WordPress网站", "Upload a zipped file of your existing WordPress website": "上传您现有 WordPress 网站的压缩文件", "Migrate Full Server": "迁移完整服务器", "Migrate all WordPress sites from Ubuntu servers with a few clicks": "一键迁移所有 WordPress 站点从 Ubuntu 服务器", "Certificate Issue": "证书问题", "Expires On": "到期日", "Renew Date": "续订日期", "Add Your New Site Into": "将您的新站点添加到", "This server created under xCloud Free plan which includes 1 server and": "此服务器是在 xCloud 免费计划下创建的，该计划包含 1 台服务器和", "website with Self Hosting. To unlock full features, please consider upgrading the server bill from free to paid plan.": "网站支持自托管。要解锁全部功能，请考虑将服务器账单从免费计划升级到付费计划。", "Your": "您的", "is low. Please upgrade your plan to avoid any downtime.": "电量不足。请升级您的计划以避免停机。", "Choose a Server to add Site": "选择服务器以添加站点", "Site Title": "网站标题", "New Site Title": "新站点标题", "Add Tag (optional)": "添加标签（可选）", "Go Live": "开始直播", "(Optional)": "（可选）", "Integrate Cloudflare forAutomatic DNS and SSL management.": "集成 Cloudflare 以自动管理 DNS 和 SSL。", "If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.": "如果您使用的附加域与主域不同，则需要切换到 xCloud SSL。", "WordPress Multisite": "WordPress 多站点", "Enable Multisite": "启用多站点", "Select Multisite Type": "选择多站点类型", "You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.": "站点创建后，需在站点的 SSL/HTTPS 页面为多站点子域安装 SSL。", "Enhancing Website Performance": "提升网站性能", "Speed up your website by using smart caching!": "通过智能缓存加速您的网站！", "Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "结合全页缓存和 Redis，让您的网站运行更快，为访问者提供更好的体验。", "Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.": "启用 LiteSpeed 缓存以加快网站速度，提升访客体验。", "LiteSpeed Cache": "LiteSpeed 缓存", "Email Provider Configuration": "电子邮件提供商配置", "Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.": "每月免费获取100封邮件，助力您的业务起步。如需使用您自己的域名，请禁用此选项。", "Read our documentation": "阅读我们的文档", "Blueprints": "蓝图", "Choose a blueprint to install WordPress with pre-configured plugins and themes.": "选择一个蓝图来安装预配置插件和主题的WordPress。", "All Blueprints": "所有蓝图", "Manage your all blueprints as you like, you can edit, delete or create new from here": "管理您的所有蓝图，您可以在此编辑、删除或创建新蓝图。", "OK": "确定", "Create Your New Site Into": "创建您的新网站", "xCloud Playground": "xCloud 游乐场", "Playground Environment": "游乐场环境", "This demo site will expire 24 hours after creation.": "此演示站点将在创建后24小时过期。", "Staging Domain Setup": "暂存域设置", "This will be auto-generated according to your site title": "这将根据您的网站标题自动生成", "Speed up your website by using smart caching! Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "使用智能缓存加速您的网站！结合全页缓存和Redis，让您的网站运行更快，提升访客体验。", "Step 1 of 3": "第 1 步，共 3 步", "Hey you are trying to access the playground site": "您正在尝试访问游乐场网站", "but you are not in the": "但您不在", "We request you to check your email and accept the invitation to join and edit the site.": "请检查您的电子邮件并接受邀请，以加入和编辑该网站。", "Enable Adminer": "启用 Adminer", "Adminer can enhance your database management experience. Activate it only when necessary to ensure optimal security measures.": "Adminer 可以提升您的数据库管理体验。仅在必要时激活，以确保最佳安全措施。", "Database Manager": "数据库管理器", "Manage your databases with Adminer, an opensource database management tool.": "使用 Adminer 管理您的数据库，这是一款开源数据库管理工具。", "Launch Adminer": "启动 Adminer", "Basic Authentication": "基本身份验证", "Protect Your Site": "保护您的网站", "Turn on to enable basic authentication for your site by adding username and password.": "启用以通过添加用户名和密码来为您的网站启用基本身份验证。", "Previous Remote Backups": "以前的远程备份", "Backup Now": "立即备份", "File": "文件", "Previous Local Backups": "以前的本地备份", "Remote Backup Settings": "远程备份设置", "Backup Type": "备份类型", "Select Bucket": "选择存储桶", "Server Bucket": "服务器存储桶", "Manage Your Storage Providers": "管理存储提供商", "Backup Items": "备份项目", "Database Backup": "数据库备份", "will be backed up.": "将被备份。", "Files Backup": "文件备份", "Exclude Paths": "排除路径", "Automatic Backup": "自动备份", "Automatic Full Backup": "自动完整备份", "Select Backup Frequency": "选择备份频率", "Select Full Backup Frequency": "选择完整备份频率", "Automatic Incremental Backup": "自动增量备份", "Automatic Delete": "自动删除", "Delete After Days": "删除天数后", "30": "30", "Local Backup Settings": "本地备份设置", "Select Incremental Backup Frequency": "选择增量备份频率", "Change": "更改", "Additional Domains": "附加域名", "Add new": "新增", "After changing the domain, you need to install SSL for Multisite Subdomain from the site SSL/HTTPS page.": "更改域名后，您需要从站点的 SSL/HTTPS 页面安装多站点子域的 SSL。", "Connect New Provider": "连接新供应商", "Use xCloud Domain": "使用 xCloud 域", "Use Your Own Domain": "使用您自己的域名", "Requires Verification": "需要验证", "From Name": "发件人姓名", "This is the name that will appear in the recipient's inbox.": "这是将在收件人收件箱中显示的名称。", "To use your own domain, please verify your domain with xCloud by following the DNS setup below.": "要使用您自己的域名，请按照以下 DNS 设置通过 xCloud 验证您的域名。", "Clear Provider": "清除提供者", "We only use the Fluent SMTP plugin to configure your given SMTP credentials on the sites, ": "我们仅使用 Fluent SMTP 插件来配置您在网站上的 SMTP 凭据。", "so that you do not have to do anything manually. If you are not getting emails then please ": "为了避免手动操作。如果您没有收到电子邮件，请", "check your email logs from your email provider. To test your email integration, please send a test email from your fluentsmtp plugin.": "请检查您的电子邮件提供商的邮件日志。要测试您的电子邮件集成，请通过 FluentSMTP 插件发送测试邮件。", "Enable File Manager": "启用文件管理器", "Tiny File Manager": "微型文件管理器", "Activate it only when necessary to ensure optimal security measures.": "仅在必要时激活，以确保最佳安全措施。", "File Manager": "文件管理器", "Manage your files, an opensource tool.": "管理您的文件，一个开源工具。", "Launch File Manager": "启动文件管理器", "Page Caching": "页面缓存", "FastCGI Nginx": "FastCGI Nginx", "Cache Duration": "缓存持续时间", "Unit": "单位", "Cache Exclusion HTTP URL Rules": "缓存排除 HTTP URL 规则", "Cache Exclusion Cookie Rules": "缓存排除 Cookie 规则", "Clear Page Cache": "清除页面缓存", "This will slow down your site until the caches are rebuilt.": "这将减慢您的网站速度，直到缓存重建完成。", "Purging Cache...": "清除缓存中...", "Action will be available once the Object Cache operation is finished": "对象缓存操作完成后，操作将可用。", "Object Cache": "对象缓存", "Redis User": "Redis 用户", "Redis Password": "Redis 密码", "Redis Object Cache Key": "Redis对象缓存键", "Redis object cache optimize performance, reduces database load, and enhances response times for a seamless browsing experience.": "Redis对象缓存优化性能，减少数据库负载，提升响应速度，带来流畅的浏览体验。", "Stores the results of queries to the site’s database.": "存储对网站数据库查询的结果。", "Clear Object Cache": "清除对象缓存", "Git Settings": "Git 设置", "Pull and deploy now": "立即拉取并部署", "Updated At": "更新于", "Production": "生产", "Staging": "暂存环境", "Demo": "演示", "Vulnerable": "易受攻击", "Migrating": "迁移中", "Add IP Address": "添加 IP 地址", "No IP addresses added yet.": "尚未添加IP地址。", "IP Addresses": "IP地址", "Updating...": "更新中...", "Adding...": "添加中...", "LiteSpeed Cache for WordPress (LSCWP) is an all-in-one site acceleration plugin, featuring an exclusive server-level cache and a collection of optimization features.": "LiteSpeed Cache for WordPress (LSCWP) 是一款全方位的网站加速插件，具有独特的服务器级缓存和一系列优化功能。", "Clear LiteSpeed Cache": "清除 LiteSpeed 缓存", "Showing logs from": "显示日志来自", "Loading..": "加载中…", "WordPress Debug Log": "WordPress 调试日志", "Reload": "重新加载", "Clear": "清除", "Clearing..": "清除中…", "Your Site Has Been Successfully Migrated": "您的网站已成功迁移", "Staging URL": "暂存网址", "Setup Email for this site": "为此站点设置电子邮件", "Monitoring Stats": "监控统计", "CPU Usages": "CPU 使用情况", "SSL Overview": "SSL 概览", "DNS Status": "DNS 状态", "SSL Status": "SSL 状态", "Expiry": "到期", "WordPress Logs": "WordPress 日志", "Updates": "更新", "Update Available": "更新可用", "Up To Date": "最新状态", "Plugins": "插件", "Themes": "主题", "Custom Nginx Config": "自定义 Nginx 配置", "Fetching Nginx..": "正在获取 Nginx...", "Preview Nginx": "预览 Nginx", "Add a New Config": "添加新配置", "Select Config Type": "选择配置类型", "Config File Name": "配置文件名", "Config Content": "配置内容", "Preview Content": "预览内容", "Running...": "运行中...", "Run & Debug": "运行与调试", "Nginx Config File": "Nginx 配置文件", "Save Settings": "保存设置", "Nginx & Security": "Nginx与安全", "Security": "安全性", "7G Firewall": "7G防火墙", "8G Firewall": "8G防火墙", "Disable Nginx File Regeneration": "禁用 Nginx 文件再生成", "PHP Execution on Upload Directory": "上传目录的PHP执行", "Enable XML-RPC": "启用 XML-RPC", "Edit X-Frame-Options": "编辑 X-Frame-Options", "Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com": "请选择以下之一：SAMEORIGIN、<PERSON><PERSON><PERSON>、ALLOW<PERSON>L、ALLOW-FROM https://example.com 或 ALLOW-FROM https://example2.com https://example3.com", "With xCloud you get 100 emails free per month in each team. Manage email for this site from": "在 xCloud 中，每个团队每月可免费获得 100 封电子邮件。管理此站点的电子邮件来自", "You can purchase more or add your own email providers from": "您可以购买更多或添加您自己的电子邮件提供商，来自", "Learn more from our": "了解更多信息，请访问我们的", "Primary Domain": "主域名", "Add New": "添加新项", "Others": "其他", "Page Cache": "页面缓存", "on": "开启", "off": "关闭", "SSL/HTTPS": "SSL/HTTPS", "Basic Auth": "基本认证", "No Updates": "无更新", "Most Recent Event": "最新事件", "View All Events": "查看所有活动", "Redirection": "重定向", "Add Redirection": "添加重定向", "Redirections": "重定向", "Edit Redirection": "编辑重定向", "WP-Cron": "WP-Cron", "WP-Cron manages time-based tasks in WordPress, relying on site visits.": "WP-Cron 依赖于网站访问来管理 WordPress 中的定时任务。", "If you want to also add additional custom cronjob then you can configure on your server from": "如果您想添加额外的自定义定时任务，可以在您的服务器上进行配置。", "Edit Site Tags": "编辑网站标签", "WP Debug": "WP 调试", "Enable WP Debug": "启用 WP 调试", "Enable this to view WordPress debug logs on the": "启用此功能以查看 WordPress 调试日志", "site's logs page": "站点日志页面", "Delete Site Confirmation": "删除站点确认", "This action is irreversible. Delete sites cannot be restored.": "此操作不可逆。删除的网站无法恢复。", "Rescue Site": "救援地点", "Run Now": "立即运行", "Repair Site User": "维修站用户", "Update Directory Permissions": "更新目录权限", "Repair PHP": "修复 PHP", "Regenerate": "重新生成", "It is recommended to keep the above options turned on before running the rescue action.": "建议在执行修复操作前保持以上选项开启。", "Back To Sites": "返回站点", "staging": "暂存环境", "Add Staging environment": "添加暂存环境", "Visit Site": "访问网站", "Deploy Staging": "部署暂存环境", "WordPress": "WordPress", "Archive": "归档", "Are You Sure You Want To Deploy Staging for  ": "您确定要部署暂存环境吗？", "A staging site is an identical copy of your production website, created for testing purposes. This isolated environment lets you test any plugin, or theme, or make any other changes before going live. This removes the risk of damaging your production website. Later, you can pull/push updates and apply to your production.": "测试站点是您的生产网站的一个相同副本，用于测试目的。这个独立环境允许您在上线前测试任何插件、主题或进行其他更改。这消除了损坏生产网站的风险。之后，您可以拉取/推送更新并应用到您的生产网站。", "Staging Site Domain": "暂存站点域名", "Preparing Deployment..": "准备部署中…", "Site SSH/sFTP Access": "站点 SSH/sFTP 访问", "Site Username": "网站用户名", "Site Path": "站点路径", "SSH String": "SSH 字符串", "Database URL Connection": "数据库 URL 连接", "DNS Setup For Multisite SSL": "多站点 SSL 的 DNS 设置", "Provide your own certificate & manage it yourself": "提供您自己的证书并自行管理", "Use Cloudflare managed SSL Certificate.": "使用 Cloudflare 管理的 SSL 证书。", "Demo Environment": "演示环境", "Staging Environment": "暂存环境", "Demo Site Domain": "演示站点域名", "Staging Domain": "暂存域", "If you’re using different additional domains then you need to switch to xCloud SSL. With Cloudflare this is not supported.": "如果您使用不同的附加域，则需要切换到 xCloud SSL。Cloudflare 不支持此功能。", "Staging Management": "阶段管理", "Pull Data from Production to Staging": "从生产环境提取数据到暂存环境", "Copy the changes from the live site to staging": "将更改从线上站点复制到预备环境", "Pulling Data...": "正在提取数据...", "Pull Data": "提取数据", "Push Data from Staging to Production": "将数据从暂存推送到生产", "Copy the changes from the staging to live": "将更改从暂存复制到上线", "Pushing Data...": "正在推送数据…", "Push Data": "推送数据", "Deployment Logs": "部署日志", "View All Logs": "查看所有日志", "Pull data from ": "从...提取数据", "Files": "文件", "Overwrite": "覆盖", "Incremental": "增量", "Full": "完整", "Selected Tables": "已选择的表格", "fetching tables...": "正在获取表格...", "Fetching tables...": "正在获取表格...", "Initiating Pulling...": "开始拉取...", "Push data from this Staging Site to the Production site(": "将数据从此暂存站点推送到生产站点", "Push to Production": "推送到生产环境", "Your staging site data will be pushed to production. The production site may be temporarily unavailable for a while.": "您的暂存站点数据将被推送到生产环境。生产站点可能会暂时不可用。", "Select Tables": "选择表格", "Take backup of your production site before pushing changes(Recommended).": "在推送更改之前备份您的生产站点（推荐）。", "Initiating Pushing...": "正在推送...", "Source Site": "来源网站", "Destination Site": "目标站点", "Initiated By": "发起者", "Inactive": "非活动", "WordPress Core": "WordPress核心", "Your current version is": "您当前的版本是", "Updating to": "更新至", "Your WordPress": "您的WordPress", "is up to date.": "已是最新版本。", "Version": "版本", "Changelog": "更新日志", "Activating...": "激活中...", "Activate": "激活", "Activated": "已激活", "Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or": "此站点的漏洞检测当前已禁用。要启用，请访问服务器>安全设置。", "click here": "点击此处", "Insecure": "不安全", "Secure": "安全", "Last Scan": "上次扫描", "An Updated Version of WordPress is Available": "WordPress的新版本可用", "CVSS Score": "CVSS 评分", "Ignored": "忽略", "Ignore": "忽略", "Patched": "已修补", "Fixed in": "已修复", "Remediation": "修复", "Direct URL": "直接URL", "First Published": "首次发布", "Last Update": "最后更新", "Update Config": "更新配置", "WP Config": "WP 配置", "Invoices of Clients": "客户发票", "Here you can check all your previous invoices": "在此查看您所有的历史发票", "Copy": "复制", "View Nova": "查看 Nova", "Total Clients": "总客户数", "Total Products": "总产品数量", "Total Servers": "服务器总数", "You don’t have any server yet": "您还没有服务器", "Sites": "站点", "You don’t have any site yet": "您还没有任何站点", "Clients Overview": "客户概览", "Total Active Clients": "活跃客户总数", "Total Inactive Clients": "总非活跃客户", "Total Invoices": "总发票数量", "You don’t have any client yet": "您还没有任何客户", "Access Dashboard & Website": "访问仪表板和网站", "Domain Settings": "域设置", "Visit Landing Page": "访问登录页", "Dashboard": "仪表板", "Brand Setup": "品牌设置", "Brand Profile": "品牌简介", "Add Your Logo": "添加您的徽标", "Upload a logo that represent your brand profile.": "上传代表您的品牌形象的标志。", "Please upload a logo with a minimum size of 212x40 pixels in PNG, JPEG, or JPG format (max 5MB) for best quality.": "请上传一个最小尺寸为212x40像素的PNG、JPEG或JPG格式的标志（最大5MB）以获得最佳质量。", "Upload Logo": "上传徽标", "Cloud Hosting": "云托管", "Brand Name": "品牌名称", "<EMAIL>": "<EMAIL>", "Support Email": "支持邮箱", "579 Spruce Court, Dallas, TX 75201": "579 Spruce Court, 达拉斯, TX 75201", "Address": "地址", "Copyright Name": "版权所有名称", "Processing...": "处理中…", "Proceed to Checkout": "继续结算", "Sell up to": "最多出售", "Checkout": "结账", "Your Order Summary": "订单摘要", "Sub Total": "小计", "Processing Payment...": "处理中付款...", "Processing Offer...": "正在处理优惠…", "Claim Free": "领取免费奖励", "Create Product": "创建产品", "Create Hosting Plan": "创建托管计划", "Plan Name": "计划名称", "Renewal Type": "续订类型", "SKU": "SKU", "Setup Products & Start Selling": "设置产品并开始销售", "Add Your Domain Name": "添加您的域名", "example.com": "示例.com", "Add the following record to the DNS manager for your domain": "将以下记录添加到您的域的 DNS 管理器中", "Skip this step, if you are not ready to point your domain server.": "如果您尚未准备好指向您的域服务器，请跳过此步骤。", "Payment Setup": "付款设置", "Integrate with Stripe Connect": "集成 Stripe Connect", "Connect to your existing Stripe account or create a new account to start processing payments via Stripe.": "连接到您现有的Stripe账户或创建新账户以开始通过Stripe处理付款。", "Connect Now": "立即连接", "Stripe Account": "Stripe 账户", "If your Stripe account is set up correctly, it will automatically connect within 1–2 minutes.": "如果您的 Stripe 帐户设置正确，它将在 1-2 分钟内自动连接。", "If you’ve just created a new Stripe business account, it may take up to 3 days for Stripe to verify it.Once verified, your account should connect automatically.": "如果您刚创建了一个新的 Stripe 商业账户，Stripe 可能需要最多 3 天来验证。验证完成后，您的账户应会自动连接。", "To check the status of your": "检查您的状态", "account": "账户", "you can visit your": "您可以访问您的", "Stripe dashboard.": "Stripe 仪表板", "Billing Account": "帐单账户", "Stripe Account Name": "Stripe 账户名称", "Stripe Account ID": "Stripe 账户 ID", "Connected On": "已连接", "Billing Currency": "计费货币", "3D Secure Card Compatibility for Your Clients (Optional)": "3D安全卡兼容性（可选）", "Stripe Publishable Key": "Stripe 公钥", "If your client prefers to pay using a 3D Secure card, you need to add your": "如果您的客户希望使用3D安全卡支付，您需要添加您的", "Stripe Publishable Key.": "Stripe 发布密钥", "Get your publishable key": "获取您的发布密钥", "from here.": "从这里。", "Syncing Account..": "同步账户中…", "Having Trouble?": "遇到问题？", "Read our comprehensive documentation & learn to manage your hosting easily.": "阅读我们的详细文档，轻松管理您的主机。", "Read Documentation": "阅读文档", "Start Your Hosting Business: Resell & Earn Revenue": "开始您的托管业务：转售并赚取收入", "Launch a cloud hosting business with xCloud Managed Servers. Resell our fully managed web hosting under your own brand or domain to maximize your revenue ensuring a reliable performance.": "使用 xCloud 托管服务器启动云托管业务。在您的品牌或域名下转售我们的全托管网络托管服务，以最大化您的收入并确保可靠的性能。", "Complete control for your personal branding": "完全掌控您的个人品牌", "Manage your client billings with Stripe Connect": "使用 Stripe Connect 管理客户账单", "Customize hosting packages & sell at your own price": "自定义托管套餐并自行定价", "Get access to powerful features of xCloud": "访问 xCloud 的强大功能", "By ticking this box, you are confirming that you have read, understood, and agree to our": "勾选此框，即表示您已阅读、理解并同意我们的内容。", "Please check this box to confirm that you accept the terms and conditions and want to proceed.": "请勾选此框以确认您接受条款和条件并继续。", "Before proceeding, ensure you have an active Stripe account, as all transactions are managed via": "在继续之前，请确保您拥有一个有效的 Stripe 账户，因为所有交易都通过该账户管理。", "Stripe Connect": "Stripe Connect", "Please check this box to confirm that you have a Stripe account and want to proceed.": "请勾选此框以确认您拥有 Stripe 账户并希望继续。", "Start Your Hosting Business Now": "立即开始您的托管业务", "Product Details": "产品详情", "Product Information": "产品信息", "Some basic information is shared over here": "这里共享了一些基本信息", "Edit Product": "编辑产品", "Checkout URL": "结账网址", "Invoices of Product": "产品发票", "Source Product": "源产品", "Includes Up to RAM": "包含高达 RAM", "Customize Package": "自定义套餐", "My Server": "我的服务器", "Price/Month": "每月价格", "XCPU110": "XCPU110", "Stripe": "Stripe", "will deduct a 3%-7% fee per sale. Your approximate profit for this sale is": "每笔销售将扣除3%-7%的费用。您此次销售的预期利润为", "Active Package": "活动套餐", "Preview Plan": "预览计划", "Save and Publish": "保存并发布", "Add Product": "添加产品", "Buying Price": "购买价格", "Selling Price": "售价", "Create New Product": "创建新产品", "Select Server Plan at xCloud": "选择 xCloud 服务器计划", "Select Server Size": "选择服务器大小", "Change Plan": "更改计划", "Duplicate Product": "复制产品", "Duplicate": "复制", "Favicon": "网站图标", "Add Your Favicon": "添加您的网站图标", "Please upload a favicon with a minimum size of 16x16 pixels in PNG or ICO format (max 1MB) for best quality.": "请上传一个最小尺寸为16x16像素的PNG或ICO格式的图标（最大1MB）以获得最佳质量。", "Upload Favicon": "上传网站图标", "Custom Domain Setup": "自定义域名设置", "Your Domain Name": "您的域名", "Landing Page Settings": "着陆页设置", "Landing Page": "着陆页", "Enable it to use and customize the ready landing page settings": "启用并自定义现成的登录页面设置", "Navbar Logo": "导航栏徽标", "Update your logo for navigation bar": "更新导航栏徽标", "Upload a logo or icon that represent your brand profile.": "上传代表您的品牌资料的标志或图标。", "Hero Section": "英雄部分", "Fast, Secure & Reliable Cloud Hosting": "快速、安全、可靠的云托管", "Heading": "标题", "Some more information that uou can add here": "您可以在此添加更多信息", "Sub Heading": "副标题", "Create Now": "立即创建", "Button Text": "按钮文本", "Button URL": "按钮 URL", "https://www.example.com": "https://www.example.com", "CTA Section": "CTA部分", "Social Media Links": "社交媒体链接", "https://facebook.com/xcloud": "https://facebook.com/xcloud", "Facebook": "脸书", "https://instagram.com/xcloud": "访问：https://instagram.com/xcloud", "Instagram": "Instagram", "https://x.com/xcloud": "https://x.com/xcloud", "X.com": "X.com", "https://linkedin.com/xcloud": "https://linkedin.com/xcloud", "Linkedin": "领英", "https://youtube.com/xcloud": "https://youtube.com/xcloud", "Youtube": "YouTube", "Preview": "预览", "Saving...": "保存中…", "Connect to Stripe": "连接到 Stripe", "Account Name": "账户名称", "Stripe 3DS Card Setup": "Stripe 3DS 卡设置", "Privacy Policy Settings": "隐私政策设置", "Default Privacy Policy": "默认隐私政策", "Use your own Privacy Policy": "使用您自己的隐私政策", "SMTP Settings": "SMTP 设置", "Use Custom SMTP": "使用自定义 SMTP", "Use my custom smtp credentials for email sending.": "使用我的自定义 SMTP 凭据发送电子邮件。", "SMTP Credentials": "SMTP 凭证", "smtp.mailgun.org": "smtp.mailgun.org", "Host": "主机", "587": "587", "SMTP username": "SMTP 用户名", "Username": "用户名", "Encryption": "加密", "ssl": "SSL", "Select Encryption": "选择加密", "Terms & Services Settings": "条款和服务设置", "Default TOS": "默认服务条款", "Use your own TOS": "使用您自己的服务条款", "Use your own TOS URL": "使用您自己的服务条款网址", "https://example.com/tos": "https://example.com/tos", "Enter the URL of your own Terms & Services page and make sure to add https:// in url": "请输入您自己的服务条款页面的 URL，并确保在 URL 中添加 https://", "Use your own Privacy Policy URL": "使用您自己的隐私政策网址", "https://example.com/privacy-policy": "隐私政策", "Enter the URL of your own Privacy Policy page and make sure to add https:// in url": "输入您自己的隐私政策页面的 URL，并确保在 URL 中添加 https://", "All Clients": "所有客户", "Client Name": "客户名称", "Press / to search": "按 / 搜索", "You don’t have any Product yet": "您还没有任何产品", "Client Details": "客户详情", "Client Information": "客户信息", "Edit Information": "编辑信息", "Billing Address": "账单地址", "Account Status": "账户状态", "Payment Information": "付款信息", "Here you can find all cards added by your clients": "在此您可以查看客户添加的所有卡片", "No payment methods found.": "未找到付款方式。", "Invoices": "发票", "Here you can check all your client's previous payments": "在此查看您客户的所有历史付款", "Edit Client Information": "编辑客户信息", "Experience Effortless Hosting With Powerful Features": "体验强大功能的轻松托管", "Enjoy the lightning performance backed by powerful features and experience hassle-free hosting": "享受强大功能支持的闪电性能，体验无忧托管", "Effortless Server Operations": "轻松的服务器操作", "Manage your server effortlessly with our intuitive dashboard. Enjoy high performance and reliability without worrying about downtime, regardless of your technical skill level.": "使用我们直观的仪表板轻松管理您的服务器。无论您的技术水平如何，都能享受高性能和可靠性，无需担心停机。", "Easy Website Management": "简易网站管理", "We offer a comprehensive platform for managing WordPress sites with ease. With our intuitive dashboard and advanced features, you can manage your sites without any hassle.": "我们提供一个全面的平台，轻松管理WordPress网站。通过我们的直观仪表板和高级功能，您可以毫不费力地管理您的网站。", "Powerful Security Measures": "强大的安全措施", "Our advanced security features ensure the safety of your data through advanced protective measures. Also, automated regular updates keep everything safe from threats and remains protected.": "我们的高级安全功能通过先进的保护措施确保您的数据安全。此外，自动定期更新可防御威胁并保持保护。", "Real-time Resources Monitoring": "实时资源监控", "Monitoring your site and server is essential for ensuring optimal performance and reliability. With real-time measures, you can quickly identify issues provide a seamless experience to your customers.": "监控您的网站和服务器对于确保最佳性能和可靠性至关重要。通过实时监测，您可以快速识别问题，为客户提供无缝体验。", "Transparent & Flexible Pricing for Everyone": "透明且灵活的定价，适合所有人", "Explore our range of plans designed to meet every needs of every web creator": "探索我们为每位网站创作者量身打造的多种方案。", "Includes": "包含", "All rights reserved.": "版权所有。", "Terms of Service": "服务条款", "Terms & Services": "服务条款", "Just One Click Away from Completing Your Order": "只需点击一下即可完成订单", "Already have an account?": "已有账户？", "Sign In": "登录", "Integrate any cloud provider to manage server and sites in xCloud": "集成任何云提供商以在 xCloud 中管理服务器和站点", "Choose Your Plan": "选择您的计划", "Cost": "费用", "Applied Coupon": "已使用优惠券", "Purchase Limit Reached!": "购买限制已达！", "Only one purchase remaining!": "仅剩一次购买机会！", "Split Pay": "分期付款", "Ready to confirm your purchase?": "准备确认购买吗？", "By clicking 'Confirm', a charge of": "点击“确认”后，将收取费用", "will be applied to your saved credit card.": "将应用于您保存的信用卡。", "Please switch to your team to checkout": "请切换到您的团队进行结账", "You are currently in the Playground Team. You can't checkout from here": "您当前在 Playground 团队。无法在此结账。", "Total Purchases": "总购买量", "Only": "仅", "purchase remaining": "购买剩余部分", "Database Root Password": "数据库根密码", "Do you want to turn off Auto Backups?": "您要关闭自动备份吗？", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks": "禁用自动备份意味着失去便捷的数据恢复、备份移动性和灾难恢复功能；如果您了解风险，请点击继续。", "Create Server": "创建服务器", "Report an Issue": "报告问题", "Something went wrong": "出现错误", "Please try again or report an issue to support": "请重试或向支持部门报告问题", "Retry": "重试", "You may exit this window and navigate away": "您可以关闭此窗口并离开", "Check Connection": "检查连接", "Add Site": "添加网站", "Install Stack": "安装 Stack", "SSH User": "SSH 用户", "SSH Key": "SSH 密钥", "Log": "日志", "Install stack on the server": "在服务器上安装堆栈", "It will install these following services on the server": "它将在服务器上安装以下服务", "PHP 7 .4": "PHP 7.4", "MySQL 8 .0": "MySQL 8.0", "Redis": "Redis", "and more. .": "更多...", "Install": "安装", "Log in": "登录", "Register": "注册", "White Label": "白标", "Playground": "游乐场", "Email not verified": "电子邮件未验证", "Please check your email to verify your account. Verify your email": "请检查您的电子邮件以验证您的账户。验证您的电子邮件", "Do you want to switch from": "您要切换自", "You can create free demo sites under Playground Team which will be removed after 24 hours. From Team Settings page you can switch back to your default team again.": "您可以在 Playground 团队下创建免费的演示站点，这些站点将在 24 小时后删除。您可以在团队设置页面切换回默认团队。", "Info": "信息", "You can switch to any team you belong by clicking on the team name in the top right header.": "您可以通过点击右上角标题中的团队名称来切换到您所属的任何团队。", "Switch to": "切换到", "Yes": "是的", "No": "否", "Try xCloud Playground": "试用 xCloud Playground", "Do you want to Switch your team?": "您要切换团队吗？", "Please accept or check your email to switch to": "请接受或查看您的电子邮件以切换到", "team. You can also visit": "团队。您还可以访问", "to manage your teams.": "管理您的团队", "Please accept or check your email to switch to your team. You can also visit": "请接受或检查您的电子邮件以切换到您的团队。您也可以访问", "My Profile": "我的资料", "Support": "支持", "Documentation": "文档", "Affiliates": "联盟成员", "Admin Panel": "管理面板", "Reports": "报告", "Horizon": "地平线", "Telescope": "望远镜", "Vapor UI": "蒸汽界面", "Documents": "文档", "Find Servers or Sites": "查找服务器或站点", "Search Results": "搜索结果", "of": "的", "Check our ": "查看我们的", " to get free hosting for 6 months with new Vultr signups.": "新用户注册 Vultr 可免费获得 6 个月主机服务。", " to get a quick start.": "快速开始", "Check out our ": "查看我们的", "Quick Start.": "快速开始", "No vulnerabilities were found on your sites. To setup vulnerability scanner please check this": "您的网站未发现任何漏洞。要设置漏洞扫描器，请查看此内容。", "Hey there! You have no ": "你好！您没有", " yet.": "尚未。", "Quick Start Documentation": "快速入门文档", "This feature is not available in Playground.": "此功能在 Playground 中不可用。", "No custom Nginx configurations found!": "未找到自定义 Nginx 配置！", "Hey there! You have no": "你好！您没有", "plugins.": "插件", "themes.": "主题", "to claim": "领取", "$20 free credits": "20美元免费额度", "if you are a new user on Hetzner.": "如果您是 <PERSON><PERSON><PERSON> 的新用户。", " to claim ": "领取", "Read our ": "阅读我们的", " on Heztner API integration.": "Hetzner API 集成", "Choose AWS Services": "选择 AWS 服务", "Select one or more AWS services for which you want to use this credentials.": "选择一个或多个您希望使用此凭证的 AWS 服务。", "Verify": "验证", "$300 free credits": "$300 免费额度", "if you are a new user on Google Cloud Platform.": "如果您是 Google Cloud Platform 的新用户。", "Back": "返回", "$100 free credits": "$100 免费额度", "if you are a new user on Linode.": "如果您是 Linode 的新用户。", "You can connect a fresh": "您可以连接新的", "Ubuntu 24.04 LTS x64": "Ubuntu 24.04 LTS x64", "server from any provider if you have root access.": "如果您有 root 访问权限，可以使用任何提供商的服务器。", "$200 free credits": "200美元免费额度", "if you are a new user on DigitalOcean.": "如果您是 DigitalOcean 的新用户。", "to collect your API key. Read our": "收集您的 API 密钥。阅读我们的", "and Use": "使用", "XCLOUD": "云端X", "coupon code to claim": "领取优惠码", "$100": "$100", "free credits on new vultr singups for 180 days.": "新用户注册Vultr可享180天免费额度。", "Database Info": "数据库信息", "Refreshing...": "正在刷新...", "By default you can use": "默认情况下，您可以使用", "user. But if you want to do it for specific site enter the specific site user name here.": "用户。如果您想为特定网站执行此操作，请在此处输入特定网站的用户名。", "Update PHP Configuration": "更新 PHP 配置", "Default PHP Version": "默认 PHP 版本", "Default Server PHP Version": "默认服务器 PHP 版本", "This will set the default PHP version for the CLI and for new site installations. However, it won't affect the PHP versions of any existing sites, and the default version for new sites can still be changed during installation.": "这将设置CLI和新站点安装的默认PHP版本。但不会影响任何现有站点的PHP版本，并且新站点的默认版本在安装期间仍可更改。", "Run Custom Command": "运行自定义命令", "Most Recent Commands": "最近命令", "This command will be executed on the server. Please make sure you are running the correct command. Running incorrect commands can break your server.": "此命令将在服务器上执行。请确保您运行的是正确的命令。运行错误的命令可能会导致服务器故障。", "If you want to do it for specific site enter the specific site user name here.": "如果要针对特定网站操作，请在此输入特定网站的用户名。", "Run Command": "运行命令", "Run New Command": "运行新命令", "Server Health": "服务器健康状况", "Filter": "筛选器", "Event": "事件", "Date & Time": "日期和时间", "Firewall Management": "防火墙管理", "Vulnerability Scan": "漏洞扫描", "Your server is set to automatically update security patches. A reboot is required to complete the updates. You can configure the server to reboot automatically at a preferred time or choose to do it manually.": "您的服务器已设置为自动更新安全补丁。需要重启以完成更新。您可以配置服务器在首选时间自动重启，或选择手动重启。", "Provider Backup Setting": "提供商备份设置", "Information": "信息", "Notes": "笔记", "Connection Settings": "连接设置", "Server Settings": "服务器设置", "Update Timezone": "更新时区", "Magic Login Settings": "魔法登录设置", "Time Zone": "时区", "Turn off Indexing": "关闭索引功能", "Turning off this setting will prevent search engines from indexing your staging site.": "关闭此设置将阻止搜索引擎索引您的临时站点。", "Search Engine Visibility": "搜索引擎可见性", "Discourage search engines from indexing this site": "阻止搜索引擎索引此网站", "It is up to search engines to honor this request.": "是否遵循此请求取决于搜索引擎。", " offers a temporary test domain that allows you to quickly deploy your site. ": "提供临时测试域名，帮助您快速部署网站。", "This temporary domain enables you to share your work in progress with teammates or clients for review ": "此临时域名可让您与团队成员或客户共享您的进行中工作以供审阅。", "and input before you finalize and launch it with your own custom domain for public access.": "在最终确定并使用您自己的自定义域名上线之前，请输入内容以供公开访问。", "HTTPS Is Enabled": "已启用 HTTPS", "Do You Want To Enable HTTPS?": "是否启用HTTPS？", "Please make sure that the database connection configuration is correct and if you are creating a new database you may want to store the information in a secure place. Storing the wp-config.php file in Git is not recommended and if the wp-config.php file is absent from your Git repository, ": "请确保数据库连接配置正确。如果您正在创建新数据库，建议将信息存储在安全的地方。不建议将 wp-config.php 文件存储在 Git 中，如果您的 Git 仓库中没有 wp-config.php 文件，", " will automatically generate it from wp-config-sample.php, adding database credentials. Additional credentials can be added later on the WP Config page in the ": "将自动从 wp-config-sample.php 生成，并添加数据库凭据。稍后可以在 WP 配置页面中添加其他凭据。", " site dashboard. If wp-config.php is already in the repository, ": "站点仪表板。如果 wp-config.php 已经在存储库中，", " won't make any changes.": "不会进行任何更改。", "Database Management": "数据库管理", "Your source server must be": "您的源服务器必须是", "or": "或", "OLS": "OLS", "server with": "服务器带有", "Ubuntu 20.04 or 22.04 LTS x64": "Ubuntu 20.04 或 22.04 LTS x64", "and should have": "应具有", "access.": "访问", "month": "月", "Email Address": "电子邮箱地址", "By default, emails are sent from": "默认情况下，电子邮件发送自", "To send emails from your domain, enter your custom SMTP credentials (e.g., Mailgun, Elastic Email)": "要从您的域发送电子邮件，请输入您的自定义 SMTP 凭据（例如，Mailgun、Elastic Email）。", "Two Factor Authentication": "双重身份验证", "You have enabled two factor authentication.": "您已启用双重身份验证。", "Finish enabling two factor authentication.": "完成启用双重身份验证。", "You have not enabled two factor authentication.": "您尚未启用双重验证。", "Enable": "启用", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application.": "要完成启用双重身份验证，请使用手机上的身份验证应用扫描以下二维码。", "Two factor authentication is now enabled.": "双重身份验证现已启用。", "Enter the text code below instead if you can't use the barcode.": "如果无法使用条形码，请输入下面的文本代码。", "Or, Scan the": "或者，扫描", "QR provided": "提供的二维码", "with your phone's two-factor authentication app.": "使用您手机的双因素认证应用程序。", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "将这些恢复代码存储在安全的密码管理器中。如果您的双因素认证设备丢失，它们可以用于恢复账户访问权限。", "Setup Key": "设置密钥", "Regenerate Recovery Codes": "重新生成恢复代码", "Show Recovery Codes": "显示恢复代码", "Create SSH Key": "创建 SSH 密钥", "Create A New SSH Key": "创建新的 SSH 密钥", "Verify & Save for": "验证并保存", "Buy & Add": "购买和添加", "Make sure you have active billing plan to use this feature.": "请确保您有有效的计费计划以使用此功能。", "This label will be used to identify this SMTP provider in": "此标签将用于识别此 SMTP 提供商", "Optional if you're using global API key of Mailgun": "如果您使用的是 Mailgun 的全局 API 密钥，则为可选", ". i.e. Mailgun, Sendgrid, etc.": "邮件服务提供商，例如 Mailgun、Sendgrid 等。", "This label will be used to identify this SMTP provider in xCloud. i.e. Mailgun, Sendgrid, etc.": "此标签将用于在 xCloud 中识别此 SMTP 提供商，例如 Mailgun、Sendgrid 等。", "Authorize on WhatsApp": "在 WhatsApp 上授权", "Authorize on": "授权开启", "Please go to Telegram and start talking with": "请前往 Telegram 并开始与", "To authenticate your chat, send this command to xCloud Notification Bot.": "要验证您的聊天，请将此命令发送给 xCloud 通知机器人。", "Due Date": "截止日期", "Recent Events": "最近事件", "Currently No Event Available": "当前无可用活动", "Mark all as read": "全部标记为已读", "No Notifications Available!": "暂无通知！", "provides": "提供", "Environment to do all the development to your site with a temporary domain.": "用于在临时域名下进行网站开发的环境。", "When you are ready, simply select the GO LIVE option and add your own domain to make the site available to your user/visitor.": "准备好后，只需选择“上线”选项，并添加您自己的域名，使网站对用户/访客可用。", "Demo Site Management": "演示站点管理", "Verifying DNS..": "正在验证 DNS...", "Your staging SSL is being managed by": "您的暂存 SSL 由以下机构管理", "Your SSL is being managed by Cloudflare.": "您的 SSL 由 Cloudflare 管理。", "If you turn on this option then": "如果您开启此选项，则", "will automatically enable NGINX to directly serve previously": "将自动启用 NGINX 直接提供先前的服务", "cached files without calling WordPress or any PHP. It also adds headers to cached CSS, JS, and images via": "缓存文件无需调用 WordPress 或任何 PHP。它还通过添加标头到缓存的 CSS、JS 和图像。", "the browser cache. As a result, your website will be much faster. If someone visits your website again,": "浏览器缓存。因此，您的网站将更快。如果有人再次访问您的网站，", "their browser can load the cached files directly from their own computer instead of making a request to": "他们的浏览器可以直接从自己的计算机加载缓存文件，而无需请求", "your web server. This reduces the number of requests to your server and further speeds up the loading of your website.": "您的网络服务器。这减少了对服务器的请求次数，并进一步加快了网站的加载速度。", "You can read more from our documentation": "您可以阅读我们的文档以获取更多信息", "Saving Changes...": "正在保存更改…", "Make sure to add your SSH key on the SSH Authentication section.": "请在 SSH 认证部分添加您的 SSH 密钥。", "Enter the configuration file name": "输入配置文件名", "Save Config": "保存配置", "Create Custom Nginx Configuration for": "创建自定义 Nginx 配置用于", "Select Template": "选择模板", "IP Whitelist/Blacklist": "IP 白名单/黑名单", "WP-Cron and xCloud-Cron": "WP-Cron 和 xCloud-Cron", "Update PHP Settings": "更新 PHP 设置", "All existing files and data on this site will be deleted": "此站点上的所有现有文件和数据将被删除", "Create a new backup before restoring?": "在恢复之前创建新备份？", "Restore": "恢复", "Take Backup": "备份", "Full Backup": "完整备份", "Are you sure you want to take a full backup? This will create a complete backup of all your data. Future incremental backups will be based on this full backup.": "您确定要进行完整备份吗？这将创建您所有数据的完整备份。未来的增量备份将基于此完整备份。", "Incremental Backup": "增量备份", "This will add to your previous backups by only saving the changes since the last backup.": "这将通过仅保存自上次备份以来的更改来添加到您之前的备份中。", "Site List": "站点列表", "Management": "管理", "Settings": "设置", "Custom Cron Jobs": "自定义 Cron 任务", "PHP Configuration": "PHP 配置", "Commands": "命令", "Monitoring": "监控", "Logs": "日志", "Events": "事件", "Firewall management": "防火墙管理", "Vulnerability Settings": "漏洞设置", "Full Server migration": "完整服务器迁移", "Metadata": "元数据", "Site Overview": "网站概览", "Caching": "缓存", "Email Configuration": "电子邮件配置", "Previous Backups": "以前的备份", "Backup Settings": "备份设置", "Site Monitoring": "站点监控", "Access Data": "访问数据", "SSH/sFTP": "SSH/sFTP", "Tools": "工具", "Nginx and Security": "Nginx与安全性", "Nginx Customization": "Nginx 自定义", "IP Management": "知识产权管理", "Site Settings": "站点设置", "User Profile": "用户资料", "Browser Sessions": "浏览器会话", "Integrations": "集成", "Cloudflare": "Cloudflare", "Notification": "通知", "Billing": "账单", "Manual Invoices": "手动发票", "Whitelabel": "白标", "All Events": "所有活动", "Payment": "付款", "Brand": "品牌", "SMTP": "SMTP", "Create Products": "创建产品", "Setup your brand to get started": "设置您的品牌以开始使用", "Setup your payment method": "设置您的支付方式", "Create customized plans": "创建自定义计划", "Setup your domain easily": "轻松设置您的域名", "Search sites...": "搜索网站...", "Create Account": "创建账户", "Password must be at least  8 characters and should contain uppercase, number and special character": "密码必须至少包含8个字符，并应包含大写字母、数字和特殊字符。", "Filter by Date": "按日期筛选", "Name your blueprint": "命名您的蓝图", "Search": "搜索", "Are You Sure You Want To Delete Server": "您确定要删除服务器吗？", "to confirm": "确认", "Are You Sure You Want To Delete Site": "您确定要删除站点吗？", "Edit Server Provider": "编辑服务器提供商", "Add Server Provider": "添加服务器提供商", "check connection": "检查连接", "Clone Blueprint": "克隆蓝图", "Delete Blueprint": "删除蓝图", "Are you sure you want to delete this blueprint?": "您确定要删除此蓝图吗？", "Are you sure you want to clone this blueprint?": "您确定要克隆此蓝图吗？", "Clone": "克隆", "Set as Default Blueprint": "设为默认蓝图", "Are you sure you want to set this blueprint as default?": "您确定要将此蓝图设为默认吗？", "Are you sure?": "您确定吗？", "You won't be able to revert this!": "无法撤销此操作！", "Yes, Remove!": "是的，移除！", "Are you sure you want to update WordPress?": "您确定要更新 WordPress 吗？", "Update WordPress Core": "更新 WordPress 核心", "Are you sure you want to active this theme?": "您确定要激活此主题吗？", "Activate Theme": "启用主题", "Are you sure you want to update those plugin?": "您确定要更新这些插件吗？", "You have selected": "您已选择", "plugins": "插件", "Are you sure you want to update those plugins?": "您确定要更新这些插件吗？", "Are you sure you want to update these themes?": "您确定要更新这些主题吗？", "themes": "主题", "Do you want to deactivate Adminer?": "您要停用 Adminer 吗？", "Do you want to activate Adminer?": "您要激活 Adminer 吗？", "We recommend deactivating it when not required.": "建议在不需要时停用。", "Yes, restore it!": "是的，恢复它！", "Are you sure you want to restore this server?": "您确定要恢复此服务器吗？", "Are you sure you want to delete this card?": "您确定要删除此卡片吗？", "You can add another card.": "您可以添加另一张卡片。", "Yes, log out!": "是的，退出登录！", "Are you sure you want to delete this integration?": "您确定要删除此集成吗？", "You want to disconnect": "您要断开连接", "Yes, Disconnect!": "是，断开连接！", "You want to reconnect": "您想重新连接", "Yes, Reconnect!": "是的，重新连接！", "Yes, disable it!": "是，禁用它！", "Are you sure you want to disable HTTPS?": "您确定要禁用 HTTPS 吗？", "Yes, Leave Team!": "是的，退出团队！", "Edit Storage Provider": "编辑存储提供商", "Add Storage Provider": "添加存储提供商", "Yes, leave team!": "是，退出团队！", "This will remove the cron job from the server.": "这将从服务器中移除定时任务。", "Are you sure you want to disable this firewall rule?": "您确定要禁用此防火墙规则吗？", "You can enable it later.": "您可以稍后启用。", "Yes, Disable": "是，禁用", "Are you sure you want to enable this firewall rule?": "您确定要启用此防火墙规则吗？", "You can disable it later.": "您可以稍后禁用。", "Yes, Enable": "是，启用", "Are you sure you want to delete this firewall rule?": "您确定要删除此防火墙规则吗？", "Deleting firewall rule will remove it permanently.": "删除防火墙规则将永久移除。", "Yes, Delete": "是，删除", "Are you sure you want to unban": "您确定要解除封禁吗？", "This will remove the IP address from the banned list.": "这将从禁用列表中移除该IP地址。", "Yes, Unban": "是，解除封禁", "This will remove the sudo user from the server.": "这将从服务器中移除 sudo 用户。", "Authentication will be disabled for this site": "此站点的身份验证将被禁用", "Yes, Remove": "是，移除", "Are you sure you want to delete this backup?": "您确定要删除此备份吗？", "This action cannot be undone.": "此操作无法撤销。", "Are you sure you want to remove this failed backup?": "您确定要删除此失败的备份吗？", "Do you want to deactivate Tiny File Manager?": "您要停用 Tiny File Manager 吗？", "Do you want to activate Tiny File Manager?": "您要激活 Tiny 文件管理器吗？", "Are you sure you want to disable": "您确定要禁用吗？", "caching?": "缓存？", "Are you sure you want to disable redis object caching?": "您确定要禁用 Redis 对象缓存吗？", "Yes, switch it!": "是的，切换！", "Are you sure you want to switch to": "您确定要切换到", "plugin?": "插件？", "Are you sure you want to disable full page caching?": "您确定要禁用整页缓存吗？", "Yes, Remove it!": "是的，删除！", "Are you sure you want to remove this IP address?": "您确定要删除此IP地址吗？", "Are you sure you want to disable LiteSpeed Cache?": "您确定要禁用 LiteSpeed 缓存吗？", "Are you sure you want to delete this Nginx Configuration?": "您确定要删除此 Nginx 配置吗？", "It will be permanently deleted.": "将被永久删除。", "Enable Nginx File Regeneration": "启用 Nginx 文件再生成", "This will prevent xCloud from regenerating nginx file on any changes made to the site. You will have to manually regenerate the nginx file.": "这将防止 xCloud 在对站点进行任何更改时重新生成 nginx 文件。您需要手动重新生成 nginx 文件。", "This will allow xCloud to regenerate nginx file on any changes made to the site. You will not have to manually regenerate the nginx file.": "这将允许 xCloud 在网站进行任何更改时重新生成 nginx 文件。您无需手动重新生成 nginx 文件。", "Enable Site": "启用站点", "Disabling the site will make it inaccessible. Are you sure you want to disable it?": "停用该网站将使其无法访问。您确定要停用吗？", "Enabling the site will make it accessible. Are you sure you want to enable it?": "启用该站点将使其可访问。您确定要启用吗？", "This action will run the rescue process on the site. Are you sure you want to run it?": "此操作将对网站运行救援过程。您确定要运行吗？", "Yes, Run Now": "是，立即运行", "Are you sure you want to update the plugin?": "您确定要更新插件吗？", "Plugin will be updated to": "插件将更新为", "Are you sure you want to not ignore this vulnerability?": "您确定不忽略此漏洞吗？", "Are you sure you want to ignore this vulnerability?": "您确定要忽略此漏洞吗？", "Are you sure you want to update the theme?": "您确定要更新主题吗？", "You have select": "您已选择", "theme": "主题", "Are You Sure You Want To Delete this SSH Key:": "您确定要删除此 SSH 密钥吗？", "Generate Invoice": "生成发票", "Add A New Payment Method": "添加新付款方式", "Log Out Other Browser Sessions": "注销其他浏览器会话", "Log Out": "退出登录", "Log Out of All Sessions": "退出所有会话", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "请输入您的密码以确认您希望退出所有设备上的其他浏览器会话。", "You won't be able to revert this to your current device.": "无法将此操作恢复到您当前的设备。", "Are you sure you want to delete": "您确定要删除吗？", "You cannot delete this team": "无法删除此团队", "You cannot delete your personal team": "您无法删除您的个人团队", "You cannot delete your current team": "无法删除您当前的团队", "Admin Username": "管理员用户名", "SIZE": "大小", "REGION": "区域", "UBUNTU": "Ubuntu", "Deleting..": "删除中…", "Delete SSH Key": "删除 SSH 密钥", "Add a new database to your server": "将新数据库添加到您的服务器", "Add a new user to your database": "将新用户添加到数据库", "Edit database user:": "编辑数据库用户：", "Are You Sure You Want To Delete this database:": "您确定要删除此数据库吗？", "Are You Sure You Want To Delete this database user:": "您确定要删除此数据库用户吗？", "Cron Job Output": "计划任务输出", "Add new firewall rule for": "为...添加新的防火墙规则", "Ban an IP Address on": "禁止 IP 地址", "Reboot Time": "重启时间", "Your server is set to automatically reboot at": "您的服务器设置为自动重启时间：", "You can change the reboot time if you'd like.": "您可以更改重启时间。", "CPU Usage": "CPU 使用率", "Reboot": "重启", "Are You Sure You Want To": "您确定要", "Service?": "服务？", "Are You Sure You Want To Restore Backup For The Server": "您确定要恢复服务器的备份吗？", "Are You Sure You Want To Resize Server": "您确定要调整服务器大小吗？", "Resize": "调整大小", "Initiating Resizing...": "正在调整大小...", "Manage Database": "管理数据库", "Last Updated": "最后更新", "Deactivate": "停用", "Last Checked": "上次检查", "Current Version": "当前版本", "RAM Usages": "内存使用情况", "Disk Usages": "磁盘使用情况", "Choose SSH Keys": "选择 SSH 密钥", "No SSH Keys found.": "未找到 SSH 密钥。", "Search SSH Keys": "搜索 SSH 密钥", "Are You Sure You Want To Disable Site": "您确定要禁用网站吗？", "Add Your Own Database": "添加您自己的数据库", "Please provide your new database information": "请提供您的新数据库信息", "Cron Interval for Server": "服务器的Cron间隔", "Add this public key in your Git repository as deploy key. This is necessary to enable": "将此公钥添加到您的 Git 仓库中作为部署密钥。这是启用所需的步骤。", "to clone the repository, ensuring a secure and authorized access for automated processes such as cloning.": "克隆存储库，确保自动化流程（如克隆）的安全和授权访问。", "Click On The Plugin Download Link": "点击插件下载链接", "Upload the zipped plugin file to WordPress under 'Add New' in the 'Plugins' tab": "在“插件”选项卡下的“添加新”中上传压缩的插件文件到WordPress", "Click 'Activate' to install the plugin": "点击“激活”以安装插件", "Copy the authentication token and paste it into the plugin page to complete the setup": "复制认证令牌并将其粘贴到插件页面以完成设置。", "Upload The Plugin Zipped File On WordPress": "在 WordPress 上上传插件压缩文件", "Click On The ‘Activate’ Button To Install Plugin": "点击“激活”按钮安装插件", "Copy The Authentication Token And Paste It Into The Plugin Page": "复制认证令牌并粘贴到插件页面", "Export Zipped File From Your Existing Site": "从您现有的网站导出压缩文件", "Upload The Exported Zip or Tar File Here": "在此处上传导出的 Zip 或 Tar 文件", "You must have root access to perform Full Server Migration": "要执行完整服务器迁移，您必须拥有 root 访问权限。", "Migrate multiple cPanel sites from shared hosting to xCloud easily for faster, scalable and secure hosting.": "轻松将多个 cPanel 站点从共享主机迁移到 xCloud，实现更快速、可扩展和安全的托管。", "If the fetched domain is incorrect, you can edit it here, the domain name needs to be accurate for successful migration": "如果获取的域名不正确，您可以在此编辑。域名需要准确以确保迁移成功。", "Total migrated": "迁移总数", "Verifying ...": "正在验证...", "Choose Web Server": "选择网络服务器", "Updating Backup Schedule...": "更新备份计划中...", "Update Backup Schedule": "更新备份计划", "Set Schedule": "设置日程", "Are You Sure?": "您确定吗？", "Restoring...": "正在恢复...", "Enabling Backup...": "启用备份中...", "Disabling Backup...": "正在禁用备份…", "Backup?": "备份？", "Provider Backup Schedule": "供应商备份计划", "Update Schedule": "更新计划", "Select Day of Week": "选择星期几", "Select Day of Month": "选择日期", "View all": "查看全部", "You are now managing": "您现在正在管理", "site on": "站点开启", "server.": "服务器", "You can not use your own domain with xCloud Free Email Service..": "您不能在 xCloud 免费电子邮件服务中使用自己的域名。", "For example, you can send <NAME_EMAIL> or <EMAIL>. It is up to you!": "例如，您可以使用 <EMAIL> 或 <EMAIL> 发送电子邮件。由您决定！", "Are you sure you want to restore this backup?": "您确定要恢复此备份吗？", "X-Frame-Options": "X-Frame-Options", "SAMEORIGIN": "同源", "Update Tags": "更新标签", "Zone": "区域", "Choose Zones": "选择区域", "Choose Regions": "选择地区", "Choose Sizes": "选择尺寸", "site": "站点", "No Vulnerabilities Found": "未发现漏洞", "Vulnerability Scan Not Enabled": "未启用漏洞扫描", "Updates Available": "可用更新", "sudo user": "超级用户", "IP": "IP", "Package": "包裹", "Can't find what you're looking for?": "找不到您要找的内容？", "Contact Support": "联系支持", "Nginx Options": "Nginx 选项", "OpenLiteSpeed Options": "OpenLiteSpeed 选项", "Link Google Drive Account": "链接 Google Drive 账户", "Upload a zipped file of your existing WordPress website (max size: 500 MB)": "上传您的现有WordPress网站的压缩文件（最大大小：500 MB）", "Recreate Site from Backup": "从备份重建站点", "Restore site backup from local or remote storage easily to create a site": "轻松从本地或远程存储恢复站点备份以创建站点", "Language Settings": "语言设置", "Copyright": "版权", "Taking Payment...": "处理中付款...", "Bill": "账单", "Pay": "支付", "Additional Information": "附加信息", "Name of Invoice": "发票名称", "Notification Language Settings": "通知语言设置", "Turning on this setting will prevent search engines from indexing your staging site.": "启用此设置将阻止搜索引擎索引您的暂存站点。", "For example, you can send emails from": "例如，您可以从发送电子邮件", "It is up to you!": "由你决定！", "Adminer and File Manager won't work with 7G/8G firewall. We recommend SFTP if you're familiar with it.": "Adminer 和文件管理器无法与 7G/8G 防火墙一起使用。如果您熟悉 SFTP，我们推荐使用它。", "Demo Site Setup": "演示站点设置", "System Cron Jobs": "系统定时任务", "others": "其他", "Items": "项目", "List of Items": "项目列表", "Integrate New Item": "集成新项目", "Add New Item": "添加新项目", "License": "许可", "Are you sure you want to delete?": "您确定要删除吗？", "Item deleted successfully": "项目已成功删除", "Failed to delete Item": "删除项目失败", "Select Integrated Plugin": "选择集成插件", "Boost your WordPress site’s performance with Object Cache Pro by caching frequently accessed data in Redis. This reduces database queries, enhances speed, and is especially beneficial for dynamic, content-heavy websites.": "通过在 Redis 中缓存常用数据，使用 Object Cache Pro 提升您的 WordPress 网站性能。这可以减少数据库查询，提高速度，特别适合动态、内容丰富的网站。", "Debug Mode": "调试模式", "Object Cache Pro": "对象缓存专业版", "Select Plugin": "选择插件", "Update Item": "更新项目", "Add Item": "添加项目", "Enter your license label": "输入您的许可证标签", "Enter your license key": "输入您的许可证密钥", "Edit Item": "编辑项目", "Access File Manager": "访问文件管理器", "Always Enabled": "始终启用", "Keep the File Manager accessible at all times.": "始终保持文件管理器可用。", "Disable File Manager": "禁用文件管理器", "Specify how long the File Manager should remain enabled before it’s automatically disabled.": "请指定文件管理器在自动禁用前应保持启用的时间。", "Set Auto Disable Duration": "设置自动禁用时长", "Choose to keep the File Manager active for always or schedule it to disable after a certain time.": "选择始终保持文件管理器激活，或设置在特定时间后禁用。", "Access Adminer": "访问 Adminer", "Keep the Adminer accessible at all times.": "确保 Adminer 始终可访问。", "Disable Adminer": "禁用 Adminer", "Specify how long the Adminer should remain enabled before it’s automatically disabled.": "指定 Adminer 启用的时长，之后将自动禁用。", "Set Auto Deactivate Duration": "设置自动停用时长", "Choose to keep the Adminer active for always or schedule it to disable after a certain time.": "选择始终保持 Adminer 活跃，或设定时间后自动停用。", "Customization": "自定义设置", "The File Manager will be disable within": "文件管理器将在...内禁用", "The Adminer will be disable within": "管理员将在以下时间内被禁用", "Login": "登录", "Login Options": "登录选项", "Log in using xCloud email account or the first admin account.": "使用 xCloud 邮箱账户或第一个管理员账户登录。", "Use a different email and, if your login URL is custom, enter it here to log in.": "请使用不同的邮箱，如果您的登录网址是自定义的，请在此输入以登录。", "Custom": "自定义", "Something went wrong, please try again.": "出现错误，请重试。", "Use a different email or username to log in.": "使用其他邮箱或用户名登录。", "Manually Upload Website": "手动上传网站", "Upload a zipped file of your existing website": "上传您现有网站的压缩文件", "Upload a zipped file of your existing website (max size: 500 MB)": "上传您现有网站的压缩文件（最大大小：500 MB）", "Custom PHP": "自定义 PHP", "laravel": "<PERSON><PERSON>", "Laravel": "<PERSON><PERSON>", "Coming": "即将推出", "Update Web Root": "更新网站根目录", "Web Root": "网站根目录", "Coming Soon": "即将推出", "Enter your email": "输入您的电子邮件", "Haven’t registered for your free account yet?": "还没有注册您的免费账户吗？", "Sign up now": "立即注册", "Glad To Have You Back!": "欢迎回来！", "Get Started For Free": "免费开始使用", "Access 1 server and 10 sites at $0 cost. No credit card required.": "访问 1 台服务器和 10 个站点，完全免费。无需信用卡。", "Enter your name": "输入您的姓名", "Please choose a strong password. Use at least 8 characters, including a mix of letters, numbers and symbols.": "请选择一个强密码。请使用至少8个字符，包括字母、数字和符号的组合。", "Retype Password": "请重新输入密码", "The passwords you entered do not match. Please ensure both fields contain the same password.": "您输入的密码不匹配。请确保两个字段包含相同的密码。", "By creating an account, you agree to our": "创建账户即表示您同意我们的", "Create Your Account": "创建账户", "Enter your credentials to sign up.": "输入您的凭证以注册。", "We have discovered a security vulnerability in the": "我们发现了一个安全漏洞", "that you are using on the following website:": "您正在使用以下网站：", "View Vulnerabilities": "查看漏洞", "Immediate Action Required: We found a security vulnerability in your Website": "立即采取行动：我们在您的网站中发现了安全漏洞", "Community": "社区", "Want To Create A Staging Site For": "要创建临时站点吗？", "A staging site is a secure clone of your live website for testing and development. It lets you experiment with plugins, themes, and changes risk-free before deploying them to live.": "测试站点是您线上网站的安全克隆，用于测试和开发。它允许您在不影响线上网站的情况下，安全地试验插件、主题和更改。", "Test Domain": "测试域", "Create a demo site with our test domains and customize it before going live.": "使用我们的测试域名创建一个演示站点，并在上线前进行自定义。", "Custom Domain": "自定义域名", "Enter your custom domain by simply pointing your domain to the server.": "只需将您的域名指向服务器即可输入自定义域名。", "Deploy Staging Site": "部署暂存站点", "Manage Staging": "管理暂存环境", "Integrity Monitor": "完整性监控", "Scan Now": "立即扫描", "Last scan": "上次扫描", "Items found": "找到的项目", "Message": "消息", "Plugin": "插件", "This website has found some checksum errors. This could be due to a corrupted file or a malicious attack. Please review the files and database of your site.": "本网站发现了一些校验和错误。这可能是由于文件损坏或恶意攻击所致。请检查您网站的文件和数据库。", "Check": "检查", "Security Settings": "安全设置", "AI Bot Blocker": "AI 机器人拦截器", "Disable Nginx Config Regeneration": "禁用 Nginx 配置重新生成", "Disable OpenLiteSpeed Config Regeneration": "禁用 OpenLiteSpeed 配置再生成", "WP Fail2Ban": "WP Fail2Ban", "Block Failed Login Attempts": "阻止登录失败尝试", "Block Common Usernames": "屏蔽常用用户名", "Block User Enumeration": "阻止用户枚举", "Protect Comments": "保护评论", "Block Spam": "拦截垃圾邮件", "Guard Password Resets": "保护密码重置", "Guard Pingbacks": "保护Pingbacks", "Enable OpenLiteSpeed Config Regeneration": "启用 OpenLiteSpeed 配置重生成", "Config file regeneration has been updated successfully": "配置文件重新生成已成功更新", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config": "这将阻止 xCloud 在对站点进行任何更改时重新生成 OpenLiteSpeed 配置。您需要手动重新生成 OpenLiteSpeed 配置。", "This will allow xCloud to regenerate OpenLiteSpeed config on any changes made to the site. You will not have to manually regenerate the OpenLiteSpeed config.": "这将允许 xCloud 在网站进行任何更改时重新生成 OpenLiteSpeed 配置。您无需手动重新生成 OpenLiteSpeed 配置。", "Serve robots.txt from file system": "从文件系统提供 robots.txt", "Failed to load nginx options": "加载 nginx 选项失败", "Failed to update config file regeneration": "配置文件重新生成失败", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config.": "这将防止 xCloud 在对站点进行任何更改时重新生成 OpenLiteSpeed 配置。您需要手动重新生成 OpenLiteSpeed 配置。", "This will prevent xCloud from regenerating nginx config on any changes made to the site. You will have to manually regenerate the nginx config.": "这将防止 xCloud 在对站点进行任何更改时重新生成 nginx 配置。您需要手动重新生成 nginx 配置。", "This will allow xCloud to regenerate nginx config on any changes made to the site. You will not have to manually regenerate the nginx config.": "这将允许 xCloud 在对站点进行任何更改时重新生成 nginx 配置。您无需手动重新生成 nginx 配置。", "Add this URL as a webhook in your Git repository settings to enable automated deployments": "在您的 Git 仓库设置中添加此 URL 作为 webhook，以启用自动部署", "Laravel Debug Log": "Laravel 调试日志", "Environment": "环境", "Update Environment": "更新环境", "Deploy Now": "立即部署", "Deploying...": "正在部署...", "Your Laravel site deployment has been initiated. Please wait while the changes are being deployed.": "您的 Laravel 站点部署已启动。请稍候，正在进行更改部署。", "Failed to deploy Laravel site. Please try again.": "部署 Laravel 站点失败。请重试。", "Add Supervisor Process": "添加监督进程", "Edit Supervisor Process": "编辑主管流程", "Update Supervisor Process": "更新主管进程", "Supervisor Processes": "监控进程", "Check Process Status": "检查进程状态", "Supervisor": "主管", "Process Management": "流程管理", "Background Processes": "后台进程", "Daemon": "守护进程", "Supervisor Process Output": "监控进程输出", "Processes": "进程", "View Logs": "查看日志", "Restart": "重启", "No supervisor processes found": "未找到主管进程", "Delete Supervisor Process": "删除主管流程", "Are you sure you want to delete this supervisor process? This action cannot be undone.": "您确定要删除此监督进程吗？此操作无法撤销。", "This will remove the supervisor process from the server.": "这将从服务器中移除监督进程。", "Running": "运行", "Stopped": "已停止", "Fatal": "致命", "Backoff": "退避", "Starting": "开始", "Stopping": "停止中", "Exited": "已退出", "Unknown": "未知", "Processing": "处理中", "You can run any executable command here. For Python, Node.js, or PHP scripts, specify the full command (e.g., python3 script.py).": "您可以在此运行任何可执行命令。对于 Python、Node.js 或 PHP 脚本，请指定完整命令（例如：python3 script.py）。", "Optional. Specify the working directory for the process.": "可选。指定进程的工作目录。", "By default, you can use root. To run as a different user, enter the username here.": "默认情况下，您可以使用 root。要以其他用户身份运行，请在此输入用户名。", "Number of Processes": "进程数量", "Number of process instances to keep running.": "保留运行的进程实例数量。", "Start Seconds (Optional)": "开始秒数（可选）", "Supervisor will consider the process started after this many seconds.": "主管将在此秒数后视为流程已启动。", "Stop Seconds (Optional)": "停止秒数（可选）", "Supervisor will wait this many seconds before force stopping the process.": "主管将在强制停止进程前等待此秒数。", "Stop Signal (Optional)": "停止信号（可选）", "Supervisor sends this signal to stop the process. Leave empty for default (TERM).": "主管发送此信号以停止进程。留空则使用默认值 (TERM)。", "Laravel Horizon is not installed": "Laravel Horizon 未安装", "Horizon is not detected in your composer.json. To use Horizon, you need to install it first:": "在您的 composer.json 中未检测到 Horizon。要使用 Horizon，您需要先安装它：", "Laravel Application": "Laravel 应用程序", "Enable Debug Mode": "启用调试模式", "When debug mode is enabled, detailed error messages will be displayed. This should be disabled in production.": "启用调试模式时，将显示详细错误信息。在生产环境中应禁用此功能。", "Enable Maintenance Mode": "启用维护模式", "When maintenance mode is enabled, a maintenance screen will be displayed for all requests to your application.": "启用维护模式时，所有对应用程序的请求将显示维护屏幕。", "Application Environment": "应用环境", "Application": "应用程序", "The application environment affects various Laravel behaviors. This setting updates APP_ENV in your .env file": "应用环境会影响 Laravel 的各种行为。此设置会更新 .env 文件中的 APP_ENV。", "Clear Application Cache": "清除应用缓存", "Clears all Laravel caches by running the optimize:clear command.": "通过运行 optimize:clear 命令清除所有 Laravel 缓存。", "Clearing...": "清除中...", "Clear Cache": "清除缓存", "Laravel Horizon": "<PERSON>vel <PERSON>", "Update Process": "更新过程", "Start Horizon": "启动 Horizon", "Not Configured": "未配置", "Horizon is running": "地平线正在运行", "Horizon is not running": "地平线未运行", "Stop": "停止", "Horizon provides a beautiful dashboard and code-driven configuration for your Laravel powered Redis queues.": "Horizon 为您的 Laravel 驱动的 Redis 队列提供美观的仪表板和代码驱动的配置。", "Laravel Scheduler": "Laravel 调度器", "Start Scheduler": "启动计划程序", "Your scheduler is running properly": "您的调度程序运行正常", "Scheduler needs to be configured": "需要配置调度程序", "Scheduler Frequency": "计划频率", "The Laravel scheduler allows you to fluently and expressively define your command schedule within Laravel itself.": "Laravel 调度器允许您在 Laravel 中流畅且直观地定义命令调度。", "Local": "本地", "Testing": "测试", "Every Minute": "每分钟", "Every Five Minutes": "每五分钟", "Every Ten Minutes": "每10分钟", "Every Fifteen Minutes": "每15分钟", "Every Thirty Minutes": "每隔三十分钟", "Hourly": "每小时", "Server is not connected": "服务器未连接", "Failed to load Laravel application status": "无法加载 Laravel 应用程序状态", "Laravel application settings updated successfully": "Laravel 应用设置更新成功", "Failed to update Laravel application settings": "无法更新 Laravel 应用程序设置", "Application cache cleared successfully": "应用缓存清除成功", "Failed to clear application cache": "清除应用缓存失败", "Horizon started successfully": "Horizon 启动成功", "Failed to start Horizon": "启动 Horizon 失败", "Horizon stopped successfully": "地平线已成功停止", "Failed to stop Horizon": "无法停止 Horizon", "Horizon restarted successfully": "地平线已成功重启", "Failed to restart Horizon": "重启 Horizon 失败", "Scheduler setup successfully": "计划程序设置成功", "Failed to setup scheduler": "无法设置调度程序", "Scheduler stopped successfully": "调度器已成功停止", "Failed to stop scheduler": "无法停止调度程序", "Laravel Queue": "<PERSON><PERSON> 队列", "Add Queue Worker": "添加队列工作者", "No queue workers are running": "没有正在运行的队列工作程序", "Connection": "连接", "Queue": "队列", "Timeout": "超时", "Memory": "内存", "Refresh Status": "刷新状态", "Edit Process": "编辑流程", "Restart Process": "重启进程", "View Output": "查看输出", "Queue workers process jobs in the background. They are useful for handling time-consuming tasks like sending emails, processing files, or any other task that should not block the main application.": "队列工作者在后台处理任务。它们适用于处理耗时的任务，如发送电子邮件、处理文件或其他不应阻塞主应用程序的任务。", "Update Queue Worker": "更新队列工作器", "redis": "Redis", "default": "默认", "0": "0", "Maximum Seconds Per Job": "每个任务的最大秒数", "0 = No Timeout": "0 = 无超时", "256": "256", "Maximum Memory (Optional)": "最大内存（可选）", "Memory limit in MB": "内存限制（MB）", "1": "1", "Number of worker processes to run": "工作进程数量", "Queue Worker Output": "队列工作器输出", "Queue worker updated successfully": "队列工作者更新成功", "Queue worker added successfully": "队列工作者添加成功", "Are you sure you want to stop this queue worker?": "您确定要停止此队列工作程序吗？", "This will remove the queue worker process.": "这将移除队列工作进程。", "Yes, Stop": "是，停止", "Queue worker stopped successfully": "队列工作器已成功停止", "Failed to stop queue worker": "无法停止队列工作程序", "Queue worker restarted successfully": "队列工作器已成功重启", "Failed to restart queue worker": "无法重启队列工作程序", "Error loading output. Please try again.": "输出加载错误。请重试。", "Failed to refresh worker status": "刷新工作状态失败", "Deployment has been initiated.": "部署已启动。", "Plugins Found": "已找到插件", "Please configure Cloudflare SSL on this site to enable edge cache.": "请在此站点配置 Cloudflare SSL 以启用边缘缓存。", "Cloudflare Edge Cache is not available for staging sites. Please switch to production with Cloudflare SSL for this site to enable edge cache.": "Cloudflare 边缘缓存不适用于暂存站点。请切换到使用 Cloudflare SSL 的生产环境以启用边缘缓存。", "You need to set up Cloudflare integration first to enable edge cache.": "您需要先设置 Cloudflare 集成以启用边缘缓存。", "Domain is not available on Cloudflare. Please add your domain to Cloudflare first to enable edge cache.": "域名在 Cloudflare 上不可用。请先将您的域名添加到 Cloudflare 以启用边缘缓存。", "Cloudflare Edge Cache has been enabled successfully.": "Cloudflare 边缘缓存已成功启用。", "Cloudflare Edge Cache has been disabled successfully.": "Cloudflare 边缘缓存已成功禁用。", "Failed to update Cloudflare Edge Cache settings.": "无法更新 Cloudflare 边缘缓存设置。", "Cloudflare Edge Cache purged successfully.": "Cloudflare边缘缓存清除成功。", "Failed to purge Cloudflare Edge Cache.": "清除 Cloudflare 边缘缓存失败。", "Cloudflare Edge Cache": "Cloudflare 边缘缓存", "Boost your website's performance with Cloudflare Edge Cache by caching content at Cloudflare's global edge network. This reduces server load, enhances speed, and improves user experience worldwide.": "通过在 Cloudflare 全球边缘网络缓存内容，使用 Cloudflare Edge Cache 提升您网站的性能。这可以减少服务器负载，提高速度，并改善全球用户体验。", "Clear Edge Cache": "清除边缘缓存", "This will purge all cached content from Cloudflare's edge network.": "这将清除 Cloudflare 边缘网络中的所有缓存内容。", "Failed to update Cloudflare Edge Cache settings": "无法更新 Cloudflare 边缘缓存设置", "Are you sure you want to disable Cloudflare Edge Cache?": "您确定要禁用 Cloudflare 边缘缓存吗？", "Failed to disable Cloudflare Edge Cache": "无法禁用 Cloudflare 边缘缓存", "Cloudflare Edge Cache purged successfully": "Cloudflare 边缘缓存清除成功", "Patchstack Subscriptions": "Patchstack 订阅", "List of Patchstack Subscriptions": "Patchstack 订阅列表", "Find all the patchstack subscriptions associated with your account here.": "在此查看与您的账户关联的所有 Patchstack 订阅。", "Vulnerability Shield Pro is active!": "漏洞防护专业版已激活！", "Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.": "漏洞防护专业版现在将提供更快速的警报，让您领先于威胁。", "WordPress Core.": "WordPress核心", "Get faster security alerts with Vulnerability Shield Pro powered by": "通过Vulnerability Shield Pro加速获取安全警报，由...提供支持", "Patchstack - only $4/month.": "Patchstack - 每月仅需 $4。", "Upgrade to PRO": "升级到专业版", "Canceling...": "取消中...", "Vulnerability Shield Pro": "漏洞防护专业版", "Powered by Patchstack": "由 Patchstack 提供支持", "Stay protected with continuous vulnerability scans and advanced threat alerts to secure your WordPress website like a pro.": "通过持续漏洞扫描和高级威胁警报来保护您的 WordPress 网站，像专业人士一样确保安全。", "mo": "\"更多\"", "Upgrade to Pro NOW": "立即升级到专业版", "Remove Subscription": "取消订阅", "Removing...": "正在移除…", "Are you sure you want to remove the subscription for this site?": "您确定要取消订阅此网站吗？", "Once confirm, this cannot be undone.": "一旦确认，将无法撤销。", "Patchstack - only": "Patchstack - 仅限", "/month.": "/月", "Subscription Status": "订阅状态", "Export Sites Data": "导出站点数据", "Export Servers Data": "导出服务器数据", "Export": "导出", "Export site data in your preferred format by selecting the desired export type, format, and data columns.": "选择所需的导出类型、格式和数据列，以您偏好的格式导出站点数据。", "Export server data in your preferred format by selecting the desired export type, format, and data columns.": "选择所需的导出类型、格式和数据列，以您偏好的格式导出服务器数据。", "Select Export Type": "选择导出类型", "Choose what to export": "选择要导出的内容", "Excel (XLSX)": "Excel (XLSX)", "CSV": "CSV", "Select Columns": "选择列", "Exporting...": "导出中...", "Export Format": "导出格式", "Issues": "问题", "No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.": "漏洞防护专业版未检测到任何问题。如发现问题，我们会立即通知您。", "Excellent! Your WordPress Core is fully secure!": "优秀！您的 WordPress 核心已完全安全！", "Excellent! Your plugins are fully secure!": "优秀！您的插件完全安全！", "Excellent! Your theme is fully secure!": "优秀！您的主题已完全安全！", "Issue": "问题", "No issues were detected by Vulnerability Scanner. You’ll be notified immediately if any issues are found.": "漏洞扫描器未检测到问题。如发现任何问题，您将立即收到通知。", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $5/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "漏洞扫描器未检测到任何问题。如发现问题，您将立即收到通知。升级到专业版仅需 $5/月，即可享受由 Patchstack 提供的高级漏洞检测和自动修补功能。", "Go PRO for": "升级到专业版以获取", "Upgrade to Vulnerability Shield Pro for automated vulnerability detection & virtual patching powered by Patchstack": "升级到 Vulnerability Shield Pro，享受由 Patchstack 提供支持的自动漏洞检测和虚拟补丁功能。", "Output": "输出", "Shield Enabled": "防护已启用", "Enable Shield": "启用防护", "Vulnerability Shield": "漏洞防护", "Pro": "专业版", "Enable Vulnerability Shield Pro to secure this site with automated virtual patching and faster notification alerts.": "启用漏洞防护专业版，以通过自动虚拟补丁和更快速的通知提醒来保护此站点。", "Pay now for": "立即支付", "Continue free plan": "继续免费计划", "I am not interested at this moment. Please, do not show this message again.": "目前我不感兴趣。请不要再显示此消息。", "The plugin has a vulnerability that makes it possible for unauthorized actions.": "插件存在一个漏洞，可能导致未经授权的操作。", "This vulnerability affects": "此漏洞影响", "We have discovered security vulnerabilities in the": "我们发现了该系统中的安全漏洞", "These": "这些", "have vulnerabilities that make it possible for unauthorized actions.": "存在漏洞，可能导致未经授权的操作。", "The plugins have vulnerabilities that make it possible for unauthorized actions.": "插件存在漏洞，可能导致未经授权的操作。", "We have discovered security vulnerabilities in": "我们发现了安全漏洞在", "and a few more plugins that you are using on the following website:": "您在以下网站上使用的其他一些插件：", "We detected this vulnerability on": "我们检测到此漏洞于", "UTC. You can ignore this message if you have already taken care of it.": "协调世界时。您可以忽略此消息，如果您已经处理过。", "Thank you for being a": "感谢您成为一名", "user!": "用户", "If you have any questions, don't hesitate to contact our": "如有任何疑问，请随时联系我们", "support team": "支持团队", "Thank you for being a xCloud user!": "感谢您使用 xCloud！", "We found a security vulnerability in your Website": "我们在您的网站中发现了一个安全漏洞", "Scanning Now": "正在扫描", "Last Scanned": "上次扫描", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $": "漏洞扫描器未检测到任何问题。如发现问题，我们会立即通知您。升级到专业版仅需 $", "/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "高级漏洞检测和自动修补由 Patchstack 提供支持的 /mo。"}