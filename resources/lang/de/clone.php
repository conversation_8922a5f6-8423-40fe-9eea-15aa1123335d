<?php

use App\Enums\SiteStatus;
use App\Services\Clone\SiteCloning;

return [
    'init' => [
        [
            'stage' => 'Prüfen und Verifizieren',
            'tasks' => [
                SiteCloning::CHECKING_STATUS => 'Server-Speicher & Verbindung prüfen',
            ]
        ],
        [
            'stage' => 'Apps installieren',
            'tasks' => [
                SiteCloning::INSTALLING_PHP => 'PHP installieren: :php_version',
                SiteCloning::SYNCING_PHP_SETTINGS => 'PHP-Einstellungen synchronisieren',
                SiteCloning::INSTALLING_DATABASE => 'Datenbank installieren',
            ]
        ],
    ],
    'config' => [
        [
            'stage' => 'Konfigurieren',
            'tasks' => [
                SiteCloning::CLONING_SITE => 'Seite klonen',
                SiteCloning::CONFIGURING_SSL => 'SSL für geklonte Seite konfigurieren',
                SiteCloning::CONFIGURING_HTTPS => 'HTTPS für geklonte Seite konfigurieren',
                SiteCloning::CONFIGURING_FULL_PAGE_CACHE => 'Full Page Cache für geklonte Seite konfigurieren',
                SiteCloning::CONFIGURING_REDIS_CACHE => 'Redis Object Cache für geklonte Seite konfigurieren',
                SiteCloning::CONFIGURING_NGINX => 'Nginx für geklonte Seite konfigurieren',
                SiteCloning::INSTALLING_MONITORING => 'Monitoring für geklonte Seite installieren',
                SiteCloning::FINISHING_UP => 'Abschließen',
            ]
        ]
    ]
];
