<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'Karte überprüfen und Zahlung entgegennehmen',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'Karte überprüfen und Zahlung von $:amount entgegennehmen',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'Server erstellen',
            'tasks' => [
                ServerProvisioning::INIT => 'Serverbereitstellung initialisieren',
                ServerProvisioning::CREATING_SERVER => 'Server auf :provider erstellen',
                ServerProvisioning::SERVER_CREATED => 'Server :ip erstellt',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'Warten auf den Start des Servers',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'Verbindung zum Server herstellen',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'SSH-Verbindung herstellen',
                ServerProvisioning::CONNECTED => 'Verbindung hergestellt',
            ]
        ],
        [
            'stage' => 'Server konfigurieren',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'Swapfile konfigurieren',
                ServerProvisioning::UPGRADING_SYSTEM => 'System aktualisieren',
                ServerProvisioning::INSTALLING_BASE => 'Basisabhängigkeiten installieren',
                ServerProvisioning::AUTHENTICATION_METHOD => 'Authentifizierungsmethode aktualisieren',
                ServerProvisioning::UPDATING_HOSTNAME => 'Hostname aktualisieren',
                ServerProvisioning::UPDATING_TIMEZONE => 'Zeitzone aktualisieren',
                ServerProvisioning::XCLOUD_USER => 'Benutzer einrichten',

                ServerProvisioning::SETUP_SSH => 'SSH einrichten',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Sudo-Berechtigungen einrichten',
                ServerProvisioning::SETTING_UP_GIT => 'Git einrichten',
                ServerProvisioning::SETUP_FIREWALL => 'Firewall einrichten',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'Reinigungsskript einrichten',
            ]
        ],
        [
            'stage' => 'Apps installieren',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'PHP :php_version installieren',
                ServerProvisioning::INSTALLING_WEBSERVER => ':stack installieren',
                ServerProvisioning::INSTALLING_NODE => 'Node installieren',
                ServerProvisioning::INSTALLING_REDIS => 'Redis installieren',
                ServerProvisioning::INSTALLING_DATABASE => ':database_type-Datenbank installieren',
                ServerProvisioning::INSTALLING_WP_CLI => 'WP CLI installieren',
            ]
        ],
        [
            'stage' => 'Abschluss',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'SSH-Berechtigungen einstellen',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => 'Überwachungsskript installieren',
                ServerProvisioning::READY_TO_DO_MAGIC => 'Bereit, Magie zu wirken!',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => 'Überprüfen und Verifizieren',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'Server-Speicher und Verbindung überprüfen',
                SiteProvisioning::VERIFYING_DNS => 'DNS für Ihre Seite verifizieren',
            ]
        ],
        [
            'stage' => 'Apps installieren',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => 'PHP :php_version installieren',
                SiteProvisioning::INSTALLING_DATABASE => 'Datenbank installieren',
                SiteProvisioning::INSTALLING_WORDPRESS => 'WordPress einrichten',
            ]
        ],
        [
            'stage' => 'Konfigurieren',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'SSL konfigurieren',
                SiteProvisioning::CONFIGURING_HTTPS => 'HTTPS konfigurieren',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => ':cache konfigurieren',
                SiteProvisioning::CONFIGURING_NGINX => ':stack konfigurieren',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Redis Objekt-Cache konfigurieren',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Blueprint installieren',
                SiteProvisioning::DEPLOY_SCRIPT => 'Deploy-Skript',
                SiteProvisioning::INSTALL_MONITORING => 'Überwachung installieren',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'WP Cron-Job installieren',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'Einrichtung des xCloud Managed E-Mail-Dienstes',
                SiteProvisioning::HANDLE_INDEXING => 'Website-Indexierung verwalten',
                SiteProvisioning::FINISHING_UP => 'Abschluss',
            ]
        ]
    ],
];
