{"xCloud": "雲端服務", "xcloud": "雲端服務", "Log In To": "登入至", "Email": "電子郵件", "Password": "密碼", "forgot_password": "忘記密碼", "Remember me": "記住我", "Log In": "登入", "Not Registered Yet? Please": "尚未註冊？請", "Sign Up": "註冊", "Email Address..": "電子郵件地址", "Sign Up For": "註冊", "Enter necessary informations for creating a new account": "輸入建立新帳戶所需的資訊。", "Name": "名稱", "Use 8 or more characters with a mix of letters, numbers & symbols": "請使用 8 個或以上的字元，包含字母、數字和符號。", "Password Confirmation": "確認密碼", "I agree to the": "我同意", "Terms": "條款", "and": "和", "Privacy Policy": "隱私政策", "Already have an account? Please": "已經有帳戶？請", "Forgot Password": "忘記密碼", "Please enter your email address to search for your account": "請輸入您的電子郵件地址以搜尋您的帳戶", "Reset Password": "重設密碼", "This is a secure area of the application. Please confirm your password before continuing.": "這是應用程式的安全區域。請確認您的密碼後繼續。", "Confirm": "確認", "Confirm Password": "確認密碼", "Two-factor Confirmation": "雙重驗證", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "請輸入您的驗證應用程式提供的驗證碼以確認帳戶存取。", "Please confirm access to your account by entering one of your emergency recovery codes.": "請輸入您的其中一個緊急恢復代碼以確認訪問您的帳戶。", "Code": "代碼", "Recovery Code": "恢復代碼", "Use a recovery code": "使用恢復代碼", "Use an authentication code": "使用驗證碼", "Verify Email Address": "驗證電子郵件地址", "To continue using": "繼續使用", "please click on the link in the verification email sent to your email.": "請點擊發送到您電子郵件中的驗證郵件中的連結。", "A new verification link has been sent to the email address you provided during registration.": "已將新的驗證連結發送到您在註冊時提供的電子郵件地址。", "Please wait": "請稍候", "seconds to resend email.": "重新發送電子郵件的秒數。", "Resend Verification Email": "重新發送驗證電子郵件", "Logout": "登出", "Overview": "概覽", "New Server": "新伺服器", "New Site": "新網站", "Active Servers": "啟用伺服器", "Total Sites": "總站點數", "No Active Billing": "無啟用的帳單", "Plan": "計劃", "Vulnerable Sites": "易受攻擊的網站", "New Team Invitations": "新團隊邀請", "Server Setup In Progress": "伺服器設定中", "Site Setup In Progress": "網站設定中", "View All": "檢視全部", "You can create 1 server and 10 sites with our Free Plan.": "您可以使用我們的免費方案建立 1 個伺服器和 10 個網站。", "Two-Factor Authentication": "雙重驗證", "You have not enabled two-factor authentication, which is highly recommended for your account security.": "您尚未啟用雙重驗證，強烈建議啟用以提高帳戶安全性。", "Skip": "跳過", "Setup Now": "立即設定", "Please check your": "請檢查您的", "invoices": "發票", "has asked you to join as a": "邀請您加入作為", "to this team": "給此團隊", "Decline": "拒絕", "Accept": "接受", "To decline the invitation please visit": "若要拒絕邀請，請造訪", "team page": "團隊頁面", "Server Disk Space Low": "伺服器磁碟空間不足", "Warning": "警告", "Your Server": "您的伺服器", "disk space is low. Please upgrade your plan to avoid any downtime.": "磁碟空間不足。請升級您的方案以避免停機。", "Reboot Required": "需要重新啟動", "Security Update": "安全更新", "will automatically update and reboot on": "將自動更新並重新啟動於", "You can also reboot now.": "您也可以立即重新啟動。", "Security Update - Server": "安全更新 - 伺服器", "requires a reboot.": "需要重新啟動。", "Reboot Now": "立即重啟", "Security updates were automatically installed on your server, but a reboot is required to complete the installation. The following packages were upgraded:": "您的伺服器已自動安裝安全更新，但需要重新啟動以完成安裝。以下套件已升級：", "Be sure to test the sites on your server after rebooting.": "重新啟動後，請確保在您的伺服器上測試這些網站。", "Server Size": "伺服器大小", "PHP": "PHP", "View Details": "檢視詳情", "Server": "伺服器", "Continue Setup": "繼續設定", "View Site": "檢視網站", "Open options": "開啟選項", "Bill Calculator": "帳單計算器", "Billing Amount": "帳單金額", "Select Renewal Period": "選擇續訂期間", "Bill From": "帳單來自", "Bill Before": "帳單之前", "Bill To": "帳單寄送至", "Processing payment... Please do not cancel or refresh the page.": "正在處理付款... 請勿取消或重新整理頁面。", "My Blueprints": "我的藍圖", "Create New Blueprint": "建立新藍圖", "Created": "已建立", "Active Plugins": "啟用的插件", "Active Theme": "主題啟用", "Edit Blueprint": "編輯藍圖", "Create Blueprint": "建立藍圖", "By": "由", "Active Installations": "啟用中的安裝數", "Update": "更新", "Create": "建立", "Staging Url": "暫存網址", "Confirm Details": "確認詳細資料", "Edit": "編輯", "Old Domain": "舊網域", "HTTPS": "HTTPS", "Enabled": "已啟用", "Disabled": "停用", "PHP Version": "PHP 版本", "Database Type": "資料庫類型", "Database Name": "資料庫名稱", "Database User": "資料庫使用者", "No Database": "無資料庫", "Site User": "網站使用者", "Configuration & Database Connection": "設定與資料庫連線", "Create Database In Server": "在伺服器中建立資料庫", "Select Destination Server To Clone Your Site": "選擇目標伺服器以複製您的網站", "Choose Server": "選擇伺服器", "DNS & SSL For": "DNS 和 SSL 用於", "Demo Site": "示範網站", "Create a demo site with our test domain and customize before going live.": "使用我們的測試域名建立示範網站，並在上線前進行自訂。", "Migrate into a New Domain": "遷移到新網域", "Get your site up and running for the world to see by simply pointing your domain to the server.": "只需將您的網域指向伺服器，即可讓您的網站上線，讓全世界看到。", "Add DNS and SSL Certificate on Cloudflare": "在 Cloudflare 上新增 DNS 和 SSL 憑證", "Integrate Cloudflare for Automatic DNS and SSL management.": "整合 Cloudflare 以自動管理 DNS 和 SSL。", "DNS Setup": "DNS 設定", "Settings & Configurations": "設定與配置", "Deploy Script (Optional)": "部署腳本（可選）", "wp plugin install woocommerce": "安裝 WooCommerce 外掛", "Verifying": "驗證中", "Uploading....": "上傳中...", "Upload/Drop Your Zipped sql or only .sql File": "上傳/拖放您的壓縮 sql 或僅 .sql 檔案", "Maximum File Size: 500 MB": "最大檔案大小：500 MB", "Use These Credentials To Transfer Database": "使用這些憑證來傳輸資料庫", "Server Connection Url": "伺服器連接網址", "Server Address": "伺服器地址", "Server SSH Host": "伺服器 SSH 主機", "Server Username": "伺服器用戶名", "Server database name": "伺服器資料庫名稱", "Database Password": "資料庫密碼", "Verify Upload": "驗證上傳", "Upload/Drop Your Zipped/Tar File": "上傳/拖放您的壓縮檔案（ZIP/TAR）", "Use These Credentials To Upload Your WordPress Website": "使用這些憑證上傳您的 WordPress 網站", "22": "22", "Port": "連接埠", "Remote Path": "遠端路徑", "Upload Migration Files": "上傳遷移檔案", "Choose a method from below to install the": "請選擇以下方法來安裝", "Migration Plugin": "遷移插件", "Optional": "可選", "Authentication Token": "驗證令牌", "Plugin Page URL": "插件頁面網址", "I have added the authentication token to my site": "我已將驗證令牌新增到我的網站", "Please agree that you have added the authentication token to your site": "請確認您已將驗證令牌新增至您的網站", "Download Plugin ZIP File": "下載插件 ZIP 檔案", "Plugin Link": "插件連結", "WP CLI": "WP CLI", "Show Previous cPanel Migrations": "顯示先前的 cPanel 遷移", "Username of the cPanel account:": "cPanel 帳戶的使用者名稱：", "Enter your cPanel username": "輸入您的 cPanel 使用者名稱", "cPanel Api Key:": "cPanel API 金鑰：", "Enter your cPanel API Key": "輸入您的 cPanel API 金鑰", "cPanel Host:": "cPanel 主機：", "Fetch Existing Backups": "擷取現有備份", "Select a backup to import from the list below": "從下方列表中選擇要匯入的備份", "Backup in Progress ...": "正在備份...", "No Backups Found": "未找到備份", "You need to create a backup in your cPanel account first": "您需要先在您的 cPanel 帳戶中建立備份", "Generate Backup": "生成備份", "Migration File": "遷移檔案", "Created At": "建立時間", "Actions": "操作", "Continue Migration": "繼續遷移", "No cPanel Migrations Found": "未找到 cPanel 遷移", "You can create a new cPanel Migration from the cPanel Migration tab": "您可以從 cPanel 遷移標籤創建新的 cPanel 遷移", "Upload Completed": "上傳完成", "Upload/Drop Your Zipped File": "上傳/拖放您的壓縮檔案", "Regenerate cPanel User": "重新生成 cPanel 使用者", "I have started the backup": "我已開始備份", "Waiting for the backup to complete": "正在等待備份完成", "Currently transferring this file from generated backup for cpanel migration": "目前正在從生成的備份中傳輸此文件以進行 cPanel 遷移", "New cPanel Migration using SCP found, click here to continue": "發現新的 cPanel 遷移使用 SCP，點擊這裡繼續", "Live Site": "即時網站", "Domain Setup": "網域設定", "Domain Name": "網域名稱", "Auto DNS management by Cloudflare is disabled for Full Server Migration.": "由於進行完整伺服器遷移，Cloudflare 的自動 DNS 管理已停用。", "Coming soon...": "即將推出...", "Your DNS setup and SSL Certificate will be done by Cloudflare and managed by xCloud.": "您的 DNS 設定和 SSL 憑證將由 Cloudflare 完成並由 xCloud 管理。", "Confirm Migration Details": "確認遷移詳情", "Source site": "來源網站", "Destination site": "目的地網站", "Select Websites to Migrate": "選擇要遷移的網站", "to": "至", "Previously migrated sites from": "先前遷移的網站來自", "Available Sites": "可用站點", "Select All": "全選", "Fetch Websites": "擷取網站", "Fetching...": "正在擷取...", "Wordpress Sites To Migrate": "要遷移的 Wordpress 網站", "Type": "類型", "Directory": "目錄", "Non-Wordpress Sites": "非 WordPress 網站", "Fetching sites...": "正在擷取網站...", "Add Websites to Migrate": "新增網站以進行遷移", "Add the domains you want to migrate from your Cpanel backup, we will search these domains and migrate to the new server": "將您想從 Cpanel 備份中遷移的網域新增，我們將搜尋這些網域並遷移到新伺服器。", "Add Website": "新增網站", "Connect Your Source Server": "連接您的來源伺服器", "Submit": "提交", "Select your Hosting Provider": "選擇您的主機提供商", "cPanel Migration Method": "cPanel 遷移方法", "Set domain for migration": "設定遷移的網域", "Full Server Migration In Progress from": "伺服器完整遷移進行中", "Migration completed successfully from": "遷移成功完成，從", "Successfully Migrated Sites": "網站遷移成功", "Migration In Progress": "遷移進行中", "Migration In Queue": "遷移排隊中", "Migration Failed": "遷移失敗", "IP Address": "IP 位址", "SSH Port": "SSH 埠", "SSH Username": "SSH 使用者名稱", "root": "根目錄", "SSH Authentication": "SSH 驗證", "SSH Password": "SSH 密碼", "Size": "尺寸", "GB": "英國", "Existing Site": "現有網站", "Full Page Cache": "全頁快取", "Redis Object Cache": "Redis 物件快取", "Default Database In Server": "伺服器中的預設資料庫", "Database": "資料庫", "Admin User Name": "管理員用戶名", "Files and Database Migration": "檔案和資料庫遷移", "Select Databases & File Systems To Migrate": "選擇要遷移的資料庫和檔案系統", "Migrate The Following Content": "遷移以下內容", "All Database Tables & Corresponding File System": "所有資料庫表格及對應的檔案系統", "Only File System But NOT Database Tables": "僅限檔案系統，不包括資料庫表格", "Select Destination Server To Migrate Your Site": "選擇目標伺服器以遷移您的網站", "Existing WordPress Site URL": "現有 WordPress 網站 URL", "Existing Site URL": "現有網站 URL", "Git Repository": "Git 儲存庫", "master": "主控", "Git Branch": "Git 分支", "Enable push to deploy": "啟用推送部署", "Deployment URL": "部署 URL", "npm run deploy": "npm 執行 deploy", "Deploy Script": "部署腳本", "Run this script after every site deployment": "在每次網站部署後執行此腳本", "Select Databases To Migrate": "選擇要遷移的資料庫", "Add Your Existing Database": "新增現有資料庫", "Without Database": "無資料庫", "Install Migration Plugin": "安裝遷移插件", "Buy Now": "立即購買", "Packages": "套件", "Products": "產品", "Bills & Payment": "帳單與付款", "Free plan which includes 1 server and 10 website with Self Hosting.": "免費方案，包括 1 台伺服器和 10 個自託管網站。", "You’re on the": "您正在使用", "Activate your": "啟用您的", "team by adding payment method today.": "立即新增付款方式以加入團隊。", "team by adding payment method.": "將付款方式新增至團隊。", "Estimated Cost": "預估成本", "This is an estimate of the amount based on your current month-to-date": "這是根據您當前本月累計的金額估算", "Cost This Month": "本月費用", "Cost Next Month": "下個月費用", "Overused Amount After 28th": "28日後超出金額", "Billing Period Monthly": "計費週期 每月", "renews 29": "續訂 29", "Read how billing works.": "了解帳單運作方式。", "Subscriptions": "訂閱", "xCloud Managed Email Provider": "xCloud 管理電子郵件提供者", "Services": "服務", "Payment Methods": "付款方式", "You can add Credit/Debit Card as your payment method": "您可以新增信用卡/借記卡作為付款方式", "Expires at": "到期時間", "Default": "預設", "Set As Default": "設為預設", "Add A Payment Method": "新增付款方式", "Billing History": "帳單記錄", "Description": "描述", "Date": "日期", "Service Status": "服務狀態", "Amount": "金額", "Payment Status": "付款狀態", "Next Billing": "下次帳單", "Renewal Period": "續訂週期", "Link a Credit/Debit Card": "連結信用卡/借記卡", "We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)": "我們接受 Visa、Mastercard、American Express、Discover、Diners Club，以及中國銀聯（CUP）、日本信用卡公司（JCB）", "Service": "服務", "Amount Payable": "應付金額", "discontinued on": "已停產於", "to lifetime": "終身", "I want to convert this bill to monthly and unlock full features": "我要將此帳單轉為每月計費並解鎖全部功能", "Please add a": "請新增一個", "payment": "付款", "method unlock more features.": "方法解鎖更多功能。", "Amount Paid": "已支付金額", "Whitelabel Subscriptions": "白標訂閱", "Current Plan": "目前方案", "Sell Up to": "最多出售", "Servers": "伺服器", "Subscription Date": "訂閱日期", "Expires on": "到期日", "Your subscription is about to expire please renew it": "您的訂閱即將到期，請續訂", "Your subscription is about to expire please renew it for uninterrupted service.": "您的訂閱即將到期，請續訂以確保服務不中斷。", "Pay Now": "立即付款", "No active subscription found": "找不到有效的訂閱", "Claim Free with LTD": "使用 LTD 免費領取", "Server Limit": "伺服器限制", "As an existing LTD customer, you are eligible to claim this plan for free.": "作為現有的LTD客戶，您有資格免費申請此方案。", "Switch Free with LTD": "使用 LTD 免費切換", "Switch to this plan for": "切換到此方案以獲得", "Choose a plan": "選擇方案", "SMTP Username": "SMTP 使用者名稱", "SMTP Password": "SMTP 密碼", "Domain": "網域", "Sendgrid Username": "Sendgrid 使用者名稱", "Sendgrid Api Key": "Sendgrid API 金鑰", "SMTP Host": "SMTP 主機", "SMTP Port": "SMTP 埠", "Label": "標籤", "Get help from our": "獲取我們的幫助", "Configure SMTP Provider": "設定 SMTP 提供者", "documentation": "文件", "From Email": "寄件者電子郵件", "To Email": "發送至電子郵件", "Email Subscriptions": "電子郵件訂閱", "With xCloud you get 100 free emails per month in each team. You can purchase additional email more from": "使用 xCloud，每個團隊每月可獲得 100 封免費電子郵件。您可以購買更多額外的電子郵件。", "here.": "這裡", "xCloud Email Balance": "xCloud 電子郵件餘額", "This is a summary of your xCloud Email Balance.": "這是您的 xCloud 電子郵件餘額摘要。", "Total Emails": "總電子郵件數", "Emails Available": "可用電子郵件", "Elastic Email API Key": "Elastic Email API 金鑰", "Invoice": "發票", "Details": "詳情", "You will pay for following bills": "您將支付以下帳單", "which are under": "在以下條件下", "Next Billing Amount": "下次帳單金額", "Amount Adjusted": "調整金額", "Add New Member": "新增成員", "Send Invitation": "發送邀請", "Server Access": "伺服器存取", "Access to all server": "訪問所有伺服器", "Choose specific server": "選擇特定伺服器", "Select here...": "選擇此處...", "Site Access": "網站存取", "Access to all site": "訪問所有網站", "Choose specific site": "選擇特定站點", "Role Permission": "角色權限", "Create New Team": "建立新團隊", "Save Changes": "儲存變更", "Upload Your Avatar": "上傳您的頭像", "Add a profile picture or icon for your account": "為您的帳戶新增個人資料圖片或圖示", "Upload Image": "上傳圖片", "Team Name": "團隊名稱", "Team Email": "團隊電子郵件", "Tags": "標籤", "Select or create tags": "選擇或建立標籤", "Edit Team": "編輯團隊", "Update Member": "更新會員", "Update Invitation": "更新邀請", "Email ID": "電子郵件 ID", "User Role": "使用者角色", "Delete Team": "刪除團隊", "Leave Team": "離開團隊", "Team Members": "團隊成員", "Role": "角色", "Status": "狀態", "Server Count": "伺服器數量", "Action": "操作", "Change Role": "變更角色", "Delete": "刪除", "Resend Email": "重新發送電子郵件", "Site": "網站", "Switch to Team": "切換至團隊", "Add Member": "新增成員", "Check Details": "查看詳情", "All Sites": "所有網站", "Refresh": "重新整理", "Archive Servers": "封存伺服器", "List of Archive Servers": "存檔伺服器列表", "Find all the archive servers associated with your team here.": "在此處查找與您的團隊相關的所有歸檔伺服器。", "Provider": "供應商", "If you proceed, this will permanently remove this service and you will not be able to access or retrieve it again.": "如果您繼續，此操作將永久移除此服務，您將無法再次訪問或恢復。", "Authentication": "驗證", "Please enter the two-factor authentication code sent to you below to verify:": "請輸入發送給您的雙重驗證碼以進行驗證：", "Billing Details": "帳單明細", "Saved Cards": "已儲存的卡片", "Add your payment methods for billing.": "新增您的付款方式以進行結算。", "Set as Default": "設為預設", "No card available": "無可用卡片", "Add Payment Method": "新增付款方式", "Bills": "帳單", "Find complete, downloadable receipts of your regular payments": "查找您定期付款的完整可下載收據", "Title": "標題", "Amount To Pay": "應付金額", "Due On": "到期日", "Next Billing Date": "下次計費日期", "Paid On": "已付款", "Manage Active Browser Sessions": "管理活動瀏覽器工作階段", "Check which sessions are still active from different devices & browsers, and manage as needed.": "檢查哪些裝置和瀏覽器上的工作階段仍在活動，並根據需要進行管理。", "Note: You can log out of all your active sessions; it will also log you out of this session and you will have to log back in to continue using": "注意：您可以登出所有活躍的會話；這也會使您登出當前會話，您需要重新登入才能繼續使用。", "Log Out Of All Sessions": "登出所有會話", "Browser Name": "瀏覽器名稱", "Last Session": "上次會話", "This device": "此裝置", "Last active": "上次活躍時間", "Cloudflare Integration": "Cloudflare 整合", "List of your Cloudflare Integrations": "您的 Cloudflare 整合列表", "Find all the cloudflare integrations associated with your account here.": "在此查看與您的帳戶相關的所有 Cloudflare 整合。", "New Cloudflare Integration": "新的 Cloudflare 整合", "Account Email": "帳戶電子郵件", "API Token": "API 令牌", "Global API Key": "全域 API 金鑰", "Origin CA Key": "來源 CA 金鑰", "Add New Cloudflare Integration": "新增 Cloudflare 整合", "Name of the integration": "整合名稱", "Your Cloudflare Account Email": "您的 Cloudflare 帳戶電子郵件", "Create a new API Token from your Cloudflare account and paste it here. (With Edit zone DNS permissions)": "從您的 Cloudflare 帳戶創建一個新的 API 令牌，並在此處貼上。（具有編輯區域權限）", "Go to your Cloudflare Profile > API Tokens > Global API Key > View": "前往您的 Cloudflare 個人資料 > API 令牌 > 全域 API 金鑰 > 檢視", "Go to your Cloudflare Profile > API Tokens > Origin CA Key > View": "前往 Cloudflare 個人資料 > API 令牌 > 原始 CA 金鑰 > 查看", "Integrate Cloudflare For DNS Management": "整合 Cloudflare 進行 DNS 管理", "Account email": "帳戶電子郵件", "Find complete, downloadable receipts of your regular payments.": "查找您定期付款的完整可下載收據。", "All Invoices": "所有發票", "This Month": "本月", "Last Month": "上個月", "Last 6 Months": "過去 6 個月", "Last 1 Year": "過去 1 年", "Paid Invoices": "已付款發票", "Unpaid Invoices": "未付款發票", "Failed Invoices": "發票失敗", "Invoice No": "發票號碼", "Download": "下載", "Cancel": "取消", "Email Provider": "電子郵件提供者", "List of Email Providers": "電子郵件提供者列表", "Find all the email providers associated with your account here.": "在此查看與您的帳戶相關聯的所有電子郵件提供商。", "Add New Provider": "新增提供者", "Username/Domain": "使用者名稱/網域", "Invitation List": "邀請名單", "Invite Users": "邀請用戶", "Share your invitation code with others. You have": "與他人分享您的邀請碼。您有", "invitation": "邀請", "remaining": "剩餘", "Invited Users": "受邀用戶", "You have invited following persons.": "您已邀請以下人員。", "Accepted": "已接受", "Notifications": "通知", "List of Notifications": "通知列表", "Integrate your notification platform. You can customize your": "整合您的通知平台。您可以自訂您的", "notification settings": "通知設定", "from": "從", "here": "這裡", "Add New Notification": "新增通知", "Disconnect": "斷開連接", "Reconnect": "重新連接", "Add Notification": "新增通知", "Being with Country Code (e.g., ****** XXX XXXX for US)": "國碼開頭（例如，美國為 ****** XXX XXXX）", "WhatsApp Phone Number": "WhatsApp 電話號碼", "Server Notifications": "伺服器通知", "For server reboots, unavailable servers, and available upgrades are included": "伺服器重啟、無法使用的伺服器和可用升級已包含在內", "Telegram": "電報", "WhatsApp": "WhatsApp", "Slack": "<PERSON><PERSON>ck", "Newly Provisioned Servers": "新配置的伺服器", "Site Notifications": "網站通知", "For site upgrades, SSL certificate issues, and deployment errors": "網站升級、SSL 證書問題和部署錯誤", "Other Notifications": "其他通知", "Get notified about team accounts and actions": "接收團隊帳戶和操作的通知", "Do Not Send Sensitive Information": "請勿發送敏感資訊", "This option will disable sending sensitive options like, sudo password, database password, wp-admin password over email and notification channels": "此選項將停用透過電子郵件和通知渠道發送敏感資訊，如 sudo 密碼、資料庫密碼、wp-admin 密碼。", "Vulnerability Notifications": "弱點通知", "Enable this option to receive notifications about vulnerabilities via Email": "啟用此選項以通過電子郵件接收漏洞通知", "Stay informed and take timely action to secure your systems": "保持資訊更新，及時採取行動以保護您的系統。", "Team Packages": "團隊方案", "Renewal type": "續訂類型", "Price Per Server": "每台伺服器價格", "Total Price": "總價格", "Change Password": "更改密碼", "Enter a strong password to keep your profile locked": "輸入強密碼以鎖定您的個人資料", "Current Password": "目前密碼", "New Password": "新密碼", "Pay Bills": "繳費", "Choose Your Payment Method": "選擇付款方式", "You are paying for following invoice": "您正在支付以下發票", "Total Amount": "總金額", "Team Products": "團隊產品", "Service Type": "服務類型", "Price": "價格", "Role Management": "角色管理", "Server Provider": "伺服器提供商", "List of Server Providers": "伺服器提供商列表", "Find all the server providers associated with your account here.": "在此查看與您的帳戶相關的所有伺服器提供商。", "Servers Count": "伺服器數量", "Select Server Provider": "選擇伺服器提供商", "Vultr API Key": "Vultr API 金鑰", "API Key": "API 金鑰", "Hetzner API Key": "Hetzner API 金鑰", "Hetzner Label": "<PERSON><PERSON><PERSON> 標籤", "Upload JSON": "上傳 JSON", "Label for AWS Credential": "AWS 憑證標籤", "AWS Access Key": "AWS 存取金鑰", "Access Key": "存取金鑰", "AWS Secret Key": "AWS 秘密金鑰", "Secret Key": "密鑰", "Setup A Digital Ocean Server In xCloud": "在 xCloud 中設置 Digital Ocean 伺服器", "Setup A Vultr Server In xCloud": "在 xCloud 中設置 Vultr 伺服器", "Setup A GCP Server In xCloud": "在 xCloud 中設置 GCP 伺服器", "SSH Keys": "SSH 金鑰", "Add New SSH Key": "新增 SSH 金鑰", "ID": "識別碼", "Fingerprint": "指紋", "Auto Provision": "自動配置", "Used By": "使用者", "A name to recognize this public key": "用於識別此公鑰的名稱", "Key Name": "鍵名稱", "Public Key": "公鑰", "Don't have a key?": "沒有鑰匙？", "Learn how to": "學習如何", "generate an SSH Key": "生成 SSH 金鑰", "Already have a key?": "已經有金鑰？", "Copy and paste your key here with": "在此處複製並貼上您的金鑰", "Always provision to new servers": "始終配置到新伺服器", "Select servers to push this key to": "選擇伺服器以推送此金鑰", "Default Sudo User": "預設 Sudo 使用者", "Default Sudo Password": "預設 Sudo 密碼", "If you proceed, this will permanently remove this SSH Key and you will not be able to access or retrieve it again.": "如果您繼續，這將永久刪除此 SSH 金鑰，您將無法再次訪問或檢索它。", "Following sudo users will be deleted": "以下的 sudo 使用者將被刪除", "SSH key will be removed from the following sites": "SSH 金鑰將從以下網站移除", "Storage Provider": "儲存提供者", "List of Storage Providers": "儲存供應商列表", "Bucket Name": "儲存桶名稱", "Region": "地區", "Site Count": "站點計數", "bucket": "儲存桶", "Enter the name of the bucket you have in your storage provider.": "輸入您在存儲提供商中的儲存桶名稱。", "Select the data location": "選擇資料位置", "Select Region": "選擇地區", "Enter the access key id here.": "在此輸入存取金鑰 ID。", "Access Key Id": "存取金鑰 ID", "Enter the secret key here.": "在此輸入密鑰。", "Endpoint": "端點", "Enter the endpoint url here and make sure to add https:// in url.": "在此輸入端點 URL，並確保在 URL 中添加 https://。", "Site Backup": "網站備份", "Enter the region here.": "在此輸入地區。", "Team Invitations": "團隊邀請", "Team Management": "團隊管理", "Add New Team": "新增團隊", "It looks like you haven’t created any team yet. Create one now.": "看起來您還沒有建立任何團隊。立即建立一個。", "Profile": "個人資料", "General Information": "一般資訊", "Set up your profile by providing the following information": "請提供以下資訊以設定您的個人檔案", "Contact Number": "聯絡電話", "Extra Billing Information": "額外帳單資訊", "If you require the inclusion of specific contact or tax details on your receipts, such as your VAT identification number, or registered address, you may add it here.": "如果您需要在收據上包含特定的聯絡或稅務資訊，例如您的增值稅識別號或註冊地址，您可以在此添加。", "Name on Invoice": "發票上的名稱", "Billing Emails": "帳單電子郵件", "If": "如果", "Send billing invoices only to the team email address": "僅將帳單發送至團隊電子郵件地址", "is unchecked, billing invoices will be sent to the your given email addresses, separated by commas. Example:": "未勾選時，帳單發票將發送至您提供的電子郵件地址，以逗號分隔。範例：", "remaining.": "剩餘", "You have invited the following persons.": "您已邀請以下人員。", "Note": "備註", "You can again enable this service.": "您可以再次啟用此服務。", "Execute": "執行", "Proceeding will permanently delete all data stored on this server and all WordPress sites under it, which can’t be recovered later. Full Server Backups created for this server will also be deleted.": "繼續操作將永久刪除儲存在此伺服器上的所有資料及其下的所有 WordPress 網站，無法恢復。為此伺服器建立的完整伺服器備份也將被刪除。", "Delete From Provider": "從提供者刪除", "Enable this to delete this server from the provider also.": "啟用此選項以同時從供應商刪除此伺服器。", "Type server name to confirm": "輸入伺服器名稱以確認", "Delete DNS records from your Cloudflare account": "從您的 Cloudflare 帳戶中刪除 DNS 記錄", "Your DNS records for the sites on this server will be deleted from your Cloudflare account.": "您在此伺服器上的網站的 DNS 記錄將從您的 Cloudflare 帳戶中刪除。", "Add site": "新增網站", "sites": "網站", "Storage": "儲存空間", "Ram": "記憶體", "No sites": "無網站", "Add New Site": "新增網站", "View Sites": "檢視網站", "Restart Server": "重新啟動伺服器", "Hard Reboot Server": "強制重啟伺服器", "Restart Nginx": "重新啟動 Nginx", "Restart LiteSpeed": "重新啟動 LiteSpeed", "Restart MySQL": "重新啟動 MySQL", "Restart MariaDB": "重新啟動 MariaDB", "Archive Server": "歸檔伺服器", "Clone Server": "克隆伺服器", "Delete Server": "刪除伺服器", "View Server": "檢視伺服器", "Individual Site Details": "個別網站詳情", "Site Name": "網站名稱", "CPU": "中央處理器", "RAM": "記憶體", "DISK": "磁碟", "LAST UPDATED": "最後更新日期", "EC2": "EC2", "Flexible, scalable virtual servers for all workloads.": "適用於所有工作負載的彈性、可擴展虛擬伺服器。", "Lightsail": "Lightsail", "Easy, budget-friendly servers for small projects.": "適合小型專案的簡單且經濟實惠的伺服器。", "Set Up Your Server With": "使用以下工具設定您的伺服器", "Connect xCloud with your AWS Account": "將 xCloud 連接到您的 AWS 帳戶", "Connect New Account": "連接新帳戶", "Verified": "已驗證", "Next": "下一步", "Provider Label": "提供者標籤", "Connected": "已連接", "Select AWS Service": "選擇 AWS 服務", "Please choose an AWS service to create an instance.": "請選擇一個 AWS 服務來建立實例。", "Choose region": "選擇地區", "Choose Region": "選擇地區", "Choose Zone": "選擇區域", "Loading zones...": "載入區域...", "Choose Server Size": "選擇伺服器大小", "Loading server types...": "正在載入伺服器類型...", "Select Database Server": "選擇資料庫伺服器", "Select Tags": "選擇標籤", "I have understood that the billing of this server will be handled on my server provider account.": "我已了解此伺服器的帳單將由我的伺服器供應商帳戶處理。", "Demo Server for Billing Plan": "計費方案示範伺服器", "Connect xCloud with your Digital Ocean account": "將 xCloud 連接到您的 Digital Ocean 帳戶", "Existing DigitalOcean Credential": "現有的 DigitalOcean 憑證", "Choose Credential": "選擇憑證", "Add new credential": "新增憑證", "Authorize on Digital Ocean": "在 Digital Ocean 上授權", "You do not have permission to add new provider": "您沒有權限新增提供者", "Enable Digital Ocean": "啟用 Digital Ocean", "Auto Backups": "自動備份", "Connect xCloud with your Google Cloud Platform account": "將 xCloud 連接到您的 Google Cloud Platform 帳戶", "Select Existing or Connect New": "選擇現有或連接新建", "Finish": "完成", "Choose your project": "選擇您的專案", "Choose Project": "選擇專案", "Connect xCloud with your Hetzner Account": "將 xCloud 連接到您的 <PERSON><PERSON><PERSON> 帳戶", "Choose Account": "選擇帳戶", "Hetzner API Token": "Hetzner API 金鑰", "Connect xCloud with your Linode (Akamai) account": "將 xCloud 連接到您的 Linode (Akamai) 帳戶", "Existing Linode Credential": "現有的 Linode 憑證", "Authorize on Linode (Akamai)": "在 <PERSON><PERSON> (Akamai) 上授權", "Connect xCloud with your Linode Account": "將 xCloud 連接到您的 Linode 帳戶", "Vultr Label": "Vultr 標籤", "Connect xCloud with your Vultr Account": "將 xCloud 連接到您的 Vultr 帳戶", "Enable Vultr": "啟用 Vultr", "I am sure that I've read the": "我確定我已經閱讀了", "and added Any": "新增任何", "and Any": "和任何", "both under Access Control.": "在存取控制下。", "Fill in the details below to get your server set up with xCloud": "請填寫以下詳細資訊以設定您的伺服器與 xCloud 連接", "Note down the generated database root password above and secure it as it will not be displayed again.": "請記下上面生成的資料庫根密碼並妥善保管，因為它不會再次顯示。", "Server Details": "伺服器詳情", "Server Tag(Optional)": "伺服器標籤（選填）", "Server Type": "伺服器類型", "General": "一般", "Cost-effective servers powered by Intel CPUs and regular SSDs.": "由 Intel CPU 和普通 SSD 驅動的高性價比伺服器。", "Premium": "高級版", "Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.": "超高速伺服器配備3GHz以上的Intel Xeon處理器、快速記憶體和NVMe儲存。", "Billed Monthly": "每月計費", "SSD": "固態硬碟", "vCPU": "虛擬CPU", "Bandwidth": "頻寬", "Recommended Option": "推薦選項", "Backup": "備份", "Enable xCloud Auto Backups": "啟用 xCloud 自動備份", "Total Cost": "總成本", "Coupon Code": "優惠代碼", "Apply": "套用", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks.": "停用自動備份意味著失去簡便的資料恢復、備份移動性和災難恢復；如果您了解風險，請點擊繼續。", "Disable Auto Backup": "停用自動備份", "Choose Your Continent": "選擇您的洲別", "Enter Code Here...": "在此輸入代碼...", "Fill in the details below to get your server set up with": "請填寫以下詳細資訊以設置您的伺服器", "Connect Your Existing Server": "連接您現有的伺服器", "Fill in the details below to connect xCloud with your existing fresh Ubuntu server from any cloud provider.": "請填寫以下詳細資訊，以將 xCloud 連接到您現有的任何雲端供應商的 Ubuntu 伺服器。", "You will need to add the xCloud public key to allow server setup access. Please SSH to the server using the default/root account and use the following command to add the public key.": "您需要新增 xCloud 公鑰以允許伺服器設置存取。請使用預設/根帳戶 SSH 連接到伺服器，並使用以下命令新增公鑰。", "I have created a new server on my provider": "我已在我的供應商上建立了一個新伺服器", "Choose Your Server Provider": "選擇您的伺服器提供商", "Choose Hosting Managed by": "選擇託管管理者", "Everything You Need to Create a Website": "建立網站所需的一切", "Bring and Manage Your Own Server": "自帶並管理您的伺服器", "Integrate Your Own Provider.": "整合您自己的提供者。", "Manage": "管理", "First Server": "第一伺服器", "10 Sites": "10 個網站", "without any cost.": "免費", "Create More, Pay Less upto": "創造更多，支付更少", "Start": "開始", "LTD Server Created": "伺服器已建立", "Create from package": "從套件建立", "You’re on the xCloud Free plan, which includes 1 server and 10 websites with self-hosting. Activate your": "您目前使用的是 xCloud 免費方案，包含 1 台伺服器和 10 個自我託管的網站。啟用您的", "team by adding a": "團隊中新增一個", "payment method": "付款方式", "today.": "今天。", "team by adding": "新增團隊", "payment method.": "付款方式", "Choose Provider": "選擇提供者", "Only Available for LTD Users": "僅限LTD用戶使用", "is pending for setup.": "待設定。", "Automatic Reboot": "自動重啟", "Enable Backup": "啟用備份", "Disable Backup": "停用備份", "This setting is to enable/disable the backup settings of cloud provider.": "此設定用於啟用/停用雲端提供商的備份設定。", "The charge will be": "收費將為", "for this server.": "伺服器專用。", "You will be charged based on the provider policy.": "您將根據供應商政策被收費。", "To prevent system abuse, the backup feature cannot be repeatedly turned on and off. Contact our support if you need any help.": "為防止系統濫用，備份功能無法反覆開啟和關閉。如需協助，請聯繫我們的支援團隊。", "Next Backup Schedule": "下次備份排程", "Select Backup Time (UTC)": "選擇備份時間 (UTC)", "Backup List on Cloud Provider": "雲端提供者備份清單", "No backup found.": "未找到備份。", "If you enable backup then you will be charged": "啟用備份後將會收費", "for this.": "為此。", "based on the provider policy.": "根據提供者政策。", "If you disable backup then you will not be able to restore the backup for the server. However, you can enable it anytime.": "如果您停用備份，將無法還原伺服器的備份。不過，您可以隨時啟用。", "If you proceed, this will restore the backup for the server. This operation is irreversible.": "如果您繼續，這將還原伺服器的備份。此操作無法撤銷。", "Do you want to set the backup schedule for the server": "您要設定伺服器的備份排程嗎？", "Backup Schedule Details": "備份排程詳細資訊", "Some features may not work due to a payment issue.": "由於付款問題，某些功能可能無法運作。", "Payment failed": "付款失敗", "Please update your": "請更新您的", "to retry billing.": "重試付款", "Note: Your billing period has been extended until": "注意：您的計費週期已延長至", "Please pay your outstanding invoices": "請支付您的未付款發票", "to avoid any service interruptions.": "避免任何服務中斷。", "Add Cron Job": "新增排程工作", "Command": "指令", "You can add wget and curl commands here. But if you want to add wp-cli commands then you need to specify the site location with cd /var/www/sitename && wp command.": "您可以在此添加 wget 和 curl 命令。但如果您想添加 wp-cli 命令，則需要使用 cd /var/www/sitename && wp 命令指定網站位置。", "User": "使用者", "Frequency": "頻率", "Custom Schedule": "自訂排程", "Save": "儲存", "Add Sudo User": "新增 Sudo 使用者", "Sudo User Name": "管理員使用者名稱", "Sudo Password": "Sudo 密碼", "Add SSH Key": "新增 SSH 金鑰", "No SSH Keys are available. Please add SSH Key.": "沒有可用的 SSH 金鑰。請新增 SSH 金鑰。", "Cron Job": "排程工作", "Scroll to end": "滾動至底部", "Scroll to top": "返回頂部", "Last checked": "上次檢查時間", "Add Database": "新增資料庫", "Loading...": "載入中...", "No associated site": "無關聯的網站", "No database found!": "找不到資料庫！", "Database Users": "資料庫使用者", "No database user found!": "找不到資料庫使用者！", "xcloud_db": "xcloud_db", "Database User Name": "資料庫使用者名稱", "User (Optional)": "使用者（可選）", "Password (Required with user)": "密碼（用戶必填）", "Add Database User": "新增資料庫使用者", "Can Access": "可存取", "Keep empty to keep the same password": "保持空白以維持相同密碼", "If you proceed, this is permanently removed and you will not be able to access or retrieve it again.": "如果您繼續，此項目將被永久刪除，您將無法再次訪問或恢復。", "Update Cron Job": "更新排程任務", "Update Sudo User": "更新 Sudo 使用者", "Reset Sudo Password": "重設 Sudo 密碼", "Add New Rule": "新增規則", "Protocol": "協議", "Traffic": "交通", "Active": "啟用", "Fail2Ban Management": "Fail2Ban 管理", "Ban New IP Address": "禁止新 IP 位址", "Banned IP Addresses": "禁止的 IP 位址", "No banned IP addresses.": "無被禁止的 IP 位址。", "SSH": "SSH", "port": "埠", "You can use multiple ports separated by comma. Leave empty to allow all ports. Also you can use a range of ports by using colon(i.e 6000:7000).": "您可以使用逗號分隔多個端口。留空以允許所有端口。您也可以使用冒號指定端口範圍（例如 6000:7000）。", "IP Address (Optional)": "IP 位址（選填）", "Valid IP address": "有效的 IP 位址", "You can use multiple IP addresses separated by comma. Leave empty to allow all IP addresses. Also you can use a subnet. (i.e *************, *************, *************/24)": "您可以使用逗號分隔多個 IP 位址。留空以允許所有 IP 位址。您也可以使用子網路。（例如：*************, *************, *************/24）", "All": "全部", "TCP": "TCP", "UDP": "UDP", "Allow": "允許", "Deny": "拒絕", "Adding Rule...": "新增規則...", "Add Rule": "新增規則", "Enter the IP address you want to ban. You can use comma to separate multiple IP addresses (e.g xx.xx.xx.xx, xx.xx.xx.xx)": "輸入您要封鎖的 IP 位址。您可以使用逗號分隔多個 IP 位址（例如 xx.xx.xx.xx, xx.xx.xx.xx）。", "Banning IP...": "封鎖 IP 中...", "Ban IP": "封鎖 IP", "All Servers": "所有伺服器", "All Web Servers": "所有網頁伺服器", "Nginx": "<PERSON><PERSON><PERSON>", "OpenLiteSpeed": "OpenLiteSpeed", "Provisioned": "已配置", "Provisioning": "佈建", "Upgrade Server": "升級伺服器", "Server Name": "伺服器名稱", "Choose Your Server Size": "選擇伺服器大小", "Be cautious when upgrading to a larger hosting plan since you can’t switch back. Also, it’s crucial to back up important data before trying to resize partitions, which can be risky.": "升級到更大的主機方案時請謹慎，因為無法切換回去。此外，在嘗試調整分區大小之前，務必備份重要資料，因為這可能存在風險。", "Can not perform server resize action on disconnected server.": "無法對已斷線的伺服器執行伺服器調整操作。", "Current Status": "目前狀態", "After Upgrading": "升級後", "RAM Size": "記憶體大小", "SSD Space": "SSD 空間", "Server Utilities": "伺服器工具", "Delete your Server": "刪除伺服器", "This action is irreversible, and will also remove the server from your provider, and all data will be erased. We recommend that you keep a backup before you proceed.": "此操作無法撤銷，並且將從您的供應商中移除伺服器，所有資料將被刪除。我們建議您在繼續之前保留備份。", "Warning: Before you upgrade": "警告：升級前", "This will require a HARD restart and the process can take up to 15 min. It is recommended to take a full server backup of your server before proceeding.": "這將需要進行強制重啟，過程可能需要長達 15 分鐘。建議在繼續之前對伺服器進行完整備份。", "New Plan": "新方案", "If you upgrade now, you’ll be charged": "如果您現在升級，將會收費", "Provider Name": "提供者名稱", "Ubuntu Version": "Ubuntu 版本", "MySQL Version": "MySQL 版本", "Full Server Migrations": "完整伺服器遷移", "Initiate Full Server Migration": "啟動完整伺服器遷移", "Public Ip": "公用 IP", "Updated": "已更新", "In Use": "使用中", "Available": "可用", "Utilization": "使用率", "Cores": "核心", "Speed/Core": "速度/核心", "Threads": "線程", "Hard Disk Usage": "硬碟使用情況", "Total": "總計", "Used": "已使用", "Uptime Overview": "運行時間概覽", "Select your site's PHP version to update php settings on the sites.": "選擇您的網站 PHP 版本以更新網站的 PHP 設定。", "Max Execution Time": "最大執行時間", "The maximum time in seconds a script is allowed to run before it is terminated by the parser. We recommend 60 seconds.": "腳本允許執行的最長時間（以秒為單位），超過此時間將被解析器終止。我們建議設置為 60 秒。", "Max Input Time": "最大輸入時間", "The maximum time in seconds a script is allowed to parse input data, like POST and GET. We recommend 60 seconds.": "腳本允許解析輸入資料（如 POST 和 GET）的最大時間（以秒為單位）。我們建議設置為 60 秒。", "Max Input Vars": "最大輸入變數", "The maximum number of input variables allowed per request. We recommend 1000 vars.": "每個請求允許的最大輸入變數數量。我們建議使用 1000 個變數。", "Memory Limit": "記憶體限制", "The maximum amount of memory a script may consume. We recommend 256MB.": "腳本可能使用的最大記憶體量。我們建議 256MB。", "Post Max Size": "上傳大小上限", "The maximum size of POST data that PHP will accept. We recommend 128MB.": "PHP 可接受的 POST 資料最大大小。我們建議 128MB。", "Max File Upload Size": "最大檔案上傳大小", "The maximum size of an uploaded file. We recommend 128MB.": "上傳檔案的最大大小。我們建議 128MB。", "Session GC Maxlifetime": "會話 GC 最大存活時間", "The maximum time in seconds a session is allowed to be idle before it is terminated by the session garbage collector. We recommend 1440 seconds.": "會話閒置的最長時間（以秒為單位），超過此時間後會話將被會話垃圾收集器終止。我們建議設置為 1440 秒。", "Important Note": "重要提示", "Enabling PHP OPCache will highly improve performance by storing precompiled scripts (PHP Code) in shared memory.": "啟用 PHP OPCache 將透過在共享記憶體中儲存預編譯腳本（PHP 代碼）大幅提升效能。", "If you allow optimizing your OPCache, you need to make sure that your deployment parse code reloads the PHP FPM services at the end of each development.": "如果您允許優化您的 OPCache，請確保您的部署解析代碼在每次開發結束時重新載入 PHP FPM 服務。", "Disable OPCache": "停用 OPCache", "Enable OPCache": "啟用 OPCache", "Server Statistics": "伺服器統計資料", "Disable it to turn off magic login feature on all sites under this server.": "停用此功能以關閉此伺服器下所有網站的魔術登入功能。", "Back To Servers": "返回伺服器", "Team": "團隊", "WEB SERVER": "網頁伺服器", "Disk Usage": "磁碟使用情況", "Checked": "已檢查", "Check Again": "重新檢查", "Your disk space is low. Please upgrade your plan to avoid any downtime.": "您的磁碟空間不足。請升級您的方案以避免停機。", "Close": "關閉", "No sites on this server": "此伺服器上無網站", "Get started by creating a new site!": "開始建立新網站！", "Sudo Users": "Sudo 使用者", "Enable Vulnerability Scan": "啟用漏洞掃描", "Enable Auto Update": "啟用自動更新", "If vulnerability found we will update in 24 hours": "如發現漏洞，我們將在24小時內更新。", "Enable Auto Backup": "啟用自動備份", "Select All Sites": "全選網站", "Choose a Server to Clone Sites from": "選擇伺服器以複製網站", "Team Details": "團隊詳情", "Create a new team to collaborate with others on projects.": "建立新團隊以便與他人協作專案。", "If you proceed, this will remove all the services and sites along with other data which cannot be recovered.": "如果您繼續，此操作將刪除所有服務和網站以及其他無法恢復的數據。", "I have understood and would like to proceed.": "我已了解並願意繼續。", "Permanently delete this team.": "永久刪除此團隊。", "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.": "一旦刪除團隊，其所有資源和數據將被永久刪除。在刪除此團隊之前，請下載您希望保留的任何有關此團隊的數據或信息。", "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.": "您確定要刪除此團隊嗎？刪除後，該團隊的所有資源和數據將被永久刪除。", "Add Team Member": "新增團隊成員", "Add a new team member to your team, allowing them to collaborate with you.": "新增團隊成員，讓他們與您協作。", "Please provide the email address of the person you would like to add to this team.": "請提供您想要新增至此團隊的人員的電子郵件地址。", "Added.": "已新增。", "Add": "新增", "Pending Team Invitations": "待處理的團隊邀請", "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.": "這些人已被邀請加入您的團隊，並已收到邀請電子郵件。他們可以通過接受電子郵件邀請來加入團隊。", "All of the people that are part of this team.": "此團隊的所有成員。", "Leave": "離開", "Remove": "移除", "Manage Role": "管理角色", "Are you sure you would like to leave this team?": "您確定要離開這個團隊嗎？", "Remove Team Member": "移除團隊成員", "Are you sure you would like to remove this person from the team?": "您確定要將此人從團隊中移除嗎？", "The team's name and owner information.": "團隊名稱和擁有者資訊。", "Saved.": "已儲存。", "Create Team": "建立團隊", "Team Settings": "團隊設定", "Join the Exclusive Waitlist!": "加入專屬候補名單！", "Your Email Address..": "您的電子郵件地址", "Join Waitlist": "加入候補名單", "Already have been invited?": "已收到邀請？", "Login to xCloud": "登入 xCloud", "When you cancel migration, already migrated data will be removed from the server.": "取消遷移時，已遷移的資料將從伺服器中移除。", "Cancel Migration": "取消遷移", "Auth": "驗證", "Cache": "快取", "Git": "Git", "Proceeding will permanently delete this Site and all of its data.": "繼續操作將永久刪除此網站及其所有數據。", "Delete All Files and Configurations.": "刪除所有檔案和設定。", "Do You Want To Delete Files?": "您要刪除檔案嗎？", "Delete Database": "刪除資料庫", "Do You Want To Delete Database?": "您是否要刪除資料庫？", "Delete Site User": "刪除網站使用者", "Do You Want To Delete User?": "您確定要刪除使用者嗎？", "Delete Local Backups": "刪除本地備份", "Do You Want To Delete Local Backups?": "您要刪除本機備份嗎？", "Type site name to confirm": "輸入網站名稱以確認", "Delete DNS record from your Cloudflare account": "從您的 Cloudflare 帳戶刪除 DNS 記錄", "Your DNS record for the site on this server will be deleted from your Cloudflare account.": "您在此伺服器上的網站的 DNS 記錄將從您的 Cloudflare 帳戶中刪除。", "Proceeding will disable this Site.": "繼續操作將停用此網站。", "Disable Site": "停用網站", "Also Disable Cron For This Site": "同時停用此網站的 Cron", "Disable HTML Markup": "停用 HTML 標記", "After turning on Disable Site, you won’t be able to use this site on the web anymore along with losing access through SFTP/SSH. Plus, all the scheduled tasks for the site will be paused once you turn on Disable Cron For This Site.": "啟用「停用網站」後，您將無法再透過網頁使用此網站，也將失去 SFTP/SSH 的訪問權限。此外，一旦啟用「停用此網站的排程任務」，該網站的所有排程任務將會暫停。", "Disable": "停用", "Site is currently disabled.": "網站目前已停用。", "Click here": "點擊這裡", "to enable this site": "啟用此網站", "The maximum amount of memory a script may consume. we recommend 128MB.": "腳本可使用的最大記憶體量。我們建議 128MB。", "The number of seconds after which data will be seen as 'garbage' and potentially cleaned up. we recommend 1440 seconds.": "資料在多少秒後會被視為「垃圾」並可能被清理。我們建議設定為 1440 秒。", "Max PHP Workers": "最大 PHP 工作者數", "The number of PHP workers (pm.max_children) that can be spawned to handle requests.": "可生成以處理請求的 PHP 工作進程數量 (pm.max_children)。", "Redirect Type": "重新導向類型", "Choose Redirect Type": "選擇重定向類型", "From": "從", "To": "至", "View": "檢視", "Magic Login": "魔法登入", "Site Logs": "站點日誌", "Site Events": "網站活動", "Purge Cache": "清除快取", "Clone Site": "複製網站", "Delete Site": "刪除網站", "Create New Database": "建立新資料庫", "Database Cluster Name": "資料庫叢集名稱", "Cluster Name": "叢集名稱", "Select Existing Database Cluster": "選擇現有資料庫叢集", "Additional Domain Name": "額外的網域名稱", "Additional Domain (Optional)": "其他網域（選填）", "Add Domain": "新增網域", "Admin Password": "管理員密碼", "Admin Email Address": "管理員電子郵件地址", "WordPress Version": "WordPress 版本", "Prefix": "字首", "wp_": "wp_", "Full Page Caching": "全頁快取", "Redis Object Caching": "Redis 物件快取", "Could not connect to the database server. Please check your database credentials and try again.": "無法連接到資料庫伺服器。請檢查您的資料庫憑證並重試。", "Database Username": "資料庫使用者名稱", "Database Host": "資料庫主機", "Database Port": "資料庫埠", "Connection URL": "連接 URL", "Please use the database connection URL provided to establish a connection between your database client and the database. We suggest using TablePlus as your database client.": "請使用提供的資料庫連接 URL 來建立您的資料庫客戶端與資料庫之間的連接。我們建議使用 TablePlus 作為您的資料庫客戶端。", "Add the following record to the DNS for your domain. This required when when you want to get a free SSL certificate issued & managed by": "將以下記錄新增至您的網域的 DNS。當您希望獲取由...發行和管理的免費 SSL 憑證時，這是必需的。", "If you have manually added DNS record with Cloudflare proxy then the verify option will not work": "如果您手動添加了使用 Cloudflare 代理的 DNS 記錄，那麼驗證選項將無法運作。", "Record": "記錄", "A": "A", "Verify My DNS": "驗證我的 DNS", "In your domain settings, you need to add the following records for configuring your site email.": "在您的網域設定中，您需要新增以下記錄來配置您的網站電子郵件。", "Record(SPF)": "記錄（SPF）", "TXT": "文字", "Value": "值", "CNAME": "CNAME", "Record(DKIM)": "記錄（DKIM）", "Record(DMARC)": "記錄（DMARC）", "Verify Record": "驗證記錄", "Use free SSL certificate issued & managed by": "使用由...發行和管理的免費 SSL 憑證", "I will provide the certificate and manage it myself.": "我將提供證書並自行管理。", "Certificate": "證書", "Paste Your Certificate Here": "在此處貼上您的證書", "Private Key": "私鑰", "Paste Your Private Key Here": "在此處貼上您的私鑰", "Enabling HTTPS makes your website more secure.": "啟用 HTTPS 可提升您的網站安全性。", "Learn more": "了解更多", "Beta": "測試版", "Install New WordPress Website": "安裝新的 WordPress 網站", "Select this option if you want to a create a fresh new WordPress website": "如果您想建立全新的 WordPress 網站，請選擇此選項", "Clone a Git Repository": "複製 Git 儲存庫", "Clone a git repository to create a new website": "複製 Git 儲存庫以建立新網站", "Migrate An Existing WordPress Website": "遷移現有的 WordPress 網站", "Have an existing website already? Select this option to migrate it with ": "已經有現有網站了嗎？選擇此選項以進行遷移", "Manually Upload WordPress Website": "手動上傳 WordPress 網站", "Upload a zipped file of your existing WordPress website": "上傳您現有 WordPress 網站的壓縮檔案", "Migrate Full Server": "遷移完整伺服器", "Migrate all WordPress sites from Ubuntu servers with a few clicks": "一鍵遷移所有 WordPress 網站從 Ubuntu 伺服器", "Certificate Issue": "證書發行", "Expires On": "到期日", "Renew Date": "續約日期", "Add Your New Site Into": "將您的新網站新增到", "This server created under xCloud Free plan which includes 1 server and": "此伺服器是在 xCloud 免費方案下建立的，包含 1 個伺服器和", "website with Self Hosting. To unlock full features, please consider upgrading the server bill from free to paid plan.": "網站自我託管。若要解鎖完整功能，請考慮將伺服器帳單從免費升級到付費方案。", "Your": "您的", "is low. Please upgrade your plan to avoid any downtime.": "電量不足。請升級您的方案以避免停機。", "Choose a Server to add Site": "選擇伺服器以新增站點", "Site Title": "網站標題", "New Site Title": "新網站標題", "Add Tag (optional)": "新增標籤（選填）", "Go Live": "開始直播", "(Optional)": "（選填）", "Integrate Cloudflare forAutomatic DNS and SSL management.": "整合 Cloudflare 以自動管理 DNS 和 SSL。", "If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.": "如果您使用的其他網域不屬於主要網域，則需要切換到 xCloud SSL。", "WordPress Multisite": "WordPress 多站點", "Enable Multisite": "啟用多站點", "Select Multisite Type": "選擇多站點類型", "You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.": "網站建立後，您需要從網站的 SSL/HTTPS 頁面為多站點子域安裝 SSL。", "Enhancing Website Performance": "提升網站效能", "Speed up your website by using smart caching!": "使用智能快取加速您的網站！", "Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "結合全頁快取和 Redis 來加速您的網站，提升訪客體驗。", "Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.": "啟用 LiteSpeed Cache 以加速網站運行，提升訪客體驗。", "LiteSpeed Cache": "LiteSpeed 快取", "Email Provider Configuration": "電子郵件提供者設定", "Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.": "每月免費獲得 100 封電子郵件以啟動您的業務。如需使用您自己的網域，請停用此選項。", "Read our documentation": "閱讀我們的文件", "Blueprints": "藍圖", "Choose a blueprint to install WordPress with pre-configured plugins and themes.": "選擇一個藍圖來安裝 WordPress，包含預配置的插件和主題。", "All Blueprints": "所有藍圖", "Manage your all blueprints as you like, you can edit, delete or create new from here": "在此管理您的所有藍圖，您可以編輯、刪除或創建新的。", "OK": "確定", "Create Your New Site Into": "建立您的新網站", "xCloud Playground": "xCloud 遊樂場", "Playground Environment": "遊樂場環境", "This demo site will expire 24 hours after creation.": "此示範網站將在建立後24小時過期。", "Staging Domain Setup": "暫存域設定", "This will be auto-generated according to your site title": "這將根據您的網站標題自動生成", "Speed up your website by using smart caching! Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "使用智慧快取加速您的網站！結合全頁面快取和 Redis，讓您的網站運行更快，為訪客提供更佳的體驗。", "Step 1 of 3": "第 1 步，共 3 步", "Hey you are trying to access the playground site": "嘿，你正在嘗試訪問遊樂場網站", "but you are not in the": "但您不在", "We request you to check your email and accept the invitation to join and edit the site.": "請檢查您的電子郵件並接受邀請以加入和編輯網站。", "Enable Adminer": "啟用 Adminer", "Adminer can enhance your database management experience. Activate it only when necessary to ensure optimal security measures.": "Adminer 可以提升您的資料庫管理體驗。僅在必要時啟用，以確保最佳安全措施。", "Database Manager": "資料庫管理員", "Manage your databases with Adminer, an opensource database management tool.": "使用 Adminer 管理您的資料庫，這是一個開源的資料庫管理工具。", "Launch Adminer": "啟動 Adminer", "Basic Authentication": "基本驗證", "Protect Your Site": "保護您的網站", "Turn on to enable basic authentication for your site by adding username and password.": "開啟以啟用網站的基本驗證，新增使用者名稱和密碼。", "Previous Remote Backups": "先前的遠端備份", "Backup Now": "立即備份", "File": "檔案", "Previous Local Backups": "先前的本地備份", "Remote Backup Settings": "遠端備份設定", "Backup Type": "備份類型", "Select Bucket": "選擇儲存桶", "Server Bucket": "伺服器儲存桶", "Manage Your Storage Providers": "管理您的儲存供應商", "Backup Items": "備份項目", "Database Backup": "資料庫備份", "will be backed up.": "將備份。", "Files Backup": "檔案備份", "Exclude Paths": "排除路徑", "Automatic Backup": "自動備份", "Automatic Full Backup": "自動完整備份", "Select Backup Frequency": "選擇備份頻率", "Select Full Backup Frequency": "選擇完整備份頻率", "Automatic Incremental Backup": "自動增量備份", "Automatic Delete": "自動刪除", "Delete After Days": "刪除天數後", "30": "30", "Local Backup Settings": "本機備份設定", "Select Incremental Backup Frequency": "選擇增量備份頻率", "Change": "更改", "Additional Domains": "其他網域", "Add new": "新增", "After changing the domain, you need to install SSL for Multisite Subdomain from the site SSL/HTTPS page.": "變更網域後，您需要從網站的 SSL/HTTPS 頁面安裝多站點子網域的 SSL。", "Connect New Provider": "連接新供應商", "Use xCloud Domain": "使用 xCloud 網域", "Use Your Own Domain": "使用您自己的網域", "Requires Verification": "需要驗證", "From Name": "寄件者名稱", "This is the name that will appear in the recipient's inbox.": "這是將顯示在收件者收件匣中的名稱。", "To use your own domain, please verify your domain with xCloud by following the DNS setup below.": "若要使用您自己的網域，請按照以下 DNS 設定驗證您的網域以連接 xCloud。", "Clear Provider": "清除提供者", "We only use the Fluent SMTP plugin to configure your given SMTP credentials on the sites, ": "我們僅使用 Fluent SMTP 插件來配置您在網站上的 SMTP 憑證。", "so that you do not have to do anything manually. If you are not getting emails then please ": "以免您需要手動操作。如果您沒有收到電子郵件，請", "check your email logs from your email provider. To test your email integration, please send a test email from your fluentsmtp plugin.": "檢查您的電子郵件提供商的郵件記錄。要測試您的電子郵件整合，請從您的 FluentSMTP 插件發送測試郵件。", "Enable File Manager": "啟用檔案管理器", "Tiny File Manager": "小型檔案管理器", "Activate it only when necessary to ensure optimal security measures.": "僅在必要時啟用，以確保最佳安全措施。", "File Manager": "檔案管理器", "Manage your files, an opensource tool.": "管理您的檔案，開源工具。", "Launch File Manager": "啟動檔案管理器", "Page Caching": "頁面快取", "FastCGI Nginx": "FastCGI Nginx", "Cache Duration": "快取持續時間", "Unit": "單位", "Cache Exclusion HTTP URL Rules": "快取排除 HTTP URL 規則", "Cache Exclusion Cookie Rules": "快取排除 Cookie 規則", "Clear Page Cache": "清除頁面快取", "This will slow down your site until the caches are rebuilt.": "這將減慢您的網站速度，直到快取重建完成。", "Purging Cache...": "正在清除快取...", "Action will be available once the Object Cache operation is finished": "操作快取完成後，動作將可用。", "Object Cache": "物件快取", "Redis User": "Redis 使用者", "Redis Password": "Redis 密碼", "Redis Object Cache Key": "Redis 物件快取鍵", "Redis object cache optimize performance, reduces database load, and enhances response times for a seamless browsing experience.": "Redis 物件快取優化效能，減少資料庫負載，提升回應速度，提供流暢的瀏覽體驗。", "Stores the results of queries to the site’s database.": "儲存查詢網站資料庫的結果。", "Clear Object Cache": "清除物件快取", "Git Settings": "Git 設定", "Pull and deploy now": "立即拉取並部署", "Updated At": "更新時間", "Production": "生產", "Staging": "暫存環境", "Demo": "演示", "Vulnerable": "弱點", "Migrating": "遷移中", "Add IP Address": "新增 IP 位址", "No IP addresses added yet.": "尚未新增 IP 位址。", "IP Addresses": "IP 位址", "Updating...": "正在更新...", "Adding...": "正在新增...", "LiteSpeed Cache for WordPress (LSCWP) is an all-in-one site acceleration plugin, featuring an exclusive server-level cache and a collection of optimization features.": "LiteSpeed Cache for WordPress (LSCWP) 是一款全方位的網站加速插件，具備獨特的伺服器級快取和一系列優化功能。", "Clear LiteSpeed Cache": "清除 LiteSpeed 快取", "Showing logs from": "顯示日誌來源", "Loading..": "載入中...", "WordPress Debug Log": "WordPress 偵錯日誌", "Reload": "重新載入", "Clear": "清除", "Clearing..": "清除中...", "Your Site Has Been Successfully Migrated": "您的網站已成功遷移", "Staging URL": "暫存網址", "Setup Email for this site": "為此網站設定電子郵件", "Monitoring Stats": "監控統計", "CPU Usages": "CPU 使用率", "SSL Overview": "SSL 概覽", "DNS Status": "DNS 狀態", "SSL Status": "SSL 狀態", "Expiry": "到期", "WordPress Logs": "WordPress 日誌", "Updates": "更新", "Update Available": "更新可用", "Up To Date": "最新狀態", "Plugins": "插件", "Themes": "主題", "Custom Nginx Config": "自訂 Nginx 配置", "Fetching Nginx..": "正在擷取 Nginx...", "Preview Nginx": "預覽 Nginx", "Add a New Config": "新增設定配置", "Select Config Type": "選擇配置類型", "Config File Name": "設定檔名稱", "Config Content": "配置內容", "Preview Content": "預覽內容", "Running...": "執行中...", "Run & Debug": "執行與除錯", "Nginx Config File": "Nginx 配置檔案", "Save Settings": "儲存設定", "Nginx & Security": "Nginx 與安全性", "Security": "安全性", "7G Firewall": "7G 防火牆", "8G Firewall": "8G 防火牆", "Disable Nginx File Regeneration": "停用 Nginx 檔案再生產", "PHP Execution on Upload Directory": "上傳目錄的 PHP 執行", "Enable XML-RPC": "啟用 XML-RPC", "Edit X-Frame-Options": "編輯 X-Frame-Options", "Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com": "請使用以下其中之一：SAMEORIGIN、<PERSON>N<PERSON>、ALLOWALL、ALLOW-FROM https://example.com 或 ALLOW-FROM https://example2.com https://example3.com", "With xCloud you get 100 emails free per month in each team. Manage email for this site from": "在 xCloud 中，每個團隊每月可免費獲得 100 封電子郵件。從以下位置管理此網站的電子郵件：", "You can purchase more or add your own email providers from": "您可以購買更多或新增您自己的電子郵件提供者，從", "Learn more from our": "了解更多資訊，請參閱我們的", "Primary Domain": "主要網域", "Add New": "新增", "Others": "其他", "Page Cache": "頁面快取", "on": "開啟", "off": "關閉", "SSL/HTTPS": "SSL/HTTPS", "Basic Auth": "基本驗證", "No Updates": "無更新", "Most Recent Event": "最新事件", "View All Events": "查看所有活動", "Redirection": "重新導向", "Add Redirection": "新增重定向", "Redirections": "重定向", "Edit Redirection": "編輯重定向", "WP-Cron": "WP-Cron", "WP-Cron manages time-based tasks in WordPress, relying on site visits.": "WP-Cron 依賴網站訪問來管理 WordPress 中的定時任務。", "If you want to also add additional custom cronjob then you can configure on your server from": "如果您想新增自訂的 cronjob，您可以從您的伺服器進行配置。", "Edit Site Tags": "編輯網站標籤", "WP Debug": "WP 偵錯", "Enable WP Debug": "啟用 WP 偵錯", "Enable this to view WordPress debug logs on the": "啟用此功能以查看 WordPress 除錯日誌", "site's logs page": "網站日誌頁面", "Delete Site Confirmation": "刪除網站確認", "This action is irreversible. Delete sites cannot be restored.": "此操作不可逆。刪除的網站無法恢復。", "Rescue Site": "救援地點", "Run Now": "立即執行", "Repair Site User": "修復網站使用者", "Update Directory Permissions": "更新目錄權限", "Repair PHP": "修復 PHP", "Regenerate": "重新生成", "It is recommended to keep the above options turned on before running the rescue action.": "建議在執行救援操作前保持上述選項開啟。", "Back To Sites": "返回網站列表", "staging": "暫存環境", "Add Staging environment": "新增 Staging 環境", "Visit Site": "造訪網站", "Deploy Staging": "部署測試環境", "WordPress": "WordPress", "Archive": "封存", "Are You Sure You Want To Deploy Staging for  ": "您確定要部署測試環境嗎？", "A staging site is an identical copy of your production website, created for testing purposes. This isolated environment lets you test any plugin, or theme, or make any other changes before going live. This removes the risk of damaging your production website. Later, you can pull/push updates and apply to your production.": "測試網站是您的生產網站的相同副本，用於測試目的。這個獨立環境讓您可以在上線前測試任何插件、主題或進行其他更改。這樣可以避免損壞您的生產網站。之後，您可以拉取/推送更新並應用到您的生產網站。", "Staging Site Domain": "測試站點域名", "Preparing Deployment..": "準備部署中...", "Site SSH/sFTP Access": "網站 SSH/sFTP 存取權限", "Site Username": "網站用戶名", "Site Path": "網站路徑", "SSH String": "SSH 字串", "Database URL Connection": "資料庫 URL 連接", "DNS Setup For Multisite SSL": "多站點 SSL 的 DNS 設定", "Provide your own certificate & manage it yourself": "提供您自己的憑證並自行管理", "Use Cloudflare managed SSL Certificate.": "使用 Cloudflare 管理的 SSL 憑證。", "Demo Environment": "演示環境", "Staging Environment": "測試環境", "Demo Site Domain": "示範網站域名", "Staging Domain": "暫存網域", "If you’re using different additional domains then you need to switch to xCloud SSL. With Cloudflare this is not supported.": "如果您使用不同的附加網域，則需要切換到 xCloud SSL。這在 Cloudflare 上不受支持。", "Staging Management": "階段管理", "Pull Data from Production to Staging": "從生產環境拉取資料到測試環境", "Copy the changes from the live site to staging": "將變更從線上網站複製到預備環境", "Pulling Data...": "正在擷取資料...", "Pull Data": "拉取資料", "Push Data from Staging to Production": "從暫存推送資料到生產環境", "Copy the changes from the staging to live": "將變更從暫存環境複製到正式環境", "Pushing Data...": "正在傳送資料...", "Push Data": "推送資料", "Deployment Logs": "部署日誌", "View All Logs": "檢視所有日誌", "Pull data from ": "從...提取資料", "Files": "檔案", "Overwrite": "覆寫", "Incremental": "增量", "Full": "完整", "Selected Tables": "選擇的表格", "fetching tables...": "正在擷取資料表...", "Fetching tables...": "正在擷取資料表...", "Initiating Pulling...": "正在啟動拉取...", "Push data from this Staging Site to the Production site(": "將此暫存站點的資料推送至生產站點", "Push to Production": "推送至生產環境", "Your staging site data will be pushed to production. The production site may be temporarily unavailable for a while.": "您的預備網站資料將被推送到正式環境。正式網站可能會暫時無法使用。", "Select Tables": "選擇資料表", "Take backup of your production site before pushing changes(Recommended).": "在推送更改之前，請備份您的生產網站（建議）。", "Initiating Pushing...": "正在推送...", "Source Site": "來源網站", "Destination Site": "目的地網站", "Initiated By": "發起者", "Inactive": "非活動", "WordPress Core": "WordPress 核心", "Your current version is": "您目前的版本是", "Updating to": "更新至", "Your WordPress": "您的 WordPress", "is up to date.": "已是最新版本。", "Version": "版本", "Changelog": "更新日誌", "Activating...": "啟動中...", "Activate": "啟用", "Activated": "已啟用", "Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or": "目前已停用此網站的漏洞檢測。若要啟用，請前往伺服器>安全性設定。", "click here": "點擊這裡", "Insecure": "不安全", "Secure": "安全", "Last Scan": "上次掃描", "An Updated Version of WordPress is Available": "WordPress 的更新版本已可用", "CVSS Score": "CVSS 分數", "Ignored": "忽略", "Ignore": "忽略", "Patched": "修補完成", "Fixed in": "已修正於", "Remediation": "修復", "Direct URL": "直接網址", "First Published": "首次發佈", "Last Update": "最後更新", "Update Config": "更新設定", "WP Config": "WP 設定", "Invoices of Clients": "客戶發票", "Here you can check all your previous invoices": "在此您可以查看所有過去的發票", "Copy": "複製", "View Nova": "檢視 Nova", "Total Clients": "總客戶數", "Total Products": "總產品數", "Total Servers": "伺服器總數", "You don’t have any server yet": "您尚未擁有任何伺服器", "Sites": "網站", "You don’t have any site yet": "您尚未擁有任何網站", "Clients Overview": "客戶概覽", "Total Active Clients": "總活躍客戶數", "Total Inactive Clients": "總不活躍客戶", "Total Invoices": "總發票數", "You don’t have any client yet": "您尚未有任何客戶", "Access Dashboard & Website": "存取儀表板與網站", "Domain Settings": "網域設定", "Visit Landing Page": "造訪登陸頁面", "Dashboard": "儀表板", "Brand Setup": "品牌設定", "Brand Profile": "品牌簡介", "Add Your Logo": "新增您的標誌", "Upload a logo that represent your brand profile.": "上傳代表您的品牌資料的標誌。", "Please upload a logo with a minimum size of 212x40 pixels in PNG, JPEG, or JPG format (max 5MB) for best quality.": "請上傳一個最小尺寸為 212x40 像素的 PNG、JPEG 或 JPG 格式的標誌（最大 5MB）以獲得最佳品質。", "Upload Logo": "上傳標誌", "Cloud Hosting": "雲端託管", "Brand Name": "品牌名稱", "<EMAIL>": "<EMAIL>", "Support Email": "支援電子郵件", "579 Spruce Court, Dallas, TX 75201": "579 Spruce Court, Dallas, TX 75201", "Address": "地址", "Copyright Name": "版權名稱", "Processing...": "處理中...", "Proceed to Checkout": "前往結帳", "Sell up to": "銷售高達", "Checkout": "結帳", "Your Order Summary": "您的訂單摘要", "Sub Total": "小計", "Processing Payment...": "正在處理付款...", "Processing Offer...": "處理優惠中...", "Claim Free": "領取免費", "Create Product": "建立產品", "Create Hosting Plan": "建立主機方案", "Plan Name": "方案名稱", "Renewal Type": "續訂類型", "SKU": "SKU", "Setup Products & Start Selling": "設定產品並開始銷售", "Add Your Domain Name": "新增您的網域名稱", "example.com": "範例.com", "Add the following record to the DNS manager for your domain": "將以下記錄新增至您的網域的 DNS 管理員", "Skip this step, if you are not ready to point your domain server.": "如果您尚未準備好指向您的域名伺服器，請跳過此步驟。", "Payment Setup": "付款設定", "Integrate with Stripe Connect": "與 Stripe Connect 整合", "Connect to your existing Stripe account or create a new account to start processing payments via Stripe.": "連接到您現有的 Stripe 帳戶或創建新帳戶以開始通過 Stripe 處理付款。", "Connect Now": "立即連接", "Stripe Account": "Stripe 帳戶", "If your Stripe account is set up correctly, it will automatically connect within 1–2 minutes.": "如果您的 Stripe 帳戶設置正確，將在 1–2 分鐘內自動連接。", "If you’ve just created a new Stripe business account, it may take up to 3 days for Stripe to verify it.Once verified, your account should connect automatically.": "如果您剛創建了一個新的 Stripe 商業帳戶，Stripe 可能需要最多 3 天來驗證。驗證後，您的帳戶應會自動連接。", "To check the status of your": "要檢查您的狀態", "account": "帳戶", "you can visit your": "您可以造訪您的", "Stripe dashboard.": "Stripe 儀表板", "Billing Account": "帳單帳戶", "Stripe Account Name": "Stripe 帳戶名稱", "Stripe Account ID": "Stripe 帳戶 ID", "Connected On": "已連接", "Billing Currency": "計費貨幣", "3D Secure Card Compatibility for Your Clients (Optional)": "3D 安全卡相容性（選用）", "Stripe Publishable Key": "Stripe 公開金鑰", "If your client prefers to pay using a 3D Secure card, you need to add your": "如果您的客戶偏好使用 3D 安全卡付款，您需要添加您的", "Stripe Publishable Key.": "Stripe 公開金鑰", "Get your publishable key": "取得您的可發布金鑰", "from here.": "從這裡開始。", "Syncing Account..": "帳戶同步中...", "Having Trouble?": "遇到問題嗎？", "Read our comprehensive documentation & learn to manage your hosting easily.": "閱讀我們的完整文件，輕鬆管理您的主機。", "Read Documentation": "閱讀文件", "Start Your Hosting Business: Resell & Earn Revenue": "開始您的主機業務：轉售並賺取收入", "Launch a cloud hosting business with xCloud Managed Servers. Resell our fully managed web hosting under your own brand or domain to maximize your revenue ensuring a reliable performance.": "使用 xCloud 管理伺服器啟動雲端代管業務。以您自己的品牌或網域轉售我們的全管理型網頁代管服務，最大化您的收益並確保穩定的效能。", "Complete control for your personal branding": "完全掌控您的個人品牌", "Manage your client billings with Stripe Connect": "使用 Stripe Connect 管理您的客戶帳單", "Customize hosting packages & sell at your own price": "自訂主機方案並以自訂價格銷售", "Get access to powerful features of xCloud": "存取 xCloud 的強大功能", "By ticking this box, you are confirming that you have read, understood, and agree to our": "勾選此框即表示您已閱讀、理解並同意我們的條款", "Please check this box to confirm that you accept the terms and conditions and want to proceed.": "請勾選此方框以確認您接受條款和條件並希望繼續。", "Before proceeding, ensure you have an active Stripe account, as all transactions are managed via": "在繼續之前，請確保您擁有一個有效的 Stripe 帳戶，因為所有交易均通過該平台管理。", "Stripe Connect": "Stripe Connect", "Please check this box to confirm that you have a Stripe account and want to proceed.": "請勾選此方框以確認您擁有 Stripe 帳戶並希望繼續。", "Start Your Hosting Business Now": "立即開始您的主機代管業務", "Product Details": "產品詳情", "Product Information": "產品資訊", "Some basic information is shared over here": "這裡顯示了一些基本資訊", "Edit Product": "編輯產品", "Checkout URL": "結帳網址", "Invoices of Product": "產品發票", "Source Product": "來源產品", "Includes Up to RAM": "包含高達 RAM", "Customize Package": "自訂套件", "My Server": "我的伺服器", "Price/Month": "每月價格", "XCPU110": "XCPU110", "Stripe": "Stripe", "will deduct a 3%-7% fee per sale. Your approximate profit for this sale is": "每筆銷售將扣除 3%-7% 的費用。您此次銷售的預計利潤是", "Active Package": "啟用套件", "Preview Plan": "預覽方案", "Save and Publish": "儲存並發佈", "Add Product": "新增產品", "Buying Price": "購買價格", "Selling Price": "售價", "Create New Product": "建立新產品", "Select Server Plan at xCloud": "選擇 xCloud 伺服器方案", "Select Server Size": "選擇伺服器大小", "Change Plan": "變更方案", "Duplicate Product": "重複產品", "Duplicate": "重複", "Favicon": "網站圖示", "Add Your Favicon": "新增您的網站圖示", "Please upload a favicon with a minimum size of 16x16 pixels in PNG or ICO format (max 1MB) for best quality.": "請上傳一個最小尺寸為 16x16 像素的 PNG 或 ICO 格式圖示（最大 1MB）以獲得最佳品質。", "Upload Favicon": "上傳網站圖示", "Custom Domain Setup": "自訂網域設定", "Your Domain Name": "您的網域名稱", "Landing Page Settings": "著陸頁設定", "Landing Page": "著陸頁面", "Enable it to use and customize the ready landing page settings": "啟用並自訂現成的登陸頁面設定", "Navbar Logo": "導覽列標誌", "Update your logo for navigation bar": "更新您的標誌以用於導航列", "Upload a logo or icon that represent your brand profile.": "上傳代表您的品牌資料的標誌或圖示。", "Hero Section": "英雄區塊", "Fast, Secure & Reliable Cloud Hosting": "快速、安全、可靠的雲端託管", "Heading": "標題", "Some more information that uou can add here": "您可以在此處添加更多資訊", "Sub Heading": "子標題", "Create Now": "立即建立", "Button Text": "按鈕文字", "Button URL": "按鈕 URL", "https://www.example.com": "https://www.example.com", "CTA Section": "行動呼籲區段", "Social Media Links": "社交媒體連結", "https://facebook.com/xcloud": "https://facebook.com/xcloud", "Facebook": "臉書", "https://instagram.com/xcloud": "https://instagram.com/xcloud", "Instagram": "Instagram", "https://x.com/xcloud": "https://x.com/xcloud", "X.com": "X.com", "https://linkedin.com/xcloud": "https://linkedin.com/xcloud", "Linkedin": "LinkedIn", "https://youtube.com/xcloud": "https://youtube.com/xcloud", "Youtube": "YouTube", "Preview": "預覽", "Saving...": "儲存中...", "Connect to Stripe": "連接到 Stripe", "Account Name": "帳戶名稱", "Stripe 3DS Card Setup": "Stripe 3DS 卡片設定", "Privacy Policy Settings": "隱私政策設定", "Default Privacy Policy": "預設隱私政策", "Use your own Privacy Policy": "使用您自己的隱私政策", "SMTP Settings": "SMTP 設定", "Use Custom SMTP": "使用自訂 SMTP", "Use my custom smtp credentials for email sending.": "使用我的自訂 SMTP 認證發送電子郵件。", "SMTP Credentials": "SMTP 憑證", "smtp.mailgun.org": "smtp.mailgun.org", "Host": "主機", "587": "587", "SMTP username": "SMTP 使用者名稱", "Username": "使用者名稱", "Encryption": "加密", "ssl": "SSL", "Select Encryption": "選擇加密", "Terms & Services Settings": "條款與服務設定", "Default TOS": "預設服務條款", "Use your own TOS": "使用您自己的服務條款", "Use your own TOS URL": "使用您自己的服務條款網址", "https://example.com/tos": "https://example.com/tos", "Enter the URL of your own Terms & Services page and make sure to add https:// in url": "輸入您自己的服務條款頁面的 URL，並確保在 URL 中添加 https://", "Use your own Privacy Policy URL": "使用您自己的隱私政策網址", "https://example.com/privacy-policy": "隱私政策：https://example.com/privacy-policy", "Enter the URL of your own Privacy Policy page and make sure to add https:// in url": "輸入您自己的隱私政策頁面的 URL，並確保在 URL 中添加 https://", "All Clients": "所有客戶", "Client Name": "客戶名稱", "Press / to search": "按 / 進行搜尋", "You don’t have any Product yet": "您尚未擁有任何產品", "Client Details": "客戶詳情", "Client Information": "客戶資訊", "Edit Information": "編輯資訊", "Billing Address": "帳單地址", "Account Status": "帳戶狀態", "Payment Information": "付款資訊", "Here you can find all cards added by your clients": "在此您可以查看客戶新增的所有卡片", "No payment methods found.": "找不到付款方式。", "Invoices": "發票", "Here you can check all your client's previous payments": "在此您可以查看所有客戶的過去付款記錄", "Edit Client Information": "編輯客戶資訊", "Experience Effortless Hosting With Powerful Features": "體驗強大功能的輕鬆主機服務", "Enjoy the lightning performance backed by powerful features and experience hassle-free hosting": "享受由強大功能支持的閃電效能，體驗無憂託管", "Effortless Server Operations": "輕鬆伺服器操作", "Manage your server effortlessly with our intuitive dashboard. Enjoy high performance and reliability without worrying about downtime, regardless of your technical skill level.": "使用我們直觀的儀表板輕鬆管理您的伺服器。無論您的技術水平如何，都能享受高效能和可靠性，無需擔心停機時間。", "Easy Website Management": "簡易網站管理", "We offer a comprehensive platform for managing WordPress sites with ease. With our intuitive dashboard and advanced features, you can manage your sites without any hassle.": "我們提供一個全面的平台，讓您輕鬆管理 WordPress 網站。透過我們直觀的儀表板和先進的功能，您可以毫不費力地管理您的網站。", "Powerful Security Measures": "強大的安全措施", "Our advanced security features ensure the safety of your data through advanced protective measures. Also, automated regular updates keep everything safe from threats and remains protected.": "我們的先進安全功能透過高級保護措施確保您的資料安全。此外，自動定期更新可防範威脅並保持保護狀態。", "Real-time Resources Monitoring": "即時資源監控", "Monitoring your site and server is essential for ensuring optimal performance and reliability. With real-time measures, you can quickly identify issues provide a seamless experience to your customers.": "監控您的網站和伺服器對於確保最佳性能和可靠性至關重要。透過即時監測，您可以快速識別問題，為客戶提供無縫的體驗。", "Transparent & Flexible Pricing for Everyone": "透明且彈性的定價方案，適合所有人", "Explore our range of plans designed to meet every needs of every web creator": "探索我們為每位網站創作者量身打造的各種方案", "Includes": "包含", "All rights reserved.": "版權所有。", "Terms of Service": "服務條款", "Terms & Services": "條款與服務", "Just One Click Away from Completing Your Order": "只需一鍵即可完成訂單", "Already have an account?": "已經有帳戶了嗎？", "Sign In": "登入", "Integrate any cloud provider to manage server and sites in xCloud": "將任何雲端供應商整合至 xCloud 以管理伺服器和網站", "Choose Your Plan": "選擇您的方案", "Cost": "成本", "Applied Coupon": "已使用優惠券", "Purchase Limit Reached!": "購買上限已達！", "Only one purchase remaining!": "僅剩一次購買機會！", "Split Pay": "分期付款", "Ready to confirm your purchase?": "準備確認您的購買嗎？", "By clicking 'Confirm', a charge of": "點擊「確認」後，將收取費用", "will be applied to your saved credit card.": "將套用至您儲存的信用卡。", "Please switch to your team to checkout": "請切換到您的團隊以結帳", "You are currently in the Playground Team. You can't checkout from here": "您目前在 Playground 團隊。您無法從這裡結帳。", "Total Purchases": "總購買額", "Only": "僅限", "purchase remaining": "購買餘額", "Database Root Password": "資料庫根密碼", "Do you want to turn off Auto Backups?": "您要關閉自動備份嗎？", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks": "停用自動備份意味著失去簡便的資料恢復、備份移動性和災難恢復；如果您了解風險，請點擊繼續", "Create Server": "建立伺服器", "Report an Issue": "回報問題", "Something went wrong": "發生錯誤", "Please try again or report an issue to support": "請重試或向支援報告問題", "Retry": "重試", "You may exit this window and navigate away": "您可以關閉此視窗並離開此頁面", "Check Connection": "檢查連線", "Add Site": "新增網站", "Install Stack": "安裝 Stack", "SSH User": "SSH 使用者", "SSH Key": "SSH 金鑰", "Log": "日誌", "Install stack on the server": "在伺服器上安裝堆疊", "It will install these following services on the server": "它將在伺服器上安裝以下服務", "PHP 7 .4": "PHP 7.4", "MySQL 8 .0": "MySQL 8.0", "Redis": "Redis", "and more. .": "及更多。", "Install": "安裝", "Log in": "登入", "Register": "註冊", "White Label": "白標", "Playground": "遊樂場", "Email not verified": "電子郵件未驗證", "Please check your email to verify your account. Verify your email": "請檢查您的電子郵件以驗證您的帳戶。驗證您的電子郵件", "Do you want to switch from": "您要切換自", "You can create free demo sites under Playground Team which will be removed after 24 hours. From Team Settings page you can switch back to your default team again.": "您可以在 Playground 團隊下創建免費的示範網站，這些網站將在 24 小時後被刪除。您可以從團隊設定頁面切換回您的預設團隊。", "Info": "資訊", "You can switch to any team you belong by clicking on the team name in the top right header.": "您可以點擊右上角標題中的團隊名稱來切換到您所屬的任何團隊。", "Switch to": "切換至", "Yes": "是", "No": "否", "Try xCloud Playground": "試用 xCloud Playground", "Do you want to Switch your team?": "您要切換團隊嗎？", "Please accept or check your email to switch to": "請接受或檢查您的電子郵件以切換至", "team. You can also visit": "團隊。您也可以造訪", "to manage your teams.": "管理您的團隊。", "Please accept or check your email to switch to your team. You can also visit": "請接受或檢查您的電子郵件以切換到您的團隊。您也可以訪問", "My Profile": "我的個人檔案", "Support": "支援", "Documentation": "文件", "Affiliates": "聯盟會員", "Admin Panel": "管理面板", "Reports": "報告", "Horizon": "地平線", "Telescope": "望遠鏡", "Vapor UI": "蒸汽介面", "Documents": "文件", "Find Servers or Sites": "尋找伺服器或網站", "Search Results": "搜尋結果", "of": "的", "Check our ": "查看我們的", " to get free hosting for 6 months with new Vultr signups.": "註冊新 Vultr 帳號即可獲得 6 個月免費主機服務。", " to get a quick start.": "快速開始", "Check out our ": "查看我們的", "Quick Start.": "快速開始", "No vulnerabilities were found on your sites. To setup vulnerability scanner please check this": "您的網站未發現任何漏洞。如需設定漏洞掃描器，請檢查此項目。", "Hey there! You have no ": "嗨！您沒有", " yet.": "尚未。", "Quick Start Documentation": "快速入門文件", "This feature is not available in Playground.": "此功能在 Playground 中不可用。", "No custom Nginx configurations found!": "未找到自訂 Nginx 配置！", "Hey there! You have no": "嗨！您沒有", "plugins.": "插件", "themes.": "主題", "to claim": "領取", "$20 free credits": "免費獲得 $20 點數", "if you are a new user on Hetzner.": "如果您是 <PERSON><PERSON><PERSON> 的新用戶。", " to claim ": "領取", "Read our ": "閱讀我們的", " on Heztner API integration.": "在 Hetzner API 整合上。", "Choose AWS Services": "選擇 AWS 服務", "Select one or more AWS services for which you want to use this credentials.": "選擇一個或多個您想使用此憑證的 AWS 服務。", "Verify": "驗證", "$300 free credits": "$300 免費額度", "if you are a new user on Google Cloud Platform.": "如果您是 Google Cloud Platform 的新用戶。", "Back": "返回", "$100 free credits": "$100 免費點數", "if you are a new user on Linode.": "如果您是 Linode 的新用戶。", "You can connect a fresh": "您可以連接新的", "Ubuntu 24.04 LTS x64": "Ubuntu 24.04 LTS x64", "server from any provider if you have root access.": "如果您有 root 訪問權限，可以使用任何提供商的伺服器。", "$200 free credits": "$200 免費點數", "if you are a new user on DigitalOcean.": "如果您是 DigitalOcean 的新用戶。", "to collect your API key. Read our": "收集您的 API 金鑰。閱讀我們的", "and Use": "使用", "XCLOUD": "雲端X", "coupon code to claim": "優惠代碼領取", "$100": "$100", "free credits on new vultr singups for 180 days.": "新用戶註冊 Vultr 可獲得 180 天免費額度。", "Database Info": "資料庫資訊", "Refreshing...": "正在重新整理...", "By default you can use": "預設情況下，您可以使用", "user. But if you want to do it for specific site enter the specific site user name here.": "使用者。如果您想針對特定網站進行操作，請在此輸入該網站的使用者名稱。", "Update PHP Configuration": "更新 PHP 設定", "Default PHP Version": "預設 PHP 版本", "Default Server PHP Version": "預設伺服器 PHP 版本", "This will set the default PHP version for the CLI and for new site installations. However, it won't affect the PHP versions of any existing sites, and the default version for new sites can still be changed during installation.": "這將設定 CLI 和新網站安裝的預設 PHP 版本。然而，這不會影響任何現有網站的 PHP 版本，且新網站的預設版本仍可在安裝過程中更改。", "Run Custom Command": "執行自訂指令", "Most Recent Commands": "最近的命令", "This command will be executed on the server. Please make sure you are running the correct command. Running incorrect commands can break your server.": "此指令將在伺服器上執行。請確保您正在執行正確的指令。執行錯誤的指令可能會損壞您的伺服器。", "If you want to do it for specific site enter the specific site user name here.": "若要針對特定網站操作，請在此輸入該網站的使用者名稱。", "Run Command": "執行命令", "Run New Command": "執行新指令", "Server Health": "伺服器狀態", "Filter": "篩選器", "Event": "事件", "Date & Time": "日期與時間", "Firewall Management": "防火牆管理", "Vulnerability Scan": "弱點掃描", "Your server is set to automatically update security patches. A reboot is required to complete the updates. You can configure the server to reboot automatically at a preferred time or choose to do it manually.": "您的伺服器已設定為自動更新安全修補程式。需要重新啟動以完成更新。您可以設定伺服器在首選時間自動重新啟動，或選擇手動重新啟動。", "Provider Backup Setting": "提供者備份設定", "Information": "資訊", "Notes": "備註", "Connection Settings": "連線設定", "Server Settings": "伺服器設定", "Update Timezone": "更新時區", "Magic Login Settings": "魔法登入設定", "Time Zone": "時區", "Turn off Indexing": "關閉索引功能", "Turning off this setting will prevent search engines from indexing your staging site.": "關閉此設定將防止搜尋引擎索引您的暫存網站。", "Search Engine Visibility": "搜尋引擎可見性", "Discourage search engines from indexing this site": "阻止搜尋引擎索引此網站", "It is up to search engines to honor this request.": "是否遵守此請求取決於搜尋引擎。", " offers a temporary test domain that allows you to quickly deploy your site. ": "提供臨時測試域名，讓您快速部署網站。", "This temporary domain enables you to share your work in progress with teammates or clients for review ": "此臨時網域可讓您與團隊成員或客戶分享您的進行中工作以供審閱。", "and input before you finalize and launch it with your own custom domain for public access.": "在您最終確定並使用自訂網域公開上線之前，請輸入內容。", "HTTPS Is Enabled": "HTTPS 已啟用", "Do You Want To Enable HTTPS?": "您要啟用 HTTPS 嗎？", "Please make sure that the database connection configuration is correct and if you are creating a new database you may want to store the information in a secure place. Storing the wp-config.php file in Git is not recommended and if the wp-config.php file is absent from your Git repository, ": "請確保資料庫連線配置正確。如果您正在建立新資料庫，建議將資訊儲存在安全的地方。不建議將 wp-config.php 檔案存放在 Git 中，如果您的 Git 儲存庫中沒有 wp-config.php 檔案，", " will automatically generate it from wp-config-sample.php, adding database credentials. Additional credentials can be added later on the WP Config page in the ": "將自動從 wp-config-sample.php 生成，並添加資料庫憑證。其他憑證可稍後在 WP 設定頁面中添加。", " site dashboard. If wp-config.php is already in the repository, ": "網站儀表板。如果 wp-config.php 已經在存儲庫中，", " won't make any changes.": "不會進行任何更改。", "Database Management": "資料庫管理", "Your source server must be": "您的來源伺服器必須是", "or": "或", "OLS": "OLS", "server with": "伺服器搭配", "Ubuntu 20.04 or 22.04 LTS x64": "Ubuntu 20.04 或 22.04 LTS x64", "and should have": "應具備", "access.": "存取", "month": "月份", "Email Address": "電子郵件地址", "By default, emails are sent from": "預設情況下，電子郵件從以下位置發送", "To send emails from your domain, enter your custom SMTP credentials (e.g., Mailgun, Elastic Email)": "若要從您的網域發送電子郵件，請輸入自訂 SMTP 憑證（例如：Mailgun、Elastic Email）", "Two Factor Authentication": "雙重驗證", "You have enabled two factor authentication.": "您已啟用雙重驗證。", "Finish enabling two factor authentication.": "完成啟用雙重驗證。", "You have not enabled two factor authentication.": "您尚未啟用雙重驗證。", "Enable": "啟用", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application.": "要完成啟用雙重驗證，請使用手機的驗證器應用程式掃描以下 QR 碼。", "Two factor authentication is now enabled.": "雙重驗證現已啟用。", "Enter the text code below instead if you can't use the barcode.": "如果無法使用條碼，請輸入下方的文字代碼。", "Or, Scan the": "或掃描", "QR provided": "提供 QR 碼", "with your phone's two-factor authentication app.": "使用手機的雙重驗證應用程式。", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "請將這些恢復代碼儲存在安全的密碼管理器中。如果您的雙重驗證裝置遺失，這些代碼可用於恢復帳戶存取權。", "Setup Key": "設定金鑰", "Regenerate Recovery Codes": "重新生成恢復代碼", "Show Recovery Codes": "顯示恢復代碼", "Create SSH Key": "建立 SSH 金鑰", "Create A New SSH Key": "建立新的 SSH 金鑰", "Verify & Save for": "驗證並儲存", "Buy & Add": "購買並新增", "Make sure you have active billing plan to use this feature.": "確保您有啟用的計費方案以使用此功能。", "This label will be used to identify this SMTP provider in": "此標籤將用於識別此 SMTP 提供者", "Optional if you're using global API key of Mailgun": "如果您使用 Mailgun 的全域 API 金鑰，這是可選的", ". i.e. Mailgun, Sendgrid, etc.": "Mailgun、Sendgrid 等", "This label will be used to identify this SMTP provider in xCloud. i.e. Mailgun, Sendgrid, etc.": "此標籤將用於在 xCloud 中識別此 SMTP 提供者，例如 Mailgun、Sendgrid 等。", "Authorize on WhatsApp": "在 WhatsApp 上授權", "Authorize on": "授權於", "Please go to Telegram and start talking with": "請前往 Telegram 並開始與", "To authenticate your chat, send this command to xCloud Notification Bot.": "要驗證您的聊天，請將此指令發送給 xCloud 通知機器人。", "Due Date": "到期日", "Recent Events": "最新事件", "Currently No Event Available": "目前沒有可用活動", "Mark all as read": "全部標記為已讀", "No Notifications Available!": "目前沒有通知！", "provides": "提供", "Environment to do all the development to your site with a temporary domain.": "使用臨時網域進行網站開發的環境。", "When you are ready, simply select the GO LIVE option and add your own domain to make the site available to your user/visitor.": "當您準備好時，只需選擇「開始直播」選項，並添加您自己的網域，使網站對您的用戶/訪客可用。", "Demo Site Management": "示範網站管理", "Verifying DNS..": "正在驗證 DNS...", "Your staging SSL is being managed by": "您的臨時環境 SSL 由以下單位管理", "Your SSL is being managed by Cloudflare.": "您的 SSL 由 Cloudflare 管理。", "If you turn on this option then": "如果您開啟此選項，則", "will automatically enable NGINX to directly serve previously": "將自動啟用 NGINX 直接提供先前的服務", "cached files without calling WordPress or any PHP. It also adds headers to cached CSS, JS, and images via": "快取檔案而不調用 WordPress 或任何 PHP。它還通過添加標頭到快取的 CSS、JS 和圖像。", "the browser cache. As a result, your website will be much faster. If someone visits your website again,": "瀏覽器快取。因此，您的網站將會更快。如果有人再次造訪您的網站，", "their browser can load the cached files directly from their own computer instead of making a request to": "他們的瀏覽器可以直接從自己的電腦載入快取檔案，而不需要發送請求至", "your web server. This reduces the number of requests to your server and further speeds up the loading of your website.": "您的網頁伺服器。這可以減少對伺服器的請求次數，進一步加快網站的加載速度。", "You can read more from our documentation": "您可以閱讀我們的文件以獲取更多資訊", "Saving Changes...": "儲存變更中...", "Make sure to add your SSH key on the SSH Authentication section.": "請確保在 SSH 驗證部分新增您的 SSH 金鑰。", "Enter the configuration file name": "輸入配置檔案名稱", "Save Config": "儲存設定", "Create Custom Nginx Configuration for": "建立自訂 Nginx 設定檔以供", "Select Template": "選擇範本", "IP Whitelist/Blacklist": "IP 白名單/黑名單", "WP-Cron and xCloud-Cron": "WP-Cron 和 xCloud-Cron", "Update PHP Settings": "更新 PHP 設定", "All existing files and data on this site will be deleted": "此網站上的所有現有檔案和資料將被刪除", "Create a new backup before restoring?": "在還原之前建立新的備份？", "Restore": "還原", "Take Backup": "備份", "Full Backup": "完整備份", "Are you sure you want to take a full backup? This will create a complete backup of all your data. Future incremental backups will be based on this full backup.": "您確定要進行完整備份嗎？這將創建您所有資料的完整備份。未來的增量備份將基於此完整備份。", "Incremental Backup": "增量備份", "This will add to your previous backups by only saving the changes since the last backup.": "這將只儲存自上次備份以來的變更，並新增至您之前的備份。", "Site List": "網站清單", "Management": "管理", "Settings": "設定", "Custom Cron Jobs": "自訂 Cron 工作", "PHP Configuration": "PHP 設定", "Commands": "指令", "Monitoring": "監控", "Logs": "日誌", "Events": "活動", "Firewall management": "防火牆管理", "Vulnerability Settings": "漏洞設定", "Full Server migration": "完整伺服器遷移", "Metadata": "中繼資料", "Site Overview": "網站概覽", "Caching": "快取", "Email Configuration": "電子郵件設定", "Previous Backups": "先前備份", "Backup Settings": "備份設定", "Site Monitoring": "網站監控", "Access Data": "存取資料", "SSH/sFTP": "SSH/sFTP", "Tools": "工具", "Nginx and Security": "Nginx 與安全性", "Nginx Customization": "Nginx 自訂設定", "IP Management": "IP 管理", "Site Settings": "網站設定", "User Profile": "用戶資料", "Browser Sessions": "瀏覽器工作階段", "Integrations": "整合", "Cloudflare": "Cloudflare", "Notification": "通知", "Billing": "帳單", "Manual Invoices": "手動發票", "Whitelabel": "白標", "All Events": "所有活動", "Payment": "付款", "Brand": "品牌", "SMTP": "SMTP", "Create Products": "建立產品", "Setup your brand to get started": "設定您的品牌以開始使用", "Setup your payment method": "設定您的付款方式", "Create customized plans": "建立自訂計劃", "Setup your domain easily": "輕鬆設定您的網域", "Search sites...": "搜尋網站...", "Create Account": "建立帳戶", "Password must be at least  8 characters and should contain uppercase, number and special character": "密碼必須至少包含 8 個字元，且需包含大寫字母、數字和特殊字元。", "Filter by Date": "按日期篩選", "Name your blueprint": "命名您的藍圖", "Search": "搜尋", "Are You Sure You Want To Delete Server": "您確定要刪除伺服器嗎？", "to confirm": "確認", "Are You Sure You Want To Delete Site": "您確定要刪除網站嗎？", "Edit Server Provider": "編輯伺服器提供者", "Add Server Provider": "新增伺服器提供者", "check connection": "檢查連線", "Clone Blueprint": "複製藍圖", "Delete Blueprint": "刪除藍圖", "Are you sure you want to delete this blueprint?": "您確定要刪除此藍圖嗎？", "Are you sure you want to clone this blueprint?": "您確定要複製此藍圖嗎？", "Clone": "複製", "Set as Default Blueprint": "設為預設藍圖", "Are you sure you want to set this blueprint as default?": "您確定要將此藍圖設為預設嗎？", "Are you sure?": "您確定嗎？", "You won't be able to revert this!": "此操作無法還原！", "Yes, Remove!": "是的，移除！", "Are you sure you want to update WordPress?": "您確定要更新 WordPress 嗎？", "Update WordPress Core": "更新 WordPress 核心", "Are you sure you want to active this theme?": "您確定要啟用此主題嗎？", "Activate Theme": "啟用主題", "Are you sure you want to update those plugin?": "您確定要更新這些插件嗎？", "You have selected": "您已選擇", "plugins": "插件", "Are you sure you want to update those plugins?": "您確定要更新這些插件嗎？", "Are you sure you want to update these themes?": "您確定要更新這些主題嗎？", "themes": "主題", "Do you want to deactivate Adminer?": "您要停用 Adminer 嗎？", "Do you want to activate Adminer?": "您要啟用 Adminer 嗎？", "We recommend deactivating it when not required.": "建議在不需要時停用。", "Yes, restore it!": "是的，還原！", "Are you sure you want to restore this server?": "您確定要還原此伺服器嗎？", "Are you sure you want to delete this card?": "您確定要刪除此卡片嗎？", "You can add another card.": "您可以新增另一張卡片。", "Yes, log out!": "是的，登出！", "Are you sure you want to delete this integration?": "您確定要刪除此整合嗎？", "You want to disconnect": "您要斷開連接", "Yes, Disconnect!": "是，斷開連接！", "You want to reconnect": "您想重新連接", "Yes, Reconnect!": "是的，重新連接！", "Yes, disable it!": "是，停用！", "Are you sure you want to disable HTTPS?": "您確定要停用 HTTPS 嗎？", "Yes, Leave Team!": "是的，退出團隊！", "Edit Storage Provider": "編輯存儲提供者", "Add Storage Provider": "新增儲存提供者", "Yes, leave team!": "是的，離開團隊！", "This will remove the cron job from the server.": "這將從伺服器中移除排程任務。", "Are you sure you want to disable this firewall rule?": "您確定要停用此防火牆規則嗎？", "You can enable it later.": "您可以稍後啟用。", "Yes, Disable": "是，停用", "Are you sure you want to enable this firewall rule?": "您確定要啟用此防火牆規則嗎？", "You can disable it later.": "您可以稍後停用。", "Yes, Enable": "是，啟用", "Are you sure you want to delete this firewall rule?": "您確定要刪除此防火牆規則嗎？", "Deleting firewall rule will remove it permanently.": "刪除防火牆規則將永久移除。", "Yes, Delete": "是，刪除", "Are you sure you want to unban": "您確定要解除封鎖嗎？", "This will remove the IP address from the banned list.": "這將從封鎖名單中移除該 IP 位址。", "Yes, Unban": "是，解除封鎖", "This will remove the sudo user from the server.": "這將從伺服器中移除 sudo 使用者。", "Authentication will be disabled for this site": "此網站的驗證功能將被停用", "Yes, Remove": "是，移除", "Are you sure you want to delete this backup?": "您確定要刪除這個備份嗎？", "This action cannot be undone.": "此操作無法撤銷。", "Are you sure you want to remove this failed backup?": "您確定要刪除這個失敗的備份嗎？", "Do you want to deactivate Tiny File Manager?": "您是否要停用 Tiny File Manager？", "Do you want to activate Tiny File Manager?": "您要啟用 Tiny File Manager 嗎？", "Are you sure you want to disable": "您確定要停用嗎？", "caching?": "快取？", "Are you sure you want to disable redis object caching?": "您確定要停用 Redis 物件快取嗎？", "Yes, switch it!": "是的，切換！", "Are you sure you want to switch to": "您確定要切換到", "plugin?": "外掛？", "Are you sure you want to disable full page caching?": "您確定要停用整頁快取嗎？", "Yes, Remove it!": "是的，移除！", "Are you sure you want to remove this IP address?": "您確定要移除此 IP 位址嗎？", "Are you sure you want to disable LiteSpeed Cache?": "您確定要停用 LiteSpeed Cache 嗎？", "Are you sure you want to delete this Nginx Configuration?": "您確定要刪除此 Nginx 配置嗎？", "It will be permanently deleted.": "將被永久刪除。", "Enable Nginx File Regeneration": "啟用 Nginx 文件再生產", "This will prevent xCloud from regenerating nginx file on any changes made to the site. You will have to manually regenerate the nginx file.": "這將防止 xCloud 在網站變更時重新生成 nginx 文件。您需要手動重新生成 nginx 文件。", "This will allow xCloud to regenerate nginx file on any changes made to the site. You will not have to manually regenerate the nginx file.": "這將允許 xCloud 在網站有任何變更時重新生成 nginx 檔案。您不需要手動重新生成 nginx 檔案。", "Enable Site": "啟用網站", "Disabling the site will make it inaccessible. Are you sure you want to disable it?": "停用此網站將使其無法訪問。您確定要停用嗎？", "Enabling the site will make it accessible. Are you sure you want to enable it?": "啟用此網站將使其可訪問。您確定要啟用嗎？", "This action will run the rescue process on the site. Are you sure you want to run it?": "此操作將在網站上執行救援程序。您確定要執行嗎？", "Yes, Run Now": "是，立即執行", "Are you sure you want to update the plugin?": "您確定要更新這個插件嗎？", "Plugin will be updated to": "插件將更新至", "Are you sure you want to not ignore this vulnerability?": "您確定要不忽略此漏洞嗎？", "Are you sure you want to ignore this vulnerability?": "您確定要忽略此漏洞嗎？", "Are you sure you want to update the theme?": "您確定要更新主題嗎？", "You have select": "您已選擇", "theme": "主題", "Are You Sure You Want To Delete this SSH Key:": "您確定要刪除此 SSH 金鑰嗎？", "Generate Invoice": "生成發票", "Add A New Payment Method": "新增付款方式", "Log Out Other Browser Sessions": "登出其他瀏覽器會話", "Log Out": "登出", "Log Out of All Sessions": "登出所有會話", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "請輸入密碼以確認您要登出所有裝置上的其他瀏覽器會話。", "You won't be able to revert this to your current device.": "您將無法將此操作還原至您目前的裝置。", "Are you sure you want to delete": "您確定要刪除嗎？", "You cannot delete this team": "您無法刪除此團隊", "You cannot delete your personal team": "您無法刪除您的個人團隊", "You cannot delete your current team": "您無法刪除目前的團隊", "Admin Username": "管理員使用者名稱", "SIZE": "尺寸", "REGION": "地區", "UBUNTU": "Ubuntu", "Deleting..": "刪除中…", "Delete SSH Key": "刪除 SSH 金鑰", "Add a new database to your server": "將新資料庫新增至您的伺服器", "Add a new user to your database": "將新使用者新增到您的資料庫中", "Edit database user:": "編輯資料庫使用者：", "Are You Sure You Want To Delete this database:": "您確定要刪除此資料庫嗎？", "Are You Sure You Want To Delete this database user:": "您確定要刪除此資料庫使用者嗎？", "Cron Job Output": "排程任務輸出", "Add new firewall rule for": "新增防火牆規則至", "Ban an IP Address on": "封鎖 IP 位址", "Reboot Time": "重啟時間", "Your server is set to automatically reboot at": "您的伺服器設定為自動重啟時間：", "You can change the reboot time if you'd like.": "您可以更改重新啟動的時間。", "CPU Usage": "CPU 使用率", "Reboot": "重新啟動", "Are You Sure You Want To": "您確定要", "Service?": "服務？", "Are You Sure You Want To Restore Backup For The Server": "您確定要還原伺服器的備份嗎？", "Are You Sure You Want To Resize Server": "您確定要調整伺服器大小嗎？", "Resize": "調整大小", "Initiating Resizing...": "正在調整大小...", "Manage Database": "管理資料庫", "Last Updated": "最後更新時間", "Deactivate": "停用", "Last Checked": "上次檢查時間", "Current Version": "目前版本", "RAM Usages": "記憶體使用量", "Disk Usages": "磁碟使用情況", "Choose SSH Keys": "選擇 SSH 金鑰", "No SSH Keys found.": "未找到 SSH 金鑰。", "Search SSH Keys": "搜尋 SSH 金鑰", "Are You Sure You Want To Disable Site": "您確定要停用網站嗎？", "Add Your Own Database": "新增您自己的資料庫", "Please provide your new database information": "請提供您的新資料庫資訊", "Cron Interval for Server": "伺服器的 Cron 間隔", "Add this public key in your Git repository as deploy key. This is necessary to enable": "將此公鑰新增至您的 Git 儲存庫作為部署金鑰。這是啟用所需的步驟。", "to clone the repository, ensuring a secure and authorized access for automated processes such as cloning.": "要複製此存放庫，確保自動化流程（如複製）的安全和授權訪問。", "Click On The Plugin Download Link": "按一下外掛程式下載連結", "Upload the zipped plugin file to WordPress under 'Add New' in the 'Plugins' tab": "在「外掛」標籤下的「新增」中上傳壓縮的外掛檔案至 WordPress", "Click 'Activate' to install the plugin": "按「啟用」以安裝插件", "Copy the authentication token and paste it into the plugin page to complete the setup": "複製驗證令牌並貼到插件頁面以完成設定", "Upload The Plugin Zipped File On WordPress": "在 WordPress 上上傳外掛壓縮檔案", "Click On The ‘Activate’ Button To Install Plugin": "按一下「啟用」按鈕以安裝插件", "Copy The Authentication Token And Paste It Into The Plugin Page": "複製驗證代碼並貼到插件頁面", "Export Zipped File From Your Existing Site": "從您現有的網站匯出壓縮檔案", "Upload The Exported Zip or Tar File Here": "在此上傳匯出的 Zip 或 Tar 檔案", "You must have root access to perform Full Server Migration": "要執行完整伺服器遷移，您必須擁有 root 訪問權限。", "Migrate multiple cPanel sites from shared hosting to xCloud easily for faster, scalable and secure hosting.": "輕鬆將多個 cPanel 網站從共享主機遷移到 xCloud，以獲得更快速、可擴展且安全的託管服務。", "If the fetched domain is incorrect, you can edit it here, the domain name needs to be accurate for successful migration": "如果擷取的網域不正確，您可以在此編輯。網域名稱需要準確才能成功遷移。", "Total migrated": "總計遷移", "Verifying ...": "正在驗證...", "Choose Web Server": "選擇網頁伺服器", "Updating Backup Schedule...": "正在更新備份排程...", "Update Backup Schedule": "更新備份排程", "Set Schedule": "設定排程", "Are You Sure?": "您確定嗎？", "Restoring...": "正在還原...", "Enabling Backup...": "正在啟用備份...", "Disabling Backup...": "正在停用備份...", "Backup?": "備份？", "Provider Backup Schedule": "提供者備份排程", "Update Schedule": "更新排程", "Select Day of Week": "選擇星期幾", "Select Day of Month": "選擇日期", "View all": "檢視全部", "You are now managing": "您現在正在管理", "site on": "網站開啟", "server.": "伺服器", "You can not use your own domain with xCloud Free Email Service..": "您無法在 xCloud 免費電子郵件服務中使用自己的網域。", "For example, you can send <NAME_EMAIL> or <EMAIL>. It is up to you!": "例如，您可以從 <EMAIL> 或 <EMAIL> 發送電子郵件。由您決定！", "Are you sure you want to restore this backup?": "您確定要還原此備份嗎？", "X-Frame-Options": "X-Frame-Options", "SAMEORIGIN": "同源", "Update Tags": "更新標籤", "Zone": "區域", "Choose Zones": "選擇區域", "Choose Regions": "選擇地區", "Choose Sizes": "選擇尺寸", "site": "站點", "No Vulnerabilities Found": "未發現漏洞", "Vulnerability Scan Not Enabled": "未啟用漏洞掃描", "Updates Available": "有可用更新", "sudo user": "超級使用者", "IP": "IP", "Package": "套件", "Can't find what you're looking for?": "找不到您要的內容嗎？", "Contact Support": "聯絡支援", "Nginx Options": "Nginx 選項", "OpenLiteSpeed Options": "OpenLiteSpeed 選項", "Link Google Drive Account": "連結 Google 雲端硬碟帳戶", "Upload a zipped file of your existing WordPress website (max size: 500 MB)": "上傳您現有 WordPress 網站的壓縮檔案（最大大小：500 MB）", "Recreate Site from Backup": "從備份重建網站", "Restore site backup from local or remote storage easily to create a site": "輕鬆從本地或遠端儲存恢復網站備份以建立網站", "Language Settings": "語言設定", "Copyright": "版權所有", "Taking Payment...": "正在付款...", "Bill": "帳單", "Pay": "付款", "Additional Information": "附加資訊", "Name of Invoice": "發票名稱", "Notification Language Settings": "通知語言設定", "Turning on this setting will prevent search engines from indexing your staging site.": "啟用此設定將防止搜尋引擎索引您的測試網站。", "For example, you can send emails from": "例如，您可以從...發送電子郵件", "It is up to you!": "由你決定！", "Adminer and File Manager won't work with 7G/8G firewall. We recommend SFTP if you're familiar with it.": "Adminer 和檔案管理器無法與 7G/8G 防火牆一起使用。如果您熟悉 SFTP，我們建議使用它。", "Demo Site Setup": "示範網站設定", "System Cron Jobs": "系統排程任務", "others": "其他", "Items": "項目", "List of Items": "項目列表", "Integrate New Item": "整合新項目", "Add New Item": "新增項目", "License": "授權條款", "Are you sure you want to delete?": "您確定要刪除嗎？", "Item deleted successfully": "項目已成功刪除", "Failed to delete Item": "刪除項目失敗", "Select Integrated Plugin": "選擇整合插件", "Boost your WordPress site’s performance with Object Cache Pro by caching frequently accessed data in Redis. This reduces database queries, enhances speed, and is especially beneficial for dynamic, content-heavy websites.": "使用 Object Cache Pro 提升您的 WordPress 網站效能，透過在 Redis 中快取常用資料來減少資料庫查詢，提升速度，特別適合動態且內容豐富的網站。", "Debug Mode": "偵錯模式", "Object Cache Pro": "物件快取專業版", "Select Plugin": "選擇插件", "Update Item": "更新項目", "Add Item": "新增項目", "Enter your license label": "輸入您的授權標籤", "Enter your license key": "輸入您的授權金鑰", "Edit Item": "編輯項目", "Access File Manager": "存取檔案管理器", "Always Enabled": "始終啟用", "Keep the File Manager accessible at all times.": "確保檔案管理器始終可用。", "Disable File Manager": "停用檔案管理員", "Specify how long the File Manager should remain enabled before it’s automatically disabled.": "指定檔案管理器在自動停用前應啟用的時間長度。", "Set Auto Disable Duration": "設定自動停用時長", "Choose to keep the File Manager active for always or schedule it to disable after a certain time.": "選擇讓檔案管理器始終保持啟用，或設定在特定時間後停用。", "Access Adminer": "存取 Adminer", "Keep the Adminer accessible at all times.": "確保 Adminer 隨時可用。", "Disable Adminer": "停用 Adminer", "Specify how long the Adminer should remain enabled before it’s automatically disabled.": "指定 Adminer 應啟用多久後自動停用。", "Set Auto Deactivate Duration": "設定自動停用時長", "Choose to keep the Adminer active for always or schedule it to disable after a certain time.": "選擇讓 Adminer 一直保持啟用，或設定在特定時間後停用。", "Customization": "自訂設定", "The File Manager will be disable within": "檔案管理器將在內停用", "The Adminer will be disable within": "管理員將在以下時間內停用", "Login": "登入", "Login Options": "登入選項", "Log in using xCloud email account or the first admin account.": "使用 xCloud 電子郵件帳戶或第一個管理員帳戶登入。", "Use a different email and, if your login URL is custom, enter it here to log in.": "請使用不同的電子郵件，若您的登入網址是自訂的，請在此輸入以登入。", "Custom": "自訂", "Something went wrong, please try again.": "發生錯誤，請再試一次。", "Use a different email or username to log in.": "請使用其他電子郵件或用戶名登入。", "Manually Upload Website": "手動上傳網站", "Upload a zipped file of your existing website": "上傳您現有網站的壓縮檔案", "Upload a zipped file of your existing website (max size: 500 MB)": "上傳您現有網站的壓縮檔案（最大大小：500 MB）", "Custom PHP": "自訂 PHP", "laravel": "<PERSON><PERSON>", "Laravel": "<PERSON><PERSON>", "Coming": "即將推出", "Update Web Root": "更新網站根目錄", "Web Root": "網站根目錄", "Coming Soon": "即將推出", "Enter your email": "輸入您的電子郵件地址", "Haven’t registered for your free account yet?": "尚未註冊免費帳戶？", "Sign up now": "立即註冊", "Glad To Have You Back!": "歡迎回來！", "Get Started For Free": "免費開始使用", "Access 1 server and 10 sites at $0 cost. No credit card required.": "免費存取 1 台伺服器和 10 個網站。不需信用卡。", "Enter your name": "輸入您的姓名", "Please choose a strong password. Use at least 8 characters, including a mix of letters, numbers and symbols.": "請選擇一個強密碼。使用至少 8 個字元，包括字母、數字和符號的組合。", "Retype Password": "重新輸入密碼", "The passwords you entered do not match. Please ensure both fields contain the same password.": "您輸入的密碼不匹配。請確保兩個欄位包含相同的密碼。", "By creating an account, you agree to our": "建立帳戶即表示您同意我們的", "Create Your Account": "建立您的帳戶", "Enter your credentials to sign up.": "輸入您的憑證以註冊。", "We have discovered a security vulnerability in the": "我們發現了安全漏洞", "that you are using on the following website:": "您正在使用以下網站：", "View Vulnerabilities": "檢視漏洞", "Immediate Action Required: We found a security vulnerability in your Website": "立即採取行動：我們在您的網站中發現了安全漏洞", "Community": "社群", "Want To Create A Staging Site For": "要建立測試網站給", "A staging site is a secure clone of your live website for testing and development. It lets you experiment with plugins, themes, and changes risk-free before deploying them to live.": "測試站點是您線上網站的安全複製，用於測試和開發。它讓您可以在不影響線上網站的情況下，無風險地試驗插件、主題和更改。", "Test Domain": "測試網域", "Create a demo site with our test domains and customize it before going live.": "使用我們的測試域名建立一個示範網站，並在上線前進行自訂。", "Custom Domain": "自訂網域", "Enter your custom domain by simply pointing your domain to the server.": "只需將您的網域指向伺服器，即可輸入自訂網域。", "Deploy Staging Site": "部署測試站點", "Manage Staging": "管理暫存環境", "Integrity Monitor": "完整性監控器", "Scan Now": "立即掃描", "Last scan": "上次掃描", "Items found": "找到的項目", "Message": "訊息", "Plugin": "插件", "This website has found some checksum errors. This could be due to a corrupted file or a malicious attack. Please review the files and database of your site.": "本網站發現了一些校驗和錯誤。這可能是由於文件損壞或惡意攻擊所致。請檢查您的網站文件和資料庫。", "Check": "檢查", "Security Settings": "安全設定", "AI Bot Blocker": "AI 機器人阻擋器", "Disable Nginx Config Regeneration": "停用 Nginx 配置重新生成", "Disable OpenLiteSpeed Config Regeneration": "停用 OpenLiteSpeed 配置重建", "WP Fail2Ban": "WP Fail2Ban", "Block Failed Login Attempts": "阻止登入嘗試失敗", "Block Common Usernames": "封鎖常見用戶名", "Block User Enumeration": "封鎖用戶枚舉", "Protect Comments": "保護評論", "Block Spam": "封鎖垃圾郵件", "Guard Password Resets": "保護密碼重設", "Guard Pingbacks": "防護回應通知", "Enable OpenLiteSpeed Config Regeneration": "啟用 OpenLiteSpeed 配置重建", "Config file regeneration has been updated successfully": "設定檔重新生成已成功更新", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config": "這將防止 xCloud 在網站進行任何更改時重新生成 OpenLiteSpeed 配置。您將需要手動重新生成 OpenLiteSpeed 配置。", "This will allow xCloud to regenerate OpenLiteSpeed config on any changes made to the site. You will not have to manually regenerate the OpenLiteSpeed config.": "這將允許 xCloud 在網站進行任何更改時重新生成 OpenLiteSpeed 配置。您不需要手動重新生成 OpenLiteSpeed 配置。", "Serve robots.txt from file system": "從檔案系統提供 robots.txt", "Failed to load nginx options": "無法載入 nginx 選項", "Failed to update config file regeneration": "更新配置文件重新生成失敗", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config.": "這將防止 xCloud 在網站變更時重新生成 OpenLiteSpeed 配置。您需要手動重新生成 OpenLiteSpeed 配置。", "This will prevent xCloud from regenerating nginx config on any changes made to the site. You will have to manually regenerate the nginx config.": "這將防止 xCloud 在網站變更時重新生成 nginx 配置。您需要手動重新生成 nginx 配置。", "This will allow xCloud to regenerate nginx config on any changes made to the site. You will not have to manually regenerate the nginx config.": "這將允許 xCloud 在網站進行任何更改時重新生成 nginx 配置。您不需要手動重新生成 nginx 配置。", "Add this URL as a webhook in your Git repository settings to enable automated deployments": "在您的 Git 儲存庫設定中新增此 URL 作為 webhook，以啟用自動部署。", "Laravel Debug Log": "Laravel 偵錯日誌", "Environment": "環境", "Update Environment": "更新環境", "Deploy Now": "立即部署", "Deploying...": "部署中...", "Your Laravel site deployment has been initiated. Please wait while the changes are being deployed.": "您的 Laravel 網站部署已開始。請稍候，正在部署變更。", "Failed to deploy Laravel site. Please try again.": "部署 Laravel 網站失敗。請再試一次。", "Add Supervisor Process": "新增監督者程序", "Edit Supervisor Process": "編輯監督程序", "Update Supervisor Process": "更新監督程序", "Supervisor Processes": "監督程序", "Check Process Status": "檢查處理狀態", "Supervisor": "監督者", "Process Management": "流程管理", "Background Processes": "背景處理程序", "Daemon": "守護程式", "Supervisor Process Output": "監督程序輸出", "Processes": "流程", "View Logs": "檢視日誌", "Restart": "重新啟動", "No supervisor processes found": "未找到監督程序", "Delete Supervisor Process": "刪除監督程序", "Are you sure you want to delete this supervisor process? This action cannot be undone.": "您確定要刪除此監督程序嗎？此操作無法撤銷。", "This will remove the supervisor process from the server.": "這將從伺服器中移除監督程序。", "Running": "運行中", "Stopped": "已停止", "Fatal": "致命錯誤", "Backoff": "退避", "Starting": "開始", "Stopping": "停止中", "Exited": "已退出", "Unknown": "未知", "Processing": "處理中", "You can run any executable command here. For Python, Node.js, or PHP scripts, specify the full command (e.g., python3 script.py).": "您可以在此執行任何可執行命令。對於 Python、Node.js 或 PHP 腳本，請指定完整命令（例如：python3 script.py）。", "Optional. Specify the working directory for the process.": "選擇性。指定程序的工作目錄。", "By default, you can use root. To run as a different user, enter the username here.": "預設情況下，您可以使用 root。若要以其他使用者身份執行，請在此輸入使用者名稱。", "Number of Processes": "程序數量", "Number of process instances to keep running.": "保留運行的流程實例數量。", "Start Seconds (Optional)": "開始秒數（可選）", "Supervisor will consider the process started after this many seconds.": "監督者將在此秒數後視為流程已開始。", "Stop Seconds (Optional)": "停止秒數（可選）", "Supervisor will wait this many seconds before force stopping the process.": "監督程式將等待此秒數後強制停止進程。", "Stop Signal (Optional)": "停止信號（可選）", "Supervisor sends this signal to stop the process. Leave empty for default (TERM).": "監督者發送此信號以停止流程。留空則使用預設值（TERM）。", "Laravel Horizon is not installed": "Laravel Horizon 未安裝", "Horizon is not detected in your composer.json. To use Horizon, you need to install it first:": "在您的 composer.json 中未檢測到 Horizon。要使用 Horizon，您需要先安裝它：", "Laravel Application": "Laravel 應用程式", "Enable Debug Mode": "啟用偵錯模式", "When debug mode is enabled, detailed error messages will be displayed. This should be disabled in production.": "啟用除錯模式時，將顯示詳細的錯誤訊息。在生產環境中應停用此功能。", "Enable Maintenance Mode": "啟用維護模式", "When maintenance mode is enabled, a maintenance screen will be displayed for all requests to your application.": "當啟用維護模式時，所有對應用程式的請求將顯示維護畫面。", "Application Environment": "應用環境", "Application": "應用程式", "The application environment affects various Laravel behaviors. This setting updates APP_ENV in your .env file": "應用程式環境會影響各種 Laravel 行為。此設定會更新 .env 檔案中的 APP_ENV。", "Clear Application Cache": "清除應用程式快取", "Clears all Laravel caches by running the optimize:clear command.": "透過執行 optimize:clear 指令清除所有 Laravel 快取。", "Clearing...": "清除中...", "Clear Cache": "清除快取", "Laravel Horizon": "<PERSON>vel <PERSON>", "Update Process": "更新過程", "Start Horizon": "啟動 Horizon", "Not Configured": "未設定", "Horizon is running": "Horizon 正在運行", "Horizon is not running": "Horizon 未啟動", "Stop": "停止", "Horizon provides a beautiful dashboard and code-driven configuration for your Laravel powered Redis queues.": "Horizon 提供美觀的儀表板和代碼驅動的配置，適用於您的 Laravel 驅動的 Redis 隊列。", "Laravel Scheduler": "Laravel 排程器", "Start Scheduler": "啟動排程器", "Your scheduler is running properly": "您的排程器運行正常", "Scheduler needs to be configured": "需要配置排程器", "Scheduler Frequency": "排程頻率", "The Laravel scheduler allows you to fluently and expressively define your command schedule within Laravel itself.": "Laravel 排程器讓您可以在 Laravel 中流暢且清晰地定義命令排程。", "Local": "本地", "Testing": "測試", "Every Minute": "每分鐘", "Every Five Minutes": "每五分鐘", "Every Ten Minutes": "每 10 分鐘", "Every Fifteen Minutes": "每十五分鐘", "Every Thirty Minutes": "每隔三十分鐘", "Hourly": "每小時", "Server is not connected": "伺服器未連線", "Failed to load Laravel application status": "無法載入 Laravel 應用程式狀態", "Laravel application settings updated successfully": "Laravel 應用程式設定更新成功", "Failed to update Laravel application settings": "無法更新 Laravel 應用程式設定", "Application cache cleared successfully": "應用程式快取已成功清除", "Failed to clear application cache": "清除應用程式快取失敗", "Horizon started successfully": "Horizon 啟動成功", "Failed to start Horizon": "無法啟動 Horizon", "Horizon stopped successfully": "Horizon 已成功停止", "Failed to stop Horizon": "無法停止 Horizon", "Horizon restarted successfully": "地平線已成功重新啟動", "Failed to restart Horizon": "無法重新啟動 Horizon", "Scheduler setup successfully": "排程設定成功", "Failed to setup scheduler": "排程器設定失敗", "Scheduler stopped successfully": "排程器已成功停止", "Failed to stop scheduler": "無法停止排程器", "Laravel Queue": "<PERSON><PERSON> 隊列", "Add Queue Worker": "新增佇列工作者", "No queue workers are running": "目前沒有佇列工作者在運行", "Connection": "連線", "Queue": "佇列", "Timeout": "逾時", "Memory": "記憶體", "Refresh Status": "更新狀態", "Edit Process": "編輯流程", "Restart Process": "重新啟動程序", "View Output": "檢視輸出", "Queue workers process jobs in the background. They are useful for handling time-consuming tasks like sending emails, processing files, or any other task that should not block the main application.": "佇列工作者在背景中處理作業。它們適用於處理耗時的任務，例如發送電子郵件、處理檔案或任何不應阻塞主應用程式的任務。", "Update Queue Worker": "更新佇列工作者", "redis": "Redis", "default": "預設", "0": "0", "Maximum Seconds Per Job": "每個作業的最大秒數", "0 = No Timeout": "0 = 無超時", "256": "256", "Maximum Memory (Optional)": "最大記憶體（可選）", "Memory limit in MB": "記憶體限制（MB）", "1": "1", "Number of worker processes to run": "工作進程數量", "Queue Worker Output": "佇列工作者輸出", "Queue worker updated successfully": "佇列工作者更新成功", "Queue worker added successfully": "佇列工作者新增成功", "Are you sure you want to stop this queue worker?": "您確定要停止此佇列工作者嗎？", "This will remove the queue worker process.": "這將移除佇列工作程序。", "Yes, Stop": "是，停止", "Queue worker stopped successfully": "佇列工作者已成功停止", "Failed to stop queue worker": "無法停止佇列工作者", "Queue worker restarted successfully": "佇列工作者已成功重新啟動", "Failed to restart queue worker": "無法重新啟動佇列工作者", "Error loading output. Please try again.": "載入輸出時發生錯誤。請重試。", "Failed to refresh worker status": "無法刷新工作者狀態", "Deployment has been initiated.": "部署已啟動。", "Plugins Found": "已找到插件", "Please configure Cloudflare SSL on this site to enable edge cache.": "請在此網站上配置 Cloudflare SSL 以啟用邊緣快取。", "Cloudflare Edge Cache is not available for staging sites. Please switch to production with Cloudflare SSL for this site to enable edge cache.": "Cloudflare Edge Cache 無法用於暫存網站。請切換至使用 Cloudflare SSL 的生產環境以啟用邊緣快取。", "You need to set up Cloudflare integration first to enable edge cache.": "您需要先設定 Cloudflare 整合以啟用邊緣快取。", "Domain is not available on Cloudflare. Please add your domain to Cloudflare first to enable edge cache.": "網域在 Cloudflare 上不可用。請先將您的網域新增到 Cloudflare 以啟用邊緣快取。", "Cloudflare Edge Cache has been enabled successfully.": "Cloudflare Edge 快取已成功啟用。", "Cloudflare Edge Cache has been disabled successfully.": "Cloudflare Edge 快取已成功停用。", "Failed to update Cloudflare Edge Cache settings.": "無法更新 Cloudflare Edge Cache 設定。", "Cloudflare Edge Cache purged successfully.": "Cloudflare Edge 快取已成功清除。", "Failed to purge Cloudflare Edge Cache.": "清除 Cloudflare Edge 快取失敗。", "Cloudflare Edge Cache": "Cloudflare 邊緣快取", "Boost your website's performance with Cloudflare Edge Cache by caching content at Cloudflare's global edge network. This reduces server load, enhances speed, and improves user experience worldwide.": "使用 Cloudflare 邊緣快取提升您的網站效能，透過在 Cloudflare 的全球邊緣網路快取內容來實現。這樣可以減少伺服器負載、提升速度，並改善全球用戶體驗。", "Clear Edge Cache": "清除邊緣快取", "This will purge all cached content from Cloudflare's edge network.": "這將清除 Cloudflare 邊緣網路中的所有快取內容。", "Failed to update Cloudflare Edge Cache settings": "無法更新 Cloudflare Edge Cache 設定", "Are you sure you want to disable Cloudflare Edge Cache?": "您確定要停用 Cloudflare Edge Cache 嗎？", "Failed to disable Cloudflare Edge Cache": "無法停用 Cloudflare Edge 快取", "Cloudflare Edge Cache purged successfully": "Cloudflare Edge 快取已成功清除", "A self-hosted monitoring tool like \"Uptime Robot\"": "自託管監控工具，如「Uptime Robot」", "A web interface for managing MySQL databases": "用於管理 MySQL 資料庫的網頁介面", "Admin User": "管理員用戶", "Default Node.js Version": "預設 Node.js 版本", "Default Server Node.js Version": "預設伺服器 Node.js 版本", "Disable phpMyAdmin": "停用 phpMyAdmin", "Disabling...": "停用中...", "Enable phpMyAdmin": "啟用 phpMyAdmin", "Enabling...": "啟用中...", "Install One Click App": "安裝一鍵應用程式", "Install One Click App Into": "安裝一鍵應用到", "Mautic": "Mautic", "n8n": "n8n", "One Click App": "一鍵應用程式", "One Click Apps": "一鍵應用程式", "Open phpMyAdmin": "開啟 phpMyAdmin", "Open-source marketing automation platform": "開源行銷自動化平台", "PHPMyAdmin": "PHPMyAdmin", "Please make sure to copy your admin password. You will not be able to see it again after installation.": "請確保複製您的管理員密碼。安裝後將無法再次查看。", "Provisioning...": "正在配置...", "Repair Node": "修復節點", "Repair PM2 Process & ecosystem.config.js": "修復 PM2 流程 & ecosystem.config.js", "Restart PM2 Process": "重啟 PM2 程序", "Select": "選擇", "Select an Application": "選擇應用程式", "Selected": "已選擇", "Site Details": "網站詳情", "This will set the default Node.js version for the server. Node.js is managed using nvm (Node Version Manager).": "這將設定伺服器的預設 Node.js 版本。Node.js 是使用 nvm（Node 版本管理器）進行管理的。", "Uptime Kuma": "正常運行 Kuma", "Workflow automation tool with a visual editor": "可視化編輯器的工作流程自動化工具", "phpMyAdmin": "phpMyAdmin", "phpMyAdmin is a free software tool written in PHP, intended to handle the administration of MySQL over the Web.": "phpMyAdmin 是一款用 PHP 編寫的免費軟體工具，用於透過網頁管理 MySQL。", "phpMyAdmin is being provisioned. Please wait until the provisioning process completes.": "正在配置 phpMyAdmin。請等待配置過程完成。", "phpMyAdmin is enabled but not yet fully provisioned. Please wait until it becomes available.": "phpMyAdmin 已啟用，但尚未完全配置。請等待其可用。", "phpMyAdmin is enabled on this server. Click the button above to open it in a new tab.": "此伺服器已啟用 phpMyAdmin。點擊上方按鈕以在新分頁中開啟。", "phpmyadmin_description": "用於管理 MySQL 資料庫的網頁介面", "n8n_description": "工作流程自動化工具，具備可視化編輯器", "uptime_kuma_description": "自託管監控工具，如「Uptime Robot」", "mautic_description": "開源行銷自動化平台", "Patchstack Subscriptions": "Patchstack 訂閱方案", "List of Patchstack Subscriptions": "Patchstack 訂閱列表", "Find all the patchstack subscriptions associated with your account here.": "在此查看與您的帳戶相關的所有 Patchstack 訂閱。", "Vulnerability Shield Pro is active!": "弱點防護專業版已啟用！", "Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.": "Vulnerability Shield Pro 現在將提供更快速的警報，讓您領先於威脅。", "WordPress Core.": "WordPress 核心", "Get faster security alerts with Vulnerability Shield Pro powered by": "透過由...提供支持的 Vulnerability Shield Pro 獲取更快速的安全警報", "Patchstack - only $4/month.": "Patchstack - 每月僅需 $4。", "Upgrade to PRO": "升級到專業版", "Canceling...": "取消中...", "Vulnerability Shield Pro": "弱點防護專業版", "Powered by Patchstack": "由 Patchstack 提供技術支援", "Stay protected with continuous vulnerability scans and advanced threat alerts to secure your WordPress website like a pro.": "透過持續的漏洞掃描和進階威脅警報來保護您的 WordPress 網站，讓您像專業人士一樣安全無虞。", "mo": "更多", "Upgrade to Pro NOW": "立即升級到專業版", "Remove Subscription": "取消訂閱", "Removing...": "正在移除...", "Are you sure you want to remove the subscription for this site?": "您確定要取消訂閱此網站嗎？", "Once confirm, this cannot be undone.": "一旦確認，將無法撤銷。", "Patchstack - only": "Patchstack - 僅限", "/month.": "/月", "Subscription Status": "訂閱狀態", "Export Sites Data": "匯出網站資料", "Export Servers Data": "匯出伺服器資料", "Export": "匯出", "Export site data in your preferred format by selecting the desired export type, format, and data columns.": "選擇所需的匯出類型、格式和資料欄位，以您偏好的格式匯出網站資料。", "Export server data in your preferred format by selecting the desired export type, format, and data columns.": "選擇所需的匯出類型、格式和資料欄位，以您偏好的格式匯出伺服器資料。", "Select Export Type": "選擇匯出類型", "Choose what to export": "選擇要匯出的項目", "Excel (XLSX)": "Excel (XLSX)", "CSV": "CSV", "Select Columns": "選擇欄位", "Exporting...": "匯出中...", "Export Format": "匯出格式", "Issues": "問題", "No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.": "Vulnerability Shield Pro 未檢測到任何問題。如有問題發現，您將立即收到通知。", "Excellent! Your WordPress Core is fully secure!": "太好了！您的 WordPress 核心已完全安全！", "Excellent! Your plugins are fully secure!": "太好了！您的插件完全安全！", "Excellent! Your theme is fully secure!": "太好了！您的主題已完全安全！", "Issue": "問題", "No issues were detected by Vulnerability Scanner. You’ll be notified immediately if any issues are found.": "弱點掃描器未檢測到任何問題。如發現問題，您將立即收到通知。", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $5/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "漏洞掃描器未檢測到任何問題。如發現問題，您將立即收到通知。升級到專業版，每月僅需 $5，即可享受由 Patchstack 提供的高級漏洞檢測和自動修補功能。", "Go PRO for": "升級至專業版以獲得", "Upgrade to Vulnerability Shield Pro for automated vulnerability detection & virtual patching powered by Patchstack": "升級至 Vulnerability Shield Pro，以享受由 Patchstack 提供的自動漏洞檢測和虛擬修補功能。", "Output": "輸出", "Shield Enabled": "防護已啟用", "Enable Shield": "啟用防護", "Vulnerability Shield": "弱點防護", "Pro": "專業版", "Enable Vulnerability Shield Pro to secure this site with automated virtual patching and faster notification alerts.": "啟用漏洞防護專業版，以自動虛擬修補和更快速的通知提醒來保護此網站。", "Pay now for": "立即支付以購買", "Continue free plan": "繼續免費方案", "I am not interested at this moment. Please, do not show this message again.": "目前不感興趣。請不要再顯示此訊息。", "The plugin has a vulnerability that makes it possible for unauthorized actions.": "插件存在一個漏洞，可能導致未經授權的操作。", "This vulnerability affects": "此漏洞影響", "We have discovered security vulnerabilities in the": "我們在中發現了安全漏洞", "These": "這些", "have vulnerabilities that make it possible for unauthorized actions.": "存在漏洞，可能導致未經授權的操作。", "The plugins have vulnerabilities that make it possible for unauthorized actions.": "插件存在漏洞，可能導致未經授權的操作。", "We have discovered security vulnerabilities in": "我們發現了安全漏洞在", "and a few more plugins that you are using on the following website:": "您在以下網站上使用的其他幾個插件：", "We detected this vulnerability on": "我們在以下日期檢測到此漏洞", "UTC. You can ignore this message if you have already taken care of it.": "協調世界時 (UTC)。如果您已經處理此問題，可以忽略此訊息。", "Thank you for being a": "感謝您成為", "user!": "使用者！", "If you have any questions, don't hesitate to contact our": "如有任何疑問，請隨時聯絡我們的", "support team": "支援團隊", "Thank you for being a xCloud user!": "感謝您成為 xCloud 的用戶！", "We found a security vulnerability in your Website": "我們在您的網站中發現了安全漏洞", "Scanning Now": "正在掃描", "Last Scanned": "上次掃描時間", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $": "漏洞掃描器未檢測到任何問題。如發現問題，您將立即收到通知。升級至專業版僅需 $", "/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "進階漏洞檢測與自動修補由 Patchstack 提供。", "This will set the default Node.js version for the server.": "這將設定伺服器的預設 Node.js 版本。", "Bucket": "桶", "Failed": "失敗", "What would you like to restore?": "您想要恢復什麼？", "Both File and Database": "檔案和資料庫", "Restore Your Site from a Backup": "從備份還原您的網站", "Select the components to restore": "選擇要還原的元件", "This will remove all files and database and then restore all from backup. Once proceed, this action cannot be undone": "這將刪除所有檔案和資料庫，然後從備份中還原。執行後，此操作無法撤銷。", "This will remove all files and then restore all files from backup. Once proceed, this action cannot be undone": "這將刪除所有檔案，然後從備份中還原所有檔案。一旦執行，此操作無法撤銷。", "This will overwrite your current files and database with the backup. Any existing content will be replaced with the new files": "這將用備份覆蓋您當前的檔案和資料庫。任何現有內容將被新檔案取代。", "This will overwrite your current files with the backup. Any existing content will be replaced with the new files": "這將用備份覆蓋您當前的檔案。任何現有內容將被新檔案取代。", "Choose how to apply the backup": "選擇備份應用方式", "Replace Existing Content": "取代現有內容", "Remove and Restore": "移除與還原", "Only Database": "僅限資料庫", "Only File": "僅限檔案", "Overwrite the current files with the selected backup": "使用選定的備份覆蓋當前檔案", "Completely remove the current files before restoring the backup": "在還原備份之前，請完全移除當前檔案。"}