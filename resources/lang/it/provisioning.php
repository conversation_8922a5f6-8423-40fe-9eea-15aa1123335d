<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'Verifica della carta e addebito del pagamento',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'Verifica della carta e addebito del pagamento di $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'Creazione del server',
            'tasks' => [
                ServerProvisioning::INIT => 'Inizializzazione del provisioning del server',
                ServerProvisioning::CREATING_SERVER => 'Creazione del server su :provider',
                ServerProvisioning::SERVER_CREATED => 'Server creato :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'In attesa dell\'avvio del server',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'Connessione al server',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'Connessione a SSH',
                ServerProvisioning::CONNECTED => 'Connessione stabilita',
            ]
        ],
        [
            'stage' => 'Configurazione del server',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'Configurazione del Swapfile',
                ServerProvisioning::UPGRADING_SYSTEM => 'Aggiornamento del sistema',
                ServerProvisioning::INSTALLING_BASE => 'Installazione delle dipendenze di base',
                ServerProvisioning::AUTHENTICATION_METHOD => 'Aggiornamento del metodo di autenticazione',
                ServerProvisioning::UPDATING_HOSTNAME => 'Aggiornamento del nome host',
                ServerProvisioning::UPDATING_TIMEZONE => 'Aggiornamento del fuso orario',
                ServerProvisioning::XCLOUD_USER => 'Impostazione degli utenti',

                ServerProvisioning::SETUP_SSH => 'Impostazione di SSH',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Impostazione dei permessi Sudo',
                ServerProvisioning::SETTING_UP_GIT => 'Impostazione di Git',
                ServerProvisioning::SETUP_FIREWALL => 'Impostazione del firewall',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'Impostazione dello script di pulizia',
            ]
        ],
        [
            'stage' => 'Installazione delle app',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'Installazione di PHP :php_version',
                ServerProvisioning::INSTALLING_WEBSERVER => 'Installazione di :stack',
                ServerProvisioning::INSTALLING_NODE => 'Installazione di Node',
                ServerProvisioning::INSTALLING_REDIS => 'Installazione di Redis',
                ServerProvisioning::INSTALLING_DATABASE => 'Installazione del database :database_type',
                ServerProvisioning::INSTALLING_WP_CLI => 'Installazione di WP CLI',
            ]
        ],
        [
            'stage' => 'Conclusione',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'Impostazione dei permessi SSH',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => 'Installazione dello script di monitoraggio',
                ServerProvisioning::READY_TO_DO_MAGIC => 'Pronto per fare magia!',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => 'Verifica e controllo',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'Verifica dello spazio di archiviazione e della connessione del server',
                SiteProvisioning::VERIFYING_DNS => 'Verifica del DNS per il tuo sito',
            ]
        ],
        [
            'stage' => 'Installazione delle app',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => 'Installazione di :site_stack :site_stack_version',
                SiteProvisioning::INSTALLING_DATABASE => 'Installazione del database',
                SiteProvisioning::INSTALLING_WORDPRESS => 'Configurazione di :type',
            ]
        ],
        [
            'stage' => 'Configurazione',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'Configurazione di SSL',
                SiteProvisioning::CONFIGURING_HTTPS => 'Configurazione di HTTPS',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => 'Configurazione di :cache',
                SiteProvisioning::CONFIGURING_NGINX => 'Configurazione di :stack',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Configurazione della cache di oggetti Redis',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Installazione di Blueprint',
                SiteProvisioning::DEPLOY_SCRIPT => 'Script di distribuzione',
                SiteProvisioning::INSTALL_MONITORING => 'Installazione del monitoraggio',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'Installazione del cron job di WP',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'Impostazione del servizio email gestito xCloud',
                SiteProvisioning::HANDLE_INDEXING => 'Gestione dell\'indicizzazione del sito',
                SiteProvisioning::FINISHING_UP => 'Conclusione',
            ]
        ]
    ],
];
