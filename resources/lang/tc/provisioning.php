<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => '驗證卡片 & 處理支付',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => '驗證卡片並處理支付 $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => '創建伺服器',
            'tasks' => [
                ServerProvisioning::INIT => '初始化伺服器配置',
                ServerProvisioning::CREATING_SERVER => '在 :provider 上創建伺服器',
                ServerProvisioning::SERVER_CREATED => '伺服器已創建 :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => '等待伺服器啟動',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => '連接到伺服器',
            'tasks' => [
                ServerProvisioning::CONNECTING => '連接到 SSH',
                ServerProvisioning::CONNECTED => '連接已建立',
            ]
        ],
        [
            'stage' => '配置伺服器',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => '配置交換檔案',
                ServerProvisioning::UPGRADING_SYSTEM => '升級系統',
                ServerProvisioning::INSTALLING_BASE => '安裝基礎依賴',
                ServerProvisioning::AUTHENTICATION_METHOD => '更新認證方法',
                ServerProvisioning::UPDATING_HOSTNAME => '更新主機名稱',
                ServerProvisioning::UPDATING_TIMEZONE => '更新時區',
                ServerProvisioning::XCLOUD_USER => '設置用戶',

                ServerProvisioning::SETUP_SSH => '設置 SSH',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => '設置 Sudo 權限',
                ServerProvisioning::SETTING_UP_GIT => '設置 Git',
                ServerProvisioning::SETUP_FIREWALL => '設置防火牆',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => '設置清理腳本',
            ]
        ],
        [
            'stage' => '安裝應用程式',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => '安裝 PHP :php_version',
                ServerProvisioning::INSTALLING_WEBSERVER => '安裝 :stack 網絡伺服器',
                ServerProvisioning::INSTALLING_NODE => '安裝 Node',
                ServerProvisioning::INSTALLING_REDIS => '安裝 Redis',
                ServerProvisioning::INSTALLING_DATABASE => '安裝 :database_type 資料庫',
                ServerProvisioning::INSTALLING_WP_CLI => '安裝 WP CLI',
            ]
        ],
        [
            'stage' => '完成',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => '設置 SSH 權限',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => '安裝監控腳本',
                ServerProvisioning::READY_TO_DO_MAGIC => '準備開始操作！',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => '檢查與驗證',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => '檢查伺服器存儲與連接',
                SiteProvisioning::VERIFYING_DNS => '驗證您的網站 DNS',
            ]
        ],
        [
            'stage' => '安裝應用程式',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => '安裝 PHP :php_version',
                SiteProvisioning::INSTALLING_DATABASE => '安裝資料庫',
                SiteProvisioning::INSTALLING_WORDPRESS => '設置 WordPress',
            ]
        ],
        [
            'stage' => '配置',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => '配置 SSL',
                SiteProvisioning::CONFIGURING_HTTPS => '配置 HTTPS',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => '配置 :cache 快取',
                SiteProvisioning::CONFIGURING_NGINX => '配置 :stack',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => '配置 Redis 對象快取',
                SiteProvisioning::INSTALL_BLUEPRINT => '安裝 Blueprint',
                SiteProvisioning::DEPLOY_SCRIPT => '部署腳本',
                SiteProvisioning::INSTALL_MONITORING => '安裝監控',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => '安裝 WP Cron 作業',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => '設置 xCloud 管理的電子郵件服務',
                SiteProvisioning::HANDLE_INDEXING => '處理網站索引',
                SiteProvisioning::FINISHING_UP => '完成',
            ]
        ]
    ],
];
