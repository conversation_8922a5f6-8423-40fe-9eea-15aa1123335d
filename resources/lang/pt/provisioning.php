<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'Verificando o cartão & Processando pagamento',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'Verificando o cartão e processando o pagamento de $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'Criando Servidor',
            'tasks' => [
                ServerProvisioning::INIT => 'Inicializando provisionamento do servidor',
                ServerProvisioning::CREATING_SERVER => 'Criando servidor em :provider',
                ServerProvisioning::SERVER_CREATED => 'Servidor criado :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'Aguardando o servidor iniciar',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'Conectando ao servidor',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'Conectando ao SSH',
                ServerProvisioning::CONNECTED => 'Conexão estabelecida',
            ]
        ],
        [
            'stage' => 'Configurando servidor',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'Configurando swapfile',
                ServerProvisioning::UPGRADING_SYSTEM => 'Atualizando sistema',
                ServerProvisioning::INSTALLING_BASE => 'Instalando dependências básicas',
                ServerProvisioning::AUTHENTICATION_METHOD => 'Atualizando método de autenticação',
                ServerProvisioning::UPDATING_HOSTNAME => 'Atualizando hostname',
                ServerProvisioning::UPDATING_TIMEZONE => 'Atualizando fuso horário',
                ServerProvisioning::XCLOUD_USER => 'Configurando usuários',

                ServerProvisioning::SETUP_SSH => 'Configurando SSH',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Configurando permissões de Sudo',
                ServerProvisioning::SETTING_UP_GIT => 'Configurando Git',
                ServerProvisioning::SETUP_FIREWALL => 'Configurando firewall',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'Configurando script de limpeza',
            ]
        ],
        [
            'stage' => 'Instalando apps',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'Instalando PHP :php_version',
                ServerProvisioning::INSTALLING_WEBSERVER => 'Instalando servidor web :stack',
                ServerProvisioning::INSTALLING_NODE => 'Instalando Node',
                ServerProvisioning::INSTALLING_REDIS => 'Instalando Redis',
                ServerProvisioning::INSTALLING_DATABASE => 'Instalando banco de dados :database_type',
                ServerProvisioning::INSTALLING_WP_CLI => 'Instalando WP CLI',
            ]
        ],
        [
            'stage' => 'Finalizando',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'Configurando permissões do SSH',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => 'Instalando script de monitoramento',
                ServerProvisioning::READY_TO_DO_MAGIC => 'Pronto para fazer mágica!',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => 'Verificando e validando',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'Verificando armazenamento e conexão do servidor',
                SiteProvisioning::VERIFYING_DNS => 'Verificando DNS para o seu site',
            ]
        ],
        [
            'stage' => 'Instalando apps',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => 'Instalando :site_stack :site_stack_version',
                SiteProvisioning::INSTALLING_DATABASE => 'Instalando banco de dados',
                SiteProvisioning::INSTALLING_WORDPRESS => 'Configurando :type',
            ]
        ],
        [
            'stage' => 'Configurando',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'Configurando SSL',
                SiteProvisioning::CONFIGURING_HTTPS => 'Configurando HTTPS',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => 'Configurando cache :cache',
                SiteProvisioning::CONFIGURING_NGINX => 'Configurando :stack',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Configurando cache de objetos Redis',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Instalando Blueprint',
                SiteProvisioning::DEPLOY_SCRIPT => 'Script de implantação',
                SiteProvisioning::INSTALL_MONITORING => 'Instalando monitoramento',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'Instalando cron job do WP',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'Configurando serviço de e-mail gerenciado xCloud',
                SiteProvisioning::HANDLE_INDEXING => 'Gerenciando indexação do site',
                SiteProvisioning::FINISHING_UP => 'Finalizando',
            ]
        ]
    ],
];
