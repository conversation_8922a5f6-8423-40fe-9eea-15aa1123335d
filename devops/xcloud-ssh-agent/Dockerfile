FROM ubuntu:20.04

RUN apt update && apt install openssh-server sudo locales -y
RUN locale-gen en_US.UTF-8
RUN adduser --disabled-password --gecos "" xcloud && usermod -aG sudo xcloud

RUN mkdir -p /root/.ssh -p /root/.xcloud && mkdir -p /home/<USER>/.ssh && mkdir -p /home/<USER>/.xcloud
COPY authorized_keys /root/.ssh/authorized_keys
COPY authorized_keys /home/<USER>/.ssh/authorized_keys
RUN chown -R xcloud:xcloud /home/<USER>/

RUN sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config

RUN apt install whois
RUN PASSWORD=$(mkpasswd -m sha-512 CzkCdIEs1szVptpPyqMWM9hCKpfjQPk4NSpHINgf) && usermod --password $PASSWORD xcloud

RUN service ssh start

EXPOSE 22

CMD ["/usr/sbin/sshd", "-D"]
