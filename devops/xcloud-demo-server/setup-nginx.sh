export DEBIAN_FRONTEND=noninteractive
export NEEDRESTART_MODE=1

# Ping xCloud With Provisioning Updates

function provisionPing {
    echo "step $2"
    curl --insecure --data "status=$1" https://2a1c-103-95-96-8.ngrok-free.app/api/callback/provisioning/EQLRljamKro1yMN7Yj6jYbA2VDBzw5OvqZpG
}

if [[ $EUID -ne 0 ]]; then
    echo "This script must be run as root."

    exit 1
fi

UNAME=$(awk -F= '/^NAME/{print $2}' /etc/os-release | sed 's/\"//g')
if [[ "$UNAME" != "Ubuntu" ]]; then
    echo "xCloud only supports Ubuntu 20.04 or 22.04 or 24.04"

    exit 1
fi

if [[ -f /root/.xcloud-provisioned ]]; then
    echo "This server has already been provisioned by xCloud."
    echo "If you need to re-provision, you may remove the /root/.xcloud-provisioned file and try again."

    exit 1
fi


apt_wait () {
    while fuser /var/lib/dpkg/lock >/dev/null 2>&1 ; do
        echo "Waiting: dpkg/lock is locked..."
        sleep 5
    done

    while fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1 ; do
        echo "Waiting: dpkg/lock-frontend is locked..."
        sleep 5
    done

    while fuser /var/lib/apt/lists/lock >/dev/null 2>&1 ; do
        echo "Waiting: lists/lock is locked..."
        sleep 5
    done

    if [ -f /var/log/unattended-upgrades/unattended-upgrades.log ]; then
        while fuser /var/log/unattended-upgrades/unattended-upgrades.log >/dev/null 2>&1 ; do
            echo "Waiting: unattended-upgrades is locked..."
            sleep 5
        done
    fi
}

echo "Checking apt-get availability..."

apt_wait
provisionPing 7

provisionPing 8
# Configure Swap Disk

if [ -f /swapfile ]; then
    echo "Swap exists."
else
    fallocate -l 1G /swapfile
    chmod 600 /swapfile
    mkswap /swapfile
    swapon /swapfile
    echo "/swapfile none swap sw 0 0" >> /etc/fstab
    echo "vm.swappiness=30" >> /etc/sysctl.conf
    echo "vm.vfs_cache_pressure=50" >> /etc/sysctl.conf
fi

provisionPing 9
apt_wait

apt-get update -y

apt_wait

apt-get upgrade -y

provisionPing 10
apt_wait

# Add A Few PPAs To Stay Current

apt-get install -y --force-yes software-properties-common
apt-add-repository ppa:ondrej/nginx -y
apt-add-repository ppa:ondrej/php -y
add-apt-repository ppa:redislabs/redis -y
add-apt-repository universe -y

apt_wait

apt-get update -y

# Base Packages

apt_wait

apt-get install -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold" -y --force-yes build-essential curl pkg-config fail2ban gcc g++ git libmcrypt4 libpcre3-dev \
make python3 python3-pip supervisor ufw zip unzip zsh ncdu uuid-runtime acl whois libpng-dev libmagickwand-dev

provisionPing 11
# Disable Password Authentication Over SSH

# Removing existing settings
sed -i "/^\s*PasswordAuthentication\s*yes/d" /etc/ssh/sshd_config
sed -i "/^\s*PermitRootLogin\s*yes/d" /etc/ssh/sshd_config

# Adding extra line to the end of the file
echo "" | sudo tee -a /etc/ssh/sshd_config
echo "" | sudo tee -a /etc/ssh/sshd_config

# Disable password authentication
echo "PasswordAuthentication no" | sudo tee -a /etc/ssh/sshd_config

# Disable root login via password
echo "PermitRootLogin prohibit-password" | sudo tee -a /etc/ssh/sshd_config



provisionPing 12
echo "nobin-utm-nginx" > /etc/hostname
sed -i 's/127\.0\.0\.1.*localhost/127.0.0.1 nobin-utm-nginx.localdomain nobin-utm-nginx localhost/' /etc/hosts
update_hosts() {
    local ip=$1
    local hostname=$2
    local entry="${ip} ${hostname}"

    # Check if the exact entry exists
    if ! grep -Pq "^${ip}\s+${hostname}" /etc/hosts; then
        # If the entry doesn't exist, append it
        echo "${entry}" | sudo tee -a /etc/hosts > /dev/null
    fi
}
update_hosts "127.0.0.1" "localhost"
update_hosts "::1" "localhost"
hostname nobin-utm-nginx

provisionPing 13
# Set The Timezone
ln -sf /usr/share/zoneinfo/UTC /etc/localtime

provisionPing 14
# Setup xCloud User

useradd xcloud
mkdir -p /home/<USER>/.ssh
mkdir -p /home/<USER>/.xcloud
adduser xcloud sudo

# Setup Bash For xCloud User
chsh -s /bin/bash xcloud
cp /root/.profile /home/<USER>/.profile
cp /root/.bashrc /home/<USER>/.bashrc

PASSWORD=$(mkpasswd -m sha-512 'HFe33ZkaKA5MlqWSp7d5xSa7X7U2ozQu')
usermod --password $PASSWORD xcloud

# Updating Sudo User

if id -u nobin > /dev/null 2>&1; then
    echo "Sudo User nobin user exists..."
else
    adduser --disabled-password --gecos "" nobin
fi

usermod -aG sudo nobin

PASSWORD=$(mkpasswd -m sha-512 'secret')
usermod --password $PASSWORD nobin


mkdir -p /home/<USER>/.ssh

cat > /home/<USER>/.ssh/authorized_keys << EOF
# nobins-macbook-pro-2
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCl6AZoVnDxo8pxlPbdwVKyFhJuyYgZsdt8KaNCvDVVIddRGz5SYw9z3SraA8kh/SpJhlIZq9hPz/DE6Mo0rnKauFpjEALTpqIV9Ro+Jk3E84na0WyW9Fkt5W3u6PmNYrcH27CazVjueWkzyeGRpQXNyNGVUNOXrBTzDBT4VKBsDBQ2kLVqg4sd/4LPou7HtKVdcZlvoXTS6536RMSfar5f5lfKWecvnjhjUGFspu35+oLw8JLMj037A/18H/0Iy+suWv6eFES0gs0yZcTxKsnaB5sJ308B4N2XwQWiLC/Bvk67XPS6lRm+CYQ8ptm8Rq8mDhPPkKbQ/A3jpB1tQRJL <EMAIL>

EOF


chown -R nobin:nobin /home/<USER>/.ssh

echo "Sudo User nobin updated..."
# Updating Sudo User

if id -u root > /dev/null 2>&1; then
    echo "Sudo User root user exists..."
else
    adduser --disabled-password --gecos "" root
fi

usermod -aG sudo root

PASSWORD=$(mkpasswd -m sha-512 'secret')
usermod --password $PASSWORD root


mkdir -p /root/.ssh

cat > /root/.ssh/authorized_keys << EOF
# nobins-macbook-pro-2
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCl6AZoVnDxo8pxlPbdwVKyFhJuyYgZsdt8KaNCvDVVIddRGz5SYw9z3SraA8kh/SpJhlIZq9hPz/DE6Mo0rnKauFpjEALTpqIV9Ro+Jk3E84na0WyW9Fkt5W3u6PmNYrcH27CazVjueWkzyeGRpQXNyNGVUNOXrBTzDBT4VKBsDBQ2kLVqg4sd/4LPou7HtKVdcZlvoXTS6536RMSfar5f5lfKWecvnjhjUGFspu35+oLw8JLMj037A/18H/0Iy+suWv6eFES0gs0yZcTxKsnaB5sJ308B4N2XwQWiLC/Bvk67XPS6lRm+CYQ8ptm8Rq8mDhPPkKbQ/A3jpB1tQRJL <EMAIL>

EOF

cat >> /root/.ssh/authorized_keys << EOF
# Owner
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDHpha0HkCKNWkg4bQamu5axKCFFo32rAqKVI9+eUD+m1SvLK1NsWCXDqKR3KBeb7DGwvEGTc5LPrY3Ityp4yJOqk2ZUzYbGw9lpJHZ7oAQq28uoYU4J3T69d9B3LrqyarOLNPEOOS3ay1tkJUUXZW7OPKR41hJ/h/vBw2LToNgbWLNqPYxkPNrWkv1S91DtwbhurbcT8s5QYXN4y1T6oY7WxD9cOxPR3ybn2f6dbgESPGyFnxiPsMWk+ygqVf4SwrhMEOcmG14gSWwbvztXQp73Z5bkiMLKXN4RnDLXjFLi8E5mQ4hY10QUkT4EhtRQxoJHTET6ttdQEzocxaKinh9m/ioU650HskzrWDOQgKwVUo1N3ivpd60WnZHITk5gpw0o8OPWrDrhq5og4jywo5lGHrfIDlfimHAx3+v4PrQlwgri/Pko3MLJ238cGqv1HcQ/+bV0MXLSxvnoz5IZvSNugkr/aTXQw/ywuiwYgWXLChezTtNb8ywqGhdLK2O8iWBysJvybaLizvDPBQkkSHW+Sim+mYZLxpaooe0blOuxCO50SPReoTuROTYAjU168+2rs9hmeg5UIIbulYZA/gG1+xx15SJx1tQY+4kvo5L6Cb7pg/4h+GeLrsb82rP2akuMVjTvNmijbCK1hA4Xr5hPYMLYshjD96TMdQ8+GF17Q== utm@xcloud
EOF

chown -R root:root /root/.ssh

echo "Sudo User root updated..."

provisionPing 15
# Create The Root SSH Directory If Necessary

if [ ! -d /root/.ssh ]
then
    mkdir -p /root/.ssh
    touch /root/.ssh/authorized_keys
fi

# Check Permissions Of /root Directory

chown root:root /root
chown -R root:root /root/.ssh

chmod 700 /root/.ssh
chmod 600 /root/.ssh/authorized_keys


# Generate Server Key
ssh-keygen -A

# Write Owner Key

cat > /root/.ssh/authorized_keys << EOF
# Owner
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDHpha0HkCKNWkg4bQamu5axKCFFo32rAqKVI9+eUD+m1SvLK1NsWCXDqKR3KBeb7DGwvEGTc5LPrY3Ityp4yJOqk2ZUzYbGw9lpJHZ7oAQq28uoYU4J3T69d9B3LrqyarOLNPEOOS3ay1tkJUUXZW7OPKR41hJ/h/vBw2LToNgbWLNqPYxkPNrWkv1S91DtwbhurbcT8s5QYXN4y1T6oY7WxD9cOxPR3ybn2f6dbgESPGyFnxiPsMWk+ygqVf4SwrhMEOcmG14gSWwbvztXQp73Z5bkiMLKXN4RnDLXjFLi8E5mQ4hY10QUkT4EhtRQxoJHTET6ttdQEzocxaKinh9m/ioU650HskzrWDOQgKwVUo1N3ivpd60WnZHITk5gpw0o8OPWrDrhq5og4jywo5lGHrfIDlfimHAx3+v4PrQlwgri/Pko3MLJ238cGqv1HcQ/+bV0MXLSxvnoz5IZvSNugkr/aTXQw/ywuiwYgWXLChezTtNb8ywqGhdLK2O8iWBysJvybaLizvDPBQkkSHW+Sim+mYZLxpaooe0blOuxCO50SPReoTuROTYAjU168+2rs9hmeg5UIIbulYZA/gG1+xx15SJx1tQY+4kvo5L6Cb7pg/4h+GeLrsb82rP2akuMVjTvNmijbCK1hA4Xr5hPYMLYshjD96TMdQ8+GF17Q== utm@xcloud
EOF

# Add key to xcloud user
cp /root/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys

# Create The Server SSH Key

rm -f /home/<USER>/.ssh/id_rsa

ssh-keygen -f /home/<USER>/.ssh/id_rsa -t rsa -N ''

# Copy Source Control Public Keys Into Known Hosts File

ssh-keyscan -H github.com >> /home/<USER>/.ssh/known_hosts
ssh-keyscan -H bitbucket.org >> /home/<USER>/.ssh/known_hosts
ssh-keyscan -H gitlab.com >> /home/<USER>/.ssh/known_hosts

# Restart SSH
service ssh restart

provisionPing 17
# Configure Git Settings

git config --global user.name "xCloud"
git config --global user.email "<EMAIL>"

provisionPing 19
# Add The Provisioning Cleanup Script Into Root Directory

cat > /root/xcloud-cleanup.sh << 'EOF'
#!/usr/bin/env bash

# xCloud Provisioning Cleanup Script

UID_MIN=$(awk '/^UID_MIN/ {print $2}' /etc/login.defs)
UID_MAX=$(awk '/^UID_MAX/ {print $2}' /etc/login.defs)
HOME_DIRECTORIES=$(eval getent passwd {0,{${UID_MIN}..${UID_MAX}}} | cut -d: -f6)

for DIRECTORY in $HOME_DIRECTORIES
do
  XCLOUD_DIRECTORY="$DIRECTORY/.xcloud"

  if [ ! -d $XCLOUD_DIRECTORY ]
  then
    continue
  fi

  echo "Cleaning $XCLOUD_DIRECTORY..."

  find $XCLOUD_DIRECTORY -type f -mtime +30 -print0 | xargs -r0 rm --
done

# Define log directory
LOG_DIR="/var/log"

# Set the maximum file size threshold for truncation
MAX_SIZE=10485760  # 10 MB in bytes

# Find all log files including rotated ones and process each one
find "$LOG_DIR" -type f \( -name "*.log" -o -name "*.log.*" \) | while read -r logfile; do
    # Check if the file is a regular log or a rotated/compressed log
    if [[ "$logfile" =~ \.gz$ ]] || [[ "$logfile" =~ \.[0-9]+$ ]]; then
        # It's a rotated or compressed log file, delete it
        rm -f "$logfile"
        echo "Removed old log file: $logfile"
    else
        # It's a current log file, check if it needs to be truncated
        actual_size=$(stat --printf="%s" "$logfile")
        if [ "$actual_size" -gt "$MAX_SIZE" ]; then
            # Truncate the file while keeping the last 10 MB
            tail -c "$MAX_SIZE" "$logfile" > "$logfile.tmp"
            mv "$logfile.tmp" "$logfile"
            echo "Truncated $logfile to the last 10 MB"
        fi
    fi
done

echo "Log cleanup completed."
EOF

chmod +x /root/xcloud-cleanup.sh

# check cronjob is already exist
if grep -q "xcloud-cleanup.sh" /etc/crontab; then
  echo "Cronjob already exist. Removing the existing cronjob..."
  # remove the cronjob
  sed -i '/xcloud-cleanup.sh/d' /etc/crontab
fi

cat > /etc/cron.d/xcloud-cleanup << EOF_XCLOUD_CLEANUP
20 23 * * * root bash /root/xcloud-cleanup.sh 2>&1
EOF_XCLOUD_CLEANUP

provisionPing 18
# Setup UFW Firewall

ufw allow 22
ufw allow 80
ufw allow 443
ufw --force enable


provisionPing 16
# Allow FPM Restart

echo "xcloud ALL=NOPASSWD: /usr/sbin/service php5.6-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php7.0-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php7.1-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php7.2-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php7.3-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php7.4-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php8.0-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php8.1-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php8.2-fpm reload" > /etc/sudoers.d/php-fpm
echo "xcloud ALL=NOPASSWD: /usr/sbin/service php8.3-fpm reload" > /etc/sudoers.d/php-fpm

# Allow Nginx Reload

echo "xcloud ALL=NOPASSWD: /usr/sbin/service nginx *" >> /etc/sudoers.d/nginx

# Allow Supervisor Reload

echo "xcloud ALL=NOPASSWD: /usr/bin/supervisorctl *" >> /etc/sudoers.d/supervisor

provisionPing 20
export DEBIAN_FRONTEND=noninteractive
export NEEDRESTART_MODE=1

apt_wait () {
    while fuser /var/lib/dpkg/lock >/dev/null 2>&1 ; do
        echo "Waiting: dpkg/lock is locked..."
        sleep 5
    done

    while fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1 ; do
        echo "Waiting: dpkg/lock-frontend is locked..."
        sleep 5
    done

    while fuser /var/lib/apt/lists/lock >/dev/null 2>&1 ; do
        echo "Waiting: lists/lock is locked..."
        sleep 5
    done

    if [ -f /var/log/unattended-upgrades/unattended-upgrades.log ]; then
        while fuser /var/log/unattended-upgrades/unattended-upgrades.log >/dev/null 2>&1 ; do
            echo "Waiting: unattended-upgrades is locked..."
            sleep 5
        done
    fi
}

echo "Checking apt-get availability..."

apt_wait

# Update
# Update the package list
apt_get_update_output=$(apt-get update -y 2>&1)

# Function to extract and add missing GPG keys
apt_get_add_missing_keys() {
    echo "$apt_get_update_output" | grep -oP 'NO_PUBKEY \K[A-Z0-9]{16}' | while read -r key; do
        echo "Missing key detected: $key"
        echo "Attempting to retrieve the key..."
        sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys "$key"
        if [ $? -eq 0 ]; then
            echo "Key $key successfully added."
        else
            echo "Failed to add key $key."
        fi
    done

    apt-get update -y 2>&1
}

# Check for the specific error in apt-get apt_get_update_output
if echo "$apt_get_update_output" | grep -q 'NO_PUBKEY'; then
    echo "GPG key errors detected. Attempting to fix..."
    apt_get_add_missing_keys
else
    echo "No GPG key errors detected."
    echo "Reading package lists... Done"
fi

# Install Base PHP Packages
apt-get install -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold" -f -y --force-yes \
php8.1-cli php8.1-fpm php8.1-dev \
php8.1-pgsql php8.1-sqlite3 php8.1-gd \
php8.1-curl php8.1-memcached \
php8.1-imap php8.1-mysql php8.1-mbstring \
php8.1-xml php8.1-zip php8.1-bcmath php8.1-soap \
php8.1-intl php8.1-readline php8.1-msgpack php8.1-igbinary \
php8.1-gmp 

# Install Composer Package Manager

if [ ! -f /usr/local/bin/composer ]; then
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
fi

# Misc. PHP CLI Configuration

sudo sed -i "s/error_reporting = .*/error_reporting = E_ALL/" /etc/php/8.1/cli/php.ini
sudo sed -i "s/display_errors = .*/display_errors = On/" /etc/php/8.1/cli/php.ini
sudo sed -i "s/;cgi.fix_pathinfo=1/cgi.fix_pathinfo=0/" /etc/php/8.1/cli/php.ini
sudo sed -i "s/memory_limit = .*/memory_limit = 512M/" /etc/php/8.1/cli/php.ini
sudo sed -i "s/;date.timezone.*/date.timezone = UTC/" /etc/php/8.1/cli/php.ini

# TODO PHP8.1: Remove if condition.

# Ensure PHPRedis Extension Is Available

echo "Configuring PHPRedis"

echo "extension=redis.so" > /etc/php/8.1/mods-available/redis.ini
yes '' | apt install php8.1-redis

# Ensure Imagick Is Available

echo "Configuring Imagick"

apt-get install -y --force-yes libmagickwand-dev
echo "extension=imagick.so" > /etc/php/8.1/mods-available/imagick.ini
yes '' | apt install php8.1-imagick

# Configure FPM Pool Settings

sed -i "s/^user = www-data/user = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^group = www-data/group = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?listen\.owner.*/listen.owner = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?listen\.group.*/listen.group = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?listen\.mode.*/listen.mode = 0660/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?request_terminate_timeout.*/request_terminate_timeout = 60/" /etc/php/8.1/fpm/pool.d/www.conf

# -----------
# Update Other PHP Versions
# -----------
            php_ini_file="/etc/php/8.1/fpm/php.ini"
    
    if [ -f "$php_ini_file" ]; then
        sudo sed -i "s/upload_max_filesize = .*/upload_max_filesize = 250M/" $php_ini_file
        sudo sed -i "s/post_max_size = .*/post_max_size = 256M/" $php_ini_file

        sudo sed -i "s/max_execution_time = .*/max_execution_time = 180/" $php_ini_file
        sudo sed -i "s/max_input_time = .*/max_input_time = 180/" $php_ini_file

        sudo sed -i "s/memory_limit = .*/memory_limit = 512M/" $php_ini_file
                    fi

    if [ -f "/etc/php/8.1/cli/php.ini" ]; then
        sudo sed -i "s/upload_max_filesize = .*/upload_max_filesize = 250M/" /etc/php/8.1/cli/php.ini
        sudo sed -i "s/post_max_size = .*/post_max_size = 256M/" /etc/php/8.1/cli/php.ini
    fi

    echo "Restarting PHP 8.1 FPM"
    service php8.1-fpm restart > /dev/null 2>&1

# -----------
# Update Nginx Conf
# -----------

if grep client_max_body_size /etc/nginx/nginx.conf; then
    sudo sed -i '/client_max_body_size/d' /etc/nginx/nginx.conf
fi

if grep fastcgi_read_timeout /etc/nginx/nginx.conf; then
    sudo sed -i '/fastcgi_read_timeout/d' /etc/nginx/nginx.conf
fi

largest_filesize=0
largest_filesize_file=''
largest_input_time=-1
largest_input_time_file=''

# Loop through each PHP version directory
for php_dir in /etc/php/*; do
    php_version=$(basename "$php_dir")
    php_ini="${php_dir}/fpm/php.ini"

    if [[ -f "$php_ini" ]]; then
        # Extract values
        upload_max_filesize=$(grep -Po '(?<=upload_max_filesize = )\d+' "$php_ini")
        max_input_time=$(grep -Po '(?<=max_input_time = )-?\d+' "$php_ini") # -1 is a valid value, meaning no limit

        # Check and update largest filesize
        if (( upload_max_filesize > largest_filesize )); then
          largest_filesize=$upload_max_filesize
          largest_filesize_file=$php_ini
        fi

        # Check and update largest input time
        if (( max_input_time > largest_input_time )); then
          largest_input_time=$max_input_time
          largest_input_time_file=$php_ini
        fi
    fi

    # Check pool configuration files
    pool_dir="${php_dir}/fpm/pool.d"
    if [[ -d "$pool_dir" ]]; then
        for pool_file in "$pool_dir"/*.conf; do
            # echo "Checking pool file: $pool_file"
            if [[ -f "$pool_file" ]]; then
                # Extract max_input_time from pool files, if defined
                pool_input_time=$(grep -Po '(?<=php_admin_value\[max_input_time\]=)-?\d+' "$pool_file")
                if [[ "$pool_input_time" != "" ]] && (( pool_input_time > largest_input_time )); then
                    largest_input_time=$pool_input_time
                    largest_input_time_file=$php_ini
                fi

                # Extract upload_max_filesize from pool files, if defined
                pool_filesize=$(grep -Po '(?<=php_admin_value\[upload_max_filesize\]=)\d+M' "$pool_file" | grep -Po '\d+')
                if [[ "$pool_filesize" != "" ]] && (( pool_filesize > largest_filesize )); then
                    largest_filesize=$pool_filesize
                    largest_filesize_file=$pool_file
                fi
            fi
        done
    fi
done

echo "Largest upload_max_filesize: ${largest_filesize}M in ${largest_filesize_file}"
echo "Largest max_input_time: ${largest_input_time} in ${largest_input_time_file}"

echo "client_max_body_size ${largest_filesize}M;" > /etc/nginx/conf.d/uploads.conf
echo "fastcgi_read_timeout $largest_input_time;" > /etc/nginx/conf.d/timeout.conf

for php_dir in /etc/php/*; do
    php_version=$(basename "$php_dir")
    # Check pool configuration files
    pool_dir="${php_dir}/fpm/pool.d"
    if [[ -d "$pool_dir" ]]; then
        for pool_file in "$pool_dir"/*.conf; do
            if [[ -f "$pool_file" ]]; then
                echo "Updating Largest request_terminate_timeout=${largest_input_time} in pool file: $pool_file"
                sudo sed -i "s/request_terminate_timeout\( *\)=.*/request_terminate_timeout = $largest_input_time/" "$pool_file"
            fi
        done
    fi
done

NGINX=$(ps aux | grep nginx | grep -v grep)

if [[ -z $NGINX ]]; then
    service nginx start
    echo "Started Nginx"
else
    service nginx reload
    echo "Reloaded Nginx"
fi


# Optimize FPM Processes

sed -i "s/^pm.max_children.*=.*/pm.max_children = 20/" /etc/php/8.1/fpm/pool.d/www.conf

# Ensure Sudoers Is Up To Date

LINE="ALL=NOPASSWD: /usr/sbin/service php8.1-fpm reload"
FILE="/etc/sudoers.d/php-fpm"
grep -qF -- "xcloud $LINE" "$FILE" || echo "xcloud $LINE" >> "$FILE"

# Configure Sessions Directory Permissions

chmod 733 /var/lib/php/sessions
chmod +t /var/lib/php/sessions

# Write Systemd File For Linode

update-alternatives --set php /usr/bin/php8.1

provisionPing 21
# Install Nginx & PHP-FPM
apt-get install -y --force-yes nginx

systemctl enable nginx.service

# Generate dhparam File

openssl dhparam -out /etc/nginx/dhparams.pem 2048

# Tweak Some PHP-FPM Settings

sed -i "s/error_reporting = .*/error_reporting = E_ALL/" /etc/php/8.1/fpm/php.ini
sed -i "s/display_errors = .*/display_errors = On/" /etc/php/8.1/fpm/php.ini
sed -i "s/;cgi.fix_pathinfo=1/cgi.fix_pathinfo=0/" /etc/php/8.1/fpm/php.ini
sed -i "s/memory_limit = .*/memory_limit = 512M/" /etc/php/8.1/fpm/php.ini
sed -i "s/;date.timezone.*/date.timezone = UTC/" /etc/php/8.1/fpm/php.ini

# Configure FPM Pool Settings

sed -i "s/^user = www-data/user = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^group = www-data/group = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?listen\.owner.*/listen.owner = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?listen\.group.*/listen.group = xcloud/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?listen\.mode.*/listen.mode = 0660/" /etc/php/8.1/fpm/pool.d/www.conf
sed -i "s/^;\?request_terminate_timeout.*/request_terminate_timeout = 60/" /etc/php/8.1/fpm/pool.d/www.conf

# Configure Primary Nginx Settings

sed -i "s/user www-data;/user xcloud;/" /etc/nginx/nginx.conf
sed -i "s/worker_processes.*/worker_processes auto;/" /etc/nginx/nginx.conf
sed -i "s/# multi_accept.*/multi_accept on;/" /etc/nginx/nginx.conf
sed -i "s/# server_names_hash_bucket_size.*/server_names_hash_bucket_size 128;/" /etc/nginx/nginx.conf

# Configure Gzip

cat > /etc/nginx/conf.d/gzip.conf << EOF
gzip_comp_level 5;
gzip_min_length 256;
gzip_proxied any;
gzip_vary on;
gzip_http_version 1.1;

gzip_types
application/atom+xml
application/javascript
application/json
application/ld+json
application/manifest+json
application/rss+xml
application/vnd.geo+json
application/vnd.ms-fontobject
application/x-font-ttf
application/x-web-app-manifest+json
application/xhtml+xml
application/xml
font/opentype
image/bmp
image/svg+xml
image/x-icon
text/cache-manifest
text/css
text/plain
text/vcard
text/vnd.rim.location.xloc
text/vtt
text/x-component
text/x-cross-domain-policy;

EOF

# Configure Nginx Default Page
if [ -f /var/www/html/*.html ]; then
cat > /var/www/html/*.html << 'EOF'
<h1>Welcome to nginx!</h1>
<p>Configured by <a href="https://xcloud.host">xCloud</a></p>
EOF
fi

service nginx restart

# Restart Nginx & PHP-FPM Services

#service nginx restart
NGINX=$(ps aux | grep nginx | grep -v grep)
if [[ -z $NGINX ]]; then
    service nginx start
    echo "Started Nginx"
else
    service nginx reload
    echo "Reloaded Nginx"
fi

PHP=$(ps aux | grep php-fpm | grep -v grep)
if [[ ! -z $PHP ]]; then
    service php5.6-fpm restart > /dev/null 2>&1
    service php7.0-fpm restart > /dev/null 2>&1
    service php7.1-fpm restart > /dev/null 2>&1
    service php7.2-fpm restart > /dev/null 2>&1
    service php7.3-fpm restart > /dev/null 2>&1
    service php7.4-fpm restart > /dev/null 2>&1
    service php8.0-fpm restart > /dev/null 2>&1
    service php8.1-fpm restart > /dev/null 2>&1
    service php8.2-fpm restart > /dev/null 2>&1
    service php8.3-fpm restart > /dev/null 2>&1
fi

# Add xCloud User To www-data Group

usermod -a -G www-data xcloud
id xcloud
groups xcloud



provisionPing 22
# Install NodeJS

apt_wait

curl --silent --location https://deb.nodesource.com/setup_18.x | bash -

# Update the package list
apt_get_update_output=$(apt-get update -y 2>&1)

# Function to extract and add missing GPG keys
apt_get_add_missing_keys() {
    echo "$apt_get_update_output" | grep -oP 'NO_PUBKEY \K[A-Z0-9]{16}' | while read -r key; do
        echo "Missing key detected: $key"
        echo "Attempting to retrieve the key..."
        sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys "$key"
        if [ $? -eq 0 ]; then
            echo "Key $key successfully added."
        else
            echo "Failed to add key $key."
        fi
    done

    apt-get update -y 2>&1
}

# Check for the specific error in apt-get apt_get_update_output
if echo "$apt_get_update_output" | grep -q 'NO_PUBKEY'; then
    echo "GPG key errors detected. Attempting to fix..."
    apt_get_add_missing_keys
else
    echo "No GPG key errors detected."
    echo "Reading package lists... Done"
fi

apt-get install -y --force-yes nodejs

npm install -g yarn

provisionPing 23
# Install & Configure Redis Server

apt-get install -y redis-server
sed -i 's/bind 127.0.0.1/bind 0.0.0.0/' /etc/redis/redis.conf
service redis-server restart
systemctl enable redis-server

yes '' | pecl install -f redis

# Ensure PHPRedis extension is available
if pecl list | grep redis >/dev/null 2>&1;
then
    echo "Configuring PHPRedis"
    echo "extension=redis.so" > /etc/php/8.1/mods-available/redis.ini
    yes '' | apt install php8.1-redis
fi

# check if user has selected mysql 8 or not
provisionPing 24
# Set The Automated Root Password

export DEBIAN_FRONTEND=noninteractive
export NEEDRESTART_MODE=1

# Update the package list
apt_get_update_output=$(apt-get update -y 2>&1)

# Function to extract and add missing GPG keys
apt_get_add_missing_keys() {
    echo "$apt_get_update_output" | grep -oP 'NO_PUBKEY \K[A-Z0-9]{16}' | while read -r key; do
        echo "Missing key detected: $key"
        echo "Attempting to retrieve the key..."
        sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys "$key"
        if [ $? -eq 0 ]; then
            echo "Key $key successfully added."
        else
            echo "Failed to add key $key."
        fi
    done

    apt-get update -y 2>&1
}

# Check for the specific error in apt-get apt_get_update_output
if echo "$apt_get_update_output" | grep -q 'NO_PUBKEY'; then
    echo "GPG key errors detected. Attempting to fix..."
    apt_get_add_missing_keys
else
    echo "No GPG key errors detected."
    echo "Reading package lists... Done"
fi


CONFIG_FILE="/etc/mysql/mysql.conf.d/mysqld.cnf"

function version { echo "$@" | awk -F. '{ printf("%d%03d%03d%03d\n", $1,$2,$3,$4); }'; }

UBUNTU_VERSION=$(lsb_release -rs)
echo "Server on Ubuntu ${UBUNTU_VERSION}"
if [ $(version $UBUNTU_VERSION) -le $(version "20.04") ]; then
    wget -c https://dev.mysql.com/get/mysql-apt-config_0.8.24-1_all.deb
    dpkg --install mysql-apt-config_0.8.24-1_all.deb
fi

debconf-set-selections <<< "mysql-community-server mysql-community-server/data-dir select ''"
debconf-set-selections <<< "mysql-community-server mysql-community-server/root-pass password oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H"
debconf-set-selections <<< "mysql-community-server mysql-community-server/re-root-pass password oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H"

# Install MySQL

apt-get install -y mysql-community-server
apt-get install -y mysql-server

# Configure Access Permissions For Root
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"

# Bind To All Interfaces
sed -i '/^bind-address/s/bind-address.*=.*/bind-address = */' "$CONFIG_FILE"

# Configure Password Expiration
echo "default_password_lifetime = 0" >> "$CONFIG_FILE"

# Set Character Set
echo "" >> /etc/mysql/my.cnf
echo "[mysqld]" >> /etc/mysql/my.cnf
echo "default_authentication_plugin=mysql_native_password" >> /etc/mysql/my.cnf
echo "skip-log-bin" >> /etc/mysql/my.cnf

# Configure Max Connections
RAM=$(awk '/^MemTotal:/{printf "%3.0f", $2 / (1024 * 1024)}' /proc/meminfo)
MAX_CONNECTIONS=$(( 70 * $RAM ))
REAL_MAX_CONNECTIONS=$(( MAX_CONNECTIONS>70 ? MAX_CONNECTIONS : 100 ))

# Changing value and uncommenting 'max_connections' if exist in the config file
sed -i -E "s/^[[:space:]]*#?[[:space:]]*(max_connections[[:space:]]*=[[:space:]]*).*/\1$REAL_MAX_CONNECTIONS/" "$CONFIG_FILE"

# Check if 'max_connections' exists after the sed operation
if ! grep -qE "^[[:space:]]*max_connections[[:space:]]*=" "$CONFIG_FILE"; then
    # 'max_connections' not found, append it
    echo "max_connections = $REAL_MAX_CONNECTIONS" >> "$CONFIG_FILE"
fi

# Configure Access Permissions For Root & xCloud User
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "FLUSH PRIVILEGES;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'root'@'************' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'root'@'%' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO root@'************' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO root@'localhost' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO root@'%' WITH GRANT OPTION;"

service mysql restart

mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'xcloud'@'************' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'xcloud'@'%' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO 'xcloud'@'************' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO 'xcloud'@'localhost' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO 'xcloud'@'%' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "FLUSH PRIVILEGES;"

# Create The Initial Database If Specified

mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE DATABASE xcloud CHARACTER SET utf8 COLLATE utf8_unicode_ci;"

service mysql restart
# If MySQL Fails To Start, Re-Install It

if [[ $? -ne 0 ]]; then
    echo "Purging previous MySQL8 installation..."

    sudo apt-get purge mysql-server mysql-community-server

    sudo apt-get autoclean && sudo apt-get clean

    # Set The Automated Root Password

export DEBIAN_FRONTEND=noninteractive
export NEEDRESTART_MODE=1

# Update the package list
apt_get_update_output=$(apt-get update -y 2>&1)

# Function to extract and add missing GPG keys
apt_get_add_missing_keys() {
    echo "$apt_get_update_output" | grep -oP 'NO_PUBKEY \K[A-Z0-9]{16}' | while read -r key; do
        echo "Missing key detected: $key"
        echo "Attempting to retrieve the key..."
        sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys "$key"
        if [ $? -eq 0 ]; then
            echo "Key $key successfully added."
        else
            echo "Failed to add key $key."
        fi
    done

    apt-get update -y 2>&1
}

# Check for the specific error in apt-get apt_get_update_output
if echo "$apt_get_update_output" | grep -q 'NO_PUBKEY'; then
    echo "GPG key errors detected. Attempting to fix..."
    apt_get_add_missing_keys
else
    echo "No GPG key errors detected."
    echo "Reading package lists... Done"
fi


CONFIG_FILE="/etc/mysql/mysql.conf.d/mysqld.cnf"

function version { echo "$@" | awk -F. '{ printf("%d%03d%03d%03d\n", $1,$2,$3,$4); }'; }

UBUNTU_VERSION=$(lsb_release -rs)
echo "Server on Ubuntu ${UBUNTU_VERSION}"
if [ $(version $UBUNTU_VERSION) -le $(version "20.04") ]; then
    wget -c https://dev.mysql.com/get/mysql-apt-config_0.8.24-1_all.deb
    dpkg --install mysql-apt-config_0.8.24-1_all.deb
fi

debconf-set-selections <<< "mysql-community-server mysql-community-server/data-dir select ''"
debconf-set-selections <<< "mysql-community-server mysql-community-server/root-pass password oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H"
debconf-set-selections <<< "mysql-community-server mysql-community-server/re-root-pass password oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H"

# Install MySQL

apt-get install -y mysql-community-server
apt-get install -y mysql-server

# Configure Access Permissions For Root
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"

# Bind To All Interfaces
sed -i '/^bind-address/s/bind-address.*=.*/bind-address = */' "$CONFIG_FILE"

# Configure Password Expiration
echo "default_password_lifetime = 0" >> "$CONFIG_FILE"

# Set Character Set
echo "" >> /etc/mysql/my.cnf
echo "[mysqld]" >> /etc/mysql/my.cnf
echo "default_authentication_plugin=mysql_native_password" >> /etc/mysql/my.cnf
echo "skip-log-bin" >> /etc/mysql/my.cnf

# Configure Max Connections
RAM=$(awk '/^MemTotal:/{printf "%3.0f", $2 / (1024 * 1024)}' /proc/meminfo)
MAX_CONNECTIONS=$(( 70 * $RAM ))
REAL_MAX_CONNECTIONS=$(( MAX_CONNECTIONS>70 ? MAX_CONNECTIONS : 100 ))

# Changing value and uncommenting 'max_connections' if exist in the config file
sed -i -E "s/^[[:space:]]*#?[[:space:]]*(max_connections[[:space:]]*=[[:space:]]*).*/\1$REAL_MAX_CONNECTIONS/" "$CONFIG_FILE"

# Check if 'max_connections' exists after the sed operation
if ! grep -qE "^[[:space:]]*max_connections[[:space:]]*=" "$CONFIG_FILE"; then
    # 'max_connections' not found, append it
    echo "max_connections = $REAL_MAX_CONNECTIONS" >> "$CONFIG_FILE"
fi

# Configure Access Permissions For Root & xCloud User
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "FLUSH PRIVILEGES;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'root'@'************' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'root'@'%' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO root@'************' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO root@'localhost' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO root@'%' WITH GRANT OPTION;"

service mysql restart

mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'xcloud'@'************' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE USER 'xcloud'@'%' IDENTIFIED BY 'oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H';"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO 'xcloud'@'************' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO 'xcloud'@'localhost' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "GRANT ALL PRIVILEGES ON *.* TO 'xcloud'@'%' WITH GRANT OPTION;"
mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "FLUSH PRIVILEGES;"

# Create The Initial Database If Specified

mysql --user="root" --password="oWpqu0LQxrKVNqC8j1BHvhXNLy7X8e7H" -e "CREATE DATABASE xcloud CHARACTER SET utf8 COLLATE utf8_unicode_ci;"

service mysql restart
fi

apt_wait

provisionPing 25
curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar

php wp-cli.phar --info

chmod +x wp-cli.phar
sudo mv wp-cli.phar /usr/local/bin/wp

provisionPing 26
# Set The Proper Directory Permissions

chown -R xcloud:xcloud /home/<USER>
chmod -R 750 /home/<USER>

chmod 700 /home/<USER>/.ssh

chmod 644 /home/<USER>/.ssh/authorized_keys
chmod 640 /home/<USER>/.ssh/id_rsa.pub
chmod 600 /home/<USER>/.ssh/id_rsa


# Clean up files older than 1 day




find /root/.xcloud -type f -mtime +1 -print0 | xargs -r0 rm --
find /home/<USER>/.xcloud -type f -mtime +1 -print0 | xargs -r0 rm --

rm -f /root/mysql-apt-config_0.8.24-1_all.deb

provisionPing 27
echo "Started Installing Monitoring.."

echo "Generating Monitoring Script..."
mkdir -p /home/<USER>/.xcloud-monitoring

#installing dependencies
apt install bc -y

cat > /home/<USER>/.xcloud-monitoring/monitor.sh << 'EOF'
#!/bin/bash
# Collects system performance statistics such as CPU, memory, and disk
# usage as well as top processes ran by users.
#
# All size values are in KiB (memory, disk, etc).

# Takes these command line arguments:
# $1 - nocallback - do not call back to the server
# $2 - cpuThreshold in % of total across all CPUs. Default is provided in no-args option.
# $3 - memThreshold in % of total avail. Default is provided in no-args option.
#
# EXAMPLE USAGE:
# ./monitor.xc nocallback 5 20
# ./monitor.xc

# Debugging and error handling
# Stop this script on any error. Unless you want to get data by any means
# this is a good option.
set -e
# Debugging options:
# set -x
# set -v

# Validate command line arguments
if [[  "$#" > 3 ]]; then
  echo "Wrong number of arguments supplied. Expects 1-3 arguments: <cpu>, <mem>, or callback."
  exit 1
fi

# Configurable variables
#
# CPU threshold in %of total CPU consumption (100%)
# All processes below this threshold will be discarded.
if [ -z "$2" ]; then
  cpuThreshold="1"
else
  cpuThreshold="$2"
fi
# % of memory usage. All processes below this threshold will be discarded.
if [ -z "$3" ]; then
  memThreshold="10"
else
  memThreshold="$3"
fi

# General OS props
HOST=$HOSTNAME
#OS=$(uname -a)
#currently keeping it static as it's taking huge cpu to process.. maybe we can inject once we insert the script!
OS=$(cat /etc/*-release | grep "PRETTY_NAME" | sed 's/PRETTY_NAME=//g')
OS=$(echo $OS | sed 's/\"//g')
#ignoring for now
#UPTIME=$(uptime)
#ARCHITECTURE=$(uname -m)

# Memory
free=$(free -m)
cpuCores=$(nproc)
uptime=$(uptime -p)
cpuSpeed=$(lscpu | grep "CPU MHz" | awk '{print $3}')
memTotal=$(echo "$free" | grep ^Mem | tr -s ' ' | cut -d ' ' -f 2)
memFree=$(echo "$free" | grep ^Mem | tr -s ' ' | cut -d ' ' -f 4)
memCached=$(echo "$free" | grep ^Mem | tr -s ' ' | cut -d ' ' -f 6)
memAvailable=$(echo "$free" | grep ^Mem | tr -s ' ' | cut -d ' ' -f 7)
#memUsed=$(($memTotal - $memAvailable))
memUsed=$(echo "$free" | grep ^Mem | tr -s ' ' | cut -d ' ' -f 3)
memPercent=$(echo "$free" | grep Mem | awk '{print $3/$2 * 100.0}')
swapTotal=$(echo "$free" | grep ^Swap | tr -s ' ' | cut -d ' ' -f 2)
swapFree=$(echo "$free" | grep ^Swap | tr -s ' ' | cut -d ' ' -f 4)
swapUsed=$(echo "$free" | grep ^Swap | tr -s ' ' | cut -d ' ' -f 3)

#server status
phpVersion=$(php -v | grep -oP 'PHP \K\d+\.\d+' | head -1)
nginxStatus=$(systemctl status nginx 2>1 | grep "Active" | awk '{print $2}')
mysqlStatus=$(systemctl status mysql 2>1 | grep "Active" | awk '{print $2}')
phpStatus=$(systemctl status "php$phpVersion-fpm" 2>1 | grep "Active" | awk '{print $2}')
redisStatus=$(systemctl status redis 2>1 | grep "Active" | awk '{print $2}')
openSshStatus=$(systemctl status ssh 2>1 | grep "Active" | awk '{print $2}')
superVisorStatus=$(systemctl status supervisor 2>1 | grep "Active" | awk '{print $2}')
openLiteSpeedStatus=$(systemctl status lsws 2>1 | grep "Active" | awk '{print $2}')

#restart ssh if openSshStatus is not active
if [ "$openSshStatus" != "active" ]; then
  sudo service ssh restart
  openSshStatus=$(systemctl status ssh | grep "Active" | awk '{print $2}')
  SSH_JSON_DATA='{ "hostname": "'"$HOST"'","status": "'"$openSshStatus"'" }'
  curl -s -X POST -H 'Content-type: application/json' -d "$SSH_JSON_DATA" --insecure https://2a1c-103-95-96-8.ngrok-free.app/api/callback/monitoring/server/EQLRljamKro1yMN7Yj6jYbA2VDBzw5OvqZpG/ssh-reboot >/dev/null
fi

# CPU
cpuThreads=$(grep processor /proc/cpuinfo | wc -l)
#cpuUtilization=$(top -bn3 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}' | tail -1)
cpuUtilization=$((100 - $(vmstat 2 2 | tail -1 | awk '{print $15}' | sed 's/%//')))

cpuAverage=$(cat /proc/loadavg)
cpuAvergaeAll=$(echo "$cpuAverage" | awk '{print $1 " " $2 " " $3}')
cpuAverage1Minute=$(echo "$cpuAverage" | awk '{print $1}')
cpuAverage5Minutes=$(echo "$cpuAverage" | awk '{print $2}')
cpuAverage15Minutes=$(echo "$cpuAverage" | awk '{print $3}')

# Disk
disk=$(df -H -P -x tmpfs -x devtmpfs -x ecryptfs -x nfs -x cifs -T | tail -n+2)
diskTotal=$(echo "$disk" | head -n 1 | awk '{print $3}')
diskUsed=$(echo "$disk" | head -n 1 | awk '{print $4}')
diskAvailable=$(echo "$disk" | head -n 1 | awk '{print $5}')
diskUsedPercent=$(echo "$disk" | head -n 1 | awk '{print $6}')
disksJson=$(for d in $(echo "$disk" | awk '{print "{""\"total\":" "\""$3"\"" ", \"used\":" "\""$4"\"" ", \"usedPercent\":" "\""$6"\"" ", \"mountPoint\":" "\""$7"\"" "},"}'); do echo $d; done | sed '$s/.$//')

# Initialize the JSON array
siteStates="["

# Get information for each user
for user in $(awk -F: '$3 >= 1000 && $1 != "nobody" {print $1}' /etc/passwd); do
  # Find all directories owned by this user in /var/www folder
  directories=$(find /var/www -maxdepth 1 -type d -user "$user")

  # If no directories are found, continue to the next user
  if [ -z "$directories" ]; then
    continue
  fi

  # Iterate over each directory using process substitution to avoid subshell
  while read directory; do
    if [ -z "$directory" ]; then
      continue
    fi
    siteName=$(basename "$directory")

    # Get process IDs (PIDs) for processes owned by the user
    # Disabling exit on error, as if no process is running for a user, it will throw an error
    set +e
    pids=$(ps -u "$user" -o pid=)
    # Enabling exit on error
    set -e

    # Initialize variables to store total memory and CPU usage
    total_memory=0
    total_cpu=0

    # Iterate through each PID and accumulate memory and CPU usage
    for pid in $pids; do
      # Get memory usage for the process (in KB)
      memory=$(ps -p "$pid" -o rss=)

      # Get CPU usage for the process (as a percentage)
      cpu=$(ps -p "$pid" -o %cpu=)

      # Add memory and CPU usage to the totals
      total_memory=$((total_memory + memory))
      total_cpu=$(echo "$total_cpu + $cpu" | bc)
    done

    # Get disk usage for the directory
    disk=$(du -s "$directory" | cut -f1) # Disk usage in KB
    disk=$(awk "BEGIN {print $disk/1024}") # Convert to MB

    # Add the information for this user and site to the JSON array
    siteStates="$siteStates{\"site_user\":\"$user\",\"cpu\":\"$total_cpu\",\"ram\":\"$total_memory\",\"disk\":\"$disk\",\"site_name\":\"$siteName\"},"
  done < <(echo "$directories") # Process substitution
done

# Remove the last comma and close the JSON array
siteStatesJson="${siteStates%,}]"

# Final result in JSON
JSON='
{
  "hostname": "'"$HOST"'",
  "operatingSystem": "'"$OS"'",
  "allSiteState": '$siteStatesJson',
  "memory":
  {
    "total": "'"$memTotal"'",
    "used": "'"$memUsed"'",
    "free": "'"$memFree"'",
    "cache": "'"$memCached"'",
    "available": "'"$memAvailable"'",
    "swap_total": "'"$swapTotal"'",
    "swap_used": "'"$swapUsed"'",
    "swap_free": "'"$swapFree"'",
    "percent": "'"$memPercent"'"
  },
  "cpu":
  {
    "uptime": "'"$uptime"'",
    "cores": "'"$cpuCores"'",
    "cpu_speed": "'"$cpuSpeed"'",
    "threads": "'"$cpuThreads"'",
    "usedPercent": "'"$cpuUtilization"'",
    "average_1_minute": "'"$cpuAverage1Minute"'",
    "average_5_minutes": "'"$cpuAverage5Minutes"'",
    "average_15_minutes": "'"$cpuAverage15Minutes"'"
  },
  "disks": [
    '$disksJson'
  ],
  "services": {
    "nginx": "'"$nginxStatus"'",
    "mysql": "'"$mysqlStatus"'",
    "php": "'"$phpStatus"'",
    "phpVersion": "'"$phpVersion"'",
    "redis": "'"$redisStatus"'",
    "ssh": "'"$openSshStatus"'",
    "supervisor": "'"$superVisorStatus"'",
    "lsws": "'"$openLiteSpeedStatus"'"
    }
}'

JSON_SLACK='{"text":"Host: '"$HOST"'\nOS: '"$OS"'\nMemory: '"$memTotal"' MB total, '"$memUsed"' MB used, '"$memFree"'MB free, '"$memCached"' MB cached, '"$memAvailable"' MB available\nSwap: '"$swapTotal"' MB total, '"$swapUsed"' MB used, '"$swapFree"' MB free\nCPU: '"$cpuAvergaeAll"' average, '"$cpuThreads"' threads, '"$cpuUtilization"'% used\nDisk: '"$diskTotal"' total, '"$diskUsed"' used, '"$diskAvailable"' free,'"$diskUsedPercent"' used"}'

# Result output: STDOUT or HTTP
if [ -z "$1" ]; then
  #  curl -X POST -H "Content-Type: application/json" -d "$JSON" "$3" as nocallback not exits
  curl -s -X POST -H 'Content-type: application/json' -d "$JSON" --insecure https://2a1c-103-95-96-8.ngrok-free.app/api/callback/monitoring/server/EQLRljamKro1yMN7Yj6jYbA2VDBzw5OvqZpG >/dev/null
    echo "Callback sent"
else
  echo "$JSON"
fi

#print current time to a new file with new line
# Check available disk space
space=$(df /home/<USER>/.xcloud-monitoring | awk 'END{print $4}')

# If there is less than 10 MB available, print an error message
if [ $space -ge 10000 ]; then
    echo "Callback sent at: $(date +%s)" >> /home/<USER>/.xcloud-monitoring/monitor.log
fi
EOF

echo "Encrypting Monitoring Script..."
chmod +x /home/<USER>/.xcloud-monitoring/monitor.sh
apt install gcc -y
apt install shc -y
shc -f /home/<USER>/.xcloud-monitoring/monitor.sh -o /home/<USER>/.xcloud-monitoring/monitor.xc
rm /home/<USER>/.xcloud-monitoring/monitor.sh.x.c
rm /home/<USER>/.xcloud-monitoring/monitor.sh

cd /home/<USER>/.xcloud-monitoring/ && ./monitor.xc  2>&1

echo "Generating Monitoring Corn..."
rm -f /etc/cron.d/server-monitoring

# Add The Cron File

cat > /etc/cron.d/server-monitoring << EOF
# xCloud Server Monitoring
*/60 * * * * root cd /home/<USER>/.xcloud-monitoring/ && ./monitor.xc  2>&1
EOF

echo "Monitoring Installed Successfully.."

provisionPing 28

