<?php

namespace Tests\Unit\Services\OneClickApp;

use App\Enums\SiteType;
use App\Enums\XcloudBilling\PlansEnum;
use App\Models\Server;
use App\Models\Site;
use App\Models\User;
use App\Services\OneClickApp\OneClickServerRequirementsValidator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Tests\TestCase;

class OneClickServerRequirementsValidatorTest extends TestCase
{
    use RefreshDatabase;

    protected OneClickServerRequirementsValidator $validator;
    protected Server $server;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user with a team for auth context
        $user = User::factory()->withPersonalTeam()->create();
        $this->actingAs($user);

        $this->validator = new OneClickServerRequirementsValidator();
        $this->server = Server::factory()->create([
            'ubuntu_version' => '22.04',
            'node_version' => '20',
            'team_id' => $user->currentTeam->id,
            'user_id' => $user->id,
        ]);
    }

    /** @test */
    public function it_validates_ubuntu_version_requirement()
    {
        // Server with unsupported Ubuntu version
        $server = Server::factory()->create([
            'ubuntu_version' => '20.04',
            'team_id' => auth()->user()->currentTeam->id,
            'user_id' => auth()->id(),
        ]);

        $response = $this->validator->validate($server);

        $this->assertNotNull($response);
        $this->assertEquals('Unsupported Ubuntu Version', session('flash.message'));
    }

    /** @test */
    public function it_validates_node_version_for_n8n_with_incompatible_version()
    {
        // Set an incompatible Node.js version
        $this->server->update(['node_version' => '16']);

        // Create a request with app_slug=n8n
        $this->withRequestInput(['app_slug' => 'n8n']);

        $response = $this->validator->validate($this->server);

        $this->assertNotNull($response);
        $this->assertEquals('Incompatible Node.js Version', session('flash.message'));
    }

    /** @test */
    public function it_validates_node_version_for_n8n_with_null_version()
    {
        // Create a mock server with null node_version
        $mockServer = $this->createPartialMock(Server::class, ['getNodeVersions']);
        $mockServer->node_version = null;
        $mockServer->ubuntu_version = '22.04';

        // Set up the mock to return null for node_version
        $mockServer->expects($this->any())
            ->method('getNodeVersions')
            ->willReturn(['default' => null, 'versions' => []]);

        // Create a request with app_slug=n8n
        $this->withRequestInput(['app_slug' => 'n8n']);

        $response = $this->validator->validate($mockServer);

        $this->assertNotNull($response);
        $this->assertEquals('Incompatible Node.js Version', session('flash.message'));
    }

    /** @test */
    public function it_passes_validation_with_compatible_node_version_for_n8n()
    {
        // Set a compatible Node.js version
        $this->server->update(['node_version' => '18']);

        // Create a request with app_slug=n8n
        $this->withRequestInput(['app_slug' => 'n8n']);

        $response = $this->validator->validate($this->server);

        $this->assertNull($response);
    }

    /** @test */
    public function it_passes_validation_with_higher_node_version_for_n8n()
    {
        // Set a higher compatible Node.js version
        $this->server->update(['node_version' => '20']);

        // Create a request with app_slug=n8n
        $this->withRequestInput(['app_slug' => 'n8n']);

        $response = $this->validator->validate($this->server);

        $this->assertNull($response);
    }

    /**
     * Helper method to set request input
     */
    protected function withRequestInput(array $input)
    {
        $this->app->instance('request', new Request($input));
    }
}
