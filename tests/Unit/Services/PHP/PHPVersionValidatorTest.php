<?php

namespace Tests\Unit\Services\PHP;

use App\Enums\Stack;
use App\Models\Server;
use App\Services\PHP\PHPVersionValidator;
use Tests\TestCase;

class PHPVersionValidatorTest extends TestCase
{
    private PHPVersionValidator $phpVersionValidator;

    protected function setUp(): void
    {
        parent::setUp();

        $this->phpVersionValidator = new PHPVersionValidator();
    }

    public function testGetVersion(): void
    {
        $this->assertEquals('7.4', $this->phpVersionValidator->getVersion(7.4));
        $this->assertEquals('8.0', $this->phpVersionValidator->getVersion('php8.0'));
        $this->assertEquals('8.1', $this->phpVersionValidator->getVersion('8.1.34221'));
        $this->assertEquals('8.1', $this->phpVersionValidator->getVersion('php8.1.34221'));
        $this->assertEquals('8.3', $this->phpVersionValidator->getVersion('foobar')); //  // default
        $this->assertEquals('8.3', $this->phpVersionValidator->getVersion('')); // default

        // openlitespeed doesn't all versions
        $this->assertEquals('5.6', $this->phpVersionValidator->getVersion(5.6));
        $this->assertEquals('8.3', $this->phpVersionValidator->getVersion(8.3));
        $this->assertEquals('5.6', $this->phpVersionValidator->getVersion(5.6, new Server(['stack' => Stack::Nginx])));
        $this->assertEquals('8.3', $this->phpVersionValidator->getVersion(8.3, new Server(['stack' => Stack::Nginx])));

        // it should use 8.1 for openlitespeed
        $this->assertEquals('8.3', $this->phpVersionValidator->getVersion(5.6, new Server(['stack' => Stack::OpenLiteSpeed]))); // default
        $this->assertEquals('8.1', $this->phpVersionValidator->getVersion(8.1, new Server(['stack' => Stack::OpenLiteSpeed])));
        $this->assertEquals('8.2', $this->phpVersionValidator->getVersion(8.2, new Server(['stack' => Stack::OpenLiteSpeed])));
        $this->assertEquals('8.3', $this->phpVersionValidator->getVersion(8.3, new Server(['stack' => Stack::OpenLiteSpeed])));
    }
}
