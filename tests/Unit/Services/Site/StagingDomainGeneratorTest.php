<?php

namespace Tests\Unit\Services\Site;

use App\Services\Site\StagingDomainGenerator;
use Illuminate\Support\Str;
use PHPUnit\Framework\TestCase;

class StagingDomainGeneratorTest extends TestCase
{
    protected StagingDomainGenerator $generator;

    protected function setUp(): void
    {
        $this->generator = new StagingDomainGenerator();
    }

    public function testGenerateStagingDomainWithSpace()
    {
        //1 random string
        $random = Str::lower(Str::random(3));
        $domain = 'example.com www.example.com';
        $siteId = 1;
        $expectedDomain = 'testing-1-'.$random.'-example-com';


        $generatedDomain = $this->generator->generateStagingDomain($domain, $siteId, $random);

        $this->assertEquals($expectedDomain, $generatedDomain);
    }

    public function testGenerateStagingDomainWithoutSubdomain()
    {
        //1 random string
        $random = Str::lower(Str::random(3));
        $domain = 'example.com';
        $siteId = 1;
        $expectedDomain = 'testing-1-'.$random.'-example-com';


        $generatedDomain = $this->generator->generateStagingDomain($domain, $siteId, $random);

        $this->assertEquals($expectedDomain, $generatedDomain);
    }

    public function testGenerateStagingDomainWithSubdomain()
    {
        $random = Str::lower(Str::random(3));
        $domain = 'sub.example.com';
        $siteId = 1;
        $expectedDomain = 'testing-1-'.$random.'-sub-example-com';


        $generatedDomain = $this->generator->generateStagingDomain($domain, $siteId, $random);

        $this->assertEquals($expectedDomain, $generatedDomain);
    }
}
