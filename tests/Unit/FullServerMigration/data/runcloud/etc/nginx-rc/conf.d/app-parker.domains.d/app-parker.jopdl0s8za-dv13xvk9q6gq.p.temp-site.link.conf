server {
    server_name  app-parker.jopdl0s8za-dv13xvk9q6gq.p.temp-site.link;

    listen       80;
    listen       [::]:80;

    return       301 https://$host$request_uri;
}
# REDIRECT_BLOCK_HTTP

server {
    server_name  app-parker.jopdl0s8za-dv13xvk9q6gq.p.temp-site.link;

    listen 443 ssl http2;
    listen [::]:443 ssl http2;

    brotli on;
    brotli_static off;
    brotli_min_length 100;
    brotli_buffers 16 8k;
    brotli_comp_level 5;
    brotli_types *;

    # SSL config
    ssl_certificate             /etc/nginx-rc/conf.d/app-parker.ssl.d/app-parker.jopdl0s8za-dv13xvk9q6gq.p.temp-site.link.crt;
    ssl_certificate_key         /etc/nginx-rc/conf.d/app-parker.ssl.d/app-parker.jopdl0s8za-dv13xvk9q6gq.p.temp-site.link.key;
    ssl_prefer_server_ciphers   on;
    ssl_session_timeout         5m;
    ssl_protocols               TLSv1.2 TLSv1.3;
    ssl_stapling                off;
    ssl_stapling_verify         off;
    resolver                    ******* ******* valid=86400s;
    resolver_timeout            5s;
    ssl_ciphers                 "EECDH+AESGCM:EDH+AESGCM";
    ssl_ecdh_curve              secp384r1;
    ssl_session_cache           shared:SSL:10m;
    ssl_session_tickets         off;
    ssl_dhparam                 /etc/nginx-rc/dhparam.pem;

    # HSTS DISABLED

    include /etc/nginx-rc/conf.d/app-parker.d/main.conf;
}
# HTTPS_BLOCK
