# 6G Firewall by <PERSON> (https://perishablepress.com/6g/)
# You may edit this file as you wish.
# It will not be overwritten when we updated this config.
# However, if we update this file, you will have a backup of the new files resides inside this folder and you can merge them as you wish.
# But if you would like us to stay updated with the new updated rules, please ping us by opening a support ticket

map $http_user_agent $bad_bot_6g {
    default "allow";
    "~*([a-z0-9]{2000})" "block_bad_bot_rule_1";
    ~*(archive.org|binlar|casper|checkpriv|choppy|clshttp|cmsworld|diavol|dotbot|extract|feedfinder|flicky|g00g1e|harvest|heritrix|httrack|kmccrew|loader|miner|nikto|nutch|planetwork|postrank|purebot|pycurl|python|seekerspider|siclab|skygrid|sqlmap|sucker|turnit|vikspider|winhttp|xxxyy|youda|zmeu|zune) "block_bad_bot_rule_2";
}

map $http_referer $bad_referer_6g {
    default "allow";
    "~*([a-z0-9]{2000})" "block_bad_referer_rule_1";
    ~*(semalt.com|todaperfeita) "block_bad_referer_rule_2";
}

map $query_string $bad_querystring_6g {
    default "allow";
    ~*(eval\() "block_bad_querystring_rule_1";
    ~*(127\.0\.0\.1) "block_bad_querystring_rule_2";
    "~*([a-z0-9]{2000})" "block_bad_querystring_rule_3";
    "~*(javascript:)(.*)(;)" "block_bad_querystring_rule_4";
    ~*(base64_encode)(.*)(\() "block_bad_querystring_rule_5";
    ~*(GLOBALS|REQUEST)(=|\[) "block_bad_querystring_rule_6";
    ~*(<|%3C)(.*)script(.*)(>|%3) "block_bad_querystring_rule_7";
    ~*(\\|\.\.\.|\.\./|~|`|<|>|\|) "block_bad_querystring_rule_8";
    ~*(boot\.ini|etc/passwd|self/environ) "block_bad_querystring_rule_9";
    ~*(thumbs?(_editor|open)?|tim(thumb)?)\.php "block_bad_querystring_rule_10";
    ~*(\'|\")(.*)(drop|insert|md5|select|union) "block_bad_querystring_rule_11";
}

map $request_uri $bad_request_6g {
    default "allow";
    "~*([a-z0-9]{2000})" "block_bad_request_rule_1";
    ~*(https?|ftp|php):/ "block_bad_request_rule_2";
    ~*(base64_encode)(.*)(\() "block_bad_request_rule_3";
    ~*(=\\\'|=\\%27|/\\\'/?)\. "block_bad_request_rule_4";
    "~*/(\$(\&)?|\*|\"|\.|,|&|&amp;?)/?$" "block_bad_request_rule_5";
    ~*(\{0\}|\(/\(|\.\.\.|\+\+\+|\\\"\\\") "block_bad_request_rule_6";
    "~*(~|`|<|>|:|;|,|\\|\s|\{|\}|\[|\]|\|)" "block_bad_request_rule_7";
    ~*/(=|\$&|_mm|cgi-|muieblack) "block_bad_request_rule_8";
    "~*(&pws=0|_vti_|\(null\)|\{\$itemURL\}|echo(.*)kae|etc/passwd|eval\(|self/environ)" "block_bad_request_rule_9";
    ~*\.(aspx?|bash|bak?|cfg|cgi|dll|exe|git|hg|ini|jsp|log|mdb|out|sql|svn|swp|tar|rar|rdf)$ "block_bad_request_rule_10";
    ~*/(^$|(wp-)?config|mobiquo|phpinfo|shell|sqlpatch|thumb|thumb_editor|thumbopen|timthumb|webshell)\.php "block_bad_request_rule_11";
}

map $request_method $not_allowed_method_6g {
    default "allow";
    ~*^(connect) "block_not_allowed_method_rule_1";
    ~*^(debug) "block_not_allowed_method_rule_2";
    ~*^(move) "block_not_allowed_method_rule_3";
    ~*^(put) "block_not_allowed_method_rule_4";
    ~*^(trace) "block_not_allowed_method_rule_5";
    ~*^(track) "block_not_allowed_method_rule_6";
    ~*^(delete) "block_not_allowed_method_rule_7";
    ~*^(patch) "block_not_allowed_method_rule_8";
    ~*^(purge) "block_not_allowed_method_rule_9";
}
