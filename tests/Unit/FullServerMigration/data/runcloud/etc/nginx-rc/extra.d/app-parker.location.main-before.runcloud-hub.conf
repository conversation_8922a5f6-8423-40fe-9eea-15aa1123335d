# Please edit this file using RunCloud Hub WordPress Plugin.
# Settings will reset when you save your config from the plugin
# or from reinstalling RunCloud Hub inside RunCloud Panel.
# If you think there is a bug, contact <NAME_EMAIL>

set $cache_uri $request_uri;

set $skip_cache 0;
set $bypass_reason "Not bypass";

if ($query_string != "") {
    set $skip_cache 1;
    set $bypass_reason "Query string is not empty";
}
if ($cache_uri ~ (.*)(&|\?)utm_source(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)utm_medium(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)utm_campaign(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)utm_expid(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)fb_action_ids(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)fb_action_types(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)fb_source(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)fbclid(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)gclid(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)age-verified(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)ao_noptimize(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)usqp(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)cn-reloaded(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)(&|\?)_ga(?:=[^&]*&?|&|$)(.*)) {
  set $cache_uri $1$2$3;
}
if ($cache_uri ~ (.*)&$) {
  set $cache_uri $1;
}
if ($cache_uri ~ (.*)\?$) {
  set $cache_uri $1;
}
if ($cache_uri !~ ([^\?]+)(\?.*)) {
  set $skip_cache 0;
  set $bypass_reason "Not bypass";
}
if ($request_uri ~* "/.well-known.*|/wp-admin/|wp-.*.php|index.php|/feed/|sitemap.*.xml|/login.*|/cart.*|/checkout.*|/my-account.*") {
    set $skip_cache 1;
    set $bypass_reason "Match rule URL PATH with value $request_uri";
}
if ($http_cookie ~* "wordpress_sec_[a-f0-9]+|wordpress_logged_in|woocommerce_cart_hash|woocommerce_items_in_cart|wp_woocommerce_session|wordpress_no_cache") {
    set $skip_cache 1;
    set $bypass_reason "Match rule COOKIE with value $http_cookie";
}

include /etc/nginx-rc/extra.d/app-parker.runcloud-hub.*.extend;

if ($request_method = POST) {
    set $skip_cache 1;
    set $bypass_reason "POST method invoked";
}


location ~ /runcache-purge-fastcgi(/.*) {
    auth_basic off;
    allow 127.0.0.1;
    deny all;
    fastcgi_cache_purge app-parker-FASTCGICACHE "rc:$host$1";
}

location ~ /runcache-purgeall-fastcgi {
    auth_basic off;
    allow 127.0.0.1;
    deny all;
    fastcgi_cache_purge app-parker-FASTCGICACHE "rc:*";
}



location ~ /runcache-purge-proxy(/.*) {
    auth_basic off;
    allow 127.0.0.1;
    deny all;
    proxy_cache_purge app-parker-PROXYCACHE "rc:$host$1";
}

location ~ /runcache-purgeall-proxy {
    auth_basic off;
    allow 127.0.0.1;
    deny all;
    proxy_cache_purge app-parker-PROXYCACHE "rc:*";
}



