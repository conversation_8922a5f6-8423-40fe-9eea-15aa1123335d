virtualhost staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app {
    listeners               http, http6, https, https6
    vhRoot                  /var/www/staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

extprocessor lsphp81-u_kf6_263_stagin {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp81-u_kf6_263_stagin.sock
  maxConns                30
  env                     PHP_LSAPI_CHILDREN=30
  env                     PHP_LSAPI_MAX_REQUESTS=5000
  env                     LSAPI_AVOID_FORK=200M
  env                     LSPHP_ENABLE_USER_INI=on
  initTimeout             60
  retryTimeout            0
  respBuffer              0
  autoStart               2
  path                    /usr/local/lsws/lsphp81/bin/lsphp
  env                     PHP_INI_SCAN_DIR=/home/<USER>/lsphp/staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app:/usr/local/lsws/lsphp81/etc/php/8.1/mods-available
  extUser                 u_kf6_263_stagin
  extGroup                u_kf6_263_stagin
}


include /usr/local/lsws/conf/vhosts/staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app/vhconf.after.conf
