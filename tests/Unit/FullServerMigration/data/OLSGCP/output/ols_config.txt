#
# PLAIN TEXT CONFIGURATION FILE
#
# If not set, will use host name as serverName
serverName
user                      xcloud
group                     xcloud
priority                  0
inMemBufSize              60M
swappingDir               /tmp/lshttpd/swap
autoFix503                1
gracefulRestartTimeout    300
mime                      conf/mime.properties
showVersionNumber         0
adminEmails               root@localhost
autoLoadHtaccess          1
indexFiles                index.html, index.php

errorlog logs/error.log {
  logLevel                DEBUG
  debugLevel              0
  rollingSize             10M
  enableStderrLog         1
}

accesslog logs/access.log {
  logReferer              1
  logUserAgent            1
  logFormat               %a %l %u %t "%r" %>s %O "%{Referer}i" "%{User-Agent}i"
  logHeaders              5
  rollingSize             10M
  keepDays                30
  compressArchive         0
}

expires  {
  enableExpires           1
  expiresByType           image/*=A604800,text/css=A604800,application/x-javascript=A604800,application/javascript=A604800,font/*=A604800,application/x-font-ttf=A604800
}

tuning  {
  maxConnections          10000
  maxSSLConnections       10000
  connTimeout             300
  maxKeepAliveReq         10000
  keepAliveTimeout        5
  sndBufSize              0
  rcvBufSize              0
  maxReqURLLen            32768
  maxReqHeaderSize        65536
  maxReqBodySize          2047M
  maxDynRespHeaderSize    32768
  maxDynRespSize          2047M
  maxCachedFileSize       4096
  totalInMemCacheSize     20M
  maxMMapFileSize         256K
  totalMMapCacheSize      40M
  useSendfile             1
  fileETag                28
  enableGzipCompress      1
  compressibleTypes       default
  enableDynGzipCompress   1
  gzipCompressLevel       6
  gzipAutoUpdateStatic    1
  gzipStaticCompressLevel 6
  brStaticCompressLevel   6
  gzipMaxFileSize         10M
  gzipMinFileSize         300

  quicEnable              1
  quicShmDir              /dev/shm
}

fileAccessControl  {
  followSymbolLink        1
  checkSymbolLink         0
  requiredPermissionMask  000
  restrictedPermissionMask 000
}

perClientConnLimit  {
  staticReqPerSec         0
  dynReqPerSec            0
  outBandwidth            0
  inBandwidth             0
  softLimit               10000
  hardLimit               10000
  gracePeriod             15
  banPeriod               300
}

CGIRLimit  {
  maxCGIInstances         20
  minUID                  11
  minGID                  10
  priority                0
  CPUSoftLimit            10
  CPUHardLimit            50
  memSoftLimit            2047M
  memHardLimit            2047M
  procSoftLimit           400
  procHardLimit           450
}

accessDenyDir  {
  dir                     /
  dir                     /etc/*
  dir                     /dev/*
  dir                     conf/*
  dir                     admin/conf/*
}

accessControl  {
  allow                   ALL
}

extprocessor lsphp {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp.sock
  maxConns                10
  env                     PHP_LSAPI_CHILDREN=10
  env                     LSAPI_AVOID_FORK=200M
  initTimeout             60
  retryTimeout            0
  persistConn             1
  respBuffer              0
  autoStart               1
  path                    lsphp74/bin/lsphp
  backlog                 100
  instances               1
  priority                0
  memSoftLimit            0
  memHardLimit            0
  procSoftLimit           1400
  procHardLimit           1500
}

scripthandler  {
  add                     lsapi:lsphp php
}

railsDefaults  {
  maxConns                1
  env                     LSAPI_MAX_IDLE=60
  initTimeout             60
  retryTimeout            0
  pcKeepAliveTimeout      60
  respBuffer              0
  backlog                 50
  runOnStartUp            3
  extMaxIdleTime          300
  priority                3
  memSoftLimit            0
  memHardLimit            0
  procSoftLimit           500
  procHardLimit           600
}

wsgiDefaults  {
  maxConns                5
  env                     LSAPI_MAX_IDLE=60
  initTimeout             60
  retryTimeout            0
  pcKeepAliveTimeout      60
  respBuffer              0
  backlog                 50
  runOnStartUp            3
  extMaxIdleTime          300
  priority                3
  memSoftLimit            0
  memHardLimit            0
  procSoftLimit           500
  procHardLimit           600
}

nodeDefaults  {
  maxConns                5
  env                     LSAPI_MAX_IDLE=60
  initTimeout             60
  retryTimeout            0
  pcKeepAliveTimeout      60
  respBuffer              0
  backlog                 50
  runOnStartUp            3
  extMaxIdleTime          300
  priority                3
  memSoftLimit            0
  memHardLimit            0
  procSoftLimit           500
  procHardLimit           600
}

module cache {
    internal            1

    checkPrivateCache   1
    checkPublicCache    1
    maxCacheObjSize     10000000
    maxStaleAge         200
    qsCache             1
    reqCookieCache      1
    respCookieCache     1
    ignoreReqCacheCtrl  1
    ignoreRespCacheCtrl 0

    enableCache         0
    expireInSeconds     3600
    enablePrivateCache  0
    privateExpireInSeconds 3600
    ls_enabled              1
}

virtualhost default {
  vhRoot                  /var/www/default/
  configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
  allowSymbolLink         1
  enableScript            1
  restrained              1
  setUIDMode              0
}

listener http {
  address                 *:80
  secure                  0
  map                     default *
}

listener http6 {
  address                 [ANY]:80
  secure                  0
  map                     default *
}

listener https {
  address                 *:443
  secure                  1
  keyFile                 /usr/local/lsws/conf/cert/server.key
  certFile                /usr/local/lsws/conf/cert/server.crt
  certChain               1
  sslProtocol             30
  map                     default *
}

listener https6 {
  address                 [ANY]:443
  secure                  1
  keyFile                 /usr/local/lsws/conf/cert/server.key
  certFile                /usr/local/lsws/conf/cert/server.crt
  certChain               1
  sslProtocol             30
  map                     default *
}

include /etc/lsws/conf.d/*.conf
virtualhost site01-h0mu.x-cloud.app {
    listeners               http, http6, https, https6
    vhRoot                  /var/www/site01-h0mu.x-cloud.app
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

extprocessor lsphp81-u1_site01_h0mu {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp81-u1_site01_h0mu.sock
  maxConns                30
  env                     PHP_LSAPI_CHILDREN=30
  env                     PHP_LSAPI_MAX_REQUESTS=5000
  env                     LSAPI_AVOID_FORK=200M
  env                     LSPHP_ENABLE_USER_INI=on
  initTimeout             60
  retryTimeout            0
  respBuffer              0
  autoStart               2
  path                    /usr/local/lsws/lsphp81/bin/lsphp
  env                     PHP_INI_SCAN_DIR=/home/<USER>/lsphp/site01-h0mu.x-cloud.app:/usr/local/lsws/lsphp81/etc/php/8.1/mods-available
  extUser                 u1_site01_h0mu
  extGroup                u1_site01_h0mu
}


include /usr/local/lsws/conf/vhosts/site01-h0mu.x-cloud.app/vhconf.after.conf
virtualhost site02-5skc.x-cloud.app {
    listeners               http, http6, https, https6
    vhRoot                  /var/www/site02-5skc.x-cloud.app
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

extprocessor lsphp81-u2_site02_5skc {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp81-u2_site02_5skc.sock
  maxConns                30
  env                     PHP_LSAPI_CHILDREN=30
  env                     PHP_LSAPI_MAX_REQUESTS=5000
  env                     LSAPI_AVOID_FORK=200M
  env                     LSPHP_ENABLE_USER_INI=on
  initTimeout             60
  retryTimeout            0
  respBuffer              0
  autoStart               2
  path                    /usr/local/lsws/lsphp81/bin/lsphp
  env                     PHP_INI_SCAN_DIR=/home/<USER>/lsphp/site02-5skc.x-cloud.app:/usr/local/lsws/lsphp81/etc/php/8.1/mods-available
  extUser                 u2_site02_5skc
  extGroup                u2_site02_5skc
}


include /usr/local/lsws/conf/vhosts/site02-5skc.x-cloud.app/vhconf.after.conf
virtualhost staging-22-gdr-site01-pwt4-x-cloud-app.x-cloud.app {
    listeners               http, http6, https, https6
    vhRoot                  /var/www/staging-22-gdr-site01-pwt4-x-cloud-app.x-cloud.app
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

extprocessor lsphp81-u_1ml_262_stagin {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp81-u_1ml_262_stagin.sock
  maxConns                30
  env                     PHP_LSAPI_CHILDREN=30
  env                     PHP_LSAPI_MAX_REQUESTS=5000
  env                     LSAPI_AVOID_FORK=200M
  env                     LSPHP_ENABLE_USER_INI=on
  initTimeout             60
  retryTimeout            0
  respBuffer              0
  autoStart               2
  path                    /usr/local/lsws/lsphp81/bin/lsphp
  env                     PHP_INI_SCAN_DIR=/home/<USER>/lsphp/staging-22-gdr-site01-pwt4-x-cloud-app.x-cloud.app:/usr/local/lsws/lsphp81/etc/php/8.1/mods-available
  extUser                 u_1ml_262_stagin
  extGroup                u_1ml_262_stagin
}


include /usr/local/lsws/conf/vhosts/staging-22-gdr-site01-pwt4-x-cloud-app.x-cloud.app/vhconf.after.conf
virtualhost staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app {
    listeners               http, http6, https, https6
    vhRoot                  /var/www/staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

extprocessor lsphp81-u_kf6_263_stagin {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp81-u_kf6_263_stagin.sock
  maxConns                30
  env                     PHP_LSAPI_CHILDREN=30
  env                     PHP_LSAPI_MAX_REQUESTS=5000
  env                     LSAPI_AVOID_FORK=200M
  env                     LSPHP_ENABLE_USER_INI=on
  initTimeout             60
  retryTimeout            0
  respBuffer              0
  autoStart               2
  path                    /usr/local/lsws/lsphp81/bin/lsphp
  env                     PHP_INI_SCAN_DIR=/home/<USER>/lsphp/staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app:/usr/local/lsws/lsphp81/etc/php/8.1/mods-available
  extUser                 u_kf6_263_stagin
  extGroup                u_kf6_263_stagin
}


include /usr/local/lsws/conf/vhosts/staging-22-isk-site02vultr-touj-x-cloud-app.x-cloud.app/vhconf.after.conf
