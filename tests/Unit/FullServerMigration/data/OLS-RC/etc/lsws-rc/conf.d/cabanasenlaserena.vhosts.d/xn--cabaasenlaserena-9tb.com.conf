# Editing this file manually might change litespeed behavior,
# Make sure you know what are you doing
virtualhost xn--cabaasenlaserena-9tb.com {
  listeners http, http6, https, https6

  vhDomain                  xn--cabaasenlaserena-9tb.com

  rewrite  {
    enable                  1
    autoLoadHtaccess        1

    RewriteCond %{HTTP:CF-Visitor} '"scheme":"http"' [OR]
    RewriteCond %{HTTPS} !=on
    RewriteRule ^(.*)$ - [env=proto:http]
    RewriteCond %{HTTP:CF-Visitor} '"scheme":"https"' [OR]
    RewriteCond %{HTTPS} =on
    RewriteRule ^(.*)$ - [env=proto:https]

    # Redirect http -> https
    RewriteCond %{HTTPS} off
    RewriteRule (.*) https://xn--cabaasenlaserena-9tb.com%{REQUEST_URI} [R=301,L]

    include /etc/lsws-rc/extra.d/cabanasenlaserena.rewrite.*.conf
  }

  vhssl {
    keyFile                 /etc/lsws-rc/conf.d/cabanasenlaserena.d/ssl/server.key
    certFile                /etc/lsws-rc/conf.d/cabanasenlaserena.d/ssl/server.crt
    certChain               1
    ciphers                 EECDH+AESGCM:EDH+AESGCM
    sslProtocol             24
    enableECDHE             1
    renegProtection         1
    sslSessionCache         1
    enableSpdy              15
    enableStapling          1
    ocspRespMaxAge          86400
  }

  context / {
    extraHeaders Strict-Transport-Security: max-age=31536000
  }

  include /etc/lsws-rc/conf.d/cabanasenlaserena.d/main.conf
}
