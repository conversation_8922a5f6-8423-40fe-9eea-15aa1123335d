<?php
define( 'WP_CACHE', true );
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the web site, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * Localized language
 * * ABSPATH
 *
 * @link https://wordpress.org/support/article/editing-wp-config-php/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'db3_ddsfsf_hfy6' );

/** Database username */
define( 'DB_USER', 'u3_ddsfsf_hfy6' );

/** Database password */
define( 'DB_PASSWORD', 'HOh7vF2bbgJ1mDW5' );

/** Database hostname */
define( 'DB_HOST', 'localhost:3306' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',          'zyJJx,>z v0Pwm2h=>Dir9~Je-@qdcC$w=R,5c&G8|huh@R2%?c$KqY[b3&/4rYX' );
define( 'SECURE_AUTH_KEY',   'Dmf4Ic{r,oS+v{!lL]`GQ;FoAF6_Hhh}pX9hH(k8+wbsd>>iouSa#:suS04L9mD*' );
define( 'LOGGED_IN_KEY',     'N=!J$y7t[y:(3Y?GlZ8@[/c6k_o+!7QQ>ZWect=5ZnuY)cdh~fi2*(BopSUm;~X}' );
define( 'NONCE_KEY',         'cp=8S/o{68=;pDzGwq{`za=_|(r&;tk=l&e;W&HV?YvH;Egdfei^S-l,2vd=x}Mu' );
define( 'AUTH_SALT',         'f8=8+vTNw6ivjbs[T+*$knAXs,iUe1lw%w~-,EqNXL0c@Mp^gvuC3plC<OIe{-.w' );
define( 'SECURE_AUTH_SALT',  '`johb(%[6HI9^SH)`t[fX7X^r5`dLAhPvsHX1q?:ojBZf)1i:L9blsl`Pc?lgfF[' );
define( 'LOGGED_IN_SALT',    'ETC{CZmrC@.GbjqetDaD84!Z5<gM$fxsihKB V=YeOPJ-7pd=UerZfm8=`I@#oJ!' );
define( 'NONCE_SALT',        '=M.KwSn1Z45_,cj.&6>Ld<Z%sf0<NwD(Z5=9 .`z7W5H2]7TX0c;`34DqNN<0z+6' );
define( 'WP_CACHE_KEY_SALT', 'Ow$k_tngHT)^QyyJ{u;][-aMV$S<HfA(/^_<vdEO<=.2t3zFoi&YCwzvZ3kM8eM2' );


/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'qazn_';


/* Add any custom values between this line and the "stop editing" line. */



/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://wordpress.org/support/article/debugging-in-wordpress/
 */
if ( ! defined( 'WP_DEBUG' ) ) {
	define( 'WP_DEBUG', false );
}

define( 'DISABLE_WP_CRON', true );
define( 'FLUENTMAIL_ELASTICMAIL_API_KEY', '' );
/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
