# Redirect every request to HTTPS...
server {
     listen 80;
     listen [::]:80;
     server_name .vultrploi2.iamshifat.com;
     return 301 https://$host$request_uri;
}

# Redirect SSL to primary domain SSL...
server {
     listen 443 ssl http2;
     listen [::]:443 ssl http2;

     ssl_certificate /etc/letsencrypt/live/vultrploi2.iamshifat.com/fullchain.pem;
     ssl_certificate_key /etc/letsencrypt/live/vultrploi2.iamshifat.com/privkey.pem;

     server_name www.vultrploi2.iamshifat.com;
     return 301 https://vultrploi2.iamshifat.com$request_uri;
}