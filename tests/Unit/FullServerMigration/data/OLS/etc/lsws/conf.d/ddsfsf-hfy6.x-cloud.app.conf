virtualhost ddsfsf-hfy6.x-cloud.app {
    listeners               http, http6, https, https6
    vhRoot                  /var/www/ddsfsf-hfy6.x-cloud.app
    configFile              $SERVER_ROOT/conf/vhosts/$VH_NAME/vhconf.conf
    allowSymbolLink         1
    enableScript            1
    restrained              1
}

extprocessor lsphp81-u3_ddsfsf_hfy6 {
  type                    lsapi
  address                 uds://tmp/lshttpd/lsphp81-u3_ddsfsf_hfy6.sock
  maxConns                30
  env                     PHP_LSAPI_CHILDREN=30
  env                     PHP_LSAPI_MAX_REQUESTS=5000
  env                     LSAPI_AVOID_FORK=200M
  env                     LSPHP_ENABLE_USER_INI=on
  initTimeout             60
  retryTimeout            0
  respBuffer              0
  autoStart               2
  path                    /usr/local/lsws/lsphp81/bin/lsphp
  env                     PHP_INI_SCAN_DIR=/home/<USER>/lsphp/ddsfsf-hfy6.x-cloud.app:/usr/local/lsws/lsphp81/etc/php/8.1/mods-available
  extUser                 u3_ddsfsf_hfy6
  extGroup                u3_ddsfsf_hfy6
}


include /usr/local/lsws/conf/vhosts/ddsfsf-hfy6.x-cloud.app/vhconf.after.conf
