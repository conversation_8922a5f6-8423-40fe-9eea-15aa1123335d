<?php

namespace Tests\Jetstream;

use App\Models\User;
use Database\Seeders\BillingPlanSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UpdateTeamNameTest extends TestCase
{
    use RefreshDatabase;

    function setUp(): void
    {
        parent::setUp();
        $this->seed(BillingPlanSeeder::class);
    }

    public function test_team_names_can_be_updated()
    {
        $this->actingAs($user = User::factory()->withPersonalTeam()->create());

        $this->put('/teams/'.$user->currentTeam->id, [
            'name' => 'Test Team',
        ])->assertNotFound();

        $this->put(route('user.team.update', ['team' => $user->currentTeam]), [
            'name' => 'Test Team',
            'email' => '<EMAIL>'
        ]);

        $this->assertCount(1, $user->fresh()->ownedTeams);
        $this->assertEquals('Test Team', $user->currentTeam->fresh()->name);
    }
}
