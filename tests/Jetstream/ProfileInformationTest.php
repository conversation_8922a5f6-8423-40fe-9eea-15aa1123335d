<?php

namespace Tests\Jetstream;

use App\Models\User;
use Database\Seeders\BillingPlanSeeder;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class ProfileInformationTest extends TestCase
{
    use RefreshDatabase;
    function setUp(): void
    {
        parent::setUp();
        $this->seed(BillingPlanSeeder::class);
    }


    public function test_profile_information_can_be_updated()
    {
        $this->actingAs($user = User::factory()->create());
        Notification::fake();
        $response = $this->put('/user/profile-information', [
            'name' => 'Test Name',
            'email' => '<EMAIL>',
        ]);
        Notification::hasSent($user, Verified::class);
        $this->assertEquals('Test Name', $user->fresh()->name);
        $this->assertEquals('<EMAIL>', $user->fresh()->email);
    }
}
