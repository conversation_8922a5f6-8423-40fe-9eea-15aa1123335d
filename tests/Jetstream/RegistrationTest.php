<?php

namespace Tests\Jetstream;

use Database\Seeders\BillingPlanSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Features;
use Tests\TestCase;

class RegistrationTest extends TestCase
{
    use RefreshDatabase;
    function setUp(): void
    {
        parent::setUp();
        $this->seed(BillingPlanSeeder::class);
    }


    public function test_registration_screen_can_be_rendered()
    {
        if (! Features::enabled(Features::registration())) {
            return $this->markTestSkipped('Registration support is not enabled.');
        }

        $response = $this->get('/register');

        $response->assertStatus(200);
    }

    public function test_registration_screen_cannot_be_rendered_if_support_is_disabled()
    {
        if (Features::enabled(Features::registration())) {
            return $this->markTestSkipped('Registration support is enabled.');
        }

        $response = $this->get('/register');

        $response->assertStatus(404);
    }

    // public function test_new_users_can_register()
    // {
    //     if (! Features::enabled(Features::registration())) {
    //         return $this->markTestSkipped('Registration support is not enabled.');
    //     }
    //
    //     $response = $this->post('/register', [
    //         'name' => 'Test User',
    //         'email' => '<EMAIL>',
    //         'password' => 'password',
    //         'password_confirmation' => 'password',
    //         'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature(),
    //     ]);
    //
    //     $this->assertAuthenticated();
    //     $response->assertRedirect(RouteServiceProvider::HOME);
    // }
}
