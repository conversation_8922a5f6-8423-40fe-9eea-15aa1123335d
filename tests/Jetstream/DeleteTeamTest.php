<?php

namespace Tests\Jetstream;

use App\Models\Team;
use App\Models\User;
use Database\Seeders\BillingPlanSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DeleteTeamTest extends TestCase
{
    use RefreshDatabase;
    function setUp(): void
    {
        parent::setUp();
        $this->seed(BillingPlanSeeder::class);
    }

    public function test_teams_can_be_deleted()
    {
        $this->actingAs($user = User::factory()->withPersonalTeam()->create());

        $user->ownedTeams()->save($team =Team::factory()->make([
            'personal_team' => true,
        ]));

        $user->ownedTeams()->save($team2 = Team::factory()->make([
            'personal_team' => false,
            'name' => 'Test Team 2',
        ]));
        $user->switchTeam($team);

        $team2->users()->attach(
            $otherUser = User::factory()->create(), ['role' => Team::TEAM_ADMIN]
        );
        //jetstream routes are disabled, check JetsreamServiceProvider.php register method for more details
        $this->delete('/teams/'.$team2->id)->assertNotFound();
        $this->delete(route('user.team.delete',['team' => $team2->id,]),[
            'confirmation' => 1,
            'team_name' => $team2->name,
        ]);
        $this->assertCount(0, $otherUser->fresh()->teams);
    }

    public function test_personal_teams_cant_be_deleted()
    {
        $this->actingAs($user = User::factory()->withPersonalTeam()->create());

        $this->delete('/teams/'.$user->currentTeam->id)->assertNotFound();

        $this->delete(route('user.team.delete',['team' => $user->currentTeam->id,]),[
            'confirmation' => true,
            'team_name' => $user->currentTeam->name,
        ]);

        $this->assertNotNull($user->currentTeam->fresh());
    }
}
