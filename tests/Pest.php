<?php

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

use App\Actions\Jetstream\AddTeamMember;

uses(Tests\TestCase::class)->in('Feature');
// uses(Tests\TestCase::class)->in('Unit');
// uses(Tests\TestCase::class)->in('Shell');
// uses(Tests\Unit\TestCase::class)->in('Unit');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-bPestox, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function something()
{
    // ..
}

function createUser($overrides = [])
{
    $user = \App\Models\User::factory()->withPersonalTeam()->create($overrides);

    return tap($user)->update(['current_team_id' => $user->personalTeam()->id]);
}

// function switchTeam($user, $user2, $team, $role = 'admin')
// {
//     app(AddTeamMember::class)->add($user2, $team, $user->email, $role);
//
//     user()->refresh();
//     team()->refresh();
//     auth()->user()->refresh();
//
//     $user->switchTeam($team);
// }

function login($user = null)
{
    return \Pest\Laravel\actingAs($user ?: createUser());
}
