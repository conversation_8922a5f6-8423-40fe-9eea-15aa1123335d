<?php

use App\Enums\SiteStatus;
use App\Models\Redirection;
use App\Models\Server;
use App\Models\Site;
use App\Models\User;
use Database\Seeders\BillingPlanSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\URL;
use Inertia\Testing\Assert;
use Inertia\Testing\AssertableInertia;
use function Pest\Laravel\get;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed(\Database\Seeders\BillingPlanSeeder::class);
    $this->user = createUser();
    $this->server = Server::factory()->for($this->user)->for($this->user->currentTeam)->create();
    $this->site = Site::factory()->for($this->server)->create();

    $this->actingAs($this->user);
});

test('redirects to full url', function () {
    get('/site/'.$this->site->id)->assertRedirect('/server/'.$this->server->id.'/site/'.$this->site->id.'/site-overview');
    get(route('site.show', [
        'server' => $this->server, 'site' => $this->site
    ]))->assertRedirect('/server/'.$this->server->id.'/site/'.$this->site->id.'/site-overview');
});

it('shows the site progress', function () {
    get(route('site.progress', ['server' => $this->server, 'site' => $this->site]))
        ->assertRedirect(route('site.redirect', ['site' => $this->site]));

    $this->site->update(['status' => SiteStatus::PROVISIONING]);

    get(route('site.progress', ['server' => $this->server, 'site' => $this->site]))
        ->assertOk()
        ->assertInertia(fn(AssertableInertia $page) => $page
            ->component('SiteProgress')
            ->has('title')
            ->has('list')
            ->etc()
        );
});

test('it can show site overview', function () {
    $response = get(URL::route('site.overview', [
        'server' => $this->server->id,
        'site' => $this->site->id,
    ]));

    $response->assertStatus(200)
        ->assertInertia(fn(AssertableInertia $page) => $page
            ->component('Site/Overview')
            ->has('server')
            ->has('site')
            ->has('additional_domains')
            ->has('has_page_cache')
            ->has('has_ssl_certificate')
            ->has('has_basic_auth_enabled')
            ->has('taskEvents')
        );
});

test('it can show site updates', function () {
    $response = get(URL::route('site.updates', [
        'server' => $this->server->id,
        'site' => $this->site->id,
    ]));

    $response->assertStatus(200)
        ->assertInertia(fn(AssertableInertia $page) => $page
            ->component('Site/Updates')
            ->has('server')
            ->has('site')
            ->has('additional_domains')
        );
});


test('site domain route displays additional domains', function () {
    $this->get(route('site.domain', [$this->server, $this->site]))
        ->assertOk()
        ->assertInertia(fn(AssertableInertia $page) => $page
            ->component('Site/Domain')
            ->has('server')
            ->has('site')
            ->has('additional_domains')
            ->has('site', fn(AssertableInertia $component) => $component
                ->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
        );
});

it('can view SSL & HTTPS page', function () {
    get(route('site.ssl', [$this->server, $this->site]))
        ->assertInertia(fn($page) => $page
            ->component('Site/SslHttps')
            ->has('server', fn($server) => $server->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn($site) => $site->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('ssl_provider')
            ->has('ssl_certificate')
            ->has('title')
        );
});


test('it shows the site redirection page', function () {
    $redirections = Redirection::factory()->count(3)->create(['site_id' => $this->site->id]);

    get(route('site.redirection', ['server' => $this->server, 'site' => $this->site]))
        ->assertStatus(200)
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Site/Redirection')
            ->has('server', fn(AssertableInertia $subpage) => $subpage->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn(AssertableInertia $subpage) => $subpage->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('redirections.data.0',
                fn(AssertableInertia $subpage) => $subpage->where('id', $redirections->first()->id)
                    ->where('from', $redirections->first()->from)
                    ->etc()
            )
            ->has('redirections.data.1',
                fn(AssertableInertia $subpage) => $subpage->where('id', $redirections->skip(1)->first()->id)
                    ->where('from', $redirections->skip(1)->first()->from)
                    ->etc()
            )
            ->has('title')
            ->has('redirection_types')
        );
});

it('can view wp-config page', function () {
    get(route('site.wp-config', [$this->server, $this->site]))
        ->assertInertia(fn($page) => $page
            ->component('Site/WpConfig')
            ->has('server', fn($page) => $page->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn($site) => $site->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('title')
        );
});

it('can view caching page', function () {
    get(route('site.caching', [$this->server, $this->site]))
        ->assertOk()
        ->assertInertia(fn($page) => $page
            ->component('Site/FullPageCaching')
            ->has('server', fn($page) => $page
                ->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn($page) => $page
                ->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('has_fullpage_caching')
            ->has('cache_duration')
            ->has('cache_duration_unit')
            ->has('cache_exclusion_http_rules')
            ->has('cache_exclusion_cookie_rules')
        );
});

it('can view SSH page', function () {
    get(route('site.ssh', [$this->server, $this->site]))
        ->assertOk()
        ->assertInertia(fn($page) => $page
            ->component('Site/SshSftp')
            ->has('server', fn($page) => $page
                ->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn($page) => $page
                ->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('site_user')
            ->has('site_ssh_authentication_mode')
            ->has('selected_ssh_keypair_ids')
            ->has('keys')
        );
});


it('can view logs page', function () {
    get(route('site.logs', [$this->server, $this->site]))
        ->assertOk()
        ->assertInertia(fn($page) => $page
            ->component('Site/Log')
            ->has('server', fn($page) => $page
                ->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn($page) => $page
                ->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('title')
        );
});

it('can view events page', function () {
    \App\Models\Task::factory()->count(2)->create([
        'server_id' => $this->server->id,
        'site_id' => $this->site->id,
        'name' => 'Site created.',
    ]);
    get(route('site.events', [$this->server, $this->site]))
        ->assertOk()
        ->assertInertia(fn($page) => $page
            ->component('Site/Events')
            ->has('server', fn($page) => $page
                ->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn($page) => $page
                ->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('taskEvents') // todo; not sure why the count is 13 here, it should be 2
            ->has('taskEvents.data.0', fn($page) => $page
//                ->where('id', 1)
//                ->where('name', 'Site created.')
                ->etc()
            )
        );
});

it('can view settings page', function () {
    get(route('site.settings', [$this->server, $this->site]))
        ->assertOk()
        ->assertInertia(fn ($page) => $page
            ->component('Site/Settings')
            ->has('server', fn ($page) => $page
                ->where('id', $this->server->id)
                ->where('name', $this->server->name)
                ->etc()
            )
            ->has('site', fn ($page) => $page
                ->where('id', $this->site->id)
                ->where('name', $this->site->name)
                ->etc()
            )
            ->has('tags')
        );
});
