<?php

use App\Enums\XcloudBilling\PlansEnum;
use App\Models\Server;
use Database\Seeders\BillingPlanSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed(\Database\Seeders\BillingPlanSeeder::class);
    $this->user = createUser();
    $this->user2 = createUser();
    $this->server = Server::factory()->for($this->user)->for($this->user->currentTeam)->create();
    $this->user->currentTeam->setPlan(PlansEnum::Free);
    $this->user->currentTeam->setBillingStatusActive();
    $this->actingAs($this->user);
});

it('can access site index route', function () {
    $response = $this->get(route('site.index'));

    $response->assertOk();
    $response->assertInertia(fn(AssertableInertia $page) => $page
        ->component('Site/Index')
        ->has('sites')
        ->has('tags')
        ->has('filter')
    );
});


it('can access choose server route', function () {
    $response = $this->get(route('site.create.server-choose'));

    $response->assertOk();
    $response->assertInertia(fn(AssertableInertia $page) => $page
        ->component('Site/New/ChooseServer')
        ->has('servers')
    );
});

it('can access create site route', function () {
    $response = $this->get(route('site.create', $this->server->id));

    $response->assertOk();
    $response->assertInertia(fn(AssertableInertia $page) => $page
        ->component('Site/New/Choose')
        ->has('server')
        ->has('monitoring')
    );
});

it('can access install wordpress route', function () {
    $response = $this->get(route('site.create.wordpress', $this->server->id));

    $response->assertOk();
    $response->assertInertia(fn(AssertableInertia $page) => $page
        ->component('Site/New/InstallWordPress')
        ->has('server')
        ->has('availablePhpVersions')
        ->has('availableWordPressVersions')
        ->has('tags')
        ->has('siteDefault')
    );
});

it('can access migrate wordpress route', closure: function () {
    $response = $this->get(route('site.migrate.wordpress', $this->server->id));

    $response->assertOk();
    $response->assertInertia(fn(AssertableInertia $page) => $page
        ->component('Site/New/MigrateWordPress')
        ->has('server')
    );
});

it('can access upload wordpress route', function () {
    $response = $this->get(route('site.upload.wordpress', $this->server->id));

    $response->assertOk();
    $response->assertInertia(fn(AssertableInertia $page) => $page
        ->component('Site/New/UploadWordPress')
        ->has('server')
    );
});

it('checks for authorization', function ($route) {
    $this->actingAs($this->user2);

    $response = $this->get($route);

    $response->assertForbidden();
})->with([
    fn() => route('site.create', $this->server->id),
    fn() => route('site.create.wordpress', $this->server->id),
    fn() => route('site.migrate.wordpress', $this->server->id),
    fn() => route('site.upload.wordpress', $this->server->id),
]);
