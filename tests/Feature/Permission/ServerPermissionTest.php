<?php

use App\Enums\CloudProviderEnums;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Enums\XcloudBilling\PaymentMethodStatus;
use App\Enums\XcloudBilling\PlansEnum;
use App\Jobs\Server\ModifyServer;
use App\Jobs\ServerProvision;
use App\Models\BillingPlan;
use App\Models\CloudProvider;
use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Models\Server;
use App\Models\SshKeyPair;
use App\Models\SudoUser;
use App\Models\Team;
use App\Models\User;
use App\Repository\StripePaymentRepository;
use App\Services\CloudServices\DigitalOcean;
use App\Services\CloudServices\DODatabaseOperations;
use App\Services\CloudServices\Fetchers\Providers\VultrFetcher;
use App\Services\CloudServices\GCPOAuth;
use App\Services\Shell\ShellProcessRunner;
use App\Services\Shell\ShellResponse;
use App\Services\Shell\SshConnector;
use Database\Seeders\CloudProviderSeeder;
use Database\Seeders\ProductSeeder;
use Database\Seeders\ServerSeeder;
use Database\Seeders\SshKeyPairSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Str;
use Inertia\Testing\AssertableInertia;
use Laravel\Jetstream\Jetstream;
use Mockery\MockInterface;
use Stripe\PaymentIntent;
use function Pest\Laravel\actingAs;
use function Pest\Laravel\delete;
use function Pest\Laravel\get;
use function Pest\Laravel\post;
use function Pest\Laravel\put;

uses(RefreshDatabase::class);

beforeEach(function () {
        if (BillingPlan::count() === 0) {
            $this->seed(\Database\Seeders\BillingPlanSeeder::class);
        }
        $this->user = User::factory()->withPersonalTeam()->create(['name' => 'user name']);
        $this->team_admin = User::factory()->withPersonalTeam()->create(['name' => 'team_admin name']);
        $this->server_admin = User::factory()->withPersonalTeam()->create(['name' => 'server_admin name']);
        $this->site_admin = User::factory()->withPersonalTeam()->create(['name' => 'site_admin name']);
        $this->user->currentTeam->setPlan(PlansEnum::Free);
        $this->user->currentTeam->setBillingStatusActive();

        $this->user->currentTeam->users()->attach(
            Jetstream::findUserByEmailOrFail($this->server_admin->email), ['role' => Team::SERVER_ADMIN]
        );

        $this->user->currentTeam->users()->attach(
            Jetstream::findUserByEmailOrFail($this->site_admin->email), ['role' => Team::SITE_ADMIN]
        );

        $this->user->currentTeam->users()->attach(
            Jetstream::findUserByEmailOrFail($this->team_admin->email), ['role' => Team::TEAM_ADMIN]
        );

        $this->server_admin->switchTeam($this->user->currentTeam);
        $this->team_admin->switchTeam($this->user->currentTeam);
        $this->site_admin->switchTeam($this->user->currentTeam);

        $this->seed([SshKeyPairSeeder::class, CloudProviderSeeder::class, ServerSeeder::class, ProductSeeder::class]);
        $product = \App\Models\Product::inRandomOrder()->first();
        $this->postData = [
            'name' => 'Example Server',
            'public_ip' => '127.0.0.1',
            'ssh_port' => 22,
            'ssh_username' => 'root',
            'product_id' => $product->id,
            'size' => $product?->slug,
            'region' => 'New York',
            //zone for gcp
            'zone' => 'us-central1-a',
            'php_version' => '7.4',
            'database_type' => 'mysql8',
            'database_name' => 'example_db',
            'ssh_authentication_mode' => 'public_key',
            'ssh_password' => 'password',
            'ssh_key_pair_id' => 1,
            'tags' => ['tag1', 'tag2'],
            'consent' => true,
            'consent_documentation' => true,
            'stack' => \App\Enums\Stack::Nginx->value,
        ];
        $this->phpData = [
            'php_version' => '8.1',
            'upload_max_filesize' => 128,
            'max_execution_time' => 30,
            'max_input_time' => 60,
            'memory_limit' => 256,
            'max_input_vars' => 1000,
            'post_max_size' => 128,
            'session_gc_maxlifetime' => 1440,
            'opcache_enabled' => true,
        ];
        $this->digital_ocean_provider = CloudProvider::where(['provider' => CloudProviderEnums::DIGITALOCEAN->value])->first();
        $this->vultr_provider = CloudProvider::where(['provider' => CloudProviderEnums::VULTR->value])->first();
        $this->xcloud_provider = CloudProvider::where(['provider' => CloudProviderEnums::XCLOUD->value])->first();
        $this->gcp_provider = CloudProvider::where(['provider' => CloudProviderEnums::GCP->value])->first();
        $this->hetzner = CloudProvider::where(['provider' => CloudProviderEnums::HETZNER->value])->first();

        $this->server = Server::first();
        $this->server->updateServerInfo('php_settings',[
            "8.1" => [
                "memory_limit" => "256M",
                "post_max_size" => "50M",
                "max_input_time" => "60",
                "max_input_vars" => "1000",
                "allow_url_fopen" => "On",
                "max_execution_time" => "60",
                "upload_max_filesize" => "50M",
                "session_gc_maxlifetime" => "1440",
            ]
        ]);
        $this->sudoUser = SudoUser::factory()->create(['server_id' => $this->server->id]);

        #add plan if not exist
        $plan= BillingPlan::updateOrCreate(['name' => PlansEnum::Starter->value]);
        #add payment method
        $paymentMethod= PaymentMethod::create([
            'payment_gateway' => PaymentGateway::Stripe,
            'customer_id' => uniqid(), // use a random id
            'default_card' =>true, // boolean
            'status' => PaymentMethodStatus::ACTIVE->value,
            'user_id' => $this->server_admin->id ?? 1,
            'team_id' => $this->server_admin->current_team_id ?? 1,
            'session_id' => Str::random(), // random string
        ]);
        $paymentMethod->saveMeta('stripe', [
            'payment_method' => 'pm_1J4X2n2eZvKYlo2C2Q2Z2Z2Z',
            'setup_intent' => 'seti_1J4X2n2eZvKYlo2C2Q2Z2Z2Z',
            'payment_method_types' => ['card'],
            'last4digit' => '4242',
            'brand' => 'Visa',
            'expiry_month' => '12',
            'expiry_year' => '2022',
        ]);
        #change team plan to starter
        $this->server_admin->currentTeam->update(['active_plan_id'=>$plan->id]);

        //use fake mailer and queue
        Mail::fake();
        Queue::fake();
        $this->mock(SshConnector::class, function ($mock) {
            $mock->shouldReceive('run')->andReturn(new ShellResponse(0, 'mock_response'));
            $mock->shouldReceive('executeScript')->andReturn(new ShellResponse(0, 'mock_response'));
        });
        $this->mock(DigitalOcean::class, function ($mock) {
            $mock->shouldReceive('getSizes')->andReturn(['1GB', '2GB']);
            $mock->shouldReceive('getRegions')->andReturn(['New York', 'San Francisco']);
            $mock->shouldReceive('getDatabases')->andReturn(['mysql8', 'mysql5']);
            $mock->shouldReceive('getServerDetails')->andReturn(['name' => 'Example Server', 'public_ip' => '']);
            $mock->shouldReceive('prepareCacheData')->andReturn(
                [
                    'droplets' => [
                        'Size1' => [
                            [
                                'region' => ['region1'],
                                'slug' => 'slug1',
                                'memory' => 1024,
                                'disk' => 30,
                                'cpu' => 1,
                                'price' => '$10/month',
                                'backupCost' => 2,
                                'title' => '1GB / 1 CPU / 30 GB SSD - $10/month',
                            ],
                        ],
                    ],
                    'databases' => [
                        'db' => [
                            'group_name' => 'Basic',
                            'db_list' => [
                                [
                                    'num_nodes' => 1,
                                    'size' => '1-4-8',
                                    'title' => '4 / 8 CPUs / 1 Node',
                                ],
                            ],
                        ],
                    ],
                ]
            );
            $mock->shouldReceive('getAllExistingDatabaseClusters')->andReturn([
                [
                    'name' => 'example_db',
                    'engine' => 'mysql',
                    'version' => '8',
                    'size' => 'db-s-1vcpu-1gb',
                    'region' => 'nyc1',
                    'status' => 'available',
                    'private_networking' => false,
                    'tags' => ['tag1', 'tag2'],
                ],
            ]);

        });

        $this->mock(DODatabaseOperations::class, function ($mock) {
            $mock->shouldReceive('getAllExistingDatabaseClusters')->andReturn(\Illuminate\Support\Str::random(10));
            $mock->shouldReceive('getClusterDatabases')->andReturn([
                'name' => 'example_db',
                'engine' => 'mysql',
                'version' => '8',
                'size' => 'db-s-1vcpu-1gb',
                'region' => 'nyc1',
                'status' => 'available',
                'private_networking' => false,
                'tags' => ['tag1', 'tag2'],
            ]);
        });

        //mock for voltr
        $this->mock(VultrFetcher::class, function ($mock) {
            $mock->shouldReceive('getServerTypes')->andReturn([
                [
                    [
                        'slug' => 'slug1',
                        'locations' => ['region1'],
                        'ram' => 1024,
                        'disk' => 30,
                        'bandwidth' => 1000,
                        'price_per_month' => 10,
                        'id' => 1,
                    ],
                    [
                        'slug' => 'slug2',
                        'ram' => 1024,
                        'disk' => 40,
                        'bandwidth' => 10000,
                        'price_per_month' => 12,
                        'locations' => ['region1'],
                        'id' => 2,
                    ],
                ]

            ]);
        });

});


function webServerCreateRoutes($assertFunction): void
{
    // WEB: SERVER CREATE PAGE
    get(route('server.create'))->$assertFunction();
    get(route('server.create.custom'))->$assertFunction();
    // xcloud and xcloud_provider doesn't need any provider that's why we are passing Enum value
    get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD->asSlug()]))->$assertFunction();

    $assertFunctionXCloudProvider = $assertFunction == 'assertOk'? 'assertFound' : 'assertForbidden'; // XCloud provider should have a LTD package, if not it will redirect back (assert found)
    get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD_PROVIDER->asSlug()]))->$assertFunctionXCloudProvider();
    // GCP has the single page to validate credential and create server
    get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::GCP->asSlug()]))->$assertFunction();
}

function apiServerCreateRoutes($object, $assertFunction): void
{
    // API: SERVER CREATE PAGE
    post(route('api.server.store'), $object->postData, ['Accept' => 'application/json'])->$assertFunction();
    post(route('api.server.store.xcloud'), $object->postData, ['Accept' => 'application/json'])->$assertFunction();

    // TODO: Need to mock billing related things (calling stripe api using testing payment method)
//    post(route('api.server.store.xcloud.vultr'), $object->postData, ['Accept' => 'application/json'])->$assertFunction();
//    post(route('api.server.store.xcloud.ltd'), $object->postData, ['Accept' => 'application/json'])->$assertFunction();
}

function webServerCreateProviderRoutes($object, $assertFunction): void
{
    // Other providers
    get(route('server.create.provider', ['cloudProvider' => $object->digital_ocean_provider?->id]))->$assertFunction();
    get(route('server.create.provider', ['cloudProvider' => $object->gcp_provider?->id]))->$assertFunction(); // GCP needs Enum value (did above) but testing with ID
    get(route('server.create.provider', ['cloudProvider' => $object->vultr_provider?->id]))->$assertFunction();
    // TODO: Need to create a mock for hetzner
    //get(route('server.create.provider', ['cloudProvider' => $object->hetzner?->id]))->$assertFunction();
}

function apiServerCreateProviderRoutes($object, $assertFunction): void
{
    // API: SERVER CREATE PAGE
    post(route('api.server.store.provider', ['cloudProvider' => $object->vultr_provider?->id]), $object->postData, ['Accept' => 'application/json'])->$assertFunction(); // it takes any provider, vultr for now
    post(route('api.server.store.do', ['cloudProvider' => $object->digital_ocean_provider?->id]), $object->postData, ['Accept' => 'application/json'])->$assertFunction();
    post(route('api.server.store.gcp', ['cloudProvider' => $object->gcp_provider?->id]), $object->postData, ['Accept' => 'application/json'])->$assertFunction();
}

function webCredentialChooseRoutes($assertFunction): void
{
    // WEB: CREDENTIAL CHOOSE PAGE
    get(route('server.choose.credential', ['cloudProviderSlug' => CloudProviderEnums::DIGITALOCEAN->asSlug()]))->$assertFunction();
    $assertFunctionGCP = $assertFunction === 'assertOk' ? 'assertRedirect' : $assertFunction; // GCP doesn't have the page, it will redirect to create.provider
    get(route('server.choose.credential', ['cloudProviderSlug' => CloudProviderEnums::GCP->asSlug()]))->$assertFunctionGCP();
    get(route('server.choose.credential', ['cloudProviderSlug' => CloudProviderEnums::VULTR->asSlug()]))->$assertFunction();
    get(route('server.choose.credential', ['cloudProviderSlug' => CloudProviderEnums::HETZNER->asSlug()]))->$assertFunction();
}

function apiCredentialChooseRoutes($assertFunction): void
{
    // API: CREDENTIAL CHOOSE PAGE
    $assertFunctionXCloud = $assertFunction === 'assertOk' ? 'assertNotFound' : $assertFunction; // if other providers can be accessible, under xcloud will show not found.
    // TODO: Need a mock for vultr
    //post(route('api.cloudProvider.validate.credential'), ['cloud_provider_name' => CloudProviderEnums::VULTR->value], ['Accept' => 'application/json'])->$assertFunction(); // this represents all
    post(route('api.cloudProvider.validate.credential'), ['cloud_provider_name' => CloudProviderEnums::XCLOUD_VULTR->value], ['Accept' => 'application/json'])->$assertFunctionXCloud(); // this represents under xcloud, there will be no credential page ever for this

}

test('users cannot access server.create routes', function () {
    actingAs($this->site_admin);
    $this->site_admin->switchTeam($this->user->currentTeam);

    //routes for site_admin who have not any permission to create server
    webServerCreateRoutes('assertForbidden');
    apiServerCreateRoutes($this, 'assertForbidden');

    webServerCreateProviderRoutes($this, 'assertForbidden');
    apiServerCreateProviderRoutes($this, 'assertForbidden');

    webCredentialChooseRoutes('assertForbidden');
    apiCredentialChooseRoutes('assertForbidden');

    Queue::assertNothingPushed();

});

test('team admin access server create depends on permissions',function(){
    actingAs($this->team_admin);
    $this->server->update(['team_id' => $this->user->current_team_id]);

    webServerCreateRoutes('assertForbidden');
    apiServerCreateRoutes($this, 'assertForbidden');

    webServerCreateProviderRoutes($this , 'assertForbidden');
    apiServerCreateProviderRoutes($this, 'assertForbidden');

    webCredentialChooseRoutes('assertForbidden');
    apiCredentialChooseRoutes('assertForbidden');

    // Changing Permission: Server Create added - so we can call assertOk()
    user()->updatePermissions(['server:create']);

    webServerCreateRoutes('assertOk');
    apiServerCreateRoutes($this, 'assertCreated');

    // These are still forbidden because user has no provider permission
    webServerCreateProviderRoutes($this , 'assertForbidden');
    apiServerCreateProviderRoutes($this, 'assertForbidden');

    webCredentialChooseRoutes('assertForbidden');
    apiCredentialChooseRoutes('assertForbidden');


    // setting both permission - add cloud provider and create server
    $this->team_admin->updateRole($this->server->team_id,Team::SERVER_ADMIN,['server:add-provider','server:create']);
    user()->refresh();
    team()->refresh();
    CloudProvider::whereNotNull('team_id')->update(['team_id' => team()->id]);

    webServerCreateRoutes('assertOk');
    apiServerCreateRoutes($this, 'assertCreated');

    webServerCreateProviderRoutes($this, 'assertOk');
    apiServerCreateProviderRoutes($this, 'assertCreated');

    webCredentialChooseRoutes('assertOk');
    apiCredentialChooseRoutes('assertOk');

    $this->mock(GCPOAuth::class,fn($mock)=>$mock->shouldReceive('getAllProjects')->andReturn([
        [
            'name' => 'project1',
            'projectId' => 'project1',
            'id' => 1,
        ],
        [
            'name' => 'project2',
            'projectId' => 'project2',
            'id' => 2,
        ],
    ]));

    $this->mock(StripePaymentRepository::class, function (MockInterface $mock) {
        $mock->shouldReceive('createPaymentIntent')
            ->with( \Mockery::type( GeneralInvoice::class ))
            ->andReturn(new PaymentIntent(['id' => Str::random()]));
    });

    $this->mock(SshConnector::class, function ($mock) {
        $mock->shouldReceive('executeScript')->andReturn(new ShellResponse(0, 'mock_response'));
    });

    // NOT SURE WHAT IS THE USE OF THIS PART, NO DATA IS CHANGING HERE I THINK
    apiServerCreateProviderRoutes($this, 'assertCreated');

    $this->user->refresh();
    $this->user->currentTeam->refresh();

    get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD->asSlug()]))->assertOk();
    get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD_PROVIDER->asSlug()]))->assertRedirect();

    apiServerCreateRoutes($this, 'assertCreated');

    Queue::assertPushed(ServerProvision::class);

});

test('team admin can not access server.edit routes without add specific server', function () {
    actingAs($this->team_admin);
    $this->server?->update(['team_id' => $this->user->currentTeam->id]);

    //routes for site_admin who have not any permission to create server
    get(route('server.show', ['server' => $this->server]))->assertForbidden();
    get(route('server.sudo', ['server' => $this->server]))->assertForbidden();

    get(route('server.sudo.create', ['server' => $this->server]))->assertForbidden();

    get(route('server.sudo.edit', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();

    get(route('server.meta', ['server' => $this->server]))->assertForbidden();
    get(route('server.settings', ['server' => $this->server]))->assertForbidden();
    get(route('server.events', ['server' => $this->server]))->assertForbidden();
    get(route('server.monitoring', ['server' => $this->server]))->assertForbidden();
    get(route('server.management', ['server' => $this->server]))->assertForbidden();

    put(route('server.php.settings.update', ['server' => $this->server]))->assertForbidden();



    get(route('server.logs', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.test', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.provision', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.timezone', ['server' => $this->server]),['time_zone'=>Arr::random(timezone_identifiers_list())],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.information', ['server' => $this->server]),['name'=>'test-server'],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.sudoUsers.store', ['server' => $this->server]),[
        'sudo_user'=>'test',
        'sudo_password'=>'secret',
        'ssh_keypair_ids'=> team()->sshKeyParis()->pluck('id')->toArray(),
    ])->assertForbidden();

    put(route('api.server.sudoUsers.update', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();

    delete(route('api.server.sudoUsers.destroy', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.installMonitoring', ['server' => $this->server]))->assertForbidden();

    post(route('api.server.pullMonitoring', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.disableMonitoring', ['server' => $this->server]))->assertForbidden();

    $this->server->refresh();
    post(route('api.server.archive', ['server' => $this->server]),['archive_confirmation'=>$this->server->name],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.restore', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.update.resize', ['server' => $this->server]),[
        'resize_confirmation'=>$this->server->name,
        'size'=> 's-1vcpu-502mb-10gb'
    ])->assertForbidden();

    post(route('api.server.update.backup', ['server' => $this->server]),[
        'backup' => true
    ])->assertForbidden();

    post(route('api.server.delete', ['server' => $this->server]),['delete_confirmation'=>$this->server->name,'delete_from_provider'=>false])->assertForbidden();

    post(route('api.server.reboot', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'supervisor'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'ssh'])->assertForbidden();

    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'redis'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'supervisor'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'ssh'])->assertForbidden();

    get("api/server/{$this->server->id}/databases")->assertForbidden();
    post("api/server/{$this->server->id}/database")->assertForbidden();

    get("api/server/{$this->server->id}/php")->assertForbidden();
    post("api/server/{$this->server->id}/php")->assertForbidden();
    post("api/server/{$this->server->id}/php/default")->assertForbidden();
    post("api/server/{$this->server->id}/php/patch")->assertForbidden();
    delete("api/server/{$this->server->id}/php")->assertForbidden();

    get(route('api.provider.do.fields', ['server' => $this->server]))->assertForbidden();
    get(route('api.provider.do.databases', ['server' => $this->server, 'clusterUUID' => 'clusterUUID']))->assertForbidden();

    get(route('api.server.databases', ['server' => $this->server]))->assertForbidden();
    get(route('api.server.database.users', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.database.add', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.database.user.add', ['server' => $this->server]))->assertForbidden();
    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]))->assertForbidden();
    get(route('api.server.database.user.get', ['server' => $this->server, 'user' => 'user']))->assertForbidden();
    post(route('api.server.database.user.edit', ['server' => $this->server, 'user' => 'user']))->assertForbidden();
    delete(route('api.server.database.user.destroy', ['server' => $this->server, 'user' => 'user']))->assertForbidden();

});

test('team admin can access server.edit routes for add specific server', function () {
    actingAs($this->team_admin);
    ShellProcessRunner::mock([new ShellResponse(0, 'mock_response')], 50);
    $this->server?->update(['team_id' => $this->user->currentTeam->id]);

    user()->saveMeta('all_servers_team',[team()->id]);
    user()->refresh();

    //routes for site_admin who have not any permission to create server
    get(route('server.show', ['server' => $this->server]))->assertRedirectToRoute('server.sites', ['server' => $this->server]);
    get(route('site.create',['server'=>$this->server]))->assertForbidden();
    get(route('server.sudo', ['server' => $this->server]))->assertForbidden();

    user()->addPermission('site:create');
    get(route('site.create',['server'=>$this->server]))->assertOk();

    user()->addPermission('server:manage-access');

    get(route('server.sudo', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/SudoUsers')->has('server')->has('sudo_users'))
        ->assertOk();

    get(route('server.sudo.create', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/CreateSudoUsers')->has('server')->has('keys')->has('sudo_users'))
        ->assertOk();

    get(route('server.sudo.edit', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/EditSudoUsers')->has('server')->has('selected_keys')->has('keys')->has('sudo_user'))
        ->assertOk();

    get(route('server.meta', ['server' => $this->server]))->assertForbidden();
    get(route('server.settings', ['server' => $this->server]))->assertForbidden();
    get(route('server.monitoring', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'ssh'],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'ssh'],['Accept' => 'application/json'])->assertForbidden();
    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]),[],['Accept' => 'application/json'])->assertForbidden();

    user()->addPermission('server:manage-services');

    get(route('server.meta', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/MetaData')->has('server')->has('tags'))
        ->assertOk();



    get(route('server.settings', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Settings')->has('server')->has('timezone_list'))
        ->assertOk();

    get(route('server.events', ['server' => $this->server]))->assertForbidden();
    get(route('server.logs', ['server' => $this->server]))->assertForbidden();

    user()->addPermission('server:manage-logs-and-events');

    get(route('server.events', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Events')->has('server')->has('taskEvents'))
        ->assertOk();

    $this->team_admin->currentTeam->setPlan(PlansEnum::Free);


    get(route('server.monitoring', ['server' => $this->server]))
        ->assertRedirect(route('user.bills-payment'));

    #change team plan to starter
    $this->team_admin->currentTeam->setPlan(PlansEnum::Starter);
    $this->team_admin->currentTeam->refresh();
    $this->server->update(['team_id' => $this->team_admin->current_team_id]);
    get(route('server.monitoring', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Monitoring')->has('server')->has('server.health'))
        ->assertOk();

    get(route('server.management', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Management')
            ->has('server')->has('currentServerTypeInfo')->has('openDeleteModal')->has('openArchiveModal')->has('isResizable')->has('can_delete_server')->has('canUpgradeServer'))
        ->assertOk();



    put(route('server.php.settings.update', ['server' => $this->server]))->assertForbidden();

    user()->addPermission('server:manage-php');

    put(route('server.php.settings.update', ['server' => $this->server]),$this->phpData)->assertRedirect()->assertSessionHas('flash.message','PHP Settings Updated.');




    get(route('server.logs', ['server' => $this->server]))->assertOk();

    post(route('api.server.test', ['server' => $this->server]),[],['Accept' => 'application/json'])
        ->assertOk();

    post(route('api.server.provision', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();
    user()->addPermission('server:delete');

    post(route('api.server.provision', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.timezone', ['server' => $this->server]),['time_zone'=>Arr::random(timezone_identifiers_list())],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.information', ['server' => $this->server]),['name'=>'test-server'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.sudoUsers.store', ['server' => $this->server]),[
        'sudo_user'=>'test',
        'sudo_password'=>'secret',
        'ssh_keypair_ids'=> SshKeyPair::whereNotNull('id')->pluck('id')->toArray(),
    ])->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    put(route('api.server.sudoUsers.update', [
        'server' => $this->server,
        'sudoUser' => $this->sudoUser,
        'ssh_keypair_ids'=> SshKeyPair::whereNotNull('id')->pluck('id')->toArray()
    ]))
        ->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    delete(route('api.server.sudoUsers.destroy', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))
        ->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.installMonitoring', ['server' => $this->server]))->assertOk();

    post(route('api.server.pullMonitoring', ['server' => $this->server]))->assertOk();
    post(route('api.server.disableMonitoring', ['server' => $this->server]))->assertOk();

    post(route('api.server.archive', ['server' => $this->server]),['archive_confirmation'=>$this->server->name],['Accept' => 'application/json'])->assertForbidden();

    user()->addPermission('server:archive');
    $this->server->refresh();
    post(route('api.server.archive', ['server' => $this->server]),['archive_confirmation'=>$this->server->name],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restore', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.update.resize', ['server' => $this->server]),[
        'resize_confirmation'=>$this->server->name,
        'size'=> 's-1vcpu-502mb-10gb'
    ])->assertForbidden();

    post(route('api.server.update.backup', ['server' => $this->server]),[
        'backup' => true,
    ])->assertForbidden();

    user()->addPermission('server:resize-server');

    post(route('api.server.update.resize', ['server' => $this->server]),[
        'resize_confirmation'=>$this->server->name,
        'size'=> 's-1vcpu-502mb-10gb'
    ])->assertRedirect()->assertSessionHas('flash.message');

    user()->addPermission('server:manage-provider-backup');
    /*post(route('api.server.update.backup', ['server' => $this->server]),[
        'backup' => true,
    ])->assertRedirectToRoute('server.index')->assertSessionHas('flash.message');*/

    Queue::assertPushed(ModifyServer::class);


    post(route('api.server.reboot', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'supervisor'],['Accept' => 'application/json'])->assertStatus(302);


    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'supervisor'],['Accept' => 'application/json'])->assertStatus(302);

    get("api/server/{$this->server->id}/databases")->assertForbidden();
    post("api/server/{$this->server->id}/database")->assertForbidden();

    user()->addPermission('server:manage-databases');
    get("api/server/{$this->server->id}/databases")->assertOk();
    post("api/server/{$this->server->id}/database",['database_name'=>'test_db'],['Accept' => 'application/json'])->assertOk();

    get("api/server/{$this->server->id}/php")->assertOk();
    post("api/server/{$this->server->id}/php",['php_version'=>'7.4'])->assertOk();
    post("api/server/{$this->server->id}/php/default",['php_version'=>'7.4'])->assertRedirect();
    post("api/server/{$this->server->id}/php/patch",['php_version'=>'7.4'])->assertOk();
    delete("api/server/{$this->server->id}/php",['php_version'=>'7.4'])->assertOk();

    get(route('api.provider.do.fields', ['server' => $this->server]))->assertOk();

    get(route('api.provider.do.databases', ['server' => $this->server, 'clusterUUID' => 'clusterUUID']))->assertOk();

    get(route('api.server.databases', ['server' => $this->server]))->assertOk();
    get(route('api.server.database.users', ['server' => $this->server]))->assertOk();
    post(route('api.server.database.add', ['server' => $this->server]),['database_name'=>'example-db'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.database.user.add', ['server' => $this->server]),[
        'selected_databases'=>[
            'example-db'
        ],
        'database_username'=>'example-user',
        'database_user_password'=>'example-password'
    ],['Accept' => 'application/json'])->assertOk();

    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]),[],['Accept' => 'application/json'])->assertOk();
    get(route('api.server.database.user.get', ['server' => $this->server, 'user' => 'user']))->assertOk();

    post(route('api.server.database.user.edit', ['server' => $this->server, 'user' => 'user']),['selected_databases'=>['example-db']])->assertRedirect();
    delete(route('api.server.database.user.destroy', ['server' => $this->server, 'user' => 'user']))->assertRedirect();

    post(route('api.server.delete', ['server' => $this->server]),['delete_confirmation'=>$this->server->name,'delete_from_provider'=>false])->assertRedirectToRoute('server.index')->assertSessionHas('flash.message');

});

test('server admin access server create depends on permissions',function(){
    actingAs($this->server_admin);

    webServerCreateRoutes('assertForbidden');
    webServerCreateProviderRoutes($this , 'assertForbidden');
    apiServerCreateRoutes($this, 'assertForbidden');
    apiServerCreateProviderRoutes($this, 'assertForbidden');

    Queue::assertNothingPushed();

    $this->server_admin->updatePermissions(['server:create']);
    team()->refresh();

    webServerCreateRoutes('assertOk');
    apiServerCreateRoutes($this, 'assertCreated');

    // These are still forbidden because user has no provider permission
    webServerCreateProviderRoutes($this , 'assertForbidden');
    apiServerCreateProviderRoutes($this, 'assertForbidden');


    $this->server_admin->updateRole($this->server->team_id,Team::SERVER_ADMIN,['server:add-provider','server:create']);
    user()->refresh();
    team()->refresh();
    CloudProvider::whereNotNull('team_id')->update(['team_id' => team()->id]);

    webServerCreateRoutes('assertOk');
    apiServerCreateRoutes($this, 'assertCreated');

    webServerCreateProviderRoutes($this, 'assertOk');
    apiServerCreateProviderRoutes($this, 'assertCreated');

    webCredentialChooseRoutes('assertOk');
    apiCredentialChooseRoutes('assertOk');

    $this->mock(StripePaymentRepository::class, function (MockInterface $mock) {
        $mock->shouldReceive('createPaymentIntent')
            ->with( \Mockery::type( GeneralInvoice::class ))
            ->andReturn(new PaymentIntent([
                'id' =>1
            ]));
    });

    $this->mock(SshConnector::class, function ($mock) {
        $mock->shouldReceive('executeScript')->andReturn(new ShellResponse(0, 'mock_response'));
    });

    apiServerCreateProviderRoutes($this, 'assertCreated');

    $this->user->refresh();
    $this->user->currentTeam->refresh();

    get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD->asSlug()]))->assertOk();

    //  TODO: To fix this, need to create an LTD user
    // get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD_PROVIDER->value]))->assertOk();

    apiServerCreateRoutes($this, 'assertCreated');

    Queue::assertPushed(ServerProvision::class);

});

test('server admin can not access server.edit routes without add specific server', function () {
    actingAs($this->server_admin);
    $this->server?->update(['team_id' => $this->user->currentTeam->id]);

    //routes for site_admin who have not any permission to create server
    get(route('server.show', ['server' => $this->server]))->assertForbidden();
    get(route('server.sudo', ['server' => $this->server]))->assertForbidden();

    get(route('server.sudo.create', ['server' => $this->server]))->assertForbidden();

    get(route('server.sudo.edit', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();

    get(route('server.meta', ['server' => $this->server]))->assertForbidden();
    get(route('server.settings', ['server' => $this->server]))->assertForbidden();
    get(route('server.events', ['server' => $this->server]))->assertForbidden();
    get(route('server.monitoring', ['server' => $this->server]))->assertForbidden();
    get(route('server.management', ['server' => $this->server]))->assertForbidden();

    put(route('server.php.settings.update', ['server' => $this->server]))->assertForbidden();



    get(route('server.logs', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.test', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.provision', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.timezone', ['server' => $this->server]),['time_zone'=>Arr::random(timezone_identifiers_list())],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.information', ['server' => $this->server]),['name'=>'test-server'],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.sudoUsers.store', ['server' => $this->server]),[
        'sudo_user'=>'test',
        'sudo_password'=>'secret',
        'ssh_keypair_ids'=> team()->sshKeyParis()->pluck('id')->toArray(),
    ])->assertForbidden();

    put(route('api.server.sudoUsers.update', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();

    delete(route('api.server.sudoUsers.destroy', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.installMonitoring', ['server' => $this->server]))->assertForbidden();

    post(route('api.server.pullMonitoring', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.disableMonitoring', ['server' => $this->server]))->assertForbidden();

    $this->server->refresh();
    post(route('api.server.archive', ['server' => $this->server]),['archive_confirmation'=>$this->server->name],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.restore', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.update.resize', ['server' => $this->server]),[
        'resize_confirmation'=>$this->server->name,
        'size'=> 's-1vcpu-502mb-10gb'
    ])->assertForbidden();

    post(route('api.server.update.backup', ['server' => $this->server]),[
        'backup' => true,
    ])->assertForbidden();

    post(route('api.server.delete', ['server' => $this->server]),['delete_confirmation'=>$this->server->name,'delete_from_provider'=>false])->assertForbidden();

    post(route('api.server.reboot', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'supervisor'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'ssh'])->assertForbidden();

    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'redis'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'supervisor'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'ssh'])->assertForbidden();

    get("api/server/{$this->server->id}/databases")->assertForbidden();
    post("api/server/{$this->server->id}/database")->assertForbidden();

    get("api/server/{$this->server->id}/php")->assertForbidden();
    post("api/server/{$this->server->id}/php")->assertForbidden();
    post("api/server/{$this->server->id}/php/default")->assertForbidden();
    post("api/server/{$this->server->id}/php/patch")->assertForbidden();
    delete("api/server/{$this->server->id}/php")->assertForbidden();

    get(route('api.provider.do.fields', ['server' => $this->server]))->assertForbidden();
    get(route('api.provider.do.databases', ['server' => $this->server, 'clusterUUID' => 'clusterUUID']))->assertForbidden();

    get(route('api.server.databases', ['server' => $this->server]))->assertForbidden();
    get(route('api.server.database.users', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.database.add', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.database.user.add', ['server' => $this->server]))->assertForbidden();
    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]))->assertForbidden();
    get(route('api.server.database.user.get', ['server' => $this->server, 'user' => 'user']))->assertForbidden();
    post(route('api.server.database.user.edit', ['server' => $this->server, 'user' => 'user']))->assertForbidden();
    delete(route('api.server.database.user.destroy', ['server' => $this->server, 'user' => 'user']))->assertForbidden();

});

test('server admin can access server.edit routes for add specific server', function () {
    actingAs($this->server_admin);
    ShellProcessRunner::mock([new ShellResponse(0, 'mock_response')],50);
    $this->server?->update(['team_id' => $this->user->currentTeam->id]);

    user()->saveMeta('all_servers_team',[team()->id]);
    user()->refresh();

    //routes for site_admin who have not any permission to create server
    get(route('server.show', ['server' => $this->server]))->assertRedirectToRoute('server.sites', ['server' => $this->server]);

    get(route('server.sudo', ['server' => $this->server]))->assertForbidden();

    user()->addPermission('server:manage-access');

    get(route('server.sudo', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/SudoUsers')->has('server')->has('sudo_users'))
        ->assertOk();

    get(route('server.sudo.create', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/CreateSudoUsers')->has('server')->has('keys')->has('sudo_users'))
        ->assertOk();

    get(route('server.sudo.edit', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/EditSudoUsers')->has('server')->has('selected_keys')->has('keys')->has('sudo_user'))
        ->assertOk();

    get(route('server.meta', ['server' => $this->server]))->assertForbidden();
    get(route('server.settings', ['server' => $this->server]))->assertForbidden();
    get(route('server.monitoring', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'ssh'],['Accept' => 'application/json'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'ssh'],['Accept' => 'application/json'])->assertForbidden();

    user()->addPermission('server:manage-services');

    get(route('server.meta', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/MetaData')->has('server')->has('tags'))
        ->assertOk();


    get(route('server.settings', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Settings')->has('server')->has('timezone_list'))
        ->assertOk();

    get(route('server.events', ['server' => $this->server]))->assertForbidden();
    get(route('server.logs', ['server' => $this->server]))->assertForbidden();

    user()->addPermission('server:manage-logs-and-events');

    get(route('server.events', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Events')->has('server')->has('taskEvents'))
        ->assertOk();

    $this->server->team->setPlan(PlansEnum::Free);

    get(route('server.monitoring', ['server' => $this->server]))
        ->assertRedirect(route('user.bills-payment'));

    #change team plan to starter
    $this->server->team->setPlan(PlansEnum::Starter);
    $this->server->team->refresh();

    get(route('server.monitoring', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Monitoring')->has('server')->has('server.health'))
        ->assertOk();

    get(route('server.management', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Management')
            ->has('server')->has('currentServerTypeInfo')->has('openDeleteModal')->has('openArchiveModal')->has('isResizable')->has('can_delete_server')->has('canUpgradeServer'))
        ->assertOk();



    put(route('server.php.settings.update', ['server' => $this->server]))->assertForbidden();

    user()->addPermission('server:manage-php');

    put(route('server.php.settings.update', ['server' => $this->server]),$this->phpData)->assertRedirect()->assertSessionHas('flash.message','PHP Settings Updated.');





    get(route('server.logs', ['server' => $this->server]))->assertOk();

    post(route('api.server.test', ['server' => $this->server]),[],['Accept' => 'application/json'])
        ->assertOk();

    post(route('api.server.provision', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertForbidden();
    user()->addPermission('server:delete');

    post(route('api.server.provision', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.timezone', ['server' => $this->server]),['time_zone'=>Arr::random(timezone_identifiers_list())],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.information', ['server' => $this->server]),['name'=>'test-server'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.sudoUsers.store', ['server' => $this->server]),[
        'sudo_user'=>'test',
        'sudo_password'=>'secret',
        'ssh_keypair_ids'=> SshKeyPair::whereNotNull('id')->pluck('id')->toArray(),
    ])->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    put(route('api.server.sudoUsers.update', [
        'server' => $this->server,
        'sudoUser' => $this->sudoUser,
        'ssh_keypair_ids'=> SshKeyPair::whereNotNull('id')->pluck('id')->toArray()
    ]))
        ->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    delete(route('api.server.sudoUsers.destroy', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))
        ->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.installMonitoring', ['server' => $this->server]))->assertOk();

    post(route('api.server.pullMonitoring', ['server' => $this->server]))->assertOk();
    post(route('api.server.disableMonitoring', ['server' => $this->server]))->assertOk();

    post(route('api.server.archive', ['server' => $this->server]),['archive_confirmation'=>$this->server->name],['Accept' => 'application/json'])->assertForbidden();

    user()->addPermission('server:archive');
    $this->server->refresh();
    post(route('api.server.archive', ['server' => $this->server]),['archive_confirmation'=>$this->server->name],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restore', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.update.resize', ['server' => $this->server]),[
        'resize_confirmation'=>$this->server->name,
        'size'=> 's-1vcpu-502mb-10gb'
    ])->assertForbidden();

    post(route('api.server.update.backup', ['server' => $this->server]),[
        'backup' => true,
    ])->assertForbidden();

    user()->addPermission('server:resize-server');

    post(route('api.server.update.resize', ['server' => $this->server]),[
        'resize_confirmation'=>$this->server->name,
        'size'=> 's-1vcpu-502mb-10gb'
    ])->assertRedirect()->assertSessionHas('flash.message');

    user()->addPermission('server:manage-provider-backup');
    /*post(route('api.server.update.backup', ['server' => $this->server]),[
        'backup' => true,
    ])->assertRedirectToRoute('server.index')->assertSessionHas('flash.message');*/

    Queue::assertPushed(ModifyServer::class);

    post(route('api.server.reboot', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'supervisor'],['Accept' => 'application/json'])->assertStatus(302);

    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'supervisor'],['Accept' => 'application/json'])->assertStatus(302);

    get("api/server/{$this->server->id}/databases")->assertForbidden();
    post("api/server/{$this->server->id}/database")->assertForbidden();
    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]),[],['Accept' => 'application/json'])->assertForbidden();

    user()->addPermission('server:manage-databases');
    get("api/server/{$this->server->id}/databases")->assertOk();
    post("api/server/{$this->server->id}/database",['database_name'=>'test_db'],['Accept' => 'application/json'])->assertOk();

    get("api/server/{$this->server->id}/php")->assertOk();
    post("api/server/{$this->server->id}/php",['php_version'=>'7.4'])->assertOk();
    post("api/server/{$this->server->id}/php/default",['php_version'=>'7.4'])->assertRedirect();
    post("api/server/{$this->server->id}/php/patch",['php_version'=>'7.4'])->assertOk();
    delete("api/server/{$this->server->id}/php",['php_version'=>'7.4'])->assertOk();

    get(route('api.provider.do.fields', ['server' => $this->server]))->assertOk();

    get(route('api.provider.do.databases', ['server' => $this->server, 'clusterUUID' => 'clusterUUID']))->assertOk();

    get(route('api.server.databases', ['server' => $this->server]))->assertOk();
    get(route('api.server.database.users', ['server' => $this->server]))->assertOk();
    post(route('api.server.database.add', ['server' => $this->server]),['database_name'=>'example-db'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.database.user.add', ['server' => $this->server]),[
        'selected_databases'=>[
            'example-db'
        ],
        'database_username'=>'example-user',
        'database_user_password'=>'example-password'
    ],['Accept' => 'application/json'])->assertOk();

    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]),[],['Accept' => 'application/json'])->assertOk();
    get(route('api.server.database.user.get', ['server' => $this->server, 'user' => 'user']))->assertOk();

    post(route('api.server.database.user.edit', ['server' => $this->server, 'user' => 'user']),['selected_databases'=>['example-db']])->assertRedirect();
    delete(route('api.server.database.user.destroy', ['server' => $this->server, 'user' => 'user']))->assertRedirect();

    post(route('api.server.delete', ['server' => $this->server]),['delete_confirmation'=>$this->server->name,'delete_from_provider'=>false])->assertRedirectToRoute('server.index')->assertSessionHas('flash.message');

});

test('users cannot access server.edit routes', function () {
    actingAs($this->site_admin);
    $this->site_admin->switchTeam($this->user->currentTeam);
    $this->server?->update(['team_id' => $this->user->currentTeam->id]);

    //routes for site_admin who has not any permission to create server
    get(route('server.show', ['server' => $this->server]))->assertForbidden();
    get(route('server.sudo', ['server' => $this->server]))->assertForbidden();
    get(route('server.sudo.create', ['server' => $this->server]))->assertForbidden();
    get(route('server.sudo.edit', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();
    get(route('server.meta', ['server' => $this->server]))->assertForbidden();
    get(route('server.settings', ['server' => $this->server]))->assertForbidden();
    get(route('server.events', ['server' => $this->server]))->assertForbidden();
    get(route('server.monitoring', ['server' => $this->server]))->assertForbidden();
    get(route('server.management', ['server' => $this->server]))->assertForbidden();
    put(route('server.php.settings.update', ['server' => $this->server]))->assertForbidden();

    get(route('server.logs', ['server' => $this->server]))->assertForbidden();

    post(route('api.server.test', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.provision', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.timezone', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.information', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.connection', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.sudoUsers.store', ['server' => $this->server]))->assertForbidden();

    put(route('api.server.sudoUsers.update', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();

    delete(route('api.server.sudoUsers.destroy', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))->assertForbidden();
    post(route('api.server.connection', ['server' => $this->server]))->assertForbidden();

    post(route('api.server.installMonitoring', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.pullMonitoring', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.disableMonitoring', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.archive', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.restore', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.update.resize', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.update.backup', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.delete', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.reboot', ['server' => $this->server]))->assertForbidden();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'supervisor'])->assertForbidden();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'ssh'])->assertForbidden();

    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'mysql'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'php'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'nginx'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'redis'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'supervisor'])->assertForbidden();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'ssh'])->assertForbidden();

    get("api/server/{$this->server->id}/databases")->assertForbidden();
    post("api/server/{$this->server->id}/database")->assertForbidden();

    get("api/server/{$this->server->id}/php")->assertForbidden();
    post("api/server/{$this->server->id}/php")->assertForbidden();
    post("api/server/{$this->server->id}/php/default")->assertForbidden();
    post("api/server/{$this->server->id}/php/patch")->assertForbidden();
    delete("api/server/{$this->server->id}/php")->assertForbidden();

    get(route('api.provider.do.fields', ['server' => $this->server]))->assertForbidden();
    get(route('api.provider.do.databases', ['server' => $this->server, 'clusterUUID' => 'clusterUUID']))->assertForbidden();

    get(route('api.server.databases', ['server' => $this->server]))->assertForbidden();
    get(route('api.server.database.users', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.database.add', ['server' => $this->server]))->assertForbidden();
    post(route('api.server.database.user.add', ['server' => $this->server]))->assertForbidden();
    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]))->assertForbidden();
    get(route('api.server.database.user.get', ['server' => $this->server, 'user' => 'user']))->assertForbidden();
    post(route('api.server.database.user.edit', ['server' => $this->server, 'user' => 'user']))->assertForbidden();
    delete(route('api.server.database.user.destroy', ['server' => $this->server, 'user' => 'user']))->assertForbidden();

});

test('owner can access server.edit routes', function () {
    ShellProcessRunner::mock([new ShellResponse(0, 'mock_response')],52);
    $this->user->refresh();
    actingAs($this->user);
    $this->server?->update(['team_id' => $this->user->current_team_id]);

    //routes for site_admin who has not any permission to create server
    get(route('server.show', ['server' => $this->server]))->assertRedirectToRoute('server.sites', ['server' => $this->server]);
    get(route('server.sudo', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/SudoUsers')->has('server')->has('sudo_users'))
        ->assertOk();

    get(route('server.sudo.create', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/CreateSudoUsers')->has('server')->has('keys')->has('sudo_users'))
        ->assertOk();

    get(route('server.sudo.edit', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/EditSudoUsers')->has('server')->has('selected_keys')->has('keys')->has('sudo_user'))
        ->assertOk();

    get(route('server.meta', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/MetaData')->has('server')->has('tags'))
        ->assertOk();
    get(route('server.settings', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Settings')->has('server')->has('timezone_list'))
        ->assertOk();
    get(route('server.events', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Events')->has('server')->has('taskEvents'))
        ->assertOk();

    $this->server->team->setPlan(PlansEnum::Free);

    get(route('server.monitoring', ['server' => $this->server]))
        ->assertRedirect(route('user.bills-payment'));

    #change team plan to starter
    $this->server->team->setPlan(PlansEnum::Starter);
    $this->server->team->refresh();

    get(route('server.monitoring', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Monitoring')->has('server')->has('server.health'))
        ->assertOk();
    get(route('server.management', ['server' => $this->server]))
        ->assertInertia(fn(AssertableInertia $page) => $page->component('Server/Management')
            ->has('server')->has('currentServerTypeInfo')->has('openDeleteModal')->has('openArchiveModal')->has('isResizable')->has('can_delete_server')->has('canUpgradeServer'))
        ->assertOk();


    put(route('server.php.settings.update', ['server' => $this->server]),$this->phpData)->assertRedirect()->assertSessionHas('flash.message','PHP Settings Updated.');



    get(route('server.logs', ['server' => $this->server]))->assertOk();
    post(route('api.server.test', ['server' => $this->server]),[],['Accept' => 'application/json'])
        ->assertOk();

    post(route('api.server.provision', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.timezone', ['server' => $this->server]),['time_zone'=>Arr::random(timezone_identifiers_list())],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.information', ['server' => $this->server]),['name'=>'test-server'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.sudoUsers.store', ['server' => $this->server]),[
        'sudo_user'=>'test',
        'sudo_password'=>'secret',
        'ssh_keypair_ids'=> SshKeyPair::whereNotNull('id')->pluck('id')->toArray(),
    ])->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    put(route('api.server.sudoUsers.update', [
        'server' => $this->server,
        'sudoUser' => $this->sudoUser,
        'ssh_keypair_ids'=> SshKeyPair::whereNotNull('id')->pluck('id')->toArray()
    ]))
        ->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    delete(route('api.server.sudoUsers.destroy', ['server' => $this->server, 'sudoUser' => $this->sudoUser]))
        ->assertRedirectToRoute('server.sudo', ['server' => $this->server])->assertSessionHas('flash.message');

    post(route('api.server.connection', ['server' => $this->server]),['public_ip'=>'*********','ssh_port'=>22],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.installMonitoring', ['server' => $this->server]))->assertOk();


    post(route('api.server.pullMonitoring', ['server' => $this->server]))->assertOk();
    post(route('api.server.disableMonitoring', ['server' => $this->server]))->assertOk();

    $this->server->refresh();
    post(route('api.server.archive', ['server' => $this->server]),['archive_confirmation'=>$this->server->name],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restore', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.update.resize', ['server' => $this->server]),[
        'resize_confirmation'=>$this->server->name,
        'size'=> 's-1vcpu-502mb-10gb'
    ])->assertRedirect()->assertSessionHas('flash.message');

    /*post(route('api.server.update.backup', ['server' => $this->server]),[
        'backup' => true,
    ])->assertRedirectToRoute('server.index')->assertSessionHas('flash.message');*/

    Queue::assertPushed(ModifyServer::class);
    $this->server->refresh();


    post(route('api.server.reboot', ['server' => $this->server]),[],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.restart', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'supervisor'],['Accept' => 'application/json'])->assertStatus(302);
    post(route('api.server.enable', ['server' => $this->server]), ['service' => 'ssh'],['Accept' => 'application/json'])->assertOk();

    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'mysql'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'php'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'nginx'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'redis'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'supervisor'],['Accept' => 'application/json'])->assertStatus(302);
    post(route('api.server.disable', ['server' => $this->server]), ['service' => 'ssh'],['Accept' => 'application/json'])->assertOk();

    get("api/server/{$this->server->id}/databases")->assertOk();
    post("api/server/{$this->server->id}/database",['database_name'=>'test-db'],['Accept' => 'application/json'])->assertOk();

    get("api/server/{$this->server->id}/php")->assertOk();
    post("api/server/{$this->server->id}/php",['php_version'=>'7.4'])->assertOk();
    post("api/server/{$this->server->id}/php/default",['php_version'=>'7.4'])->assertRedirect();
    post("api/server/{$this->server->id}/php/patch",['php_version'=>'7.4'])->assertOk();
    delete("api/server/{$this->server->id}/php",['php_version'=>'7.4'])->assertOk();

    get(route('api.provider.do.fields', ['server' => $this->server]))->assertOk();
    get(route('api.provider.do.databases', ['server' => $this->server, 'clusterUUID' => 'clusterUUID']))->assertOk();

    get(route('api.server.databases', ['server' => $this->server]))->assertOk();
    get(route('api.server.database.users', ['server' => $this->server]))->assertOk();
    post(route('api.server.database.add', ['server' => $this->server]),['database_name'=>'example-db'],['Accept' => 'application/json'])->assertOk();
    post(route('api.server.database.user.add', ['server' => $this->server]),[
        'selected_databases'=>[
            'example-db'
        ],
        'database_username'=>'example-user',
        'database_user_password'=>'example-password'
    ],['Accept' => 'application/json'])->assertOk();
    delete(route('api.server.database.destroy', ['server' => $this->server, 'database' => $this->server?->database_name]),[],['Accept' => 'application/json'])->assertOk();

    get(route('api.server.database.user.get', ['server' => $this->server, 'user' => 'user']))->assertOk();

    post(route('api.server.database.user.edit', ['server' => $this->server, 'user' => 'user']),['selected_databases'=>['example-db']])->assertRedirect();
    delete(route('api.server.database.user.destroy', ['server' => $this->server, 'user' => 'user']))->assertRedirect();

    post(route('api.server.delete', ['server' => $this->server]),['delete_confirmation'=>$this->server->name,'delete_from_provider'=>false])->assertRedirectToRoute('server.index')->assertSessionHas('flash.message');

});

test('owner can access server.create routes', function () {

    actingAs($this->user);
    CloudProvider::whereNotNull('provider')->update(['team_id' => $this->user->currentTeam->id]);

    webServerCreateRoutes('assertOk');
    apiServerCreateRoutes($this, 'assertCreated');

    webServerCreateProviderRoutes($this , 'assertOk');
    apiServerCreateProviderRoutes($this, 'assertCreated');

    $this->mock(StripePaymentRepository::class, function (MockInterface $mock) {
        $mock->shouldReceive('createPaymentIntent')
            ->with( \Mockery::type( GeneralInvoice::class ))
            ->andReturn(new PaymentIntent(['id'=>1]));
    });

    $this->mock(SshConnector::class, function ($mock) {
        $mock->shouldReceive('executeScript')->andReturn(new ShellResponse(0, 'mock_response'));
    });

    apiServerCreateProviderRoutes($this, 'assertCreated');

    $this->user->refresh();
    $this->user->currentTeam->refresh();

    get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD->asSlug()]))->assertOk();

    //  TODO: To fix this, need to create an LTD user
    //  get(route('server.create.provider', ['cloudProvider' => CloudProviderEnums::XCLOUD_PROVIDER->value]))->assertOk();

    apiServerCreateRoutes($this, 'assertCreated');

    //    as validation of cloud provider credential is under same controller so putting that here
    // TODO: Need to create a mock for vultr
    // post(route('api.cloudProvider.validate.credential'), ['cloud_provider_name' => CloudProviderEnums::VULTR->value], ['Accept' => 'application/json'])->assertStatus(302); // this represents all
    post(route('api.cloudProvider.validate.credential'), ['cloud_provider_name' => CloudProviderEnums::XCLOUD_VULTR->value], ['Accept' => 'application/json'])->assertStatus(404); // this represents under xcloud

    Queue::assertPushed(ServerProvision::class);
});
