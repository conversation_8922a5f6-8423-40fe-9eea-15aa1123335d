<?php


use App\Models\SshKeyPair;
use App\Models\Team;
use App\Models\User;
use App\Services\Shell\SecureShellKey;
use Database\Seeders\CloudProviderSeeder;
use Database\Seeders\SshKeyPairSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Laravel\Jetstream\Jetstream;
use function Pest\Laravel\get;
use function Pest\Laravel\post;
use function Pest\Laravel\delete;
use function Pest\Laravel\actingAs;
use function Pest\Laravel\put;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed(\Database\Seeders\BillingPlanSeeder::class);
    $this->user = User::factory()->withPersonalTeam()->create(['name' => 'user name']);
    $this->team_admin = User::factory()->withPersonalTeam()->create(['name' => 'team_admin name']);
    $this->server_admin = User::factory()->withPersonalTeam()->create(['name' => 'server_admin name']);
    $this->site_admin = User::factory()->withPersonalTeam()->create(['name' => 'site_admin name']);

    $this->user->currentTeam->users()->attach(
        Jetstream::findUserByEmailOrFail($this->server_admin->email), ['role' => Team::SERVER_ADMIN]
    );

    $this->user->currentTeam->users()->attach(
        Jetstream::findUserByEmailOrFail($this->site_admin->email), ['role' => Team::SITE_ADMIN]
    );

    $this->user->currentTeam->users()->attach(
        Jetstream::findUserByEmailOrFail($this->team_admin->email), ['role' => Team::TEAM_ADMIN]
    );

    $this->server_admin->switchTeam($this->user->currentTeam);
    $this->team_admin->switchTeam($this->user->currentTeam);
    $this->site_admin->switchTeam($this->user->currentTeam);

    $this->seed([SshKeyPairSeeder::class, CloudProviderSeeder::class]);

    //use fake mailer and queue
    Mail::fake();
    Queue::fake();
    SshKeyPair::first()->update(['team_id' => $this->user->current_team_id]);

});


test('site admin cannot access team account.* routes', function () {
    actingAs($this->site_admin);
    $this->site_admin->switchTeam($this->user->currentTeam);
    get(route('user.profile.server'))->assertForbidden();
    get(route('user.ssh'))->assertForbidden();
    post(route('api.team.key.store'), ['key_name' => 'test', 'public_key' => 'test', 'always_provision' => false])->assertForbidden();
    delete(route('api.team.key.destroy', ['sshKeyPair' => SshKeyPair::first()]))->assertForbidden();
    get(route('user.notifications'))->assertForbidden();
    post(route('user.notifications'))->assertForbidden();
    //get(route('user.all-events'))->assertForbidden();
    get(route('user.archive-servers'))->assertForbidden();
    Queue::assertNothingPushed();
});

test('server admin cannot access team account.* routes', function () {
    actingAs($this->server_admin);
    $this->server_admin->switchTeam($this->user->currentTeam);
    get(route('user.profile.server'))->assertForbidden();
    get(route('user.ssh'))->assertForbidden();
    post(route('api.team.key.store'), ['key_name' => 'test', 'public_key' => 'test', 'always_provision' => false])->assertForbidden();
    delete(route('api.team.key.destroy', ['sshKeyPair' => SshKeyPair::first()]))->assertForbidden();
    get(route('user.notifications'))->assertForbidden();
    post(route('user.notifications'))->assertForbidden();
   // get(route('user.all-events'))->assertForbidden();
    get(route('user.archive-servers'))->assertForbidden();
    Queue::assertNothingPushed();
});

test('super cannot access team account.* routes without permissions', function () {
    actingAs($this->team_admin);
    $this->team_admin->switchTeam($this->user->currentTeam);
    get(route('user.profile.server'))->assertForbidden();
    get(route('user.ssh'))->assertForbidden();
    post(route('api.team.key.store'), ['key_name' => 'test', 'public_key' => 'test', 'always_provision' => false])->assertForbidden();
    delete(route('api.team.key.destroy', ['sshKeyPair' => SshKeyPair::first()]))->assertForbidden();
    get(route('user.notifications'))->assertForbidden();
    post(route('user.notifications'))->assertForbidden();
    //get(route('user.all-events'))->assertForbidden();
    get(route('user.archive-servers'))->assertForbidden();
    Queue::assertNothingPushed();
});

test('super can access team account.* routes with permissions', function () {
    actingAs($this->team_admin);
    $this->team_admin->switchTeam($this->user->currentTeam);
    get(route('user.profile.server'))->assertForbidden();

    user()->updatePermissions([
        'server:add-provider',
        'server:edit-provider',
        'server:delete-provider',
        'account:manage-ssh',
        'account:manage-notifications',
        'account:manage-logs',
        'account:manage-archive-servers',
        'server:manage-access'
    ]);
    get(route('user.profile.server'))->assertOk();
    get(route('user.ssh'))->assertOk();

    $this->mock(SecureShellKey::class, fn ($mock) => $mock->shouldReceive('getFingerPrint')->andReturn(true));
    post(route('api.team.key.store'), ['key_name' => 'test', 'public_key'=>'ssh-rsa AAAAB3NzaC1yc2EA', 'always_provision' => false])->assertRedirect();
    delete(route('api.team.key.destroy', ['sshKeyPair' => SshKeyPair::first()]))->assertRedirect();
    get(route('user.notifications'))->assertOk();
    post(route('user.notifications'))->assertRedirect();
    get(route('user.all-events'))->assertOk();
    get(route('user.archive-servers'))->assertOk();
    Queue::assertNothingPushed();
});
test('owner can access team account.* routes', function () {
    actingAs($this->user);
    get(route('user.profile.server'))->assertOk();
    get(route('user.ssh'))->assertOk();
    $this->mock(SecureShellKey::class, fn ($mock) => $mock->shouldReceive('getFingerPrint')->andReturn(true));
    post(route('api.team.key.store'), ['key_name' => 'test', 'public_key'=>'ssh-rsa AAAAB3NzaC1yc2EA', 'always_provision' => false])->assertRedirect();
    delete(route('api.team.key.destroy', ['sshKeyPair' => SshKeyPair::first()]))->assertRedirect();
    get(route('user.notifications'))->assertOk();
    post(route('user.notifications'))->assertRedirect();
    get(route('user.all-events'))->assertOk();
    get(route('user.archive-servers'))->assertOk();
    Queue::assertNothingPushed();
});
