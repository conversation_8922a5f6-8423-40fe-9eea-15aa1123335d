<?php

use App\Models\Server;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\Assert;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed(\Database\Seeders\BillingPlanSeeder::class);
    $this->user = createUser();
    $this->user2 = createUser();
    $this->team = $this->user->currentTeam;
    $this->server1 = Server::factory()->for($this->team)->for($this->user)->create();
    $this->server2 = Server::factory()->for($this->team)->for($this->user)->create();
});

it('returns expected server data', function () {
    $this->actingAs($this->user)
        ->get('/server')
        ->assertInertia(fn($assert) => $assert
            ->component('Server/Index')
            ->has('filter')
            ->has('tags')
            ->has('servers.data', 2)
            ->has('servers.data.1', fn($assert) => $assert
                ->where('id', $this->server1->id)
                ->where('name', $this->server1->name)
                ->etc()
            )
            ->has('servers.data.0', fn($page) => $page
                ->where('id', $this->server2->id)
                ->where('name', $this->server2->name)
                ->etc()
            )
        )->assertOk();

    $this->actingAs($this->user)
        ->getJson('/server')
        ->assertOk()
        ->assertJsonFragment([
            'filter' => 'All Servers',
        ]);
});

it('requires authentication', function () {
    $this->get('/server')->assertRedirect('/login');
});
