<?php

namespace Tests\Feature\Commands;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Models\Bill;
use App\Models\Product;
use App\Models\Server;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MatchServerProductSlugsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_match_server_slugs_with_product_slugs()
    {
        // Create a server
        $server = Server::factory()->create([
            'size' => 'vc2-1c-1gb',
        ]);

        // Create a product with matching slug
        $product = Product::factory()->create([
            'slug' => 'vc2-1c-1gb',
            'service_type' => BillingServices::xCloudManagedHosting,
        ]);

        // Create a bill with incorrect product_id
        $bill = Bill::factory()->create([
            'generator_type' => Server::class,
            'generator_id' => $server->id,
            'service' => BillingServices::xCloudManagedHosting,
            'renewal_period' => BillRenewalPeriod::Monthly,
            'product_id' => null,
        ]);

        // Run the command
        $this->artisan('xcloud:match-server-product-slugs')
            ->expectsOutput('Starting to match server slugs with product slugs...')
            ->assertExitCode(0);

        // Check if the bill's product_id has been updated
        $this->assertEquals($product->id, $bill->fresh()->product_id);
    }

    /** @test */
    public function it_respects_dry_run_option()
    {
        // Create a server
        $server = Server::factory()->create([
            'size' => 'vc2-1c-1gb',
        ]);

        // Create a product with matching slug
        $product = Product::factory()->create([
            'slug' => 'vc2-1c-1gb',
            'service_type' => BillingServices::xCloudManagedHosting,
        ]);

        // Create a bill with incorrect product_id
        $bill = Bill::factory()->create([
            'generator_type' => Server::class,
            'generator_id' => $server->id,
            'service' => BillingServices::xCloudManagedHosting,
            'renewal_period' => BillRenewalPeriod::Monthly,
            'product_id' => null,
        ]);

        // Run the command with dry-run option
        $this->artisan('xcloud:match-server-product-slugs --dry-run')
            ->expectsOutput('Running in dry-run mode. No changes will be made.')
            ->assertExitCode(0);

        // Check that the bill's product_id has NOT been updated
        $this->assertNull($bill->fresh()->product_id);
    }
}
