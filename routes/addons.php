<?php

use App\Http\Controllers\Addons\BackblazeReportController;
use App\Http\Controllers\Addons\MailboxAddonController;
use App\Http\Controllers\Addons\S3StorageProviderController;
use App\Http\Controllers\API\Addons\MailboxAddonsAPIController;

Route::middleware(['auth', 'force.payment', 'impersonate.action', 'team.switcher', 'email.verified'])->group(function () {
    ## Mailbox Addon routes
    Route::controller(MailBoxAddonController::class)->group(function () {
        Route::get('/mailboxes', 'index')->name('addons.mailboxes');
    });

    #s3 storage provider addon
    Route::controller(S3StorageProviderController::class)
        ->group(function () {
        Route::get('/s3-storage-provider', 'index')->name('addons.s3-storage-provider');
        Route::get('/s3-storage-provider/{addonStorageProvider}/file-manager', 'fileManager')->name('addons.s3-storage-provider.file-manager');
    });

    # Backblaze reporting
    Route::controller(BackblazeReportController::class)
        ->group(function () {
        Route::get('/s3-storage-provider/{addonStorageProvider}/report', 'bucketReport')->name('addons.s3-storage-provider.report');
    });
});

## API routes
Route::middleware(['auth:sanctum', 'force.payment'])->name('api.')->group(function () {
    // Mailbox API routes
    Route::controller(MailboxAddonsAPIController::class)
        ->prefix('mailbox')
        ->group(function () {
            Route::post('create-mailbox', 'create')->name('addons.mailbox.create');
            Route::post('{mailboxDomain}/verify-mailbox-dns-records', 'verifyMailboxDNSRecords')->name('addons.mailbox.verify-dns-records');
        });

    Route::controller(S3StorageProviderController::class)
        ->prefix('s3-storage-provider')
        ->group(function () {
            Route::post('store', 'store')->name('addons.storage-provider.store');
            #Delete
            Route::delete('delete/{addonStorageProvider}', 'destroy')->name('addons.storage-provider.destroy');

            # S3 File Manager API endpoints
            Route::prefix('{addonStorageProvider}')->group(function () {
                Route::get('list-files', 'listFiles')->name('addons.storage-provider.list-files');
                Route::post('upload-file', 'uploadFile')->name('addons.storage-provider.upload-file');
                Route::post('create-folder', 'createFolder')->name('addons.storage-provider.create-folder');
                Route::get('download-file', 'downloadFile')->name('addons.storage-provider.download-file');
                Route::post('rename-file', 'renameFile')->name('addons.storage-provider.rename-file');
                Route::delete('delete-file', 'deleteFile')->name('addons.storage-provider.delete-file');
                Route::get('get-file-content', 'getFileContent')->name('addons.storage-provider.get-file-content');
                Route::post('update-file-content', 'updateFileContent')->name('addons.storage-provider.update-file-content');
            });
        });
});
