<?php

use App\Callbacks\monitoring\MonitoringCallbackController;
use App\Http\Controllers\API\AlertController;
use App\Http\Controllers\API\BasicAuthController;
use App\Http\Controllers\API\CallbackController;
use App\Http\Controllers\API\CloudflareEdgeCacheController;
use App\Http\Controllers\API\CloudflareEdgeCachePluginController;
use App\Http\Controllers\API\CpanelMigrationController;
use App\Http\Controllers\API\CronJobController;
use App\Http\Controllers\API\DatabaseController;
use App\Http\Controllers\API\FirewallRuleController;
use App\Http\Controllers\API\FullPageCacheController;
use App\Http\Controllers\API\GoogleDriveController;
use App\Http\Controllers\API\InvitationCodeController;
use App\Http\Controllers\API\LogViewerController;
use App\Http\Controllers\API\MagicLoginController;
use App\Http\Controllers\API\NavigationController;
use App\Http\Controllers\API\PaymentGatewayController;
use App\Http\Controllers\API\PHPVersionController;
use App\Http\Controllers\API\RebootController;
use App\Http\Controllers\API\RedirectionController;
use App\Http\Controllers\API\RedisObjectCacheController;
use App\Http\Controllers\API\RestartServiceController;
use App\Http\Controllers\API\SearchController;
use App\Http\Controllers\API\ServerCloneController;
use App\Http\Controllers\API\ServerController;
use App\Http\Controllers\API\ServerCreationController;
use App\Http\Controllers\API\ServerDeleteController;
use App\Http\Controllers\API\ServerMigrationController;
use App\Http\Controllers\API\ServerMonitoringController;
use App\Http\Controllers\API\ServerUpdateController;
use App\Http\Controllers\API\SiteController;
use App\Http\Controllers\API\SiteEnvController;
use App\Http\Controllers\API\SiteMigrationController;
use App\Http\Controllers\API\SiteMonitoringController;
use App\Http\Controllers\API\SiteSshKeyController;
use App\Http\Controllers\API\SslCertificateController;
use App\Http\Controllers\API\StartServiceController;
use App\Http\Controllers\API\StopServiceController;
use App\Http\Controllers\API\SudoUserController;
use App\Http\Controllers\API\SupervisorProcessController;
use App\Http\Controllers\API\TaskController;
use App\Http\Controllers\API\TeamSshKePairyController;
use App\Http\Controllers\API\TelegramController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\WhiteLabelDomainCheckController;
use App\Http\Controllers\API\WPCachePluginController;
use App\Http\Controllers\API\WaitlistController;
use App\Http\Controllers\API\PackageController;
use App\Http\Controllers\API\XCloudAdminerController;
use App\Http\Controllers\API\XCloudFileManagerController;
use App\Http\Controllers\APIIntegrationController;
use App\Http\Controllers\BillController;
use App\Http\Controllers\API\InvoiceController;
use App\Http\Controllers\DebugController;
use App\Http\Controllers\OptionController;
use App\Http\Controllers\ProviderDatabaseController;
use App\Http\Controllers\TeamSubscriptionProductWebhook;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\CartFormController;
use App\Http\Middleware\ServerMigrationRedirect;
use App\Http\Controllers\API\SiteCloneController;
use App\Services\GitHub\GitHubService;
use Illuminate\Http\Request;

Route::get('/debug', [DebugController::class, 'api'])->name('api.xcloud.debug');

Route::controller(WebhookController::class)->group(function () {
    Route::post('payment/webhook/{gateway}', 'handle');
    Route::post('/fluentforms-split-payment', 'handleSplitPayment');
});

Route::controller(TeamSubscriptionProductWebhook::class)->prefix('stripe')->group(function () {
    Route::post('webhook/team-subscription-product', TeamSubscriptionProductWebhook::class)->name('stripe.team-subscription-product');
});

Route::controller(GoogleDriveController::class)->prefix('google-drive')->group(function () {
    Route::any('/callback', 'googleDriveCallback')->name('google-drive.callback');
    Route::any('/refresh-token/{hashid}', 'refreshAccessToken')->name('google-drive.refresh-token');
});

Route::controller(CallbackController::class)->group(function () {
    Route::get('/callback/{hashid}', 'handle');
    Route::any('/callback/provisioning/{hashid}', 'updateProvisioningStatus')->name('server.provisioning.status');
    Route::any('/callback/migrating/{hashid}', 'updateMigratingStatus')->name('site.migration.status');
    Route::any('/callback/deleting/{hashid}', 'updateDeletingStatus')->name('site.deleting.status');
    Route::any('/callback/updating/wp-core/{hashid}', 'updateWPCoreStatus')->name('site.updating.wp-core.status');
    Route::any('/callback/cpanelMigrationPath/{hashid}', 'updateCpanelMigrationPath')->name('serverMigration.cpanel-path.update');
    Route::any('/callback/cron-job/{hashid}/', 'getCronJobOutput')->name('server.cron-job');
    Route::any('/callback/certbot/domain-challenge/{hashid}/status', 'domainChallengeStatus')->name('site.certbot.domain-challenge.status');
    Route::any('/callback/certbot/domain-challenge/{hashid}/acme-challenge-done', 'acmeChallengeDone')->name('site.certbot.acme.challenge.done');
    Route::any('/callback/staging/{hashid}/pull-push/status', 'pullPushStatus')->name('site.staging.pull-push.status');
});

Route::controller(MonitoringCallbackController::class)->group(function () {
    Route::post('/callback/monitoring/server/{hashid}', 'severMonitoring');
    Route::post('/callback/monitoring/server/{hashid}/ssh-reboot', 'sshReboot');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}', 'siteMonitoring');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/updates', 'siteUpdates');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/backup', 'siteBackup');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/duplicity-remote-backups', 'duplicityRemoteBackups');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/duplicity-backups', 'duplicityBackups');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/database-backup', 'databaseBackup');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/restore', 'siteRestore');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/deleteBackup', 'deleteBackup');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/duplicity-manifest-backups', 'duplicityManifestBackups');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/store-incremental-backups', 'storeIncrementalBackups');
    Route::post('/callback/monitoring/server/{hashid}/site/{site_hashid}/incremental-backups', 'incrementalBackup');
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::controller(InvitationCodeController::class)->name('api.')->group(function () {
    Route::post('/validate/invitation_code', 'validateCode')->name('validate.invitation-code');
});

Route::controller(WaitlistController::class)->name('api.')->group(function () {
    Route::post('/waitlist/add', 'addToWaitlist')->name('waitlist.add');
});

Route::middleware(['auth:sanctum', 'force.payment'])->name('api.')->group(function () {

    Route::controller(\App\Http\Controllers\API\MegaMenuController::class)->group(function () {
        Route::get('site-list', 'getSiteList')->name('site-list');
        Route::get('server-list', 'getServerList')->name('server-list');
    });

    Route::get('/teams', [UserController::class, 'getTeams'])->name('user.teams');

    Route::controller(ServerController::class)->group(function () {
        Route::post('server/{server}/test', 'test')->name('server.test');
        Route::get('server/{server}/php-settings', 'phpSettings')->name('server.php-settings');
        Route::post('server/{server}/provision', 'provision')->name('server.provision');
        Route::post('server/{server}/timezone', 'updateTimezone')->name('server.timezone');
        Route::post('server/{server}/information', 'updateInformation')->name('server.information');
        Route::post('server/{server}/connection', 'updateConnection')->name('server.connection');
        Route::post('server/{server}/magic-login', 'updateMagicLoginSetting')->name('server.magic-login');
        Route::post('server/{server}/notes', 'updateNotes')->name('server.notes');
        Route::post('server/{server}/sudoUsers', [SudoUserController::class, 'store'])->name('server.sudoUsers.store');
        Route::put('server/{server}/sudoUsers/{sudoUser}', [SudoUserController::class, 'update'])->name('server.sudoUsers.update');
        Route::delete('server/{server}/sudoUsers/{sudoUser}', [SudoUserController::class, 'destroy'])->name('server.sudoUsers.destroy');
        Route::post('provider/connection/check', 'checkProviderConnection')->name('server.provider.connection');
        Route::post('server/{server}/retry-payment', 'retryPayment')->name('server.retry-payment');
        Route::get('server/{server}/timezone', 'getTimezone')->name('server.timezone.get');
        Route::post('server/{server}/cron-job', [CronJobController::class, 'store'])->name('server.cron.job.store');
        Route::put('server/{server}/cron-job/{cronJob}', [CronJobController::class, 'update'])->name('server.cron.job.update');
        Route::get('server/{server}/cron-job/{cronJob}/output', [CronJobController::class, 'output'])->name('server.cron.job.output');
        Route::delete('server/{server}/cron-job/{cronJob}', [CronJobController::class, 'destroy'])->name('server.cron.job.destroy');
        Route::get('server/{server}/fail2ban/banned-ip-addresses', 'bannedIpAddresses')->name('server.fail2ban.banned-ip-addresses');
        Route::post('server/{server}/fail2ban/unban-ip-address', 'unbanIpAddress')->name('server.fail2ban.unban-ip-address');
        Route::post('server/{server}/fail2ban/ban-ip-address', 'banIpAddress')->name('server.fail2ban.ban-ip-address');
        Route::get('server/{server}/upgradable-types', 'getUpgradableServerTypes')->name('server.upgradable_types')->middleware('permission.feature:server:upgrade');
        Route::get('server/{server}/backupList', 'getXCloudServerBackupList')->name('server.provider.backup.list');
        Route::get('server/{server}/backupSchedule', 'getBackupSchedule')->name('server.provider.backup.schedule');
        Route::post('server/{server}/vulnerability-scan', 'updateVulnerabilityScan')->name('server.vulnerability-scan.update');
        Route::get('server/{server}/site-list', 'sites')->name('server.site-list');
        Route::get('server/{server}/upgradeable-packages', 'upgradeablePackages')->name('server.upgradeable.packages');
        Route::post('server/{server}/upgrade-packages', 'upgradePackages')->name('server.upgrade.packages');
        Route::post('server/{server}/security-update', 'securityUpdate')->name('server.security-update');
        Route::get('server/{server}/cron-jobs', 'cronJobs')->name('server.cron-jobs');
    });

    Route::controller(SupervisorProcessController::class)->prefix('server/{server}')->group(function () {
        Route::post('/supervisor', 'store')->name('server.supervisor.store');
        Route::put('/supervisor/{supervisorProcess}', 'update')->name('server.supervisor.update');
        Route::get('/supervisor/{supervisorProcess}/output', 'output')->name('server.supervisor.output');
        Route::delete('/supervisor/{supervisorProcess}', 'destroy')->name('server.supervisor.destroy');
        Route::post('/supervisor/{supervisorProcess}/restart', 'restart')->name('server.supervisor.restart');
        Route::post('/supervisor/{supervisorProcess}/pause', 'pause')->name('server.supervisor.pause');
        Route::get('/supervisor/{supervisorProcess}/check-status', 'checkStatus')->name('server.supervisor.check-status');
        Route::get('/supervisor/check-all-status', 'checkAllStatus')->name('server.supervisor.check-all-status');
    });

    Route::controller(FirewallRuleController::class)->prefix('server/{server}')->group(function () {
        Route::post('/firewall-rule/create', 'create')->name('firewall-rule.create');
        Route::post('/firewall-rule/{firewallRule}/enable', 'enable')->name('firewall-rule.enable');
        Route::post('/firewall-rule/{firewallRule}/disable', 'disable')->name('firewall-rule.disable');
        Route::post('/firewall-rule/{firewallRule}/delete', 'delete')->name('firewall-rule.delete');
    });

    Route::controller(ServerMonitoringController::class)->group(function () {
        Route::post('server/{server}/installMonitoring', 'installMonitoring')->name('server.installMonitoring');
        Route::post('server/{server}/pullMonitoring', 'pullMonitoring')->name('server.pullMonitoring');
        Route::post('server/{server}/disableMonitoring', 'disableMonitoring')->name('server.disableMonitoring');
        Route::get('server/{server}/usages', 'usages')->name('server.monitoring.usages');
    });

    Route::controller(ServerCreationController::class)->group(function () {
//        ANY
        Route::post('server', 'customProvider')->name('server.store');

//        ALL UNDER XCLOUD
//        TODO: move these end-points to universal endpoints
        Route::post('xcloud', 'storeXCloud')->name('server.store.xcloud');
        Route::post('xcloudVultr', 'storeXCloudVultr')->name('server.store.xcloud.vultr');
        Route::post('whiteLabelVultr', 'storeWhiteLabelVultr')->name('server.store.white-label.vultr');
        Route::post('create-with-xCloud-ltd', 'storeXcloudLTD')->name('server.store.xcloud.ltd');
//        Route::get('xcloud/check-availability', 'checkAvailability')->name('server.xcloud.check-availability');


//        ALL DIGITALOCEAN ENDPOINTS
//        TODO: move this end-point to universal endpoints
        Route::post('digitalocean/{cloudProvider}', 'storeDigitalOcean')->name('server.store.do');

//        ALL GCP ENDPOINTS
//        TODO: move these end-points to universal endpoints
        Route::post('gcp/storeAndVerifyApiToken', 'verifyApiTokenGCP')->name('gcp.verifyApiToken');
        Route::post('gcp/verifyApiToken/{cloudProvider}', 'verifyProvider')->name('gcp.verifyProvider');
        // Route::post('gcp/regions/{cloudProvider}', 'verifyProvider')->name('gcp.verifyProvider');
        Route::post('gcp/getZones', 'getZones')->name('gcp.getZones');
//        Route::get('gcp/{provider}/{zone}/getSizes', 'getSizes')->name('gcp.getSizes'); // uncomment for gcp oAuth
        Route::post('gcp/getSizes', 'getSizes')->name('gcp.getSizes');
        Route::post('/server/store/gcp/{cloudProvider}', 'storeGCP')->name('server.store.gcp');

//        UNIVERSAL ENDPOINT
        Route::post('cloudProvider/validateCredential', 'validateAPICredential')->name('cloudProvider.validate.credential');
        Route::post('create/server', 'storeServer')->name('server.store.provider');
        Route::post('execute/provider/function', 'executeProviderFunction')->name('server.execute.provider.function');
    });

    Route::controller(ServerMigrationController::class)->middleware(ServerMigrationRedirect::class)->prefix('server/migrate/store')->group(function () {
        Route::post('connect/{serverMigration?}', 'customProvider')->name('server.migrate.store.connect');
        Route::post('sites/{serverMigration?}', 'sites')->name('server.migrate.store.sites');
        Route::post('domains/{serverMigration?}', 'domains')->name('server.migrate.store.domains');
        Route::post('confirm/{serverMigration?}', 'confirmMigration')->name('server.migrate.store.confirm');
        Route::post('domains/{serverMigration?}/updatedomain', 'updateFetchedDomain')->name('server.migrate.update.domain');
    });

    Route::controller(CpanelMigrationController::class)->middleware(ServerMigrationRedirect::class)->prefix('server/{server}/cpanel-migrate')->group
    (function () {
        Route::post('store/backup/{serverMigration?}', 'storeBackup')->name('server.migrate.cpanel.store.backup');
        Route::post('connect/api/{serverMigration?}', 'storeApi')->name('server.migrate.cpanel.store.api');
        Route::post('sites/{serverMigration?}', 'sites')->name('server.migrate.store.cpanel.sites');
        Route::post('api-sites/{serverMigration?}', 'apiSites')->name('server.migrate.store.cpanel.api.sites');
        Route::post('zip', 'uploadFile')->name('server.migrate.store.cpanel.file');
        Route::get('server/user', 'getCpanelUserForSCP')->name('server.migrate.cpanel.user');
        Route::post('server/backup-started', 'cpanelBackupStarted')->name('server.migrate.cpanel.started');
        Route::post('backup-list', 'fetchCpanelApiBackup')->name('server.migrate.cpanel.backup-list');
        Route::get('freshSites/{serverMigration?}', 'fetchFreshSitesFromAPI')->name('server.migrate.cpanel.sites.refresh');
        Route::post('generate-backup/{serverMigration?}', 'generateBackupUsingApi')->name('server.migrate.cpanel.generate-backup');
    });

    Route::controller(ServerMigrationController::class)->prefix('server/migrate/get')->group(function () {
        Route::get('freshSites/{serverMigration?}', 'fetchFreshSites')->name('server.migrate.get.sites.force');
        Route::get('sites/{serverMigration?}', 'fetchSites')->name('server.migrate.get.sites');
        Route::post('checkKeyPairByIp', 'checkKeyPairByIp')->name('server.migrate.check.keypair');
        Route::get('cpanel-migrations', 'getCpanelMigrations')->name('server.migrate.get.cpanel-migrations');
    });

    Route::controller(ServerDeleteController::class)->group(function () {
        Route::post('server/{server}/archive', 'archive')->name('server.archive');
        Route::post('server/{server}/restore', 'restore')->name('server.restore')->withTrashed();
        Route::post('server/{server}/delete', 'delete')->name('server.delete')->withTrashed();
    });

    Route::controller(ServerUpdateController::class)
        ->prefix('server/update/{server}')
        ->group(function () {
            Route::post('resize', 'resize')->name('server.update.resize')->middleware('permission.feature:server:upgrade');
            Route::post('backup', 'backup')->name('server.update.backup');
            Route::post('backupRestore', 'backupRestore')->name('server.update.backup.restore');
            Route::post('setBackupSchedule', 'setBackupSchedule')->name('server.update.backup.schedule');
        });

    Route::controller(TeamSshKePairyController::class)->group(function () {
        Route::post('/team/keys', 'store')->name('team.key.store');
        Route::delete('/team/keys/{sshKeyPair}', 'destroy')->name('team.key.destroy');
    });

    Route::controller(RebootController::class)->prefix('server/{server}')->group(function () {
        Route::post('/reboot', RebootController::class)->name('server.reboot');
        Route::post('/restart', RestartServiceController::class)->name('server.restart');
        Route::post('/enable', StartServiceController::class)->name('server.enable');
        Route::post('/disable', StopServiceController::class)->name('server.disable');
        Route::post('/hard-reboot', 'hardRebootServerFromVultr')->name('server.hard-reboot');
    });

    Route::controller(DatabaseController::class)->prefix('server/{server}')->group(function () {
        Route::get('/databases', 'index');
        Route::post('/database', 'store');
        // Route::post('/database/user', 'updateUser');
    });

    Route::controller(PHPVersionController::class)->prefix('server/{server}')->group(function () {
        Route::get('/php', 'index');
        Route::post('/php', 'store');
        Route::post('/php/default', 'updateDefault')->name('server.php.default');
        Route::post('/php/patch', 'patch');
        Route::delete('/php', 'destroy');
    });

    Route::controller(ProviderDatabaseController::class)->prefix('server/provider')->group(function () {
        Route::get('get-do-fields/{server}', 'getDoFields')->name('provider.do.fields');
        Route::get('get-do-databases/{server}/{clusterUUID}', 'getDoDatabases')->name('provider.do.databases');
        Route::post('/database', 'store'); // TODO: missing?
        Route::post('/database/user', 'updateUser'); // TODO: missing?
    });

    Route::post('/site/storeToPlayground', [SiteController::class, 'storeToPlayground'])->name('site.store-to-playground');

    Route::get('site/check-domain-exists', [SiteController::class, 'checkDomainExists'])->name('site.check-domain-exists');

    Route::controller(SiteController::class)->prefix('server/{server:id}')->group(function () {
        Route::get('/sites', 'index');
        Route::post('/site', 'store')->name('site.store');
        Route::get('/site/{site:id}', 'show');
        Route::post('/site/{site:id}/provision', 'provision');
        Route::post('/site/{site:id}/domain', 'updateDomain')->name('site.update.domain');
        Route::delete('/site/{site:id}/delete', 'destroy')->name('site.delete');
        Route::post('/site/{site:id}/ssh', [SiteSshKeyController::class, 'store'])->name('site.ssh.update');
        Route::post('/verifyDns', 'verifyDns')->name('site.verify.dns');
        Route::post('/{site:id}/verifyDnsForEmail', 'verifyDnsForEmail')->name('site.verify.dns-for-email');
        Route::post('/site/{site:id}/ssl', [SslCertificateController::class, 'update'])->name('site.ssl.update');
        Route::delete('/site/{site:id}/ssl', [SslCertificateController::class, 'destroy'])->name('site.ssl.destroy');
        Route::put('/site/{site:id}/tags', 'updateTags')->name('site.tags.update');
        Route::get('/site/{site:id}/phpVersion', 'getPhpVersionSettings')->name('site.phpVersion');
        Route::post('/site/{site:id}/phpVersion', 'updatePhpVersion')->name('site.phpVersion.update');
        Route::get('/site/{site:id}/wp-cron', 'getWpCron')->name('site.wpCron.get');
        Route::get('/site/{site:id}/indexing-settings/get', 'getSiteIndexingSettings')->name('site.indexing-settings.get');
        Route::post('/site/{site:id}/indexing-settings/update', 'updateSiteIndexingSettings')->name('site.indexing-settings.update');
        Route::get('/site/{site:id}/disable-html', 'getDisableHtml')->name('site.disable-html.get');
        Route::post('/site/{site:id}/wp-cron', 'updateWpCron')->name('site.wpCron.update');
        Route::post('/site/{site:id}/purgeObjectCache', 'purgeObjectCache')->name('site.purge-cache');
        Route::post('/site/{site:id}/purgeRedisObjectCache', 'purgeRedisObjectCache')->name('site.purge-redis-cache');
        Route::post('/site/{site:id}/redirection', [RedirectionController::class, 'addRedirection'])->name('site.redirection.add');
        Route::put('/site/{site:id}/redirection/{redirection:id}', [RedirectionController::class, 'updateRedirection'])->name('site.redirection.update');
        Route::delete('/site/{site:id}/redirection/{redirection:id}', [RedirectionController::class, 'removeRedirection'])->name('site.redirection.remove');
        Route::post('/site/{site:id}/domain/go_live', 'goLiveDomain')->name('site.update.domain.go_live');
        Route::post('/site/{site:id}/backup', 'updateBackup')->name('site.backup.update');
        Route::post('/site/{site:id}/email_provider', 'updateEmailProvider')->name('site.update.email_provider');
        Route::post('/site/{site:id}/email_provider/clear', 'clearEmailProvider')->name('site.clear.email_provider');
        Route::post('/site/{site:id}/backup/backup-now', 'backupNow')->name('site.backup.backup_now');
        Route::post('/site/{site:id}/backup/restore', 'restoreNow')->name('site.backup.restore');
        Route::delete('/site/{site:id}/backup/delete', 'deleteBackup')->name('site.backup.delete');
        Route::get('/site/{site:id}/backup/incremental-files', 'incrementalFiles')->name('site.backup.incremental');
        Route::delete('/site/{site:id}/backup/remove', 'removeBackup')->name('site.backup.remove');
        Route::post('/site/{site:id}/backup/update-note', 'updateNote')->name('site.backup.update_note');
        Route::post('/site/{site:id}/hide-mail-alert', 'hideMailAlert')->name('site.hide-mail-alert');
        Route::post('/site/{site:id}/ignore-checksum-alert', 'ignoreChecksumAlert')->name('site.ignore-checksum-alert');
        Route::post('/site/{site:id}/send-test-mail', 'sendTestMail')->name('site.send-test-mail');
        Route::post('/site/{site:id}/update-nginx-config', 'updateNginxConfig')->name('site.nginxOptions.update');
        Route::post('/site/{site:id}/state', 'updateSiteState')->name('site.state.update');
        Route::post('/site/{site:id}/update-wp-debug', 'updateWpDebug')->name('site.wpDebug.update');
        Route::post('/site/{site:id}/switch-regenerate-conf', 'switchRegenerateConf')->name('site.nginxOptions.switch.regenerate');
        Route::get('/site/{site:id}/nginx-config', 'getNginxConfig')->name('site.nginxOptions.preview');
        Route::get('/site/{site:id}/re-provision', 'reProvision')->name('site.re-provision');
        Route::post('/site/{site:id}/git/update', 'gitUpdate')->name('site.git.update');
        Route::post('/site/{site:id}/git/pull-and-deploy', 'pullAndDeployLaravel')->name('site.git.pull-and-deploy');
        Route::post('/site/{site:id}/adminer/', 'updateAdminer')->name('site.adminer');
        Route::post('/site/{site:id}/file-manager/', 'updateFileManager')->name('site.file.manager');
        Route::post('/site/{site:id}/user-isolation/', 'updateUserIsolation')->name('site.user.isolation');

        Route::post('/site/{site:id}/nginx/run-and-debug', 'nginxConfigRunAndDebug')->name('site.nginx.run-and-debug');
        Route::delete('/site/{site:id}/nginx/{customNginx}/delete', 'deleteCustomNginxConfig')->name('site.custom-nginx.delete');
        Route::get('/site/domain-challenge/initiate-challenge', 'initiateDomainChallenge')->name('site.domain-challenge.initiate-challenge');
        Route::get('/site/domain-challenge/challenged', 'domainChallenged')->name('site.domain-challenged');
        Route::post('/site/{site:id}/rescue/', 'recueSite')->name('site.rescue');
        Route::post('/site/{site:id}/vulnerability-scan', 'updateVulnerabilityScan')->name('site.vulnerabilityScan.update');

        Route::post('/site/{site:id}/deploy-staging', 'deployStaging')->name('site.staging.deploy');
        Route::get('/site/{site:id}/fetch-production-database-tables', 'fetchProductionDatabaseTables')->name('site.staging.fetch-production-site-database-tables');
        Route::post('/site/{site:id}/pull-from-production', 'pullFromProduction')->name('site.staging.pull-from-production');
        Route::post('/site/{site:id}/push-to-production', 'pushToProduction')->name('site.staging.push-to-production');
        Route::get('/site/{site:id}/fetch-deployment-logs', 'fetchDeploymentLogs')->name('site.staging.fetch-deployment-logs');
        Route::get('/site/{site:id}/fetch-staging-and-production-sites', 'fetchStagingAndProductionSites')->name('site.fetch-staging-and-production-sites');
        Route::post('/site/{site:id}/ignore-vulnerability', 'ignoreVulnerability')->name('site.ignore-vulnerability');
        Route::post('/site/{site:id}/scan-vulnerability', 'scanVulnerability')->name('site.scan-vulnerability');
        Route::delete('/site/{site:id}/ip-remove', 'removeIpAddress')->name('site.ip.remove');
        Route::post('/site/{site:id}/ip-store', 'storeIPAddress')->name('site.ip.store');
        Route::post('/site/{site:id}/ip-update', 'updateIPAddress')->name('site.ip.update');
        Route::post('/site/{site:id}/object-cache-pro', 'updateObjectCachePro')->name('site.object-cache-pro.update');
        Route::delete('/site/{site:id}/purge-object-cache-pro', 'purgeObjectCachePro')->name('site.purge-object-cache-pro');
        Route::post('/site/{site:id}/cloudflare-edge-cache', [CloudflareEdgeCacheController::class, 'update'])->name('site.cloudflare-edge-cache.update');
        Route::delete('/site/{site:id}/purge-cloudflare-edge-cache', [CloudflareEdgeCacheController::class, 'purge'])->name('site.purge-cloudflare-edge-cache');
        Route::post('/site/{site:id}/web-root', 'updateWebRoot')->name('site.web-root.update');
        Route::post('/site/{site:id}/scan-wp-checksum', 'scanWpChecksum')->name('site.scan.checksum');
        Route::post('/site/{site:id}/wp-checksum-result', 'getWpChecksumResult')->name('site.scan.result');
        Route::post('/site/{site:id}/patchstack-invoice', 'generatePatchstackInvoice')->name('site.patchstack-invoice');
        Route::post('/site/{site:id}/enable/patchstack-vulnerability', 'enablePatchstackVulnerability')->name('site.enable.patchstack-vulnerability');
        Route::post('/site/{site:id}/cancel/patchstack-vulnerability', 'cancelPatchstackVulnerability')->name('site.cancel.patchstack-vulnerability');
        Route::post('/site/{site:id}/scan/patchstack-vulnerability', 'scanPatchstackVulnerability')->name('site.scan.patchstack-vulnerability');
        Route::post('/site/{site:id}/ignore/patchstack-vulnerability', 'ignorePatchstackVulnerability')->name('site.ignore.patchstack-vulnerability');
        Route::post('/site/{site:id}/hide/patchstack-vulnerability', 'hidePatchstackVulnerability')->name('site.hide.patchstack-vulnerability');
    });

    Route::controller(\App\Http\Controllers\API\LaravelApplicationController::class)->prefix('server/{server:id}')->group(function () {
        Route::get('/site/{site:id}/laravel-application', 'getLaravelApplication')->name('site.laravel-application');
        Route::post('/site/{site:id}/maintenance', 'updateMaintenanceMode')->name('site.maintenance.update');
        Route::post('/site/{site:id}/laravel-app/update', 'updateLaravelAppSettings')->name('site.laravel-app.update');
        Route::post('/site/{site:id}/laravel-app/clear-cache', 'clearApplicationCache')->name('site.laravel-app.clear-cache');
        Route::post('/site/{site:id}/horizon/start', 'startHorizon')->name('site.horizon.start');
        Route::post('/site/{site:id}/horizon/stop', 'stopHorizon')->name('site.horizon.stop');
        Route::post('/site/{site:id}/horizon/restart', 'restartHorizon')->name('site.horizon.restart');
        Route::post('/site/{site:id}/scheduler/setup', 'setupScheduler')->name('site.scheduler.setup');
        Route::post('/site/{site:id}/scheduler/stop', 'stopScheduler')->name('site.scheduler.stop');
        Route::post('/site/{site:id}/queue/start', 'startQueueWorker')->name('site.queue.start');
        Route::post('/site/{site:id}/queue/{supervisorProcess}/stop', 'stopQueueWorker')->name('site.queue.stop');
        Route::put('/site/{site:id}/queue/{supervisorProcess}/update', 'updateQueueWorker')->name('site.queue.update');
    });

    Route::controller(SiteMonitoringController::class)->prefix('server/{server:id}')->group(function () {
        Route::post('/site/{site:id}/installMonitoring', 'installMonitoring')->name('site.installMonitoring');
        Route::post('/site/{site:id}/pullMonitoring', 'pullMonitoring')->name('site.pullMonitoring');
        Route::post('/site/{site:id}/pullUpdateMonitoring', 'pullUpdateMonitoring')->name('site.pullUpdateMonitoring');
        Route::post('/site/{site:id}/disableMonitoring', 'disableMonitoring');
        Route::post('/site/{site:id}/disableMigrationBanner', 'disableMigrationBanner')->name('site.disableMigrationBanner');
        Route::post('/site/{site:id}/updateWordPress', 'updateWordPress')->name('site.updateWordPress');
        Route::post('/site/{site:id}/updateTheme', 'updateTheme')->name('site.updateTheme');
        Route::get('/site/{site:id}/wpUpdates', 'wpUpdates')->name('site.wp-updates');
        Route::post('/site/{site:id}/updatePlugin', 'updatePlugin')->name('site.updatePlugin');
        Route::post('/site/{site:id}/togglePlugin', 'togglePlugin')->name('site.togglePlugin');
        Route::post('/site/{site:id}/activeTheme', 'activeTheme')->name('site.activeTheme');
    });

    Route::controller(SiteMigrationController::class)
        ->prefix('migrate/store/')
        ->group(function () {
            Route::post('destination/{siteMigration?}', 'storeDestination')->name('site.migrate.store.destination');
            Route::post('domain/auto/{server}', 'storeAutoDomain')->name('site.migrate.store.domains.auto');
            Route::post('domain/git/{server}', 'storeGitDomain')->name('site.migrate.store.domains.git');
            Route::post('domain/restore-backup/{server}', 'storeBackupRestoreDomain')->name('site.migrate.store.domains.restore_backup');
            Route::post('database/{siteMigration}/auto', 'setupDatabase')->name('site.migrate.store.database.auto');
            Route::post('domain/manual/{server}/', 'storeManualDomain')->name('site.migrate.store.domains.manual');
            Route::post('database/{siteMigration}/manual', 'setupManualDatabase')->name('site.migrate.store.database.manual');
            Route::post('settings/{siteMigration}', 'storeSettings')->name('site.migrate.store.settings');
            Route::post('plugin/{siteMigration}', 'configurePlugin')->name('site.migrate.store.plugin');
            Route::post('git/{siteMigration}', 'configureGitInfo')->name('site.migrate.store.git_repo');
            Route::post('backup-restore/{siteMigration}/backups', 'storeBackupFiles')->name('site.migrate.store.backups');
            Route::post('backup-source/{siteMigration}', 'storeBackupSource')->name('site.migrate.store.backup_restore.source');
            Route::get('backup-files/{siteMigration}', 'getBackupFiles')->name('site.migrate.backup_files');
            Route::post('confirm/{siteMigration}', 'confirmMigration')->name('site.migrate.store.confirm');

            //auto migration
            Route::post('files/{siteMigration}', 'configureFiles')->name('site.migrate.store.files');

            //manual migration
            Route::post('zip/{siteMigration}', 'uploadFile')->name('site.migrate.store.file');

            Route::post('database/{siteMigration}', 'uploadDatabase')->name('site.migrate.store.database');

            //cancel migration
            Route::delete('cancel/{siteMigration}', 'cancelMigration')->name('site.migrate.cancel');
        });

    ### Site clone routes
    Route::post('clone/store/destination/{siteClone?}', [SiteCloneController::class, 'storeDestination'])
        ->name('site.clone.store.destination');

    Route::controller(SiteCloneController::class)
        ->prefix('clone/store/')
        ->group(function () {
            Route::post('domain/auto/{server}/siteClone/{siteClone?}/site/{site}', 'storeAutoDomain')
                ->name('site.clone.store.domains.auto');
//            Route::post('database/{siteClone}/auto/site/{site}', 'setupDatabase')->name('site.clone.store.database.auto');
            Route::post('database/{siteClone}', 'storeDatabase')->name('site.clone.store.database');
            Route::post('domain/manual/{server}/site/{site}', 'storeManualDomain')->name('site.clone.store.domains.manual');
//            Route::post('database/{siteClone}/manual', 'setupManualDatabase')->name('site.clone.store.database.manual');
            Route::post('settings/{siteClone}/site/{site}', 'storeSettings')->name('site.clone.store.settings');
            Route::post('confirm/{siteClone}/site/{site}', 'confirmClone')->name('site.clone.store.confirm');

        });

    Route::controller(ServerCloneController::class)
        ->prefix('server-clone/store/')
        ->group(function () {
            Route::post('destination', 'cloneTo')
                ->name('server.clone.destination');
        });

    Route::controller(TaskController::class)->group(function () {
        Route::get('/tasks', 'index')->name('tasks');
        Route::post('/tasks/server/{server}', 'store')->name('task.store');
        Route::get('/task/{task}', 'show')->name('task.show');
        Route::get('/event/output/{task}', 'eventOutput')->name('task.event.output');
    });

    Route::get('site/{site}/env', [SiteEnvController::class, 'show'])->name('site.env.show');
    Route::put('site/{site}/env', [SiteEnvController::class, 'store'])->name('site.env.store');

    Route::post('site/{site}/basicAuth/enable', [BasicAuthController::class, 'enable'])->name('basicAuth.enable');
    Route::post('site/{site}/basicAuth/disable', [BasicAuthController::class, 'disable'])->name('basicAuth.disable');

    Route::post('site/{site}/fullPageCache/enable', [FullPageCacheController::class, 'enable'])->name('fullPageCache.enable');
    Route::post('site/{site}/fullPageCache/disable', [FullPageCacheController::class, 'disable'])->name('fullPageCache.disable');

    Route::post('site/{site}/redisObjectCache/enable', [RedisObjectCacheController::class, 'enable'])->name('redisObjectCache.enable');
    Route::post('site/{site}/wpPluginCache/{plugin_slug}/enable', [WPCachePluginController::class, 'enable'])->name('wpPluginCache.enable');
    Route::post('site/{site}/wpPluginCache/{plugin_slug}/disable', [WPCachePluginController::class, 'disable'])->name('wpPluginCache.disable');
    Route::post('site/{site}/wpPluginCache/{plugin_slug}/switch', [WPCachePluginController::class, 'switch'])->name('wpPluginCache.switch');
    Route::post('site/{site}/redisObjectCache/disable', [RedisObjectCacheController::class, 'disable'])->name('redisObjectCache.disable');

    ## LogController
    Route::controller(LogViewerController::class)->group(function () {
        Route::get('server/{server}/logs/nginx', 'nginxLog')->name('server.logs');
        Route::get('server/{server}/logs/nginx/clear', 'clearServerLog')->name('server.logs.clear');
        Route::get('site/{site}/logs', 'siteLog')->name('site.logs');
        Route::get('site/{site}/logs/clear', 'clearSiteLog')->name('site.logs.clear');
    });

    ### Database controller ###
    Route::controller(DatabaseController::class)->group(function () {
        Route::get('server/{server}/databases', 'index')->name('server.databases');
        Route::get('server/{server}/database_users', 'users')->name('server.database.users');
        Route::post('server/{server}/database/add', 'store')->name('server.database.add');
        Route::post('server/{server}/database/user/add', 'addDatabaseUser')->name('server.database.user.add');
        Route::delete('server/{server}/database/{database}', 'destroy')->name('server.database.destroy');
        Route::get('server/{server}/database/user/{user}', 'getDbUserData')->name('server.database.user.get');
        Route::post('server/{server}/database/user/{user}/edit', 'updateUser')->name('server.database.user.edit');
        Route::delete('server/{server}/database/user/{user}', 'destroyUser')->name('server.database.user.destroy');
    });


    Route::get('search', [SearchController::class, 'search'])->name('search');
    Route::get('servers/search', [SearchController::class, 'searchServers'])->name('search.servers');
    Route::get('sites/search', [SearchController::class, 'searchSites'])->name('search.sites');
    Route::get('servers/site-backup/search', [SearchController::class, 'searchServerSiteBackup'])->name('search.server.site-backup');
    Route::get('sites/site-backup/search', [SearchController::class, 'searchSitesBackup'])->name('search.sites.site-backup');
    Route::get('buckets/search', [SearchController::class, 'searchBucket'])->name('search.bucket');

    Route::group(['prefix' => 'alerts'], function () {
        Route::get('/', [AlertController::class, 'alerts'])->name('alerts');
        Route::put('/view/{alert}', [AlertController::class, 'markAsRead'])->name('alerts.view');
        Route::put('/read', [AlertController::class, 'markAllAsRead'])->name('alerts.readAll');
    });

    ### Payment Gateway
    Route::controller(PaymentGatewayController::class)->group(function () {
        Route::get('/payment_method/{gateway}/add', 'addPaymentMethod')->name('payment.add');
        Route::post('/payment_method/{payment_method}/set_as_default', 'setAsDefault')->name('payment-method.set-as-default');
        Route::delete('/payment_method/{payment_method}/delete', 'deletePaymentMethod')->name('payment-method.delete');
    });

    Route::controller(APIIntegrationController::class)->group(function () {
        Route::post('/user/integration/store/cloudflare', 'storeCloudflareIntegration')->name('user.integration.cloudflare.store');
        Route::post('/user/integration/cloudflare/{cloudflare}/set_as_default', 'setAsDefault')->name('user.integration.cloudflare.set-as-default');
        Route::delete('/user/integration/cloudflare/{cloudflare}/delete', 'deleteIntegration')->name('user.integration.cloudflare.delete');
        Route::get('/user/integration/cloudflare/{domain}/check_domain_exists', 'checkDomainExists')->name('user.integration.cloudflare.check-domain-exists');
    });

    Route::controller(BillController::class)->group(function () {
        Route::post('bills/pay', 'payBills')->name('bill.pay');
    });

    Route::controller(InvoiceController::class)->group(function () {
        Route::get('invoice/get/{invoice_id}', 'getInvoice')->name('invoice.get');
        Route::post('invoice/pay/{invoice:invoice_number}', 'payInvoice')->name('invoice.pay');
        Route::post('invoice/{invoice}/cancel', 'cancelInvoice')->name('invoice.cancel');
        Route::post('bill/invoice/generate', 'generateInvoiceFromBills')->name('bill.invoice.generate');
    });

    Route::controller(\App\Http\Controllers\BluePrintController::class)->group(function () {
        Route::post('{bluePrint}/default', 'makeDefault')->name('blueprints.default');
        Route::post('{bluePrint}/clone', 'clone')->name('blueprints.clone');
        Route::delete('{bluePrint}/delete', 'destroy')->name('blueprints.delete');
    });

    Route::post('/white-label/verifyDns', [WhiteLabelDomainCheckController::class, 'verifyDns'])->name('white-label-domain-check.verify-dns');
});

Route::get('/checkout/package/{package}', [PackageController::class, 'checkoutPackage'])->name('api.package-xc.checkout');

# Git pull and deploy
Route::any('git/{hashid}/deploy', [SiteController::class, 'pullAndDeploy'])->name('git-site.deploy');
Route::any('/site/remote-login/verify', [MagicLoginController::class, 'verify']);
Route::any('/site/remote-login/failed', [MagicLoginController::class, 'failed']);

# Cloudflare Edge Cache Plugin
Route::any('/site/cloudflare-edge-cache/purge', [CloudflareEdgeCachePluginController::class, 'purge']);

Route::any('/site/xcloud-adminer/verify', [XCloudAdminerController::class, 'verify']);
Route::any('/site/xcloud-tfm/verify', [XCloudFileManagerController::class, 'verify']);

Route::post('user/check/', [UserController::class, 'checkUser'])->name('api.user.check');
Route::get('user/email', [UserController::class, 'getUserEmail'])->name('api.user.email');
Route::post('user/email/update', [UserController::class, 'updateUserEmail'])->name('api.user.email.update');
Route::post('user/sites/vulnerability-scan', [UserController::class, 'scanVulnerability'])->name('api.user.sites.vulnerability-scan');

Route::controller(CartFormController::class)->prefix('cart')->group(function () {
    Route::post('package/{package}/checkout', 'storeWithPackage')->name('api.cart.storeWithPackage');
    Route::post('product/{product}/checkout', 'storeWithProduct')->name('api.cart.storeWithProduct');
});

Route::post('/telegram/webhook', [TelegramController::class, 'handleTelegramWebhook'])->name('telegram.webhook');
Route::get('/caddy/validate-whitelabel-domain', [WhiteLabelDomainCheckController::class, 'check']);
Route::post('/confirm-3d-secured-payment', [PaymentGatewayController::class, 'confirm3DSecurePayment'])->name('api.payment.confirm-3d-secured-payment');
Route::post('/confirm-package-3d-secured-payment', [PaymentGatewayController::class, 'confirmPackage3DSecurePayment'])->name('api.package.payment.confirm-3d-secured-payment');
Route::post('/confirm-product-3d-secured-payment', [PaymentGatewayController::class, 'confirmProduct3DSecurePayment'])->name('api.product.payment.confirm-3d-secured-payment');
Route::post('/confirm-subscription-product-3d-secured-payment', [PaymentGatewayController::class, 'confirmSubscriptionProduct3DSecurePayment'])->name('api.subscription-product.payment.confirm-3d-secured-payment');
Route::any('/clear-github-release-cache', [GitHubService::class, 'clearCache']);

Route::get('/navigation-store', [NavigationController::class, 'index'])->name('navigation-store')->middleware('auth');
Route::post('/hide-optin', [OptionController::class, 'hideOptin'])->name('admin.action.optin.hide')->middleware('auth');
