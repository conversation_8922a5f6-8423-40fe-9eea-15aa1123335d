<?php

use App\Enums\CloudProviderEnums;
use App\Http\Controllers\AffiliateController;
use App\Http\Controllers\API\GoogleDriveController;
use App\Http\Controllers\API\MagicLoginController;
use App\Http\Controllers\API\PhpMyAdminLoginController;
use App\Http\Controllers\API\SitePhpMyAdminLoginController;
use App\Http\Controllers\API\XCloudAdminerController;
use App\Http\Controllers\API\XCloudFileManagerController;
use App\Http\Controllers\AutoSiteCloneController;
use App\Http\Controllers\AutoSiteMigrationController;
use App\Http\Controllers\BillController;
use App\Http\Controllers\BluePrintController;
use App\Http\Controllers\CpanelMigrationController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DebugController;
use App\Http\Controllers\EmailProviderController;
use App\Http\Controllers\GitSiteMigrationController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\PluginIntegrationController;
use App\Http\Controllers\ServerCloneController;
use App\Http\Controllers\ServerMigrationController;
use App\Http\Controllers\ManualSiteMigrationController;
use App\Http\Controllers\OAuthController;
use App\Http\Controllers\ServerController;
use App\Http\Controllers\ServerCreationController;
use App\Http\Controllers\SingleProfileController;
use App\Http\Controllers\SingleServerController;
use App\Http\Controllers\SingleSiteController;
use App\Http\Controllers\SiteBackupRestoreMigrationController;
use App\Http\Controllers\SiteController;
use App\Http\Controllers\SiteMigrationController;
use App\Http\Controllers\StripeOAuthController;
use App\Http\Controllers\TeamInvitationController;
use App\Http\Controllers\CartFormController;
use App\Http\Controllers\WhiteLabel\LandingPageController;
use App\Http\Controllers\XcloudProductDashboard;
use App\Http\Middleware\ServerMigrationRedirect;
use App\Http\Middleware\ServerRedirector;
use App\Http\Middleware\SiteCloneRedirector;
use App\Http\Middleware\SiteMigrationRedirector;
use App\Http\Middleware\SiteRedirector;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Laravel\Fortify\Http\Controllers\VerifyEmailController;
use Laravel\Jetstream\Http\Controllers\CurrentTeamController;
use Laravel\Jetstream\Jetstream;
use Laravel\Nova\Contracts\ImpersonatesUsers;

Route::get('/', [LandingPageController::class, 'index'])->name('landing-page');
Route::get('/terms-and-services', [LandingPageController::class, 'tos'])->name('terms-and-services');
Route::get('/privacy-policy', [LandingPageController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/debug', [DebugController::class, 'index'])->name('xcloud.debug');

Route::get('/waitlist', function (){
    return Inertia::render('Waitlist/User');
})->name('waitlist.user');

Route::get('/preloader', function (){
    return Inertia::render('Preloader');
})->name('preloader');

Route::post('/payment_webhook', [PaymentGatewayController::class, 'webhook'])->name('payment-webhook');
Route::post('affiliate-webhook-xcloud', [AffiliateController::class, 'affiliateWebhook'])->name('affiliate-webhook');

Route::middleware(['auth', 'force.payment', 'impersonate.action', 'team.switcher', 'email.verified'])->group(function () {

    Route::get('switch-to-team/{team}', function (Request $request, Team $team) {
        if ($request->user()->belongsToTeam($team)) {
            $request->user()->switchTeam($team);
        }
        return $request->get('switch_team_request_url') ? redirect()->to($request->get('switch_team_request_url')) : redirect()->back();
    })->name('switch-to-team');

    Route::get('/phpinfo', fn() => phpinfo())->name('phpinfo')->middleware('admin');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Server Routes
    Route::get('server', [ServerController::class, 'index'])->name('server.index');

    Route::controller(ServerCreationController::class)->group(function () {
        Route::get('/server/create', 'chooseProvider')->name('server.create');
        Route::get('/server/create/any', 'addWithCustomProvider')->name('server.create.custom');

        // Universal
        Route::get('/credential/choose/{cloudProviderSlug}', 'viewChooseCredentials')->name('server.choose.credential'); // $cloudProviderSlug is enum as slug only
        Route::get('/server/create/{cloudProvider?}', 'createWithProvider')->name('server.create.provider'); // here cloudProvider can be id or enum as slug (only gcp, xcloud, xcloud-provider)
        Route::get('/server-create', 'whiteLabelServer')->name('white-label-server.create'); // here cloudProvider can be id or enum as slug (only gcp, xcloud, xcloud-provider)

    });

    // Single server functions
    Route::controller(SingleServerController::class)
        ->middleware(ServerRedirector::class)
        ->prefix('/server/{server}')->group(function () {
            Route::get('/', 'show')->name('server.show');
            Route::get('/sites', 'sites')->name('server.sites');
            Route::get('/database', 'database')->name('server.database');
            Route::get('/sudo', 'sudoUsers')->name('server.sudo');
            Route::get('/sudo/create', 'createSudoUser')->name('server.sudo.create');
            Route::get('/sudo/{sudoUser}/edit', 'editSudoUser')->name('server.sudo.edit');
            Route::get('/meta', 'metaData')->name('server.meta');
            Route::get('/firewall-management', 'firewallManagement')->name('server.firewall-management');
            Route::get('/vulnerability-settings', 'vulnerabilitySettings')->name('server.vulnerability-settings');
            Route::get('/security-update', 'securityUpdate')->name('server.security-update');
            Route::get('/others', 'settings')->name('server.settings');
            Route::get('/events', 'events')->name('server.events');
            Route::get('/command-runner', 'commandRunner')->name('server.command.runner');
            Route::get('/monitoring', 'monitoring')->name('server.monitoring');
            Route::get('/server-settings', 'management')->name('server.management');
            Route::get('/backup', 'backup')->name('server.backup');
            Route::get('/migration', 'migration')->name('server.migration');
            Route::get('/php-configuration', 'phpSettings')->name('server.php.settings');
            Route::put('/php-settings', 'updatePhpSettings')->name('server.php.settings.update');
            Route::get('/logs', 'logs')->name('server.logs');
            Route::get('/progress', 'progress')->name('server.progress');
            Route::get('/cron-job', 'cronJob')->name('server.cron.job');
            Route::get('/cron-job/create', 'createCronJob')->name('server.cron.job.create');
            Route::get('/cron-job/{cronJob}/edit', 'editCronJob')->name('server.cron.job.edit');
            Route::get('/supervisor', 'supervisor')->name('server.supervisor');
            Route::get('/supervisor/create', 'createSupervisorProcess')->name('server.supervisor.create');
            Route::get('/supervisor/{supervisorProcess}/edit', 'editSupervisorProcess')->name('server.supervisor.edit');
        });

    // Site Routes
    Route::get('/site', [SiteController::class, 'index'])->name('site.index');
    Route::get('/site/{site:id}/remote-login', [MagicLoginController::class, 'redirect'])->name('site.remote-login');
    Route::get('/site/{site:id}/xc-adminer', [XCloudAdminerController::class, 'redirect'])->name('site.adminer-login');
    Route::get('/site/{site:id}/xc-tfm', [XCloudFileManagerController::class, 'redirect'])->name('site.tfm-login');
    Route::get('/server/{server:id}/phpmyadmin', [PhpMyAdminLoginController::class, 'redirect'])->name('server.phpmyadmin-login');
    Route::get('/site/{site:id}/phpmyadmin', [SitePhpMyAdminLoginController::class, 'redirect'])->name('site.phpmyadmin-login');
    Route::get('/site/create', [SiteController::class, 'chooseServer'])->name('site.create.server-choose');
    Route::get('/server/{server}/clone', [ServerCloneController::class, 'chooseServer'])->name('server.clone.server-choose');
    Route::get('/site/sort-by-server', [SiteController::class, 'siteSortByServer'])->name('site.sort-by-server');
    Route::controller(SiteController::class)
        ->middleware(ServerRedirector::class)
        ->prefix('/server/{server}/site/')->group(function () {
            Route::get('create', 'create')->name('site.create');
            Route::get('create/wordpress', 'wordpress')->name('site.create.platform.wordpress');
            Route::get('create/custom-php', 'customPhp')->name('site.create.platform.custom-php');
            Route::get('create/laravel', 'laravel')->name('site.create.platform.laravel');
            Route::get('create/script-installer', 'scriptInstaller')->name('site.create.platform.script-installer');
            Route::get('installWordPress', 'installWordPress')->name('site.create.wordpress');
            Route::get('installOneClickApp', 'installOneClickApp')->name('site.create.oneclick');
            Route::get('migrateWordPress', 'migrateWordPress')->name('site.migrate.wordpress');
            Route::get('uploadWordPress', 'uploadWordPress')->name('site.upload.wordpress');
        });

    Route::get('/site/installWordPressToPlayground', [SiteController::class, 'installWordPressToPlayground'])->name('site.create.wordpress.playground');
    Route::get('/site/{site:id}/playground/progress', [SiteController::class, 'playgroundProgress'])->name('site.playground.progress');

    // Single Site Routes
    Route::get('/site/{site}', [SingleSiteController::class, 'show'])->name('site.redirect')
        ->middleware(SiteRedirector::class); // Redirect to full

    Route::controller(SingleSiteController::class)
        ->middleware([SiteRedirector::class, ServerRedirector::class])
        ->prefix('/server/{server:id}/site/{site:id}')->group(function () {
            Route::get('/', 'show')->name('site.show');
            Route::get('/progress', 'progress')->name('site.progress');
            Route::get('/site-overview', 'overview')->name('site.overview');
            Route::get('/updates', 'updates')->name('site.updates');
            Route::get('/vulnerability-scan', 'vulnerabilityScan')->name('site.vulnerability-scan');
            Route::get('/git', 'git')->name('site.git');
            Route::get('/monitoring', 'monitoring')->name('site.monitoring');
            Route::get('/domain', 'domain')->name('site.domain');
            Route::get('/ssl', 'sslHttps')->name('site.ssl');
            Route::get('/redirection', 'redirection')->name('site.redirection');
            Route::get('/email-configuration', 'emailProvider')->name('site.email_provider');
            Route::get('/wp-config', 'wpConfig')->name('site.wp-config');
            Route::get('/environment', 'wpConfig')->name('site.environment');
            Route::get('/caching', 'fullPageCaching')->name('site.caching');
            Route::get('/ssh', 'sshFtp')->name('site.ssh');
            Route::get('/logs', 'log')->name('site.logs');
            Route::get('/events', 'events')->name('site.events');
            Route::get('/command-runner', 'commandRunner')->name('site.command.runner');
            Route::get('/backup', 'backup')->name('site.backup');
            Route::get('/backups', 'backups')->name('site.backups');
            Route::get('/backups/{backupFile}/download', 'downloadBackup')->name('site.backups.download');
            Route::get('/settings', 'settings')->name('site.settings');
            Route::get('/application', 'application')->name('site.application');
            Route::get('/clone', 'clone')->name('site.clone');
            Route::get('/staging_environment', 'stagingEnvironment')->name('site.staging.environment');
            Route::get('/database', 'database')->name('site.database');
            Route::get('/wp-debug', 'getWpDebug')->name('site.wp.debug');
            Route::get('/file-manager', 'fileManager')->name('site.file.manager');
            Route::get('/nginx-customization', 'nginx')->name('site.nginx');
            Route::get('/web-server-security', 'webServerSecurity')->name('site.web-server.security');
            Route::get('/basic-authentication', 'basicAuthentication')->name('site.basic.authentication');
            // Route::get('/nginx', 'nginx')->name('site.nginx'); // duplicate route
            Route::get('/staging-management', 'stagingManagement')->name('site.staging.management');
            Route::get('/ip-management', 'ipManagement')->name('site.ip_management');
            Route::get('/integrity-monitor', 'integrityMonitor')->name('site.integrity.monitor');

        });


    Route::get('/server/{server:id}/viewSite/{siteName}', [SingleSiteController::class, 'viewSite'])->name('site.view');
    // Migration from Git
    Route::get('server/{server?}/site/{siteMigration?}/gitMigrate', [GitSiteMigrationController::class, 'destination'])
        ->name('site.migrate.git');
    Route::controller(GitSiteMigrationController::class)->prefix('server/{server?}/site/')
        ->middleware(SiteMigrationRedirector::class)->group(function () {
            Route::get('migrate/git/domains', 'domains')->name('site.migrate.git.domains');
            Route::get('{siteMigration?}/git-repository', 'git_repo')->name('site.migrate.git_repo');
        });

    // Migration from Backup
    Route::get('server/{server?}/site/{siteMigration?}/backup-restore', [SiteBackupRestoreMigrationController::class, 'destination'])
        ->name('site.migrate.backup-restore');

    Route::controller(SiteBackupRestoreMigrationController::class)->prefix('server/{server?}/site/')
        ->middleware(SiteMigrationRedirector::class)->group(function () {
            Route::get('migrate/backup-restore/domains', 'domains')->name('site.migrate.backup_restore.domains');
            Route::get('migrate/backup-restore/{siteMigration?}/source', 'source')->name('site.migrate.backup_restore.source');
            Route::get('migrate/backup-restore/{siteMigration?}/backups', 'backups')->name('site.migrate.backup_restore.backups');
        });
    //end of migration from backup

    // Migration Routes
    Route::get('server/{server?}/site/{siteMigration?}/autoMigrate', [AutoSiteMigrationController::class, 'destination'])
        ->name('site.migrate.destination');

    Route::controller(SiteMigrationController::class)->prefix('server/{server?}/site/{siteMigration?}/migrate')
        ->middleware([SiteMigrationRedirector::class, ServerRedirector::class])
        ->group(function () {
            Route::get('settings', 'settings')->name('site.migrate.settings');
            Route::get('confirm', 'confirm')->name('site.migrate.confirm');
            Route::get('processing', 'processing')->name('site.migrate.processing');
        });

    Route::controller(AutoSiteMigrationController::class)->prefix('server/{server?}/site/')
        ->middleware(SiteMigrationRedirector::class)->group(function () {
        Route::get('migrate/auto/domains', 'domains')->name('site.migrate.auto.domains');
        Route::get('{siteMigration?}/migrate/auto/database', 'database')->name('site.migrate.auto.database');
        Route::get('{siteMigration?}/migrateplugin', 'plugin')->name('site.migrate.plugin');
    });

    // Migration Routes
    Route::get('server/{server?}/site/{siteMigration?}/manualMigrate', [ManualSiteMigrationController::class, 'destination'])
        ->name('site.upload.destination');

    Route::controller(ManualSiteMigrationController::class)
        ->prefix('server/{server?}/site/')
        ->middleware(SiteMigrationRedirector::class)
        ->group(function () {
            Route::get('/migrate/manual/domains', 'domains')->name('site.migrate.manual.domains');
            Route::get('{siteMigration?}/migrate/manual/database', 'database')->name('site.migrate.manual.database');
            Route::get('{siteMigration?}/migrate/files', 'files')->name('site.migrate.files');
        });

    ### Clone routes
    Route::get('server/{server?}/site/{siteClone?}/autoClone', [AutoSiteCloneController::class, 'destination'])
        ->name('site.clone.destination');

    Route::controller(AutoSiteCloneController::class)->prefix('server/{server?}/site/{site}')
        ->middleware(SiteCloneRedirector::class)->group(function () {
            Route::get('{siteClone?}/clone/auto/domains', 'domains')->name('site.clone.auto.domains');
            Route::get('{siteClone?}/clone/database', 'database')->name('site.clone.database');
            Route::get('{siteClone?}/clone/settings', 'settings')->name('site.clone.settings');
            Route::get('{siteClone?}/clone/confirm', 'confirm')->name('site.clone.confirm');
        });

    //server migration controller
    Route::get('server/{server?}/site/{serverMigration?}/serverMigrate', [ServerMigrationController::class, 'source'])
        ->name('server.migrate.source');

    Route::get('server/{server?}/server-migration/{serverMigration?}/cpanelMigrate', [CpanelMigrationController::class, 'source'])
        ->name('server.cpanel.migrate.source');


    Route::controller(CpanelMigrationController::class)->prefix('server/{server?}/server-migration/{serverMigration?}/cpanelMigrate')
        ->middleware([ServerMigrationRedirect::class, ServerRedirector::class])
        ->group(function () {
            Route::get('sites', 'sites')->name('server.cpanel.migrate.sites');
        });

    Route::controller(ServerMigrationController::class)->prefix('server/{server?}/site/{serverMigration?}/serverMigrate')
        ->middleware([ServerMigrationRedirect::class, ServerRedirector::class])
        ->group(function () {
            Route::get('sites', 'sites')->name('server.migrate.sites');
            Route::get('domains', 'domains')->name('server.migrate.domains');
            Route::get('confirm', 'confirm')->name('server.migrate.confirm');
            Route::get('processing', 'processing')->name('server.migrate.processing');
        });

    // single profile functions
    Route::controller(SingleProfileController::class)->group(function () {
        Route::get('/user/profile', 'profile')->name('user.profile');
        Route::get('/user/profile/invitations', 'invitations')->name('user.profile.invitations');
        Route::post('/user/profile', 'profileUpdate');
        Route::get('/user/password', 'password')->name('user.password');
        Route::post('/user/password', 'passwordUpdate');
        Route::get('/user/team-management', 'teamManagement')->name('user.team');
        Route::get('/user/customization', 'customization')->name('user.customization');
        Route::get('/user/export', 'export')->name('user.export');
        Route::get('/user/export-data', 'exportData')->name('user.export-data');
        Route::post('/user/disable/file-manager', 'disableFileManager')->name('user.disable.file-manager');
        Route::post('/user/disable/adminer', 'disableAdminer')->name('user.disable.adminer');
        Route::get('/user/vulnerable-sites', 'vulnerableSites')->name('user.vulnerability_scanner');
        Route::get('/user/team-invitations', 'teamInvitations')->name('user.team.invitations');
        Route::get('/user/team/create', 'createTeam')->name('user.team.create');
        Route::post('/user/team/', 'storeTeam')->name('user.team.store');
        Route::get('/user/team/{team}/edit', 'editTeam')->name('user.team.edit');
        Route::delete('/user/team/{team}/', 'deleteTeam')->name('user.team.delete');
        Route::put('/user/team/{team}/', 'updateTeam')->name('user.team.update');
        Route::get('/user/team/{team}/team-member/create', 'addUser')->name('user.team.member.create');
        Route::get('/user/team/{team}/team-member/{user}/edit', 'editTeamMember')->name('user.team.member.edit');
        Route::put('/user/team/{team}/team-member/{user}/', 'updateTeamMember')->name('user.team.member.update');
        Route::delete('/user/team/{team}/team-member/{user}/', 'removeTeamMember')->name('user.team.member.destroy');
        Route::delete('/user/team/{team}/invitation/{invitation}/', 'removeTeamInvitation')->name('user.team.invitation.destroy');
        Route::post('/user/team/{team}/invitation/{invitation}/', 'resendInvitationMail')->name('user.team.invitation.resend');
        Route::get('/user/team/{team}/invitation/{invitation}/edit', 'editTeamInvitation')->name('user.team.invitation.edit');
        Route::put('/user/team/{team}/invitation/{invitation}/edit', 'updateTeamInvitation')->name('user.team.invitation.update');
        Route::post('/user/team/{team}/team-member/', 'storeUserToTeam')->name('user.team.member.store');
        Route::get('/user/team/{team}', 'teamDetails')->name('user.team.show');
        Route::get('/user/role-management', 'roleManagement')->name('user.role');
        Route::get('/user/server-provider', 'serverProvider')->name('user.profile.server');
        Route::post('/user/server-provider', 'addServerProvider')->name('user.profile.server.add');
        Route::put('/user/server-provider/{provider}', 'updateServerProvider')->name('user.profile.server.update');
        Route::delete('/user/server-provider/{provider}', 'removeServerProvider')->name('user.profile.server.remove');
        Route::get('/user/ssh-key', 'sshKey')->name('user.ssh');
        Route::get('/user/browser-sessions', 'browserSessions')->name('user.sessions');
        Route::delete('/user/browser-sessions/{session}', [SingleProfileController::class, 'logOutSpecificSession'])->name('user.sessions.logout');
        Route::delete('/user/all-browser-sessions', [SingleProfileController::class, 'logOutOtherSessions'])->name('other-browser-sessions.destroy');
        Route::get('/user/authentication', 'authentication')->name('user.authentication');
        Route::delete('/user/authentication/disable-warning', 'disable2FAWarning')->name('user.authentication.disable-warning');
//        Route::get('/user/billing-details', 'billingDetails')->name('user.billingDetails');
        Route::get('/user/bill-details/{id}', 'billDetails')->name('user.bill-details');
        Route::get('/user/manual-invoices/{invoice?}', 'manualInvoices')->name('user.manual.invoices');
        Route::get('/user/bills-payment', 'billsPayment')->name('user.bills-payment');
        Route::get('/user/notifications', 'notifications')->name('user.notifications');
        Route::post('/user/notifications', 'updateNotifications');
        Route::get('/user/team-packages', 'packages')->name('user.team.packages');
        Route::get('/user/team-products', 'products')->name('user.team.products');
        Route::get('/user/email-subscriptions', 'emailSubscriptions')->name('user.email.subscriptions');
        Route::get('/user/patchstack-subscriptions', 'patchstackSubscriptions')->name('user.patchstack.subscriptions');
        Route::get('/user/whitelabel-subscriptions', 'whitelabelSubscriptions')->name('user.whitelabel.subscriptions');
        Route::get('/user/detailed-invoices/{invoice?}', 'detailedInvoices')->name('user.detailedInvoices');
        Route::get('/user/all-events', 'allEvents')->name('user.all-events');
        Route::get('/user/storage-provider', 'storageProvider')->name('user.storage-provider');
        Route::post('/user/storage-provider', 'storeStorageProvider')->name('user.storage-provider.store');
        Route::put('/user/storage-provider/{provider?}', 'updateStorageProvider')->name('user.storage-provider.update');
        Route::delete('/user/storage-provider/{provider?}', 'dropStorageProvider')->name('user.storage-provider.delete');
        Route::get('/user/notification-integration', 'notificationIntegration')->name('user.notification-integration');
        Route::post('/user/notification-integration', 'storeNotificationIntegration')->name('user.notification-integration.store');
        Route::get('/user/slack/redirect', 'slackRedirect')->name('user.slack.redirect');
        Route::post('/user/notification-integration/disconnect/{notificationIntegration}', 'disconnectNotificationIntegration')->name('user.notification-integration.disconnect');
        Route::post('/user/notification-integration/reconnect/{notificationIntegration}', 'reconnectNotificationIntegration')->name('user.notification-integration.reconnect');
        Route::get('/user/whatsapp/callback', 'whatsAppCallback')->name('user.whatsapp.callback');
        Route::post('/user/language', 'language')->name('user.language');
        #jetstream routes
        Route::put('/current-team', [CurrentTeamController::class, 'update'])->name('current-team.update');

        # Archive Servers
        Route::get('/user/archive-servers', 'archiveServers')->name('user.archive-servers');
        Route::delete('/user/server/{server:id}/delete', 'deleteServer')->name('user.archive-servers.delete')->withTrashed();
        Route::put('/user/server/{server:id}/restore', 'restoreServer')->name('user.archive-servers.restore')->withTrashed();

        # Cloudflare integration
        Route::get('/user/integration/cloudflare', 'cloudflareIntegration')->name('user.integration.cloudflare');
    });

    Route::controller(PluginIntegrationController::class)->prefix('integration')->group(function () {
        Route::get('others', 'integrationPlugins')->name('integration.plugins');
        Route::post('others', 'storeIntegrationPlugins')->name('integration.plugins.store');
        Route::put('others/{pluginIntegration}', 'updateIntegrationPlugins')->name('integration.plugins.update');
        Route::get('others/search', 'searchIntegrationPlugins')->name('integration.plugins.search');
        Route::delete('others/{pluginIntegration}', 'destroy')->name('integration.plugins.destroy');
    });

    #Blue print integration
    Route::resource('blueprints', BluePrintController::class);

    //email provider controller
    Route::controller(EmailProviderController::class)->prefix('user')->group(function () {
        Route::get('/email-provider', 'index')->name('team.email_provider');
        Route::put('/email-provider/', 'store')->name('team.email_provider.store');
        //update email provider
        Route::put('/email-provider/{email_provider}', 'update')->name('team.email_provider.update');
        //delete email provider
        Route::delete('/email-provider/{email_provider}', 'destroy')->name('team.email_provider.destroy');
        Route::post('/send-test-email/{email_provider}', 'sendTestEmail')->name('team.email_provider.send_test_email');
    });

    Route::controller(BillController::class)->prefix('bill')->group(function () {
//        Route::get('/user/billing-details', 'billingDetails')->name('user.billingDetails');
        Route::get('/user/bills', 'bills')->name('user.bills');
    });

    Route::controller(InvoiceController::class)->prefix('invoice')->group(function () {
        #Route::get('/user/detailed-invoices/{invoice?}', 'detailedInvoices')->name('user.detailedInvoices');
        Route::post('invoice/refund', 'refundInvoice')->name('invoice.refund');
        Route::post('detailed-invoices/{invoice?}/pay', 'payInvoiceBill')->name('user.invoice.pay');
        Route::get('{invoice:invoice_number}/pay', 'payInvoice')->name('invoice.pay');
        Route::get('{invoice:invoice_number}/download', 'downloadInvoice')->name('user.invoice.download');
        Route::get('{invoice:invoice_number}/manual/download', 'downloadManualInvoice')->name('user.invoice.manual.download');
        Route::get('{invoice:invoice_number}/preview-download', 'previewDownloadInvoice')->name('user.invoice.preview-download');
        Route::get('{invoice:invoice_number}/manual/preview-download', 'previewDownloadManualInvoice')->name('user.invoice.manual.preview-download');
        Route::get('{invoice:invoice_number}/preview-as-html', 'previewAsHtml')->name('user.invoice.preview-as-html');
        Route::get('manual/{invoice:invoice_number}/download', 'downloadManualInvoice')->name('user.invoice.manual.download');
    });

    if (Jetstream::hasTeamFeatures()) {
        Route::get('/team-invitations/{invitation}', [TeamInvitationController::class, 'accept'])->name('team-invitations.accept');
    }

    //OAuth Cloud Providers
    Route::get('/oauth/{provider}/redirect', [OAuthController::class, 'redirectToProvider'])
        ->name('oauth.redirect')
        ->where('provider', implode('|', CloudProviderEnums::asValue()));

    Route::get('/oauth/{provider}/callback', [OAuthController::class, 'handleProviderCallback'])
        ->name('oauth.callback')
        ->where('provider', implode('|', CloudProviderEnums::asValue()));

    Route::get('/stripe/card_add/success', [PaymentGatewayController::class, 'stripeCardAddSuccess'])->name('stripe.card_add.success');
    Route::get('/stripe/card_add/cancelled', [PaymentGatewayController::class, 'stripeCardAddCancelled'])->name('stripe.card_add.cancelled');

    Route::get('/stripe/card/requires_3d_security', [PaymentGatewayController::class, 'stripeCardRequires3DSecurity'])->name('stripe.checkout.requires3dsecure');
    Route::get('/package/stripe/card/requires_3d_security', [PaymentGatewayController::class, 'packageStripeCardRequires3DSecurity'])->name('stripe.package.checkout.requires3dsecure');
    Route::get('/product/stripe/card/requires_3d_security', [PaymentGatewayController::class, 'productStripeCardRequires3DSecurity'])->name('stripe.product.checkout.requires3dsecure');
    Route::get('/subscription-product/stripe/card/requires_3d_security', [PaymentGatewayController::class, 'subscriptionProductStripeCardRequires3DSecurity'])->name('stripe.subscription-product.checkout.requires3dsecure');

    // Nova impersonation routes
    Route::get('/impersonation-stop', function (Request $request, ImpersonatesUsers $impersonator) {
        if ($impersonator->impersonating($request) || (request()->has('from') && request()->get('from') == 'nova')) {
            if (session()->get('impersonate_url')) {

                $backUrl = session()->get('impersonate_url');

                User::forgetImpersonationSession();

                $impersonator->stopImpersonating($request, Auth::guard(), User::class);

                return redirect($backUrl);
            } else {
                $impersonator->stopImpersonating($request, Auth::guard(), User::class);
                abort(404);
//                back();
            }
        } else {
            abort(404);
        }
    })->name('impersonation.stop');
});
Route::get('/user/team/invitation/{invitation}/show', [TeamInvitationController::class,'invitation'])->name('user.team.invitation.show');
Route::get('/user/team/invitation/{invitation}/login', [TeamInvitationController::class,'invitationLogin'])->name('user.team.invitation.login');
Route::get('/user/team/invitation/{invitation}/register', [TeamInvitationController::class,'invitationRegister'])->name('user.team.invitation.register');

Route::controller(CartFormController::class)->prefix('cart')->group(function () {
    Route::get('package/checkout', 'checkoutWithPackage')->name('cart.checkoutWithPackage');
    Route::get('product/checkout', 'checkoutWithProduct')->name('cart.checkoutWithProduct');
    Route::get('products/{product}/checkout', 'checkoutWithProductForWhiteLabel')->name('cart.checkoutWithProductForWhiteLabel');

    // Route::get('package-lists', 'packageLists')->name('cart.package-lists');
    Route::get('/stripe/package/checkout/success', 'checkoutPackage')->name('stripe.package.checkout.success');
    Route::get('/stripe/package/checkout/cancelled', 'cancelPackageCheckout')->name('stripe.package.checkout.cancelled');

    Route::get('/stripe/product/checkout/success', 'checkoutProduct')->name('stripe.product.checkout.success');
    Route::get('/stripe/product/checkout/cancelled', 'cancelProductCheckout')->name('stripe.product.checkout.cancelled');
});

Route::controller(BillController::class)->group(function () {
    Route::get('/bill-calculator', 'billCalculator')->name('user.bill-calculator');
    Route::post('/calculate-bill', 'calculateBill')->name('user.bill.calculate');
});

Route::controller(XcloudProductDashboard::class)->prefix('landing')->group(function () {
    Route::get('products', 'product')->name('xcloud.product');
    Route::get('packages', 'package')->name('xcloud.package');
});
#Route::get('invitation-code/mail', [InvitationCodeController::class,'show'])->name('invitation-code.show');
Route::post('/view-type', [SingleProfileController::class, 'storeViewType'])->name('store.view.type');

Route::controller(GoogleDriveController::class)->prefix('google-drive')->group(function () {
    Route::post('/authorize', 'googleDriveAuthorize')->name('google-drive.authorize');
    Route::put('/update/{provider?}', 'googleDriveUpdate')->name('google-drive.update');
});

// Override the default email verification route, we need to use `signed:relative` middleware to make it work on white label
Route::group(['middleware' => config('fortify.middleware', ['web'])], function () {
    $enableViews = config('fortify.views', true);
    $verificationLimiter = config('fortify.limiters.verification', '6,1');
    $limiter = config('fortify.limiters.login');
    Route::get('/email/verify/{id}/{hash}', [VerifyEmailController::class, '__invoke'])
        ->middleware([config('fortify.auth_middleware', 'auth').':'.config('fortify.guard'), 'signed:relative', 'throttle:'.$verificationLimiter])
        ->name('verification.verify');

    // Password Reset...
    if (\Laravel\Fortify\Features::enabled(\Laravel\Fortify\Features::resetPasswords())) {
        Route::post('/reset-password', [\App\Http\Controllers\API\ResetUserPasswordController::class, 'store'])
            ->middleware(['guest:'.config('fortify.guard')])
            ->name('password.update');
    }

    // user login
    if ($enableViews) {
        Route::post('/login', [\App\Http\Controllers\API\UserLoginController::class, 'store'])
            ->middleware(array_filter([
                'guest:'.config('fortify.guard'),
                $limiter ? 'throttle:'.$limiter : null,
            ]));
    }
});
