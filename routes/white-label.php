<?php

use App\Http\Controllers\WhiteLabel\BillingController;
use App\Http\Controllers\WhiteLabel\ClientController;
use App\Http\Controllers\WhiteLabel\OnboardController;
use App\Http\Controllers\WhiteLabel\ProductController;
use App\Http\Controllers\WhiteLabel\SettingsController;
use App\Http\Controllers\WhiteLabelController;
use Illuminate\Support\Facades\Route;

## Web routes
Route::middleware(['auth', 'force.payment', 'impersonate.action', 'team.switcher', 'email.verified', 'white-label.access'])->group(function () {
    Route::get('dashboard', [WhiteLabelController::class, 'dashboard'])->name('white-label.dashboard');
    Route::get('all-servers', [WhiteLabelController::class, 'AllServers'])->name('white-label.all-servers');
    Route::get('all-sites', [WhiteLabelController::class, 'AllSites'])->name('white-label.all-sites');

    Route::prefix('onboarding')
        ->controller(OnboardController::class)
        ->name('white-label.onboarding.')->group(function () {
            Route::get('/startup', 'startup')->name('startup');
            Route::get('/checkout', 'checkout')->name('checkout');
            Route::get('/brand-setup', 'brandSetup')->name('brand-setup');
            Route::get('/payment-setup', 'paymentSetup')->name('payment-setup');
            Route::get('/create-product', 'createProduct')->name('create-product');
            Route::get('/domain-setup', 'domainSetup')->name('domain-setup');
            Route::post('/store-domain-setup', 'storeDomainSetup')->name('store-domain-setup');
            Route::post('/skip-domain-setup', 'skipDomainSetup')->name('skip-domain-setup');
            Route::post('/signup', 'storeSignup')->name('store.signup');
            Route::post('/brand-setup', 'storeBrandSetup')->name('store.brand-setup');
    });

    Route::prefix('api')->name('api.white-label.')->group(function () {
        Route::prefix('products')->name('products.')->group(function () {
            Route::get('/', [App\Http\Controllers\API\Whitelabel\ProductController::class, 'index'])->name('index');
        });
    });

    Route::prefix('products')
        ->name('white-label.products.')->group(function () {
            Route::get('/', [ProductController::class, 'index'])->name('index');
            Route::get('/{product}/edit', [ProductController::class, 'edit'])->name('edit');
            Route::get('/details/{product}', [ProductController::class, 'details'])->name('details');
            Route::post('store', [ProductController::class, 'store'])->name('store');
            Route::post('update/{product}', [ProductController::class, 'update'])->name('update');
            Route::delete('delete/{product}', [ProductController::class, 'delete'])->name('delete');
            Route::post('duplicate/{product}', [ProductController::class, 'duplicate'])->name('duplicate');
//            Route::resource('products', ProductController::class); // Todo: why not we are using resource controller? @Rakib vai
        });

    Route::prefix('clients')
        ->name('white-label.clients.')->group(function () {
            Route::get('/', [ClientController::class, 'index'])->name('index');
            Route::get('/details/{user}', [ClientController::class, 'details'])->name('details');
            Route::post('/update/information', [ClientController::class, 'updateInformation'])->name('update.information');
    });

    Route::prefix('billing')
        ->name('white-label.billing.')->group(function () {
        Route::get('/', [BillingController::class, 'index'])->name('index');
    });

    Route::get('/payment/settings', [SettingsController::class, 'paymentSettings'])->name('white-label.payment.settings');
    Route::get('/brand/settings', [SettingsController::class, 'brandSettings'])->name('white-label.brand.settings');
    Route::post('/brand/settings/update', [SettingsController::class, 'brandSettingsUpdate'])->name('white-label.brand.settings.update');
    Route::get('/domain/settings', [SettingsController::class, 'domainSettings'])->name('white-label.domain.settings');
    Route::post('/domain/settings', [SettingsController::class, 'domainSettingsUpdate'])->name('white-label.domain.settings.update');
    Route::get('/landing-page/settings', [SettingsController::class, 'landingPageSettings'])->name('white-label.landing-page.settings');
    Route::post('/landing-page/settings/update', [SettingsController::class, 'landingPageSettingsUpdate'])->name('white-label.landing-page.settings.update');
    Route::get('/terms-and-services/settings', [SettingsController::class, 'tosPageSettings'])->name('white-label.terms-and-services.settings');
    Route::post('/terms-and-services/settings/update', [SettingsController::class, 'tosPageSettingsUpdate'])->name('white-label.terms-and-services.settings.update');
    Route::get('/privacy-policy/settings', [SettingsController::class, 'privacyPolicySettings'])->name('white-label.privacy-policy.settings');
    Route::post('/privacy-policy/settings/update', [SettingsController::class, 'privacyPolicySettingsUpdate'])->name('white-label.privacy-policy.settings.update');
    Route::get('/smtp/settings', [SettingsController::class, 'smtpSettings'])->name('white-label.smtp.settings');
    Route::post('/smtp/settings/update', [SettingsController::class, 'smtpSettingsUpdate'])->name('white-label.smtp.settings.update');
});

## API routes
Route::middleware(['auth:sanctum', 'force.payment', 'white-label.access'])->name('api.')->group(function () {
    // Billing routes
    Route::controller(\App\Http\Controllers\API\Whitelabel\BillingController::class)
        ->prefix('whitelabel/{whiteLabel}')
        ->group(function () {
            Route::post('process-payment', 'processPayment')->name('whitelabel-billing.process-payment');
            Route::post('claim-offer', 'claimOffer')->name('whitelabel-billing.claim-offer');
            Route::post('connect-to-stripe', 'connectToStripe')->name('whitelabel-billing.connect-to-stripe');
            Route::get('{callFrom}/stripe-connect-callback', 'stripeConnectCallback')->name('whitelabel-billing.stripe-connect-callback');
            Route::get('{callFrom}/stripe-connect-refresh', 'stripeConnectRefresh')->name('whitelabel-billing.stripe-connect-refresh');
            Route::get('check-account-status', 'checkAccountIsActivated')->name('whitelabel-billing.check-account-status');
            Route::post('save-publishable-key', 'savePublishableKey')->name('whitelabel-billing.save-publishable-key');
        });

    // Stripe OAuth routes
    Route::controller(\App\Http\Controllers\StripeOAuthController::class)
        ->group(function () {
            Route::get('/stripe/{whiteLabel}/{callFrom}/connect', 'redirectToStripe')->name('stripe.oauth.connect');
            Route::get('/stripe/callback', 'handleCallback')->name('stripe.oauth.callback');
            Route::post('/stripe/disconnect', 'disconnectAccount')->name('stripe.oauth.disconnect');
        });
});
