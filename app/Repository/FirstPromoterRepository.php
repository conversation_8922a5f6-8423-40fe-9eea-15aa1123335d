<?php

namespace App\Repository;

use App\Models\Promoter;
use App\Models\Team;
use App\Services\FirstPromoter\FirstPromoterClient;
use Arr;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class FirstPromoterRepository extends FirstPromoterClient
{
    public function __construct()
    {
        parent::__construct(config('services.first_promoter.api_key'));
    }

    /**
     * Sign up a lead on FirstPromoter.
     *
     * @param Team   $team
     * @param string $affiliateId
     *
     * @return bool
     * @throws GuzzleException
     */
    public function signupLead(Team $team, string $affiliateId): bool
    {
        $promoter = Promoter::where('default_ref_id', $affiliateId)->first();

        if (!$promoter) {
            return false;
        }

        $data = [
            'email'  => $team->email,
            'ref_id' => $promoter->default_ref_id,
        ];

        $leadOnFirstPromoter = $this->createLead($data);

        if (empty($leadOnFirstPromoter)) {
            $this->logLeadCreationFailure($team, $affiliateId, $promoter);
            return false;
        }

        // attach promoter id on teams table
        $team->promoter()->associate($promoter)->save();

        $this->updateTeamAffiliateMeta($team, $affiliateId, $promoter, $leadOnFirstPromoter);

        return true;
    }

    /**
     * Log lead creation failure.
     *
     * @param Team   $team
     * @param string $affiliateId
     * @param Promoter $promoter
     */
    private function logLeadCreationFailure(Team $team, string $affiliateId, Promoter $promoter): void
    {
        Log::error('Lead not created on FirstPromoter', [
            'team_id'             => $team->id,
            'team_name'           => $team->name,
            'affiliate_id'        => $affiliateId,
            'promoter_profile_id' => $promoter->profile_id,
        ]);
    }

    /**
     * Update team affiliate meta data.
     *
     * @param Team   $team
     * @param string $affiliateId
     * @param Promoter $promoter
     * @param array  $leadOnFirstPromoter
     */
    private function updateTeamAffiliateMeta(Team $team, string $affiliateId, Promoter $promoter, array $leadOnFirstPromoter): void
    {
        $team->update([
            'meta->affiliate->affiliate_code'      => $affiliateId,
            'meta->affiliate->promoter_profile_id' => $promoter->profile_id,
            'meta->affiliate->lead_id'             => Arr::get($leadOnFirstPromoter, 'lead.id'),
        ]);
    }
}
