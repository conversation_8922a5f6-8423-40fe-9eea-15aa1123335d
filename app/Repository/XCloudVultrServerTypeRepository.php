<?php

namespace App\Repository;

use App\Enums\XcloudBilling\BillingServices;
use App\Models\Product;
use App\Services\CloudServices\Fetchers\Providers\VultrFetcher;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;


class XCloudVultrServerTypeRepository
{
    protected function mergeBySlug($products, $vultrSizes): Collection
    {
        // Group products by type for easier processing.
        $groupedProducts = collect($products->toArray())->groupBy('type');

        // Merge product information with Vultr sizes based on slugs.
        $mergedProductDetails = $groupedProducts->map(function ($productGroup) use ($vultrSizes) {
            return $vultrSizes->map(function ($sizeGroup) use ($productGroup) {
                // Filter matching products based on slug.
                $matchedProducts = array_filter($sizeGroup, fn($size) => in_array($size['slug'], Arr::pluck($productGroup, 'slug')));

                // Merge matching product details.
                return Arr::map($matchedProducts, function ($matchedProduct) use ($productGroup) {
                    $matchingProduct = Arr::first($productGroup, fn($p) => $p['slug'] === $matchedProduct['slug']);
                    $merged = array_merge($matchedProduct, $matchingProduct);

                    // Add backup cost - check for dependent product first
                    if (isset($matchingProduct['dependents']) && !empty($matchingProduct['dependents'])) {
                        // Find backup dependent product
                        $backupProduct = collect($matchingProduct['dependents'])->first(function($dependent) {
                            return isset($dependent['service_type']) &&
                                   in_array($dependent['service_type'], [
                                       BillingServices::BackupXCloudManagedHosting->value,
                                       BillingServices::BackupXCloudProviderHosting->value,
                                       BillingServices::BackupManagedHosting->value
                                   ]);
                        });

                        if ($backupProduct) {
                            $merged['backupCost'] = format_billing($backupProduct['price']);
                        } else {
                            // Fallback to 20% calculation
                            $merged['backupCost'] = format_billing(ceil($merged['price'] * 0.2));
                        }
                    } else {
                        // No dependents, use 20% calculation
                        $merged['backupCost'] = format_billing(ceil($merged['price'] * 0.2));
                    }

                    return $merged;
                });
            })->flatten(1)->toArray();
        });

        return $mergedProductDetails;
    }


    public function getXCloudServerTypes($products): array
    {
        $vultrSizes = collect((new VultrFetcher(config('services.xvultr.token')))->getServerTypes());
        $mergedProductDetails = $this->mergeBySlug($products, $vultrSizes);

        // Extract sizes excluding 'locations' and aggregate region data.
        $sizesWithoutLocations = $mergedProductDetails->map(fn($items) => Arr::map($items, fn($item) => Arr::except($item, 'locations')));
        $regionsBySlug = $mergedProductDetails->flatten(1)->mapWithKeys(fn($item) => [$item['slug'] => $item['locations']])->all();

        // Determine the selected size based on request parameters.
        $selectedSize = $sizesWithoutLocations->flatten(1)->first(fn($item) =>
            $item['id'] == request('product') || Str::lower($item['title']) == Str::lower(request('name'))
        );

        return [
            'sizes' => $sizesWithoutLocations,
            'regions' => $regionsBySlug,
            'selectedSize' => $selectedSize,
        ];

    }

    public function getXCloudUpgradableServerTypes(Collection $products,string $serverId): Collection
    {
        $vultrSizes = collect((new VultrFetcher(config('services.xvultr.token')))->getUpgradableServerTypes($serverId));
        return $this->mergeBySlug($products, $vultrSizes);
    }

    public function getXCloudProductTypesByBillingService(BillingServices $billingService): Collection
    {
        $xcloudPackages = collect(config('services.xvultr.supported_machines'));

        return Product::query()
            ->withCount(['teams' => fn($q) => $q->where(['teams.id' => team()->id])])
//            ->whereIn('slug', $xcloudPackages->flatten(1)->pluck('slug')->toArray()) // Todo: @Arif vai, @Bashar vai need to check if there's dependency on this line
            ->where([
                'is_active' => true,
                'service_type' => $billingService
            ])
            ->with('dependents')
            ->where(fn($q) => $q->whereRequiresBillingPlan(false)
                ->orWhereNull('requires_billing_plan')
                ->orWhere('requires_billing_plan', team()->active_plan_id)
            )
            ->get()
            ->filter(fn($product) => $product->max_purchase_limit == null || $product->max_purchase_limit > $product->teams_count);

    }

    public function getXCloudProductTypesByBillingServiceForWhitelabel(BillingServices $billingService): Collection
    {
        return Product::query()
            ->withCount(['teams' => fn($q) => $q->where(['teams.id' => team()->id])])
            ->where([
                'is_active' => true,
                'service_type' => $billingService,
                'white_label_id' => team()->whiteLabel->id
            ])
            ->where(fn($q) => $q->whereRequiresBillingPlan(false)
                ->orWhereNull('requires_billing_plan')
                ->orWhere('requires_billing_plan', team()->active_plan_id)
            )
            ->get()
            ->filter(fn($product) => $product->max_purchase_limit == null || $product->max_purchase_limit > $product->teams_count);
    }

    public function getWhiteLabelProductTypesByBillingService(BillingServices $billingService): Collection
    {
        $xcloudPackages = collect(config('services.xvultr.supported_machines'));

        return Product::query()
            ->withCount(['teams' => fn($q) => $q->where(['teams.id' => team()->id])])
            ->whereIn('slug', $xcloudPackages->flatten(1)->pluck('slug')->toArray())
            ->where([
                'is_active' => true,
                'service_type' => $billingService,
                'available_for_white_label' => true,
                'white_label_id' => currentWhiteLabel()->id
            ])
            ->where(fn($q) => $q->whereRequiresBillingPlan(false)
                ->orWhereNull('requires_billing_plan')
                ->orWhere('requires_billing_plan', team()->active_plan_id)
            )
            ->get()
            ->filter(fn($product) => $product->max_purchase_limit == null || $product->max_purchase_limit > $product->teams_count);

    }

}
