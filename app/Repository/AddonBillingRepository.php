<?php

namespace App\Repository;

use App\Addons\Enum\MailboxEnums\MailboxPlans;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Models\CartForm;
use App\Models\GeneralInvoice;
use App\Models\Product;
use App\Models\Team;
use App\Traits\Billable;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Stripe\Checkout\Session;
use Stripe\Customer;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class AddonBillingRepository
{
    /**
     * The billable model
     *
     * @var Model
     */
    protected $billableModel;

    /**
     * The team
     *
     * @var Team
     */
    protected $team;

    /**
     * The product
     *
     * @var Product|null
     */
    protected $product;

    /**
     * Create a new repository instance
     *
     * @param Model $billableModel
     * @param Team|null $team
     */
    public function __construct(Model $billableModel, ?Team $team = null)
    {
        if (!in_array(Billable::class, class_uses_recursive($billableModel))) {
            throw new Exception($billableModel::class . ' must use Billable trait');
        }

        $this->billableModel = $billableModel;
        $this->team = $team ?? $billableModel->teamInfo();
    }

    /**
     * Set the product
     *
     * @param Product $product
     * @return $this
     */
    public function setProduct(Product $product)
    {
        $this->product = $product;
        return $this;
    }

    /**
     * Get the product
     *
     * @return Product|null
     */
    public function getProduct()
    {
        if ($this->product) {
            return $this->product;
        }

        // Try to get the product from the model's plan attribute
        if (isset($this->billableModel->plan)) {
            return Product::where('slug', $this->billableModel->plan)->first();
        }

        return null;
    }

    /**
     * Handle payment for the addon
     *
     * @param array $options Additional options for payment processing
     * @return array
     * @throws Exception
     */
    public function handlePayment(array $options = [])
    {
        $product = $this->getProduct();
        $uniqueId = uniqid();
        $paymentMethod = $this->team->activePaymentMethod()->first();

        try {
            // create a cart
            $cart = CartForm::create([
                'model' => $product->service_type->getServiceModel(),
                'email' => $this->team->email,
                'status' => CartFormStatuses::Pending,
                'service' => $product->service_type,
                'product_id' => $product->id
            ]);

            // Get the billing service from the model or use the default
            $billingService = $this->billableModel->getDefaultBillingService();

            // Get the title and description from the model
            $title = $this->billableModel->getBillingTitle($billingService);
            $description = $this->billableModel->getBillingShortDescription($billingService);

            if($product->price === 0){
                // for free mailbox plan, create bill only
                $bill = $this->billableModel->cost($product->price)
                    ->title($title)
                    ->prepaid()
                    ->hasOffer()
                    ->useProduct($product)
                    ->service($billingService)
                    ->description($description)
                    ->renewMonthly()
                    ->generateBill();

                // generate invoice
                $invoice = $this->billableModel->generateInvoice(InvoiceSourceEnum::SinglePurchase);

                $bill->update([
                    'invoice_id' => $invoice->id,
                ]);

                $bill->setStatusPaid();
                $invoice->setStatusPaid();

                // attach product to team
                $this->team->attachProduct($product);

                $cart->invoice()->associate($invoice);
                $cart->save();

                // no need to take payment for free mailbox plan
                $cart->setStatusCompleted();

                return [
                    'sessionId' => $paymentMethod->session_id ?? null,
                    'productId' => $product->id,
                    'affiliateId' => $this->team->getMeta('affiliate.affiliate_code'),
                    'paymentIntentId' => null, // free provider won't have any payment intent
                    'cartId' => $cart->id,
                    'invoiceId' => $invoice->id,
                    'is_paid' => $invoice->status->is(BillingStatus::Paid)
                ];
            }else{
                $cart->setStatusPaymentProcessing();

                Stripe::setApiKey(config('services.stripe.secret_key'));

                // check if customer already exists
                $customer = null;
                if($this->team->stripe_customer_id){
                    $customer = Customer::retrieve($this->team->stripe_customer_id);
                }
                if(!$customer || optional($customer)->deleted){
                    // create new customer
                    $customerData = [
                        'name' => $this->team->owner->name,
                        'email' => $this->team->email,
                        'description' => 'Customer ' . $this->team->name . ' , added when purchasing Mailbox ',
                    ];

                    $customer = Customer::create($customerData);

                    $this->team->update(['stripe_customer_id' => $customer->id]);
                }

                $queryParams = "session_id={CHECKOUT_SESSION_ID}&product_id={$product->id}";

                $session = Session::create([
                    'payment_method_types' => ['card'],
                    'mode' => 'setup',
                    'customer' => $customer->id,
                    'success_url' => route('stripe.product.checkout.success', [], true) . "?{$queryParams}",
                    'cancel_url' =>  route('stripe.product.checkout.cancelled')
                ]);

                $cart->update([
                    'checkout_session_id' => $session->id,
                    'meta->stripe->checkout_session' => [
                        'customer_id' => $customer->id,
                        'checkout_url' => $session->url,
                    ],
                    'team_id' => $this->team->id,
                    'product_id' => $product->id
                ]);

                // generate bill
                $bill = $this->billableModel->cost($product->price)
                    ->title($title)
                    ->prepaid()
                    ->useProduct($product)
                    ->service($billingService)
                    ->description($description)
                    ->renewMonthly()
                    ->generateBill();

                // generate invoice
                $invoice = $this->billableModel->generateInvoice(InvoiceSourceEnum::SinglePurchase);

                $bill->update([
                    'invoice_id' => $invoice->id
                ]);

                if ($this->team->activePlan->support_manual_billing) {
                    return [
                        'sessionId' => $session->id,
                        'productId' => $product->id,
                        'affiliateId' => $this->team->getMeta('affiliate.affiliate_code'),
                        'paymentIntentId' => null,
                        'cartId' => $cart->id,
                        'invoiceId' => $invoice->id,
                        'is_paid' => $invoice->status->is(BillingStatus::Paid)
                    ];
                }

                // take payment
                $paymentIntent = $this->takePayment($invoice, $billingService);

                if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
                    // attach invoice to cart form
                    $cart->invoice()->associate($invoice);
                    $cart->save();

                    $bill->setStatusPaid();
                    // attach product to team
                    $this->team->attachProduct($product);

                    // update the cart
                    $cart->setStatusCompleted();
                    $cart->update([
                        'meta->stripe->checkout_session->payment_method_types' => $session->payment_method_types,
                        'meta->stripe->checkout_session->customer' => $session->customer,
                        'meta->stripe->checkout_session->customer_email' => $session->customer_email,
                    ]);

                    return [
                        'sessionId' => $session->id,
                        'productId' => $product->id,
                        'affiliateId' => $this->team->getMeta('affiliate.affiliate_code'),
                        'paymentIntentId' => $paymentIntent->id,
                        'cartId' => $cart->id,
                        'invoiceId' => $invoice->id,
                        'is_paid' => $invoice->status->is(BillingStatus::Paid)
                    ];

                }else{
                    if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                        $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
                            'requires_3d_secure_authentication' => true
                        ]);

                        $invoice->setStatusRequiresAction();

                        return [
                            'sessionId' => $session->id,
                            'productId' => $product->id,
                            'affiliateId' => $this->team->getMeta('affiliate.affiliate_code'),
                            'paymentIntentId' => $paymentIntent->id,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                            'is_paid' => $invoice->status->is(BillingStatus::Paid)
                        ];
                    }else{
                        $invoice->setStatusPaymentFailed();
                        $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), 'Payment failed');
                        $bill->setStatusUnpaid();

                        return [
                            'sessionId' => $session->id,
                            'productId' => $product->id,
                            'affiliateId' => $this->team->getMeta('affiliate.affiliate_code'),
                            'paymentIntentId' => $paymentIntent->id,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                            'is_paid' => $invoice->status->is(BillingStatus::Paid)
                        ];
                    }
                }
            }
        }catch (Exception $e) {
            return [
                'error' => true,
                'message' => 'Payment Failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Take payment for the invoice
     *
     * @param GeneralInvoice $invoice
     * @param BillingServices $billingService
     * @return PaymentIntent
     */
    private function takePayment(GeneralInvoice $invoice, BillingServices $billingService)
    {
        try {
            $paymentIntent = (new StripePaymentRepository())->takePayment($invoice);

            if ($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED) {
                $invoice->setStatusPaid();

                // send email
                $invoice->team->sendInvoiceEmail($invoice);

                // ensure that service is provided after payment
                if (!$this->billableModel->serviceProvidedAfterPayment($billingService)) {
                    \Log::error('Service not provided after payment for addon: ' . $this->billableModel->id);
                    // provide service
                    $serviceProvided = $this->billableModel->provideService($billingService);
                    if (!$serviceProvided) {
                        \Log::error('Failed to provide service after trying again for addon: ' . $this->billableModel->id);
                    }
                }
            } else {
                if ($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION) {
                    $invoice->setStatusRequiresAction();
                } else {
                    $invoice->setStatusPaymentFailed();
                    $invoice->saveLog('payment_error', $paymentIntent->last_payment_error?->message);
                    \Log::warning('Payment failed for invoice: ' . $invoice->id . ' with error: ' . $paymentIntent->last_payment_error?->message);
                }
            }

            return $paymentIntent;
        } catch (Exception $e) {
            $invoice->setStatusPaymentFailed();
            $invoice->saveLog('payment_error', $e->getMessage());

            $paymentIntent = PaymentIntent::retrieve($invoice->gateway_invoice_or_intent_id);
            $paymentIntent->cancel();
            return $paymentIntent;
        }
    }
}
