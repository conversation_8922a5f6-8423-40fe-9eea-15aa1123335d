<?php

namespace App\Repository;

use App\Addons\Enum\MailboxEnums\MailboxPlans;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Models\Addons\EmailAccount;
use App\Models\CartForm;
use App\Models\GeneralInvoice;
use App\Models\Product;
use App\Models\Team;
use Exception;
use Illuminate\Support\Arr;
use Stripe\Checkout\Session;
use Stripe\Customer;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class MailboxBillingRepository
{
    public function __construct(public EmailAccount $emailAccount)
    {
    }

    /**
     * Handle payment for the mailbox addon
     * 
     * @return array
     * @throws Exception
     */
    public function handlePayment()
    {
        $product = Product::where('slug', $this->emailAccount->plan)->first();
        $uniqueId = uniqid();
        $paymentMethod = $this->emailAccount->team->activePaymentMethod()->first();

        if(!$product) {
            throw new Exception('No product found for this mailbox plan');
        }

        try {
            // create a cart
            $cart = CartForm::create([
                'model' => $product->service_type->getServiceModel(),
                'email'=> $this->emailAccount->team->email,
                'status'=> CartFormStatuses::Pending,
                'service' => $product->service_type,
                'product_id' => $product->id
            ]);

            if($product->price === 0){
                // for free mailbox plan, create bill only
                $bill = $this->emailAccount->cost(0)
                    ->title(MailboxPlans::getPlanName($this->emailAccount->plan->value))
                    ->prepaid()
                    ->hasOffer()
                    ->useProduct($product)
                    ->service(BillingServices::Mailbox)
                    ->description(MailboxPlans::getPlanName($this->emailAccount->plan->value))
                    ->renewMonthly()
                    ->generateBill();

                // generate invoice
                $invoice = $this->emailAccount->generateInvoice(InvoiceSourceEnum::SinglePurchase);

                $bill->update([
                    'invoice_id' => $invoice->id,
                ]);

                $bill->setStatusPaid();
                $invoice->setStatusPaid();

                // attach product to team
                $this->emailAccount->team->attachProduct($product);

                $cart->invoice()->associate($invoice);
                $cart->save();

                // no need to take payment for free mailbox plan
                $cart->setStatusCompleted();

                return [
                    'sessionId' => $paymentMethod->session_id ?? null,
                    'productId' => $product->id,
                    'affiliateId' => $this->emailAccount->mailboxDomain->team->getMeta('affiliate.affiliate_code'),
                    'paymentIntentId' => null, // free provider won't have any payment intent
                    'cartId' => $cart->id,
                    'invoiceId' => $invoice->id,
                ];
            }else{
                $cart->setStatusPaymentProcessing();

                Stripe::setApiKey(config('services.stripe.secret_key'));

                // check if customer already exists
                $customer = null;
                if($this->emailAccount->team->stripe_customer_id){
                    $customer = Customer::retrieve($this->emailAccount->team->stripe_customer_id);
                }
                if(!$customer || optional($customer)->deleted){
                    // create new customer
                    $customerData = [
                        'name' => $this->emailAccount->team->owner->name,
                        'email' => $this->emailAccount->team->email,
                        'description' => 'Customer ' . $this->emailAccount->team->name . ' , added when purchasing Mailbox ',
                    ];

                    $customer = Customer::create($customerData);

                    $this->emailAccount->team->update(['stripe_customer_id' => $customer->id]);
                }

                $queryParams = "session_id={CHECKOUT_SESSION_ID}&product_id={$product->id}";

                $session = Session::create([
                    'payment_method_types' => ['card'],
                    'mode' => 'setup',
                    'customer' => $customer->id,
                    'success_url' => route('stripe.product.checkout.success', [], true) . "?{$queryParams}",
                    'cancel_url' =>  route('stripe.product.checkout.cancelled')
                ]);

                $cart->update([
                    'checkout_session_id' => $session->id,
                    'meta->stripe->checkout_session' => [
                        'customer_id' => $customer->id,
                        'checkout_url' => $session->url,
                    ],
                    'team_id' => $this->emailAccount->team->id,
                    'product_id' => $product->id
                ]);

                // generate bill
                $bill = $this->emailAccount->cost($product->price)
                    ->title(MailboxPlans::getPlanName($this->emailAccount->plan->value))
                    ->prepaid()
                    ->useProduct($product)
                    ->service(BillingServices::Mailbox)
                    ->description(MailboxPlans::getPlanName($this->emailAccount->plan->value))
                    ->renewMonthly()
                    ->generateBill();

                // generate invoice
                $invoice = $this->emailAccount->generateInvoice(InvoiceSourceEnum::SinglePurchase);

                $bill->update([
                    'invoice_id' => $invoice->id
                ]);

                if ($this->emailAccount->team->activePlan->support_manual_billing) {
                    return [
                        'sessionId' => $session->id,
                        'productId' => $product->id,
                        'affiliateId' => $this->emailAccount->team->getMeta('affiliate.affiliate_code'),
                        'paymentIntentId' => null,
                        'cartId' => $cart->id,
                        'invoiceId' => $invoice->id,
                    ];
                }

                // take payment
                $paymentIntent = $this->takePayment($invoice);

                if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
                    // attach invoice to cart form
                    $cart->invoice()->associate($invoice);
                    $cart->save();

                    $bill->setStatusPaid();
                    // attach product to team
                    $this->emailAccount->team->attachProduct($product);

                    // update the cart
                    $cart->setStatusCompleted();
                    $cart->update([
                        'meta->stripe->checkout_session->payment_method_types' => $session->payment_method_types,
                        'meta->stripe->checkout_session->customer' => $session->customer,
                        'meta->stripe->checkout_session->customer_email' => $session->customer_email,
                    ]);

                    return [
                        'sessionId' => $session->id,
                        'productId' => $product->id,
                        'affiliateId' => $this->emailAccount->team->getMeta('affiliate.affiliate_code'),
                        'paymentIntentId' => $paymentIntent->id,
                        'cartId' => $cart->id,
                        'invoiceId' => $invoice->id,
                    ];

                }else{
                    if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                        $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
                            'requires_3d_secure_authentication' => true
                        ]);

                        $invoice->setStatusRequiresAction();

                        return [
                            'sessionId' => $session->id,
                            'productId' => $product->id,
                            'affiliateId' => $this->emailAccount->team->getMeta('affiliate.affiliate_code'),
                            'paymentIntentId' => $paymentIntent->id,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                        ];
                    }else{
                        $invoice->setStatusPaymentFailed();
                        $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), 'Payment failed');
                        $bill->setStatusUnpaid();

                        return [
                            'sessionId' => $session->id,
                            'productId' => $product->id,
                            'affiliateId' => $this->emailAccount->team->getMeta('affiliate.affiliate_code'),
                            'paymentIntentId' => $paymentIntent->id,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                        ];
                    }
                }
            }
        }catch (Exception $e) {
            dd($e->getMessage());
            return [
                'error' => true,
                'message' => 'Payment Failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Take payment for the invoice
     * 
     * @param GeneralInvoice $invoice
     * @return PaymentIntent
     */
    private function takePayment(GeneralInvoice $invoice)
    {
        try {
            $paymentIntent = (new StripePaymentRepository())->takePayment($invoice);

            if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
                $invoice->setStatusPaid();

                // send email
                $invoice->team->sendInvoiceEmail($invoice);

                // ensure that service is provided after payment
                if(!$this->emailAccount->serviceProvidedAfterPayment(BillingServices::EmailProvider)){
                    \Log::error('Service not provided after payment for mailbox: ' . $this->emailAccount->id);
                    // provide service
                    $serviceProvided = $this->emailAccount->provideService(BillingServices::EmailProvider);
                    if(!$serviceProvided){
                        \Log::error('Failed to provide service after trying again for mailbox: ' . $this->emailAccount->id);
                    }
                }
            }else{
                if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                    $invoice->setStatusRequiresAction();
                }else{
                    $invoice->setStatusPaymentFailed();
                    $invoice->saveLog('payment_error', $paymentIntent->last_payment_error?->message);
                    \Log::warning('Payment failed for invoice: ' . $invoice->id . ' with error: ' . $paymentIntent->last_payment_error?->message);
                }
            }

            return $paymentIntent;
        }catch (Exception $e) {
            $invoice->setStatusPaymentFailed();
            $invoice->saveLog('payment_error', $e->getMessage());

            $paymentIntent = PaymentIntent::retrieve($invoice->gateway_invoice_or_intent_id);
            $paymentIntent->cancel();
            return $paymentIntent;
        }
    }
}
