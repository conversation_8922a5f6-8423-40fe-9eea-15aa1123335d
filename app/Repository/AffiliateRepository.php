<?php

namespace App\Repository;

use App\Enums\PromoterStatusEnum;
use App\Enums\XcloudBilling\BillingStatus;
use App\Models\GeneralInvoice;
use App\Models\Promoter;
use Arr;
use GuzzleHttp\Exception\GuzzleException;
use Log;

class AffiliateRepository
{
    public function __construct(public $invoice)
    {
    }

    /**
     * @throws GuzzleException
     */
    public function generateAffiliateCommission(): void
    {
        $affiliateId = $this->invoice->team->promoter->default_ref_id ?? $this->invoice->team->getMeta('affiliate->affiliate_code');
        $leadId = $this->invoice->team->getMeta('affiliate->lead_id');

        // handle affiliate already generated
        if($this->affiliateGeneratedOnInvoice()) {
            Log::warning('Affiliate commission already generated', [
                'team_id' => $this->invoice->team->id,
                'team_name' => $this->invoice->team->name,
                'promoter_affiliate_id' => $this->invoice->team->promoter->default_ref_id,
                'affiliate_id' => $affiliateId,
                'lead_id' => $leadId,
            ]);
            return; // already handled
        }

        // handle inactive promoter
        if(!$this->invoice->team->promoter->isActive()){
            Log::warning('Promoter is not active', [
                'team_id' => $this->invoice->team->id,
                'team_name' => $this->invoice->team->name,
                'promoter_id' => $this->invoice->team->promoter->id,
                'promoter_name' => $this->invoice->team->promoter->name,
                'promoter_affiliate_id' => $this->invoice->team->promoter->default_ref_id,
            ]);

            $this->invoice->team->promoter->saveMeta('promoter->log', [
                'message' => 'Promoter is not active',
                'team_id' => $this->invoice->team->id,
                'promoter_affiliate_id' => $this->invoice->team->promoter->default_ref_id,
            ]);

            $this->invoice->team->update([
                'meta->affiliate->affiliate_expired' => true
            ]);
            return;
        }


        // handle expired affiliate
        if($this->invoice->team->affiliateExpired()){
            Log::warning('Affiliate Expired', [
                'team_id' => $this->invoice->team->id,
                'team_name' => $this->invoice->team->name,
                'promoter_affiliate_id' => $this->invoice->team->promoter->default_ref_id,
                'affiliate_expiry_at' => $this->invoice->team->affiliate_expiry_at,
                'current_date' => now()->format('Y-m-d H:i:s')
            ]);

            $this->invoice->team->promoter->saveMeta('promoter->log', [
                'message' => 'Affiliate Expired',
                'team_id' => $this->invoice->team->id,
                'promoter_affiliate_id' => $this->invoice->team->promoter->default_ref_id,
                'affiliate_expiry_at' => $this->invoice->team->affiliate_expiry_at,
                'current_date' => now()->format('Y-m-d H:i:s')
            ]);

            $this->invoice->team->update([
                'meta->affiliate->affiliate_expired' => true
            ]);
            return;
        }

        $promoter = Promoter::where('default_ref_id', $affiliateId)->first();

        if(!$promoter){
            Log::error('Promoter not found', [
                'team_id' => $this->invoice->team->id,
                'team_name' => $this->invoice->team->name,
                'promoter_affiliate_id' => $this->invoice->team->promoter->default_ref_id,
                'affiliate_id' => $affiliateId,
                'lead_id' => $leadId,
            ]);

            return;
        }

        // check if lead is active
        $leadFromFirstPromoter = (new FirstPromoterRepository())->getLead([
            'id' => $leadId
        ]);

        if(!$promoter->isActive() || empty($leadFromFirstPromoter)){
            // deactivate affiliate
            $affiliateLog = $promoter->getMeta('affiliate->log') ?? [];
            $newLogEntry = [
                'message' => 'Affiliate is not active',
                'lead_id' => $leadId,
            ];

            $updatedLog = Arr::prepend($affiliateLog, $newLogEntry);

            $promoter->update([
                'status' => PromoterStatusEnum::Inactive,
                'meta->affiliate->log' => $updatedLog
            ]);

            Log::warning('Promoter is not active', [
                'promoter' => $promoter->toArray(),
                'team_id' => $this->invoice->team->id,
                'team_name' => $this->invoice->team->name,
                'lead_id' => $leadId,
            ]);

            return;
        }

        $salesData = [
            'email' => Arr::get($leadFromFirstPromoter, 'email'),
            'event_id' =>  $this->invoice->reference_no . ':' .  GeneralInvoice::class,
            'amount' => $this->invoice->amount * 100, // amount in cents
        ];

        $promoterCommission = $promoter->promoterCommissions()->create([
            'title' => Arr::get($leadFromFirstPromoter, 'promotion.current_referral_reward.name') . ' on ' . Arr::get($leadFromFirstPromoter, 'email'),
            'invoice_id' => $this->invoice->id,
            'purchase_amount' => $this->invoice->amount,
            'commission_amount' => $this->invoice->amount * ((float)Arr::get($leadFromFirstPromoter, 'promotion.current_referral_reward.per_of_sale') / 100), // 20% commission
            'commission_percentage' => Arr::get($leadFromFirstPromoter, 'promotion.current_referral_reward.per_of_sale'),
            'campaign_id' => Arr::get($leadFromFirstPromoter, 'promotion.campaign_id'),
        ]);

        // generate sales on first promoter
        $response = (new FirstPromoterRepository())->trackingSalesAndCommission($salesData);
        if(empty($response)){
            $affiliateLog = $promoter->getMeta('affiliate->log') ?? [];
            $newLogEntry = [
                'message' => 'failed to generate sales on first promoter',
                'response' => $response,
            ];

            $updatedLog = Arr::prepend($affiliateLog, $newLogEntry);

            $promoter->update([
                'meta->affiliate->log' => $updatedLog
            ]);

            $promoterCommission->update([
                'billing_status' => BillingStatus::Failed
            ]);

            Log::error('failed to generate sales on first promoter', [
                'promoter' => $promoter->id,
                'team_id' => $this->invoice->team->id,
                'team_name' => $this->invoice->team->name,
                'lead_id' => $leadId,
                'response' => $response,
                'promo_commission' => $promoterCommission->id
            ]);

            return;
        }

        $promoterCommission->update([
            'billing_status' => BillingStatus::Paid
        ]);

        Log::info('Sales generated on first promoter', [
            'promoter' => $promoter->id,
            'team_id' => $this->invoice->team->id,
            'team_name' => $this->invoice->team->name,
            'lead_id' => $leadId,
            'response' => $response,
            'promo_commission' => $promoterCommission->id
        ]);

        // attach promoter to invoice
        $this->invoice->update([
            'promoter_id' => $promoter->id
        ]);

        // after successful affiliate commission, clear the affiliate error if any
        $promoter->update([
            'meta->affiliate->log' => []
        ]);
    }

    private function affiliateGeneratedOnInvoice()
    {
        return $this->invoice->promoter_id;
    }
}
