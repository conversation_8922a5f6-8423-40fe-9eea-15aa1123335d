<?php

namespace App\Providers\CloudProviders;

use App\Enums\CloudProviderEnums;
use InvalidArgumentException;

class OAuthProviderFactory
{
    public function getProvider(CloudProviderEnums $provider): OAuthProvider
    {

        return match ($provider) {
            CloudProviderEnums::DIGITALOCEAN => new DigitalOceanOAuthProvider(
                config('services.digitalocean.client_id'),
                config('services.digitalocean.client_secret'),
                route('oauth.callback', ['provider' => $provider->value]),
                ['read', 'write']
            ),
            CloudProviderEnums::GCP => new GoogleOAuthProvider(
                config('services.gcp.client_id'),
                config('services.gcp.client_secret'),
                route('oauth.callback', ['provider' => $provider->value]),
                [
                    'https://www.googleapis.com/auth/cloudplatformprojects.readonly',
                    'https://www.googleapis.com/auth/compute',
                    'https://www.googleapis.com/auth/sqlservice.admin'
                ]
            ),
            CloudProviderEnums::LINODE => new LinodeOAuthProvider(
                config('services.linode.client_id'),
                config('services.linode.client_secret'),
                route('oauth.callback', ['provider' => $provider->value]),
                ['linodes:read_write']
            ),

            default => throw new InvalidArgumentException("Unsupported provider: {$provider->value}"),
        };
    }
}
