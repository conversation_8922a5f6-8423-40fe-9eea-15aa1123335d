<?php

namespace App\Providers\CloudProviders;

use App\Enums\CloudProviderEnums as EnumsCloudProvider;
use App\Models\CloudProvider;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class LinodeOAuthProvider extends OAuthProvider
{
    protected string $baseUrl = 'https://login.linode.com/oauth/';


    public function saveDetails($infoWithAccessToken): Model|CloudProvider
    {
        return team()->cloudProviders()->create(
            [
                'name' => 'Linode_' . Str::random(),
                'user_id' => auth()->user()->id,
                'provider' => EnumsCloudProvider::LINODE->value,
                'access_token' => Arr::get($infoWithAccessToken, 'access_token'),
                'refresh_token' => Arr::get($infoWithAccessToken, 'refresh_token'),
                'token_expires_at' => now()->addSeconds(Arr::get($infoWithAccessToken, 'expires_in')),
            ]
        );
    }

    public function updateDetails($refreshToken, mixed $infoWithAccessToken, ?CloudProvider $cloudProvider): void
    {
        if(!$cloudProvider){
            // Find the existing cloud provider record by refresh_token
            $cloudProvider = team()->cloudProviders()
                ->where('provider', EnumsCloudProvider::LINODE->value)
                ->where('refresh_token', $refreshToken)
                ->first();
        }

        // If the cloud provider record is found, update it
        if ($cloudProvider) {
            $cloudProvider->update([
                'access_token' => $infoWithAccessToken['access_token'],
                'refresh_token' => Arr::get($infoWithAccessToken, 'refresh_token') ?: $cloudProvider->refresh_token,
                'token_expires_at' => now()->addSeconds($infoWithAccessToken['expires_in']),
            ]);
        } else {
            // If the cloud provider record is not found, create a new one
            $this->saveDetails($infoWithAccessToken);
        }
    }


    public function deleteDetails($accessToken, ?CloudProvider $cloudProvider): void
    {
        if(!$cloudProvider){
            $cloudProvider = team()->cloudProviders()
                ->where('provider', EnumsCloudProvider::LINODE->value)
                ->where('refresh_token', $accessToken)
                ->first();
        }

        // If the cloud provider record is found, delete it
        $cloudProvider?->delete();
    }

    /**
     * @throws Exception
     */
    public function checkAuthTokenAndRefresh(Exception $exception, CloudProvider $cloudProvider): void
    {
        if ($exception->getCode() === 401 && !empty($cloudProvider->refresh_token)) {
            $infoWithAccessToken = $this->refreshToken($cloudProvider->refresh_token);

            // Check if there was an error in the response
            if (isset($infoWithAccessToken['error']) && $infoWithAccessToken['error'] === 'invalid_grant') {
                throw new Exception("Invalid Refresh Token");
            }

            // Update details if no error is present
            $this->updateDetails($cloudProvider->refresh_token, $infoWithAccessToken, $cloudProvider);
        }

    }
}
