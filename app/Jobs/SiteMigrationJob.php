<?php

namespace App\Jobs;


use App\Jobs\Site\HandleSiteIndexingJob;
use App\Jobs\Site\InstallDatabase;
use App\Jobs\Site\InstallPhpVersion;
use App\Jobs\Site\InstallSiteDirectory;
use App\Jobs\Site\MigrationEligibilityCheck;
use App\Jobs\Site\RemoveWPDebugLog;
use App\Jobs\Site\SetupMigrationClient;
use App\Models\Site;
use App\Models\AutoSiteMigration;
use App\Services\Migration\SiteMigrating;
use App\Traits\SiteMigrationDispatcher;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SiteMigrationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, SiteMigrationDispatcher;

    private Site $site;

    public function __construct(public AutoSiteMigration $siteMigration)
    {
        $this->site = $siteMigration->site;
    }

    public function handle()
    {
        $this->siteMigration->setStatusProcessing();

        $jobs = [
            SiteMigrating::CHECKING_STATUS => new MigrationEligibilityCheck($this->site),
            SiteMigrating::INSTALLING_PHP => new InstallPhpVersion($this->site),
            SiteMigrating::INSTALLING_DATABASE => new InstallDatabase($this->site),
            SiteMigrating::INSTALLING_SITE => new InstallSiteDirectory($this->site),
            SiteMigrating::SCANING_FILESYSTEM => new SetupMigrationClient($this->site),
            SiteMigrating::REMOVE_DEBUG_LOG => new RemoveWPDebugLog($this->site),
            SiteMigrating::HANDLE_SITE_INDEXING => new HandleSiteIndexingJob($this->site),
        ];

        $this->dispatchSiteMigrationChainJob($jobs);
    }
}
