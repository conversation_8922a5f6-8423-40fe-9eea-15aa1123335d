<?php

namespace App\Jobs;

use App\Enums\EmailProvider;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Models\Product;
use App\Models\Site;
use App\Scripts\Site\SettingUpXcloudEmailProviderScript;
use Arr;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SettingUpDefaultXcloudEmailProviderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Site $site)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if($this->site->getMeta('useXcloudDefaultEmailProvider')) {
            $nameFromEmail = explode('@', $this->site->team()->email)[0];
            if (!$this->site->team()->hasAvailedFreeXcloudEmailProviderPlan()) {

                if(app()->environment('production')){
                    $subaccountEmail = 'team_' . $this->site->team()->id . '@' . 'xcloud.email';
                } else {
                    $subaccountEmail = env('APP_ENV') .  '_team_' . $this->site->team()->id . '@' . 'xcloud.email';
                }

//                $subaccountEmail = 'team_' . $this->site->team()->id . '@' . 'xcloud.email';
                // create subaccount on elastic email
                $this->site->team()->createElasticEmailSubAccount($subaccountEmail, credit: 100);

                $emailProvider = \App\Models\EmailProvider::firstOrCreate(
                    [
                        'provider' => EmailProvider::XCLOUD_EMAIL_PROVIDER,
                        'username' => $nameFromEmail . '@' . 'xcloud.email',
                        'team_id' => $this->site->team()->id,
                        'plan' => xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS,
                    ],
                    [
                        'domain' => app()->environment('production')
                            ? config('services.elastic_email.production_domain')
                            : config('services.elastic_email.staging_domain'),
                        'provider_type' => EmailProvider::ELASTIC_EMAIL->value,
                        'api_key' => $this->site->team()->getElasticEmailSubAccountApiKey(),
                        'email_limit' => xCloudEmailProviderPlanEnum::getPlanLimit(xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS->value),
                        'price' => xCloudEmailProviderPlanEnum::getPlanPrice(xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS->value),
                        'label' => 'xCloud Free ' . xCloudEmailProviderPlanEnum::getPlanLimit(xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS->value) . ' Emails',
                    ]
                );

                // generate free bill
                $product = Product::where('slug', $emailProvider->plan)->first();

                $bill = $emailProvider->cost(0)
                    ->title(BillingServices::EmailProvider->toReadableSentence())
                    ->prepaid()
                    ->hasOffer()
                    ->useProduct($product)
                    ->service(BillingServices::EmailProvider)
                    ->description(xCloudEmailProviderPlanEnum::getFreePlanName()[$emailProvider->plan->value])
                    ->renewMonthly()
                    ->generateBill();
            } else {
                // User availed free plan. Just need to install fluent smtp plugin on user's site using free xcloud plan
                $emailProvider = $this->site->team()->emailProviders()
                    ->whereProvider(\App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER)
                    ->where('plan', xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS)
                    ->first();
            }

            // attach email provider to site
            $this->site->update([
                'email_provider_id' => $emailProvider->id,
                'email_provider_data' => [
                    'domain' => app()->environment('production')
                        ? config('services.elastic_email.production_domain')
                        : config('services.elastic_email.staging_domain'),
                    'from_name' => $this->site->team()->name,
                    'from_email' => $nameFromEmail . '@' . 'xcloud.email',
                    'xcloud_provider_email' => $nameFromEmail . '@' . 'xcloud.email'
                ]
            ]);

            // install fluent smtp plugin on user's site
            // configure fluent smtp plugin with elastic email credentials
            $this->site->server->runInline(
                new SettingUpXcloudEmailProviderScript(
                    site: $this->site,
                    server: $this->site->server,
                    emailProvider: $emailProvider,
                    providerData: [
                        'domain' => app()->environment('production')
                            ? config('services.elastic_email.production_domain')
                            : config('services.elastic_email.staging_domain'),
                        'api_key' =>  $this->site->team()->getElasticEmailSubAccountApiKey(),
                        'from_name' => $this->site->team()->name,
                        'from_email' => $nameFromEmail . '@' . 'xcloud.email',
                    ]
                ),
            );
        }
    }
}
