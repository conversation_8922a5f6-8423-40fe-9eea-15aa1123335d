<?php

namespace App\Jobs;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\Bill;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateBillsMetaOnModelDeleted implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $model;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($model)
    {
        $this->model = $model;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $bills = Bill::where('generator_id', $this->model->id)
                        ->where('generator_type', get_class($this->model))
                        ->orderBy('id', 'desc')
                        ->get();

        if (!$bills) {
            return;
        }

        foreach ($bills as $bill) {

            $refundableAmount = 0;

            if ($bill->service && $bill->renewal_period) {
                $refundableAmount = $this->model->getRefundableAmount($bill->service, $bill->renewal_period);
            }

            $title = $bill->title;
            $description = $bill?->description;

            $description .= ' # Next billing date was '. ($bill?->next_billing_date?->toDateString() ?: 'not set');
            $description .= ' # Renewal period was '. ($bill?->renewal_period?->value ?: 'not set');

            $updated = $bill->update([
                'title' => $title,
                'description' => $description,
                'service_is_active' => false,
                'service_deactivated_from' => now(),
                'refundable_amount' => $refundableAmount,
                'next_billing_date' => null,
            ]);


            if ($bill->status !== BillingStatus::Paid && $bill?->invoice && ($bill?->invoice?->status !== BillingStatus::Paid)) {
                $bill->invoice->update([
                    'status' => BillingStatus::Cancelled,
                ]);
            }

            if (!$updated) {
                throw new Exception('Failed to update bill meta for bill #'. $bill->id .' of '. get_class($this->model) .' #'. $this->model->id );
            }
        }
    }
}
