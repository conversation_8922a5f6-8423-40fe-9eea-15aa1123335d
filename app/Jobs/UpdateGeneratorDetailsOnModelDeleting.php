<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateGeneratorDetailsOnModelDeleting implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $model;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($model)
    {
        $this->model = $model;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $bills = $this->model->bills()->orderBy('id', 'desc')->get();

        foreach ($bills as $bill) {

            if (!$bill?->generator) {
                continue;
            }

            $bill->update([
                'meta->generator_details' => [
                    'name' => $bill->generator->getBillingName($bill->service),
                    'title' => $bill->getTitleForInvoice,
                    'short_description_title' => $bill->generator->getBillingShortDescriptionTitle($bill->service),
                    'short_description' => $bill->generator->getBillingShortDescription($bill->service),
                ]
            ]);
        }
    }
}
