<?php

namespace App\Jobs\Addons;

use App\Addons\AddonsManager;
use App\Addons\Enum\AddonTypeEnum;
use App\Models\AddonStorageProvider;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessStorageProviderPaymentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param AddonStorageProvider $addon The addon storage provider to process payment for
     */
    public function __construct(public AddonStorageProvider $addon)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $addonManager = app(AddonsManager::class)->resolve(AddonTypeEnum::STORAGE_PROVIDER);

            $response = $addonManager->handlePayment([
                'model_id' => $this->addon->id,
                'team_id' => $this->addon->team_id,
            ]);

            // Check if payment was successful
            if (!isset($response['is_paid']) || !$response['is_paid']) {
                throw new \Exception('Payment failed for storage provider');
            }

            // Enable the storage provider
            $this->addon->update(['is_enabled' => true]);

            Log::info('Storage provider payment processed successfully', [
                'addon_id' => $this->addon->id,
                'team_id' => $this->addon->team_id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process payment for storage provider', [
                'addon_id' => $this->addon->id,
                'team_id' => $this->addon->team_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }
}
