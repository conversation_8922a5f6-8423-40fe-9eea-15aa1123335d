<?php

namespace App\Jobs;

use App\Events\DomainChallengeStatusChanged;
use App\Events\SiteSSLStatusChanged;
use App\Models\DomainChallenge;
use App\Models\SslCertificate;
use App\Scripts\Site\GetCertificateDetails;
use Arr;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;

class FinishACMEChallengeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 1200;

    const MAX_ATTEMPTS = 3;

    public function __construct(public DomainChallenge $domainChallenge)
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->domainChallenge->markAsVerifying();

        $output = $this->domainChallenge->site?->runInline(new GetCertificateDetails(domainChallenge: $this->domainChallenge))->output;

        if (empty($output) || !str_contains($output, $this->domainChallenge->sslCertificate->getCertificatePath())) {
            // dispatch this job again 3 more times
            if ($this->attempts() < self::MAX_ATTEMPTS) {
                $this->release(60);
                return;
            }


            $this->domainChallenge->markAsFailed();
            # mark ssl certificate as failed
            $this->domainChallenge->sslCertificate->markAsFailed();

            DomainChallengeStatusChanged::dispatch($this->domainChallenge);
            SiteSSLStatusChanged::dispatch($this->domainChallenge->sslCertificate);

            return;
        }

        preg_match("/Expiry Date: (?<date>.*)\(/", $output, $match);

        # mark ssl_certificate as obtained
        $this->domainChallenge->sslCertificate->update([
            'status' => SslCertificate::STATUS_OBTAINED,
            'obtained_from' => "Let's Encrypt",
            'expires_at' => Arr::get($match, 'date') ?: null,
        ]);

        # reload nginx config
        $this->domainChallenge->site?->regenerateNginxConf();

        # mark domainChallenge as completed
        $this->domainChallenge->markAsCompleted();

        DomainChallengeStatusChanged::dispatch($this->domainChallenge);
        SiteSSLStatusChanged::dispatch($this->domainChallenge->sslCertificate);
    }

    /**
     * The job failed to process.
     *
     * @param $exception
     * @return void
     * @throws Exception
     */
    public function failed($exception): void
    {
        $this->domainChallenge->markAsFailed();

        if($this->domainChallenge?->sslCertificate){
            $this->domainChallenge->sslCertificate?->markAsFailed();
            SiteSSLStatusChanged::dispatch($this->domainChallenge->sslCertificate);
        }

        $this->domainChallenge->saveMeta('error', $exception->getMessage());

        DomainChallengeStatusChanged::dispatch($this->domainChallenge);

        Log::error('Failed to finish ACME challenge', [
            'domain_challenge_id' => $this->domainChallenge->id,
            'exception' => $exception->getMessage(),
        ]);
    }
}
