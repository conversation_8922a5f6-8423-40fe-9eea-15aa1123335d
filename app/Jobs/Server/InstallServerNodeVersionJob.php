<?php

namespace App\Jobs\Server;

use App\Models\NodeVersion;
use App\Models\Server;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstallServerNodeVersionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(private readonly Server $server, private readonly string $nodeVersion, private readonly bool $setDefault = false)
    {
    }

    public function handle(): void
    {
        $this->server->nodeManager()->install($this->nodeVersion, $this->setDefault);
    }
}
