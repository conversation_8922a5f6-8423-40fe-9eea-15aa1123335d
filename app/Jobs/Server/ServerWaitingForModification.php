<?php

namespace App\Jobs\Server;

use App\Enums\CloudProviderEnums;
use App\Enums\ServerStatus;
use App\Models\Server;
use App\Services\CloudServices\Fetchers\FetcherFactory;
use App\Services\Server\Api\ServerModification\ServerModificationControllerInterface;
use App\Traits\HandlesServerProvisioningError;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class ServerWaitingForModification implements ShouldQueue
{
    // All server modification jobs that need background process should use this
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, HandlesServerProvisioningError;

    public int $tries = 30;

    protected Server $server;
    protected string $credential;
    protected ServerModificationControllerInterface $action;

    protected function __construct(Server $server, string $credential, ServerModificationControllerInterface $action)
    {
        $this->server = $server;
        $this->credential = $credential;
        $this->action = $action;
    }

    /**
     * @throws Throwable
     */
    public function handle(): void
    {
        // Determine the appropriate fetcher based on your criteria
        $providerService = FetcherFactory::getService(
            cloudProviderEnum: CloudProviderEnums::tryFrom($this->server->cloudProvider->provider->value),
            credential: $this->credential
        );

        if ($providerService->isInstanceRunning($this->server?->resource_id)) {
            // if the instance is running again, then the modification is successful
            $this->action->onSuccessWork();
            $this->server->update(['status' => ServerStatus::PROVISIONED]);
            $this->server->log('Successfully done modifying the instance on function call: ' . $this->action->getFunctionName() . ' and instance id: ' . $this->server->resource_id);
        } else {
            $this->server->log('ServerWaitingForModification: error 1: retry after 1 minute, for ' . $this->tries . ' times.');
            $this->release(now()->addMinute()); // retry after 1 minute, for 30 times
        }
    }

    public function failed(Throwable $e): void
    {
        report($e);

        // Call the onFailedWork method from the action to handle specific failure logic
        $serverStatus = $this->action->onFailedWork();

        // Update the server's status to MODIFIED_FAILED
        $this->server->update(['status' => $serverStatus]);

        // Optionally, log the failure and exception details
        $this->server->log("Modification failed for Server ID: {$this->server->id}: {$e->getMessage()}");
    }
}
