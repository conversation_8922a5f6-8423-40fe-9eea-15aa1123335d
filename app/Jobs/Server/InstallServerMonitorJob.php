<?php

namespace App\Jobs\Server;

use App\Models\Server;
use App\Models\ServerMonitor;
use App\Models\Site;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstallServerMonitorJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 600;

    public function __construct(public Server $server, public int $interval = 60, public bool $withSite = false)
    {
    }

    public function handle(): void
    {
        $this->server->log('Installing monitoring to server: '.$this->server->id.' : '.$this->server->name);

        $this->server->installMonitoring($this->interval);

        if ($this->withSite) {
            $this->server->sites()->each(function (Site $site) {
                $site->log('Installing monitoring to site: '.$site->id.' : '.$site->name);
                $site->installMonitoring();
            });
        }
    }
}
