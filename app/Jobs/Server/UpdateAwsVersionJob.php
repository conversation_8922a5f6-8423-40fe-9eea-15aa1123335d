<?php

namespace App\Jobs\Server;

use App\Enums\SiteStatus;
use App\Models\Server;
use App\Models\Site;
use App\Scripts\Server\UpdateAwsToV2;
use App\Services\PHP\PHPVersionManager;
use App\Traits\MigrationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateAwsVersionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MigrationHelper;


    public function __construct(public Server $server)
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->server->runInBackground(new UpdateAwsToV2(server: $this->server));
    }
}
