<?php

namespace App\Jobs\Migration;

use App\Events\SiteProvisioningStatusChanged;
use App\Models\Site;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MigrationEligibilityCheck implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    /**
     * @param Site $site
     * @param int|float $requiredSpace in bytes
     *
     * @return void
     */
    public function __construct(public Site $site, public int|float $requiredSpace = 0)
    {

    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        dump('EligibilityCheck');

        SiteProvisioningStatusChanged::dispatch($this->site);

        // throw new Exception("Database provider {$this->site->database_provider} is not supported at the moment.");

        // [0/8] Checking eligibility
        // - access
        // - space
        // - unique

        if (!$this->site->server->testSshConnection()->is_connected) {
            throw new Exception('Failed to establish connection to the server.');
        }
        if ($this->requiredSpace > 0) {
            #add additional 1 GB to the required space
            $minimumSpace = $this->requiredSpace + (1024 * 1024 * 1024);
            $serverSpace = $this->site->server->getFreeSpaceInByte();
            if ( $serverSpace < $minimumSpace) {
                throw new Exception('Not enough space on the server to migrate the site. minimum required space is '. format_bytes($minimumSpace) . ' but only '. format_bytes($serverSpace) . ' available.');
            }
        }
    }
}
