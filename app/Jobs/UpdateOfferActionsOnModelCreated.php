<?php

namespace App\Jobs;

use App\Services\XcloudProduct\ActiveOffers;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateOfferActionsOnModelCreated implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $model, public $isBillable = false)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $team = $this->model->getTeam();

        if (!$team) {
            return;
        }

        $offerService = $this->model->getOfferService();

        if (!$offerService) {
            return;
        }

        $canAvailOfferServiceCount = $team->activePlan
            ? ActiveOffers::get($team->activePlan->name)[$offerService] ?? 0
            : 0;

        if ($this->isBillable) {
            $offerBills = $team->bills()
                                ->where('has_offer', true)
                                ->where('service_is_active', true)
                                ->where('service', $offerService)
                                ->count();

            $availedOfferServiceCount = intval($team->availed_offers[$offerService] ?? 0);

            if ($offerBills <= $availedOfferServiceCount) {
                return;
            }
        }

        $availedOfferServiceCount = $team->availed_offers[$offerService] ?? 0;

        if ($availedOfferServiceCount < $canAvailOfferServiceCount) {
            $team->update([
                'availed_offers' => array_merge($team->availed_offers ?: [], [
                    $offerService => $availedOfferServiceCount + 1,
                ]),
            ]);
        }
    }
}
