<?php

namespace App\Jobs;

use App\Enums\XcloudBilling\BillingStatus;
use App\Events\UnpaidInvoiceExportEvent;
use App\Exports\UnpaidInvoiceExportSheet;
use App\Models\GeneralInvoice;
use AWS\CRT\Log;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class UnpaidInvoiceExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $filters;
    protected $exportType;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($filters, $exportType)
    {
        $this->filters = $filters;
        $this->exportType = $exportType;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $exportType = $this->exportType;
        $filters = $this->filters;
        $fileName = 'XC-INV-' . Carbon::parse((now()->toDateString()))->format('Ymd') . '-' . strtoupper(Str::random(12)).'.'.$exportType;
        $filePath = 'invoice/'.$fileName;
        $invoices = GeneralInvoice::query()
            //->invoiceSearchFilter($filters)
            ->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])
            ->selectRaw('invoices.*, date_format(invoices.created_at, "%d %b %Y") as date, date_format(invoices.created_at, "%M %Y") as month_year')
            ->get();
        $months = $invoices->pluck('month_year')->unique()->values();

        Excel::store(new UnpaidInvoiceExportSheet($invoices, $months), $filePath, 's3');

        // Get the URL of the stored file
        $temporarySignedUrl = Storage::disk('s3')->temporaryUrl($filePath, now()->addHours(1));

        // Cache the URL for 1 month
        Cache::put('invoice_export_file_name', $fileName, 604800);
        Cache::put('invoice_export_url', $temporarySignedUrl, 604800);

        event(new UnpaidInvoiceExportEvent($temporarySignedUrl));
    }
}
