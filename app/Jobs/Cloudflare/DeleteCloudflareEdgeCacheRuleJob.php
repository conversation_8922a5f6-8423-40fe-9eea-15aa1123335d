<?php

namespace App\Jobs\Cloudflare;

use App\Jobs\Abstract\TaskJob;
use App\Models\Site;
use App\Models\Task;
use App\Scripts\InlineScript;
use App\Services\Integrations\CloudflareService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class DeleteCloudflareEdgeCacheRuleJob extends TaskJob
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 2;

    /**
     * Create a new job instance.
     *
     * @param Site $site
     * @return void
     */
    public function __construct(public Task $task, protected Site $site)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function performTask()
    {
        Log::info("Starting DeleteCloudflareEdgeCacheRuleJob for site: {$this->site->name}");

        // Get the rule ID and zone ID from site meta
        $ruleId = $this->site->getMeta('cloudflare_edge_cache.rule_id');
        $zoneId = $this->site->getMeta('cloudflare_edge_cache.zone_id');

        if (empty($ruleId) || empty($zoneId)) {
            Log::info("No cache rule found for site: {$this->site->name}");
            return;
        }

        // Check if site has Cloudflare integration
        if (!$this->site->team()->cloudflareIntegrations()->exists()) {
            Log::error("Site {$this->site->name} has no Cloudflare integration");
            return;
        }

        // Find the Cloudflare integration for this zone
        $cloudflareIntegration = null;

        foreach ($this->site->team()->cloudflareIntegrations as $integration) {
            $response = (new CloudflareService($integration))->checkDomainExists($this->site->name);
            if (Arr::get($response, 'domain_active_on_cloudflare')) {
                $cloudflareIntegration = $integration;
                break;
            }
        }

        if (!$cloudflareIntegration) {
            Log::error("Domain {$this->site->name} not found in any Cloudflare integration");
            return;
        }

        // Delete the cache rule
        try {
            $cloudflareService = new CloudflareService($cloudflareIntegration);
            $result = $cloudflareService->deleteCacheRule($zoneId, $ruleId);

            if ($result && isset($result['success']) && $result['success']) {
                // Remove the rule ID from site meta
                $this->site->update([
                    'meta->cloudflare_edge_cache->rule_id' => null,
                    'meta->cloudflare_edge_cache->zone_id' => null,
                ]);

                Log::info("Successfully deleted Cloudflare edge cache rule for site: {$this->site->name}");

                $this->task->update(['output' => 'Cloudflare edge cache rule deleted successfully.']);

                $script = new InlineScript("rm -rf {$this->site->manager()->siteBasePath()}/wp-content/mu-plugins/xcloud-cloudflare-edge-cache.php");

                $script->setScriptName("Delete Cloudflare Edge Cache helper plugin for site: {$this->site->name}");

                $task = $this->site->runInline($script);

                if ($task->successful()) {
                    Log::info("Successfully deleted Cloudflare Edge Cache helper plugin for site: {$this->site->name}");
                } else {
                    Log::error("Failed to delete Cloudflare Edge Cache helper plugin for site: {$this->site->name}. Error: {$task->output}");
                }
            } else {
                Log::error("Failed to delete Cloudflare edge cache rule for site: {$this->site->name}", [
                    'result' => $result
                ]);
                $this->task->update(['output' => 'Failed to delete Cloudflare edge cache rule.']);
            }
        } catch (\Exception $e) {
            Log::error("Exception deleting Cloudflare edge cache rule for site: {$this->site->name}", [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e; // Re-throw to trigger job retry
        }
    }
}
