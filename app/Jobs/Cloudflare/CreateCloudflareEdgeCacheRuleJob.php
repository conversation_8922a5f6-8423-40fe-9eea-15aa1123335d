<?php

namespace App\Jobs\Cloudflare;

use App\Jobs\Abstract\TaskJob;
use App\Models\Site;
use App\Models\Task;
use App\Scripts\UploadCloudflareEdgeCachePlugin;
use App\Services\Integrations\CloudflareService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class CreateCloudflareEdgeCacheRuleJob extends TaskJob
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param  Task  $task
     * @param  Site  $site
     */
    public function __construct(public Task $task, protected Site $site)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function performTask()
    {
        Log::info("Starting CreateCloudflareEdgeCacheRuleJob for site: {$this->site->name}");

        // Check if site has Cloudflare integration
        if (!$this->site->team()->cloudflareIntegrations()->exists()) {
            Log::error("Site {$this->site->name} has no Cloudflare integration");
            return;
        }

        // Find the Cloudflare integration that has the domain
        $cloudflareIntegration = null;
        $domainInfo = null;

        foreach ($this->site->team()->cloudflareIntegrations as $integration) {
            $response = (new CloudflareService($integration))->checkDomainExists($this->site->name);
            if (Arr::get($response, 'domain_active_on_cloudflare')) {
                $cloudflareIntegration = $integration;
                $domainInfo = $response;
                break;
            }
        }

        if (!$cloudflareIntegration) {
            Log::error("Domain {$this->site->name} not found in any Cloudflare integration");
            return;
        }

        Log::info("Domain {$this->site->name} found in Cloudflare integration: {$cloudflareIntegration->email}. Zone ID: {$domainInfo['zone_id']}");

        // Create the cache rule
        try {
            $cloudflareService = new CloudflareService($cloudflareIntegration);

            // Check if there's an existing rule
            $existingRuleId = $this->site->getMeta('cloudflare_edge_cache.rule_id');
            if ($existingRuleId) {
                // Delete the existing rule
                $deleteResult = $cloudflareService->deleteCacheRule($domainInfo['zone_id'], $existingRuleId);
                if (!$deleteResult || !isset($deleteResult['success']) || !$deleteResult['success']) {
                    Log::error("Failed to delete existing Cloudflare edge cache rule for site: {$this->site->name}", [
                        'result' => $deleteResult
                    ]);
                }
            }

            $result = $cloudflareService->createCacheRule(
                $domainInfo['zone_id'],
                $this->site->name,
                $this->getCacheExclusionRules()
            );

            if ($result && isset($result['success']) && $result['success']) {
                // Store the rule ID in site meta for future reference

                $ruleId = collect(Arr::get($result, 'result.rules'))
                    ->where('description', CloudflareService::buildCacheRuleName($this->site->name))
                    ->last()['id'] ?? null;

                $this->site->update([
                    'meta->cloudflare_edge_cache->rule_id' => $ruleId,
                    'meta->cloudflare_edge_cache->zone_id' => $domainInfo['zone_id'],
                ]);

                Log::info("Successfully created Cloudflare edge cache rule for site: {$this->site->name}. Rule ID: {$ruleId}");

                $this->task->update(['output' => 'Cloudflare edge cache rule created successfully. Rule ID: '.$ruleId]);

                // Install the Cloudflare Edge Cache helper plugin
                if ($this->site->isWordPress()) {
                    // log
                    Log::info("Installing Cloudflare Edge Cache helper plugin for site: {$this->site->name}");
                    $task = $this->site->runInline(new UploadCloudflareEdgeCachePlugin($this->site));

                    if ($task->successful()) {
                        Log::info("Successfully installed Cloudflare Edge Cache helper plugin for site: {$this->site->name}");
                    }else{
                        Log::error("Failed to install Cloudflare Edge Cache helper plugin for site: {$this->site->name}. Error: {$task->output}");
                    }

                }
            } else {
                Log::error("Failed to create Cloudflare edge cache rule for site: {$this->site->name}", [
                    'result' => $result
                ]);

                $this->task->update(['output' => 'Failed to create Cloudflare edge cache rule.']);
            }
        } catch (\Exception $e) {
            Log::error("Exception creating Cloudflare edge cache rule for site: {$this->site->name}", [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Re-throw to trigger job retry
        }
    }

    /**
     * Get cache exclusion rules from site settings
     *
     * @return array
     */
    protected function getCacheExclusionRules()
    {
        $exclusions = [
            'http_rules' => [],
            'cookie_rules' => [],
        ];

        // Parse HTTP exclusion rules
        $httpRules = $this->site->getMeta('cloudflare_edge_cache.cache_exclusion_http_rules');
        if ($httpRules) {
            $exclusions['http_rules'] = array_filter(array_map('trim', explode("\n", $httpRules)));
        }

        // Parse cookie exclusion rules
        $cookieRules = $this->site->getMeta('cloudflare_edge_cache.cache_exclusion_cookie_rules');
        if ($cookieRules) {
            $exclusions['cookie_rules'] = array_filter(array_map('trim', explode("\n", $cookieRules)));
        }

        return $exclusions;
    }
}
