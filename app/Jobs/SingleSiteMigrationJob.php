<?php

namespace App\Jobs;

use App\Jobs\Site\EstablishConnection;
use App\Jobs\Site\FetchRemoteSiteJob;
use App\Jobs\Site\InstallDatabase;
use App\Jobs\Site\InstallPhpVersion;
use App\Jobs\Site\MigrationEligibilityCheck;
use App\Models\ServerMigration;
use App\Models\Site;
use App\Models\SiteMigration;
use App\Services\Migration\ServerMigrating;
use App\Traits\SiteMigrationDispatcher;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SingleSiteMigrationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, SiteMigrationDispatcher;


    public function __construct(public Site $site, public ServerMigration $serverMigration,public SiteMigration $siteMigration)
    {
    }

    public function handle()
    {
        $this->site->markAsMigrating();

        $jobs = [
            ServerMigrating::CHECKING_STATUS => new MigrationEligibilityCheck($this->site),
            ServerMigrating::INSTALLING_PHP => new InstallPhpVersion($this->site),
            ServerMigrating::INSTALLING_DATABASE => new InstallDatabase($this->site),
            ServerMigrating::ESTABLISHING_CONNECTION => new EstablishConnection($this->site->server,$this->serverMigration,$this->site),
            ServerMigrating::INSTALLING_SITE => new FetchRemoteSiteJob($this->serverMigration,$this->site),
            //when fetching site is completed in background doing the rest of the jobs inside FetchSiteThenCompleMigrationJob
        ];

        $this->dispatchSiteMigrationChainJob($jobs);
    }
}
