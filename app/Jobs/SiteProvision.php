<?php

namespace App\Jobs;

use App\Enums\SiteType;
use App\Jobs\Site\BluePrintInstallJob;
use App\Jobs\Site\CreateNginxConf;
use App\Jobs\Site\EnableFullPageCaching;
use App\Jobs\Site\EnableRedisObjectCaching;
use App\Jobs\Site\EnableSsl;
use App\Jobs\Site\ExecuteDeployScriptJob;
use App\Jobs\Site\HandleSiteIndexingJob;
use App\Jobs\Site\InstallDatabase;
use App\Jobs\Site\InstallLaravel;
use App\Jobs\Site\InstallMonitoring;
use App\Jobs\Site\InstallPhpVersion;
use App\Jobs\Site\InstallWordPress;
use App\Jobs\Site\InstallWpCronJob;
use App\Jobs\Site\MarkSiteAsProvisioned;
use App\Jobs\Site\ProvisioningProgress;
use App\Jobs\Site\VrifyDns;
use App\Jobs\Site\EligibilityCheck;
use App\Models\Site;
use App\Services\Clone\SiteCloning;
use App\Services\Provisioning\SiteProvisioning;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Throwable;

class SiteProvision implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {
        //
    }

    public function handle()
    {
        $this->site->markAsProvisioning();
        $this->site->server->update(['meta->script_initiate_by' => $this->site->getMeta('script_initiate_by')]);
        $installSite = match ($this->site->type) {
            SiteType::LARAVEL => new InstallLaravel($this->site),
            SiteType::WORDPRESS => new InstallWordPress($this->site),
        };

        $jobs = [
            SiteProvisioning::CHECKING_STATUS => new EligibilityCheck($this->site),
            SiteProvisioning::VERIFYING_DNS => new VrifyDns($this->site, isProvisioning: true),
            SiteProvisioning::INSTALLING_PHP => new InstallPhpVersion($this->site),
            SiteProvisioning::INSTALLING_DATABASE => new InstallDatabase($this->site),
            SiteProvisioning::INSTALLING_WORDPRESS => $installSite,
            SiteProvisioning::CONFIGURING_SSL => new CreateStagingEnvUsingCloudflare($this->site),
            SiteProvisioning::CONFIGURING_HTTPS => new EnableSsl($this->site, reloadNginx: false),
            SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => new EnableFullPageCaching($this->site, reloadNginx: false),
            SiteProvisioning::CONFIGURING_REDIS_CACHE => new EnableRedisObjectCaching($this->site),
            SiteProvisioning::CONFIGURING_NGINX => new CreateNginxConf($this->site),
            SiteProvisioning::INSTALL_BLUEPRINT => new BluePrintInstallJob($this->site),
            SiteProvisioning::DEPLOY_SCRIPT => new ExecuteDeployScriptJob($this->site),
            SiteProvisioning::INSTALL_MONITORING => new InstallMonitoring($this->site),
            SiteProvisioning::INSTALLING_WP_CRON_JOB => new InstallWpCronJob($this->site),
            SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => new SettingUpDefaultXcloudEmailProviderJob($this->site),
            SiteProvisioning::HANDLE_INDEXING => new HandleSiteIndexingJob($this->site),
            SiteProvisioning::FINISHING_UP => new MarkSiteAsProvisioned($this->site),
        ];

        if ($this->site->isIPSite()) {
            unset($jobs[SiteProvisioning::VERIFYING_DNS]);
            unset($jobs[SiteProvisioning::CONFIGURING_SSL]);
            unset($jobs[SiteProvisioning::CONFIGURING_HTTPS]);
        }

        // If the site is a white label site, it doesn't get xcloud email provider
        if ($this->site->server->whiteLabel()) {
            unset($jobs[SiteProvisioning::SETTING_UP_EMAIL_PROVIDER]);
        }

        if ($this->site->getMeta('deploy_script')=== null) {
            unset($jobs[SiteProvisioning::DEPLOY_SCRIPT]);
        }

        if ($this->site->getMeta('blueprint_id') === null) {
            unset($jobs[SiteProvisioning::INSTALL_BLUEPRINT]);
        }

        $jobsChain = [];

        foreach ($jobs as $progress => $job) {
            $jobsChain[] = new ProvisioningProgress($this->site, $progress);
            $jobsChain[] = $job;
        }

        $siteId = $this->site->id;

        Bus::chain($jobsChain)->catch(function (Throwable $e) use ($siteId) {
            Site::find($siteId)?->markAsProvisioningFailure($e->getMessage());
        })->dispatch();
    }

    public function failed($e)
    {
        $this->site->markAsProvisioningFailure($e->getMessage());
    }
}
