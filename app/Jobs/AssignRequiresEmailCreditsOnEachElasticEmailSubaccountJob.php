<?php

namespace App\Jobs;

use App\Models\Team;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;

class AssignRequiresEmailCreditsOnEachElasticEmailSubaccountJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $teamId)
    {
        //
    }

    public function handle()
    {
        $team = Team::find($this->teamId);

        Log::info("Processing team id: {$team->id}");

        $baseAPIKey = config('services.elastic_email.api_key');
        $subAccountAPIKey = $team->getElasticEmailSubAccountApiKey();
        $subAccountEmail = $team->getMeta('elastic_email.subAccount.emailOnSubaccount');

        if($subAccountAPIKey && $subAccountEmail){
            Log::info("Assigning email credits to subaccount: {$subAccountEmail}");

            $baseUrl = 'https://api.elasticemail.com/v4/';

            $client = new Client([
                'base_uri' => $baseUrl,
                'headers' => [
                    'X-ElasticEmail-ApiKey' => $baseAPIKey,
                ],
            ]);

            try {
                // get subaccount
                $response = $client->get("subaccounts/{$subAccountEmail}")->getBody()->getContents();
                $subAccount = json_decode($response, true);

                // set RequiresEmailCredits to true
                $client->put("subaccounts/{$subAccountEmail}/settings/email", [
                    'json' => [
                        'RequiresEmailCredits' => true,
                        'EmailSizeLimit' => 10,
                        'MonthlyRefillCredits' => \Arr::get($subAccount, 'EmailCredits'),
                    ],
                ]);
                Log::info("Assigned RequiresEmailCredits to subaccount: {$subAccountEmail}");
                $team->saveMeta('elastic_email.subAccount.set_requires_email_credits', true);
            } catch (GuzzleException $e) {
                Log::error("Failed to assign RequiresEmailCredits to subaccount: {$subAccountEmail}");
            }
        }else{
            Log::warning("Skipping team id: {$team->id} as subaccount email or api key is missing");
        }
    }
}
