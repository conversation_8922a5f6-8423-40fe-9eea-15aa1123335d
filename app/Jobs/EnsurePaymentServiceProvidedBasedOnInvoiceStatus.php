<?php

namespace App\Jobs;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Services\PaymentGateway\BillingServices\EnsurePaymentServiceProvided;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class EnsurePaymentServiceProvidedBasedOnInvoiceStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param GeneralInvoice|ManualInvoice $invoice The invoice to process
     * @return void
     */
    public function __construct(public GeneralInvoice|ManualInvoice $invoice)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Check if the invoice is paid
        if ($this->invoice->status === BillingStatus::Paid) {
            $this->ensureServiceProvided();
        }
        // Check if the invoice payment failed
        elseif (in_array($this->invoice->status, [
            BillingStatus::Failed,
            BillingStatus::PaymentFailed,
            BillingStatus::Cancelled
        ])) {
            $this->revokeService();
        }
    }

    /**
     * Ensure services are provided for paid invoices
     *
     * @return void
     */
    private function ensureServiceProvided(): void
    {
        try {
            // Process bills
            if ($this->invoice->bills->count() > 0) {
                // Update bill status to paid only for unpaid bills
                $this->invoice->bills()->where('status', '!=', BillingStatus::Paid)->update([
                    'status' => BillingStatus::Paid,
                    'paid_on' => now()
                ]);

                // Provide service for each bill
                foreach ($this->invoice->bills as $bill) {
                    if ($bill->generator instanceof EnsurePaymentServiceProvided) {
                        if (!$bill->generator->serviceProvidedAfterPayment($bill->service)) {
                            Log::info('Service not provided for bill # ' . $bill->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $bill->service->toReadableSentence());

                            if ($bill->generator->provideService($bill->service)) {
                                Log::info('Provided service for bill # ' . $bill->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $bill->service->toReadableSentence());
                            } else {
                                Log::error('Failed to provide service for bill # ' . $bill->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $bill->service->toReadableSentence());
                            }
                        }
                    }
                }
            }

            // Process cart forms
            if ($this->invoice->cartForm->count() > 0) {
                // Update cart form status to paid only for unpaid cart forms
                $this->invoice->cartForm()->where('status', '!=', BillingStatus::Paid)->update([
                    'status' => BillingStatus::Paid
                ]);

                // Provide service for each cart form
                foreach ($this->invoice->cartForm as $cartForm) {
                    if (!$cartForm->serviceProvidedAfterPayment($cartForm->service)) {
                        Log::info('Service not provided for cart form # ' . $cartForm->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $cartForm->service->toReadableSentence());

                        if ($cartForm->provideService($cartForm->service)) {
                            Log::info('Provided service for cart form # ' . $cartForm->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                        } else {
                            Log::error('Failed to provide service for cart form # ' . $cartForm->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error ensuring service provided for invoice # ' . $this->invoice->id, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Revoke services for failed or cancelled invoices
     *
     * @return void
     */
    private function revokeService(): void
    {
        try {
            // Process bills
            if ($this->invoice->bills->count() > 0) {
                // Update bill status to failed or cancelled only if not already in that status
                $this->invoice->bills()->where('status', '!=', $this->invoice->status)->update([
                    'status' => $this->invoice->status,
                    'service_is_active' => false
                ]);

                // Cancel service for each bill
                foreach ($this->invoice->bills as $bill) {
                    if ($bill->generator instanceof EnsurePaymentServiceProvided) {
                        if ($bill->generator->cancelService($bill->service)) {
                            Log::info('Cancelled service for bill # ' . $bill->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $bill->service->toReadableSentence());
                        } else {
                            Log::error('Failed to cancel service for bill # ' . $bill->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $bill->service->toReadableSentence());
                        }
                    }
                }
            }

            // Process cart forms
            if ($this->invoice->cartForm->count() > 0) {
                // Update cart form status to failed only if not already in that status
                $this->invoice->cartForm()->where('status', '!=', CartFormStatuses::PaymentFailed)->update([
                    'status' => CartFormStatuses::PaymentFailed
                ]);

                // Cancel service for each cart form
                foreach ($this->invoice->cartForm as $cartForm) {
                    if ($cartForm->cancelService($cartForm->service)) {
                        Log::info('Cancelled service for cart form # ' . $cartForm->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                    } else {
                        Log::error('Failed to cancel service for cart form # ' . $cartForm->id . ' for team # ' . $this->invoice->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error revoking service for invoice # ' . $this->invoice->id, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
