<?php

namespace App\Jobs\FullServerMigration;

use App\Events\SiteFetchingCompleted;
use App\Http\Clients\CpanelApiClient;
use App\Models\ServerMigration;
use App\Services\Cpanel\CpanelApiService;
use App\Services\DataFormat\ValidJsonMaker;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchCpanelSiteList implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public ServerMigration $serverMigration)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $cpanelApi = $this->serverMigration->getCpanelClient();
        if (!$cpanelApi instanceof CpanelApiClient) {
            return;
        }

        $cpanelApiService = new CpanelApiService();
        $domainData = $cpanelApi->getAllDomainData()->collect();
        $sites = $cpanelApiService->getDomains($domainData, function ($wpConfigPath) use ($cpanelApi) {
            return $cpanelApi->isFileExists($wpConfigPath);
        });

        $this->serverMigration->update([
            'sites' => $sites,
            'meta->isFetchingCpanelSites' => false,
            'meta->fetchingCpanelSitesTime' => null,
        ]);

        //dispatch a pusher event to update the site list
        SiteFetchingCompleted::dispatch($this->serverMigration->id, $sites);
    }
}
