<?php

namespace App\Jobs\Site;

use App\Enums\SiteType;
use App\Models\Server;
use App\Models\Site;
use App\Scripts\DisableSiteMonitoringPing;
use App\Scripts\DeleteSiteDatabase;
use App\Scripts\DeleteSiteFiles;
use App\Scripts\DeleteSiteUser;
use App\Scripts\InlineScript;
use App\Scripts\Site\RemoveBackupCron;
use App\Scripts\Site\RemoveWPCron;
use App\Scripts\Site\StopPm2Process;
use App\Services\DNS\CloudflareDns;
use App\Services\Integrations\CloudflareService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class DeleteSiteJob implements ShouldQueue
{
    public int $timeout = 600;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Server $server,
        public Site $site,
        public bool $deleteFiles,
        public bool $deleteDatabase,
        public bool $deleteUser,
        public bool $deleteLocalBackups=false,
        public bool $deleteSiteModel = false
    ) {

    }

    public function handle(): void
    {
        dump('delete site init');

        if($this->site->hasStagingEnvironment() || $this->site->hasDemoEnvironment()){
            // delete dns from cloudflare
            (new CloudflareDns(url:$this->site->name))->delete(name: $this->site->name);
        }

        if($this->site->hasProductionEnvironment()){
            if($this->site->deploymentPullLogs()->exists()){
                $this->site->deploymentPullLogs()->delete();
            }

            if($this->site->deploymentPushLogs()->exists()){
                $this->site->deploymentPushLogs()->delete();
            }
        }

        if($this->site->getMeta('delete_dns_record') && $this->site->team()->hasCloudflareIntegration()){
            foreach($this->site->team()->cloudflareIntegrations as $cloudflareIntegration) {
                // delete dns from user's cloudflare account
                (new CloudflareService($cloudflareIntegration))
                    ->deleteDns(zoneId: Arr::get($this->site->meta,'cloudflare_integration.zone_id'), name: $this->site->name);

                if($this->site->isMultiSite() && $this->site->isMultiSiteSubdomain()){
                    // delete wildcard dns from user's cloudflare account
                    (new CloudflareService($cloudflareIntegration))
                        ->deleteDns(zoneId: Arr::get($this->site->meta,'cloudflare_integration.zone_id'), name: '*.' . $this->site->name);
                }

                // delete additional domain dns from user's cloudflare account
                if(!empty($this->site->additional_domains)){
                    foreach($this->site->additional_domains as $additionalDomain){
                        if(Arr::get($additionalDomain, 'status') === 'valid'){
                            (new CloudflareService($cloudflareIntegration))
                                ->deleteDns(
                                    zoneId: Arr::get($this->site->meta,'cloudflare_integration.zone_id'),
                                    name: Arr::get($additionalDomain, 'value')
                                );
                        }
                    }
                }

                // revoke ssl certificate from cloudflare
                (new CloudflareService($cloudflareIntegration))
                    ->revokeOriginCertificate(Arr::get($this->site->meta, 'cloudflare_integration.origin_certificate.certificate_id'));
            }
        }

        // Stop PM2 process for Node.js apps
        if ($this->site->isNodeApp()) {
            dump('stopping Node.js PM2 process');
            $this->server->runInline(
                new StopPm2Process($this->site)
            );
        }

        if ($this->deleteFiles) {
            $this->server->run(
                new DeleteSiteFiles($this->server, $this->site)
            );
        }

        if ($this->deleteDatabase) {
            dump('deleting database');
            $this->server->run(
                new DeleteSiteDatabase($this->server, $this->site)
            );
        }

        if ($this->deleteUser) {
            dump('deleting user');
            $this->server->run(
                new DeleteSiteUser($this->server, $this->site)
            );
        }

        #remove backup settings files
        $this->site->backupFiles()->delete();
        $this->site->backupSettings()->delete();

        #remove vulnerability scan
        $this->site->vulnerabilities()->delete();

        #remove backup cron
        $this->server->run(
            new RemoveBackupCron(site:  $this->site)
        );

        if ($this->site->isWordpress()){
            // remove wp cron
            $this->server->run(
                new RemoveWpCron(site:  $this->site)
            );
        }

        if ($this->deleteLocalBackups){
            dump('deleting local backups');
            $remove_script = "rm -rf ".$this->site->backupFilesPath()." && rm -rf ".$this->site->incrementalPath();
            $script = new InlineScript($remove_script);
            $script->setScriptName("Delete Local Backups of ({$this->site->name} on {$this->server->name})");
            $this->server->runInline($script);
        }

        $this->server->run(
            new DisableSiteMonitoringPing($this->server, $this->site)
        );

        // pull monitoring data for the server as it will reduce the storage size
        $this->server->pullMonitoring();

        $this->server->update(['meta->script_initiate_by' => null]);

        if($this->deleteSiteModel){
            $this->site->delete();
        }
    }
}
