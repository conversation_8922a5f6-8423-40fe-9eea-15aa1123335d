<?php

namespace App\Jobs\Site;

use App\Enums\SiteMigrationStatus;
use App\Enums\SiteStatus;
use App\Events\SiteProvisioned;
use App\Models\Site;
use App\Scripts\InlineScript;
use App\Scripts\ResetSiteUserPermission;
use App\Scripts\Site\SearchReplaceUrl;
use App\Services\Migration\SiteMigrating;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MarkAsMigratedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {

    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        dump('Marking As Migrated');

        // update permissions & generate nginx conf
        $this->site->siteMigration->migrationProgress(SiteMigrating::CONFIGURING_NGINX);
        $this->site->runInline(new ResetSiteUserPermission($this->site));
        $this->site->regenerateNginxConf(createSymblink: true);

        // search replace with new url (It is placed on migration script now(runXcloudMigrationCli.blade.php)
//        if (!($this->site->siteMigration->isSingle() || $this->site->siteMigration->isGit())) {
//            $this->site->runInline(new SearchReplaceUrl($this->site, $this->site->siteMigration));
//        }

        $this->site->siteMigration->migrationProgress(SiteMigrating::INSTALLING_MONITORING);
        $this->site->installMonitoringInline();

        $this->site->siteMigration->migrationProgress(SiteMigrating::INSTALLING_WP_CRON_JOB);
        if ($this->site->isWordpress()){
            $this->site->installWpCronJob();
        }
        $this->site->siteMigration->migrationProgress(SiteMigrating::FINISHING_UP);

        if ($this->site->siteMigration->isAuto()) {
            $this->site->siteMigration->connector()->sendStatusToPlugin(SiteMigrationStatus::FINISHED);
        }

        // mark as success
        $this->site->update(['status' => SiteStatus::PROVISIONED]);
        $this->site->siteMigration->update(['status' => SiteMigrationStatus::FINISHED]);

        // if belongs to server migration
        if ($this->site->siteMigration->has('serverMigration') && $this->site->siteMigration->serverMigration) {
            $this->site->siteMigration->serverMigration?->increaseMigrationCount();
        }
        $this->site->saveMeta('show_migration_banner', true);
        $notification_mails = $this->site->siteMigration->notification_mails;
        if (!is_array($notification_mails)) {
            $notification_mails = [];
        }
        SiteProvisioned::dispatch($this->site,$notification_mails);
    }
}
