<?php

namespace App\Jobs\Site;

use App\Enums\BackupType;
use App\Enums\BackupVersion;
use App\Models\BackupSetting;
use App\Models\Site;
use App\Models\User;
use App\Models\Server;
use App\Scripts\Site\InstallSiteBackupScript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TakeSiteBackupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Server $server, public Site $site, public ?User $user=null)
    {
    }

    public function handle(): void
    {
        $backupSetting =  BackupSetting::create([
            'site_id' => $this->site->id,
            'type' => BackupType::FULL,
            'is_local' => true,
            'database' => true,
            'files' => true,
            'auto_backup' => false,
            'auto_delete' => false,
            'user_id' => $this->user?->id,
            'status' => BackupSetting::PENDING,
            'version' => BackupVersion::VERSION_TWO,
        ]);
        $this->site->runInline(new InstallSiteBackupScript(server: $this->server, site:  $this->site, backupSetting:  $backupSetting,take_backup: true ));
    }
}
