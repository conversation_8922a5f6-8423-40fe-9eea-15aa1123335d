<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\InstallMauticSite;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstallMautic implements ShouldQueue
{
    public $timeout = 600;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {

    }

    /**
     * Install Mautic on the server.
     *
     * @throws Exception
     */
    public function handle()
    {
        dump('InstallMautic');

        $task = $this->site->run(new InstallMauticSite($this->site->server, $this->site));

        if (!$task->successful()) {
            throw new Exception('Install Mautic failed');
        }
    }
}
