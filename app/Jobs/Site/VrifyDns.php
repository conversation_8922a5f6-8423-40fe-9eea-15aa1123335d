<?php

namespace App\Jobs\Site;

use App\Enums\SslProvider;
use App\Events\SiteProvisioningStatusChanged;
use App\Models\Site;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class VrifyDns implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site, public bool $isProvisioning = false)
    {

    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        dump('VrifyDns');

        if($this->site->domainActiveOnCloudflare()){
            // no need to check dns verification for cloudflare domain
            return;
        }

        if ($this->site->ssl_provider === SslProvider::XCLOUD->value && !$this->site->server->hasDnsRecord($this->site->name)) {
            throw new Exception(__('dns.verification_failed'));
        }
    }
}
