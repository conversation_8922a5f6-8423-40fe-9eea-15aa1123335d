<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\Site\GitDeployment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
class PullGitRepository implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300;

    public int $tries = 10;

    public function __construct(public Site $site,public bool $createConfig = true)
    {

    }

    public function handle()
    {
        $this->site->runInline(new GitDeployment(
            $this->site,
            $this->site->getMeta('git_info')
        ));
    }
}
