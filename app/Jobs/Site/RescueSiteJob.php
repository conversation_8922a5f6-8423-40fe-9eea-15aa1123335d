<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\InlineScript;
use App\Scripts\Site\IsolateSiteUser;
use App\Scripts\Site\RepairN8nNode;
use App\Scripts\Site\RepairPm2Process;
use App\Scripts\Site\RestartPm2Process;
use App\Traits\MigrationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RescueSiteJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MigrationHelper;

    public int $timeout = 600;

    public function __construct(public Site $site, public array $data = [])
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        if ($this->data['directory_permissions'] ?? false) {
            $this->site->resetDirectoryPermission();
        }

        if ($this->data['reinstall_php'] ?? false) {
            $this->site->server->phpManager()->installSync($this->site->php_version);
        }

        if ($this->data['isolate_user'] ?? false) {
            $this->site->runInline(new IsolateSiteUser(site: $this->site));
        }

        if ($this->data['repair_node'] ?? false) {
            $this->site->runInline(new RepairN8nNode($this->site));
        }

        if ($this->data['repair_pm2'] ?? false) {
            $this->site->runInline(new RepairPm2Process($this->site));
        }

        if ($this->data['restart_pm2'] ?? false) {
            $this->site->runInline(new RestartPm2Process($this->site));
        }

        if ($this->data['regenerate_nginx'] ?? false) {
            $this->site->regenerateNginxConf(createSymblink: false, restartNginx: $this->data['restart_nginx'] ?? false);
        }
    }
}
