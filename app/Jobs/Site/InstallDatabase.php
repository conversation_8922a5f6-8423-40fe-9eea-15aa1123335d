<?php

namespace App\Jobs\Site;

use App\Enums\SiteStatus;
use App\Jobs\Server\FetchServerDatabasesWithUsers;
use App\Models\Site;
use App\Scripts\CreateDatabase;
use App\Scripts\CreateDatabaseUser;
use App\Scripts\TestDatabaseConnection;
use App\Services\Database\DatabaseProvider;
use App\Traits\MigrationHelper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class InstallDatabase implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,MigrationHelper;

    public int $timeout = 300;

    public int $tries = 10;

    public function __construct(public Site $site)
    {

    }

    public function handle()
    {
       $this->checkIfMigrationCancelled($this->site);
        if ($this->site->database_provider === DatabaseProvider::IN_SERVER) {

            dump("InstallDatabase: {$this->site->database_name}");

            $this->site->runInline(new CreateDatabase(
                $this->site->server,
                $this->site->database_name,
                $this->site->database_user,
                $this->site->database_password
            ));
        }

        if ($this->site->database_provider === DatabaseProvider::FROM_PROVIDER) {
            dump("InstallProviderDatabase: {$this->site->database_name}");
            $this->site->server->cloudProvider->createDatabase($this->site, $this);
        }

        // sync database info
        FetchServerDatabasesWithUsers::dispatch($this->site->server);
    }
}
