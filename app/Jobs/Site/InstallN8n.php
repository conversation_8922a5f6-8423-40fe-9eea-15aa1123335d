<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\InstallN8nSite;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstallN8n implements ShouldQueue
{
    public $timeout = 3600;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {

    }

    /**
     * Install n8n on the server.
     *
     * @throws Exception
     */
    public function handle()
    {
        dump('InstallN8n');

        $task = $this->site->run(new InstallN8nSite($this->site->server, $this->site));

        if (!$task->successful()) {
            throw new Exception('Install n8n failed');
        }
    }
}
