<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\Site\DeactivateFullPageCache;
use App\Scripts\Site\DeactivateLiteSpeedCache;
use App\Scripts\Site\InstallFullPageCache;
use App\Scripts\Site\InstallLiteSpeedCache;
use App\Services\WordPress\FullPageCaching;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DisableFullPageCaching implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {
    }

    public function handle()
    {
        if ($this->site->server->stack->isNginx()) {
            $this->site->run(new DeactivateFullPageCache($this->site));
        } else {
            $this->site->run(new DeactivateLiteSpeedCache($this->site));
        }

        $this->site->regenerateNginxConf();
    }
}
