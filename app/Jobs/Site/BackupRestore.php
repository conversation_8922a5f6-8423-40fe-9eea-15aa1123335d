<?php

namespace App\Jobs\Site;

use App\Callbacks\SiteBackRestoreUpdated;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Server;
use App\Models\Site;
use App\Models\StorageProvider;
use App\Scripts\Site\RestoreBackup;
use App\Scripts\Site\RestoreIncrementalBackup;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class BackupRestore implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Server $server,public Site $site, public BackupSetting $backupSetting, public Collection $files, public ?StorageProvider $storageProvider=null, public ?string $restoreMethod='replace')
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $file = $this->files->where('is_sql',false)->first();
        $sql_file = $this->files->where('is_sql',true)->first();
        $backupDomain = BackupFile::getBackUpDomain($this->files->value('file_name'));
        if(!$this->hasIncrementalFiles()){
            $this->site->runInBackground(new RestoreBackup(
                site: $this->site,
                server: $this->server,
                storageProvider: $this->storageProvider,
                file:  $file?->file_name ?? null,
                sqlFile:  $sql_file?->file_name ?? null,
                required_size:  $this->files->sum('taken_size'),
                backupDomain:  $backupDomain,
                s3Profile:  BackupFile::backupProfile($this->files->value('file_name')),
                is_local:  $this->backupSetting->is_local,
                filePath:  $this->backupSetting->is_local ? $this->files->value('file_path') : $this->site->backupFilesPath(),
                restoreMethod: $this->restoreMethod,
            ), [
                'then' => [
                    new SiteBackRestoreUpdated($this->site,$this->backupSetting),
                ],
            ]);
        }else{
            $restoreTime = $file ? Carbon::parse($file->server_datetime)->format('Y-m-d\TH:i:sP') : null;
            $this->site->runInBackground(new RestoreIncrementalBackup(
                site: $this->site,
                server: $this->server,
                storageProvider: $this->storageProvider,
                file: $file?->file_name ?? null,
                sqlFile: $sql_file?->file_name ?? null,
                backupDomain: $backupDomain,
                is_local: $this->backupSetting->is_local,
                filePath: $this->backupSetting->is_local ? $file->file_path : $this->site->backupFilesPath(),
                restoreTime: $restoreTime,
                restoreMethod: $this->restoreMethod,
            ), [
                'then' => [
                    new SiteBackRestoreUpdated($this->site,$this->backupSetting),
                ],
            ]);
        }
    }

    #Check is having incremental files or not
    public function hasIncrementalFiles(): bool
    {
        return $this->files->whereIn('type',[BackupFile::INCREMENTAL_FULL,BackupFile::INCREMENTAL_BACKUP])->isNotEmpty();
    }
}
