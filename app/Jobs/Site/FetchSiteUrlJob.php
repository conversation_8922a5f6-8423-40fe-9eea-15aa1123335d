<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\InlineScript;
use App\Scripts\ResetSiteUserPermission;
use App\Scripts\Site\GetSiteUrl;
use App\Scripts\Site\InstallFullPageCache;
use App\Services\WordPress\FullPageCaching;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchSiteUrlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public $siteMigration,public Site $site)
    {
    }

    public function handle()
    {
        $site_url = $this->site->runInline(new GetSiteUrl($this->site))->output;
        dump($site_url);
        $this->siteMigration->update(['existing_site_url' => $site_url]);
    }
}
