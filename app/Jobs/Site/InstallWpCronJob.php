<?php

namespace App\Jobs\Site;

use App\Models\Site;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class InstallWpCronJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 600;

    public function __construct(public Site $site)
    {

    }

    public function handle(): void
    {
        $server = $this->site->server->testSshConnection();

        if($server->is_connected){
            $this->site->saveMeta('enable_wp_cron', Site::DEFAULT_WP_CRON);
            $this->site->installWpCronJob();
        }else{
            Log::warning('SSH connection failed for site: ' . $this->site->name . ' on server: ' . $server->name . ' with IP: ' . $server->public_ip . ' and ID: ' . $server->id . ' while running InstallWpCronJob');
        }

    }
}
