<?php

namespace App\Jobs\Site;

use App\Callbacks\SiteFetchingCompleted;
use App\Models\ServerMigration;
use App\Models\Site;
use App\Scripts\CreateServerMigrationDirectory;
use App\Scripts\CreateSiteDirectory;
use App\Scripts\ServerMigration\FetchRemoteSite;
use App\Traits\MigrationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchRemoteSiteJob implements ShouldQueue
{
    public $timeout = 3600;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,MigrationHelper;

    public function __construct(public ServerMigration $serverMigration, public Site $site)
    {

    }

    public function handle()
    {
        $this->checkIfMigrationCancelled($this->site);
        dump('fetching remote site');

        $this->site->run(new CreateSiteDirectory($this->site->server, $this->site));


        $this->site->runInBackground(new FetchRemoteSite($this->site->server, $this->site, $this->serverMigration), [
            'then' => [
                new SiteFetchingCompleted($this->serverMigration->id,$this->site->id)
            ]
        ]);
    }
}
