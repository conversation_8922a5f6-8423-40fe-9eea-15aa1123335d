<?php

namespace App\Jobs\Site;

use App\Enums\SiteMigrationStatus;
use App\Enums\SiteStatus;
use App\Events\SiteProvisioned;
use App\Models\Site;
use App\Scripts\InlineScript;
use App\Scripts\Site\SearchReplaceUrl;
use App\Services\Migration\ServerMigrating;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MarkAsServerMigratedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {

    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        dump('Marking As Migrated');

        // update permissions & generate nginx conf
        $this->site->siteMigration->migrationProgress(ServerMigrating::CONFIGURING_NGINX);
        $this->site->runInline(new InlineScript("chown -R {$this->site->site_user}:{$this->site->site_user} /var/www/{$this->site->name}"));
        $this->site->regenerateNginxConf(createSymblink: true);

        // search replace with new url
        if (!$this->site->siteMigration->isSingle() && !$this->site->siteMigration->isCpanel()) {
            $this->site->runInline(new SearchReplaceUrl($this->site, $this->site->siteMigration));
        }


        $this->site->siteMigration->migrationProgress(ServerMigrating::INSTALLING_MONITORING);
        $this->site->installMonitoringInline();

        $this->site->siteMigration->migrationProgress(ServerMigrating::INSTALLING_WP_CRON_JOB);
        $this->site->installWpCronJob();

        $this->site->siteMigration->migrationProgress(ServerMigrating::FINISHING_UP);

        if ($this->site->siteMigration->isAuto()) {
            $this->site->siteMigration->connector()->sendStatusToPlugin(SiteMigrationStatus::FINISHED);
        }

        // mark as success
        $this->site->update(['status' => SiteStatus::PROVISIONED]);
        $this->site->siteMigration->update(['status' => SiteMigrationStatus::FINISHED]);

        $notification_mails = []; //$this->site->siteMigration->notification_mails;

        // if belongs to server migration
        if ($this->site->siteMigration->has('serverMigration') && $this->site->siteMigration->serverMigration) {
            $this->site->siteMigration->serverMigration?->increaseMigrationCount();
            $this->site->siteMigration->serverMigration?->dispatchNextJob();
            $notification_mails = $this->site->siteMigration->serverMigration?->notification_mails;
        }
        $this->site->saveMeta('show_migration_banner', true);
        if (!is_array($notification_mails)) {
            $notification_mails = [];
        }
        SiteProvisioned::dispatch($this->site, $notification_mails);
    }
}
