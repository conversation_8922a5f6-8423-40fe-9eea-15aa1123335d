<?php

namespace App\Jobs\Site;

use App\Models\BackupSetting;
use App\Models\Site;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstallBackupScript implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300;

    public function __construct(public Site $site, public BackupSetting $backupSetting)
    {

    }

    public function handle()
    {
        dump("Installing Backup script inside Site ({$this->site->name})");

         $this->site->installBackupScript($this->backupSetting);
    }
}
