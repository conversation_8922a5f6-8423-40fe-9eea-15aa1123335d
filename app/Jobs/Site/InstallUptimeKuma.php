<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\InstallUptimeKumaSite;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstallUptimeKuma implements ShouldQueue
{
    public $timeout = 300;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {

    }

    /**
     * Install Uptime Kuma on the server.
     *
     * @throws Exception
     */
    public function handle()
    {
        dump('InstallUptimeKuma');

        $task = $this->site->run(new InstallUptimeKumaSite($this->site->server, $this->site));

        if (!$task->successful()) {
            throw new Exception('Install Uptime Kuma failed');
        }
    }
}
