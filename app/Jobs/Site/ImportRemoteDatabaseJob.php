<?php

namespace App\Jobs\Site;

use App\Callbacks\DatabaseImportingCompleted;
use App\Models\ServerMigration;
use App\Models\Site;
use App\Scripts\ServerMigration\ImportRemoteDatabase;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ImportRemoteDatabaseJob implements ShouldQueue
{
    public $timeout = 300;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public ServerMigration $serverMigration, public Site $site,public mixed $nextSiteForMigration = null)
    {

    }

    public function handle()
    {
        dump('Importing remote database');


        $this->site->runInBackground(new ImportRemoteDatabase($this->site->server, $this->site, $this->serverMigration), [
            'then' => [
                new DatabaseImportingCompleted($this->serverMigration->id,$this->site->id)
            ]
        ]);
    }

}
