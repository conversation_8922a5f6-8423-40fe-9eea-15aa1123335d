<?php

namespace App\Jobs\Site;

use App\Enums\SiteStatus;
use App\Models\NodeVersion;
use App\Models\Server;
use App\Models\Site;
use App\Services\Node\NodeVersionManager;
use App\Traits\MigrationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InstallNodeVersion implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MigrationHelper;

    public int $timeout = 600;

    private NodeVersionManager $nodeManager;

    public function __construct(public Server $server, public bool $forceInstall = false)
    {
        $this->nodeManager = $this->server->nodeManager();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // $this->checkIfMigrationCancelled($this->site);
        // [1/8] Installing Node..
        // Make sure Node version is installed
        // If yes, maybe re-verify?
        // If it doesn't have it reinstall

        if (is_null($this->server->node_version)) {
            $this->server->log("Node version not set, checking available versions");
            $this->server->getNodeVersions();
        }

        if (is_null($this->server->node_version)) {
            // If no node version is set, set it to default
            $this->server->log("Node version not set, setting to default");
            $this->server->node_version = NodeVersion::DEFAULT;
            $this->server->save();
        }

        // need to make it
        if ($this->forceInstall || !$this->nodeManager->ensureInstalled($this->server->node_version)) {
            dump("InstallNodeVersion {$this->server->node_version}");
            $this->nodeManager->installSync($this->server->node_version, true);
        } else {
            dump("InstallNodeVersion: Node {$this->server->node_version} is already installed and set as default");
        }
    }
}
