<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Services\Migration\MigrationClientInstaller;
use App\Traits\MigrationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SetupMigrationClient implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MigrationHelper;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Site $site)
    {
        //
    }

    public function handle()
    {
        $this->checkIfMigrationCancelled($this->site);

        (new MigrationClientInstaller($this->site))->handle();
    }
}
