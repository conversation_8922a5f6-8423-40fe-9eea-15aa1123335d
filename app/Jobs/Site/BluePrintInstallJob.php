<?php

namespace App\Jobs\Site;

use App\Models\BluePrint;
use App\Models\Site;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class BluePrintInstallJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Site $site)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        if (null != $this->site->getMeta('blueprint_id')){
            dump('Installing BluePrint on site: '.$this->site->name);
            $bluePrint = BluePrint::find($this->site->getMeta('blueprint_id'));
            $this->site->installBluePrint($bluePrint);
        }
    }
}
