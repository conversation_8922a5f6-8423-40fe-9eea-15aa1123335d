<?php

namespace App\Jobs\Site;

use App\Enums\SiteStatus;
use App\Events\SiteProvisioningStatusChanged;
use App\Models\Site;
use App\Traits\MigrationHelper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MigrationEligibilityCheck implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MigrationHelper;

    public function __construct(public Site $site)
    {

    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        dump('EligibilityCheck');

        $this->checkIfMigrationCancelled($this->site);

        SiteProvisioningStatusChanged::dispatch($this->site);

        // throw new Exception("Database provider {$this->site->database_provider} is not supported at the moment.");

        // [0/8] Checking eligibility
        // - access
        // - space
        // - unique

        if (!$this->site->server->testSshConnection()->is_connected) {
            throw new Exception('Failed to establish connection to the server.');
        }
    }
}
