<?php

namespace App\Jobs\Site;

use App\Callbacks\PluginStateChanged;
use App\Callbacks\PluginUpdated;
use App\Models\Site;
use App\Scripts\Updating\SiteWordPressPluginUpdating;
use App\Scripts\Updating\WordPressPluginActiveDeactivate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ToggleSitePlugins implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site, public array $plugins, public string $action)
    {
    }

    public function handle(): void
    {
        $this->site->runInBackground(new WordPressPluginActiveDeactivate($this->site, $this->plugins,$this->action), [
            'then' => [
                new PluginStateChanged($this->site)
            ],
        ]);
    }

    public function failed($e)
    {
        $this->site->markAsProvisioningFailure($e->getMessage());
    }
}
