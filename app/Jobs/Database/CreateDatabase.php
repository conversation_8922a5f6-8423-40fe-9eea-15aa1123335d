<?php

namespace App\Jobs\Database;

use App\Enums\ServerStatus;
use App\Events\ServerProvisioningFailed;
use App\Events\ServerProvisioningStatusChanged;
use App\Jobs\Abstract\TaskJob;
use App\Models\Database;
use App\Models\Task;
use App\Traits\HandlesServerProvisioningError;


class CreateDatabase extends TaskJob
{
    use HandlesServerProvisioningError;

    public function __construct(public Task $task, public Database $database)
    {
    }

    /**
     * @throws \Exception
     */
    protected function performTask(): void
    {
        // If the server is already being created, we don't need to do anything
        if ($this->database->isCreating()) {
            return;
        }

        $this->database->markAsCreating();

        // If the server doesn't have a provider, we can't create it
        if (!$this->database->databaseCluster->cloudProvider){
            throw new \RuntimeException('Database does not have a provider');
        }

        // Create server using terraform
        $this->database->createUsingTerraformOrApi($this->task);

        $this->database->refresh();

        if (!$this->database->isCreated()) {
            throw new \RuntimeException('Failed to create database on the cluster.');
        }

        if (!$this->database->name) {
            throw new \RuntimeException('Failed to retrieve database data.');
        }

    }
}



