<?php

namespace App\Jobs\Database;

use App\Models\Site;
use App\Services\CloudServices\DODatabaseOperations;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DigitalOceanManagedDatabaseCreator implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Site $site;
    public DODatabaseOperations $digitalOcean;
    public array $clusterInfo;
    public array $managed_database_options;

    public $tries = 10;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Site $site)
    {
        $this->site = $site;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->digitalOcean = new DODatabaseOperations($this->site->server->cloudProvider->getAccessToken());
        $this->clusterInfo = $this->digitalOcean->getClusterInfo($this->site['managed_database_options']['do_cluster_id']);
        $this->managed_database_options = $this->site['managed_database_options'];


        try {
            $response = $this->digitalOcean->createDatabase($this->clusterInfo['id'], $this->site->database_name);
        } catch (\Exception $e) {

//            if ($this->job->maxTries() === $this->job->attempts()) {
//
//                dump('Max tries reached. Updating database name to ' . $this->managed_database_options['db_names'][0]);
//
//                $this->site->update([
//                    'database_name' => $this->managed_database_options['db_names'][0]
//                ]);
//            }

            dump($e->getMessage());

//          $this->release(60);
            return;
        }

        $this->site->update([
            'database_name' => $response['name']
        ]);

    }
}
