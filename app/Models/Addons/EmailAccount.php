<?php

namespace App\Models\Addons;

use App\Addons\Enum\MailboxEnums\EmailAccountStatusEnum;
use App\Addons\Enum\MailboxEnums\MailboxPlans;
use App\Addons\Enum\MailboxEnums\MailboxStatus;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Models\Product;
use App\Models\Team;
use App\Traits\Billable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class EmailAccount extends Model implements CipherSweetEncrypted
{
    use HasFactory, UsesCipherSweet, Billable;

    protected $guarded = ['id'];

    protected $casts = [
        'email_settings' => 'array',
        'plan' => MailboxPlans::class,
        'forwarding_emails' => 'array',
    ];

//    protected $hidden = [
//        'password'
//    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('password');
    }

    public function getMaskedPasswordAttribute(): string
    {
        return str_repeat('*', strlen($this->password));
    }

    public function mailboxDomain(): BelongsTo
    {
        return $this->belongsTo(MailboxDomain::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function markAsFailed(): void
    {
        $this->update([
            'status' => EmailAccountStatusEnum::FAILED,
        ]);
    }

    public function markAsActive(): void
    {
        $this->update([
            'status' => EmailAccountStatusEnum::ACTIVE,
        ]);
    }

    public function markAsInactive(): void
    {
        $this->update([
            'status' => EmailAccountStatusEnum::INACTIVE,
        ]);
    }

    /**
     * Get the billing name for the service
     *
     * @param BillingServices $service
     * @return string
     */
    public function getBillingName(BillingServices $service): string
    {
        return 'Mailbox: ' . $this->email;
    }

    /**
     * Get the billing short description title for the service
     *
     * @param BillingServices $service
     * @return string
     */
    public function getBillingShortDescriptionTitle(BillingServices $service): string
    {
        return 'Mailbox Plan: ' . MailboxPlans::getPlanName($this->plan->value);
    }

    /**
     * Get the billing short description for the service
     *
     * @param BillingServices $service
     * @return string
     */
    public function getBillingShortDescription(BillingServices $service): string
    {
        return 'Email account with ' . MailboxPlans::getPlanSize($this->plan->value) . ' storage';
    }

    /**
     * Get the default billing service
     *
     * @return BillingServices
     */
    public function getDefaultBillingService(): BillingServices
    {
        return BillingServices::Mailbox;
    }

    /**
     * Get the pricing for the service
     *
     * @param BillingServices $service
     * @param BillRenewalPeriod|null $renewalPeriod
     * @param Product|null $product
     * @return float
     */
    public function getPricing(BillingServices $service, BillRenewalPeriod $renewalPeriod = null, Product $product = null): float
    {
        return MailboxPlans::getPlanPrice($this->plan->value);
    }

    /**
     * Get the billing title for the service
     *
     * @param BillingServices $service
     * @return string
     */
    public function getBillingTitle(BillingServices $service): string
    {
        return 'Mailbox: ' . $this->email;
    }

    /**
     * Get the columns to sync data with bill when these dirty on generator
     *
     * @return array
     */
    public function getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator(): array
    {
        return ['plan', 'status'];
    }

    /**
     * Get the columns to store data on bill
     *
     * @return array
     */
    public function getColumnsToStoreDataOnBill(): array
    {
        return ['email', 'plan', 'status', 'mailbox_domain_id'];
    }

    /**
     * Get the team info
     *
     * @return Team
     */
    public function teamInfo(): Team
    {
        return $this->mailboxDomain->team;
    }

    /**
     * Get the billing started from date
     *
     * @return Carbon
     */
    public function billingStartedFrom(): Carbon
    {
        return $this->created_at;
    }

    /**
     * Check if service is provided after payment
     *
     * @param BillingServices $service
     * @return bool
     */
    public function serviceProvidedAfterPayment(BillingServices $service): bool
    {
        // check if mailbox is added properly in qboxmail end(CC: Faisal)
        return true;
        return $this->status === EmailAccountStatusEnum::ACTIVE;
    }

    /**
     * Provide service after payment
     *
     * @param BillingServices $service
     * @return bool
     */
    public function provideService(BillingServices $service): bool
    {
        $this->markAsActive();
        return true;
    }
}
