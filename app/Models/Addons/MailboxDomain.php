<?php

namespace App\Models\Addons;

use App\Addons\Enum\MailboxEnums\MailboxPlans;
use App\Addons\Enum\MailboxEnums\MailboxStatus;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class MailboxDomain extends Model implements CipherSweetEncrypted
{
    use HasFactory, UsesCipherSweet;

    protected $guarded = ['id'];

    protected $casts = [
        'mailbox_settings' => 'array',
        'plan' => MailboxPlans::class,
    ];

    protected $hidden = [
        'password'
    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('password');
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function getMailboxSettingsAttribute(): array
    {
        return $this->mailbox_settings ?? [];
    }

    public function emailAccounts(): HasMany
    {
        return $this->hasMany(EmailAccount::class);
    }

    public function markAsFailed(): void
    {
        $this->update([
            'status' => MailboxStatus::FAILED,
        ]);
    }

    public function markAsActive(): void
    {
        $this->update([
            'status' => MailboxStatus::ACTIVE,
        ]);
    }

    public function markAsInactive(): void
    {
        $this->update([
            'status' => MailboxStatus::INACTIVE,
        ]);
    }
}
