<?php

namespace App\Models;

use App\Enums\PatchstackVulnerabilityStatus;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Services\PaymentGateway\BillingServices\EnsurePaymentServiceProvided;
use App\Services\Vulnerability\PatchstackService;
use App\Traits\Billable;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class PatchstackVulnerability extends Model implements EnsurePaymentServiceProvided, CipherSweetEncrypted
{
    use Billable, UsesCipherSweet;

    protected $casts = [
        'vulnerabilities' => 'array',
        'is_purchase' => 'boolean',
        'status' => PatchstackVulnerabilityStatus::class
    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('site_api_key');
    }

    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    public static function scanPatchstackVulnerability(Site $site, $patchstackSiteId)
    {
        $patchastackVulnerability = app(PatchstackService::class)->patchstackVulnerabilities($patchstackSiteId);
        if ($patchastackVulnerability) {
            $patchastackVulnerability = $site->patchstackVulnerability()->update([
                'vulnerabilities' => $patchastackVulnerability
            ]);
        }

        return $patchastackVulnerability;
    }

    public function getVulnerabilitiesAttribute(): array
    {
        $wpUpdates = $this->site->wp_updates ?? [];
        $rawVulnerabilities = $this->attributes['vulnerabilities'] ?? '[]';
        $vulnerabilities = json_decode($rawVulnerabilities, true);

        if (!is_array($vulnerabilities)) {
            $vulnerabilities = [];
        }

        $formattedData = [
            'wp_cores' => [],
            'plugins' => [],
            'themes' => [],
        ];

        // Create lookup arrays for vulnerable items
        $vulnerablePlugins = [];
        $vulnerableThemes = [];

        // Process vulnerabilities first
        foreach ($vulnerabilities as $vulnerability) {
            // Add security_type = Insecure to all vulnerabilities
            $vulnerability['security_type'] = 'insecure';

            $name = strtolower($vulnerability['name'] ?? '');
            $slug = strtolower($vulnerability['slug'] ?? '');

            if ($name === 'wordpress') {
                $formattedData['wp_cores'][] = $vulnerability;
            } elseif (strpos($slug, 'plugin') !== false) {
                $formattedData['plugins'][] = $vulnerability;
                $vulnerablePlugins[strtolower($vulnerability['issue'])] = true;
            } elseif (strpos($slug, 'theme') !== false) {
                $formattedData['themes'][] = $vulnerability;
                $vulnerableThemes[strtolower($vulnerability['issue'])] = true;
            }
        }

        // Process WP Updates plugins
        if (!empty($wpUpdates['plugins'])) {
            foreach ($wpUpdates['plugins'] as $plugin) {
                $pluginTitle = strtolower($plugin['title'] ?? '');
                // Only add if not already in vulnerabilities
                if (!isset($vulnerablePlugins[$pluginTitle])) {
                    $plugin['security_type'] = 'secure';
                    $formattedData['plugins'][] = $plugin;
                }
            }
        }

        // Process WP Updates themes
        if (!empty($wpUpdates['themes'])) {
            foreach ($wpUpdates['themes'] as $theme) {
                $themeTitle = strtolower($theme['title'] ?? '');
                // Only add if not already in vulnerabilities
                if (!isset($vulnerableThemes[$themeTitle])) {
                    $theme['security_type'] = 'secure';
                    $formattedData['themes'][] = $theme;
                }
            }
        }

        return $formattedData;
    }

    public function getBillingName(BillingServices $service): string
    {
        return 'Patchstack Addon';
    }

    public function getBillingShortDescriptionTitle(BillingServices $service): string
    {
        return 'Patchstack Addon';
    }

    public function getBillingShortDescription(BillingServices $service): string
    {
        return '';
    }

    public function getDefaultBillingService(): BillingServices
    {
        return BillingServices::PatchstackAddon;
    }

    public function getPricing(BillingServices $service, BillRenewalPeriod $renewalPeriod = null, SubscriptionProduct|Product|Package $billThrough = null, float $defaultPrice = 5.0): float
    {
        if ($billThrough instanceof Product) {
            $product = $billThrough;
        } else {
            $product = Product::where('service_type', BillingServices::PatchstackAddon->value)->first();
        }

        return $product->price ?? $defaultPrice;
    }

    public function getBillingTitle(BillingServices $service, Bill $relatedOrExistingBill = null): string
    {
        return 'Patchstack Addon';
    }

    public function getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator(): array
    {
        return [
            'id',
            'patchstack_site_id',
            'status'
        ];
    }

    public function getColumnsToStoreDataOnBill(): array
    {
        return [
            'id',
            'patchstack_site_id',
            'status'
        ];
    }

    public function teamInfo(): Team
    {
        return $this->site->server->team;
    }

    public function billingStartedFrom(): Carbon
    {
        return $this->created_at;
    }

    public function serviceIsProvidedAfterPayment(BillingServices $billingService): bool
    {
        if ($this->is_purchase && !blank($this->patchstack_site_id)) {
            return true;
        }

        return false;
    }

    public function onPaymentSuccessProvideService(BillingServices $billingService): bool
    {
        app(PatchstackService::class)->enablePatchstack($this->site);
        return $this->serviceIsProvidedAfterPayment(BillingServices::PatchstackAddon);
    }

    public function cancelPaymentService(BillingServices $billingService): bool
    {
        app(PatchstackService::class)->cancelPatchstack($this->site);

        return true;
    }

    public function getAdditionalUsageChargeForCurrentPeriod(Bill $relatedOrExistingBill = null, SubscriptionProduct|Product|Package $billThrough = null): float
    {
        // TODO: Implement getAdditionalUsageChargeForCurrentPeriod() method.
        return 0.00;
    }

    public function getAdditionalUsageDetailsAsComment(): string
    {
        // TODO: Implement getAdditionalUsageDetailsAsComment() method.
        return '';
    }

    public function getAdditionalUsageLog(): array
    {
        // TODO: Implement getAdditionalUsageLog() method.
        return [];
    }

    public function resetLastAdditionalUsageChargeAndComment(Bill $addedOnBill): void
    {
        // TODO: Implement resetLastAdditionalUsageChargeAndComment() method.
    }

    public function getProductLimitation(string $className, BillingServices $service = null): float|null
    {
        // TODO: Implement getProductLimitation() method.
    }
}
