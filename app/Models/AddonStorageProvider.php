<?php

namespace App\Models;

use App\Enums\XcloudBilling\BillingServices;
use App\Services\PaymentGateway\BillingServices\EnsurePaymentServiceProvided;
use App\Services\StorageProvider\BackBlazeService;
use App\Traits\Billable;
use App\Traits\MetaAccessors;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AddonStorageProvider extends Model implements EnsurePaymentServiceProvided
{
    use Billable, MetaAccessors;
    const S3_STORAGE = 's3';

    const BACKBLAZE = 'backblaze';

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }
    public function billingStartedFrom() : Carbon
    {
        return $this->created_at;
    }

    public function getBillingName(BillingServices $service): string
    {
        return "S3 Storage: ".$this->bucket_name;
    }

    public function getBillingShortDescriptionTitle(BillingServices $service): string
    {
        return 'Limit';
    }

    public function getBillingShortDescription(BillingServices $service): string
    {
        return $this->bucket_limit . ' GB';
    }

    public function getPricing(BillingServices $service, ?BillRenewalPeriod $renewalPeriod = null, Product|Package|SubscriptionProduct|null $billThrough = null, float|int $defaultPrice = 0): float
    {
        return $this->product?->price;
    }

    public function getDefaultBillingService(): BillingServices
    {
        return BillingServices::xCloudStorageProvider;
    }

    public function getBillingTitle(BillingServices $service, Bill $relatedOrExistingBill = null): string
    {
        return 'xCloud S3 Storage Provider';
    }

    public function getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator(): array
    {
        return [
            'id',
            'provider_type',
            'host',
            'port',
            'label'
        ];
    }

    public function getColumnsToStoreDataOnBill(): array
    {
        return [
            'id',
            'provider_type',
            'host',
            'port',
            'label'
        ];
    }

    public function teamInfo(): Team
    {
        return $this->team;
    }

    public function serviceProvidedAfterPayment(BillingServices $billingService): bool
    {
        $response  =  app(BackBlazeService::class)
            ->setApplicationUser(keyId: $this->backblazeMember->application_key_id,applicationKey:  $this->backblazeMember->application_key)
            ->createBucket(bucketName: $this->synthesizeBucketName(bucket_name: $this->bucket_name));
        $data = $response->json();
        Log::info('Backblaze bucket created: ' . json_encode($data));;
        if ($response->successful()){
            return $this->update([
                'provider_bucket' => $data['bucketName'],
                'bucket_id' => $data['bucketId']
            ]);
        }
        Log::error('Backblaze bucket not created: ' . json_encode($data));
        return false;

    }


    /**
     * Generate a valid S3 bucket name from any input string.
     *
     * Rules:
     * - Only lowercase letters, numbers, and hyphens.
     * - Must start and end with a letter or number.
     * - No periods, underscores, or uppercase letters.
     * - Max length: 63 characters.
     *
     * @param string $bucket_name
     * @return string
     */
    function synthesizeBucketName(string $bucket_name): string
    {
        $input = implode('-', [
            Str::slug($bucket_name),
            $this->team_id,
            time()
        ]);
        // Convert to lowercase
        $bucketName = strtolower($input);

        // Replace all invalid characters with hyphens
        $bucketName = preg_replace('/[^a-z0-9-]/', '-', $bucketName);

        // Remove leading/trailing hyphens
        $bucketName = trim($bucketName, '-');

        // Ensure it starts and ends with a letter or digit
        $bucketName = ltrim($bucketName, "-");
        $bucketName = rtrim($bucketName, "-");

        // Truncate to 63 characters
        if (strlen($bucketName) > 63) {
            $bucketName = substr($bucketName, 0, 63);
            // Ensure it doesn't end with a hyphen after truncation
            $bucketName = rtrim($bucketName, '-');
        }

        return $bucketName;
    }

    public function getPlanAttribute(){
        return $this->product_slug;
    }

    public function provideService(BillingServices $billingService): bool
    {
        // TODO: Implement provideService() method.
    }

    public function cancelService(BillingServices $billingService): bool
    {
        // TODO: Implement cancelService() method.
    }

    public function storageProvider(): hasOne
    {
        return $this->hasOne(StorageProvider::class);
    }

    /**
     * @throws \Exception
     */
    public function generateKey($bucketID =null): string
    {
        if (!$bucketID){
            $bucketID = $this->bucket_id;
        }
        $response  =  app(BackBlazeService::class)
            ->setApplicationUser(keyId: $this->backblazeMember->application_key_id,applicationKey:  $this->backblazeMember->application_key)
            ->createKey(keyName: $this->bucket_name, bucketId: $bucketID);
        if ($response->successful()){
            return json_encode([...$response->json(),'provider_bucket'=>$this->provider_bucket]);
        }else{
            throw new \Exception('Backblaze key not created: ' . json_encode($response->json()));
        }
    }

    public function getCredentialKey($key=null): array|string|null
    {
        if ($this->storageProvider){
            $credentials = decrypt($this->storageProvider->credentials);
            if ($key) {
                return $credentials[$key];
            }
            return $credentials;
        }
        return null;
    }

    public function backblazeMember(): BelongsTo
    {
        return $this->belongsTo(BackblazeMember::class);
    }

    public function getEndpoint(): string
    {
       return str('https://')->append($this->backblazeMember->s3_endpoint)->toString();
    }

    /**
     * Get the additional usage charge for the current billing period
     *
     * @param Bill|null $relatedOrExistingBill The related bill if available
     * @param SubscriptionProduct|Product|Package|null $billThrough The product being billed
     * @return float The additional charge amount
     */
    public function getAdditionalUsageChargeForCurrentPeriod(Bill $relatedOrExistingBill = null, SubscriptionProduct|Product|Package $billThrough = null): float
    {
        // Get the billing period start and end dates
        $startDate = $relatedOrExistingBill?->period_start ?? Carbon::now()->startOfMonth();
        $endDate = $relatedOrExistingBill?->period_end ?? Carbon::now()->endOfMonth();

        // Calculate the over usage for the period
        $overUsage = $this->monthlyOverUsage($startDate, $endDate);

        // Store the details for later reference
        $this->saveMeta('last_additional_usage_charge', $overUsage['total_charge']);
        $this->saveMeta('last_additional_usage_comment', $overUsage['comment']);
        $this->saveMeta('last_additional_usage_details', $overUsage['details']);
        $this->saveMeta('last_additional_usage_over_gb', $overUsage['over_usage_gb']);
        $this->saveMeta('last_additional_usage_period', [
            'start' => $startDate->format('Y-m-d'),
            'end' => $endDate->format('Y-m-d')
        ]);

        return $overUsage['total_charge'];
    }

    /**
     * Get the additional usage details as a comment string
     *
     * @return string The comment describing the additional usage
     */
    public function getAdditionalUsageDetailsAsComment(): string
    {
        return $this->getMeta('last_additional_usage_comment', 'No additional usage details available.');
    }

    /**
     * Get the additional usage log details
     *
     * @return array The detailed log of additional usage
     */
    public function getAdditionalUsageLog(): array
    {
        return [
            'charge' => $this->getMeta('last_additional_usage_charge', 0),
            'over_usage_gb' => $this->getMeta('last_additional_usage_over_gb', 0),
            'details' => $this->getMeta('last_additional_usage_details', []),
            'period' => $this->getMeta('last_additional_usage_period', [
                'start' => Carbon::now()->startOfMonth()->format('Y-m-d'),
                'end' => Carbon::now()->endOfMonth()->format('Y-m-d')
            ])
        ];
    }

    /**
     * Reset the last additional usage charge and comment
     */
    public function resetLastAdditionalUsageChargeAndComment(): void
    {
        $this->saveMeta([
            'last_additional_usage_charge' => 0,
            'last_additional_usage_comment' => '',
            'last_additional_usage_details' => [],
            'last_additional_usage_over_gb' => 0,
            'last_additional_usage_period' => null
        ]);
    }

    /**
     * Get the Backblaze reports associated with this storage provider
     *
     * @return HasMany
     */
    public function backblazeReports(): HasMany
    {
        return $this->hasMany(BackblazeReport::class,'bucket_id','bucket_id');
    }

    /**
     * Calculate the monthly over-usage charges for this storage provider
     *
     * This method calculates the charges for storage usage that exceeds the
     * purchased limit. It uses the backblazeReports to determine actual usage
     * and charges $0.2 per GB per hour for any usage over the limit.
     *
     * @param Carbon|null $startDate Optional start date for the calculation period
     * @param Carbon|null $endDate Optional end date for the calculation period
     * @return array Returns an array with 'total_charge', 'over_usage_gb', 'details', and 'comment'
     */
    public function monthlyOverUsage(Carbon $startDate = null, Carbon $endDate = null): array
    {
        # Available limit in GB from the purchased product
        $availableLimit = $this->product->unit;
        $payPerUseRatePerGBPerHour = 0.2;

        # Set default date range to current month if not provided
        $startDate = $startDate ?? Carbon::now()->startOfMonth();
        $endDate = $endDate ?? Carbon::now()->endOfMonth();

        # Get all reports for the specified period
        $reports = $this->backblazeReports()
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->orderBy('date')
            ->get();

        if ($reports->isEmpty()) {
            return [
                'total_charge' => 0,
                'over_usage_gb' => 0,
                'details' => [],
                'comment' => 'No usage data available for the period.'
            ];
        }

        $overUsageDetails = [];
        $totalCharge = 0;
        $totalOverUsageGB = 0;

        foreach ($reports as $report) {
            $storedGB = (float) $report->stored_gb;

            # Calculate over usage (if any)
            $overUsageGB = max(0, $storedGB - $availableLimit);

            if ($overUsageGB > 0) {
                # Calculate storage byte hours for the over usage
                # Convert GB to bytes for the over usage portion
                $overUsageBytes = $overUsageGB * 1024 * 1024 * 1024;

                # Calculate the proportion of storage_byte_hours for the over usage
                $overUsageProportion = $overUsageGB / $storedGB;
                $overUsageByteHours = $report->storage_byte_hours * $overUsageProportion;

                # Convert byte-hours to GB-hours
                $overUsageGBHours = $overUsageByteHours / (1024 * 1024 * 1024);

                # Calculate charge for this day
                $dailyCharge = $overUsageGBHours * $payPerUseRatePerGBPerHour;

                $overUsageDetails[] = [
                    'date' => $report->date,
                    'stored_gb' => $storedGB,
                    'over_usage_gb' => $overUsageGB,
                    'over_usage_gb_hours' => $overUsageGBHours,
                    'charge' => $dailyCharge
                ];

                $totalCharge += $dailyCharge;
                $totalOverUsageGB += $overUsageGB;
            }
        }

        # Generate a comment for the bill
        $comment = '';
        if ($totalCharge > 0) {
            $comment = "Over usage charge for {$this->bucket_name}: {$totalOverUsageGB} GB over the {$availableLimit} GB limit. ";
            $comment .= "Charged at \${$payPerUseRatePerGBPerHour} per GB per hour.";
        } else {
            $comment = "No over usage charges for {$this->bucket_name}.";
        }

        return [
            'total_charge' => round($totalCharge, 2),
            'over_usage_gb' => round($totalOverUsageGB, 2),
            'details' => $overUsageDetails,
            'comment' => $comment
        ];
    }
}
