<?php

namespace App\Models;


use App\Enums\ServerMigrationStatus;
use App\Enums\SiteMigrationStatus;
use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Events\SiteMigrationStatusChanged;
use App\Events\SiteProvisioningFailed;
use App\Jobs\BackupRestoreMigrationJob;
use App\Jobs\GitSiteMigrationJob;
use App\Jobs\ManualSiteMigrationJob;
use App\Jobs\Site\DeleteSiteJob;
use App\Jobs\SiteMigrationJob;
use App\Services\Migration\MigrationConnector;
use App\Services\Migration\ServerMigrating;
use App\Services\Migration\SiteMigrating;
use App\Traits\MetaAccessors;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SiteMigration extends Model
{
    use HasFactory, MetaAccessors;

    const DESTINATION = 'destination';
    const DOMAINS = 'domains';

    const DATABASE = 'database';
    const CONFIRM = 'confirm';
    const PROCESSING = 'processing';

    // Types
    //automatic site migration by using the migration plugin
    const DEFAULT = 'default';
    //manual site migration by zipping and uploading
    const MANUAL = 'manual';

    const GIT = 'git';
    const BACKUP_RESTORE = 'backup_restore';

    //single site migration in full server migration
    const SINGLE = 'single';
    const CPANEL = 'cpanel';

    const SETTINGS = 'settings';

    public static array $types = [
        self::DEFAULT,
        self::MANUAL,
        self::SINGLE,
        self::CPANEL,
    ];

    protected $appends = [
        'domain_name_with_http'
    ];

    protected static function booted()
    {
        parent::applyScopes(function ($query) {
            return $query->where('type', self::DEFAULT);
        });
    }

    public array $stepsToFill = [];

    public array $multiStepHierarchy = [];

    public static array $canSkipSteps = [];

    public static array $canSkipStepsIf = [];

    public array $unModifiableSteps = [];

    public array $stepGetRouteNames = [];

    public function getStepRouteNames(): array
    {

        return array_merge([
            self::DESTINATION => 'site.migrate.destination',
            self::SETTINGS => 'site.migrate.settings',
            self::CONFIRM => 'site.migrate.confirm'
        ], $this->stepGetRouteNames);
    }


    public array $stepPostRouteNames = [];

    public function getStepPostRouteNames(): array
    {
        return array_merge([
            self::DESTINATION => 'api.site.migrate.store.destination',
            self::SETTINGS => 'api.site.migrate.store.settings',
            self::CONFIRM => 'api.site.migrate.store.confirm'
        ], $this->stepPostRouteNames);
    }


    public array $stepIcons = [];

    public function getStepIcons(): array
    {
        return array_merge([
            self::DESTINATION => 'xc-destination',
            self::DOMAINS => 'xc-domains',
            self::SETTINGS => 'xc-settings',
            self::DATABASE => 'xc-database',
            self::CONFIRM => 'xc-confirm'
        ], $this->stepIcons);
    }

    protected $guarded = [
        'id'
    ];

    protected $casts = [
        'form' => 'array',
        'meta' => 'array',
        'logs' => 'array',
        'notification_mails' => 'array',
        'status' => SiteMigrationStatus::class,
    ];

    protected $with = [
        'server'
    ];

    protected $hidden = [
        'form'
    ];

    public function getStep($key = '')
    {
        if (!$key) {
            return $this->form;
        }

        $keys = explode(".", $key);
        $value = $this->form;
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return null;
            }
            $value = $value[$k];
        }
        return $value;
    }

    public function scopeManual($query): SiteMigration
    {
        return $query->where('type', self::MANUAL);
    }


    public function scopeDefault($query): SiteMigration
    {
        return $query->where('type', self::DEFAULT);
    }

    public function getDestination(string $key = ''): ?string
    {
        return $this->getStep(SiteMigration::DESTINATION.'.'.$key);
    }

    public function getDomains(string $key = ''): ?string
    {
        return $this->getStep(SiteMigration::DOMAINS.'.'.$key);
    }

    public function getDatabase(string $key = ''): ?string
    {
        return $this->getStep(SiteMigration::DATABASE.'.'.$key);
    }

    public function getGitInfo(string $key = ''): ?string
    {
        return $this->getStep(SiteMigration::GIT.'.'.$key);
    }

    public function shouldMigrateDatabase(): ?string
    {
        return $this->getDatabase('database_migrate_type') !== 'only_file';
    }

    public function getConfirm(string $key = ''): ?string
    {
        return $this->getStep(SiteMigration::CONFIRM.'.'.$key);
    }

    public function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     *
     */
    public function getFormSteps(): array
    {
        $steps = [];
        if ($this->site && !$this->site?->hasDatabase()) {
            $this->stepsToFill = collect($this->stepsToFill)
                ->filter(fn ($step)=> $step !== self::DATABASE)
                ->values()->toArray();
        }
        foreach ($this->stepsToFill as $key => $step) {

            if (!isset($this->getStepRouteNames()[$step]) ||
                !isset($this->getStepPostRouteNames()[$step]) ||
                !isset($this->getStepIcons()[$step])) {
                continue;
            }

            $routes = [];

            if ($this->server) {
                $routes = [
                    'get_route' => route($this->getStepRouteNames()[$step], [
                        'siteMigration' => $this->id,
                        'server' => $this->server->id
                    ]),
                    'post_route' => route($this->getStepPostRouteNames()[$step], [
                        'siteMigration' => $this->id,
                        'server' => $this->server->id
                    ])
                ];

            } elseif (!$this->server && $step == self::DESTINATION) {
                $routes = [
                    'get_route' => route($this->getStepRouteNames()[$step], [
                        'server' => 'select',
                        'siteMigration' => 'new'
                    ]),
                    'post_route' => route($this->getStepPostRouteNames()[$step])
                ];
            }
            $steps[$step] = array_merge([
                'step_no' => $key + 1,
                'name' => title($step),
                'icon' => $this->getStepIcons()[$step],
                'completed_steps' => $key,
                'remaining_steps' => count($this->stepsToFill) - $key,
                'total_steps' => count($this->stepsToFill),
            ], $routes);


        }

        return $steps;
    }


    public function setStatusFilling(): bool
    {
        return $this->updateQuietly([
            'status' => SiteMigrationStatus::FILLING
        ]);
    }

    public function setStatusInit(): bool
    {
        return $this->updateQuietly([
            'status' => SiteMigrationStatus::INIT
        ]);
    }

    public function setStatusProcessing()
    {
        $this->update(['status' => SiteMigrationStatus::MIGRATING]);
        $this->site->update(['status' => SiteStatus::MIGRATING]);
    }

    function isMigrating(): bool
    {
        return $this->status === SiteMigrationStatus::MIGRATING;
    }

    public function setStatusMigrating(): bool
    {
        return $this->updateQuietly([
            'status' => SiteMigrationStatus::MIGRATING
        ]);
    }


    function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    function getProgressData(): array
    {

        if ($this->type === SiteMigration::SINGLE || $this->type === SiteMigration::CPANEL) {
            return $this->getSingleSiteMigrationProgressData();
        }
        $title = match (true) {
            $this->isGit() => "Git Clone from " . $this->formData('git_repo.git_repository') . " to " . $this->domain_name,
            $this->isRestoreBackup() => "Restoring Backup to " . $this->domain_name,
            without_http($this->existing_site_url) => "Migrating from " . without_http($this->existing_site_url) . " to " . $this->domain_name,
            default => "Migrating to " . $this->domain_name,
        };

        return [
            'title' =>  $title,
            'list' => SiteMigrating::get($this),
            'statusMax' => SiteMigrating::PROGRESS_MAX,
            'siteStatus' => $this->site->status,
            'server_id' => $this->server_id,
            'site_id' => $this->site_id,
            'site_name' => $this->site->name,
            'status' => (int) $this->getMeta('migratingStatus', 1),
            'percentage' => (int) $this->getMeta('migratingStatusPercentage', 0),
            'exception' => $this->getMeta('migratingErrorMessage') ?: null,
            'socketChannel' => 'site.migration.'.$this->site_id,
            'socketListenFor' => 'SiteMigrationStatusChanged',
            'animate_object' => asset('img/progress/Migration.svg'),
            'additionalData' => $this->getAdditionalProgressData(),
            'siteMigrationId' => $this->id,
            'serverId' => $this->server->id,
            'siteName' => $this->site->name,
            'siteId' => $this->site->id,
            'allowCancelProgress' => $this->isAuto() && $this->status !== SiteMigrationStatus::CANCELED,
            'autoSiteDeleteTime' => $this->site->getMeta('site_will_be_deleted_after'),
            'progressText' => "This may take a few moments and it depends on your source server."
        ];
    }

    function getSingleSiteMigrationProgressData()
    {
        return [
            'title' => 'Migrating Site '.$this->domain_name,
            'list' => ServerMigrating::get($this),
            'statusMax' => ServerMigrating::PROGRESS_MAX,
            'siteStatus' => $this->site->status,
            'server_id' => $this->server_id,
            'site_id' => $this->site_id,
            'site_name' => $this->site->name,
            'status' => (int) $this->getMeta('migratingStatus', 1),
            'percentage' => (int) $this->getMeta('migratingStatusPercentage', 0),
            'exception' => $this->getMeta('migratingErrorMessage') ?: null,
            'allowCancelProgress' => true,

            'socketChannel' => 'site.migration.'.$this->site_id,
            'socketListenFor' => 'SiteMigrationStatusChanged',
            'animate_object' => asset('img/progress/Migration.svg'),
            'siteMigrationId' => $this->id,

            'additionalData' => $this->getAdditionalProgressData(),
            'serverId' => $this->server->id,
            'siteName' => $this->site->name,
            'siteId' => $this->site->id,
            'progressText' => "This may take a few moments and it depends on your source server."
        ];
    }

    function getAdditionalProgressData(): array
    {
        return [
            ':php_version' => $this->site->php_version,
            ':found_file_count' => number_format($this->getProgressDataValueByKey('found_file_count')) ?: '',
            ':found_file_size' => format_bytes($this->getProgressDataValueByKey('found_file_size')) ?: '',
            ':file_migration_percentage' => $this->getProgressPercentageForSection(
                $this->getProgressDataValueByKey('downloaded_file_count'),
                $this->getProgressDataValueByKey('found_file_count')
            ),
            ':re_verified_file_count' => number_format($this->getProgressDataValueByKey('re_verified_file_count')) ?: '',
            ':file_retry_percentage' => $this->getProgressPercentageForSection(
                $this->getProgressDataValueByKey('retry_files_done'),
                $this->getProgressDataValueByKey('retry_files_count')
            ),
            ':found_table_count' => number_format($this->getProgressDataValueByKey('found_table_count')) ?: '',
            ':found_row_count' => number_format($this->getProgressDataValueByKey('found_row_count')) ?: '',
            ':db_migration_percentage' => $this->getProgressPercentageForSection(
                $this->getProgressDataValueByKey('migrated_row_count'),
                $this->getProgressDataValueByKey('found_row_count')
            ),
            ':stack' => $this->site->server->stack->toReadableString() ?: '',
            ':cache' => $this->site->server->stack->cachingMechanism() ?: '',
        ];
    }

    function getProgressDataValueByKey($key): int
    {
        return (int ) $this->getMeta('migrationProgressData->'.$key);
    }

    private function getProgressPercentageForSection(int $doneCount, int $totalCount): float|int|string|null
    {
        $percentage = $totalCount > 0 ? round(($doneCount / $totalCount) * 100) : 0;

        if ($percentage) {
            $percentage = number_format($doneCount).'/'.number_format($totalCount).' ('.$percentage.'%)';
        } elseif ($totalCount > 0) {
            $percentage = number_format($doneCount).'/'.number_format($totalCount);
        }

        return $percentage ?: '';
    }

    public function migrationProgress(int $status, $progressData = [])
    {
        if ($this->type === SiteMigration::SINGLE || $this->type === SiteMigration::CPANEL) {
            $percentage = number_format(($status / ServerMigrating::PROGRESS_MAX) * 100);
        } else {
            $percentage = number_format(($status / SiteMigrating::PROGRESS_MAX) * 100);
        }

        $this->update([
            'status' => SiteMigrationStatus::MIGRATING,
            'meta->migratingStatus' => $status,
            'meta->migratingStatusPercentage' => $percentage,
            'meta->migratingErrorMessage' => '',
            'meta->migrationProgressData' => $progressData + $this->getMeta('migrationProgressData', []),
        ]);
        SiteMigrationStatusChanged::dispatch($this);
    }

    public function markAsMigrationFailure($messages = null): void
    {
        $this->update([
            'status' => SiteMigrationStatus::FAILED,
            'meta->migratingErrorMessage' => $messages
        ]);

        $this->site->siteMigrationFailed();

        if ($this->type === SiteMigration::SINGLE || $this->type === SiteMigration::CPANEL) {
            dump('single site migration failed');
            $this->serverMigration?->dispatchNextJob();
        }

        if ($this->isAuto()) {
            $this->connector()->sendStatusToPlugin(SiteMigrationStatus::FAILED);
        }
        SiteProvisioningFailed::dispatch($this->site);
    }

    public function markAsMigrationCancelled($messages = null): void
    {
        $task = Task::find($this->site->getMeta('xcloud_migration_task_id'));

        if ($task) {
            $task->kill();
        }

        if ($this->isAuto()) {
            $this->connector()->sendStatusToPlugin(SiteMigrationStatus::CANCELED);
        }

        if ($this->getMeta('migration_cancelled_at')) {
            return;
        }

        $this->site->update([
            'status' => SiteStatus::MIGRATION_CANCELLED
        ]);

        $clearAfter = now()->addMinutes(5);

        $this->site->update([
            'meta->site_will_be_deleted_after' => $clearAfter
        ]);

        $this->update([
            'status' => SiteMigrationStatus::CANCELED,
            'meta->migratingErrorMessage' => $messages . ' at ' . now()->format('g:i A, d M Y') . ' GMT and the
                        migrated site data will be cleared automatically after ' . $clearAfter->format('g:i A, d M Y') . ' GMT',
            'meta->migration_cancelled_at' => now()->toDateTimeString()
        ]);

        DeleteSiteJob::dispatch($this->site->server, $this->site, true, true, true, false, true)->delay($clearAfter);
    }

    function getFormNextStepRoute()
    {
        $nextStep = $this->form['next_step'] ?? self::DOMAINS;

        if ($this->isAuto()) {
            return (new AutoSiteMigration)->stepGetRouteNames[$nextStep] ?? 'site.migrate.destination';
        } elseif ($this->isGit()) {
            return (new GitSiteMigration)->stepGetRouteNames[$nextStep] ?? 'site.migrate.destination';
        } elseif ($this->isRestoreBackup()) {
            return (new SiteBackupRestoreMigration())->stepGetRouteNames[$nextStep] ?? 'site.migrate.destination';
        } else {
            return (new ManualSiteMigration)->stepGetRouteNames[$nextStep] ?? 'site.migrate.destination';
        }
    }

    function startMigration()
    {
        $this->updateSite();

        $this->update(['status' => SiteMigrationStatus::INIT]);
        if ($this->isRestoreBackup()) {
            BackupRestoreMigrationJob::dispatch(SiteBackupRestoreMigration::findOrFail($this->id));
        }elseif ($this->isGit()) {
            GitSiteMigrationJob::dispatch(GitSiteMigration::findOrFail($this->id));
        } else {
            if ($this->isAuto()) {
                SiteMigrationJob::dispatch(AutoSiteMigration::findOrFail($this->id));
            } else {
                ManualSiteMigrationJob::dispatch(ManualSiteMigration::findOrFail($this->id));
            }
        }
    }

    public function getSettings(string $key = '')
    {
        return $this->getStep(self::SETTINGS.'.'.$key);
    }

    function updateSite(): Site
    {
        $site = Site::updateOrCreate([
            'id' => $this->site_id,
        ],
            [
                'server_id' => $this->server_id,
                'name' => $this->domain_name,
                'title' => $this->getDomains('site_title') ?: $this->domain_name,
                'database_provider' => $this->getSettings('database_provider'),
                'database_user' => $this->getSettings('database_user'),
                'database_name' => $this->getSettings('database_name'),
                'database_password' => $this->getSettings('database_password'),
                'managed_database_mode' => $this->getSettings('managed_database_mode'),
                'managed_database_options' => $this->getSettings('managed_database_options'),
                'redis_password' => $this->server->isRedisSeven() ? Str::random(32) : null,
                'site_user' => $this->getSettings('site_user'),
                'prefix' => $this->getSettings('prefix'),
                'php_version' => $this->getSettings('php_version'),

                'wordpress_version' => '',
                'status' => SiteStatus::MIGRATION_INIT,

                'ssl_provider' => $this->isDemo()
                    ? SslCertificate::PROVIDER_STAGING
                    : $this->getDomains('ssl_provider'),
                'environment' => $this->isDemo() ? Site::DEMO : Site::PRODUCTION
            ]);

        if ($site->ssl_provider == SslCertificate::PROVIDER_CUSTOM) {
            $site->sslCertificates()->updateOrCreate([
                'site_id' => $site->id,
                'provider' => SslCertificate::PROVIDER_CUSTOM,
            ], [
                'status' => 'new',
                'ssl_certificate' => $this->getDomains('ssl_certificate'),
                'ssl_private_key' => $this->getDomains('ssl_private_key'),
            ]);
        }

        return $site;
    }

    function isFinished(): bool
    {
        return $this->status === SiteMigrationStatus::FINISHED;
    }

    function connector(): MigrationConnector
    {
        return new MigrationConnector($this);
    }

    function setDomainNameAttribute($value)
    {
        $this->attributes['domain_name'] = get_domain($value);
    }

    function getDomainNameWithHttpAttribute()
    {
        return insert_http($this->domain_name);
    }

    public function isManual(): bool
    {
        return $this->type === self::MANUAL;
    }

    public function isGit(): bool
    {
        return $this->type === self::GIT;
    }

    public function isRestoreBackup(): bool
    {
        return $this->type === self::BACKUP_RESTORE;
    }

    public function isAuto()
    {
        return $this->type === self::DEFAULT;
    }

    public function isSingle() //full server migration single site
    {
        return $this->type === self::SINGLE;
    }

    public function isCpanel() //full server migration single site
    {
        return $this->type === self::CPANEL;
    }

    public function getDatabaseRemotePath()
    {
        //check if zip file
        if ($this->isDatabaseZip()) {
            return "/home/<USER>/.xcloud/migration-uploads/{$this->site->name}/db/{$this->site->name}_db.zip";
        }

        return "/home/<USER>/.xcloud/migration-uploads/{$this->site->name}/db/{$this->site->name}_db.sql";
    }

    public function isDatabaseZip()
    {
        $filePath = $this->getMeta('migration_database_url');
        return Str::endsWith($filePath, '.zip');
    }

    public function isDemo(): bool
    {
        return Arr::get($this, 'form.domains.domain_parking_method') === 'staging_env';
    }

    public function formData($key, $default = null)
    {
        return Arr::get($this->form, $key, $default);

    }


    public function serverMigration(): BelongsTo
    {
        return $this->belongsTo(ServerMigration::class);
    }

    public function isCanceled()
    {
        return $this->status === SiteMigrationStatus::CANCELED;
    }
}
