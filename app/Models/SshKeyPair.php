<?php

namespace App\Models;

use App\Scripts\AddPublicKey;
use App\Services\Shell\SecureShellKey;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use ParagonIE\CipherSweet\EncryptedRow;
use <PERSON>tie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use <PERSON>tie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;
use Throwable;

class SshKeyPair extends Model implements CipherSweetEncrypted
{
    use HasFactory, UsesCipherSweet;

    protected $hidden = ['private_key', 'default_sudo_password'];

    const SYSTEM_DEFAULT = 'system_default';

    const SYSTEM_DEFAULT_KEY_PAIR_NAME = 'System Default';
    const TEAM_KEY = 'team_key';
    const SERVER_DEFAULT = 'server_default';
    const SITE_GIT = 'site_git';


    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function (SshKeyPair $keypair) {
            $keypair->fingerprint ??= SecureShellKey::getFingerPrint($keypair->public_key);
        });
    }

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('private_key');
        $encryptedRow->addField('default_sudo_password');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Builder|Model|null
     * @throws Throwable
     */
    static function getSystemDefaulKeyPair()
    {
        $keypair = SshKeyPair::query()
            ->select(['id', 'public_key'])
            ->where('type', SshKeyPair::SYSTEM_DEFAULT)
            ->first();

        throw_unless($keypair, new Exception('System default keypair not found'));

        return $keypair;
    }

    function isSystemDefault()
    {
        return $this->type === SshKeyPair::SYSTEM_DEFAULT;
    }

    static function getSystemDefaulPublictKey(bool $enableRootLogin = false): array
    {
        $keypair = self::getSystemDefaulKeyPair();

        return [
            'id' => $keypair->id,
            'public_key' => $keypair->public_key,
            'command' => (new AddPublicKey($keypair->public_key, $enableRootLogin))->script(),
        ];
    }

    public static function getPublicKey($keypair, bool $enableRootLogin)
    {
        return [
            'id' => $keypair->id,
            'public_key' => $keypair->public_key,
            'command' => (new AddPublicKey($keypair->public_key, $enableRootLogin))->script(),
        ];
    }

    function privateKeyPath(): string
    {
        return SecureShellKey::storeFor('keypair-'.$this->id, $this->private_key);
    }

    function getSlug(): string
    {
        return Str::slug($this->name.'-'.$this->id);
    }

    function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    function servers()
    {
        return $this->belongsToMany(Server::class)->withTimestamps();
    }

    function sudoUsers()
    {
        return $this->belongsToMany(SudoUser::class)->withTimestamps(); //->withPivot('status');
    }

    function sites()
    {
        return $this->belongsToMany(Site::class)->withTimestamps();
    }

    function siteClone(): BelongsTo
    {
        return $this->belongsTo(SiteClone::class);
    }

    static function createServerDefaultKey($keyName = 'bot@xcloud', $type = SshKeyPair::SERVER_DEFAULT)
    {
        $key = SecureShellKey::forNewUser(keyName: $keyName);

        return SshKeyPair::create([
            'name' => $keyName,
            'public_key' => $key['publicKey'],
            'private_key' => $key['privateKey'],
            'type' => $type,
        ]);
    }

    static function saveServerDefaultKey($keyDetails, $type = SshKeyPair::SERVER_DEFAULT)
    {
        // This is for services like AWS EC2 where the default key can't be provided from user end instead
        // the cloud provider generates one and provide private key, fingerprint to access the server

        return SshKeyPair::create([
            'name' => $keyDetails['keyName'],
            'fingerprint' => $keyDetails['publicKeyFingerprint'],
            'private_key' => $keyDetails['privateKey'],
            'type' => $type,
        ]);
    }

    static function genearteServerDefaultKey($keyName = 'bot@xcloud', $type = SshKeyPair::SERVER_DEFAULT): array
    {
        $key = SecureShellKey::forNewUser(keyName: $keyName);

        return [
            'name' => $keyName,
            'public_key' => $key['publicKey'],
            'private_key' => $key['privateKey'],
            'type' => $type,
        ];
    }

    static function generatePublicKey($keypair, bool $enableRootLogin = false): array
    {

        return [
            'public_key' => $keypair['public_key'],
            'command' => (new AddPublicKey($keypair['public_key'], $enableRootLogin))->script(),
        ];
    }

    static function createSiteDeployKey($keyName = 'bot@xcloud')
    {
        $key = SecureShellKey::forNewUser(keyName: $keyName);

        return SshKeyPair::create([
            'name' => $keyName,
            'public_key' => $key['publicKey'],
            'private_key' => $key['privateKey'],
            'type' => SshKeyPair::SITE_GIT,
        ]);
    }
}
