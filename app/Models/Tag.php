<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'user_id',
    ];

    /*
     * Set Tag Slug from name
     */
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;
        do {
            $slug = Str::slug($value);
            $slugCount = Tag::where('slug', $slug)->count();
            if ($slugCount > 0) {
                $slug = $slug . '-' . $slugCount;
            }
        } while ($slugCount > 0);
        $this->attributes['slug'] = $slug;
        $this->attributes['user_id'] = auth()->id() ?? 1;
    }

    /**
     * Get all the servers that are assigned this tag.
     */
    public function servers()
    {
        return $this->morphedByMany(Server::class, 'taggable');
    }

    /**
     * Get all the teams that are assigned this tag.
     */
    public function teams()
    {
        return $this->morphedByMany(Team::class, 'taggable');
    }

    /**
     * Get all the sites that are assigned this tag.
     */
    public function sites()
    {
        return $this->morphedByMany(Site::class, 'taggable');
    }

    public static function associatedTags($table=null): Collection
    {
        $currentTeamId = auth()->user()->current_team_id;
        $cacheKey = "tags.associatedTags.{$currentTeamId}";
        if(!isset($GLOBALS[$cacheKey])){
            $GLOBALS[$cacheKey] = self::select(['name as value', 'name as label'])
                ->when($table, fn($q) => $q->withCount("{$table} as counter"))
                ->whereHas('servers', fn($q) => $q->where('team_id', auth()->user()->current_team_id))
                ->orWhereHas('teams', fn($q) => $q->where('teams.id', auth()->user()->current_team_id))
                ->orWhereHas('sites', fn($query) => $query->whereHas('server', fn($q) => $q->where('team_id', auth()->user()->current_team_id)))
                ->get();
        }
        return $GLOBALS[$cacheKey];
    }
}
