<?php

namespace App\Models;


use App\Console\Commands\SyncLastMonthVultrBill;
use App\Contracts\Loggable;
use App\Contracts\ProvisionableContract;
use App\Enums\CloudProviderEnums;
use App\Enums\CloudProviderEnums as EnumsCloudProvider;
use App\Enums\DatabaseType;
use App\Enums\CloudProviderServiceEnums;
use App\Enums\ServerModificationActions;
use App\Enums\ServerStatus;
use App\Enums\Stack;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Events\ServerCpuUsedHigh;
use App\Events\ServerCpuUsedNormal;
use App\Events\ServerDisconnected;
use App\Events\ServerDiskSpaceLow;
use App\Events\ServerHealServiceRestart;
use App\Events\ServerMemoryUsedHigh;
use App\Events\ServerMemoryUsedNormal;
use App\Events\ServerProvisioningStatusChanged;
use App\Http\Resources\ServerResource;
use App\Jobs\RemoveFilesJob;
use App\Jobs\ServerProvision;
use App\Events\ServerRebootRequired;
use App\Repository\IntervalBuilder;
use App\Repository\IntervalScheduler;
use App\Repository\ServerBillingRepository;
use App\Scripts\FetchServerTimezone;
use App\Scripts\GetCurrentDirectory;
use App\Scripts\InlineScript;
use App\Scripts\Monitoring\DisableServerMonitoring;
use App\Scripts\Monitoring\InstallServerMonitoring;
use App\Scripts\Monitoring\PullServerMonitoring;
use App\Scripts\PullPhpSettings;
use App\Scripts\Reboot;
use App\Scripts\RestartService;
use App\Scripts\Server\AddUser;
use App\Scripts\Server\BanIpAddress;
use App\Scripts\Server\FetchBannedIpAddresses;
use App\Scripts\Server\FetchServerUsers;
use App\Scripts\Server\FetchSiteUpdates;
use App\Scripts\Server\GetCronJobOutput;
use App\Scripts\Server\FetchCronJobsScript;
use App\Scripts\Server\InstallCronJob;
use App\Scripts\Server\RemoveCronJob;
use App\Scripts\Server\RemoveFileScript;
use App\Scripts\Server\RemoveMagicLogin;
use App\Scripts\Server\ServerHasEnoughStorageToCloneSite;
use App\Scripts\Server\SetupAutoSecurityUpdate;
use App\Scripts\Server\UnbanIpAddress;
use App\Scripts\Server\UpgradePackages;
use App\Scripts\UpdateServerTimezone;
use App\Services\DNS\DnsChecker;
use App\Services\PaymentGateway\BillingServices\EnsurePaymentServiceProvided;
use App\Services\PHP\PHPVersionManager;
use App\Services\Provisioning\ServerProvisioning;
use App\Services\Server\Monitoring\DiskUsage;
use App\Services\Server\Monitoring\WhiteLabelProcesses;
use App\Services\Shell\ShellResponse;
use App\Services\Shell\SshConnector;
use App\Services\Terraform\Terraform;
use App\Services\XcloudProduct\BillingServicePricing;
use App\Traits\Billable;
use App\Traits\HasLogger;
use App\Traits\HasTags;
use App\Traits\MetaAccessors;
use App\Traits\Provisionable;
use App\Traits\TerraformAndAPIOperations;
use App\Traits\Statistic;
use App\Traits\XcloudOfferable;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Jetstream\Jetstream;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

/**
 * @property mixed $name
 */
class Server extends Model implements CipherSweetEncrypted, ProvisionableContract, Loggable, EnsurePaymentServiceProvided
{
    use HasFactory, Provisionable, MetaAccessors, UsesCipherSweet, HasTags, SoftDeletes, TerraformAndAPIOperations, Statistic, Billable, XcloudOfferable, HasLogger;

    protected $dates = ['deleted_at'];

    protected $casts = [
        'meta' => 'json',
        'server_info' => 'json',
        'keypair' => 'json',
        'php_versions' => 'json',
        'status' => ServerStatus::class,
        'stack' => Stack::class,
        'database_type' => DatabaseType::class,
        'services' => 'json',
        'is_provisioned' => 'boolean',
        'is_connected' => 'boolean',
        'checked_at' => 'datetime',
        'last_visited_at' => 'datetime',
        'database_info' => 'json',
        'backups' => 'boolean',
        'cloud_service_name' => CloudProviderServiceEnums::class,
    ];

    protected $guarded = [];

    protected $hidden = [
        'provisioning_job_dispatched_at',
        'sudo_password',
        'database_password',
        'redis_password',
        'ssh_password',
    ];

    protected $appends = [
        'cloud_provider_name',
        'cloud_provider_readable',
        'state',
        'location'
    ];

    const AUTO_HEAL_MAIL_INTERVAL_IN_HOUR = 72;
    const WARNING_MAIL_INTERVAL_IN_HOUR = 24;
    const MEMORY_USAGE_THRESHOLD = 80;
    const CPU_USAGE_THRESHOLD = 80;
    const DEFAULT_TIMEZONE = 'UTC';

    const REDIS_SEVEN = 7;

    const DEFAULT_MAGIC_LOGIN = true;

    public function isRedisSeven(): bool
    {
        return $this->redis_version == self::REDIS_SEVEN;

    }

    public function getTimeZoneAttribute()
    {
        return $this->attributes['time_zone'] = $this->attributes['time_zone'] ?? self::DEFAULT_TIMEZONE;
    }

    public function getLatestBillGeneratedOnAttribute()
    {
        return $this->bills()->where('service', $this->getDefaultBillingService())->orderBy('bill_from', 'desc')?->first()?->bill_from;
    }

    public function getLatestBillDueOnAttribute()
    {
        return $this->bills()->where('service', $this->getDefaultBillingService())->orderBy('bill_from', 'desc')?->first()?->due_on;
    }

    public function getFirstBillGeneratedOnAttribute()
    {
        return $this->bills()->where('service', $this->getDefaultBillingService())->orderBy('bill_from')?->first()?->bill_from;
    }

    public function getSkuAttribute()
    {
        return $this->isXcloudVultr() ? $this->bills()->orderByDesc('id')->where('service', BillingServices::xCloudManagedHosting)->first()?->product?->sku : $this->size;
    }

    public function getLocationAttribute(): string
    {
        return get_location($this->region);
    }

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('database_password')
            ->addField('redis_password')
            ->addField('sudo_password')
            ->addField('ssh_password');
    }

    public function isPaymentFailed(): bool
    {
        return ($this->getMeta('provisioningStatus') === ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT && $this->getMeta('provisioningErrorMessage') !== null)
            || $this->bills()->paymentFailed()->latest()->exists();
    }

    public function resetProvisioningStatus(): string
    {
        return $this->update([
            'meta->provisioningErrorMessage' => null,
            'meta->provisioningStatusPercentage' => 0,
        ]);
    }

    public function createServerTask(): Task
    {
        return Task::create([
            'name' => 'Creating Server',
            'status' => 'pending',
            'server_id' => $this->id,
            'initiated_by' => auth()->id(),
            'team_id' => $this->team_id,
        ]);
    }


    public function siteMigration(): HasMany
    {
        return $this->hasMany(AutoSiteMigration::class);
    }

    public function manualMigration(): HasMany
    {
        return $this->hasMany(ManualSiteMigration::class);
    }

    #Current user access filter

    public function scopeSearch($query, $search)
    {
        return $query
            ->where('servers.name', 'like', '%'.$search.'%')
            ->orWhere('servers.public_ip', 'like', '%'.$search.'%');
    }

    public function scopeAccessFilter($query, $user = null)
    {
        $user = $user ?? auth()->user();
        return $query
            ->where('team_id', $user->current_team_id)
            ->when(!$user->currentTeam->hasAllServerAccess(), fn($q) => $q->whereIn('id', $user->serversByInvitation()->pluck('servers.id'))->orWhere('user_id', $user->id));
    }

    public function usersByInvitation()
    {
        return $this->belongsToMany(User::class, 'server_user')->withTimestamps();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function provision(): void
    {
        $this->log('Provisioning server');

        $this->markAsProvisioning();

        ServerProvision::dispatch($this);
    }

    public function ipAddress()
    {
        return $this->public_ip;
    }

    public function isNew(): bool
    {
        return $this->status === ServerStatus::NEW;
    }

    public function markAsProvisioning()
    {
        $now = now();

        $this->log("Marking server as provisioning. provisioning_job_dispatched_at: {$now}");

        return tap($this)->update([
            'log' => '',
            'status' => ServerStatus::PROVISIONING,
            'provisioning_job_dispatched_at' => $now
        ]);
    }

    public function markAsCreating()
    {
        return tap($this)->update(['status' => ServerStatus::CREATING, 'log' => '']);
    }

    public function markAsModifying()
    {
        return tap($this)->update(['status' => ServerStatus::MODIFYING, 'log' => '']);
    }

    public function markAsDeleting()
    {
        return tap($this)->update(['status' => ServerStatus::DELETING, 'log' => '']);
    }

    public function markAsSuspended()
    {
        return tap($this)->update(['status' => ServerStatus::SUSPENDED, 'log' => '']);
    }


    public function isProvisioned(): bool
    {
        return (bool) $this->is_provisioned;
    }

    public function isConnected(): bool
    {
        return (bool) $this->is_connected;
    }

    public function canMigrateSite(): bool
    {
        return  $this->isProvisioned() && $this->isConnected() && in_array($this->status, ServerStatus::successStates(), true);
    }

    public function isCreating(): bool
    {
        return $this->status === ServerStatus::CREATING;
    }

    public function readableStatus(): string
    {
        if (!$this->isProvisioned()) {
            return title($this->status->value);
        }
        if (!$this->isConnected()) {
            return 'Disconnected';
        }
        if ($this->hasLowStorage()) {
            return 'Low disk space';
        }
        if ($this->doesRebootRequire()) {
            return 'Reboot Required';
        }
        return title($this->status->value);
    }


    public function isCreated(): bool
    {
        return $this->status === ServerStatus::CREATED;
    }

    public function isModified(): bool
    {
        return $this->status === ServerStatus::MODIFIED;
    }

    public function isModifying(): bool
    {
        return $this->status === ServerStatus::MODIFYING;
    }

    public function isArmCpu(): bool
    {
        $armArchitectures = ['armv7l', 'aarch64', 'arm64'];
        return in_array($this->cpu_architecture, $armArchitectures);
    }

    /**
     * @throws Exception
     */
    public function getCpuArchitecture()
    {
        // Run the lscpu command and extract the architecture information
        $task  = $this->runInline(new InlineScript('lscpu | grep "Architecture" | awk \'{print $2}\''));
        $architecture = $task->output;

        if (!$task->successful()){
            throw new Exception("Failed to fetch CPU architecture. Received invalid output: {$architecture}");
        }

        // Check if the architecture is fetched successfully and update the model if necessary
        if ($architecture && $architecture !== $this->cpu_architecture) {
            $this->update(['cpu_architecture' => $architecture]);
            $this->log("CPU architecture updated to $architecture");
        }

        // Return the fetched architecture
        return $architecture;
    }


    /**
     * @throws Exception
     */
    public function getUbuntuVersion()
    {
        // Run the lsb_release command and extract the Ubuntu version information
        $task = $this->runInline(new InlineScript('lsb_release -r | awk \'{print $2}\''));

        $versionOutput = $task->output;

        if (!$task->successful()){
            throw new Exception("Failed to fetch Ubuntu version. Received invalid output: {$versionOutput}");
        }

        // Validate the version number format (e.g., "20.04")
        if (!preg_match('/^\d+(\.\d+)?$/', trim($versionOutput))) {
            throw new Exception("Failed to fetch Ubuntu version. Received invalid output: {$versionOutput}");
        }

        $version = trim($versionOutput);

        // Update the model if the fetched version is different from the stored one
        if ($version !== $this->ubuntu_version) {
            $updated = $this->update(['ubuntu_version' => $version]);

            if (!$updated) {
                throw new Exception('Failed to update Ubuntu version in the database.');
            }
        }

        // Return the fetched version
        return $version;
    }

    /**
     * @throws Exception
     */
    public function fetchPublicSshKeyFromAuthorizedKeys(bool $updateDB = false)
    {
        // Extract the first public SSH key from /root/.ssh/authorized_keys
        $publicKey = $this->runInline(new InlineScript('grep -E "^ssh-(rsa|dss|ed25519|ecdsa) " /root/.ssh/authorized_keys | head -n 1'))->output;

        $isValidPublicKey = preg_match('/^ssh-(rsa|dss|ed25519|ecdsa) [A-Za-z0-9+\/=]+/', trim($publicKey)) === 1;

        // Validate if the fetched string is actually a public SSH key
        if (!$isValidPublicKey) {
            // Throw an exception if the public key is invalid
            throw new Exception('The fetched Public Key is not valid');
        }

        // Check if the public key is fetched successfully and update the model if necessary
        if ($updateDB) {
            $this->keyPair->public_key = $publicKey;
            $this->keyPair->save();
        }

        // Return the fetched public key
        return $publicKey;
    }



    public function isDeleted(): bool
    {
        return $this->status === ServerStatus::DELETED;
    }

    public function isSuspended(): bool
    {
        return $this->status === ServerStatus::SUSPENDED;
    }

    public function isXCloud(): bool
    {
        // Condition: DigitalOcean (xCloud) or xCloud Managed or xCloud Provider
        return $this->cloudProvider?->isXcloud() ?? false;
    }

    public function isXcloudDO(): bool
    {
        // Condition: DigitalOcean (xCloud)
        return $this->cloudProvider?->provider === EnumsCloudProvider::XCLOUD;
    }

    public function isVultrOnly(): bool
    {
        return $this->cloudProvider?->provider === EnumsCloudProvider::VULTR;
    }

    public function isXcloudVultr(): bool
    {
        // Condition: xCloud Managed
        return $this->cloudProvider?->provider === EnumsCloudProvider::XCLOUD_VULTR;
    }

    public function isXcloudProviderHosting() : bool
    {
        // Condition: xCloud Provider
        return $this->cloudProvider?->provider === EnumsCloudProvider::XCLOUD_PROVIDER;
    }

    public function isWhiteLabelVultr() : bool
    {
        return $this->cloudProvider?->provider === EnumsCloudProvider::WHITE_LABEL_VULTR;
    }

    public function isXCloudOrWhiteLabel() : bool
    {
        return $this->isXcloud() || $this->isWhiteLabelVultr();
    }

    public function isUnderXCloudVultr() : bool
    {
        // Condition: xCloud Managed or xCloud Provider
        return $this->cloudProvider?->provider === EnumsCloudProvider::XCLOUD_VULTR || $this->cloudProvider?->provider === EnumsCloudProvider::XCLOUD_PROVIDER;
    }

    public function isUnderXCloudOrWhiteLabelVultr() : bool
    {
        // Condition: xCloud Managed or xCloud Provider or White Label Vultr
        return $this->cloudProvider?->provider === EnumsCloudProvider::XCLOUD_VULTR ||
            $this->cloudProvider?->provider === EnumsCloudProvider::XCLOUD_PROVIDER ||
            $this->cloudProvider?->provider === EnumsCloudProvider::WHITE_LABEL_VULTR;
    }

    public function isAnyProvider(): bool
    {
        return $this->cloudProvider?->provider === EnumsCloudProvider::ANY || is_null($this->cloudProvider?->provider) ;
    }

    public function isDigitalOcean(): bool
    {
        return $this->cloudProvider?->isDigitalOcean() ?? false;
    }

    public function isAws(): bool
    {
        return $this->cloudProvider?->isAws() ?? false;
    }

    public function isEc2(): bool
    {
        return $this->isAws() && $this->cloud_service_name->is(CloudProviderServiceEnums::EC2);
    }

    public function isGcp(): bool
    {
        return $this->cloudProvider?->isGcp() ?? false;
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function teamInfo(): Team
    {
        return $this->team;
    }

    function privateKeyPath(): ?string
    {
        return $this->keyPair?->privateKeyPath();
    }

    function getPublicSshKey(): string
    {
        return trim($this->keyPair->public_key);
    }

    function getPublicSshKeyFingerprint(): ?string
    {
        return $this->keyPair->fingerprint;
    }

    public function port()
    {
        return $this->ssh_port;
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    public function sites(): HasMany
    {
        return $this->hasMany(Site::class);
    }

    public function sudoUsers(): HasMany
    {
        return $this->hasMany(SudoUser::class);
    }

    public function cronJobs(): HasMany
    {
        return $this->hasMany(CronJob::class);
    }

    public function supervisorProcesses(): HasMany
    {
        return $this->hasMany(SupervisorProcess::class);
    }

    public function activePHPVersions(): array
    {
        return $this->phpVersions()->pluck('php_version')->toArray();
    }

    function phpVersions(): HasMany
    {
        return $this->hasMany(PhpVersion::class);
    }

    function phpManager(): PHPVersionManager
    {
        return new PHPVersionManager($this);
    }

    function installMonitoring(int $interval = 60): void
    {
        $this->runInline(new InstallServerMonitoring($this, $interval));
    }

    function disableMonitoring(): void
    {
        $this->runInBackground(new DisableServerMonitoring($this));
    }

    public function cloudProvider(): BelongsTo
    {
        return $this->belongsTo(CloudProvider::class, 'cloud_provider_id');
    }

    function getIsLoadingAttribute(): bool
    {
        return in_array($this->status, [
            ServerStatus::NEW,
            ServerStatus::CREATING,
            ServerStatus::MODIFYING,
            ServerStatus::DELETING,
            ServerStatus::PROVISIONING,
        ]);
    }

    function getIsErrorAttribute(): bool
    {
        return !$this->is_connected || $this->isStatusFailed();
    }

    function isStatusFailed(): bool
    {
        return in_array($this->status, [
            ServerStatus::CREATION_FAILED,
            ServerStatus::PROVISIONING_FAILED,
            ServerStatus::PAYMENT_FAILED
        ]);
    }

    public function getReadableDatabaseTypeAttribute(): string
    {
        return $this->database_type->readable();
    }

    public function getNameSlugAttribute(): string
    {
        return Str::slug($this->name);
    }

    public function database(): HasOne
    {
        return $this->hasOne(Database::class);
    }

    function keyPair(): BelongsTo
    {
        return $this->belongsTo(SshKeyPair::class, 'ssh_key_pair_id');
    }

    function sshKeyPairs(): BelongsToMany
    {
        return $this->belongsToMany(SshKeyPair::class)->withTimestamps();
    }

    public function isProvisioning(): bool
    {
        return $this->status == ServerStatus::PROVISIONING;
    }

    public function isDeleting(): bool
    {
        return $this->status == ServerStatus::DELETING;
    }

    public function markAsProvisioned()
    {
        return tap($this)->update(['status' => ServerStatus::PROVISIONED]);
    }

    function hasDnsRecord($domain): bool
    {
        return DnsChecker::check($domain, $this->public_ip);
    }


    public function progressPageData(): array
    {
        return [
            'title' => Lang::get('emails.server_title_text').' '.$this->name,
            'statusMax' => ServerProvisioning::PROGRESS_MAX,
            'status' => (int) $this->getMeta('provisioningStatus', 1),
            'percentage' => (int) $this->getMeta('provisioningStatusPercentage', 0),
            'exception' => $this->getMeta('provisioningErrorMessage') || $this->isStatusFailed()
                ? $this->getMeta('provisioningErrorMessage', 'Server provisioning failed')
                : null,
            'list' => ServerProvisioning::get($this),
            'socketChannel' => 'server.'.$this->id,
            'socketListenFor' => 'ServerProvisioningStatusChanged',
            'animate_object' => asset('img/progress/creating_server.svg'),
            'additionalData' => $this->additionalProgressData(),
            'serverName' => $this->name,
            'serverId' => $this->id,
            'paymentFailed' => $this->isPaymentFailed(),
            'isXcloud' => $this->isXcloud(),
            'isXcloudOrWhiteLabel' => $this->isXcloudOrWhiteLabel(),
            'provider_readable' => $this->cloud_provider_readable,
            'progressText' => Lang::get('emails.server_progress_text')
        ];
    }

    public function additionalProgressData(): array
    {
//        $amount = $this->bills->where('status', BillingStatus::Unpaid)->sum('amount_to_pay');
        $amount = $this->bills()->orderBy('id', 'desc')->first()?->invoice?->amount;

        return [
            ':ip' => $this->ipAddress() ?: '',
            ':provider' => $this->getCloudProviderReadableAttribute(),
            ':php_version' => $this->php_version,
            ':database_type' => $this->readable_database_type,
            ':stack' => $this->stack->toReadableString(),
            ':amount' => $amount ?: 0,
        ];
    }

    public function getCloudProviderReadableAttribute()
    {
        if ($this->isAws()) {
            return $this->getAwsProviderReadable();
        }

        if (currentWhiteLabel()) {
            return currentWhiteLabel()?->getBranding('brand_name');
        }

        return $this?->cloudProvider?->provider_readable ?: 'Other Provider';
    }

    /**
     * Get the readable attribute for AWS providers.
     *
     * @return string
     */
    protected function getAwsProviderReadable(): string
    {
        $service = $this->isEc2()
            ? CloudProviderServiceEnums::EC2->getReadableAttribute()
            : CloudProviderServiceEnums::LIGHTSAIL->getReadableAttribute();

        return CloudProviderEnums::AWS->getProviderReadableAttribute() . ' ' . $service;
    }


    public function getCloudProviderNameAttribute()
    {
        return $this?->cloudProvider?->provider ?: 'other';
    }

    public function modifyingProgressPageData(): array
    {
        $actionName = "Upgrading"; // this is default action name
        foreach (ServerModificationActions::cases() as $action) {
            // finding out which action is being performed from meta column as we are storing the action details temporarily there
            $modificationData = $this->getServerModificationData($action);

            if (is_array($modificationData) && !empty($modificationData)) {
                // if in meta column the action is found then we can say the action is being performed
                $actionName = ucwords($action->getGerundForm());
                break;
            }
        }

        return [
            'title' => $actionName . ' Your Server: '.$this->name,
            'statusMax' => 1,
            'status' => 1,
            'percentage' => 1,
            'exception' => null,
            'list' => [
                [
                    'stage' => $actionName. ' Server',
                    'tasks' => [
                        1 => $actionName . ' Server',
//                        ServerProvisioning::CREATING_SERVER => 'Creating Server on :provider',
                    ]
                ]
            ],
            'socketChannel' => 'server.status.'.$this->id,
            'socketListenFor' => 'ServerStatusChanged',
            'animate_object' => asset('img/progress/server_resizing.svg'),
            'serverName' => $this->name,
            'serverId' => $this->id,
            'siteStatus' => $this->status,
            'paymentFailed' => $this->bills()->paymentFailed()->latest()->exists(),
            'isXcloud' => $this->isXcloud(),
        ];
    }

    public function provisioningProgress(int $status, $error = null): void
    {
        $this->update([
            'status' => ServerStatus::PROVISIONING,
            'meta->provisioningStatus' => $status,
            'meta->provisioningStatusPercentage' => number_format(($status / ServerProvisioning::PROGRESS_MAX) * 100),
            'meta->provisioningErrorMessage' => $error,
        ]);

        ServerProvisioningStatusChanged::dispatch($this);
    }

    public function updateTimezone($timezone): void
    {
        $this->update([
            'time_zone' => $timezone,
        ]);

        $this->runInBackground(new UpdateServerTimezone($this, $timezone));
    }

    public function fetchTimezone(): void
    {
        try {
            $this->update([
                'time_zone' => $this->runInline(new FetchServerTimezone($this))->output,
            ]);
        } catch (Exception $e) {
            //
        }
    }

    /**
     * @throws Exception
     */
    public function fetchBannedIpAddresses(): array
    {
        $task = $this->runInline(new FetchBannedIpAddresses($this));

        if(!$task->successful()) {
            throw new Exception('Failed to fetch banned IP addresses');
        }

        // // Extract IP addresses from output
        // preg_match_all('/\b(?:\d{1,3}\.){3}\d{1,3}\b/', $task->output_json, $matches);

        // // Store extracted IP addresses in an array
        // $ipAddresses = $matches[0];

        return $task->output_json['banned_ips'] ?? [];
    }

    public function unbanIpAddress($ipAddress): bool
    {
        return $this->runInline(new UnbanIpAddress($this, $ipAddress))->successful();
    }

    public function banIpAddress($ipAddresses): bool
    {
        return $this->runInline(new BanIpAddress($this, $ipAddresses))->successful();
    }

    // public function forceDelete()
    // {
    //     DeleteServer::dispatch($this);
    //     return parent::forceDelete();
    // }

    public function archive(): void
    {
        $this->team->dropOrDeleteNotificationAction($this, 'Archived');
        $this->delete();
    }

    //update server_info json field

    public function getPhpSettings()
    {
        try {
            $phpSettings = $this->runInline(new PullPhpSettings($this));
            $settings = json_decode($phpSettings->output, true);
            $this->updateServerInfo('php_settings', $settings);
            return $this->getServerInfo('php_settings');
        } catch (Exception $e) {
            return $this->getServerInfo('php_settings', [
                'max_execution_time' => '',
                'upload_max_filesize' => '',
                'memory_limit' => '',
                'php_opcache_enabled' => false,
            ]);
        }
    }

    // get server_info json field

    public function updateServerInfo($key, $value): void
    {
        $server_info = $this->server_info;

        $server_info[$key] = $value;

        $this->update([
            'server_info' => $server_info
        ]);
    }

    /*
     * get php settings
     */

    public function getServerInfo($key, $default = null)
    {
        if (Str::contains($key, '->')) {
            $key = str_replace('->', '.', $key);
        }

        return Arr::get($this->server_info, $key, $default) ?? $default;
    }

    public function getServerMonitoringInfo(): array
    {
        $monitor = ServerResource::monitor($this);
        if ($monitor && $monitor->disk) {
            $diskUsage = new DiskUsage($monitor->disk);
            // disk
            $diskUsed = $diskUsage->getTotalUsed();
            $diskTotal = $diskUsage->getTotalSize();
            $diskAvailable = $diskUsage->getAvailable();


            // ram
            $ram = $monitor->memory;
            $ramUsed = Arr::get($ram, 'used');
            $ramFree = Arr::get($ram, 'free');
            $ramCached = Arr::get($ram, 'cache');
            $ramTotal = Arr::get($ram, 'total');
            $ramAvailable = Arr::get($ram, 'available');
            $ramPercentage = Arr::get($ram, 'percent');

            $ramAvailable = $ramTotal - $ramUsed;

            return [
                'last_checked' => $monitor->created_at->diffForHumans(),
                'disk' => [
                    'used' => $diskUsed,
                    'total' => $diskTotal,
                    'available' => $diskAvailable,
                    'isLow' => $diskUsage->isLow(),
                    'isCritical' => $diskUsage->isCritical()
                ],
                'ram' => [
                    'used' => $ramUsed,
                    'total' => $ramTotal,
                    'available' => $ramAvailable,
                    'percent' => $ramPercentage,
                    'isLow' => $ramAvailable < 1,
                ]
            ];
        }

        return [
            'last_checked' => 'Never',
            'disk' => 'Disk usage: 0 of 0 used',
            'ram' => 'Ram usage: 0 of 0 used',
        ];
    }

    public function monitors(): HasMany
    {
        return $this->hasMany(ServerMonitor::class);
    }

    public function latestMonitor(): HasOne
    {
        return $this->hasOne(ServerMonitor::class)->latestOfMany();
    }

    public function latestBill(): MorphOne
    {
        return $this->morphOne(Bill::class, 'generator')->latestOfMany();
    }

    public function getServerHealth()
    {
        # return $this->monitors()->latest()->first();
        return ServerResource::monitor($this);

    }

    public function storageAvailable(): float
    {
        # $monitor = $this->monitors()->latest()->first();
        $monitor = ServerResource::monitor($this);
        if ($monitor && ($monitor->disk[0] ?? null)) {
            $disk = $monitor->disk[0];
            return (float) str_replace('G', '', $disk['total']) - (float) str_replace('G', '', $disk['used']);
        }
        //no monitoring data, so assume 10GB
        return 10;
    }

    public function scopeFilter($query, $filters)
    {
        if ($filters->filterType === 'status' && in_array(Str::upper($filters->filter), $this->getAllStatus(), true)) {
            return $query->where('status', $filters->filter);
        }

        if ($filters->filter && $filters->filterType === 'tag') {
            return $query->whereHas('tags', fn($query) => $query->where('name', $filters->filter));
        }

        if ($filters->sortByWebServer && $filters->sortByWebServer !== 'All Web Servers') {
            return $query->where('stack', strtolower($filters->sortByWebServer));
        }

        if ($filters->client && $filters->client !== 'All Clients') {
            return $query->where('servers.user_id', $filters->client);
        }

        return $query;
    }

    //low disk space filter

    public function getAllStatus(): array
    {
        return array_column(ServerStatus::cases(), 'name');
    }

    public function scopeDoesntHaveLowDiskSpace($query)
    {
        return $query->whereHas('monitors', function ($q) {
            $q->orderByDesc('created_at')
                ->whereRaw('(CAST(JSON_EXTRACT(disk, "$[0].total") AS UNSIGNED) - CAST(JSON_EXTRACT(disk, "$[0].used") AS UNSIGNED)) >= 1');
        })->orWhereDoesntHave('monitors');
    }

    public function scopeProvisioned($query, $isProvisioned = true)
    {
        return $query->where('is_provisioned', $isProvisioned);
    }

    public function getStateAttribute()
    {

        //get the current state based on the status
        //there will be 4 state initial, Processing, success and Error
        if (in_array($this->status, ServerStatus::initialStates())) {
            return 'Initial';
        }

        if (in_array($this->status, ServerStatus::processingStates())) {
            return 'Processing';
        }

        if (!$this->is_connected) {
            return 'Disconnected';
        }
        if ($this->doesRebootRequire() || $this->hasLowStorage()) {
            return 'Warning';
        }

        if (in_array($this->status, ServerStatus::successStates())) {
            return 'Success';
        }

        if (in_array($this->status, ServerStatus::failedStates())) {
            return 'Error';
        }
    }

    public function pullMonitoring(): Task
    {
        $task = $this->runInline(new PullServerMonitoring($this));
        $this->saveMonitoring($task->output_json);
        return $task;
    }

    public function saveMonitoring($data): void
    {
        //check if $data is array if not then return
        if (!is_array($data)) {
            return;
        }
        $ubuntu = Arr::get($data, 'operatingSystem');

        // parse the version 20.04.5 from Ubuntu 20.04.5 LTS
        $version = explode(' ', $ubuntu)[1] ?? null;

        $cpu = Arr::get($data, 'cpu');
        $memory = Arr::get($data, 'memory');
        $disks = Arr::get($data, 'disks');
        $services = Arr::get($data, 'services');
        $siteStates = Arr::get($data, 'allSiteState');
        $upgradablePackages = Arr::get($data, 'upgradablePackages');
        $rebootRequire = (bool) Arr::get($data, 'rebootRequire');
        $topProcesses = (new WhiteLabelProcesses())->update(
            Arr::get($data, 'topProcesses') ?: [],
            str(currentWhiteLabel()?->getBranding('brand_name'))->slug()->__toString() ?? 'xcloud'
        );

        if (!$this->doesRebootRequire() && $this->getServerInfo('security_settings->automatic_reboot' ,false) && $rebootRequire) {
            $data = $this->getServerInfo('security_settings');
            $this->setupSecurityUpdate($data);
        }

        if (!$this->doesRebootRequire() && $rebootRequire) {
            $this->team->serverRebootRequired(server: $this);
        }

        $this->update([
            'reboot_required' => $rebootRequire,
            'services' => $services,
            'ubuntu_version' => $version,
            'server_info->upgradable_packages' => $upgradablePackages,
            'server_info->reboot_require' => $rebootRequire,
        ]);

        if (is_array($disks)) {
            // The comma , is used as a decimal separator in many parts of the world instead of the period. Therefore, 6,8G is equivalent to 6.8G in countries that use the period as a decimal separator.
            if (str_contains($disks[0]['used'], ',')) {
                $disks[0]['used'] = str_replace(',', '.', $disks[0]['used']);
            }

            if (str_contains($disks[0]['total'], ',')) {
                $disks[0]['total'] = str_replace(',', '.', $disks[0]['total']);
            }

            if (str_contains($disks[0]['used'], 'T')) {
                $disks[0]['used'] = ((float) str_replace('T', '', $disks[0]['used']) * 1024) .'G';
            }

            if (str_contains($disks[0]['total'], 'T')) {
                $disks[0]['total'] = ((float) str_replace('T', '', $disks[0]['total']) * 1024) .'G';
            }
        }
        $sites = $this->sites()->select(['id','name'])->pluck('name')->toArray();
        $siteStates = array_filter($siteStates, fn($site) => in_array($site['site_name'], $sites));
        $otherData['top_processes'] = $topProcesses;
        $this->monitors()->create([
            'cpu' => $cpu,
            'memory' => $memory,
            'disk' => $disks,
            'services' => $services,
            'site_states' => $siteStates,
            'others' => $otherData,
        ]);

        $this->checkDiskSpaceWarning(disks: $disks);

        if ($cpu['usedPercent'] ?? 0 > self::CPU_USAGE_THRESHOLD && (null == $this->getMeta('high_cpu_used_mail_sent_at') || now()->diffInHours($this->getMeta('high_cpu_used_mail_sent_at')) > self::WARNING_MAIL_INTERVAL_IN_HOUR)) {
            // Stop sending high cpu usage email for better user experience
           # ServerCpuUsedHigh::dispatch($this, $cpu);
            $this->saveMeta('high_cpu_used_mail_sent_at', now()->toDateTimeString());
        } else {
            if (isset($cpu['usedPercent']) && $cpu['usedPercent'] < self::CPU_USAGE_THRESHOLD && $this->getMeta('high_cpu_used_mail_sent_at')) {
                // Stop sending high cpu usage email for better user experience
                #ServerCpuUsedNormal::dispatch($this, $cpu);
                // Stop sending high cpu usage email for better user experience
                $this->saveMeta('high_cpu_used_mail_sent_at', null);
            }
        }
        //ram
        if ($memory['percent'] ?? 0 > self::MEMORY_USAGE_THRESHOLD && (null == $this->getMeta('high_memory_used_mail_sent_at') || now()->diffInHours($this->getMeta('high_memory_used_mail_sent_at')) > self::WARNING_MAIL_INTERVAL_IN_HOUR)) {
            // Stop sending high cpu usage email for better user experience
            #ServerMemoryUsedHigh::dispatch($this, $memory);
            $this->saveMeta('high_memory_used_mail_sent_at', now()->toDateTimeString());
        } elseif ($memory['percent'] ?? 0 < self::MEMORY_USAGE_THRESHOLD && $this->getMeta('high_memory_used_mail_sent_at')) {
            // Stop sending high cpu usage email for better user experience
            #ServerMemoryUsedNormal::dispatch($this, $memory);
            // Stop sending high cpu usage email for better user experience
            $this->saveMeta('high_memory_used_mail_sent_at', null);
        }
        $this->autoHealServicesCheck($services);
    }

    public function checkDiskSpaceWarning($disks): void
    {
        if (!is_array($disks) || empty($disks)) {
            return;
        }
        if ((new DiskUsage($disks))->isLow()) {
            $startLowSpaceMail = $this->getMeta('low_space_notification_start_at', now()->toDateString());

            $this->update([
                'meta->low_space_notification_start_at' => $startLowSpaceMail,
            ]);

            if ($this->shouldSendNotification( start_at: $startLowSpaceMail,end_at: $this->getMeta('low_space_mail_sent_at'))) {
                ServerDiskSpaceLow::dispatch($this);
                $this->saveMeta('low_space_mail_sent_at', now()->toDateTimeString());
            }
        }else{
            if ($this->getMeta('low_space_mail_sent_at')) {
                $this->update([
                    'meta->low_space_notification_start_at' => null,
                    'meta->low_space_mail_sent_at' => null,
                ]);
            }
        }

    }
    public function shouldSendNotification($start_at, $end_at):bool
    {
        $disconnectionIntervals = IntervalBuilder::create()
            ->everyHoursAfter(thresholdHours: 12, notifyEveryHours: 6)    // First 12 hours: Notify every 6 hours
            ->everyHoursAfter(thresholdHours: 24 * 3, notifyEveryHours: 24)  // First 3 days: Notify every 24 hours
            ->everyDaysAfter(thresholdDays: 30, notifyEveryDays: 7)    // Next 30 days: Notify every 7 days
            ->everyDaysAfter(thresholdDays: 31, notifyEveryDays: 30)   // After 31 days: Notify every 30 days
            ->build();

        $scheduler = new IntervalScheduler(
            $end_at,
            $start_at,
            $disconnectionIntervals
        );

       return $scheduler->shouldTrigger();
    }

    public function autoHealServicesCheck(array $services): void
    {
        $monitoringActivePhpVersions = $services['phpVersions'] ?? false;

        if ($this->stack->isNginx()){
            $services = Arr::only($services, ['php', 'mysql', 'nginx', 'redis', 'supervisor']);
        }else{
            $services = Arr::only($services, ['mysql', 'redis', 'supervisor', 'lsws']);
        }

        $services = array_filter($services, fn($service) => $service !== 'active');

        foreach ($services as $service => $value) {
            $script = new RestartService($service == 'php' ? "php{$this->php_version}-fpm" : $service);
            $task = $this->runInline($script);
            if ($task->successful()) {
                $this->update(['services->'.$service => 'active']);
                $task->update(['output' => trim($task->output.PHP_EOL."Service {$service} restarted successfully")]);
            }else{
                $task->update(['output' => trim($task->output.PHP_EOL."Service {$service} failed to restart")]);
            }

            #Stop sending high resource usage email for better user experience
            /*if ((null == $this->getMeta("auto_heal_{$service}_service_mail_at")
                    || now()->diffInHours($this->getMeta("auto_heal_{$service}_service_mail_at")) > self::AUTO_HEAL_MAIL_INTERVAL_IN_HOUR)) {
                ServerHealServiceRestart::dispatch($this, $service, $task);
                $this->saveMeta("auto_heal_{$service}_service_mail_at", now()->toDateTimeString());
            }*/
        }

        if ($monitoringActivePhpVersions === false || !is_array($monitoringActivePhpVersions)) {
            return; // this is probably using the old version of monitoring. skip
        }

        // we may have to send notification this a bit differently.
        $serverRequiredPhpVersions = $this->sites()->pluck("php_version")->push($this->php_version)->unique()->filter()->sort()->toArray();

        foreach ($serverRequiredPhpVersions as $phpVersion){
            if (! ($monitoringActivePhpVersions[$phpVersion] ?? false)){
                $task = $this->phpManager()->install($phpVersion);
                $task->update(['name' => "Auto Healing PHP {$phpVersion} Installation"]);
            }
        }

        if ($monitoringActivePhpVersions === false || !is_array($monitoringActivePhpVersions)) {
            return; // this is probably using the old version of monitoring. skip
        }

        // we may have to send notification this a bit differently.
        $serverRequiredPhpVersions = $this->sites()->pluck("php_version")->push($this->php_version)->unique()->filter()->sort()->toArray();

        foreach ($serverRequiredPhpVersions as $phpVersion){
            if (! ($monitoringActivePhpVersions[$phpVersion] ?? false)){
                $task = $this->phpManager()->install($phpVersion);
                $task->update(['name' => "Auto Healing PHP {$phpVersion} Installation"]);
            }
        }
    }

    public function checkedNow(): void
    {
        $this->update([
            'is_connected' => true,
            'checked_at' => now(),
            'meta->disconnected_at' => null,
            'meta->disconnection_start_at' => null,
            'meta->disconnection_notification_sent_at' => null,
        ]);
    }

    public function checkConnection(): Task
    {
        $task = $this->runInline(new GetCurrentDirectory());

        if (!$task->successful()) {
            $this->setAsDisconnected('checkConnection', 1);
            return $task;
        }

        $this->checkedNow();

        return $task;
    }


    public function testSshConnection(): Server
    {
        $canAccess = $this->ipAddress() && $this->runInline(new GetCurrentDirectory)->output == '/root';

        if (!$canAccess) {
            $this->setAsDisconnected('testSshConnection', 1);
            return $this;
        }

        $this->checkedNow();

        return $this;
    }

    public function checkHeartBit(int $maxRetries = 3): ShellResponse
    {
        $attempts = 0;

        while ($attempts < $maxRetries) {
            $task = SshConnector::runScript(
                ip: $this->ipAddress(),
                port: $this->port(),
                user: 'root',
                password: null,
                privateKeyPath: $this->keyPair?->privateKeyPath(),
                command: 'pwd',
                timeout: 15
            );

            if ($task->successful()) {
                $this->checkedNow();
                return $task;
            } else {
                $this->log("CheckHeartBit: failed attempt $attempts exit code: ".$task->exitCode." output: ".$task->output);
            }

            $attempts++;
        }

        //send notification
        $this->setAsDisconnected('checkHeartBit', $attempts);

        return $task ?? new ShellResponse('', '', 1);
    }

    public function setAsDisconnected($method = 'unknown', $attempts = 1): void
    {
        tap($this)->update([
            'is_connected' => false,
            'checked_at' => now(),
            'meta->disconnected_at' => now()->toDateTimeString().' - '.$attempts.' attempts'.' - '.$method.' method',
            'meta->disconnection_start_at' => $this->is_connected ? now()->toDateString() : $this->getMeta('disconnection_start_at', now()->toDateString()),
        ]);

        ServerDisconnected::dispatch($this);
    }

    public function checkServerHasRequiredStorageToCloneSite(Site $site, $sourceSiteStorage): Task
    {
        return $this->runInline(new ServerHasEnoughStorageToCloneSite($site, $sourceSiteStorage));
    }

    /*
     * Get the storage available on the server in GB
     */
    public function getStorageAvailable(): float
    {
        # $monitor = $this->monitors()->latest()->first();
        $monitor = ServerResource::monitor($this);

        //if monitor was created in the last 40 minutes, use that
        if ($monitor && $monitor->created_at->diffInMinutes(now()) < 40) {
            return $this->storageAvailable();
        }
        $this->pullMonitoring();
        return $this->storageAvailable();
    }

    public function hasLowStorage(): bool
    {
        $monitor = ServerResource::monitor($this);
        if($monitor){
            return (new DiskUsage($monitor?->disk))->isLow();
        }
        return false;
    }

    public function serverMigration(): HasMany
    {
        return $this->hasMany(ServerMigration::class);
    }

    public function getDatabases()
    {
        return Arr::get($this->database_info, 'databases', []);
    }

    public function getDatabaseNames()
    {
        return collect($this->getDatabases())
            ->map(fn($database) => str($database)->explode("||")->first())
            ->map(fn($database) => trim($database))
            ->filter()
            ->toArray();
    }

    public function getDatabaseUsers()
    {
        return Arr::get($this->database_info, 'db_users', []);
    }

    public function permissions($user = null): array
    {
        $user = $user ?: auth()->user();

        // Check if the user owns the team
        if ($user->ownsTeam($this->team)) {
            return $this->getOwnerPermissions($user);
        }

        // Fetch permissions for non-owners
        return $this->getMemberPermissions($user);
    }

    private function getOwnerPermissions($user): array
    {
        // Get all default permissions
        $permissions = Arr::flatten_key(Jetstream::$permissions, INF, ['server:', 'site:create']);

        // Filter permissions based on the user's abilities
        return array_values(array_filter($permissions, function ($permission) use ($user) {
            return match ($permission) {
                'server:delete' => $user->can('delete', $this),
                'server:custom-command-runner' => $user->can('customCommandRunner', $this),
                'server:manage-access' => $user->can('access', $this),
                'server:manage-services' => $user->can('settings', $this),
                default => true,
            };
        }));
    }

    private function getMemberPermissions($user): array
    {
        if (!$user->canAccessServer($this)) {
            return [];
        }

        // Get permissions from the membership record
        $permissions = Membership::where([
            'user_id' => $user->id,
            'team_id' => $this->team_id,
        ])->value('permissions', []) ?? [];

        // Filter permissions relevant to servers and site creation
        return array_filter($permissions, fn($permission) =>
            Str::contains($permission, 'server:') || Str::startsWith($permission, 'site:create')
        );
    }


    public function getPricing(BillingServices $service, BillRenewalPeriod $renewalPeriod = null, Product|Package|SubscriptionProduct $billThrough = null, float $defaultPrice = 0): float
    {
        // If it's package billing, use the package pricing
        if ($billThrough instanceof Package) {
            return $defaultPrice;
        }

        if ($billThrough instanceof Product) {
            return $billThrough->price;
        }

//        if ($billThrough instanceof SubscriptionProduct) {
//            return $billThrough->price;
//        }

        if ($defaultPrice) {
            return $defaultPrice;
        }

        if (!$renewalPeriod) { $renewalPeriod = BillRenewalPeriod::Monthly;}
        return match ($service) {
            BillingServices::SelfManagedHosting => BillingServicePricing::get($this->team, $service, $renewalPeriod),
            BillingServices::xCloudProviderHosting => $this->getServerProviderPriceForLTD($renewalPeriod),
            BillingServices::xCloudManagedHosting, BillingServices::ManagedHosting => $this->getServerProviderPrice($renewalPeriod, $billThrough),
            BillingServices::BackupXCloudManagedHosting, BillingServices::BackupXCloudProviderHosting => $this->getBackupCost(),
            BillingServices::BackupManagedHosting => $this->getServerProviderPrice($renewalPeriod, $billThrough) * 0.2,
            BillingServices::Site => 0
        };
    }

    public function getAdditionalUsageChargeForCurrentPeriod(Bill $relatedOrExistingBill = null, Product|Package|SubscriptionProduct $billThrough = null): float
    {
        return 0.0;
    }

    public function getAdditionalUsageDetailsAsComment(): string
    {
        // Return a custom message about additional server usage charges
        // If this string contains {amount}, it will be replaced with the actual amount and currency symbol
        // If not, the amount will be appended to this message
        return 'Additional server resource usage charges for this period:';
    }

    public function getAdditionalUsageLog(): array
    {
        // Return an array of usage log entries
        // This should contain details about additional resource usage
        // Each entry should have a timestamp, resource type, and usage amount
        return [];
    }

    public function resetLastAdditionalUsageChargeAndComment(Bill $addedOnBill): void
    {
        // Reset the additional usage charge and comment for this server
        // This should be called after the charge has been processed
        // and a new billing cycle begins
//        $this->update([
//            'meta->last_additional_usage_charge' => 0,
//            'meta->last_additional_usage_comment' => null
//        ]);
    }

    public function getApplicationFee(BillingServices $service, BillRenewalPeriod $renewalPeriod = null, Product $product = null): float
    {
        if (!$renewalPeriod) { $renewalPeriod = BillRenewalPeriod::Monthly;}

        if (!$product) {
            $product = Product::where('slug', $this->size)->where('renewal_type', $renewalPeriod)
                ->whereNotNull('source_product_id')
                ->where('service_type', $service)
                ->where('requires_billing_plan', $this->team->active_plan_id)->first();
        }

        if (!$product) {
            $product = Product::where('slug', $this->size)->where('renewal_type', $renewalPeriod)
                ->whereNotNull('source_product_id')
                ->where('service_type', $service)
                ->whereNull('requires_billing_plan')->first();
        }

        if ($product && $product->source && ($renewalPeriod && $product->renewal_type === $renewalPeriod) && ($service && $product->service_type === $service)) {
            return $product->source->price;
        }

        $isBackup = match ($service) {
            BillingServices::BackupManagedHosting => true, // BillingServices::BackupXCloudManagedHosting, BillingServices::BackupXCloudProviderHosting // Only Managed Hosting are allowed for now
            default => false,
        };

        if ($isBackup && !$product) {
            return $this->getBackupCost();
        }

        if ($product && $product->service_type->is(BillingServices::ManagedHosting)) {
            Log::error("Server ID: {$this->id} - Invalid product for {$service->toReadableString()}: {$this->size}");
        }

        return 0;
    }
    public function getServerProviderPrice(BillRenewalPeriod $renewalPeriod = BillRenewalPeriod::Monthly, $product = null): float
    {
        if ($this->isXcloudVultr()) {

            if (!$product) {
                $product = Product::search($this->size, $renewalPeriod, BillingServices::xCloudManagedHosting, $this->team->active_plan_id)->first();
//                $product = Product::where('slug', $this->size)->where('renewal_type', $renewalPeriod)
//                    ->where('service_type', BillingServices::xCloudManagedHosting)
//                    ->where('requires_billing_plan', $this->team->active_plan_id)->first();
            }

            if (!$product) {
                $product = Product::search($this->size, $renewalPeriod, BillingServices::xCloudManagedHosting)->first();
//                $product = Product::where('slug', $this->size)->where('renewal_type', $renewalPeriod)
//                                    ->where('service_type', BillingServices::xCloudManagedHosting)
//                                    ->whereNull('requires_billing_plan')->first();
            }

            if ($product) {

                if (($renewalPeriod && $product->renewal_type !== $renewalPeriod) ||
                    $product->service_type !== BillingServices::xCloudManagedHosting ||
                    $product->slug !== $this->size
                ) {
                    $_product = Product::search($this->size, $renewalPeriod, BillingServices::xCloudManagedHosting, $this->team->active_plan_id)->first();

                    if (!$_product) {
                        $_product = Product::search($this->size, $renewalPeriod, BillingServices::xCloudManagedHosting)->first();
                    }

                    if ($_product) {
                        $product = $_product;
                    }

                    Log::error("Server ID: {$this->id}, Invalid product for Vultr xCloud Managed Hosting: {$renewalPeriod?->toReadableString()}/{$this->size}");
                }

                return $product->price;
            }

            $vultr_packages = collect(config('services.xvultr.supported_machines', []));
            return $vultr_packages->flatten(1)->where('slug', $this->size)->value('price') ?? 0;
        }

        if ($this->isWhiteLabelVultr()) {

            if (!$product) {
                $product = Product::where('slug', $this->size)->where('renewal_type', $renewalPeriod)
                    ->where('service_type', BillingServices::ManagedHosting)
                    ->where('requires_billing_plan', $this->team->active_plan_id)->first();
            }

            if (!$product) {
                $product = Product::where('slug', $this->size)->where('renewal_type', $renewalPeriod)
                                    ->where('service_type', BillingServices::ManagedHosting)
                                    ->whereNull('requires_billing_plan')->first();
            }

            if ($product) {

                if (($renewalPeriod && $product->renewal_type !== $renewalPeriod) ||
                    $product->service_type !== BillingServices::ManagedHosting ||
                    $product->slug !== $this->size
                ) {
                    Log::error("Server ID: {$this->id}, Invalid product for Vultr Managed Hosting: {$renewalPeriod?->toReadableString()}/{$this->size}");
                }

                return $product->price;
            }

            $vultr_packages = collect(config('services.vultr.supported_machines', []));
            return $vultr_packages->flatten(1)->where('slug', $this->size)->value('price') ?? 0;
        }

        return 0;
    }

    public function getServerProviderPriceForLTD(BillRenewalPeriod $renewalPeriod = BillRenewalPeriod::Monthly): float
    {
        if ($this->isXcloudProviderHosting()) {

            $product = Product::where('slug', $this->size)
                                ->where('service_type', BillingServices::xCloudProviderHosting)
                                ->where('renewal_type', $renewalPeriod)
                                ->where('requires_billing_plan', $this->team->active_plan_id)->first();

            if (!$product) {
                $product = Product::where('slug', $this->size)
                                    ->where('service_type', BillingServices::xCloudProviderHosting)
                                    ->where('renewal_type', $renewalPeriod)
                                    ->whereNull('requires_billing_plan')->first();
            }

            if ($product) {
                return $product->price;
            }

            $vultr_packages = collect(config('services.xvultr.supported_machines', []));
            return $vultr_packages->flatten(1)->where('slug', $this->size)->value('price') ?? 0;
        }
        return 0;
    }

    public function billPaid(): bool
    {
        return $this->bills()->latest()->first()->isPaid();
    }

    public function setStatusPaymentFailed(): void
    {
        $this->update([
            'status' => ServerStatus::PAYMENT_FAILED->value
        ]);
    }

    public function whereHasNoBillings()
    {
        return $this->whereDoesntHave('bills');
    }

    public function getDefaultBillingService(): BillingServices
    {
        if ($this->isXcloudVultr()) {
            return BillingServices::xCloudManagedHosting;
        }

        if ($this->isXcloudProviderHosting()) {
            return BillingServices::xCloudProviderHosting;
        }

        if ($this->isWhiteLabelVultr()) {
            return BillingServices::ManagedHosting;
        }

        return BillingServices::SelfManagedHosting;
//        return $this->isXcloudVultr() ? BillingServices::xCloudManagedHosting : BillingServices::SelfManagedHosting;
    }

    /**
     * @throws Exception
     */
    public function getDefaultBackupBillingService(): BillingServices
    {
        if ($this->isXcloudVultr()) {
            return BillingServices::BackupXCloudManagedHosting;
        }

        if ($this->isXcloudProviderHosting()) {
            return BillingServices::BackupXCloudProviderHosting;
        }

        if ($this->isWhiteLabelVultr()) {
            return BillingServices::BackupManagedHosting;
        }

        throw new \Exception('Could not find Backup Billing Service for this server');
    }

    public function hasFailedPayments(): bool
    {
        return $this->bills()->where('status', BillingStatus::PaymentFailed->value)->exists();
    }

    public function isBackupEnabled(): bool
    {
        return $this->backups ?? false;
    }

    public function isBackupBillable(): bool
    {
        return ($this->backups && $this->getBackupCost() > 0) ?? false;
    }

    public function getBackupCost(): int
    {
        return (int) Arr::get($this->meta, 'backupCost');
    }

    public function getSize(): ?string
    {
        return $this->size;
    }

    function sizeShortDescription(string $size): string
    {
        $descriptions = [
            'vc2-1c-1gb' => '1 CPU core, 1 GB RAM',
            'vc2-1c-2gb' => '1 CPU core, 2 GB RAM',
            'vc2-2c-4gb' => '2 CPU cores, 4 GB RAM',
            'vc2-4c-8gb' => '4 CPU cores, 8 GB RAM',
            'vc2-6c-16gb' => '6 CPU cores, 16 GB RAM',
            'vhf-1c-1gb' => '1 CPU core, 1 GB RAM',
            'vhf-2c-2gb' => '2 CPU cores, 2 GB RAM',
            'vhf-2c-4gb' => '2 CPU cores, 4 GB RAM',
            'vhf-3c-8gb' => '3 CPU cores, 8 GB RAM',
            'vhf-4c-16gb' => '4 CPU cores, 16 GB RAM',
            'vhf-8c-32gb' => '8 CPU cores, 32 GB RAM',
        ];

        return $descriptions[$size] ?? $size;
    }

    public function getBillingTitle(BillingServices $service, Bill $relatedOrExistingBill = null): string
    {
        return (new ServerBillingRepository($this))->getTitle($relatedOrExistingBill);
    }

    public function getDefaultBillingOfferService(): BillingServices
    {
        return $this->getDefaultBillingService();
    }

    public function getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator(): array
    {
        return [
            'name',
            'size',
            'public_ip'
        ];
    }

    public function getColumnsToStoreDataOnBill(): array
    {
        return [
            'id',
            'name',
            'size',
            'public_ip'
        ];
    }

    public function serviceProvidedAfterPayment(BillingServices $billingService): bool
    {
        return $this->isProvisioned() || $this->isProvisioning();
    }

    /**
     * Count the number of XCloud hosted servers.
     *
     * @param string $type
     * @param bool $allTime
     * @param bool $countOutput
     * @return int|array
     */
    public function countXCloudHosted(string $type = 'all', bool $allTime = true, bool $countOutput = true): int|array
    {
        $query = $this;

        $query = $this->applyTimeFilter($query, $allTime)
            ->withoutTrashed()
            ->whereNotIn('status', ServerStatus::notCreatedOnHost());

        $conditions = $this->getConditionsBasedOnType($type); // only xcloud

        $query = $this->applyCloudProviderConditions($query, $conditions);

        if ($countOutput) {
            return $query->count();
        } else {
            return $query->get('id')->pluck('id')->all();
        }
    }


    private function applyTimeFilter($query, bool $allTime)
    {
        if (!$allTime) {
            $query = $query->applyTimezoneAndTime();
        }

        return $query;
    }

    public function getDisconnected(bool $allTime = true)
    {
        // only for xcloud
        $query = $this;

        $query = $this->applyTimeFilter($query, $allTime);

        $conditions = $this->getConditionsBasedOnType('all'); // all under xcloud

        $query = $this->applyCloudProviderConditions($query, $conditions);

        return $query->where('is_connected', false);
    }

    public function getDeletionFailed(bool $allTime = true)
    {
        // only for xcloud
        $query = $this;

        $query = $this->applyTimeFilter($query, $allTime);

        $conditions = $this->getConditionsBasedOnType('all');

        $query = $this->applyCloudProviderConditions($query, $conditions);

        return $query->where('status', ServerStatus::DELETION_FAILED);
    }


    public function countOnDifferentProviders(bool $allTime=true): array
    {
        $query = $this;

        if (!$allTime) {
            // Adjust the query based on time and timezone
            $query = $query->applyTimezoneAndTime();
        }

        // Fetch all servers and group them by cloud provider or assign to "Other Providers"
        $servers = $query->with('cloudProvider')->get()->groupBy(function ($server) {
            return $server->cloudProvider ? $server->cloudProvider->provider->value : EnumsCloudProvider::ANY->value;
        });

        // Initialize counts array
        $counts = [];

//        // Handle merged xCloud providers count
//        $xCloudCount = 0;
//        foreach (EnumsCloudProvider::xCloudManaged() as $xCloudProvider) {
//            $xCloudCount += $servers->get($xCloudProvider->value, collect())->count();
//        }
//        $counts['xCloud'] = $xCloudCount;

        // Count for individual providers and "Other Providers"
        foreach (EnumsCloudProvider::cases() as $providerEnum) {
            //$providerName = $providerEnum->getProviderReadableAttribute();
            if (!(in_array($providerEnum, EnumsCloudProvider::underXCloud())
                || $providerEnum == EnumsCloudProvider::WHITE_LABEL_VULTR)) {
                $counts[$providerEnum->value] = $servers->get($providerEnum->value, collect())->count();
            }
        }

        // Include "Other Providers" count
        // $counts['Other Providers'] = $servers->get('Other Providers', collect())->count();

        return $counts;
    }

    public static function findServersAndMissingIds(array $ids): array
    {
        $foundServers = self::whereIn('id', $ids)->get(['id']);

        // Extracting the IDs of found servers
        $foundIds = $foundServers->pluck('id')->all();

        // Determining which of the requested IDs were not found and returning them
        return array_diff($ids, $foundIds);
    }

    public function provideService(BillingServices $billingService): bool
    {
        $this->log('Service provided for server # '.$this->id);

        if ($this->provisioningJobDispatched()) {
            $this->log('Service is already provisioned. No need to provision again.');
        } else {
            $this->log('Service is not provisioned yet {provisioning_job_dispatched_at} is null. Provisioning now...');
            $this->provision();
        }

        return true;
    }

    public function cancelService(BillingServices $billingService): bool
    {
        return true;
    }

    public function serviceIsActive(BillingServices $billingService): bool
    {
        return $this->isProvisioned();
    }


    public function getTimezone()
    {
        $timezone = $this->runInline(new InlineScript('timedatectl | grep "Time zone" | awk \'{print $3}\''))->output;
        if ($timezone && $timezone !== $this->time_zone) {
            $this->update([
                'time_zone' => $timezone
            ]);
        }
        return $timezone;
    }

    public function doesUserExist($user):?bool
    {
        return $this->runInline(new FetchServerUsers(server: $this,user: $user))->output === $user;
    }

    public function installCronJob(CronJob $cronJob): bool
    {
        $runner = $cronJob->site_id ? $cronJob->site : $this;
        return $runner->runInline(new InstallCronJob($this, $cronJob))->successful();
    }
    public function removeCronJob(CronJob $cronJob): bool
    {
        $runner = $cronJob->site_id ? $cronJob->site : $this;
        return $runner->runInline(new RemoveCronJob(server: $this, cronJob: $cronJob))->successful();
    }

    public function getCronJobOutput(CronJob $cronJob): string
    {
        $runner = $cronJob->site_id ? $cronJob->site : $this;
        return $runner->runInline(new GetCronJobOutput(server: $this,cronJob: $cronJob))->output;
    }

    public function removeMagicLogin()
    {
        return $this->runInline(new RemoveMagicLogin(server: $this))->output;
    }

    public function isMagicLoginEnabled(): bool
    {
        return $this->getMeta('has_magic_login',self::DEFAULT_MAGIC_LOGIN);
    }

    public function firewallRules(): HasMany
    {
        return $this->hasMany(FirewallRule::class);
    }

    public function getLsPhpVersionAttribute(): string
    {
        return str_replace('.', '', $this->php_version);
    }

    /**
     * Retrieve a specific server modification data by key.
     *
     * @param  ServerModificationActions $serverModificationAction
     * @return array|null
     */
    public function getServerModificationData(ServerModificationActions $serverModificationAction): ?array
    {
        return Arr::get($this->meta, "serverModification.{$serverModificationAction->value}");
    }

    /**
     * Delete a specific server modification data by key if it exists.
     *
     * @param ServerModificationActions $serverModificationAction
     * @return void
     */
    public function deleteServerModificationData(ServerModificationActions $serverModificationAction): void
    {
        // Retrieve the current meta data as an array
        $meta = $this->meta;

        // Use Arr::get to check if the server modification data exists
        if (!is_null(Arr::get($meta, "serverModification.{$serverModificationAction->value}"))) {
            // Use Arr::forget to remove the specified key from the meta array copy
            Arr::forget($meta, "serverModification.{$serverModificationAction->value}");

            // Reassign the modified array back to the model's meta property
            $this->meta = $meta;

            // Save the model to persist changes
            $this->save();
        }
    }

    public function billingStartedFrom(): Carbon
    {
        return $this->created_at;
    }
    public function isVulnerabilityScanEnable(): bool
    {
       return $this->vulnerabilitySetting()->exists();
    }

    public function vulnerabilitySetting(): HasOne
    {
        return $this->hasOne(VulnerabilitySetting::class);
    }

    public function getAllSiteUpdate(): void
    {
        $this->runInBackground(new FetchSiteUpdates($this));
    }

    public function getBillingName(BillingServices $service): string
    {
        if ($this->cloudProvider?->provider?->isUnderXcloud()) {
            return $this->name;
        }

        if (!$this->cloudProvider?->provider) {
            return $this->name;
        }

        return $this->name . ' ('. $this->cloudProvider?->provider?->getProviderReadableAttribute() . ')';
    }

    public function getBillingShortDescriptionTitle(BillingServices $service): string
    {
        return 'IP';
    }

    public function getBillingShortDescription(BillingServices $service): string
    {
        if ($service->isUnderServerBackup()) {
            $monitorInfo = $this->getServerMonitoringInfo();

//            $storageInfo = Arr::get($monitorInfo, 'disk.used', 0) && Arr::get($monitorInfo, 'disk.total', 0) ?
//                Arr::get($monitorInfo, 'disk.used', 0) . ' GB of ' . Arr::get($monitorInfo, 'disk.total', 0) . ' GB storage used' : '';

            $storageInfo = Arr::get($monitorInfo, 'disk.total', 0) ? intval(Arr::get($monitorInfo, 'disk.total', 0)) . ' GB Storage' : '';

            return $this?->public_ip ? $this?->public_ip. ($storageInfo ? '; ('.$storageInfo.')' : '') : '';
        }

        if (!$this->getSize()) {
            return $this?->public_ip ? $this?->public_ip : '';
        }

        return $this?->public_ip ? $this?->public_ip. ($this->sizeShortDescription($this->getSize()) ?
                '; ('.$this->sizeShortDescription($this->getSize()).')' : '') : '';
    }

    public function hasUpgradeablePackages(): bool
    {
        return count($this->getServerInfo('upgradable_packages', [])) > 0;
    }

    public function doesRebootRequire(): bool
    {
        return (bool) $this->reboot_required;
    }

    public function upgradePackages(): void
    {
        #remove upgrade packages from server_info json field
        $this->update([
            'server_info' => Arr::except($this->server_info, ['upgradable_packages','reboot_require']),
            'reboot_required' => false,
        ]);
        $this->runInline(new UpgradePackages(server: $this));
    }

    public function setupSecurityUpdate(array $data): void
    {
        $this->runInline(new SetupAutoSecurityUpdate(server: $this, data: $data));
    }

    public function whiteLabel()
    {
        return $this->team->whiteLabel;
    }

    public function hasWhiteLabelAccess(?User $user = null): bool
    {
        $user = $user ?? user(); // Use provided user or fallback to the global `user()` function
        $whiteLabel = $this->whiteLabel(); // Cache whiteLabel to avoid repeating the same call

        // Early return if whiteLabel doesn't exist
        if (!$whiteLabel) {
            return false;
        }

        // Check if the user belongs to the team of the whiteLabel owner
        return $user->belongsToTeam($whiteLabel->owner);
    }

    public function ipAddresses(): MorphMany
    {
        return $this->morphMany(IpAddress::class, 'ipable');
    }

    public function whitelistedIps(): MorphMany
    {
        return $this->ipAddresses()->where('is_whitelist', true);
    }

    public function blacklistedIps(): MorphMany
    {
        return $this->ipAddresses()->where('is_whitelist', false);
    }

    public function getFreeSpaceInByte()
    {
       return $this->runInline(new InlineScript('df -B1 / | tail -n 1 | awk \'{print $4}\''))?->output;
    }

    public function updateSourceInvoice(array $invoice): void
    {
        $startDate = Arr::get($invoice, 'startDate');
        $total = Arr::get($invoice, 'total');
        $backupStartDate = Arr::get($invoice, 'backup.startDate');
        $backupTotalPrice = Arr::get($invoice, 'backup.total');

        $this->bills()
            ->whereMonth('bill_from',Carbon::parse($startDate)->month)
            ->whereIn('service',[BillingServices::xCloudManagedHosting,BillingServices::xCloudProviderHosting])
            ->where('renewal_period',BillRenewalPeriod::Monthly)
            ->update(['source_bill_amount' => $total]);

        if ($backupTotalPrice) {
            $this->bills()
                ->whereMonth('bill_from',Carbon::parse($backupStartDate)->month)
                ->whereIn('service',[BillingServices::BackupXCloudProviderHosting,BillingServices::BackupXCloudManagedHosting,BillingServices::BackupManagedHosting])
                ->where('renewal_period',BillRenewalPeriod::Monthly)
                ->update(['source_bill_amount' => $backupTotalPrice]);
        }
    }

    public function fetchCronJobs(): Task
    {
        return $this->runInline(new FetchCronJobsScript(server:$this));
    }

    public function autoDisableFileManagerAdminer($sites, Team $team): void
    {
        $adminerSites = [];
        $fileMangerSites = [];
        $files = $sites->flatMap(function (Site $site) use ($team,&$adminerSites,&$fileMangerSites) {
            $adminerTime = $site->getMeta('adminer_enable_time');
            $fileManagerTime = $site->getMeta('file_manager_enable_time');
            $paths = [];
            if ($team->canDisableAdminer($adminerTime)) {
                $adminerSites[] = $site->id;
                $paths[] = $site->getAdminerPath();
            }
            if ($team->canDisableFileManager($fileManagerTime)) {
                $fileMangerSites[] = $site->id;
                $paths[] = $site->getFileMangerPath();
            }
            //filters paths that are null or not ending with .php
            return array_filter($paths, fn($path) => $path && Str::endsWith($path, '.php'));
        })->toArray();

        if (count($files)>0){
            RemoveFilesJob::dispatch($this, $files, $adminerSites, $fileMangerSites);
        }
    }

    public function removeFiles(array $files): Task
    {
        return $this->runInline(new RemoveFileScript($this, $files));
    }
}
