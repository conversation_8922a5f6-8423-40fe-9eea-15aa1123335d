<?php

namespace App\Models;

use App\Enums\EmailProviderInfoEnum;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Services\Integrations\ElasticEmailService;
use App\Services\PaymentGateway\BillingServices\EnsurePaymentServiceProvided;
use App\Traits\Billable;
use App\Traits\XcSoftDelete;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Mailgun\Mailgun;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class EmailProvider extends Model implements EnsurePaymentServiceProvided, CipherSweetEncrypted
{
    use Billable, XcSoftDelete, HasFactory, UsesCipherSweet;

    protected $guarded = [
        'id'
    ];

    protected $casts = [
        'provider' => \App\Enums\EmailProvider::class,
        'plan' => xCloudEmailProviderPlanEnum::class,
    ];

    protected $hidden = [
      //'api_key',
      'password'
    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('api_key');
        $encryptedRow->addField('password');
    }


    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function sites(): HasMany
    {
        return $this->hasMany(Site::class);
    }

    public function getHost() {
      return $this->provider === \App\Enums\EmailProvider::MAILGUN ? EmailProviderInfoEnum::MAILGUN_HOST->value : $this->host;
    }
    public function getPort() {
      return $this->provider === \App\Enums\EmailProvider::MAILGUN ? EmailProviderInfoEnum::PORT_587->value : $this->port;
    }

    public function getUsername() {
        return $this->username ?? '';
    }

    public function getPassword() {
        return $this->password ?? '';
    }

    public function getExtraSettings(): array
    {
        if($this->provider === \App\Enums\EmailProvider::MAILGUN || $this->provider === \App\Enums\EmailProvider::OTHER) {
            return [
                'host' => $this->getHost(),
                'port' => $this->getPort(),
                'username' => $this->getUsername(),
                'password' => $this->getPassword(),
                'provider' => 'smtp'
            ];
        }
        if ($this->provider === \App\Enums\EmailProvider::SENDGRID) {
            return [
                'provider' => strtolower(\App\Enums\EmailProvider::SENDGRID->value),
                'host' => $this->getHost(),
                'port' => $this->getPort(),
                'username' => $this->getUsername(),
                'password' => $this->getPassword(),
                'encryption' => 'tls',
            ];
        }
        if ($this->provider === \App\Enums\EmailProvider::MAILGUN_API){
            return [
                'provider' =>  'mailgun',
                'region' => 'us'
            ];
        }
        return [];
    }

    public function setHostAndPort(): void
    {
        if ($this->provider === \App\Enums\EmailProvider::MAILGUN) {
            $this->update([
                'host' => EmailProviderInfoEnum::MAILGUN_HOST->value,
                'port' => EmailProviderInfoEnum::PORT_587->value,
            ]);
        }else if ($this->provider === \App\Enums\EmailProvider::SENDGRID) {
            $this->update([
                'host' => EmailProviderInfoEnum::SENDGRID_HOST->value,
                'port' => EmailProviderInfoEnum::PORT_587->value,
            ]);
        }
    }

    public function getDefaultBillingService(): BillingServices
    {
        return BillingServices::EmailProvider;
    }

    public function getPricing(BillingServices $service, BillRenewalPeriod $renewalPeriod = null, Product|Package|SubscriptionProduct $billThrough = null, float $defaultPrice = 0): float
    {
        return $defaultPrice; // email provider price will be on product
    }

    public function getAdditionalUsageChargeForCurrentPeriod(Bill $relatedOrExistingBill = null, Product|Package|SubscriptionProduct $billThrough = null): float
    {
        return 0;
    }

    public function getAdditionalUsageDetailsAsComment(): string
    {
        return 'Additional email usage charges for this period:';
    }

    public function getAdditionalUsageLog(): array
    {
        return [];
    }

    public function resetLastAdditionalUsageChargeAndComment(Bill $addedOnBill): void
    {
//        $this->update([
//            'meta->last_additional_usage_charge' => 0,
//            'meta->last_additional_usage_comment' => null
//        ]);
    }

    public function getBillingTitle(BillingServices $service, Bill $relatedOrExistingBill = null): string
    {
        return 'xCloud Managed Email Service';
    }

    public function getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator(): array
    {
        return [
            'id',
            'provider_type',
            'host',
            'port',
            'label'
        ];
    }

    public function getColumnsToStoreDataOnBill(): array
    {
        return [
            'id',
            'provider_type',
            'host',
            'port',
            'label'
        ];
    }

    public function teamInfo(): Team
    {
        return $this->team;
    }

    public function serviceIsProvidedAfterPayment(BillingServices $billingService): bool
    {
        // get all xcloud managed email provider for this team. Combine their email limit.
        // check if his subaccount has enough limit as his combined email provider limit.

        $xcloudManagedEmailCount = $this->team->xcloudEmailProviders()->get()->sum('email_limit');
        $subAccountApi = $this->team->getElasticEmailSubAccountApiKey();
        $subAccountEmail = Arr::get($this->team->meta, 'elastic_email.subAccount.emailOnSubaccount');

        if($xcloudManagedEmailCount > 0 && $subAccountApi && $subAccountEmail) {
            // get email limit of subaccount
            $emailBalances = Arr::get((new ElasticEmailService(config('services.elastic_email.api_key')))->getSubAccount(email: $subAccountEmail), 'EmailCredits');
            if($emailBalances < $xcloudManagedEmailCount) {
                return false;
            }
        }
        return true;
    }

    public function onPaymentSuccessProvideService(BillingServices $billingService): bool
    {
        // top up email limit of subaccount
        $this->team->updateElasticEmailSubAccountCredit($this->plan, $this->email_limit);

        return $this->serviceIsProvidedAfterPayment(BillingServices::EmailProvider);
    }

    public function cancelPaymentService(BillingServices $billingService): bool
    {
        if($this->provider === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER){
            // delete xcloud email provider on elastic email
            $this->team->subtractCreditFromSubaccount(credit: -$this->email_limit);
        }

        return true;
    }

    public function serviceIsActive(BillingServices $billingService): bool
    {
        // check if his API key is valid
        if($this->provider === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER) {
            $accountDetails = (new ElasticEmailService($this->api_key))->getAccountDetails();
            if(Arr::get($accountDetails, 'success') === false) {
                return false;
            }
        }

        return true;
    }

    public function billingStartedFrom() : Carbon
    {
        return $this->created_at;
    }

    public function getBillingName(BillingServices $service): string
    {
        return $this->label;
    }

    public function getBillingShortDescriptionTitle(BillingServices $service): string
    {
        return 'Limit';
    }

    public function getBillingShortDescription(BillingServices $service): string
    {
        return $this->email_limit . ' emails';
    }

    public function getProductLimitation(string $className, BillingServices $service = null): float|null
    {
        // TODO: Implement getProductLimitation() method.
    }
}
