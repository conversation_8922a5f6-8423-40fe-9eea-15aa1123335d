<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;

class BluePrint extends Model
{
   protected $casts = [
        'theme' => 'array',
        'plugins' => 'array',
        'created_at' => 'datetime:Y-m-d H:i A',
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function getTheme(): string
    {
        return Arr::get($this->theme, 'slug');
    }

    public function getPlugins(): array
    {
        return Arr::pluck($this->plugins, 'slug');
    }
}
