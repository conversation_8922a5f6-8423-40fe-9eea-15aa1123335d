<?php

namespace App\Models;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Jobs\CreateServerFromCart;
use App\Services\PaymentGateway\BillingServices\CanGenerateInvoice;
use App\Services\PaymentGateway\BillingServices\EnsurePaymentServiceProvided;
use App\Traits\MetaAccessors;
use App\Traits\XcSoftDelete;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartForm extends Model implements EnsurePaymentServiceProvided, CanGenerateInvoice
{
    use HasFactory, XcSoftDelete, MetaAccessors;

    protected $table = 'cart_forms';

    protected $casts = [
        'form' => 'array',
        'resource' => 'array',
        'meta' => 'array',
        'user_session' => 'array',
        'should_expire_on' => 'date',
        'status' => CartFormStatuses::class,
        'service' => BillingServices::class
    ];

    protected $guarded = [
        'id',
        'package_id',
        'product_id',
        'subscription_product_id'
    ];

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function subscriptionProduct(): BelongsTo
    {
        return $this->belongsTo(SubscriptionProduct::class);
    }

    public function createServer(): void
    {
        if ($this->status->isNot(CartFormStatuses::Paid)) {
            return;
        }
        $this->update([
            'status' => CartFormStatuses::Processing,
        ]);
        CreateServerFromCart::dispatch($this);
    }

    public function setStatusPaymentProcessing(): void
    {
        $this->update([
            'status' => CartFormStatuses::PaymentProcessing,
        ]);
    }

    public function setStatusPaymentFailed(): void
    {
        $this->update([
            'status' => CartFormStatuses::PaymentFailed,
        ]);
    }

    public function setStatusPaid(): void
    {
        $this->update([
            'status' => CartFormStatuses::Paid,
        ]);
    }

    public function setStatusCompleted(): void
    {
        $this->update([
            'status' => CartFormStatuses::Completed,
        ]);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(GeneralInvoice::class);
    }

    public function serviceProvidedAfterPayment(BillingServices $billingService): bool
    {
        return true;
    }

    public function provideService(BillingServices $billingService): bool
    {
        return true;
    }

    public function cancelService(BillingServices $billingService): bool
    {
        return true;
    }

    public function serviceIsActive(BillingServices $billingService): bool
    {
        return true;
    }

    public function getBillableAmount()
    {
        if ($this->invoice->source === InvoiceSourceEnum::CartFormPurchase) {
            return $this->package_id ? $this->package->price : $this->product->price;
        }
        return  $this->bills()->sum('amount_to_pay');

    }

    public function getGetNameForInvoiceAttribute(): string
    {
        if ($this->subscriptionProduct) {
            $subscriptionProduct = $this->subscriptionProduct;

            return $subscriptionProduct->unit.' '.substr($subscriptionProduct->service_model, strrpos($subscriptionProduct->service_model, '\\') + 1);
        }

        return '';
    }

    public function getGetTitleForInvoiceAttribute(): string
    {
        if ($this->product) {
            return $this->product->title;
        }

        if ($this->package) {
            return $this->package->name;
        }

        if ($this->subscriptionProduct) {
            $subscriptionProduct = $this->subscriptionProduct;

            return '$'.$subscriptionProduct->price . '/' .$subscriptionProduct->renewal_period->value;
        }

        return '';
    }

    public function getGetShortDescriptionTitleForInvoiceAttribute(): string
    {
        if ($this->subscriptionProduct) {
            return $this->subscriptionProduct->name;
        }

        return '';
    }

    public function getGetShortDescriptionForInvoiceAttribute(): string
    {
        if ($this->subscriptionProduct) {
            return $this->subscriptionProduct->short_description;
        }

        return '';
    }
}
