<?php

namespace App\Models;

use App\Enums\NotificationIntegrationStatus;
use App\Enums\NotificationIntegrationTypes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use ParagonIE\CipherSweet\EncryptedRow;
use <PERSON><PERSON>\LaravelCipherSweet\Concerns\UsesCipherSweet;
use <PERSON><PERSON>\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class NotificationIntegration extends Model implements CipherSweetEncrypted
{
    use HasFactory, UsesCipherSweet;

    protected $guarded = [];

    protected $casts = [
        'meta' => 'array',
        'type' => NotificationIntegrationTypes::class,
        'status' => NotificationIntegrationStatus::class
    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('token');
    }

    public function service()
    {
        $serviceClass = $this->type->getServiceClass();

        return new $serviceClass($this);
    }
}
