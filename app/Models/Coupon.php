<?php

namespace App\Models;

use App\Enums\CloudProviderEnums;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CouponDiscountType;
use App\Enums\XcloudBilling\CouponType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Coupon extends Model
{
    use HasFactory;

    protected $casts = [
        'name' => 'string',
        'description' => 'string',
        'discount_type' => CouponDiscountType::class,
        'discount' => 'float',
        'code' => 'string',
        'type' => CouponType::class,
        'services' => 'array',
        'added_by' => 'integer',
        'max_usage_limit' => 'integer',
        'is_active' => 'boolean',
        'valid_only_package_id' => 'integer',
        'valid_only_billing_service' => BillingServices::class,
        'valid_only_cloud_provider' => CloudProviderEnums::class,
        'valid_only_product_id' => 'integer',
        'valid_only_on_plan' => 'integer',
        'valid_from' => 'datetime',
        'valid_till' => 'datetime',
    ];

    public function scopeActive($query) : Builder
    {
        return $query->where('is_active', true);
    }

    public function addedBy() : BelongsTo
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    public function isUsable(Team $team) : bool
    {
        if ($this->max_usage_limit !== -1 && $this->generalInvoices()->count() >= $this->max_usage_limit) {
            return false;
        }

        if ($this->max_usage_per_team !== -1 && $this->generalInvoices()->where('team_id', $team->id)->count() >= $this->max_usage_per_team) {
            return false;
        }

        return true;
    }

    public function validForPackage() : BelongsTo
    {
        return $this->belongsTo(Package::class, 'valid_only_package_id');
    }

    public function validForProduct() : BelongsTo
    {
        return $this->belongsTo(Product::class, 'valid_only_product_id');
    }

    public function validOnlyOnPlan() : BelongsTo
    {
        return $this->belongsTo(BillingPlan::class, 'valid_only_on_plan');
    }

    public function generalInvoices() : HasMany
    {
        return $this->hasMany(GeneralInvoice::class, 'coupon_id');
    }

    public function isNotExpired(): bool
    {
        $now = now();

        if ($this->valid_till === null) {
            return true;
        }

        if ($this->valid_from !== null && $now->lt($this->valid_from)) {
            return false;
        }

        return $now->lte($this->valid_till);
    }
}
