<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class BackupFile extends Model
{
    protected $casts = [
        'is_sql' => 'boolean',
        'is_remote' => 'boolean',
    ];

    const FULL_BACKUP = 'full';
    const INCREMENTAL_BACKUP = 'incremental';
    const INCREMENTAL_FULL = 'incremental_full';

    const TEMPORARY_URL_EXPIRATION =  24; // 24 hours

    public function backupSetting()
    {
        return $this->belongsTo(BackupSetting::class);
    }

    public function storageProvider()
    {
        return $this->belongsTo(StorageProvider::class);
    }

    public function user() {
        return $this->belongsTo(User::class);
    }

    public static function getBackUpDomain($file): string|null
    {
        $pattern = '/([a-z0-9-]+(?:\.[a-z0-9-]+)*)\.(?:[a-z]{2,})(?:\.[a-z]{2,})?/i';
        if (preg_match($pattern, $file, $matches)) {
            return $matches[0];
        }
        return null;
    }

    public static function backupProfile($file): string|null
    {
        $domain = self::getBackUpDomain($file);
        if ($domain){
          return  str_replace('.', '-', $domain);
        }
        return null;
    }

    /**
     * @throws \Exception
     */
    public function download(string $dirName): string
    {
        if ($this->storageProvider->isGDrive()){
            return $this->downloadFromGDrive();
        }
        if ($this->storageProvider->isPcloud()){
            return $this->downloadFromPcloud();
        }
        $fileData = [
            $this->file_path,
            $this->server_datetime ?? now()->format('Y-m-d H:i:s'),
            $this->is_sql ? 'sql' : 'tar.gz'
        ];
        $fileName = implode('.', $fileData);
        return StorageProvider::storage($this->storageProvider->getConfig())->temporaryUrl(
            path: $this->file_path . DIRECTORY_SEPARATOR . $this->file_name,
            expiration: now()->addHours(self::TEMPORARY_URL_EXPIRATION),
            options: [
                'ResponseContentDisposition' => 'attachment; filename="'.$fileName.'"',
            ]
        );
    }

    /**
     * @throws \Exception
     */
    private function downloadFromGDrive(): string
    {
        return $this->storageProvider->generateDownloadLink(file: $this);
    }

    private function downloadFromPcloud(): string
    {
        return $this->storageProvider->downloadPcloudFile($this->file_path);

    }


    private function getFullPath(): string
    {
        if (Str::contains($this->file_path, $this->file_name)) {
            return $this->file_path;
        }
        return $this->file_path .DIRECTORY_SEPARATOR. $this->file_name;
    }

    public function isIncrementalBackup(): bool
    {
        return in_array($this->type,[
            self::INCREMENTAL_BACKUP,
            self::INCREMENTAL_FULL
        ]);
    }
}
