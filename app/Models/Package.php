<?php

namespace App\Models;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\CouponDiscountType;
use App\Enums\XcloudBilling\ProductSources;
use App\Traits\BelongsToWhiteLabel;
use App\Traits\XcSoftDelete;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;

class Package extends Model
{
    use HasFactory, XcSoftDelete, BelongsToWhiteLabel;

    protected $table = 'packages';

    protected $guarded = [
        'id'
    ];

    protected $casts = [
        'services' => 'array',
        'unit' => 'integer',
        'price' => 'float',
        'requires_billing_plan' => 'integer',
        'start_from' => 'date',
        'expire_at' => 'date',
        'renewal_type' => BillRenewalPeriod::class,
        'service_type' => BillingServices::class,
        'expected_product_source' => ProductSources::class,
        'currency' => BillingCurrency::class,
        'required_purchase_any_of_packages' => 'array',
        'required_purchase_any_of_products' => 'array',
        'is_deleted' => 'boolean',
    ];

   protected static function booted()
   {
       // static::addGlobalScope('notExpired', function (Builder $builder) {
       //     $builder->where('expire_at', '>', now()->toDateString());
       // });

         static::saving(function ($package) {
            $package->required_purchase_any_of_products = array_map('intval', $package->required_purchase_any_of_products ?: []);
            $package->required_purchase_any_of_packages = array_map('intval', $package->required_purchase_any_of_packages ?: []);
         });
   }

    public function getDescriptionAttribute($value): string|array|null
    {
        //if route is cart.checkoutWithPackage
        if (request()->routeIs('cart.checkoutWithPackage')) {
            return $value ? explode(PHP_EOL, $value) : [];
        }
        return $value;
    }

    public function coupons() : HasMany
    {
        return $this->hasMany(Coupon::class, 'valid_only_package_id');
    }

    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_package', 'package_id')
            ->using(TeamPackage::class)
            ->withPivot('id', 'attached_at');
    }

    public function bills(): HasMany
    {
        return $this->hasMany(Bill::class);
    }

    public function scopeNotExpired($query)
    {
        return $query->where('expire_at', '>', now()->toDateString());
    }

    public function hasBillingPlan() : bool
    {
        return $this->requires_billing_plan !== null;
    }

    public function requiredBillingPlan(): BelongsTo
    {
        return $this->belongsTo(BillingPlan::class, 'requires_billing_plan', 'id');
    }

    public function cartForms(): HasMany
    {
        return $this->hasMany(CartForm::class);
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function isExpired(): bool
    {
        return $this->expire_at && $this->expire_at->isPast();
    }

    public function isNotExpired(): bool
    {
        return !$this->isExpired();
    }

    public function scopeActive($query)
    {
        return $query->where('start_from', '<=', now()->toDateString())->where('expire_at', '>=', now()->toDateString());
    }

    public function filterPriceByCoupon(string $coupon) : self
    {
        $coupon = Coupon::where('code', $coupon)->active()->first();

        if ($coupon) {

            if (($coupon->max_usage_limit !== -1) && ($coupon->generalInvoices()->count() >= $coupon->max_usage_limit)) {
                return $this;
            }

            $this->original_price = $this->price;
            $price = $coupon->discount_type == CouponDiscountType::Percentage ? $this->price - ($this->price * $coupon->discount / 100) : $this->price - $coupon->discount;
            if ($price < 0) {
                $price = 0;
            }
            if ($coupon->valid_only_package_id && $coupon->valid_only_package_id != $this->id) {
                $price = $this->original_price;
            }
            $this->price = format_billing($price);

            if ($price !== $this->original_price) {
                $this->applied_coupon = $coupon->id;
            }
        }

        return $this;
    }
}
