<?php

namespace App\Models;

use App\Enums\IPAddressType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class IpAddress extends Model
{
    use HasFactory;

    protected $casts = [
       'type' => IPAddressType::class,
    ];
    public function ipable(): MorphTo
    {
        return $this->morphTo();
    }
}
