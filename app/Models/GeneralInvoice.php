<?php

namespace App\Models;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Services\PaymentGateway\InvoiceServices\Invoiceable;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

/**
 * @property mixed|null $invoice_number
 * @property mixed $affiliatedFrom
 * @property mixed $promoter_id
 */
class GeneralInvoice extends Invoiceable
{
    use HasFactory;

    public function __construct()
    {
        parent::__construct();

        static::addGlobalScope('general', function ($query) {
            $query->where('type', InvoiceTypesEnum::General);
        });

        static::creating(function ($model) {
            $model->type = InvoiceTypesEnum::General;
        });
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * @throws ApiErrorException
     */
    public function requestPayment(): PaymentIntent
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));

        $stripe = new StripeClient(config('services.stripe.secret_key'));

        $metadata = [
            'invoice_id' => $this->id,
            'invoice_reference_no' => $this->reference_no,
            'xcloud_team_id' => $this->team_id,
            'xcloud_payment_method_id' => $this->payment_method_id,
        ];

        $options = $this->team->whiteLabel ? [
            'stripe_account' => $this->team->whiteLabel->connectedAccount->stripe_account_id
        ] : [];

        $paymentIntent = $stripe->paymentIntents->update($this->gateway_invoice_or_intent_id, [
            'metadata' => $metadata,
        ], $options);

        // taking the payment
        return $paymentIntent->confirm();
    }

    public function requires3dSecureAuthentication(): bool
    {
        return Arr::get($this->meta, 'stripe.requires_3d_secure_authentication', false);
    }

    /**
     * @throws Exception
     */
    public static function create(array $attributes = []): Invoiceable
    {
        $invoice = new self();

        $invoice->canCreate();

        $invoice->fill($attributes);

        $invoice->save();

        return $invoice;
    }

    public function invoicePdfView(User $user): \Barryvdh\DomPDF\PDF
    {
        $fontsPath = storage_path('fonts');
        if (!file_exists($fontsPath)) {
            mkdir($fontsPath, 0755, true);
        }

        chmod($fontsPath, 0755);

        $this->load(['paymentMethod', 'cartForm']);

        $userMeta = [
            'customer_name' => Arr::get($user->meta, 'customer_name', ''),
            'address' => str_replace("\n", "</br>", Arr::get($user->meta, 'address', '')),
        ];

        $bills = $this->bills->groupBy(function ($bill) {
            return $bill->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        });

        $cartForms = $this->cartForm->groupBy(function ($cartForm) {
            return $cartForm->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        })->map(function ($cartFormGroup) {
            return $cartFormGroup->filter(function ($cartForm) {
                // Check if the cart form product/package id matches any bill's product/package id
                return !$this->bills->contains(function ($bill) use ($cartForm) {
                    return $bill->product_id === $cartForm->product_id || $bill->package_id === $cartForm->package_id;
                });
            });
        })->filter(function ($group) {
            // Ensure we only keep non-empty groups
            return $group->isNotEmpty();
        });;

        $cartFormAmount = 0;

        foreach ($cartForms as $cartForm) {
            foreach ($cartForm as $cart) {
                $cartFormAmount += $cart?->package?->price ?: $cart?->product?->price ?: $cart?->subscriptionProduct?->price;
            }
        }

        return Pdf::loadView('Invoice.general', [
            'invoice' => $this,
            'billsGroup' => $bills,
            'cartFormsGroup' => $cartForms,
            'isPaid' => $this->status->is(BillingStatus::Paid),
            'currentMonth' => Carbon::now()->format('F'),
            'cartFormAmount' => $cartFormAmount,
            'billableAmount' => ($this->bills->sum('amount_to_pay') + $cartFormAmount) ?: $this->amount,
            'paymentMethod' => $this->paymentMethod,
            'userMeta' => $userMeta
        ]);
    }

    /**
     * @throws Exception
     */
    public function renderInvoicePdf($user=null): string
    {
        $user = $user ?? $this->team->owner;
        $pdf =$this->invoicePdfView($user);
        $pdf->render();
        return $pdf->output();
    }


    /**
     * @throws ApiErrorException
     */
    public function switchToPaymentMethod(PaymentMethod $paymentMethod): void
    {
        $this->update([
            'payment_method_id' => $paymentMethod->id,
            'gateway_payment_method_id' => $paymentMethod->getPaymentMethod()->id
        ]);
    }

    public function totalInvoiceAmount()
    {
        $amount = $this->bills()->sum('amount_to_pay');
        if ($this->source === InvoiceSourceEnum::CartFormPurchase) {
            $this->cartForm->first(function ($cart) use(&$amount) {
                $amount =  $cart->getBillableAmount();
            });
        }
        return $amount;
    }

    /**
     * @throws Exception
     */
    public function fetchPaymentWebhookFromStripe() : PaymentIntent|null
    {
        $stripe = new StripeClient(config('services.stripe.secret_key'));

        if (!$this->gateway_invoice_or_intent_id) {
            throw new \Exception('No gateway_invoice_or_intent_id found for invoice # ' . $this->id);
        }

        $paymentIntent = null;
        $chargeId = null;

        try {
            if($this->team->whiteLabel){
                $paymentIntent = $stripe->paymentIntents->retrieve($this->gateway_invoice_or_intent_id, [], [
                    'stripe_account' => $this->team->whiteLabel->connectedAccount->stripe_account_id
                ]);
            }else{
                $paymentIntent = $stripe->paymentIntents->retrieve($this->gateway_invoice_or_intent_id);
            }

            $chargeId = $paymentIntent->charges->latest_charge;
        } catch (\Exception $e) {
            Log::error($this->invoice_number.' - Payment Intent Failure: '.$e->getMessage());
        }

        $charge = null;

        if($chargeId){
            try {
                if($this->team->whiteLabel){
                    $charge = $stripe->charges->retrieve($chargeId, [], [
                        'stripe_account' => $this->team->whiteLabel->connectedAccount->stripe_account_id
                    ]);
                }else{
                    $charge = $stripe->charges->retrieve($chargeId);
                }
            } catch (\Exception $e) {
                Log::error($this->invoice_number.' - Charge Failure: '.$e->getMessage());
            }
        }

        // Check the charge is refunded or not update the statuses accordingly
        if ($charge && $charge->refunded) {
            $refundedAmount = $charge->amount_refunded / 100; // Convert from cents to dollars
            $this->updateQuietly([
                'status' => BillingStatus::Refunded,
                'refunded_amount' => $refundedAmount
            ]);

            if ($this->bills->count() > 0) {
                $this->bills->each(function ($bill) {
                    $bill->updateQuietly([
                        'status' => BillingStatus::Refunded
                    ]);
                });
            }

            return $paymentIntent;
        }

        if ($paymentIntent && $paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED) {
            $this->updateQuietly([
                'status' => BillingStatus::Paid
            ]);

            if ($this->bills->count() > 0) {
                $this->bills->each(function ($bill) {
                    $bill->updateQuietly([
                        'status' => BillingStatus::Paid
                    ]);
                });
            }

            $this->ensureServiceProvided();
        } else {
            $this->update([
                'status' => BillingStatus::Failed,
            ]);

            $this->cancelProvidedServices();
        }

        return $paymentIntent;
    }

    public function scopeInvoiceSearchFilter($query, $search)
    {
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', '%' . $search . '%')
                    ->orWhere('title', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%')
                    ->orWhere('amount', 'like', '%' . $search . '%')
                    ->orWhere('status', 'like', '%' . $search . '%')
                    ->orWhere('customer_email', 'like', '%' . $search . '%')
                    ->orWhere('due_date', 'like', '%' . $search . '%')
                    ->orWhere('created_at', 'like', '%' . $search . '%');
            });
        }

        return $query;
    }

    public function isWhiteLabelPurchase(): bool
    {
        return $this->source === InvoiceSourceEnum::WhitelabelPurchase;
    }
}
