<?php

namespace App\Models;

use App\Enums\SiteMigrationStatus;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use JetBrains\PhpStorm\NoReturn;
use League\Flysystem\FilesystemException;

class SiteBackupRestoreMigration extends SiteMigration
{
    protected $table = 'site_migrations';
    const BACKUPS = 'backups';
    const SOURCE = 'source';

    public array $stepsToFill = [
        self::DESTINATION,
        self::DOMAINS,
        self::SOURCE,
        self::BACKUPS,
        self::SETTINGS,
        self::CONFIRM
    ];

    public array $multiStepHierarchy = [
        self::DESTINATION,
        self::DOMAINS,
        self::SOURCE,
        self::SETTINGS,
        self::BACKUPS,
        self::CONFIRM,
        self::PROCESSING
    ];

    protected static function booted(): void
    {
        static::creating(function ($siteMigration) {
            $siteMigration->type = self::BACKUP_RESTORE;
            $siteMigration->status = SiteMigrationStatus::FILLING;
            $siteMigration->auth_token = Str::random(32);
            $siteMigration->encryption_key = Str::random(32);
        });
    }


    public array $unModifiableSteps = [];

    public array $stepGetRouteNames = [
        self::DOMAINS => 'site.migrate.backup_restore.domains',
        self::SOURCE => 'site.migrate.backup_restore.source',
        self::BACKUPS => 'site.migrate.backup_restore.backups',
        self::SETTINGS => 'site.migrate.settings',
        self::CONFIRM => 'site.migrate.confirm',
    ];

    public array $stepPostRouteNames = [
        self::DOMAINS => 'api.site.migrate.store.domains.restore_backup',
        self::SOURCE => 'api.site.migrate.store.backup_restore.source',
        self::BACKUPS => 'api.site.migrate.store.backups',
    ];

    public array $stepIcons = [
        self::SOURCE => 'xc-file',
        self::BACKUPS => 'xc-backup-restore',
    ];

    public function isFilled($step) : bool
    {
        return Arr::exists($this->form, $step);
    }

    public function getStepKeyValue($step,$key)
    {
        return Arr::get($this->form, $step.'.'.$key);
    }
    public function getStepValue($step)
    {
        return Arr::get($this->form, $step);
    }

    public function isBucket(): bool
    {
        return !$this->isOtherServerLocalBackup() && $this->isFilled(self::SOURCE) && $this->getStepKeyValue(self::SOURCE,'import_type') === 'bucket';
    }

    /**
     * @throws \Exception
     */
    public function getBackups(?string $continuationToken=null): array
    {

        $bucket_id = $this->getStepKeyValue(self::SOURCE,'bucket_id');

        $storageProvider = StorageProvider::find($bucket_id);
        $domain = $this->getStepKeyValue(self::SOURCE,'domain_name');
        $directory = self::sanitizeDirectoryName($domain);
        if ($storageProvider->isGDrive()){
            $response =  $storageProvider->getGoogleDriveFiles(location: $directory,perPage: 20,nextPageToken: $continuationToken);
            $response['files'] = $storageProvider->filterGdriveFiles($response['files']);
            $response['directory'] =$directory;
            $response['isLastPage'] = !isset($response['nextPageToken']);
            $response['nextContinuationToken'] = $response['nextPageToken'] ?? null;
            return $response;
        }
        return $storageProvider->paginateFiles(location: $directory, perPage: 800, continuationToken: $continuationToken);
    }

    public function getStorageProvider(): ?StorageProvider
    {
        return match (true) {
            $this->isBucket() => StorageProvider::find($this->getStepKeyValue(self::SOURCE, 'bucket_id')),
            $this->isSourceBackupTypeRemote() => StorageProvider::whereHas('backupFiles', function ($query) {
                $query->whereIn('id', Arr::pluck($this->getStepKeyValue(self::BACKUPS, 'backup_files'), 'id'));
            })->first(),
            default => null,
        };
    }

    public function getSourceSite(): ?Site
    {
        $site_id = Arr::get($this->form, self::SOURCE.'.site_id');
        return $site_id ? Site::find($site_id) : null;
    }

    public function isDifferentServer(): bool
    {
        return $this->getSourceSite()?->server_id !== $this->site?->server_id;
    }

    public function isOtherServerLocalBackup(): bool
    {
        return Arr::get($this->form, self::SOURCE.'.backup_type') === 'local' && $this->isDifferentServer();
    }

    public function getBackupDirName(): ?string
    {
       $domain =$this->getSourceDomainName();
       return  str_replace('.', '-',$domain);
    }

    public function getSourceDomainName(): ?string
    {   if ($this->isImportTypeBucket()) {
            return Arr::get($this->form, self::SOURCE.'.domain_name');
        }
       return $this->getSourceSite()?->name;
    }
    public function getSshKeyPair(): ?SshKeyPair
    {
        $sshKeyPairId = $this->getMeta('ssh_keypair_id');
        return $sshKeyPairId && $this->isOtherServerLocalBackup() ? SshKeyPair::find($sshKeyPairId) : null;
    }
    public function isSourceBackupTypeRemote(): bool
    {
        return !$this->isOtherServerLocalBackup() && Arr::get($this->form, self::SOURCE.'.backup_type') === 'remote';
    }
    public function isImportTypeBucket(): bool
    {
        return !$this->isOtherServerLocalBackup() && Arr::get($this->form, self::SOURCE.'.import_type') === 'bucket';
    }

    public function siteBackupFiles(): LengthAwarePaginator|array|null
    {
        if ($this->isImportTypeBucket()) {
            return null;
        }
        return $this->getSourceSite()
            ->backupFiles()
            ->selectRaw('*,DATE_FORMAT(backup_files.server_datetime, "%d %b %Y %h:%i %p") as date_format, DATE_FORMAT(backup_files.date, "%d %b %Y %h:%i %p") as last_backup')
            ->whereNot('backup_files.status',BackupSetting::FAILED)
            ->where('backup_files.is_remote',$this->isSourceBackupTypeRemote())
            ->latest('backup_files.id')
            ->paginate(20);
    }

    /**
     * Sanitize the directory name by replacing dots with hyphens.
     *
     * @param string $value
     * @return string
     */
    public static function sanitizeDirectoryName(string $value): string
    {
        #remove https:// and http:// from the domain name if present
        $value = preg_replace('/^https?:\/\//', '', $value);
        #replace all dots with hyphens
        return str_replace('.', '-', $value);
    }

    public function getDependedFiles(array $file): array
    {
        $bucket_id = $this->getStepKeyValue(self::SOURCE,'bucket_id');
        $storageProvider = StorageProvider::find($bucket_id);
        $domain = $this->getStepKeyValue(self::SOURCE,'domain_name');
        $directory = self::sanitizeDirectoryName($domain);
        $manifestFile = $file['Key'];
        if (!Str::contains($file['Key'], 'duplicity-full.')) {
            $manifestFile = $storageProvider->getManifestFile(directory: $directory, file: $file);
        }
        $timestamp = preg_replace('/^.*duplicity-full\.(\d{8}T\d{6}Z)\.manifest$/', '$1', $manifestFile);

        $backups = $storageProvider->getIncrementalFullFiles($timestamp, $directory);
        if (!Str::contains($file['Key'], 'duplicity-full.')) {
            $incrementalBackups = $storageProvider->getIncrementalFiles($timestamp, $directory);
            $backups = array_merge($backups, $incrementalBackups);
        }
        return $backups;
    }

    public function getFileFromBucket($file)
    {
        $bucket_id = $this->getStepKeyValue(self::SOURCE,'bucket_id');
        $domain = $this->getStepKeyValue(self::SOURCE,'domain_name');
        $storageProvider = StorageProvider::find($bucket_id);
        $directory = self::sanitizeDirectoryName($domain);
        $result = $storageProvider->getSpecificFile(directory: $directory, file: basename($file));
        return $result ? Arr::first($result) : null;
    }

    public function backupFiles(): Collection
    {
        return BackupFile::whereIn('id',Arr::pluck($this->getStepKeyValue(self::BACKUPS,'backup_files'),'id'))->get();
    }

    public function selectedFiles() : ?array
    {
        return Arr::flatten($this->getStepKeyValue(self::BACKUPS,'backup_files'));
    }

    public function getSqlFileName() : ?string
    {
        if ($this->isImportTypeBucket()) {
            return collect($this->selectedFiles())
                ->filter(fn ($file) => Str::endsWith($file, '.sql'))
                ->first();
        }
        $file = $this->backupFiles()
            ->filter(fn ($file)=>$file->is_sql)
            ->first();
        return str($file->file_path)->append(DIRECTORY_SEPARATOR)->append($file->file_name);
    }

    public function backupFileFullPath($isSql): string
    {
        $file_name = $isSql ? $this->getSqlFileName() : $this->getNonSqlFileName();
        return expandPath($this->site->backupFileFullPath(path: '',file: $file_name,is_local: !($this->isBucket() || $this->isSourceBackupTypeRemote())));
    }
    public function getNonSqlFileName() : ?string
    {
        if ($this->isImportTypeBucket()) {
            return collect($this->selectedFiles())
                ->filter(fn ($file) => !Str::endsWith($file, '.sql'))
                ->first();
        }
        $file = $this->backupFiles()
            ->filter(fn ($file)=>!$file->is_sql)
            ->first();
        return str($file->file_path)->append(DIRECTORY_SEPARATOR)->append($file->file_name);
    }

    public function incrementalRestoreDateTime(): ?string
    {
        $filename = basename($this->getNonSqlFileName());
        $pattern = '/duplicity-(?:full|inc)\.(\d{8}T\d{6}Z)/';
        if (preg_match($pattern, $filename, $matches)) {
            // Parse the start date
            return Carbon::createFromFormat('Ymd\THis\Z', $matches[1], 'UTC')->format('Y-m-d\TH:i:sP');
        }
        return null;
    }

    public function hasIncrementalFiles(): bool
    {
        if ($this->isImportTypeBucket()) {
            return collect($this->selectedFiles())
                ->filter(fn ($file) => Str::contains($file, 'duplicity-'))
                ->isNotEmpty();
        }
        return $this->backupFiles()->whereIn('type',[BackupFile::INCREMENTAL_FULL,BackupFile::INCREMENTAL_BACKUP])->isNotEmpty();
    }

    public function getNonSqlSourceSize()
    {
        $domain = $this->getSourceDomainName();
        $file = $this->getNonSqlFileName();
        $pattern = '/^' . preg_quote($domain, '/') . '_s_(\d+)_\d+\.tar\.gz$/';
        if (preg_match($pattern, basename($file), $matches)) {
            return $matches[1] * 1024 * 1024;
        }
        $bucket_file = $this->getFileFromBucket($file);
        // approximate the size of the source file by ratio 4
        return $bucket_file['Size'] ?? $bucket_file['size'];
    }

    public  function convertToBytes($duplicitySize): float|int
    {
        // Remove extra spaces and split the value and unit
        $parts = explode(' ', trim($duplicitySize));

        // Extract value and unit
        $value = (float) $parts[0];
        $unit = strtolower($parts[1]);

        // Convert to bytes based on the unit
        return match ($unit) {
            'bytes' => $value,
            'kb' => $value * 1024,
            'mb' => $value * 1024 * 1024,
            'gb' => $value * 1024 * 1024 * 1024,
            'tb' => $value * 1024 * 1024 * 1024 * 1024,
            default => 0,
        };
    }

    public function getIncrementalFileSize()
    {
        $backup = $this->backupFiles()
            ->whereIn('type', [BackupFile::INCREMENTAL_FULL, BackupFile::INCREMENTAL_BACKUP])
            ->first();

        if ($backup->type === BackupFile::INCREMENTAL_FULL) {
            return $this->convertToBytes($backup->file_size);
        }

        $full_backup_id = $this->getSourceSite()->backupFiles()
            ->select('backup_files.id')
            ->where('backup_files.id', '<', $backup->id)
            ->where('backup_files.type', BackupFile::INCREMENTAL_FULL)
            ->where('backup_files.is_remote', $backup->is_remote)
            ->latest('backup_files.id')
            ->value('backup_files.id');

        return  $this->getSourceSite()->backupFiles()
            ->whereBetween('backup_files.id', [$full_backup_id, $backup->id])
            ->whereIn('backup_files.type', [BackupFile::INCREMENTAL_FULL, BackupFile::INCREMENTAL_BACKUP])
            ->where('backup_files.is_sql', false)
            ->where('backup_files.is_remote', $backup->is_remote)
            ->get()
            ->map(fn ($file) => $this->convertToBytes($file->file_size))
            ->sum();
    }



    public function getTotalRequireSizeInByte() : float|int
    {
        $size = 0;
        if ($this->isBucket()){
            if ($this->hasIncrementalFiles()){
               $backups = $this->getStepKeyValue('backups','incremental_files');
               $size += collect($backups)->sum('Size') * 4;
            }else{
                $size += $this->getNonSqlSourceSize();
            }
            $sqlFile = $this->getFileFromBucket($this->getSqlFileName());
            if ($sqlFile){
                $size += $sqlFile['Size'] ?? $sqlFile['size'];
            }
        }else{
            if ($this->hasIncrementalFiles()){
                $size += $this->getIncrementalFileSize() *4;
            }else{
                $taken_size =  $this->backupFiles()->where('is_sql',false)->sum('taken_size');
                // mb to bytes conversion
                $size += $taken_size * 1024 * 1024;
            }
          $sql_size =  $this->backupFiles()->where('is_sql',true)->value('file_size');
          $size += $sql_size  * 1024;
        }
        return $size;
    }
}
