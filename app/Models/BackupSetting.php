<?php

namespace App\Models;

use App\Enums\BackupType;
use App\Enums\BackupVersion;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Closure;

class BackupSetting extends Model
{
    use HasFactory;

    protected $casts = [
        'type' => BackupType::class,
        'version' => BackupVersion::class
    ];

    const RUNNING = 'running';
    const COMPLETED = 'completed';
    const PENDING = 'pending';

    const PROCESSED = 'processed';

    const FAILED = 'failed';

    const NEED_EXTERNAL_SPACE = 3072.0; // 3GB

    public function storageProvider()
    {
        return $this->belongsTo(StorageProvider::class);
    }

    public function backupFiles()
    {
        return $this->hasMany(BackupFile::class);
    }

    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    public function generateCronPattern(): string
    {
        $time = $this->generateRandomTime();
        return match ($this->auto_backup_frequency) {
            'weekly' => $this->weeklyBackupPattern($time),
            'monthly' => $this->monthlyBackupPattern($time),
            'yearly' => "{$time->minute} {$time->hour} {$time->day} {$time->month} *",
            default => "{$time->minute} {$time->hour} * * *",
        };
    }
    public function generateBackupCommand(): string
    {
        return match ($this->auto_backup_frequency) {
            'twelve_hours' => $this->dailyRandomPatternTwice(frequency: $this->auto_backup_frequency),
            'daily' => $this->dailyCommnad(frequency: $this->auto_backup_frequency),
            'weekly' => $this->weeklyCommnad(frequency: $this->auto_backup_frequency),
            'monthly' => $this->monthlyCommnad(frequency: $this->auto_backup_frequency),
            default => $this->dailyCommnad(frequency: $this->auto_backup_frequency),
        };
    }

    public function generateIncBackupCommand(): string
    {
        return match ($this->auto_incremental_frequency) {
            'twelve_hours' => $this->dailyRandomPatternTwice(frequency: $this->auto_incremental_frequency),
            'daily' => $this->dailyCommnad(frequency: $this->auto_incremental_frequency),
            'weekly' => $this->weeklyCommnad(frequency: $this->auto_incremental_frequency),
            'monthly' => $this->monthlyCommnad(frequency: $this->auto_incremental_frequency),
            default => $this->dailyCommnad(frequency: $this->auto_incremental_frequency),
        };
    }

    public function generateCronCommand(): string
    {
        $time = $this->generateRandomTime();
        return match ($this->auto_backup_frequency) {
            'weekly' => $this->weeklyBackupPattern($time),
            'monthly' => $this->monthlyBackupPattern($time),
            'yearly' => "{$time->minute} {$time->hour} {$time->day} {$time->month} *",
            default => "{$time->minute} {$time->hour} * * *",
        };
    }
    public function generateIncCronPattern(): string
    {
        $time = $this->generateRandomTime();
        return match ($this->auto_incremental_frequency) {
            'weekly' => $this->weeklyBackupPattern($time),
            'monthly' => $this->monthlyBackupPattern($time),
            'yearly' => "{$time->minute} {$time->hour} {$time->day} {$time->month} *",
            default => "{$time->minute} {$time->hour} * * *",
        };
    }

    public function getFrequencyCronFile(): string
    {
        return Str::of($this->auto_backup_frequency)
            ->when($this->is_local, fn ($string) => $string->append('Local'))
            ->when(!$this->is_local, fn ($string) => $string->append('Remote'))
            ->append('Backup.xc')
            ->toString();
    }
    public function getIncrementalFrequencyCronFile(): string
    {
        return Str::of($this->auto_incremental_frequency)
            ->when($this->is_local, fn ($string) => $string->append('Local'))
            ->when(!$this->is_local, fn ($string) => $string->append('Remote'))
            ->append('IncBackup.xc')
            ->toString();
    }

    public function getFileName($type=BackupFile::FULL_BACKUP){
        if ($this->version->isOne()){
            return $this->versionOneFileName($type);
        }
        return $type == BackupFile::FULL_BACKUP ? $this->getFrequencyCronFile() : $this->getIncrementalFrequencyCronFile();
    }

    public function versionOneFileName($type='full')
    {
        $file_name = $this->is_local ? "siteBackup_local.xc" : "siteBackup_remote.xc";
        if ($type !== 'full'){
            $file_name = $this->is_local ? "siteIncBackup_local.xc" : "siteIncBackup_remote.xc";
        }
        return $file_name;
    }

    public function generateDeleteCronPattern(): string
    {
        try{
            $time = $this->generateRandomTime()->addMinutes(5);
            return "{$time->minute} {$time->hour} * * *";
        }catch (\Exception $e){
            // if there is an error, return the default cron pattern
            return "5 0 * * *";
        }

    }

    public function weeklyBackupPattern($time): string
    {
        return  "{$time->minute} {$time->hour} * * ".rand(0,6);
    }

    public function monthlyBackupPattern($time): string
    {
        $randomDay = rand(1,28);
        return  "{$time->minute} {$time->hour} {$randomDay} * *";
    }

    public function generateRandomTime(): bool|Carbon
    {
       return Carbon::today()->addHours(rand(1, 23))->addMinutes(rand(1, 59));
    }

    public function deleteAfterDaysInLocal()
    {
       #minus 1 because we queried with -ctime +(days) ago
       return $this->delete_after_days -1;
    }

    public function isIncremental()
    {
        return $this->type->is(BackupType::INCREMENTAL);
    }

    public function incrementalFiles(BackupFile $backupFile, ?Closure $closure = null)
    {
        $next_full_backup =  $this->nextIncrementalFullBackup($backupFile);
        return $this
            ->backupFiles()
            ->whereNot('status', BackupSetting::FAILED)
            ->selectRaw('*,DATE_FORMAT(backup_files.server_datetime, "%d %b %Y %h:%i:%s %p") as date_format')
            ->when($closure, $closure)
            ->orderByDesc('id')
            ->when($next_full_backup, fn ($query) => $query->whereBetween('backup_files.id', [$backupFile->id, $next_full_backup->id]))
            ->when($next_full_backup, fn ($query) => $query->where('backup_files.id', '!=', $next_full_backup->id))
            ->when(!$next_full_backup, fn ($query) => $query->where('backup_files.id', '>=', $backupFile->id))
            ->get();
    }

    public function nextIncrementalFullBackup(?BackupFile $backupFile=null)
    {
       return $this->backupFiles()
           ->where('id','>',$backupFile->id)
            ->where(function ($query) use ($backupFile) {
                $query->where('type', BackupFile::INCREMENTAL_FULL)
                    ->orWhere(fn ($q) => $q->where(['type' => BackupFile::FULL_BACKUP,'is_sql' => false]));
            })
           ->orderBy('id')
           ->first();
    }


    public function isFull(): bool
    {
        return $this->type->is(BackupType::FULL);
    }

    public function getCommand($frequency)
    {
       return 'root find /home/<USER>/.xcloud-backup -type f -name "'.$frequency.'*Backup.xc" | sort -r | while read -r file; do sh -c "$file"; done';
    }

    public static function dailyRandomCron(): string
    {
        return rand(1, 59)." ".rand(1, 23)." * * *";
    }


    public function dailyCommnad($frequency)
    {
        return self::dailyRandomCron()." ".$this->getCommand(frequency:$frequency);
    }
    public function dailyRandomPatternTwice($frequency): string
    {
        // Generate random minute between 0 and 59
        $minute = rand(0, 59);

        // Generate random hour between 0 and 11
        $firstHour = rand(0, 11);

        // Calculate the second hour (12 hours later)
        $secondHour = $firstHour + 12;

        // Define the cron job command
        $cronCommand = $this->getCommand(frequency:$frequency);

        // Create the two cron job entries
        $firstCronJob = $minute . ' ' . $firstHour . ' * * * ' . $cronCommand;
        $secondCronJob = $minute . ' ' . $secondHour . ' * * * ' . $cronCommand;

        // Return the two cron job patterns
        return $firstCronJob . "\n" . $secondCronJob;
    }

    public function monthlyCommnad($frequency)
    {
       return rand(1, 59).' '.rand(1, 23).' '.rand(1, 28).' * *'." ".$this->getCommand(frequency:$frequency);
    }

    public function weeklyCommnad($frequency)
    {
       return rand(1, 59).' '.rand(1, 23).' * * '.rand(0, 6)." ".$this->getCommand(frequency:$frequency);
    }

    public function getIncrementalBackupFullPath(): string
    {
        if($this->is_local){
          return $this->site->incrementalFilesPath();
        }
        return $this->storageProvider->getS3EndPoint().DIRECTORY_SEPARATOR.$this->site->backupDirName();
    }

    public function getIncrementalBackupFullPathWithS3Flag(): string
    {
        if($this->is_local){
          return $this->site->incrementalFilesPath();
        }
        return $this->storageProvider->getEndPointWithS3Flag().DIRECTORY_SEPARATOR.$this->site->backupDirName();
    }

    public function getIncrementalFilePath(): string
    {
        if($this->is_local){
          return $this->site->incrementalPath();
        }
        return  $this->site->backupDirName();
    }
    public function getIncrementalMySqlPath(): string
    {
        if($this->is_local){
          return $this->site->incrementalPath();
        }
        return  $this->site->backupDirName();
    }

    public function isGDrive(): bool
    {
        if ($this->is_local){
            return false;
        }
        return $this->storageProvider->isGDrive();
    }
}
