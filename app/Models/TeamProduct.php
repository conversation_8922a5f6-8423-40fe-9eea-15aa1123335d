<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\Pivot;

class TeamProduct extends  Pivot
{
    protected $casts = [
        'attached_at' => 'datetime:Y-m-d H:i:s',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $product = Product::find($model->product_id);
            $team = Team::find($model->team_id);

            if ($product->required_purchase_any_of_packages) {
                $requiredPackages = Package::whereIn('id', $product->required_purchase_any_of_packages)->get();
                $teamPackages = $team->packages->pluck('id')->toArray();

                if (count(array_intersect($requiredPackages->pluck('id')->toArray(), $teamPackages)) === 0) {
                    throw new \Exception('You need to purchase one of the required packages to purchase this product. Please contact support for more information.');
                }
            }

            if ($product->required_purchase_any_of_products) {
                $requiredProducts = Product::whereIn('id', $product->required_purchase_any_of_products)->get();
                $teamProducts = $team->products->pluck('id')->toArray();

                if (count(array_intersect($requiredProducts->pluck('id')->toArray(), $teamProducts)) === 0) {
                    throw new \Exception('You need to purchase one of the required products to purchase this product. Please contact support for more information.');
                }
            }

            if ($product && $team->products) {
                $count = $team->products->where('id', $model->product_id)->count();

                $maxPurchaseLimit = $product->max_purchase_limit;

                if ($maxPurchaseLimit && $count >= $maxPurchaseLimit) {
                    throw new \Exception('You have reached the maximum purchase limit for this product.');
                }
            }

            if ($product->requires_billing_plan && $product->requires_billing_plan !== $team->active_plan_id) {
                throw new \Exception('You need to upgrade your billing plan to purchase this product.');
            }

            $model->attached_by = auth()?->id();

            $model->attached_at = Carbon::now();
        });
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function attachedBy()
    {
        return $this->belongsTo(User::class, 'attached_by');
    }

}
