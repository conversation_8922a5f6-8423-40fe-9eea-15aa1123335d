<?php

namespace App\Models;

use App\Callbacks\SiteBackRestoreUpdated;
use App\Contracts\Loggable;
use App\Contracts\ProvisionableContract;
use App\Contracts\SiteContract;
use App\Enums\CustomNginxEnum;
use App\Enums\PatchstackVulnerabilityStatus;
use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Enums\WPCachePlugins;
use App\Enums\XcloudBilling\BillingServices;
use App\Events\DomainUpdateFailed;
use App\Events\SiteDeleted;
use App\Events\SiteMigrationStatusChanged;
use App\Events\SiteMonitoringUpdated;
use App\Events\SiteProvisioned;
use App\Events\SiteProvisioningFailed;
use App\Events\SiteProvisioningStatusChanged;
use App\Jobs\Site\SiteBackupNow;
use App\Jobs\Site\TakeSiteBackupJob;
use App\Jobs\Site\UpdateVulnerabilitiesJob;
use App\Jobs\SiteProvision;
use App\Repository\Haiku;
use App\Scripts\CheckPluginIsActive;
use App\Scripts\GenerateNginxConfig;
use App\Scripts\GenerateOpenLiteSpeedConfig;
use App\Scripts\InlineScript;
use App\Scripts\Monitoring\DisableSiteMonitoring;
use App\Scripts\Monitoring\InstallSiteMonitoring;
use App\Scripts\Monitoring\PullSiteMonitoring;
use App\Scripts\Monitoring\ReInstallSiteMonitoring;
use App\Scripts\ReadSiteEnv;
use App\Scripts\ReadSiteHtAccess;
use App\Scripts\ReadSiteNginxConf;
use App\Scripts\ResetSiteUserPermission;
use App\Scripts\Site\BackupSite;
use App\Scripts\Site\CheckSiteStorage;
use App\Scripts\Site\DeactivateFullPageCache;
use App\Scripts\Site\DeletePatchstackScript;
use App\Scripts\Site\ExecuteDeployScript;
use App\Scripts\Site\FetchProductionSiteDatabaseTables;
use App\Scripts\Site\GetPhpVersionSettings;
use App\Scripts\Site\InstallBluePrintScript;
use App\Scripts\Site\InstallPatchstackScript;
use App\Scripts\Site\InstallSiteBackupScript;
use App\Scripts\Site\InstallWpCron;
use App\Scripts\Site\MonitorSiteCloneStorage;
use App\Scripts\Site\PullSiteUpdates;
use App\Scripts\Site\UpdateWpDebug;
use App\Scripts\Site\SiteSaltsShuffle;
use App\Scripts\Site\WpDebugChecker;
use App\Scripts\UpdateFileManagerCallback;
use App\Scripts\UpdateSiteEnv;
use App\Scripts\UpdateAdminerCallback;
use App\Scripts\UploadMagicLoginPlugin;
use App\Scripts\UploadCloudflareEdgeCachePlugin;
use App\Services\Database\DatabaseProvider;
use App\Services\Deleting\SiteDeleting;
use App\Services\Integrations\CloudflareService;
use App\Services\Nginx\SyncServerNginxVersion;
use App\Services\Provisioning\SiteProvisioning;
use App\Services\Site\CustomPhpSite;
use App\Services\Site\LaravelSite;
use App\Services\Site\MauticSite;
use App\Services\Site\SiteManager;
use App\Services\Site\N8nSite;
use App\Services\Site\PhpMyAdminSite;
use App\Services\Site\UptimeKumaSite;
use App\Services\Site\WordPressSite;
use App\Services\WordPress\PluginCacheConfig;
use App\Services\WordPress\WordPressVersion;
use App\Traits\HasLogger;
use App\Traits\HasTags;
use App\Traits\MetaAccessors;
use App\Traits\Provisionable;
use App\Traits\Statistic;
use App\Traits\XcloudOfferable;
use Exception;
use Illuminate\Bus\Batch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Jetstream\Jetstream;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;
use Throwable;
use Pdp\Rules;

class Site extends Model implements CipherSweetEncrypted, ProvisionableContract, Loggable
{
    use HasFactory, MetaAccessors, UsesCipherSweet, HasTags, Provisionable, XcloudOfferable, HasLogger, Statistic;

    const PRODUCTION = 'production';
    const STAGING = 'staging';

    const STAGING_WITH_OWN_DOMAIN = 'staging_with_own_domain';
    const TESTING = 'testing';
    const DEMO = 'demo';

    const DEFAULT_CRON_INTERVAL = 'every_five_minutes';
    const DEFAULT_WP_CRON = false;

    const CHECKSUM_MONITORING_INTERVAL = 86400 * 3; // 3 days in seconds

    const WP_CRON_INTERVALS = [
        'random' => 'Random',
        'every_minute' => 'Every Minute',
        'every_five_minutes' => 'Every Five Minutes',
        'every_ten_minutes' => 'Every Ten Minutes',
        'every_fifteen_minutes' => 'Every Fifteen Minutes',
        'every_thirty_minutes' => 'Every Thirty Minutes',
        'hourly' => 'Hourly',
    ];

    protected static function booted(): void
    {
        // Automatically update the base_name on every save.
        static::saving(function ($site) {
            $site->base_name = self::extractBaseDomain($site->name);
        });
    }

    /**
     * Extract the registrable (main) domain using the PHP Domain Parser.
     * This method caches the Public Suffix List (PSL) in a local file.
     *
     * @param string $domainName
     * @return string
     * @throws Exception
     */
    public static function extractBaseDomain(string $domainName): string
    {
        $resourcePath = resource_path('psl/public_suffix_list.dat');

        // Since you maintain this file manually, you can assume it exists.
        if (!file_exists($resourcePath)) {
            // Optionally, throw an exception or log an error if the file is missing.
            throw new \Exception("Public Suffix List file not found at {$resourcePath}");
        }

        // Create a Rules instance from the local PSL file.
        $rules = Rules::fromPath($resourcePath);
        $result = $rules->resolve($domainName);

        // registrableDomain() returns the base domain (e.g. "shop.co.uk" from "sub.shop.co.uk").
        return $result->registrableDomain()->toString();
    }

    public static function getUniqueDomainCount()
    {
        return self::distinct('base_name')->count('base_name');
    }

    public static function halfHourCronPattern(): string
    {
        return implode(',', range(rand(1, 29), 59, 30)) . ' * * * *';
    }

    const WP_UPDATE_MAIL_INTERVAL_IN_DAYS = 7;

    const ENVIRONMENTS = [
        self::PRODUCTION,
        self::STAGING,
        self::TESTING
    ];

    protected $guarded = [];
    protected $appends = [
        'cloud_provider',
        'is_connected',
        'state'
    ];
    protected $casts = [
        'managed_database_options' => 'array',
        'meta' => 'json',
        'redirects' => 'json',
        'additional_domains' => 'json',
        'wp_updates' => 'json',
        'email_provider_data' => 'json',
        'status' => SiteStatus::class,
        'type' => SiteType::class,
        'is_disabled' => 'boolean',
        'disabled_at' => 'datetime',
        'last_visited_at' => 'datetime',
        'disabled_by' => 'integer',
    ];
    protected $hidden = [
        'database_name',
        'database_password',
        'database_user',
        'admin_password',
        'database_host',
        'database_port',
        'meta',
        'redis_password'
    ];
    /** @var SiteManager */
    private  $_manager;


    public static function getSiteTypes(): array
    {
        return SiteType::toSelectArray();
    }

    public static function weeklyCronPattern(): string
    {
        return rand(1, 59) . ' ' . rand(1, 23) . ' * * ' . rand(0, 6);
    }


    public function getCronJobPattern(): string
    {
        $cornInterval = $this->getMeta('wp_cron_interval', self::DEFAULT_CRON_INTERVAL);
        return match ($cornInterval) {
            'every_minute' => '* * * * *',
            'every_five_minutes' => implode(',', range(rand(1, 4), 59, 5)) . ' * * * *',
            'every_ten_minutes' => implode(',', range(rand(1, 9), 59, 10)) . ' * * * *',
            'every_fifteen_minutes' => implode(',', range(rand(1, 14), 59, 15)) . ' * * * *',
            'every_thirty_minutes' => implode(',', range(rand(1, 29), 59, 30)) . ' * * * *',
            'hourly' => rand(1, 59) . ' * * * *',
            default => rand(1, 59) . ' ' . rand(1, 23) . ' * * *',
        };
    }

    public static function getDefaultConfig(array $configs = [], ?Server $server = null): array
    {
        return [
            'admin_user' => $configs['admin'] ?? 'admin',
            'admin_password' => $configs['admin_password'] ?? Str::random(),
            'admin_email' => $configs['admin_email'] ?? auth()->user()->email,
            'database_provider' => $configs['database_provider'] ?? DatabaseProvider::DEFAULT,
            'database_password' => $configs['database_password'] ?? Str::random(),
            'managed_database_mode' => $configs['managed_database_mode'] ?? 'create_new',
            'managed_database_options' => $configs['managed_database_options'] ?? [],
            'do_cluster_name' => $configs['do_cluster_name'] ?? [],
            'database_name' => $configs['database_name'] ?? 'xcloud',
            'database_user' => $configs['database_user'] ?? 'xcloud',
            'database_host' => $configs['database_host'] ?? 'localhost',
            'database_port' => $configs['database_port'] ?? 3306,
            'wordpress_version' => $configs['wordpress_version'] ?? WordPressVersion::DEFAULT,
            'php_version' => $configs['php_version'] ?? $server?->php_version ?: PhpVersion::DEFAULT,
            'prefix' => $configs['prefix'] ?? 'wp_',
            'site_user' => $configs['site_user'] ?? 'xcloud',
            'port' => $configs['port'] ?? null,
        ];
    }

    public function hasDatabase(): bool
    {
        return $this->database_provider && $this->database_provider !== DatabaseProvider::NULL;
    }

    public function isWordpress(): bool
    {
        return $this->database_provider !== DatabaseProvider::NULL && $this->type === SiteType::WORDPRESS;
    }

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('redis_password');
        $encryptedRow->addField('database_password');
        $encryptedRow->addField('admin_password');
        $encryptedRow->addField('site_ssh_password');
    }

    #Current user access filter

    public function scopeSearch($query, $search)
    {
        return $query->where('sites.name', 'like', '%' . $search . '%');
    }

    public function scopeAccessFilter($query, $user = null)
    {
        $user = $user ?? auth()->user();
        if (user()->hasTeamRole(team(), Team::SITE_ADMIN)) {
            return $query->when(!$user->currentTeam->hasAllSiteAccess(), fn($q) => $q->whereIn('sites.id', $user->sitesByInvitation()->pluck('sites.id')));
        }
        return $query->whereHas('server', fn($q) => $q->where('team_id', $user->current_team_id))
            ->when(!$user->currentTeam->hasAllServerAccess(), fn($q) => $q->whereIn('server_id', $user->serversByInvitation()->pluck('servers.id')));

    }

    public function scopeForResource($query)
    {
        $query->with([
            'siteMigration',
            'tags:id,name',
            'server.tags:id,name',
            'server',
            'server.latestMonitor',
            'server.latestBill',
            'server.team:id,name,user_id,active_plan_id',
            'server.cloudProvider',
            'server.vulnerabilitySetting',
        ])
        ->withCount([
            'vulnerabilities' => fn($q) => $q->where('ignored', false),
        ])
        ->withExists([
            'backupSettings as is_backup_enabled' => function ($query) {
                $query->where('auto_backup', true);
            },
            'backupFiles as has_local_backup' => function ($query) {
                $query->where('is_remote', false);
            }
        ]);

        return $query;
    }

    public function allowedToModifyXFrameOptions(): bool
    {
        return $this->getMeta('modify_x_frame_options', false);
    }

    public function disableNginxAutoRegeneration(): bool
    {
        return $this->getMeta('disable_nginx_config_regeneration', false);
    }

    public function enablePhpExecutionOnUploadDirectory(): bool
    {
        return $this->getMeta('enable_php_execution_on_upload_directory') ?: false;
    }

    public function enableXmlRpc(): bool
    {
        return $this->getMeta('enable_xml_rpc') ?: false;
    }

    public function isWpDebugEnabled(): bool
    {
        return $this->runInline(new WpDebugChecker($this->server, $this))->output == "true";
    }

    public function isNew(): bool
    {
        return $this->status === SiteStatus::NEW;
    }

    public function isMigrating(): bool
    {
        return $this->status === SiteStatus::MIGRATING;
    }

    public function isProvisioning(): bool
    {
        return $this->status === SiteStatus::PROVISIONING;
    }

    public function isProvisioned(): bool
    {
        return $this->status === SiteStatus::PROVISIONED;
    }

    public function markAsProvisioning()
    {
        $this->update([
            'status' => SiteStatus::PROVISIONING,
            'meta->provisioningErrorMessage' => null
        ]);

        SiteProvisioningStatusChanged::dispatch($this);
    }

    public function markAsMigrating()
    {
        $this->update([
            'status' => SiteStatus::MIGRATING,
        ]);

        SiteMigrationStatusChanged::dispatch($this->siteMigration);
    }

    public function provisioningProgress(int $status, $error = null)
    {
        $this->update([
            'status' => SiteStatus::PROVISIONING,
            'meta->provisioningStatus' => $status,
            'meta->provisioningStatusPercentage' => number_format(($status / SiteProvisioning::PROGRESS_MAX) * 100),
            'meta->provisioningErrorMessage' => $error,
        ]);

        SiteProvisioningStatusChanged::dispatch($this);
    }

    public function getProgress(): array
    {
        if ($this->isDeleting()) {
            return $this->deleteProgressData();
        }

        if ($this->isCloning() || $this->status == SiteStatus::CLONE_FAILED) {
            return $this->getCloneProgressData();
        }

        return $this->getProrgessData();
    }

    public function isDeleting()
    {
        return $this->status === SiteStatus::DELETING;
    }

    public function deleteProgressData(): array
    {
        return [
            'title' => 'Deleting Your Site ' . $this->name,
            'statusMax' => SiteDeleting::PROGRESS_MAX,
            'status' => (int)$this->getMeta('deletingStatus', 1),
            'percentage' => (int)$this->getMeta('deletingStatusPercentage', 0),
            'exception' => $this->getMeta('deletingErrorMessage') ?: null,
            'list' => SiteDeleting::get($this),
            'socketChannel' => 'site.deleting.' . $this->id,
            'socketListenFor' => 'SiteDeletingStatusChanged',
            'animate_object' => asset('img/progress/creating_server.svg'),
            'serverName' => $this->name,
            'serverId' => $this->id,
            'siteStatus' => $this->status,
        ];
    }

    public function isCloning()
    {
        return $this->status === SiteStatus::CLONING;
    }

    public function getCloneProgressData(): array
    {
        $stages = Arr::map(SiteProvisioning::get(), fn($stage) => [
            ...$stage,
            'tasks' => Arr::where($stage['tasks'], fn($task) => !($task === 'Installing Database' && !$this->hasDatabase()))
        ]);
        return [
            'title' => $this->hasStagingEnvironment() || $this->hasStagingWithCustomDomainEnvironment() ? 'Creating Staging Site ' . $this->name . ' from ' . $this->siteClone->existing_site_url : 'Cloning site from ' . $this->siteClone->existing_site_url . ' to ' . $this->name,
            'list' => $stages,
            'statusMax' => SiteProvisioning::PROGRESS_MAX,
            'siteStatus' => $this->status,
            'status' => (int)$this->getMeta('cloningStatus', 1),
            'percentage' => (int)$this->getMeta('cloningStatusPercentage', 0),
            'exception' => $this->getMeta('cloningErrorMessage') ?: null,

            'socketChannel' => 'site.clone.' . $this->id,
            'socketListenFor' => 'SiteCloneStatusChanged',
            'animate_object' => asset('img/progress/Migration.svg'),
            'serverId' => $this->server_id,
            'siteId' => $this->id,
            'siteName' => $this->name,
            'additionalData' => [
                ':stack' => $this->server->stack->toReadableString() ?: '',
                ':cache' => $this->server->stack->cachingMechanism() ?: '',
                ':php_version' => $this->php_version ?: '',
            ],
        ];
    }

    public function getProrgessData(): array
    {
        $task = SiteProvisioning::get();

        if ($this->isIPSite()) {
            //remove ssl and https task from configuring stage for IP site
            unset($task[2]['tasks'][SiteProvisioning::CONFIGURING_SSL]);
            unset($task[2]['tasks'][SiteProvisioning::CONFIGURING_HTTPS]);
        }

        if (!$this->isWordpress()) {
            //remove ssl and https task from configuring stage for IP site
            unset($task[2]['tasks'][SiteProvisioning::INSTALL_BLUEPRINT]);
            unset($task[2]['tasks'][SiteProvisioning::INSTALLING_WP_CRON_JOB]);
            unset($task[2]['tasks'][SiteProvisioning::SETTING_UP_EMAIL_PROVIDER]);
            unset($task[2]['tasks'][SiteProvisioning::HANDLE_INDEXING]);
            unset($task[2]['tasks'][SiteProvisioning::CONFIGURING_REDIS_CACHE]);
            unset($task[2]['tasks'][SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE]);
        }

        if ($this->isN8n() || $this->isUptimeKuma()) {
            unset($task[2]['tasks'][SiteProvisioning::DEPLOY_SCRIPT]);
        }

        return [
            'title' => Lang::get('emails.site_title_text').' ' . $this->name,
            'list' => $task,
            'statusMax' => SiteProvisioning::PROGRESS_MAX,
            'siteStatus' => $this->status->value,
            'status' => (int)$this->getMeta('provisioningStatus', 1),
            'percentage' => (int)$this->getMeta('provisioningStatusPercentage', 0),
            'exception' => $this->getMeta('provisioningErrorMessage') ?: null,
            'socketChannel' => 'site.' . $this->id,
            'socketListenFor' => 'SiteProvisioningStatusChanged',
            'animate_object' => asset('img/progress/wp_site_install.svg'),
            'additionalData' => [
                ':stack' => $this->server->stack->toReadableString() ?: '',
                ':site_stack' => $this->isNodeApp() ? 'Node' : 'PHP',
                ':site_stack_version' => ($this->isNodeApp() ? $this->server->node_version : $this->php_version) ?: '',
                ':cache' => $this->server->stack->cachingMechanism() ?: '',
                ':type' => $this->type->getDisplayName(),
            ],
            'serverId' => $this->server_id,
            'siteId' => $this->id,
            'siteName' => $this->name,
            'retry' => user()->can('addSite',
                    $this->server) && $this->getMeta('provisioningErrorMessage') && $this->status === SiteStatus::PROVISIONING_FAILED && request()->routeIs(['site.progress']),
            'retryUrl' => route('api.site.re-provision', [
                'server' => $this->server_id,
                'site' => $this->id,
            ]),
            'progressText' => Lang::get('emails.site_progress_text')
        ];
    }

    public function markAsProvisioningFailure($messages = null)
    {
        $this->update([
            'status' => SiteStatus::PROVISIONING_FAILED,
            'meta->provisioningErrorMessage' => $messages,
            'meta->script_initiate_by' => null,
        ]);
        $this->server->update(['meta->script_initiate_by' => null]);
        SiteProvisioningStatusChanged::dispatch($this);
        SiteProvisioningFailed::dispatch($this);

        // not sure if we'll need something like this.
        // $this->tasks()->where('status', Task::STATUS_RUNNING)->update(['status' => Task::STATUS_FAILED]);
    }

    public function markAsProvisioned(): void
    {
        $this->update([
            'status' => SiteStatus::PROVISIONED,
            'meta->provisioningStatus' => SiteProvisioning::PROGRESS_MAX,
            'meta->provisioningStatusPercentage' => 100,
            'meta->provisioningErrorMessage' => null,
            'meta->script_initiate_by' => null,
        ]);
        $this->server->update(['meta->script_initiate_by' => null]);

        SiteProvisioningStatusChanged::dispatch($this);
        SiteProvisioned::dispatch($this);
    }

    public function hasCachePlugin(): bool
    {
        return isset($this->meta['wp_cache_plugin']) && $this->meta['wp_cache_plugin'] !== null;
//        return array_intersect(array_keys($this->getCachePlugins()), $this->getInstalledPluginList()) !== [];
    }

    public function hasCachePluginThatHandleOwnDirectory(): bool
    {
        if ($this->hasCachePlugin()) {
            return WPCachePlugins::fromValue($this->meta['wp_cache_plugin'])->handleOwnDirectory();
        }

        return false;
    }

    public function getActiveCachePlugin(): string
    {
        return $this->meta['wp_cache_plugin'] ?? '';
    }

    function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    function hasSuccessfulSslCertificate(): bool
    {
        return $this->hasSslCertificate() && $this->sslCertificate()->whereIn('status', [
                SslCertificate::STATUS_OBTAINED,
                SslCertificate::STATUS_INSTALLED,
            ])->exists();
    }

    function hasSslCertificate(): bool
    {
        return (bool)$this->ssl_provider;
    }

    public function sslCertificate(): HasOne
    {
        return $this->hasOne(SslCertificate::class)->where('provider', $this->ssl_provider);
    }

    function hasBasicAuthEnabled(): bool
    {
        return Arr::get($this->meta, 'has_basic_auth') == true;
    }

    function hasFullPageCaching(): bool
    {
        return Arr::get($this->meta, 'has_fullpage_caching') == true;
    }

    public function hasPageCache(): bool
    {
        return (bool)$this->hasFullPageCaching() || $this->hasCachePlugin();
    }

    function disableFullPageCache(): Task
    {
        $this->update(['meta->has_fullpage_caching' => false]);

        return $this->runInBackground(new DeactivateFullPageCache($this));
    }

    function hasRedisObjectCaching(): bool
    {
        if ($this->server?->stack?->isOpenLiteSpeed()) {
            return $this->hasFullPageCaching(); // LiteSpeed cache plugin is used for Redis object caching
        }

        return Arr::get($this->meta, 'has_redis_object_caching') == true;
    }

    function has7gFirewallEnabled(): bool
    {
        return (bool)Arr::get($this->meta, 'enable_7g_firewall') ?? false;
    }

    function fullPageCachingDuration()
    {
        return ($this->getMeta('cache_duration', 60) . $this->getMeta('cache_duration_unit', 'm')) ?: '60m';
    }

    function getSiteUrlAttribute(): string
    {
        return ($this->hasSslCertificate() ? 'https://' : 'http://') . $this->name;
    }

    function getSiteAdminUrlAttribute(): string
    {
        return $this->site_url . ($this->isWordPress() ? '/wp-admin' : '');
    }

    function provision(): void
    {
        $this->update(['meta->script_initiate_by' => user()?->id]);

        SiteProvision::dispatch($this);
    }

    public function getSiteHealth()
    {
        return $this->monitors()->latest()->first();
    }

    public function monitors()
    {
        return $this->hasMany(SiteMonitor::class);
    }

    function readEnv()
    {
        return $this->runInline(new ReadSiteEnv($this))->output;
    }

    function putEnv($envBody): bool
    {
        return $this->runInline(new UpdateSiteEnv($this, $envBody))->successful();
    }

    function regenerateNginxConf($createSymblink = false, $restartNginx = false): Task
    {
        if ($this->getMeta('disable_nginx_config_regeneration', false)) {
            return Task::create([
                'name' => 'Regenerate '.$this->server->stack->title().' Config for ' . $this->name,
                'output' => $this->server->stack->title(). ' config regeneration is disabled for site ' . $this->name,
                'status' => Task::STATUS_FINISHED,
                'site_id' => $this->id,
                'server_id' => $this->server_id,
                'team_id' => $this->server->team_id,
                'initiated_by' => user()->id ?? $this->server?->user_id,
            ]);
        }

        if ($this->server->stack->isNginx()) {
            (new SyncServerNginxVersion($this->server))->handle();
            $task = $this->runInline(new GenerateNginxConfig($this, createSymblink: $createSymblink, restartNginx: $restartNginx));
        } else {
            $task = $this->runInline(new GenerateOpenLiteSpeedConfig($this));
        }

        if ($task->successful()) {
            $this->saveMeta('nginx_config_version', CustomNginxEnum::NGINX_CONFIG_VERSION);
        }

        return $task;
    }

    function useNginxHttp2Directive(): bool|int
    {
        if (!$this->server?->getServerInfo('nginx_version')){
            return false;
        }

        // Required version to use http2 directive
        // @url https://aruljohn.com/blog/nginx-listen-http2-directive-deprecated/#:~:text=If%20you%20have%20updated%20Nginx,instead%20in%20.....
        $required_version = "1.25.1";

        return (bool) version_compare($this->server->getServerInfo('nginx_version'), $required_version, '>=');
    }

    public function resetDirectoryPermission(): Task
    {
        return $this->runInline(new ResetSiteUserPermission(site: $this));
    }

    function preparePluginCacheFile(): Task
    {
        return PluginCacheConfig::generate($this);
    }

    public function isMultiSite(): bool
    {
        return $this->getMeta('enable_multisite', false);
    }

    public function isMultiSiteSubdomain(): bool
    {
        return $this->isMultiSite() && $this->getMeta('multisite_subdomain', false);
    }

    public function isMultiSiteDirectory(): bool
    {
        return $this->isMultiSite() && !$this->isMultiSiteSubdomain();
    }

    function checkPluginIsActivated(string $plugin): Task
    {
        return $this->runInline(new CheckPluginIsActive($this, $plugin));
    }

    function executeDeployScript($script): Task
    {
        return $this->runInline(new ExecuteDeployScript($this, $script));
    }

    function shouldRunDeployScript(): bool
    {
        return $this->getMeta('git_info.run_after_deployment') && $this->getMeta('deploy_script');
    }

    function installBluePrint(BluePrint $bluePrint = null): Task
    {
        $bluePrint = $bluePrint ?? BluePrint::find($this->getMeta('blueprint_id'));
        return $this->runInline(new InstallBluePrintScript($this, $bluePrint));
    }

    function installPatchstack(PatchstackVulnerability $patchstackVulnerability = null): Task
    {
        $patchstackVulnerability = $patchstackVulnerability ?? PatchstackVulnerability::find($this->id);
        $task = $this->runInline(new InstallPatchstackScript($this, $patchstackVulnerability));

        if ($task->successful() && $this->patchstackVulnerability) {
            $this->patchstackVulnerability()->update([
                'status' => PatchstackVulnerabilityStatus::CONNECTED->value
            ]);
        }

        return $task;
    }

    function deletePatchstack(PatchstackVulnerability $patchstackVulnerability = null): Task
    {
        $patchstackVulnerability = $patchstackVulnerability ?? PatchstackVulnerability::find($this->id);
        return $this->runInline(new DeletePatchstackScript($this, $patchstackVulnerability));
    }

    function isLaravel()
    {
        return $this->type === SiteType::LARAVEL;
    }

    function isNodeApp(): bool
    {
        return $this->type === SiteType::N8N || $this->type === SiteType::UPTIME_KUMA;
    }

    function isPhpMyAdmin()
    {
        return $this->type === SiteType::PHPMYADMIN;
    }

    function isN8n()
    {
        return $this->type === SiteType::N8N;
    }

    function pm2ProcessName(): string
    {
        return "{$this->type->value}-$this->name";
    }

    function isUptimeKuma()
    {
        return $this->type === SiteType::UPTIME_KUMA;
    }

    public function getWordpressVersionAttribute($value): string
    {
        $version = $value ?? WordPressVersion::DEFAULT;
        if ($version === WordPressVersion::DEFAULT && isset($this->wp_updates['wp_version'])) {
            $this->update(['wordpress_version' => $this->wp_updates['wp_version']]);
            $version = $this->wp_updates['wp_version'];
        }
        return $version;
    }

    public function sslCertificates(): HasMany
    {
        return $this->hasMany(SslCertificate::class);
    }

    public function getRedirectDomains(): Collection
    {
        return collect($this->additional_domains)->filter(fn($domain) => ($domain['is_redirect'] ?? false))->map(fn(
            $domain
        ) => trim($domain['value'] ?? ''))->filter();
    }

    public function getDomainNames(): Collection
    {
        return $this->getAliases()->merge([$this->name]);
    }

    public function getAliases(): Collection
    {
        return collect($this->additional_domains)->filter(fn($domain) => !($domain['is_redirect'] ?? false))->map(fn($domain) => trim($domain['value'] ?? ''))->filter();
    }

    public function getAllDomainNames(): Collection
    {
        return $this->getAdditionalDomainNames()->merge([$this->name]);
    }

    public function shouldAddExtraWwwDomainRedirection(): bool
    {
        return !str($this->name)->startsWith('www.') && !$this->getAllDomainNames()->contains('www.' . $this->name);
    }

    public function getAdditionalDomainNames(): Collection
    {
        return collect($this->additional_domains)->map(fn($domain) => trim($domain['value'] ?? ''))->filter();
    }

    function getWpCliAttribute(): string
    {
        return '/usr/bin/php'.$this->php_version.' -d error_reporting=0 -d display_errors=0 $(which wp)';
    }

    function wpCli($args = null): string
    {
        $command = '/usr/bin/php'.$this->php_version;

        if ($args) {
            $command .= ' '.$args . ' ';
        }

        $command .= '$(which wp)';

        return $command;
    }

    function getPhpAttribute(): string
    {
        return'/usr/bin/php'.$this->php_version;
    }

    function getPhpShortAttribute(): string
    {
        return'php'.$this->php_version;
    }

    // get cloud provider attribute from server

    public function getCloudProviderReadableAttribute()
    {
        return $this->getCloudProviderAttribute();
    }

    public function getCloudProviderAttribute()
    {
        return $this?->server?->cloudProvider?->provider_readable ?: 'Other Provider';
    }

    public function getIsConnectedAttribute()
    {
        return $this->server?->is_connected;
    }

    function sshKeyParis()
    {
        return $this->belongsToMany(SshKeyPair::class)->withTimestamps();
    }

    function sshKeyPair(): BelongsTo
    {
        return $this->belongsTo(SshKeyPair::class);
    }

    public function redirections(): HasMany
    {
        return $this->hasMany(Redirection::class);
    }

    public function getNginxAccessLog(): array
    {
        exec('tail -n 100 ' . '/opt/homebrew/var/log/nginx/access.log', $output);

        return $output;
    }

    function ipAddress()
    {
        return $this->server->ipAddress();
    }

    function port()
    {
        return $this->server->port();
    }

    function privateKeyPath()
    {
        return $this->server->privateKeyPath();
    }

    public function scopeFilter($query, $filters)
    {
        if ($filters->filterType === 'status' && in_array(Str::upper($filters->filter), $this->getAllStatus(), true)) {
            return $query->where('sites.status', $filters->filter);
        }

        if ($filters->filter && $filters->filterType === 'tag') {
            return $query->whereHas('tags', fn($query) => $query->where('name', $filters->filter));
        }

        if ($filters->filter && $filters->filterType === 'environment') {
            return $query->where('sites.environment', $filters->filter);
        }

        if($filters->filter && $filters->filterType === 'vulnerability') {
            return $query->whereHas('vulnerabilities')->orWhereHas('patchstackVulnerabilitySite');
        }

        if ($filters->client && $filters->client !== 'All Clients') {
            return $query->whereHas('server', fn($query) => $query->where('user_id', $filters->client));
        }

        return $query;
    }

    public function scopeSortByServer($query, $filters)
    {
        if ($filters->sortByServer) {
            return $query->where('server_id', $filters->sortByServer);
        }

        return $query;
    }

    public function getAllStatus()
    {
        return array_column(SiteStatus::cases(), 'name');
    }

    public function getStateAttribute()
    {
        if ($this->is_disabled) {
            return 'Disabled';
        }
        //get the current state based on the status
        //there will be 4 state initial, Processing, success and Error
        if (in_array($this->status, SiteStatus::initialStates())) {
            return 'Initial';
        }

        if (in_array($this->status, SiteStatus::inQueueStates())) {
            return 'InQueue';
        }

        if (in_array($this->status, SiteStatus::processingStates())) {
            return 'Processing';
        }

        if (in_array($this->status, SiteStatus::successStates())) {
            return 'Success';
        }

        if (in_array($this->status, SiteStatus::failedStates())) {
            return 'Error';
        }
    }

    public function getStateColor()
    {
        if (in_array($this->status, SiteStatus::initialStates())) {
            return 'bg-primary-light/10 text-primary-light hover:bg-primary-light hover:text-white';
        }

        if (in_array($this->status, SiteStatus::inQueueStates())) {
            return 'border-transparent bg-warning/10 text-sm text-warning hover:bg-warning hover:text-white';
        }

        if (in_array($this->status, SiteStatus::processingStates())) {
            return 'border-transparent bg-warning/10 text-sm text-warning hover:bg-warning hover:text-white';
        }

        if (in_array($this->status, SiteStatus::successStates())) {
            return 'text-success-full group-hover:text-white bg-success-full/10 group-hover:bg-success-full';
        }

        if (in_array($this->status, SiteStatus::failedStates())) {
            return 'text-danger bg-danger/10 hover:bg-danger hover:text-white';
        }
    }

    function siteMigration(): HasOne
    {
        return $this->hasOne(SiteMigration::class);
    }

    function siteClone(): HasOne
    {
        return $this->hasOne(SiteClone::class);
    }

    public function cronJobs(): HasMany
    {
        return $this->hasMany(CronJob::class);
    }

    function setNameAttribute($value)
    {
        $this->attributes['name'] = get_domain($value);
    }

    function getSlugAttribute(): string
    {
        return Str::slug(str_replace('.', '-', $this->name));
    }

    function getRedisObjectCacheKeyAttribute(): string
    {
        return "redis:{$this->server_id}:{$this->id}:{$this->slug}";
    }

    function getCacheKeyForDomain($domain): string
    {
        $slug = Str::slug(str_replace('.', '-', $domain));
        return "redis:{$this->server_id}:{$this->id}:{$slug}";
    }

    public function delete()
    {
        SiteDeleted::dispatch($this);
        return parent::delete(); // TODO: Change the autogenerated stub
    }

    public function isDeleted(): bool
    {
        return $this->status === SiteStatus::DELETED;
    }

    public function installMonitoring(): void
    {
        $this->runInBackground(new InstallSiteMonitoring($this->server, $this));
    }

    public function reInstallMonitoring(): void
    {
        $this->runInBackground(new ReInstallSiteMonitoring($this->server, $this));
    }

    public function disableMonitoring(): void
    {
        $this->runInBackground(new DisableSiteMonitoring($this->server, $this));
    }

    public function installWpCronJob(): void
    {
        $this->runInline(new InstallWpCron($this));
    }

    public function readWpOption($option, $default = '0')
    {
        $script = view('scripts.site.readWPOption', ['site' => $this, 'option' => $option, 'default' => $default])->render();
        return $this->runInline(new InlineScript($script))->output;
    }

    public function uploadMagicLoginPlugin(): array
    {
        try {
            $response = Http::baseUrl($this->site_url)->get('/', [
                'rest_route' => '/xcloud-magic-login/v1/plugin-status',
                'auth_token' => hashid_encode($this->id),
                'v' => time(),
            ])->json();

            if (($response['code'] ?? null) == 'xcloud_magic_login_is_active'
                && ($response['version'] ?? null) == UploadMagicLoginPlugin::XCLOUD_MAGIC_HELPER_VERSION
                && ($response['callback_url'] ?? null) == callback_url('/api/site/remote-login')) {
                // [$pluginIsValid, !$recentlyInstalled]
                return [true, false];
            }
        } catch (\Exception $exception) {
            // do nothing
        }

        $task = $this->runInline(new UploadMagicLoginPlugin($this->server, $this));

        // [$uploadStatus, $recentlyInstalled]
        return [$task->successful(), true];
    }

    public function installAdminer()
    {
        $file = $this->getMeta('enable_adminer');
        $webRoot = $this->manager()->siteDocumentRoot();
        $task = $this->server->uploadFile([
            'local_path' => resource_path('views/scripts/site/adminer/adminer.php'),
            'remote_path' => $webRoot.DIRECTORY_SEPARATOR.$file
        ]);
        if ($task->successful()) {
            $this->runInline(new UpdateAdminerCallback($this->server, $this));
        }
    }

    public function updateWpDebug($enableWpDebug)
    {
        $this->runInline(new UpdateWpDebug($this->server, $this, $enableWpDebug));
    }

    public function installFileManager(): void
    {
        $file = $this->getMeta('file_manager');
        $webRoot = $this->manager()->siteDocumentRoot();
        $localPath =  resource_path('views/scripts/site/filemanager/tinyfilemanager.php');
        $isProduction = app()->environment('production');
        if ($isProduction){
            $localPath = resource_path('views/scripts/site/filemanager/fileManagerProduction.php');
        }
        $task = $this->server->uploadFile([
            'local_path' =>  $localPath,
            'remote_path' => $webRoot.DIRECTORY_SEPARATOR.$file
        ]);

        if ($task->successful() && ($this->web_root || !$isProduction)) {
            $this->runInline(new UpdateFileManagerCallback($this->server, $this));
        }
    }

    public function removeSiteFile($file_name): bool
    {
        if ($file_name) {
            $webRoot = $this->manager()->siteDocumentRoot();
            $script = new InlineScript("rm -rf $webRoot/{$file_name}");
            $script->name = "Remove File: {$file_name}";
            $task = $this->runInline($script); // remove adminer directory
            return $task->successful();
        }
        return true;
    }

    public function installMonitoringInline(): void
    {
        $this->runInline(new InstallSiteMonitoring($this->server, $this));
    }

    public function pullMonitoring(): Task
    {
        $task = $this->runInline(new PullSiteMonitoring($this->server, $this));
        if ($task->output_json) {
            $this->saveMonitoring($task->output_json);
        }
        return $task;
    }

    public function saveMonitoring(array $data): void
    {
        if ($data['ssl_expiration_date'] == 0) {
            $data['ssl_expiration_date'] = null;
        } else {
            $data['ssl_expiration_date'] = Carbon::createFromTimestamp($data['ssl_expiration_date'])->format('Y-m-d');
        }

        $data['site_id'] = $this->id;
        $monitoring_data = Arr::except($data, ['site_integrity']);
        $this->monitors()->create($monitoring_data);
        if ($this->isWordpress() && $site_integrity = Arr::get($data, 'site_integrity')){
            $this->syncIntegrity($site_integrity);
        }
        /*
        //send email when ssl expiration date is less than 30 days
        if (Carbon::parse($data['ssl_expiration_date'])->diffInDays(now()) < 30 && (null == $this->getMeta('ssl_expiration_mail_at') || now()->diffInHours($this->meta['ssl_expiration_mail_at']) > 12)) {
            SiteSslExpireSoon::dispatch($this, $data['ssl_expiration_date']);
            $this->saveMeta('ssl_expiration_mail_at', now()->toDateTimeString());
        }
        if ($data['dns_status'] == 'error' && (null == $this->getMeta('dns_error_mail_at') || now()->diffInHours($this->meta['dns_error_mail_at']) > 12)) {
            SiteDnsError::dispatch($this, $data['dns_status']);
            $this->saveMeta('dns_error_mail_at', now()->toDateTimeString());
        }
        if ($data['ssl_status'] == 'error' && (null == $this->getMeta('ssl_error_mail_at') || now()->diffInHours($this->meta['ssl_error_mail_at']) > 12)) {
            SiteSslError::dispatch($this, $data['ssl_status']);
            $this->saveMeta('ssl_error_mail_at', now()->toDateTimeString());
        }
        */

        SiteMonitoringUpdated::dispatch($this);
    }

    public function syncIntegrity($site_integrity): void{
        $integrityPlugins = $site_integrity['plugin_checksums']['plugins'] ?? [];

        $plugins = collect($integrityPlugins)
            ->partition(fn($plugin) => is_array($plugin) && isset($plugin['plugin_name']));

        // First partition: valid plugins with plugin_name
        $groupedValidPlugins = $plugins[0]->groupBy('plugin_name');

        // Second partition: warnings, strings, or malformed entries
        $otherPlugins = $plugins[1]->values(); // Reset keys

        $site_integrity['plugin_checksums']['plugins'] = $groupedValidPlugins->merge([
            'misc' => $otherPlugins
        ])->toArray();

        $this->integrityChecksum()->updateOrCreate(
            ['site_id' => $this->id],
            Arr::only($site_integrity, ['core_checksums','plugin_checksums'])
        );
    }

    public function pullUpdates(): Task
    {
        $task = $this->runInline(new PullSiteUpdates($this->server, $this));
        $data = $task->output_json;
        if (isset($data['wp_updates']) && is_array($data['wp_updates'])) {
            $data['wp_updates']['datetime'] = now();
            $data['wp_updates']['refreshing'] = false;
            $this->update([
                'wp_updates' => $data['wp_updates'],
                'wordpress_version' => $data['wp_updates']['wp_version'] ?? $this->wordpress_version,
            ]);
            if ($this->isVulnerabilityScanEnable()){
                $vulnerabilities = WordfenceVulnerability::scanVulnerability($this->wp_updates);
                $this->syncVulnerabilities(vulnerabilities: $vulnerabilities);
            }
        }
        return $task;
    }

    public function showMigrationBanner()
    {
        return $this->getMeta('show_migration_banner', false);
    }

    public function checkUpdate($wp_core = 'wp_core')
    {
        $data = $this->wp_updates;
        if ($data !== null && array_key_exists($wp_core, $data)) {
            return is_array($data[$wp_core]) ? $data[$wp_core] : json_decode($data[$wp_core], true);
        }
        return null;
    }

    public function theme_updates(): array
    {
        $themes = $this->wp_updates['themes'] ?? [];

        return array_filter($themes, function ($theme) {
            return !empty($theme['update_version']);
        });
    }

    public function plugin_updates(): array
    {
        $plugins = $this->wp_updates['plugins'] ?? [];
        return array_filter($plugins, function ($plugin) {
            return !empty($plugin['update_version']);
        });
    }

    public function wp_core_updates(): array
    {
        $wp_core = $this->wp_updates['wp_core'] ?? [];
        if (is_array($wp_core)) {
            return $wp_core;
        }
        return [];
    }

    public function hasProductionEnvironment(): bool
    {
        return $this->environment === self::PRODUCTION;
    }

    public function hasStagingEnvironment(): bool
    {
        return $this->environment === self::STAGING;
    }

    public function hasStagingWithCustomDomainEnvironment(): bool
    {
        return $this->environment === self::STAGING_WITH_OWN_DOMAIN;
    }

    public function hasTestingEnvironment(): bool
    {
        return $this->environment === self::TESTING;
    }

    public function hasDemoEnvironment(): bool
    {
        return $this->environment === self::DEMO;
    }

    public function getTemporarySiteUrl()
    {
        return Arr::get($this->meta, 'temporarySiteUrl');
    }

    public function markAsDomainUpdateFailure($messages = null): void
    {
        // merge with existing log
        $log = array_merge($this->getMeta('domainChangeInfo'), [
            'status' => 'failed',
            'message' => $messages
        ]);
        $this->saveMeta('domainChangeInfo', $log);

        // send failure notification
        DomainUpdateFailed::dispatch($this);
    }

    public function checkSiteStorageAvailability($oldSiteDomain = null): Task
    {
        return $this->runInline(new MonitorSiteCloneStorage($this, $oldSiteDomain));
    }

    public function checkSiteStorage($exclude = false): Task
    {
        return $this->runInline(new CheckSiteStorage($this, $exclude));
    }

    public function generateStagingSiteName($prefix = null): string
    {
        // $combinations = [
        //     'colorName' => 'colorName',
        //     'colorName' => 'firstName',
        //     'colorName' => 'city',
        //     'firstName' => 'firstName',
        //     'firstName' => 'colorName',
        //     'firstName' => 'city',
        //     'city' => 'city',
        //     'city' => 'colorName',
        //     'city' => 'firstName',
        // ];
        // $randomKey = array_rand($combinations);
        // $randomOutput = [$randomKey => $combinations[$randomKey]];

        // $faker = Factory::create();
        // return $prefix
        //     ? $prefix.'-'.Str::slug($faker->{$randomKey}.'-'.$faker->{$randomOutput[$randomKey]}).'-'.$this->id.'.x-cloud.app'
        //     : Str::slug($faker->{$randomKey}.'-'.$faker->{$randomOutput[$randomKey]}).'-'.$this->id.'.x-cloud.app';


        $domain = config('services.cloudflare_updated.active');

        if ($prefix) {
//            $word = Arr::random([
//                "Red", "Green", "Blue", "Yellow", "Purple", "Cyan", "Magenta", "Lime", "Pink", // colors
//                "Lavender", "Brown", "Beige", "Maroon", "Mint", "Olive", "Navy", "Grey", "White", "Black",
//                'Lion', 'Tiger', 'Bear', 'Turtle', 'Eagle', 'Wolf', 'Fox', 'Elephant',  // animals
//            ]);

            return Str::slug("{$prefix}-{$this->id}") . "." . $domain;
        }

        $haiku = Haiku::name();

        return Str::slug("{$haiku}-{$this->id}") . "." . $domain;
    }

    public function updateDomain($domain): void
    {
        $this->update([
            'name' => rtrim($domain, '/')
        ]);
    }

    public function cloneToRemote(): bool
    {
        $cloneFromSite = Site::find(Arr::get($this->meta, 'cloneInfo.cloneFromSiteId'));
        return $cloneFromSite->server_id !== $this->server_id;
    }

    public function team()
    {
        return $this->server->team;
    }

    public function teamInfo(): Team
    {
        return $this->team();
    }

    public function getRSAKeyPath(): string
    {
        return "/home/<USER>/.ssh/id_rsa_xcloud_$this->id";
    }

    public function getRSAKeyTempPath(): string
    {
        return "/tmp/$this->name/.ssh/id_rsa_xcloud_$this->id";
    }

    public function siteMigrationFailed(): void
    {
        $this->update(['status' => SiteStatus::MIGRATION_FAILED]);
        // CleanUpSiteOnFailure::dispatch($this->server, $this);

    }

    public function cancelMigration(): void
    {
        $this->siteMigration->markAsMigrationCancelled();
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    public function getPluginPath(): string
    {
        return $this->manager()->sitePluginPath();
    }

    /**
     *
     *
     * @return SiteContract
     * @throws Exception
     */
    function manager(): SiteContract
    {
        if ($this->_manager) {
            return $this->_manager;
        }
        $this->_manager = match ($this->type) {
            SiteType::LARAVEL => new LaravelSite($this),
            SiteType::WORDPRESS => new WordPressSite($this),
            SiteType::CUSTOM_PHP => new CustomPhpSite($this),
            SiteType::PHPMYADMIN => new PhpMyAdminSite($this),
            SiteType::N8N => new N8nSite($this),
            SiteType::UPTIME_KUMA => new UptimeKumaSite($this),
            SiteType::MAUTIC => new MauticSite($this),
            SiteType::ONECLICK => throw new \Exception('To be implemented'),
        };

        return $this->_manager;
    }

    public function getInstalledPluginList(): array
    {
        $pluginDetails = $this->wp_updates['plugins'] ?? [];

        $plugins = [];

        if (empty($pluginDetails)) {
            return $plugins;
        }

        foreach ($pluginDetails as $plugin) {
            $plugins[] = $plugin['name'];
        }

        return $plugins;
    }

    public function goLive(): void
    {
        $this->update([
            'ssl_provider' => null,
            'environment' => self::PRODUCTION
        ]);
    }

    public function permissions($user = null): array
    {
        $user = $user ?: auth()->user();

        // Check if the user owns the team
        if ($user->ownsTeam($this->server->team)) {
            return $this->getOwnerPermissions($user);
        }

        // Fetch permissions for non-owners
        return $this->getMemberPermissions($user);
    }

    private function getOwnerPermissions($user): array
    {
        // Get all default permissions for team owners
        $permissions = Arr::flatten_key(Jetstream::$permissions, INF, ['site:']);
        // Filter permissions based on user abilities and server settings
        return array_values(array_filter($permissions, fn ($permission) =>
             match ($permission) {
                'site:access-magic-login' => $this->server->isMagicLoginEnabled(),
                'site:custom-command-runner' => $user->can('customCommandRunner', $this),
                'site:manage-update' => $user->can('update', $this),
                'site:delete' => $user->can('delete', $this),
                'site:manage-wpconfig' => $user->can('wpConfig', $this),
                 'site:manage-email-providers' => $user->can('emailProvider', $this),
                 'site:manage-file-manager' => $user->can('fileManager', $this),
                default => true,
            }));
    }

    private function getMemberPermissions($user): array
    {
        if (!$user->can('view', $this)) {
            return [];
        }

        // Get permissions from the membership record
        $permissions = Membership::where([
            'user_id' => $user->id,
            'team_id' => $this->team()->id
        ])->value('permissions', []) ?? [];

        // Filter permissions relevant to servers and site creation
        return array_filter($permissions, fn($permission) => Str::contains($permission, 'site:'));
    }


    public function isDomainUpdating(): bool
    {
        return Arr::get($this->meta, 'domainChangeInfo.status') === 'updating';
    }

    public function backup($backupSetting): JsonResponse
    {
        $this->runInBackground(new BackupSite($this, $this->server, $backupSetting));
        return response()->json(['message' => 'Backup process started successfully'], 200);
    }

    public function backupNow(bool $is_local = true, $type = 'full', ?User $user= null): void
    {
        //$this->saveMeta('backup_task_running', now()->toDateTimeString());
        $backupSetting = BackupSetting::where(['site_id' => $this->id, 'is_local' => $is_local])->first();
        $is_pending = $backupSetting->status === BackupSetting::PENDING;

        # get server timezone or default to UTC
        $server_timezone = $this->server->time_zone;
        try {
            $server_timezone = Carbon::now($server_timezone)->toDateTimeString();
        } catch (Exception $e) {
            $server_timezone = Carbon::now('UTC')->toDateTimeString();
        }

        $backupSetting->update(['status' => BackupSetting::RUNNING, 'last_backup_at' => $server_timezone]);
        if ($is_pending) {
            $this->installBackupScript(backupSetting: $backupSetting, take_backup: true);
        } else {
            SiteBackupNow::dispatch($this, $backupSetting, $user, $type);
        }
    }

    public function backupExcludesPaths(?BackupSetting $backupSetting = null): array
    {
        $backupSetting ??= BackupSetting::firstWhere('site_id', $this->id);
        return collect(explode("\n", $backupSetting?->exclude_paths ?? ''))
            ->map('trim')
            ->filter()
            ->map('escapeshellcmd')
            ->all();
    }

    public function installBackupScript($backupSetting, $take_backup = false): void
    {
        $this->runInBackground(new InstallSiteBackupScript($this->server, $this, $backupSetting, $take_backup), [
            'timeout' => -1,
            'then' => [
                new SiteBackRestoreUpdated($this, $backupSetting),
            ],
        ]);
    }

    public function backupFilesPath($domain = null): string
    {
        $name = $domain ?? $this->name;
        return "~/.backup/{$name}";
    }

    public function backupFileFullPath(?string $path, ?string $file=null, bool $is_local=false): string
    {
        if ($is_local) {
            return basename($file) === $file ?  $path . DIRECTORY_SEPARATOR . $file :$file;
        }
        return $this->backupFilesPath() . DIRECTORY_SEPARATOR . "backup-remote" . DIRECTORY_SEPARATOR . $file;
    }

    public function incrementalPath(): string
    {
        return "~/.backup/{$this->id}";
    }

    public function restoreLogPath(): string
    {
        return "~/.site-migration/{$this->id}";
    }

    public function incrementalFilesPath(): string
    {
        return "file:///root/.backup/{$this->id}";
    }

    public function backupScriptsPath(): string
    {
        return "/home/<USER>/.xcloud-backup/{$this->name}";
    }

    public function monitoringCronJobPattern(): string
    {
        $total_sites = $this->server()->sites()->count() + 1;
        return $total_sites / 60;
    }

    public function backupDirName(): string
    {
        return str_replace('.', '-', $this->name);
    }

    public function backupSettings(): HasMany
    {
        return $this->hasMany(BackupSetting::class);
    }

    public function backupFiles(): HasManyThrough
    {
        return $this->hasManyThrough(BackupFile::class, BackupSetting::class);
    }

    public function getDefaultBillingOfferService(): BillingServices
    {
        return BillingServices::Site;
    }

    /**
     * @throws Exception
     */
    public function createCloudflareOriginCertificate()
    {
        foreach ($this->team()->cloudflareIntegrations as $cloudflareIntegration) {
            if ($this->getMeta('cloudflare_integration.domain_active_on_cloudflare')) {
                $cloudflareService = new CloudflareService($cloudflareIntegration);
                // check if already have a certificate for this domain
                $originCertificateId = $this->getMeta('cloudflare_integration.origin_certificate.certificate_id');
                if (!empty($originCertificateId)) {
                    $response = $cloudflareService->getOriginCertificate($originCertificateId);
                    if (Arr::get($response, 'success') && Arr::get($response, 'result.id') == $originCertificateId) {
                        $response = $cloudflareService->revokeOriginCertificate($originCertificateId);
                        if (Arr::get($response, 'success')) {
                            Log::info('origin certificate: ' . $originCertificateId . ' revoked from cloudflare');
                        }
                    }
                }
                $response = $cloudflareService->createOriginSSLCertificate($this);
                // if fails, try with other cloudflare integrations
                if (!Arr::get($response, 'success')) {
                    continue;
                }
                return $response;
            }
        }
        return null;
    }

    public function domainActiveOnCloudflare(): bool
    {
        return Arr::get($this->meta, 'cloudflare_integration.domain_active_on_cloudflare') ?? false;
    }

    public function removeCloudflareIntegration(): bool
    {
        return $this->update([
           'meta->cloudflare_integration' => [],
        ]);
    }

    public function productionToStaging($newDomain): bool
    {
        return Str::contains($this->name, 'wp1.site')
            && !Str::contains($newDomain, 'wp1.site')
            && $this->ssl_provider !== SslCertificate::PROVIDER_CLOUDFLARE;
    }

    public function emailProvider(): BelongsTo
    {
        return $this->belongsTo(EmailProvider::class);
    }

    public function disabledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'disabled_by');
    }

    function readNginxConf()
    {
        return $this->runInline(new ReadSiteNginxConf($this))->output;
    }

    function readHtaccess()
    {
        return $this->runInline(new ReadSiteHtAccess($this))->output;
    }

    public function getNginxXFrameOption()
    {
        return $this->getMeta('modify_x_frame_options', false) ?
            $this->getMeta('x_frame_options', 'SAMEORIGIN') : '';
    }

    public function getDomainTypeForEmailProvider()
    {
        return Arr::get($this->email_provider_data, 'domain_type');
    }

    public function getSiteCreatorName($name = null)
    {
        $userId = $this->getMeta('user_id');
        if ($userId) {
            // Use the `find` method directly and use optional to handle potential null values
            return optional(User::find($userId))->name;
        }
        return $name;
    }

    public function setShuffleSalts(): void
    {
        $this->update(['meta->shuffle_salts' => true]);
        $this->runInline(new SiteSaltsShuffle(server: $this->server, site: $this));
        Log::info('ShuffleSalts updated on site ' . $this->id);
    }

    public function getPhpVersionSettings(): ?array
    {
        return $this->runInline(new GetPhpVersionSettings(
            site: $this,
            server: $this->server
        ))->output_json;
    }

    public function getLsPhpVersionAttribute(): string
    {
        return str_replace('.', '', $this->php_version);
    }

    public function getSitePathAttribute(): string
    {
        return $this->manager()->siteBasePath();
    }

    public function customNginxConfigs(): HasMany
    {
        return $this->hasMany(CustomNginx::class);
    }

    public function isDisable(): bool
    {
        return $this->is_disabled ?: false;
    }

    public function domainChallenges(): HasMany
    {
        return $this->hasMany(DomainChallenge::class);
    }

    public function checkRedisObjectCacheInstalled(): bool
    {
        $file = $this->site_path . '/wp-content/object-cache.php';
        return $this->runInline(new InlineScript("if [ -f $file ]; then echo 'true'; else echo 'false'; fi"))?->output === 'true';
    }

    public function updateVulnerabilityScan(bool $vulnerability_scan): void
    {
        $this->saveMeta('vulnerability_scan', $vulnerability_scan);
    }

    public function isVulnerabilityScanEnable(): bool
    {
        // Check if the site is in the vulnerability setting sites into loaded relation first
        if ($this->relationLoaded('server') && $this->server->relationLoaded('vulnerabilitySetting')) {
            return is_null($this->server?->vulnerabilitySetting?->sites) || in_array($this->id, $this->server?->vulnerabilitySetting?->sites ?: []);
        }

        if ($this->wordpress_version && $this->hasDatabase()) {
            return VulnerabilitySetting::where('server_id', $this->server_id)
                ->where(function ($query) {
                    $query->whereJsonContains('sites', $this->id)
                        ->orWhereNull('sites');
                })
                ->exists();
        }
        return false;
    }

    public function vulnerabilitySetting()
    {
        return $this->server->vulnerabilitySetting;
    }

    public function isIPSite(): bool
    {
        return filter_var($this->name, FILTER_VALIDATE_IP);
    }

    public function isStagingSite(): bool
    {
        return $this->parent_site_id && $this->environment === self::STAGING;
    }

    public function isTestingSite(): bool
    {
        return !$this->parent_site_id && $this->environment === self::TESTING;
    }

    public function stagingSites(): HasMany
    {
        return $this->hasMany(Site::class, 'parent_site_id');
    }

    public function productionSite(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'parent_site_id');
    }

    public function fetchProductionSiteDatabaseTables(): Task
    {
        return $this->runInline(new FetchProductionSiteDatabaseTables($this));
    }
    public function deploymentLogs(): HasMany
    {
        return $this->hasMany(DeploymentLog::class, 'production_site_id');
    }

    public function deploymentPullLogs(): HasMany
    {
        return $this->hasMany(DeploymentLog::class, 'destination_site_id');
    }

    public function deploymentPushLogs(): HasMany
    {
        return $this->hasMany(DeploymentLog::class, 'source_site_id');
    }

    public function vulnerabilities()
    {
        return $this->hasMany(VulnerabilitySite::class);
    }

    public function patchstackVulnerabilitySite() :HasMany
    {
        return $this->hasMany(PatchstackVulnerabilitySite::class);
    }

    public function syncVulnerabilities($vulnerabilities, bool $auto_update=true): void
    {

        $site_vulnerabilities = $this->vulnerabilities()->get();

        $wp_version = $this->wp_updates['wp_version'] ?? '';
        $wp_updates = collect($this->wp_updates)->flatten(1)->keyBy('name');
        $vulnerabilities_data = collect($vulnerabilities)
            ->flatten(1)
            ->pluck('vulnerabilities')
            ->flatten(1)
            ->filter()
            ->unique('slug')
            ->mapWithKeys(function ($vulnerability) use ($site_vulnerabilities, $wp_version, $wp_updates) {
                $slug = $vulnerability['slug'];
                $uuid = $vulnerability['uuid'];

                $version = $slug === 'wordpress'
                    ? $wp_version
                    : $wp_updates->get($slug)['version'] ?? null;

                $existing_vulnerability = $site_vulnerabilities
                    ->where('slug', $slug)
                    ->where('version', $version)
                    ->first();

                if ($existing_vulnerability) {
                    return [$slug => $existing_vulnerability->toArray()];
                }

                return [$slug => [
                    'uuid' => $uuid,
                    'slug' => $slug,
                    'version' => $version,
                    'site_id' => $this->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]];
            })
            ->values()
            ->toArray();
        $this->vulnerabilities()->delete();
        if (count($vulnerabilities_data) > 0){
            VulnerabilitySite::insert($vulnerabilities_data);
            if ($auto_update && $this->vulnerabilitySetting()->auto_update){
                $last_fixed_at = $this->getMeta('vulnerability_fixed_at');
                //check if last fixed at is more than 24 hours
                if (!$last_fixed_at || now()->diffInHours($last_fixed_at) > VulnerabilitySetting::VULNERABILITY_FIX_INTERVAL_HOURS){
                    $this->solveVulnerabilities();
                }
            }
        }
    }

    public function syncPatchstackVulnerabilities($vulnerabilities, bool $auto_update=true): void
    {
        $vulnerabilities = json_decode($vulnerabilities);
        $site_vulnerabilities = $this->patchstackVulnerabilitySite()->get();
        $wp_version = $this->wp_updates['wp_version'] ?? '';
        $wp_updates = collect($this->wp_updates)->flatten(1)->keyBy('title');

        $vulnerabilities_data = collect($vulnerabilities)
            ->flatten(1)
            ->filter()
            ->unique('name')
            ->map(function ($vulnerability) use ($site_vulnerabilities, $wp_version, $wp_updates) {
                $name = $vulnerability->name;
                $slug = strtolower(preg_replace('/[^A-Za-z0-9]+/', '-', trim($name)));

                $version = $name === 'WordPress'
                    ? $wp_version
                    : $wp_updates->get($name)['version'] ?? null;

                $existing = $site_vulnerabilities
                    ->where('slug', $slug)
                    ->where('version', $version)
                    ->first();

                if ($existing) {
                    return null; // Skip duplicates
                }

                return [
                    'slug' => $slug,
                    'version' => $version,
                    'site_id' => $this->id,
                    'ignored' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            })
            ->filter()
            ->values()
            ->toArray();

        //$this->patchstackVulnerabilitySite()->delete();
        if (count($vulnerabilities_data) > 0){
            PatchstackVulnerabilitySite::insert($vulnerabilities_data);
            if ($auto_update && $this->vulnerabilitySetting()->auto_update){
                $last_fixed_at = $this->getMeta('vulnerability_fixed_at');
                if (!$last_fixed_at || now()->diffInHours($last_fixed_at) > VulnerabilitySetting::VULNERABILITY_FIX_INTERVAL_HOURS){
                    $this->solvePatchstackVulnerabilities();
                }
            }
        }
    }

    public function solveVulnerabilities(): void
    {
        $vulnerabilities = $this->vulnerabilities()
            ->whereNot('ignored', true)
            ->pluck('slug')
            ->toArray();
        if (count($vulnerabilities) > 0) {
            $jobs = [];
            if ($this->vulnerabilitySetting()->auto_backup) {
                $backupSetting = $this->backupSettings()->orderBy('is_local')->first();
                if ($backupSetting) {
                    $jobs[] = new SiteBackupNow(site: $this, backupSetting: $backupSetting);
                } else {
                    $jobs[] = new TakeSiteBackupJob(server: $this->server, site: $this);
                }
            }
            $jobs[] = new UpdateVulnerabilitiesJob(site: $this, vulnerabilities: $vulnerabilities);
            $this->saveMeta('vulnerability_fixed_at', now()->toDateTimeString());
            Bus::chain($jobs)
                ->catch(fn (Batch $batch, Throwable $e) => Log::info("solveVulnerabilities:" . $e->getMessage()))
                ->dispatch();
        }
    }

    public function solvePatchstackVulnerabilities(): void
    {
        $vulnerabilities = $this->patchstackVulnerabilitySite()
            ->whereNot('ignored', true)
            ->pluck('slug')
            ->toArray();
        if (count($vulnerabilities) > 0) {
            $jobs = [];
            if ($this->vulnerabilitySetting()->auto_backup) {
                $backupSetting = $this->backupSettings()->orderBy('is_local')->first();
                if ($backupSetting) {
                    $jobs[] = new SiteBackupNow(site: $this, backupSetting: $backupSetting);
                } else {
                    $jobs[] = new TakeSiteBackupJob(server: $this->server, site: $this);
                }
            }
            $jobs[] = new UpdateVulnerabilitiesJob(site: $this, vulnerabilities: $vulnerabilities);
            $this->saveMeta('vulnerability_fixed_at', now()->toDateTimeString());
            Bus::chain($jobs)
                ->catch(fn (Batch $batch, Throwable $e) => Log::info("solveVulnerabilities:" . $e->getMessage()))
                ->dispatch();
        }
    }

    public function ignoreVulnerability(string $slug, bool $is_ignored): void
    {
        $this->vulnerabilities()
            ->where('slug',$slug)
            ->update([
                'ignored' => $is_ignored
            ]);
    }

    public function ignorePatchstackVulnerability(string $slug, bool $is_ignored): void
    {
        $this->patchstackVulnerabilitySite()
            ->where('slug', $slug)
            ->update([
                'ignored' => $is_ignored
            ]);
    }

    public function getRedisObjectCacheProPrefix(): string
    {
        return $this->pluginIntegrations()->withPivot('prefix')->value('prefix');
    }

    public function getRedisObjectCacheProUser(): string
    {
        return $this->pluginIntegrations()->withPivot('username')->value('username');

    }
    public function getRedisObjectCacheProPassword(): string
    {
        return $this->pluginIntegrations()->withPivot('password')->value('password');
    }

    public function ipAddresses(): MorphMany
    {
        return $this->morphMany(IpAddress::class, 'ipable');
    }

    public function whitelistedIps(): MorphMany
    {
        return $this->ipAddresses()->where('is_whitelist', true);
    }

    public function blacklistedIps(): MorphMany
    {
        return $this->ipAddresses()->where('is_whitelist', false);
    }

    public function pluginIntegrations()
    {
        return $this->belongsToMany(PluginIntegration::class)
            ->withPivot([
                'debug_mode',
                'prefix',
                'username',
                'password',
            ])
            ->withTimestamps();
    }


    public function getAdminerPath(): ?string
    {
        try{
            return $this->manager()->siteDocumentRoot().DIRECTORY_SEPARATOR.$this->getMeta('enable_adminer','adminer-[a-zA-Z0-9]+.php');
        }catch (Exception $e){
            return null;
        }
    }

    public function getAdminEmailAttribute(): ?string
    {
       return $this->getMeta('admin_email');
    }


    public function getFileMangerPath(): ?string
    {
        try{
            return $this->manager()->siteDocumentRoot().DIRECTORY_SEPARATOR.$this->getMeta('file_manager','file-manager-[a-zA-Z0-9]+.php');
        }catch (Exception $e){
            return null;
        }

    }

    public function integrityChecksum() : HasOne
    {
        return $this->hasOne(SiteIntegrityChecksum::class);
    }

    #check checksum has matched or not
    public function hasIntegrityChecksumError(): ?bool
    {
        $ignore_at = $this->getMeta('ignore_checksum_alert_till');
        if (Carbon::parse($ignore_at)->isPast()) {
            return $this->integrityChecksum()
                ->where(fn($query) =>
                    $query->whereRaw("JSON_EXTRACT(core_checksums, '$') LIKE '%\"Error:%'")
                        ->orWhere('plugin_checksums->output', 'like', 'Error:%')
                )
                ->exists();
        }
        return false;
    }

    public function supervisorProcesses(): \Illuminate\Database\Eloquent\Builder|HasMany|Site
    {
        return $this->hasMany(SupervisorProcess::class);
    }

    public function patchstackVulnerability() :HasOne
    {
        return $this->hasOne(PatchstackVulnerability::class);
    }
}

