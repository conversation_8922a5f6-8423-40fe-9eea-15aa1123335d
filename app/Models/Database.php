<?php

namespace App\Models;

use App\Enums\DatabaseStatus;
use App\Traits\TerraformAndAPIOperations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class Database extends Model implements CipherSweetEncrypted
{
    use HasFactory, UsesCipherSweet, TerraformAndAPIOperations;

    const MANAGED_CREATE_NEW = 'create_new';
    const MANAGED_USE_EXISTING = 'use_existing';

    protected $guarded = [];

    protected $hidden = [
        'password',
    ];

    protected $appends = [];


    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('password');
    }

    public function databaseCluster(): BelongsTo
    {
        return $this->belongsTo(DatabaseCluster::class);
    }

    public function markAsCreating()
    {
        return tap($this)->update(['status' => DatabaseStatus::CREATING]);
    }

    public function isCreating(): bool
    {
        return $this->status === DatabaseStatus::CREATING;
    }

    public function isCreated(): bool
    {
        return $this->status === DatabaseStatus::CREATED;
    }

    public function markAsModifying()
    {
        return tap($this)->update(['status' => DatabaseStatus::MODIFYING]);
    }

    public function isModified(): bool
    {
        return $this->status === DatabaseStatus::MODIFIED;
    }

    public function isDeleted()
    {
        return $this->status == DatabaseStatus::DELETED;
    }

}
