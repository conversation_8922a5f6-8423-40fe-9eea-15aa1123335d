<?php

namespace App\Models;

use App\Enums\CloudProviderEnums as EnumsCloudProvider;
use App\Enums\XcloudBilling\BillingServices;
use App\Services\ProviderDatabase\DigitalOcean;
use App\Services\ProviderDatabase\xCloud;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Validation\Rule;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

/**
 * @property mixed $amount
 * @property mixed $currency
 */
class CloudProvider extends Model implements CipherSweetEncrypted
{
    use HasFactory, UsesCipherSweet;

    protected $guarded = [];

    protected $casts = [
        'gcp_access' => 'array',
        'token_expires_at' => 'datetime',
        'service_names' => 'array',
        'credentials' => 'json',
        'provider' => EnumsCloudProvider::class,
    ];

    protected $hidden = [
        'do_token',
        'api_key',
        'credentials',
        'access_token',
        'refresh_token',
        'gcp_access'
    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('do_token');
        $encryptedRow->addField('api_key');
        $encryptedRow->addField('credentials');
    }


    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function servers()
    {
        return $this->hasMany(Server::class);
    }

    function getProviderReadableAttribute()
    {
        return $this->provider->getProviderReadableAttribute();
    }

    function getProviderURLAttribute(): string
    {
        return match ($this->provider) {
            EnumsCloudProvider::DIGITALOCEAN => 'digitalocean',
            EnumsCloudProvider::XCLOUD => 'xcloud',
            EnumsCloudProvider::GCP => 'gcp',
            EnumsCloudProvider::LINODE => 'linode',
            default => 'other',
        };
    }

    static function asRule()
    {
        return Rule::in([EnumsCloudProvider::DIGITALOCEAN->value]);
    }

    function databaseCluster(): HasOne
    {
        return $this->hasOne(DatabaseCluster::class);
    }

    function isUsingConnectedProvider(): bool
    {
        // EnumsCloudProvider::all() is providing all the value of CloudProviderEnums including xCloud, xCloud
        // Provider and managed, except ANY
        return $this->provider && in_array($this->provider, EnumsCloudProvider::asValue(), true);
    }

    function createDatabase(Site $site, $job)
    {
        return match ($this->provider) {
            EnumsCloudProvider::DIGITALOCEAN => (new DigitalOcean($site, $job))(),
            EnumsCloudProvider::XCLOUD => (new xCloud($site, $job))()
        };
    }

    public function isDigitalOcean(): bool
    {
        return $this->provider === EnumsCloudProvider::DIGITALOCEAN;
    }

    public function isXcloud(): bool
    {
        return $this->provider === EnumsCloudProvider::XCLOUD || $this->provider === EnumsCloudProvider::XCLOUD_VULTR || $this->provider === EnumsCloudProvider::XCLOUD_PROVIDER;
    }

    public function isXCloudOrWhiteLabel() : bool
    {
        return $this->isXcloud() || $this->provider === EnumsCloudProvider::WHITE_LABEL_VULTR;
    }

    public function isAws(): bool
    {
        return $this->provider === EnumsCloudProvider::AWS;
    }

    public function isGcp(): bool
    {
        return $this->provider === EnumsCloudProvider::GCP;
    }

    public function isVultr(): bool
    {
        return $this->provider === EnumsCloudProvider::VULTR || $this->provider === EnumsCloudProvider::XCLOUD_VULTR ||  $this->provider === EnumsCloudProvider::XCLOUD_PROVIDER;
    }

    public function getAccessToken(): string
    {
        return match ($this->provider) {
            EnumsCloudProvider::DIGITALOCEAN => $this->do_token ?: $this->access_token,
            EnumsCloudProvider::XCLOUD => config('services.digitalocean.token'),
            EnumsCloudProvider::AWS => $this->awsBindCredentialWithServices(),
            EnumsCloudProvider::GCP => isset($this->gcp_access) ? (is_array($this->gcp_access) ? json_encode($this->gcp_access) : $this->gcp_access) : $this->access_token,
            EnumsCloudProvider::VULTR, EnumsCloudProvider::HETZNER => $this->api_key,
            EnumsCloudProvider::LINODE => $this->access_token
        };
    }


    private function awsBindCredentialWithServices(): ?string
    {
        if ($this->provider == EnumsCloudProvider::AWS) {
            $credential = $this->credentials;
            if(!empty($this->service_names)){
                $credential['service_names'] = $this->service_names;
            }
            // Encode the array back into a JSON string
            return json_encode($credential);
        }
        return null;
    }

    public function awsBindCredentialWithRegion(string $region): ?string
    {
        if ($this->provider == EnumsCloudProvider::AWS) {
            $credential = $this->credentials;
            // Add the region to the array
            $credential['region'] = $region;
            // Encode the array back into a JSON string
            return json_encode($credential);
        }
        return null;
    }


    public function isResizable(): bool
    {
//        return $this->provider === EnumsCloudProvider::XCLOUD
//            || $this->provider === EnumsCloudProvider::DIGITALOCEAN
//            || $this->provider === EnumsCloudProvider::GCP
//            || $this->provider === EnumsCloudProvider::VULTR
//            || $this->provider === EnumsCloudProvider::XCLOUD_VULTR;
//
//        only under xcloud and vultr for now
        return $this->provider === EnumsCloudProvider::VULTR || $this->provider === EnumsCloudProvider::XCLOUD_VULTR || $this->provider === EnumsCloudProvider::XCLOUD_PROVIDER;
    }

    public function isProviderBackupManageable(): bool
    {
        $manageableProviders = [
            EnumsCloudProvider::VULTR,
            EnumsCloudProvider::XCLOUD_VULTR,
            EnumsCloudProvider::XCLOUD_PROVIDER,
            EnumsCloudProvider::WHITE_LABEL_VULTR
        ];

        return in_array($this->provider, $manageableProviders, true);
    }

    static function createXcloudVultr()
    {
        return CloudProvider::factory()->create([
            'provider' => EnumsCloudProvider::XCLOUD_VULTR,
            'user_id' => 1,
            'name' => 'default xcloud vultr'
        ]);
    }

    static function createXcloudProviderHosting()
    {
        return CloudProvider::factory()->create([
            'provider' => EnumsCloudProvider::XCLOUD_PROVIDER,
            'user_id' => 1,
            'name' => 'xcloud provider hosting'
        ]);
    }

    static function createWhiteLabelVultr()
    {
        return CloudProvider::factory()->create([
            'provider' => EnumsCloudProvider::WHITE_LABEL_VULTR,
            'user_id' => 1,
            'name' => 'white label managed hosting'
        ]);
    }

    public function getDefaultBillingService(): BillingServices
    {
        return BillingServices::SelfManagedHosting;
    }

    public function getPricing(BillingServices $service): float
    {
        // TODO: Implement getPricing() method.
    }

    public function getBillingTitle(BillingServices $service): string
    {
        // TODO: Implement getBillingTitle() method.
    }

    public function getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator(): array
    {
        return [];
    }

    public function getColumnsToStoreDataOnBill(): array
    {
        return [];
    }

    public function teamInfo(): Team
    {
        // TODO: Implement teamInfo() method.
    }
}
