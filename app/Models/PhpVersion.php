<?php

namespace App\Models;

use App\Enums\SiteType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

class PhpVersion extends Model
{
    protected $dates = [
        'php_version_updated_at'
    ];

    const DEFAULT_MAX_INPUT_VARS = 3500;
    const DEFAULT_SESSION_GC_MAXLIFETIME = '1440';
    const ALLOW_URL_FOPEN = 'On';

    const DEFAULT_UPLOAD_MAX_FILESIZE = '250M'; // upload_max_filesize
    const DEFAULT_POST_MAX_SIZE = '256M'; // post_max_size

    const DEFAULT_MAX_EXECUTION_TIME = 180; // max_execution_time
    const DEFAULT_MEMORY_LIMIT = '512M'; // memory_limit
    const DEFAULT_PM_MAX_CHILDREN = 20;
    const MAX_INPUT_TIME = 180;
    /**
     * Available versions of <PERSON><PERSON> in the system
     *
     * @var array
     */
    const VERSIONS = [
        '5.6',
        '7.0',
        '7.1',
        '7.2',
        '7.3',
        '7.4',
        '8.0',
        '8.1',
        '8.2',
        '8.3',
        '8.4',
    ];

    /**
     * Available versions of PHP in the system
     *
     * @var array
     */
    const LS_VERSIONS = [
        '7.4',
        '8.0',
        '8.1',
        '8.2',
        '8.3',
    ];

    const DEFAULT = '8.1';

    /**
     * Get all version
     *
     * @param  Server|null  $server
     * @return array
     */
    static function getVersions(?Server $server = null): array
    {
        if ($server && $server->stack?->isOpenLiteSpeed()){
            # https://github.com/xCloudDev/xCloud/pull/1607
            if ($server->isArmCpu()) {
                # Ubuntu 24 arm
                if (version_compare($server->ubuntu_version, '24.04', '>=') && version_compare($server->ubuntu_version, '26.04', '<')) {
                    return ['8.3'];
                }

                # Ubuntu 20 arm
                if (version_compare($server->ubuntu_version, '20.04', '>=') && version_compare($server->ubuntu_version, '22.04', '<')) {
                    return ['7.4', '8.1'];
                }

                // Ubuntu 22 arm
                return collect(PhpVersion::LS_VERSIONS)->reject(fn($version) => $version === "8.0")->values()->toArray();
            }

            if (version_compare($server->ubuntu_version, '24.04', '>=') && version_compare($server->ubuntu_version, '26.04', '<')) {
                // Version is 24.04 or any of its sub versions.
                // 24.04 does not support PHP 7.4 & 8.0
                return self::getFilteredVersions(self::LS_VERSIONS, ['7.4', '8.0']);
            }

            return self::LS_VERSIONS;
        }

        return self::VERSIONS;
    }

    public static function canUseImagick($server): bool
    {
        // https://github.com/xCloudDev/xCloud/issues/1898
        if ($server && $server->stack?->isOpenLiteSpeed() && $server->isArmCpu()){
            # Ubuntu 24 arm
            if (version_compare($server->ubuntu_version, '24.04', '>=') && version_compare($server->ubuntu_version, '26.04', '<')) {
                return false;
            }
        }

        return true;
    }

    static function getFilteredVersions(array $versions, array $excluded): array
    {
        return array_values(array_filter($versions, function($version) use ($excluded) {
            return !in_array($version, $excluded);
        }));
    }

    public static function getDefaultEnvVars(): array
    {
        return [
            'upload_max_filesize' => self::DEFAULT_UPLOAD_MAX_FILESIZE,
            'max_execution_time' => self::DEFAULT_MAX_EXECUTION_TIME,
            'memory_limit' => self::DEFAULT_MEMORY_LIMIT,
            'max_input_vars' => self::DEFAULT_MAX_INPUT_VARS,
            'post_max_size' => self::DEFAULT_POST_MAX_SIZE,
            'session_gc_maxlifetime' => self::DEFAULT_SESSION_GC_MAXLIFETIME,
            'max_input_time' => self::MAX_INPUT_TIME,
            'pm_max_children' => self::DEFAULT_PM_MAX_CHILDREN,
        ];
    }

    /**
     * Get the list of available versions of PHP inside Rule::in
     *
     * @param  bool  $acceptNull
     * @param  Server|null  $server
     * @return In
     */
    static function asRule(bool $acceptNull = false, ?Server $server = null): In
    {
        if ($acceptNull) {
            return Rule::in(array_merge(self::getVersions($server), [null]));
        }

        return Rule::in(self::getVersions($server));
    }

    static function asOneClickAppRule(bool $acceptNull = false, ?Server $server = null, ?string $appSlug = null): In
    {
        $phpVersions = self::getVersions($server);

        if ($appSlug) {
            $minPhpVersion = SiteType::fromValue($appSlug)->minPhpVersion();
            if ($minPhpVersion) {
                $phpVersions = array_filter(
                    $phpVersions,
                    fn($version) => version_compare($version, $minPhpVersion, '>=')
                );
            }
        }

        return Rule::in($acceptNull ? array_merge($phpVersions, [null]) : $phpVersions);
    }


    function server()
    {
        return $this->belongsTo(Server::class);
    }
}
