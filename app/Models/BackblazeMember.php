<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use ParagonIE\CipherSweet\EncryptedRow;
use <PERSON><PERSON>\LaravelCipherSweet\Concerns\UsesCipherSweet;
use <PERSON><PERSON>\LaravelCipherSweet\Contracts\CipherSweetEncrypted;
class BackblazeMember extends Model implements CipherSweetEncrypted
{
    use UsesCipherSweet;
    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('application_key_id');
        $encryptedRow->addField('application_key');
    }

}
