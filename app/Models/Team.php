<?php

namespace App\Models;

use App\Addons\MailboxAddon\MailboxAddon;
use App\Enums\CloudProviderEnums;
use App\Enums\FluentCRM\SubscriptionStatuses;
use App\Enums\FluentCRM\TagTypes;
use App\Enums\NotificationIntegrationTypes;
use App\Enums\XcloudBilling\StripeProductSubscriptionStatus;
use App\Enums\XcloudBilling\TeamBillingAlertType;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\BillType;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Enums\XcloudBilling\PaymentMethodStatus;
use App\Enums\XcloudBilling\PlansEnum;
use App\Enums\XcloudBilling\TeamBillingStatus;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Interfaces\SupportWhiteLabel;
use App\Jobs\SubscribeUserToFluentCRM;
use App\Jobs\UpdateContactOnFluentCRM;
use App\Models\Addons\EmailAccount;
use App\Models\Addons\MailboxDomain;
use App\Services\Integrations\ElasticEmailService;
use App\Services\XcloudProduct\ActiveOffers;
use App\Services\XcloudProduct\CalculateBill;
use App\Traits\BelongsToWhiteLabel;
use App\Traits\EventDispatchMailer;
use App\Traits\HasTags;
use App\Traits\MetaAccessors;
use App\Traits\Statistic;
use App\Traits\SupportUtmMetaAccessor;
use App\Traits\TeamBillingHelper;
use App\Traits\xCloudNotifiable;
use Database\Seeders\BluePrintSeeder;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Http\UploadedFile;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Laravel\Jetstream\Events\TeamCreated;
use Laravel\Jetstream\Events\TeamDeleted;
use Laravel\Jetstream\Events\TeamUpdated;
use Laravel\Jetstream\HasProfilePhoto;
use Laravel\Jetstream\Jetstream;
use Laravel\Jetstream\Team as JetstreamTeam;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;
use function Pest\Laravel\get;


/**
 * @property mixed $gcpCloudProviders
 * @property mixed $id
 * @property mixed|null $activePlan
 * @property mixed $whiteLabel
 * @property mixed $name
 * @property mixed $email
 */
class Team extends JetstreamTeam implements CipherSweetEncrypted, SupportWhiteLabel
{
    use HasFactory, HasProfilePhoto, HasTags, EventDispatchMailer, xCloudNotifiable, MetaAccessors, UsesCipherSweet,
        SupportUtmMetaAccessor, TeamBillingHelper, Statistic, BelongsToWhiteLabel;

    const TEAM_PHOTO_COLORS = [
        [
            'text' => 'FFFFFF',
            'background' => 'F4DF4E'
        ],
        [
            'text' => 'FFFFFF',
            'background' => '078282'
        ],
        [
            'text' => 'FFFFFF',
            'background' => 'b5910d'
        ],
        [
            'text' => 'FFFFFF',
            'background' => '6E8898'
        ],
        [
            'text' => 'FFFFFF',
            'background' => 'A37B73'
        ],
        [
            'text' => 'FFFFFF',
            'background' => 'D34F73'
        ]
    ];

    const DEFAULT_TEAM_COUNT = 10;
    const DEFAULT_MAX_SERVERS = 1;
    const DEFAULT_BASIC_MAX_SERVERS = 1;

    const VULNERABILITY_NOTIFICATION_INTERVAL = 10; // in days

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'personal_team' => 'boolean',
        'permissions' => 'array',
        'plan_activated_from' => 'datetime',
        'billing_status' => TeamBillingStatus::class,
        'billing_type' => BillType::class,
        'billing_alert_type' => TeamBillingAlertType::class,
        'availed_offers' => 'array',
        'meta' => 'array',
        'settings' => 'array',
        'billing_emails' => 'array',
        'extension_time_for_billing' => 'datetime',
        'billing_comment' => 'string',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'name',
        'email',
        'notification_language',
        'personal_team',
        'elastic_email_password',
        'billing_emails'
    ];

    protected $hidden = [
        'elastic_email_password',
        'elastic_email_api_key',
    ];

    protected $with = ['activePlan'];

    protected $appends = ['team_photo_url', 'is_playground'];

    const TEAM_ADMIN = 'team-admin';
    const SERVER_ADMIN = 'server-admin';

    const SITE_ADMIN = 'site-admin';

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'created' => TeamCreated::class,
        'updated' => TeamUpdated::class,
        'deleted' => TeamDeleted::class,
    ];

    const DISABLE_FILE_MANAGER_INTERVALS = [
        'six_hour' => '6 Hour',
        'twelve_hour' => '12 Hour',
        'one_day' => '1 Day',
        'one_week' => '1 Week',
        'one_month' => '1 Month',
    ];

    const DISABLE_ADMINER_INTERVALS = [
        'six_hour' => '6 Hour',
        'twelve_hour' => '12 Hour',
        'one_day' => '1 Day',
        'one_week' => '1 Week',
        'one_month' => '1 Month',
    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('elastic_email_password');
        $encryptedRow->addField('elastic_email_api_key');
    }

    public function teamUserPermissions() : HasMany
    {
        return $this->hasMany(Membership::class, 'team_id');
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function (Team $team) {
            if ($team->active_plan_id) {
                $billingPlan = BillingPlan::where('id', $team->active_plan_id)->first();

                # TODO: @Caution: Checking Has Active Payment Method with query instead of $team->hasActivePaymentMethod() because it was not working on the time of updating
                $hasActivePaymentMethod = PaymentMethod::where('default_card', true)
                    ->where('team_id', $team->id)
                    ->where('status', PaymentMethodStatus::ACTIVE->value)->exists();

                if ($billingPlan->requires_billing) {
                    if (!$hasActivePaymentMethod) {
                        throw new Exception('Plan ' . $billingPlan->name_as_title . ' requires active payment method but no active payment method found');
                    }
                }

                Log::info('#BillingPlan - Creating team#' . $team->id . ' - ' . $team?->email . ' with plan ' . $billingPlan?->name_as_title);

                $team->active_plan_id = $billingPlan?->id;
                $team->plan_activated_from = now();
            }
        });

        static::created(function (Team $team) {
            // Subscribe user to FluentCRM
//            UpdateContactOnFluentCRM::dispatch($team);
        });

        static::updating(function (Team $team) {
            if ($team->isDirty('active_plan_id')) {
                $billingPlan = BillingPlan::where('id', $team->active_plan_id)->first();

                if (!$billingPlan->is_active) {
                    throw new Exception('Plan ' . $billingPlan->name_as_title . ' is not active');
                }

                if ($billingPlan) {
                    $team->active_plan_id = $billingPlan?->id;
                    $team->plan_activated_from = now();
                }

                // TODO: @Caution: Checking Has Active Payment Method with query instead of $team->hasActivePaymentMethod() because it was not working on the time of updating
                $hasActivePaymentMethod = PaymentMethod::where('default_card', true)
                    ->where('team_id', $team->id)
                    ->where('status', PaymentMethodStatus::ACTIVE->value)->exists();

                if ($billingPlan && $billingPlan->requires_billing && !$hasActivePaymentMethod) {
                    throw new Exception('#BillingPlan - Plan ' . $billingPlan?->name_as_title . ' requires active payment method but no active payment method found');
                }

                Log::info('#BillingPlan Updating team#' . $team->id . ' plan - ' . $team?->email . ' with plan ' . $billingPlan?->name_as_title . (auth()->check() ? ' by ' . auth()?->user()?->email : ''));

                if ($team->isDirty('extension_time_for_billing')) {
                    Log::warning('Team#' . $team->id . ' - ' . $team->email . ' has extension time for billing. Permission granted by user' . auth()->user()->email);
                }
            }
        });

        // FluentCRM
        static::updated(function (Team $team) {
            if (config('app.fluent_crm_api_username') && config('app.fluent_crm_api_password') && !app()->runningUnitTests()) {
                if ($team->isDirty('active_plan_id') && $team->getOriginal('active_plan_id') && $team->fluent_crm_contact_id) {
//                    UpdateContactOnFluentCRM::dispatch($team);
                }
            }
        });

        static::created(function (Team $team) {
            #inserting default blueprints by seeder
            app(BluePrintSeeder::class)->run($team);
        });

        static::deleted(function (Team $team) {
            $team->bluePrints()->delete();
        });
    }

    public function ownedWhiteLabel() : HasOne
    {
        return $this->hasOne(WhiteLabel::class, 'owner_team_id');
    }

    public function getMaskedApiKeyAttribute(): string
    {
        $apiKey = $this->getElasticEmailSubAccountApiKey();
        if (!empty($apiKey) && strlen($apiKey) >= 4) {
            $lastFourChars = substr($apiKey, -4); // Get the last 4 characters
            return str_repeat('*', 20) . $lastFourChars;
        }
        return '';
    }

    public function getIsPlaygroundAttribute(): bool
    {
        return $this->isPlayGround();
    }

    public function getFreeWhiteLabelSubscriptionProduct() : SubscriptionProduct | null
    {
        $ltdPackagesUnitCount = $this->packages()
            ->where('service_type', BillingServices::SelfManagedHosting)
            ->where('renewal_type', BillRenewalPeriod::Lifetime)
            ->sum('unit');

        $serverLimit = match (true) {
            $ltdPackagesUnitCount >= 250 => 400,
            $ltdPackagesUnitCount >= 50 => 150,
            $ltdPackagesUnitCount >= 15 => 60,
            $ltdPackagesUnitCount >= 2 => 25,
            default => 0,
        };

        return SubscriptionProduct::where('unit', '<=', $serverLimit)
            ->where('is_active', true)
            ->where('service_type', BillingServices::WhiteLabelSubscription)
            ->orderBy('unit', 'desc')
            ->first();
    }

    public function hasLtdPackage(): bool
    {
        return $this->packages()->where('renewal_type', BillRenewalPeriod::Lifetime)->exists();
    }

    public function hasLimitOnLtdPackage(): bool
    {
        return intval($this->packages()?->where('renewal_type', BillRenewalPeriod::Lifetime)?->sum('unit')) >
            $this->bills()->active()->where('renewal_period', BillRenewalPeriod::Lifetime)->count();
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'team_product', 'team_id', 'product_id')
            ->using(TeamProduct::class)
            ->withPivot('id', 'attached_at');
    }

    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class, 'team_package', 'team_id', 'package_id')
            ->using(TeamPackage::class)
            ->withPivot('id', 'attached_at');
    }

    public function attachProduct($id, array $attributes = [], $touch = true): void
    {
        $this->products()->attach($id, array_merge(['attached_at' => now()], $attributes), $touch);
    }

    public function detachProduct($id, $touch = true): void
    {
        $this->products()->detach($id, $touch);
    }

    public function attachPackage($id, array $attributes = [], $touch = true): void
    {
        $this->packages()->attach($id, array_merge(['attached_at' => now()], $attributes), $touch);
    }

    public function countSelfManagedHostingForPlanBasedBilling(): int
    {
        $activePlan = $this->activePlan;

        if (!$activePlan) return 0;

        return $this->servers()->where(function ($query) {
            return $query->whereHas('bills', function ($q) {
                return $q->whereNull('package_id')->where('has_offer', false);
            })->orWhereDoesntHave('bills');
        })->where(function ($query) {
            return $query->whereHas('cloudProvider', function ($q) {
                return $q->whereNotIn('provider', CloudProviderEnums::underXCloud());
            })->orWhereDoesntHave('cloudProvider');
        })->count();
    }

    public function updateBillingComment(string $comment, TeamBillingAlertType $billingAlertType = TeamBillingAlertType::Info): void
    {
        $this->billing_comment = $comment;
        $this->billing_alert_type = $billingAlertType;
        $this->saveQuietly();
    }

    public function eligibleToUpgradeNextPlan(): PlansEnum
    {
        return PlansEnum::getPlanByServerCount($this->countSelfManagedHostingForPlanBasedBilling() + 1, $this);
    }

    protected static function booted(): void
    {
        static::updating(function (Team $team) {
            if ($team->billing_status == TeamBillingStatus::Active && $team->activePlan?->name == PlansEnum::Free && !$team->whiteLabel) {
                $team->active_plan_id = BillingPlan::where('name', PlansEnum::Starter)->value('id');
                $team->plan_activated_from = now();
            }

            if ($team->isDirty('active_plan_id')) {
                try {
                    $team->active_plan_id = BillingPlan::where('id', $team->active_plan_id)->first()?->id;
                } catch (Exception $exception) {
                    throw new Exception('Plan not found');
                }
            }
        });

        // Skip eager loading activePlan for broadcasting
        static::addGlobalScope('excludeActivePlanForBroadcasting', function (Builder $builder) {
            if (request()->is('broadcasting/auth') || request()->is('api/navigation-store')) {
                $builder->without('activePlan');
            }
        });
    }

    /**
     * Get the URL to the user's profile photo.
     *
     * @return string
     */
    public function getTeamPhotoUrlAttribute(): string
    {
        return $this->team_photo_path
            ? Storage::disk($this->profilePhotoDisk())->url($this->team_photo_path)
            : $this->defaultProfilePhotoUrl();
    }

    public function getPhotoPath($extension = 'jpg'): string
    {
        return "team-photos/" . $this->id . "-team-" . md5($this->email) . "." . $extension;
    }

    protected function defaultProfilePhotoUrl(): string
    {
        $cleanName = strip_tags($this->name);
        $cleanName = preg_replace('/[^A-Za-z0-9\s]/', '', $cleanName);
        $cleanName = trim($cleanName);

        if (empty($cleanName)) {
            $cleanName = 'Team';
        }

        $name = trim(collect(explode(' ', $cleanName))->map(function ($segment) {
            return mb_substr($segment, 0, 1);
        })->join(' '));

        $index = $this->id % count(self::TEAM_PHOTO_COLORS);
        $color = self::TEAM_PHOTO_COLORS[$index];

        return 'https://ui-avatars.com/api/?name=' . urlencode($name) . '&color=' . Arr::get($color, 'text') . '&background=' . Arr::get($color, 'background');
    }

    /**
     * Update Team  photo.
     *
     */
    public function updateTeamPhoto(UploadedFile $photo)
    {
        tap($this->team_photo_path, function ($previous) use ($photo) {
            $this->forceFill([
                'team_photo_path' => $photo->storePublicly(
                    'team-photos', ['disk' => $this->profilePhotoDisk()]
                ),
            ])->save();

            if ($previous) {
                Storage::disk($this->profilePhotoDisk())->delete($previous);
            }
        });
    }

    public function servers()
    {
        return $this->hasMany(Server::class);
    }

    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    public function sites(): HasManyThrough
    {
        return $this->hasManyThrough(Site::class, Server::class);
    }

    public function siteMigrations(): HasMany
    {
        return $this->hasMany(AutoSiteMigration::class);
    }

    public function siteClones(): HasMany
    {
        return $this->hasMany(AutoSiteClone::class);
    }

    public function serverMigrations(): HasMany
    {
        return $this->hasMany(ServerMigration::class);
    }

    public function serverTasks(): Collection
    {
        return $this->servers->pluck('tasks')->flatten();
    }

    public function siteTasks()
    {
        return $this->sites->pluck('tasks')->flatten();
    }

    public function alerts()
    {
        return $this->morphMany(Alert::class, 'notifiable');
    }

    public function memberships(): HasMany
    {
        return $this->hasMany(Jetstream::$membershipModel);
    }

    public function sshKeyParis(): HasMany
    {
        return $this->hasMany(SshKeyPair::class);
    }

    public function cloudProviders(): HasMany
    {
        return $this->hasMany(CloudProvider::class);
    }

    public function gcpCloudProviders(): HasMany
    {
        return $this->hasMany(CloudProvider::class)->where('type', CloudProviderEnums::GCP->value);
    }

    public function isAdmin(User $user = null)
    {
        $user = $user ?? auth()->user();

        return $user->hasTeamRole($this, User::ADMIN);
    }

    public function isOwner(User $user = null,bool $allowSupport=true)
    {
        $user = $user ?? auth()->user();

        return $user->ownsTeam($this,allowSupport: $allowSupport);
    }

    //if user has current team all server access
    public function hasAllServerAccess(User $user = null,bool $allowSupport=true): bool
    {
        $user = $user ?? auth()->user();

        return $this->isOwner($user,$allowSupport) || in_array($this->id, $user->getMeta('all_servers_team', []));
    }

    public function hasAllSiteAccess(User $user = null): bool
    {
        $user = $user ?? auth()->user();

        return $this->isOwner($user) || in_array($this->id, $user->getMeta('all_sites_team', []));
    }


    public function isFreePlan(): bool
    {
        return $this->getPlan() === PlansEnum::Free;
    }

    public function isTrailMode()
    {
        return $this->isFreePlan();
    }

    public function isStarterPlan(): bool
    {
        return $this->getPlan() === PlansEnum::Starter;
    }

    public function isProfessionalPlan(): bool
    {
        return $this->getPlan() === PlansEnum::Professional;
    }

    public function isAgencyPlan(): bool
    {
        return $this->getPlan() === PlansEnum::Agency;
    }

    public function getPlan(): PlansEnum|null
    {
        return $this->activePlan?->name;
    }

    // Todo: @CautionBilling: this is a temporary solution for Billing
    public function hasAvailedOffers(): bool
    {
        $activeOffers = ActiveOffers::get($this->activePlan?->name);
        $availedOffers = $this->availed_offers;

        if (!$activeOffers) {
            return true;
        }

        if (!$availedOffers) {
            return false;
        }

        foreach ($activeOffers as $activeOffer) {
            $activeOffer = intval($activeOffer);

            if ($activeOffer > 0) {
                $availedOffer = intval($availedOffers[$activeOffer] ?? 0);
                if ($availedOffer < $activeOffer) {
                    return false;
                }
            }
        }

        return true;
    }

    public function hasDuesToPay() : bool
    {
        return ($this->generalInvoices()->whereNotIn('status', BillingStatus::noIssueWithInvoices())->sum('amount')
                + $this->manualInvoices()->whereNotIn('status', BillingStatus::noIssueWithInvoices())->sum('amount'))
            > 0.50;
    }

    public function allowedToUseProduct(): bool
    {
        if ($this->billing_status === TeamBillingStatus::Active) {
            return true;
        }

        if (!$this->activePlan) {
            return false;
        }

        if ($this->billing_type === BillType::Postpaid) {
            return true;
        }

        $hasOverdueBills = $this->bills()->where('status', BillingStatus::Unpaid)
                ->where('has_offer', false)
                ->where('due_on', '<', now()->toDateString())
                ->sum('amount_to_pay') < 0.50;

        if ($hasOverdueBills) {
            return false;
        }

        $billingPlan = BillingPlan::where('id', $this->active_plan_id)->first();

        if (!$this->hasAvailedOffers()) {
            return true;
        }

        if ($billingPlan && $billingPlan->requires_billing) {
            return false;
        }

        if ($billingPlan && !$billingPlan->requires_billing) {
            return true;
        }

        return false;
    }

    public function getGetBrandNameAttribute(): string
    {
        return $this->whitelabel ? $this->whitelabel?->getBranding('brand_name') : config('app.name');
    }

    public function getGetBrandAddressAttribute(): string
    {
        return $this->whitelabel ? $this->whitelabel?->getBranding('address') : '124 Broadkill Road Suit 599 Milton, DE 19968 United States';
    }

    public function maxMemberCount(): int
    {
        return $this->isPlayGround() ? 1000000 : (int)($this->getMeta('max_member_count') ?: self::DEFAULT_TEAM_COUNT);
    }

    public function canAddMember(): bool
    {
        if ($this->billing_status === TeamBillingStatus::Active) {
            return true;
        }

        return ($this->maxMemberCount() - 1) > $this->users()->count() + $this->teamInvitations()->count();
    }

    public function permissions(?array $filters = null, $user = null)
    {
        $user = $user ?: auth()->user();

        if (!$user->belongsToTeam($this)) {
            return [];
        }

        $permissions = $this->isOwner($user)
            ? Arr::flatten_key(Jetstream::$permissions)
            : Membership::where(['user_id' => $user->id, 'team_id' => $this->id])->value('permissions', []) ?? [];
        if ($this->getPlan() == null) {
            $permissions = array_filter($permissions, fn($permission) => !in_array($permission, ['account:manage-ssh', 'account:manage-server:providers']));
        }
        if ($filters) {
            return array_filter($permissions, function ($permission) use ($filters) {
                foreach ($filters as $filter) {
                    if (Str::contains($permission, $filter)) {
                        return true;
                    }
                }
                return false;
            });
        }

        return $permissions;
    }

    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class);
    }

    public function activePaymentMethod(): Collection
    {
        $activePaymentMethod = $this->paymentMethods->where('default_card', true)->where('status', PaymentMethodStatus::ACTIVE);

        if ($activePaymentMethod?->isEmpty()) {
            $activePaymentMethod = $this->paymentMethods->where('status', PaymentMethodStatus::ACTIVE);
        }

        return $activePaymentMethod;
    }

    public function hasActivePaymentMethod(): bool
    {
        return $this->activePaymentMethod()?->isNotEmpty();
    }

    public function getTriedCards(): array
    {
        $key = 'tried_payment_methods_' . $this->id;

        return cache()->get($key, []);
    }

    public function generalInvoices(): HasMany
    {
        return $this->hasMany(GeneralInvoice::class)->where('type', InvoiceTypesEnum::General);
    }

    public function refunds(): HasMany
    {
        return $this->hasMany(Refund::class);
    }

    public function manualInvoices(): HasMany
    {
        return $this->hasMany(ManualInvoice::class)->where('type', InvoiceTypesEnum::Manual);
    }

    public function bills(): HasMany
    {
        return $this->hasMany(Bill::class);
    }

    public function subscriptions() : BelongsToMany
    {
        return $this->belongsToMany(SubscriptionProduct::class)
                    ->using(TeamSubscription::class)
                    ->withPivot('team_id', 'stripe_subscription_id','ends_at','status');
    }

    public function hasActiveWhiteLabelSubscription() : bool
    {
        $subscriptions = $this->subscriptions()->where('service_type', BillingServices::WhiteLabelSubscription)->get();

        foreach ($subscriptions as $subscription) {
            $teamSubscription = $subscription->pivot;
            if ($teamSubscription->status === StripeProductSubscriptionStatus::Active && $teamSubscription->ends_at > now()) {
                return true;
            }
        }

        return false;

//            ->each(function ($subscription) {
//                $teamSubscription = $subscription->pivot;
//                if ($teamSubscription->status === StripeProductSubscriptionStatus::Active && $teamSubscription->ends_at < now()) {
//                    return $teamSubscription;
//                }
//            });
//        return TeamSubscription::where('team_id', $this->id)
//            ->where('service_type', BillingServices::WhiteLabelSubscription)
//            ->where('status', StripeProductSubscriptionStatus::Active)
//            ->where('ends_at', '>', now())
//            ->exists();
    }

    public function unpaidBills(): HasMany
    {
        return $this->hasMany(Bill::class)->where('status', BillingStatus::Unpaid);
    }

    public function paidBills(): HasMany
    {
        return $this->hasMany(Bill::class)->where('status', BillingStatus::Paid);
    }

    public function hasBillingBanner(): bool
    {
        if (request()->routeIs(['server.progress'])) {
            return false;
        }

        return $this->billingIsExpired() ||
            ($this->hasDuesToPay() &&
                $this->billingIsNot(TeamBillingStatus::Active) &&
                !$this?->activePlan?->support_manual_billing);
    }

    public function setBillingStatusActive(): void
    {
        $this->billing_status = TeamBillingStatus::Active;
        $this->extension_time_for_billing = null;
        $this->saveQuietly();
    }

    public function setBillingStatusInactive(): void
    {
        $this->billing_status = TeamBillingStatus::Inactive;
        $this->saveQuietly();
    }

    public function setBillingStatusOverdue(): void
    {
        $this->billing_status = TeamBillingStatus::Overdue;
        $this->save();
    }

    public function setBillingStatusSuspended(): void
    {
        $this->billing_status = TeamBillingStatus::Suspended;
        $this->save();
    }

    public function setBillingStatusExpired(): void
    {
        $this->billing_status = TeamBillingStatus::Expired;
        $this->save();
    }

    public function billingIsExpired(): bool
    {
        return in_array($this->billing_status, TeamBillingStatus::getExpiredStatues());
    }

    public function billingIs(TeamBillingStatus $status): bool
    {
        return $this->billing_status === $status;
    }

    public function billingIsNot(TeamBillingStatus $status): bool
    {
        return $this->billing_status !== $status;
    }

    public function billingIsActive(): bool
    {
        return $this->billing_status === TeamBillingStatus::Active;
    }

    public function billingUsagesInRunningMonth(): float
    {
        // Todo: @CautionBilling: this is a temporary solution for Billing
        // This is a temporary solution for Prepaid Billing
        // The time of postpaid solution this won't work

        $cacheKey = 'billing_usage_in_running_month_' . $this->id;

        $billingUsageInRunningMonth = $this->bills()->where('next_billing_date', (new Bill())->getNextBillingDate(now(), BillRenewalPeriod::Monthly))
            ->sum(DB::raw('amount_to_pay + adjustable_amount'));

        if (Cache::get($cacheKey) > $billingUsageInRunningMonth) {
            return Cache::get($cacheKey);
        }

        return Cache::remember($cacheKey, now()->day(CalculateBill::billingDate)->endOfDay(), function () use ($billingUsageInRunningMonth) {
            return $billingUsageInRunningMonth;
        });
    }

    public function billingUsagesForUpcomingMonth(): float
    {
        return $this->bills()->withInNextBilling()->sum(DB::raw('billing_amount + adjustable_amount'));
    }

    /**
     * @throws \Exception
     */
    public function canAccessWithBilling(BillingServices $billingService): bool
    {
        if ($this->billingIsActive()) {
            return true;
        }

        $activePlan = optional($this->activePlan);

        if (!$activePlan->requires_billing && $activePlan->support_manual_billing) {
            return true;
        }

        $hasProductToUse = $this->products->where('service_type', $billingService)->count() > 0;

        if ($hasProductToUse) {
            return true;
        }

        $hasPackageToUse = $this->hasAvailablePackageToUse($billingService, $this);

        if ($hasPackageToUse) {
            return true;
        }

        $quantityAvailed = $this->availed_offers[$billingService->value] ?? 0;

        $canAvailInTotal = ActiveOffers::matchGetQuantity($billingService, $this->activePlan?->name);

        return $quantityAvailed < $canAvailInTotal;
    }

    public function activePlan(): HasOne
    {
        return $this->hasOne(BillingPlan::class, 'id', 'active_plan_id');
    }

    /**
     * This is for the Nova
     * @return BelongsTo
     */
    public function activePlan2(): BelongsTo
    {
        return $this->belongsTo(BillingPlan::class, 'active_plan_id');
    }


    public function billingIsOverdue(): bool
    {
        return $this->billing_status === TeamBillingStatus::Overdue;
    }

    public function billingIsSuspended(): bool
    {
        return $this->billing_status === TeamBillingStatus::Suspended;
    }

    /**
     * @throws Exception
     */
    public function setPlan(PlansEnum $plan): void
    {
        try {
            $plan = BillingPlan::where('name', $plan)->firstOrFail();
            if ($plan) {
                $this->active_plan_id = $plan->id;
                $this->save();
            }
        } catch (Exception $exception) {
            throw new Exception('Plan not found');
        }

    }

    public function getMonthlyBillingAmount(): float
    {
        $billingAmount = 0;

        $team = $this ?: team();

        $bills = $team->bills()->withoutRefundOrCancellation()->withoutOffer()->monthly()->withInRunningMonth()->select([
            'billing_amount', 'bill_from', 'amount_to_pay', 'adjustable_amount', 'next_billing_date', 'plan_id', 'generated_by',
            'generator_id', 'generator_type', 'service'
        ])->get();

        foreach ($bills as $billing) {
            $billingAmount += $billing->amount_to_pay;
        }

        return format_billing($billingAmount);
    }

    public function getNextMonthBillingAmount(): float
    {
        $billingAmount = 0;

        $team = $this ?: team();

        if (!$team->servers) {
            return format_billing($billingAmount);
        }

        $bills = $team->bills()->withoutRefundOrCancellation()->withoutOffer()->monthly()->active()->withInRunningMonth()->select([
            'billing_amount', 'bill_from', 'amount_to_pay', 'adjustable_amount', 'next_billing_date', 'plan_id', 'generated_by',
            'generator_id', 'generator_type', 'service', 'product_id', 'package_id', 'id'
        ])->get();

        foreach ($bills as $billing) {
            $billingAmount += $billing->generator?->getPricing($billing->service, BillRenewalPeriod::Monthly, ($billing->product ?: $billing->package), $billing->billing_amount) + $billing->adjustable_amount;
        }

        return format_billing($billingAmount);
    }

    public function getOverUsedBillingAmount(): float
    {
        $team = $this ?: team();

        return format_billing(
            $team->bills()
                ->withoutOffer()
                ->withoutRefundOrCancellation()
                ->monthly()
                ->active()
                ->createdInThisMonth()
                ->onNextMonth()
                ->where('adjustable_amount', '>', 0)
                ->sum('adjustable_amount')
        );
    }

    public function cloudflareIntegrations(): HasMany
    {
        return $this->hasMany(Cloudflare::class);
    }

    public function hasCloudflareIntegration(): bool
    {
        return $this->cloudflareIntegrations()->exists();
    }

//    public function activeCloudflareIntegration(): Cloudflare | null
//    {
//        return $this->cloudflareIntegrations()->whereDefault(true)->first();
//    }

    public function getStartingPrice(): float
    {
        return number_format((float)Arr::get($this->activePlan?->service_pricing, 'monthly.self_managed_hosting') ?? 0, 2);
    }

    public function maxCloudflareIntegrationLimitReached(): bool
    {
        return $this->cloudflareIntegrations()->count() >= (Arr::get($this->meta, 'cloudflare_integration_limit') ?? Cloudflare::MAX_INTEGRATION_LIMIT);
    }

    public function emailProviders(): HasMany
    {
        return $this->hasMany(EmailProvider::class);
    }

    public function xcloudEmailProviders(): HasMany
    {
        return $this->hasMany(EmailProvider::class)->whereProvider(\App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER);
    }

    public function createElasticEmailSubAccount($email, $credit = 100): void
    {
        $password = Str::random(10);

        $data = [
            'apikey' => config('services.elastic_email.api_key'), // main account api key
            'email' => $email,
            'password' => $password,
            'confirmPassword' => $password,
            'requiresEmailCredits' => true,
        ];

        $response = (new ElasticEmailService(config('services.elastic_email.api_key')))->createSubAccount(data: $data, credit: $credit);
//        dump($response);

        $this->update([
            'meta->elastic_email->subAccount->email' => explode('@', $this->email)[0] . '@' . 'xcloud.email',
            'meta->elastic_email->subAccount->emailOnSubaccount' => $email,
            'elastic_email_password' => $password,
            'elastic_email_api_key' => Arr::get($response, 'data'),
            'meta->elastic_email->subAccount->credit_plan' => xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS->value,
            'meta->elastic_email->subAccount->credit' => 100,
            'meta->elastic_email->subAccount->credit_used' => 0,
            'meta->elastic_email->subAccount->note' => 'Free 100 emails',
            'meta->elastic_email->subAccount->responseFromElasticEmail' => $response,
        ]);
    }

    public function updateElasticEmailSubAccountCredit($plan, $credit): void
    {
        $response = (new ElasticEmailService(config('services.elastic_email.api_key')))->updateCreditOnSubAccount(
            subAccountEmail: Arr::get($this->meta, 'elastic_email.subAccount.emailOnSubaccount'),
            credit: $credit
        );

        if ($response->getStatusCode() === 200) {
            $existingCredits = Arr::get($this->meta, 'elastic_email.subAccount.credit');
            $this->update([
                'meta->elastic_email->subAccount->credit_plan' => $plan,
                'meta->elastic_email->subAccount->credit' => $existingCredits + $credit,
                'meta->elastic_email->subAccount->notes' => 'Assigned ' . $credit . ' credits. Previous credit was ' . $existingCredits . '. Total Available credits ' . ($existingCredits + $credit)
            ]);
        }
    }

    public function subtractCreditFromSubaccount($credit): void
    {
        $response = (new ElasticEmailService(config('services.elastic_email.api_key')))->updateCreditOnSubAccount(
            subAccountEmail: Arr::get($this->meta, 'elastic_email.subAccount.emailOnSubaccount'),
            credit: $credit
        );

        if ($response->getStatusCode() === 200) {
            $existingCredits = Arr::get($this->meta, 'elastic_email.subAccount.credit');
            $this->update([
                'meta->elastic_email->subAccount->credit' => $existingCredits - $credit,
                'meta->elastic_email->subAccount->notes' => 'Subtracted ' . $credit . ' credits. Previous credit was ' . $existingCredits . '. Total Available credits ' . ($existingCredits + $credit)
            ]);
        }
    }

    public function hasXcloudEmailProvider(): bool
    {
        return $this->emailProviders()->where('provider', \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER)->exists();
    }

    public function hasAvailedFreeXcloudEmailProviderPlan(): bool
    {
        return $this->emailProviders()->whereProvider(\App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER)
            ->where('plan', xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS)
            ->exists();
    }

    public function hasAvailableEmailCredit(): bool
    {
        return $this->getMeta('elastic_email.subAccount.credit') > 0;
    }

    public function hasElasticEmailSubAccount(): bool
    {
        return $this->getMeta('elastic_email.subAccount.emailOnSubaccount') !== null;
    }

    public function getElasticEmailSubAccountApiKey()
    {
        return $this->elastic_email_api_key ?: $this->getMeta('elastic_email.subAccount.api_key');
    }

    public function getElasticEmailSubAccountEmail()
    {
        return $this->getMeta('elastic_email.subAccount.email');
    }

    public function removeElasticEmailAccount()
    {
        if($this->hasElasticEmailSubAccount()){
            (new ElasticEmailService($this->getElasticEmailSubAccountApiKey()))->removeAllDomains();
            (new ElasticEmailService($this->getElasticEmailSubAccountApiKey()))->deleteSubAccount($this->getMeta('elastic_email.subAccount.emailOnSubaccount'));
        }
    }

    public function isPlayGround(): bool
    {
        return $this->email === config('app.playground_team_email');
    }

    public function promoter(): BelongsTo
    {
        return $this->belongsTo(Promoter::class, 'affiliate_promoter_id');
    }

    public function affiliateExpired(): bool
    {
        return $this->affiliate_expiry_at && now()->isAfter($this->affiliate_expiry_at);
    }

    public function onPaymentSuccess(GeneralInvoice $invoice): void
    {
        Log::info('onPaymentSuccess'. $invoice->id);
        if (null === $this->owner->getMeta('fluentcrm_payment_hook')){
            Log::info('Invoice paid fluent CRM event fired '. $invoice->id);
            SubscribeUserToFluentCRM::dispatch($this->owner, [TagTypes::PaidUser->value]);
        }
    }

    public function teamLatestUnsuccessfulInvoiceStatus(): BillingStatus | null
    {
        return team()?->generalInvoices?->whereNotIn('status', BillingStatus::noIssueWithInvoices())?->first()?->status;
    }

    public function hasSplitPaymentPlan(): bool
    {
        return $this->activePlan?->name === PlansEnum::SplitPayment;
    }

    public function webhooks(): HasMany
    {
        return $this->hasMany(Webhook::class);
    }

    public function integrations() : HasMany
    {
        return $this->hasMany(NotificationIntegration::class);
    }

    public function integration(NotificationIntegrationTypes $type) : ?NotificationIntegration
    {
        return $this->integrations->where('type', $type)->first();
    }

    public function bluePrints(): HasMany
    {
        return $this->hasMany(BluePrint::class);
    }

    public function scopeSearchFilter($query, $search)
    {
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%')
                    ->orWhere('billing_status', 'like', '%' . $search . '%');
            });
        }

        return $query;
    }

    function url()
    {
        $args = func_get_args();
        $url = url(...$args);

        if ($this->whiteLabel){
            // Parse the current URL to get the scheme and path
            $parsedUrl = parse_url($url);
            // Replace the host with the white label domain
            $url = Str::replaceFirst($parsedUrl['host'], $this->whiteLabel->active_domain, $url);
        }

        return $url;
    }

    /**
     * @param $className
     * @return MailMessage
     */
    function mailMessage($className = null)
    {
        if (is_null($className)) {
            $className = MailMessage::class;
        }

        if ($this->whitelabel && blank($this->notification_language)) {
            App::setLocale($this->whiteLabel?->getBranding('language') ?? 'en');
        } else {
            App::setLocale($this->notification_language ?? 'en');
        }

        // Use a generic email from like `NoReply`, to see if that solves the spam issue
        // config(['mail.mailers.white-label-smtp.from.name' => $this->get_brand_name]);

        config(['mail.mailers.white-label-custom-smtp.from.name' => $this->get_brand_name]);

        $driver = $this->whitelabel ? 'white-label-smtp' : 'smtp';

        if ($this->whitelabel && $this->whiteLabel->hasCustomSmtp()) {
            $driver = 'white-label-custom-smtp';
            config(['mail.mailers.white-label-custom-smtp.transport' => 'smtp']);
            config(['mail.mailers.white-label-custom-smtp.host' => Arr::get($this->whiteLabel->settings, 'smtp.host')]);
            config(['mail.mailers.white-label-custom-smtp.port' => Arr::get($this->whiteLabel->settings, 'smtp.port')]);
            config(['mail.mailers.white-label-custom-smtp.username' => Arr::get($this->whiteLabel->settings, 'smtp.username')]);
            config(['mail.mailers.white-label-custom-smtp.password' => $this->whiteLabel->smtp_password]);
            config(['mail.mailers.white-label-custom-smtp.encryption' => Arr::get($this->whiteLabel->settings, 'smtp.encryption')]);
            config(['mail.mailers.white-label-custom-smtp.from.address' => Arr::get($this->whiteLabel->settings, 'smtp.from_email')]);
        }

        return tap((new $className), fn($m) => $m->viewData['notifiable'] = $this)->mailer($driver);
    }

    public function storageProviders()
    {
        return $this->hasMany(StorageProvider::class);
    }

    public function getSettings($key, $default = null): mixed
    {
        if (str_contains($key, '->')) {
            $key = str_replace('->', '.', $key);
        }
        return Arr::get($this->settings, $key, $default) ?? $default;
    }

    public function saveSettings($key, $value)
    {
        return $this->update([
            'settings->'.$key => $value
        ]);
    }

    function canDisableFileManager($enableTime):bool
    {
        $fileManagerInterval = $this->getSettings('feature_customization->file_manager_interval');
        $enableTime = Carbon::parse($enableTime);
        return match ($fileManagerInterval) {
            'six_hour' => $enableTime->addHours(6) < now(),
            'twelve_hour' => $enableTime->addHours(12) < now(),
            'one_day' => $enableTime->addDay() < now(),
            'one_week' => $enableTime->addWeek() < now(),
            'one_month' => $enableTime->addMonth() < now(),
            default => false,
        };
    }


    function canDisableAdminer($enableTime): bool
    {
        $adminerInterval = $this->getSettings('feature_customization->adminer_interval');
        $enableTime = Carbon::parse($enableTime);
        return match ($adminerInterval) {
            'six_hour' => $enableTime->addHours(6) < now(),
            'twelve_hour' => $enableTime->addHours(12) < now(),
            'one_day' => $enableTime->addDay() < now(),
            'one_week' => $enableTime->addWeek() < now(),
            'one_month' => $enableTime->addMonth() < now(),
            default => false,
        };
    }

    function disableTime($enableTime, $intervalType)
    {
        $interval = $this->getSettings('feature_customization->'.$intervalType);
        $enableTime = Carbon::parse($enableTime);

        return match ($interval) {
            'six_hour' => $enableTime->addHours(6),
            'twelve_hour' => $enableTime->addHours(12),
            'one_day' => $enableTime->addDay(),
            'one_week' => $enableTime->addWeek(),
            'one_month' => $enableTime->addMonth(),
            default => false,
        };
    }

    public function allowSupportOneAction(User $user): bool
    {
        return $user->belongsToTeam(team: $this,allowSupport: !$user->isSupportLevel1());
    }

    public function mailboxes(): HasMany
    {
        return $this->hasMany(MailboxDomain::class);
    }

    public function addonStorageProviders(): HasMany
    {
        return $this->hasMany(AddonStorageProvider::class);
    }

    public function emailAccounts(): HasMany
    {
        return $this->hasMany(EmailAccount::class);
    }

    public function backblazeMembers()
    {
        return $this->hasMany(BackblazeMember::class);
    }
}
