<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\URL;
use <PERSON><PERSON>\Jetstream\Jetstream;
use <PERSON><PERSON>\Jetstream\TeamInvitation as JetstreamTeamInvitation;

class TeamInvitation extends JetstreamTeamInvitation
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'email',
        'role',
        'permissions',
    ];

    protected $casts = [
        'permissions' => 'array',
    ];
    protected $appends = ['signed_url'];

    public function getSignedUrlAttribute()
    {
        return URL::signedRoute('team-invitations.accept', ['invitation' => $this]);
    }

    public function getPermission($key, $default = null)
    {
        return Arr::get($this->permissions, $key, $default);
    }

    public function isAllServers(): bool
    {
        return $this->getPermission('server_access') == 'all';
    }

    public function isAllSites(): bool
    {
        return $this->getPermission('site_access') == 'all';
    }

    public function getServersAttribute()
    {
        return $this->getPermission('server_access') == 'choose' ? $this->getPermission('selected_servers', []) : [];
    }

    public function getSitesAttribute(): array
    {
        return $this->getPermission('site_access') == 'choose' ? $this->getPermission('selected_sites', []) : [];
    }

    public function savePermission($key, $value)
    {
        $permissions = $this->permissions;
        Arr::set($permissions, $key, $value);
        $this->permissions = $permissions;
    }

    public function team()
    {
        return $this->belongsTo(Jetstream::teamModel());
    }
}
