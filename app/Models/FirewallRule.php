<?php

namespace App\Models;

use App\Enums\FirewallProtocolEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FirewallRule extends Model
{
    use HasFactory;

    protected $guarded = [
        'id'
    ];

    protected $casts = [
        'protocol' => FirewallProtocolEnum::class,
    ];

    public function setAsActive(): void
    {
        $this->update(['is_active' => true]);
    }

    public function setAsInactive(): void
    {
        $this->update(['is_active' => false]);
    }
    public function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }
}
