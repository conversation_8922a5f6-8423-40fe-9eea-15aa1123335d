<?php

namespace App\Models;

use App\Traits\XcSoftDelete;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Artisan;

class Campaign extends Model
{
    use HasFactory, XcSoftDelete;

    protected $guarded = [
        'id',
    ];

    protected $casts = [
        'applicable_only' => 'array',
        'applicable_item_restricted_for' => 'array',
        'meta' => 'array'
    ];

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            Artisan::queue('fetch:promoters');
        });

        static::updated(function ($model) {
            Artisan::queue('fetch:promoters');
        });
    }

    public function promoters() : BelongsToMany
    {
        return $this->belongsToMany(Promoter::class, 'campaign_promoters', 'campaign_id', 'promoter_id');
    }
}
