<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BackblazeReport extends Model
{
    /**
     * Get the storage provider associated with this report.
     */
    public function storageProvider()
    {
        return $this->belongsTo(AddonStorageProvider::class);
    }

    /**
     * Get the total cost for this report entry.
     *
     * Note: This is a simplified calculation and should be adjusted based on
     * your actual pricing structure with Backblaze.
     */
    public function calculateCost()
    {
        // Example pricing (adjust based on your actual Backblaze pricing)
        $storageCostPerGb = 0.005; // $0.005 per GB per month
        $downloadCostPerGb = 0.01; // $0.01 per GB
        $classACostPer1000 = 0.004; // $0.004 per 1000 Class A transactions
        $classBCostPer1000 = 0.0004; // $0.0004 per 1000 Class B transactions

        // Calculate storage cost (convert byte-hours to GB-months)
        $gbMonths = $this->storage_byte_hours / (1024 * 1024 * 1024 * 24 * 30);
        $storageCost = $gbMonths * $storageCostPerGb;

        // Calculate download cost
        $downloadCost = $this->downloaded_gb * $downloadCostPerGb;

        // Calculate transaction costs
        $classACost = ($this->api_txn_class_a / 1000) * $classACostPer1000;
        $classBCost = ($this->api_txn_class_b / 1000) * $classBCostPer1000;

        // Total cost
        return $storageCost + $downloadCost + $classACost + $classBCost;
    }
}
