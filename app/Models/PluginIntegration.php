<?php

namespace App\Models;

use App\Enums\Integration\PluginIntegrationType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class PluginIntegration extends Model
{
    protected $casts = [
        'type' => PluginIntegrationType::class,
        'config' => 'encrypted',
    ];

    public function getObjectCacheProLicense()
    {
        return Arr::get(json_decode($this->config,true), 'license');
    }

}
