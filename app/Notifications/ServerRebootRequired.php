<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\Server;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class ServerRebootRequired extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Server $server;
    protected array $mails = [];

    public string $emailFilter = 'server_email';
    public string $slackFilter = 'server_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Server $server, array $mails = [])
    {
        $this->server = $server;
        $this->mails = $mails;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        //mail with not subcopy
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.reboot_required.subject'))
            ->cc($this->mails)
            ->greeting(Lang::get('emails.greeting', ['name' => $notifiable->name]))
            ->lineIf(!$this->server->getServerInfo('security_settings->automatic_reboot' ,false), Lang::get('emails.reboot_required.line', ['name' => $this->server->name]))
            ->lineIf($this->server->getServerInfo('security_settings->automatic_reboot' ,false), Lang::get('emails.reboot_required.auto_reboot_line', ['name' => $this->server->name, 'scheduled_time' => $this->server->getServerInfo('security_settings->reboot_time' ,false),'time_zone' => $this->server->time_zone]))
            ->action(Lang::get('emails.reboot_required.action'), $notifiable->url(route('server.security-update', $this->server)));
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage|null
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.reboot_required.line', ['name' => $this->server->name]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.reboot_required.subject'))
                        ->action(Lang::get('slack.reboot_required.action'), route('server.show', $this->server));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.reboot_required.line', ['name' => $this->server->name]))
                ->url("server/{$this->server->id}/security-update");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.reboot_required.line', ['name' => $this->server->name]))
                ->button(Lang::get('telegram.reboot_required.action'))
                ->url(route('server.security-update', ['server' => $this->server->id]));
        }
    }

    public function toAlert($notifiable): array
    {
        $message = Lang::get('emails.reboot_required.line', ['name' => $this->server->name]);
        if ($this->server->getServerInfo('security_settings->automatic_reboot' ,false)){
            $message = Lang::get('emails.reboot_required.auto_reboot_line', ['name' => $this->server->name, 'scheduled_time' => $this->server->getServerInfo('security_settings->reboot_time' ,false),'time_zone' => $this->server->time_zone]);
        }
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::INFO,
            'type' => 'ServerRebootRequired',
            'related_id' => $this->server->id,
            'related_type' => Server::class,
            'meta' => [
                    'message' => $message,
                    'url' => route('server.security-update', $this->server->id),
                ]
            ];
       }
}
