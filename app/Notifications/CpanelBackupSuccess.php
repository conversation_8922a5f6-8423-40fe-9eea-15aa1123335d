<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\ServerMigration;
use App\Services\Mail\CustomizeMailer;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class CpanelBackupSuccess extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected ServerMigration $serverMigration;

    public string $emailFilter = 'site_email';
    public string $slackFilter = 'site_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(ServerMigration $serverMigration)
    {
        $this->serverMigration = $serverMigration;
    }


    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return CustomizeMailer
     */
    public function toMail($notifiable)
    {
        return $notifiable->mailMessage(\App\Services\Mail\CustomizeMailer::class)
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.cpanel_backup_success.subject'))
            ->greeting(Lang::get('emails.cpanel_backup_success.greeting'))
            ->line(Lang::get('emails.cpanel_backup_success.greeting'))
            ->line(Lang::get('emails.cpanel_backup_success.line'))
            ->line(Lang::get('emails.cpanel_backup_success.details', ['brand_name' => $notifiable->getBrandName]))
            ->action(Lang::get('emails.cpanel_backup_success.action'), $notifiable->url(route($this->serverMigration->getStepRouteNames()
            [ServerMigration::SITES], [
                'server' => $this->serverMigration->getOriginal('server_id'),
                'serverMigration' => $this->serverMigration->id,
            ])))
            ->salutation(Lang::get('emails.cpanel_backup_success.salutation'));
    }

    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::SUCCESS,
            'type' => 'CpanelBackupSuccess',
            'related_id' => $this->serverMigration->id,
            'related_type' => ServerMigration::class,
            'meta' => [
                'message' => Lang::get('emails.cpanel_backup_success.notification',
                    ['name' => $this->serverMigration->name]),
                'url' => route($this->serverMigration->getStepRouteNames()[ServerMigration::SITES], [
                    'server' => $this->serverMigration->getOriginal('server_id'),
                    'serverMigration' => $this->serverMigration->id,
                ]),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage|null
     */
    public function toSlack($notifiable): ?SlackMessage
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.cpanel_backup_success.line', ['name' => $this->serverMigration->name]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.cpanel_backup_success.subject'))
                        ->action(Lang::get('slack.cpanel_backup_success.action'),
                            route($this->serverMigration->getStepRouteNames()[ServerMigration::SITES], [
                                'server' => $this->serverMigration->getOriginal('server_id'),
                                'serverMigration' => $this->serverMigration->id,
                            ]));
                });
        }
        return null;
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.cpanel_backup_success.line'))
                ->url("server/{$this->serverMigration->getOriginal('server_id')}/site/{$this->serverMigration->id}/serverMigrate/sites");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.cpanel_backup_success.line'))
                ->button(Lang::get('telegram.cpanel_backup_success.action'))
                ->url(route($this->serverMigration->getStepRouteNames()[ServerMigration::SITES], [
                    'server' => $this->serverMigration->getOriginal('server_id'),
                    'serverMigration' => $this->serverMigration->id,
                ]));
        }
    }
}
