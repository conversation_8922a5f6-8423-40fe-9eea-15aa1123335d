<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\GeneralInvoice;
use App\Services\Mail\InvoiceMailer;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class SendRefundInvoiceNotification extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    public string $emailFilter = 'other_email';
    public string $slackFilter = 'other_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public GeneralInvoice $invoice)
    {
        //
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param    $notifiable
     * @return InvoiceMailer
     * @throws \Exception
     */
    public function toMail($notifiable): InvoiceMailer
    {
        $teamName = '<strong>' . e($this->invoice->team->name) . '</strong>';

        return $notifiable->mailMessage(\App\Services\Mail\InvoiceMailer::class)
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.invoice_refund.subject', [
                'app_name' => $notifiable->getBrandName,
                'team' =>$this->invoice?->team?->name,
            ]))
            ->greeting(Lang::get('emails.greeting', ['name' => $this->invoice->team->owner->name]))
            ->line(Lang::get('emails.invoice_refund.line', [
                'team' => $teamName, // Bold team name
                'purchase_title' => $this->invoice->getPurchaseTitle(),
            ]))
            ->line(Lang::get('emails.invoice_refund.line_2'))
            ->table([
                [
                    'Invoice No',
                    Lang::get('emails.invoice_refund.invoice_no', [
                        'invoice_number' => $this->invoice?->number
                    ]),
                ],
                [
                    'Date',
                    Lang::get('emails.invoice_refund.invoice_date', [
                        'invoice_date' => $this->invoice?->created_at?->format('d/m/Y'),
                    ]),
                ],
                [
                    'Refunded Amount',
                    Lang::get('emails.invoice_refund.refund_amount', [
                        'refund_amount' => $this?->invoice?->refunded_amount,
                    ]),
                ],
            ])
            ->action(Lang::get('emails.invoice_refund.action'), $notifiable->url(route('user.bills-payment', [
                'switchToTeam' => $this->invoice->team->id,
            ])))
            ->salutationInvoice($notifiable)
            ->attachData($this->invoice->renderInvoicePdf(), $this->invoice->number . '.pdf', [
                'mime' => 'application/pdf',
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param   $notifiable
     * @return array
     */
    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::SUCCESS,
            'type' => 'InvoiceSent',
            'related_id' => $this->invoice->id,
            'related_type' => GeneralInvoice::class,
            'meta' => [
                'message' => Lang::get('emails.invoice_refund.subject', [
                    'app_name' => $notifiable->getBrandName,
                    'team' => $this->invoice->team->name,
                ]),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.invoice_refund.line', [
                    'team' => $this->invoice?->team?->name,
                    'purchase_title' => $this->invoice->getPurchaseTitle(),
                ]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.invoice_refund.subject', [
                        'app_name' => config('app.name'),
                        'team' =>$this->invoice?->team?->name,
                    ]))
                    ->action(Lang::get('slack.monthly_manual_invoice.action'), route('user.bills-payment', [
                        'switchToTeam' => $this->invoice?->team?->id,
                    ]));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.invoice_refund.line',
                        [
                            'app_name' => config('app.name'),
                            'team_name' =>$this->invoice?->team?->name,
                            'team' => $this->invoice?->team?->name,
                            'purchase_title' => $this->invoice->getPurchaseTitle(),
                        ])
                )
                ->url("user/bills-payment");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.invoice_refund.line',
                    [
                        'app_name' => config('app.name'),
                        'team_name' =>$this->invoice?->team?->name,
                        'team' => $this->invoice?->team?->name,
                        'purchase_title' => $this->invoice->getPurchaseTitle(),
                    ]))
                ->button(Lang::get('telegram.invoice_refund.action'))
                ->url(route('user.bills-payment', [
                    'switchToTeam' => $this->invoice?->team?->id
                ]));
        }
    }
}
