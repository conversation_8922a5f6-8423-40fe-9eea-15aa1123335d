<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\Site;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class SiteProvisionFailed extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Site $site;
    protected array $mails = [];

    public string $emailFilter = 'site_email';
    public string $slackFilter = 'site_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Site $site, array $mails = [])
    {
        $this->site = $site;
        $this->mails = $mails;
    }


    /**
     * Get the mail representation of the notification.
     *
     * @param $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.site_provisioning_failed.subject'))
            ->cc($this->mails)
            ->greeting(Lang::get('emails.site_provisioning_failed.greeting'))
            ->line(Lang::get('emails.greeting', ['name' => $notifiable->owner->name]))
            ->line(Lang::get('emails.site_provisioning_failed.line', ['name' => $this->site->name]))
            ->line(Lang::get('emails.line', ['brand_name' => $notifiable->getBrandName]))
            ->line(Lang::get('emails.site_provisioning_failed.salutation'))
            ->action(Lang::get('emails.action', ['brand_name' => $notifiable->getBrandName]), $notifiable->url(url('/login')));
    }

    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::ERROR,
            'type' => 'SiteProvisionFailed',
            'related_id' => $this->site->id,
            'related_type' => Site::class,
            'meta' => [
                'message' => Lang::get('emails.site_provisioning_failed.greeting'),
                'url' => route('site.show', ['server'=>$this->site->server_id,'site'=>$this->site->id]),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.site_provisioning_failed.line', ['name' => $this->site->name]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.site_provisioning_failed.subject'));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.site_provisioning_failed.line', ['name' => $this->site->name]))
                ->url("server/{$this->site->server_id}/site/{$this->site->id}");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.site_provisioning_failed.line', ['name' => $this->site->name]))
                ->button(Lang::get('telegram.site_provisioning_failed.action'))
                ->url(route('site.show', ['server'=>$this->site->server_id,'site'=>$this->site->id]));
        }
    }
}
