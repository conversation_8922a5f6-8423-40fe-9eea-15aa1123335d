<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Channels\TelegramChannel;
use App\Channels\WhatsAppChannel;
use App\Services\Mail\CustomizeMailer;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class BillingSuccess extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;
    private string $amount;
    private string $date;

    public string $emailFilter = 'other_email';
    public string $slackFilter = 'other_slack';


    public function __construct($amount,$date)
    {
        $this->amount = $amount;
        $this->date = $date;
        $this->defaultChannels = ['mail', 'slack', WhatsAppChannel::class, TelegramChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  $notifiable
     * @return CustomizeMailer
     */
    public function toMail($notifiable): CustomizeMailer
    {
        //$otherEmails = optional($notifiable)->getOtherEmails() ?: [];
        $billingEmails = array_map('trim', explode(',', $notifiable->billing_emails));
        $billingEmails = array_filter($billingEmails, function($email) {
            return !empty($email);
        });
        //$allEmails = array_merge($otherEmails, $billingEmails);

        $mail = $notifiable->mailMessage(\App\Services\Mail\CustomizeMailer::class)
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.billing_success.subject'))
            //->cc(optional($notifiable)->getOtherEmails())
            ->greeting(Lang::get('emails.billing_success.greeting', ['name' => $notifiable->owner->name]))
            ->line(Lang::get('emails.billing_success.line_1', ['brand_name' => $notifiable->getBrandName]))
            ->table([
                [
                    Lang::get('emails.billing_success.table_column_1',[
                        'name' => $notifiable->owner->name,
                        'phone' => $notifiable->owner->contact_number,
                        'amount' => $this->amount,
                        'date' => $this->date
                    ]),
                    Lang::get('emails.billing_success.table_column_2')
                ]
            ]);

        if ($billingEmails) {
            $mail->cc($billingEmails);
        }

        return $mail;
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.billing_success.line'))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.billing_success.subject'));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.billing_success.line'))
                ->url("user/bills-payment");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.billing_success.line'))
                ->button(Lang::get('telegram.billing_success.action'))
                ->url(route('user.bills-payment'));
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
