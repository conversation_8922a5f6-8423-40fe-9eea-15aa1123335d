<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\Server;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class ServerMemoryUsedHighNotification extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Server $server;
    protected array $mails = [];
    protected array $memory = [];

    public string $emailFilter = 'server_email';
    public string $slackFilter = 'server_slack';


    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Server $server, array $memory, array $mails = [])
    {
        $this->server = $server;
        $this->memory = $memory;
        $this->mails = $mails;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        //mail with not subcopy
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.resource_used_high.subject'))
            ->cc($this->mails)
            ->greeting(Lang::get('emails.resource_used_high.subject'))
            ->line(Lang::get('emails.resource_used_high.line', [
                'serverName' => $this->server->name,
                'serverIP' => $this->server->ipAddress(),
                'intervalInHour' => Server::WARNING_MAIL_INTERVAL_IN_HOUR,
            ]))
            ->line('High resource usage:')
            ->line('Memory usage: ' . round($this->memory['percent'],2) . '( threshold: ' . Server::MEMORY_USAGE_THRESHOLD . '%) of ' . $this->memory['total'] )
            ->line(join('<br>',['Regards,', $notifiable->getBrandName.' team']))
            ->salutation(Lang::get('emails.salutation', ['brand_name' => $notifiable->getBrandName]));

    }

    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::INFO,
            'type' => 'ServerProvisioned',
            'related_id' => $this->server->id,
            'related_type' => Server::class,
            'meta' => [
                'message' => strip_tags(Lang::get('emails.resource_used_high.line', [
                    'serverName' => $this->server->name,
                    'serverIP' => $this->server->ipAddress(),
                    'intervalInHour' => Server::WARNING_MAIL_INTERVAL_IN_HOUR,
                ])),
                'url' => route('server.show', $this->server->id),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.resource_used_high.line', [
                    'serverName' => $this->server->name,
                    'serverIP' => $this->server->ipAddress(),
                    'intervalInHour' => Server::WARNING_MAIL_INTERVAL_IN_HOUR,
                ]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.resource_used_high.subject'));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.resource_used_high.line', [
                        'serverName' => $this->server->name,
                        'serverIP' => $this->server->ipAddress(),
                        'intervalInHour' => Server::WARNING_MAIL_INTERVAL_IN_HOUR,
                    ])
                )
                ->url("server/{$this->server->id}/sites");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.resource_used_high.line', [
                    'serverName' => $this->server->name,
                    'serverIP' => $this->server->ipAddress(),
                    'intervalInHour' => Server::WARNING_MAIL_INTERVAL_IN_HOUR,
                ]))
                ->button(Lang::get('telegram.resource_used_high.action'))
                ->url(route('server.sites', ['server' => $this->server->id]));
        }
    }
}
