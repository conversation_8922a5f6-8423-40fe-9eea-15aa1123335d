<?php

namespace App\Notifications;

use App\Services\Mail\CustomizeMailer;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class SendWelcomeEmail extends VerifyEmail implements ShouldQueue
{
    use Queueable;

    /**
     * Get to verify email notification mail message for the given URL.
     *
     * @param  string  $url
     * @return CustomizeMailer
     */
    protected function buildMailMessage($url):CustomizeMailer
    {
        return (new CustomizeMailer)
            ->subject(Lang::get('emails.user_welcome.subject'))
            ->greeting(Lang::get('emails.user_welcome.greeting'))
            ->lines([
                Lang::get('emails.user_welcome.welcome_to_xcloud'),
                Lang::get('emails.user_welcome.what_xcloud_can_do'),
                Lang::get('emails.user_welcome.automated_server_management'),
                Lang::get('emails.user_welcome.simplified_dashboard'),
                Lang::get('emails.user_welcome.centralized_site_management')
            ])
            ->image(asset('img/mail/simplified-dashboard.gif'))
            ->lines([
                Lang::get('emails.user_welcome.seamless_integrations'),
                Lang::get('emails.user_welcome.flexible_integration_support')
            ])
            ->image(asset('img/mail/seamless-integrations.png'))
            ->lines([
                Lang::get('emails.user_welcome.vultr_partnership'),
                Lang::get('emails.user_welcome.vultr_partnership_benefits')
            ])
            ->image(asset('img/mail/vultr-partnership.png'))
            ->lines([
                Lang::get('emails.user_welcome.free_migrate'),
                Lang::get('emails.user_welcome.free_seamless_migrations'),
                Lang::get('emails.user_welcome.team_collaboration'),
                Lang::get('emails.user_welcome.auto_team_create'),
                Lang::get('emails.user_welcome.dedicated_support')
            ])
            ->action(Lang::get('emails.user_welcome.action'), 'https://xcloud.host/')
            ->salutation(Lang::get('emails.user_welcome.salutation'));
    }
}
