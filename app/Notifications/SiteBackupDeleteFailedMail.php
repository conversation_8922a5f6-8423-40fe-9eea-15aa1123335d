<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\Site;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class SiteBackupDeleteFailedMail extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Site $site;
    protected array $mails = [];
    private string $error = '';

    public string $emailFilter = 'site_email';
    public string $slackFilter = 'site_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Site $site, array $mails = [], string $error = '')
    {
        $this->site = $site;
        $this->mails = $mails;
        $this->error = $error;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->subject('Site Backup Failed')
            ->cc($this->mails)
            ->greeting(Lang::get('emails.site_backup_delete_failed.greeting', ['name' => $notifiable->name]))
            ->line(Lang::get('emails.greeting', ['name' => $notifiable->owner->name]))
            ->line(Lang::get('emails.site_backup_delete_failed.line', ['name' => $this->site->name]))
            ->line(Lang::get('emails.site_backup_delete_failed.error', ['error' => $this->error]))
            ->action('Backup Settings', $notifiable->url(route('site.backup', [$this->site->server->id, $this->site->id])));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param $notifiable
     * @return array
     */

    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::ERROR,
            'type' => 'SiteBackupFailed',
            'related_id' => $this->site->id,
            'related_type' => Site::class,
            'meta' => [
                'message' => Lang::get('emails.site_backup_delete_failed.line', ['name' => $this->site->name]) . PHP_EOL . Lang::get('emails.site_backup_delete_failed.error', ['error' => $this->error]),
                'url' => route('site.backup', [$this->site->server->id, $this->site->id]),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.site_backup_delete_failed.line', ['name' => $this->site->name]) . PHP_EOL . Lang::get('slack.site_backup_delete_failed.error', ['error' => $this->error]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.site_backup_delete_failed.subject'));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.site_backup_delete_failed.line', ['name' => $this->site->name, 'error' => $this->error]))
                ->url("server/{$this->site->server->id}/site/{$this->site->id}/backup");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.site_backup_delete_failed.line', ['name' => $this->site->name, 'error' => $this->error]))
                ->button(Lang::get('telegram.site_backup_delete_failed.action'))
                ->url(route('site.backup', [$this->site->server->id, $this->site->id]));
        }
    }
}
