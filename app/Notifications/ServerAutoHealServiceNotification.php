<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\Server;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class ServerAutoHealServiceNotification extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Server $server;
    protected array $mails = [];
    protected string $service;
    protected bool $is_success;

    public string $emailFilter = 'server_email';
    public string $slackFilter = 'server_slack';


    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Server $server,string $service, bool $is_success, array $mails = [])
    {
        $this->server = $server;
        $this->mails = $mails;
        $this->service = $service;
        $this->is_success = $is_success;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        //mail with not subcopy
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->subject(
                $this->service . ' ' . ($this->is_success ? 'Restarted' : 'Restart Failed'). ' on ' . $this->server->name
            )
            ->cc($this->mails)
            ->greeting($this->service . ' ' . ($this->is_success ? 'Restarted' : 'Failed'). ' on ' . $this->server->name)
            ->line(Lang::get('emails.greeting', ['name' => $notifiable->name]))
           ->line(
               ($this->is_success ? $this->service . ' Restarted' : $this->service . ' Restart Failed') . ' on ' . $this->server->name
           )
            ->salutation(Lang::get('emails.salutation', ['brand_name' => $notifiable->getBrandName]))
            ->action(Lang::get('emails.action', ['brand_name' => $notifiable->getBrandName]), $notifiable->url(url('/login')));

    }

    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::INFO,
            'type' => 'ServerAutoHealServiceNotification',
            'related_id' => $this->server->id,
            'related_type' => Server::class,
            'meta' => [
                'message' => $this->service . ' ' . ($this->is_success ? 'Restarted' : 'Restart Failed'). ' on ' . $this->server->name,
                'url' => route('server.show', $this->server->id),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content($this->service . ' ' . ($this->is_success ? 'Restarted' : 'Restart Failed'). ' on ' . $this->server->name)
                ->attachment(function ($attachment) {
                    $attachment->title('Server Auto Heal Service Notification')
                        ->action(Lang::get('slack.server_auto_heal.action'), url('/login'));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content($this->service . ' ' . ($this->is_success ? 'Restarted' : 'Restart Failed'). ' on ' . $this->server->name)
                ->url("server/{$this->server->id}/sites");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content($this->service . ' ' . ($this->is_success ? 'Restarted' : 'Restart Failed'). ' on ' . $this->server->name)
                ->button("Sign In To xCloud")
                ->url(route('login'));
        }
    }
}
