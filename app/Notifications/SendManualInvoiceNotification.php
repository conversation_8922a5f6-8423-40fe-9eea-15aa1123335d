<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Services\Mail\InvoiceMailer;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class SendManualInvoiceNotification extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    public string $emailFilter = 'other_email';
    public string $slackFilter = 'other_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public ManualInvoice $invoice)
    {
        //
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param    $notifiable
     * @return InvoiceMailer
     * @throws \Exception
     */
    public function toMail($notifiable): InvoiceMailer
    {
        $teamName = '<strong>' . e($this->invoice->team->name) . '</strong>';

        return $notifiable->mailMessage(\App\Services\Mail\InvoiceMailer::class)
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.manual_invoice.subject', [
                'app_name' => $notifiable->getBrandName,
                'team' => $this->invoice->team->name,
            ]))
            ->greeting(Lang::get('emails.greeting', ['name' => $this->invoice->team->owner->name]))
            ->line(Lang::get('emails.manual_invoice.line', [
                'team' => $teamName, // Bold team name
                'purchase_title' => $this->invoice->getPurchaseTitle(),
            ]))
            ->line(Lang::get('emails.manual_invoice.line_2'))
            ->table([
                [
                    'GeneralInvoice No',
                    Lang::get('emails.manual_invoice.invoice_no', [
                        'invoice_number' => $this->invoice?->number,
                    ]),
                ],
                [
                    'Date',
                    Lang::get('emails.manual_invoice.invoice_date', [
                        'invoice_date' => $this->invoice?->created_at?->format('d/m/Y'),
                    ]),
                ],
                [
                    'Paid Amount',
                    Lang::get('emails.manual_invoice.paid_amount', [
                        'paid_amount' => $this?->invoice?->amount,
                    ]),
                ],
            ])
            ->action(Lang::get('emails.manual_invoice.action'), $notifiable->url(route('user.bills-payment', [
                'switchToTeam' => $this->invoice->team->id,
            ])))
            ->salutationInvoice($notifiable)
            ->attachData($this->invoice->renderInvoicePdf(), $notifiable->getBrandName . '_invoice_' . $this->invoice->reference_no . '.pdf', [
                'mime' => 'application/pdf',
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param   $notifiable
     * @return array
     */
    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::SUCCESS,
            'type' => 'InvoiceSent',
            'related_id' => $this->invoice->id,
            'related_type' => GeneralInvoice::class,
            'meta' => [
                'message' => Lang::get('emails.manual_invoice.subject', [
                    'app_name' => $notifiable->getBrandName,
                    'team' => $this->invoice->team->name,
                ]),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.manual_invoice.line', [
                    'team' => $this->invoice->team->name,
                    'purchase_title' => $this->invoice->getPurchaseTitle(),
                ]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.manual_invoice.subject', ['app_name' => config('app.name'), 'team' => $this->invoice->team->name]))
                        ->action(Lang::get('slack.manual_invoice.action'), route('user.bills-payment', [
                            'switchToTeam' => $this->invoice->team->id,
                        ]));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(
                    Lang::get('whatsapp.manual_invoice.line',
                        [
                            'app_name' => config('app.name'),
                            'team_name' => $this->invoice->team->name,
                            'team' => $this->invoice->team->name,
                            'purchase_title' => $this->invoice->getPurchaseTitle(),
                        ])
                )
                ->url("user/bills-payment");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.manual_invoice.line',
                    [
                        'app_name' => config('app.name'),
                        'team_name' => $this->invoice->team->name,
                        'team' => $this->invoice->team->name,
                        'purchase_title' => $this->invoice->getPurchaseTitle(),
                    ]))
                ->button(Lang::get('telegram.manual_invoice.action'))
                ->url(route('user.bills-payment', [
                    'switchToTeam' => $this->invoice->team->id
                ]));
        }
    }
}
