<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Enums\XcloudBilling\BillingStatus;
use App\Models\GeneralInvoice;
use App\Services\Mail\InvoiceMailer;
use App\Traits\VIAxCloudNotificationHelper;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class SendMonthlyInvoiceNotification extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    public string $emailFilter = 'other_email';
    public string $slackFilter = 'other_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public GeneralInvoice $invoice)
    {
        //
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param    $notifiable
     * @return MailMessage
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        return $notifiable->mailMessage(\App\Services\Mail\InvoiceMailer::class)
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.subject',[
                'app_name' => $notifiable->getBrandName,
                'current_month' => Carbon::now()->format('F'),
                'team' => $this->invoice?->team?->name
            ]))
            ->line(Lang::get('emails.greeting', ['name' => $notifiable->owner?->name]))
            ->line(Lang::get('emails.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.line', [
                'app_name' => config('app.name'),
                'current_month' => Carbon::now()->format('F'),
                'team' => $this->invoice?->team?->name
            ]))
            ->line(Lang::get('emails.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.line_2', [
                'current_month' => Carbon::now()->format('F'),
                'team' => $this->invoice?->team?->name,
            ]))
            ->line(Lang::get('emails.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.invoice_no', [
                'invoice_number' => $this->invoice->number,
            ]))
            ->line(Lang::get('emails.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.invoice_date', [
                'issue_date' => $this->invoice->created_at->format('d/m/Y'),
            ]))
            ->lineIf(!$this->invoice->isPaid(), Lang::get('emails.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.due_date', [
                'due_date' => optional($this->invoice->due_date)->format('d/m/Y') ?? now()->endOfDay()->format('d/m/Y'),
            ]))
            ->attachData($this->invoice->renderInvoicePdf(), $notifiable->getBrandName . '_invoice_' . $this->invoice->reference_no . '.pdf', [
                'mime' => 'application/pdf',
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param   $notifiable
     * @return array
     */
    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::SUCCESS,
            'type' => 'InvoiceSent',
            'related_id' => $this->invoice->id,
            'related_type' => GeneralInvoice::class,
            'meta' => [
                'message' => Lang::get('emails.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.subject', [
                    'app_name' => $notifiable->getBrandName,
                    'current_month' => Carbon::now()->format('F'),
                    'team' => $this->invoice?->team?->name
                ]),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.line', [
                    'app_name' => config('app.name'),
                    'team' => $this->invoice?->team?->name,
                    'current_month' => Carbon::now()->format('F')
                ]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.subject', [
                        'app_name' => config('app.name'),
                        'current_month' => Carbon::now()->format('F'),
                        'team' => $this->invoice?->team?->name
                    ]))
                        ->action(Lang::get('slack.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.action'), route('user.bills-payment', [
                            'switchToTeam' => $this->invoice?->team?->id,
                        ]));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(
                    Lang::get('whatsapp.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.line',
                        [
                            'app_name' => config('app.name'),
                            'team' => $this->invoice?->team?->name,
                            'current_month' => Carbon::now()->format('F')
                        ])
                )
                ->url("user/bills-payment");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.line',
                    [
                        'app_name' => config('app.name'),
                        'team' => $this->invoice?->team?->name,
                        'current_month' => Carbon::now()->format('F')
                    ]))
                ->button(Lang::get('telegram.'.($this->invoice->status->is(BillingStatus::Paid) ? 'monthly_invoice' : 'monthly_invoice_payment_failed').'.action'))
                ->url(route('user.bills-payment', [
                    'switchToTeam' => $this->invoice?->team?->id
                ]));
        }
    }
}
