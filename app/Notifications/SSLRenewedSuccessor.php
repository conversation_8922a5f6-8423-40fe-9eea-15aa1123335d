<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Channels\TelegramChannel;
use App\Channels\WhatsAppChannel;
use App\Models\Site;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class SSLRenewedSuccessor extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    public string $emailFilter = 'other_email';
    public string $slackFilter = 'other_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public Site $site, public array $mails = [])
    {
        $this->defaultChannels = ['mail', 'slack', WhatsAppChannel::class, TelegramChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.ssl_renewed_successor.subject'))
            ->cc($this->mails)
            ->greeting(Lang::get('emails.greeting', ['name' => $notifiable->owner->name]))
            ->line(Lang::get('emails.ssl_renewed_successor.line', ['name' => $this->site->name, 'brand_name' => $notifiable->getBrandName]))
            ->salutation(Lang::get('emails.ssl_renewed_successor.salutation'))
            ->action('Site SSL', $notifiable->url(route('site.ssl',['server'=>$this->site->server,'site'=>$this->site])));
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.ssl_renewed_successor.line', ['name' => $this->site->name]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.ssl_renewed_successor.subject'))
                        ->action('Site SSL',route('site.ssl',['server'=>$this->site->server,'site'=>$this->site]));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['other_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('slack.ssl_renewed_successor.line', ['name' => $this->site->name]))
                ->url("dashboard");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.ssl_renewed_successor.line', ['name' => $this->site->name]))
                ->button(Lang::get('telegram.ssl_renewed_successor.action'))
                ->url(route('dashboard'));
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
