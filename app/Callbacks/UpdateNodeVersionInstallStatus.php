<?php

namespace App\Callbacks;

use App\Models\Server;
use App\Models\Task;

class UpdateNodeVersionInstallStatus
{
    /**
     * @var int
     */
    private $serverId;

    /**
     * @var string
     */
    private $nodeVersion;

    /**
     * @param int $serverId
     * @param string $nodeVersion
     */
    public function __construct(int $serverId, string $nodeVersion)
    {
        $this->serverId = $serverId;
        $this->nodeVersion = $nodeVersion;
    }

    /**
     * @param Task $task
     */
    public function __invoke(Task $task)
    {
        $server = Server::find($this->serverId);

        if (! $server) {
            return;
        }

        $server->nodeVersions()->updateOrCreate([
            'node_version' => $this->nodeVersion
        ], [
            'status' => $task->successful() ? 'installed' : 'failed',
            'log' => $task->output,
            'node_version_updated_at' => now()->toDateTimeString(),
        ]);
    }
}
