<?php

namespace App\Callbacks;
use App\Events\SiteMonitoringUpdated;
use App\Models\Site;
use App\Models\Task;
use Illuminate\Support\Facades\Log;

class PluginUpdated
{
    public function __construct(private Site $site)
    {
    }

    function handle(Task $task): void
    {
        $wp_updates = $this->site->wp_updates;
        if ($task->output_json == null || !isset($task->output_json['plugin'])){
            Log::error('Plugin name not found in task output', [
                'task_id' => $task->id,
                'site_id' => $this->site->id,
            ]);
            return;
        }
        $plugin_name = $task->output_json['plugin'];
        if ($task->successful()) {
            $status = $task->output_json['update_status'];
            $plugins = array_map(function($plugin)use($status,$plugin_name){
                if($plugin['name'] == $plugin_name){
                    unset($plugin['update_action']);
                    if ($status=='success'){
                        $plugin['version'] = $plugin['update_version'];
                        $plugin['update_version'] = null;
                    }else{
                        $plugin['error_status'] = $status;
                    }
                }
                return $plugin;
            },$wp_updates['plugins']);
            $wp_updates['plugins'] = $plugins;
            $this->site->update(['wp_updates' => $wp_updates]);
        }else{
            $plugins = array_map(function($plugin)use($plugin_name){
                if($plugin['name'] == $plugin_name){
                    unset($plugin['update_action']);
                    $plugin['error_status'] = 'failed';
                }
                return $plugin;
            },$wp_updates['plugins']);
            $wp_updates['plugins'] = $plugins;
            $this->site->update(['wp_updates' => $wp_updates]);
        }
        $this->site->update(['wp_updates' => $wp_updates]);
        SiteMonitoringUpdated::dispatch($this->site);
    }
}
