<?php

namespace App\Callbacks;

use App\Jobs\Site\FetchRemoteDatabaseJob;
use App\Models\Server;
use App\Models\ServerMigration;
use App\Models\Site;
use App\Models\SiteMigration;
use App\Models\Task;
use App\Services\Migration\ServerMigrating;
use App\Traits\SiteMigrationDispatcher;
use Illuminate\Support\Str;

class SiteFetchingCompleted
{
    use SiteMigrationDispatcher;

    private Site $site;
    private SiteMigration $siteMigration;
    private int $serverMigrationId;
    private Server $server;

    public function __construct(int $serverMigrationId, int $siteid)
    {
        $this->site = Site::find($siteid);
        $this->siteMigration = $this->site->siteMigration;
        $this->serverMigrationId = $serverMigrationId;
        $this->server = $this->site->server;
    }

    function handle(Task $task): void
    {
        if ($task->successful()) {
            //fetching is completed, now we can start the migration
            $jobs = [
                ServerMigrating::FETCHING_DATABASE => new FetchRemoteDatabaseJob(ServerMigration::find($this->serverMigrationId), $this->site),
            ];

            $this->dispatchSiteMigrationChainJob($jobs);
            if ($this->site->checkRedisObjectCacheInstalled()){
                //check if redis is installed and running
                $this->site->update(['meta->has_redis_object_caching' => true]);
            }

        } else if (!$this->siteMigration->isCanceled()) {
            //if migration is not canceled mark it as failed
            $this->siteMigration->markAsMigrationFailure('Site fetching failed');
        }


    }
}
