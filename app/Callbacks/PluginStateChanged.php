<?php

namespace App\Callbacks;
use App\Events\SiteMonitoringUpdated;
use App\Models\Site;
use App\Models\Task;
class PluginStateChanged
{
    public function __construct(private Site $site)
    {
    }

    function handle(Task $task): void
    {
        $wp_updates = $this->site->wp_updates;
        if($task->successful() && $task->output_json){
            $plugins = $task->output_json['plugins'];
            array_walk($wp_updates['plugins'], function(&$plugin)use($plugins,&$plugin_list,$task){
                if(is_array($plugins) && in_array($plugin['name'],$plugins)){
                    unset($plugin['action']);
                    $plugin['status'] =$this->getState($task->successful(),$task->output_json['action']);
                }
            });
        }
        if (!is_array($wp_updates['plugins']) || count($wp_updates['plugins']) === 0) {
            $this->site->pullUpdates();
        }
        $this->site->update(['wp_updates' => $wp_updates]);
        SiteMonitoringUpdated::dispatch($this->site);
    }

    public function getState($isSuccessful,$action): string
    {
        if($isSuccessful){
            $state = ($action == 'activate') ? 'active' : 'inactive';
        }else{
            $state = ($action == 'activate') ? 'inactive' : 'active';
        }
        return $state;

    }
}
