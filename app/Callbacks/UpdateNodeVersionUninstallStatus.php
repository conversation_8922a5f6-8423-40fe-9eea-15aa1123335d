<?php

namespace App\Callbacks;

use App\Models\Server;
use App\Models\Task;

class UpdateNodeVersionUninstallStatus
{
    /**
     * @var int
     */
    private $serverId;

    /**
     * @var string
     */
    private $nodeVersion;

    /**
     * @param int $serverId
     * @param string $nodeVersion
     */
    public function __construct(int $serverId, string $nodeVersion)
    {
        $this->serverId = $serverId;
        $this->nodeVersion = $nodeVersion;
    }

    /**
     * @param Task $task
     */
    public function __invoke(Task $task)
    {
        $server = Server::find($this->serverId);

        if (! $server) {
            return;
        }

        if ($task->successful()) {
            $server->nodeManager()->remove($this->nodeVersion);
        } else {
            $server->nodeVersions()->updateOrCreate([
                'node_version' => $this->nodeVersion
            ], [
                'status' => 'failed',
                'log' => $task->output,
                'node_version_updated_at' => now()->toDateTimeString(),
            ]);
        }
    }
}
