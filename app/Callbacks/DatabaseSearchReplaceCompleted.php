<?php

namespace App\Callbacks;

use App\Jobs\CreateStagingEnvUsingCloudflare;
use App\Jobs\Site\EnableFullPageCaching;
use App\Jobs\Site\EnableRedisObjectCaching;
use App\Jobs\Site\EnableSsl;
use App\Jobs\Site\MarkAsServerMigratedJob;
use App\Models\Site;
use App\Models\SiteMigration;
use App\Models\Task;
use App\Services\Migration\ServerMigrating;
use App\Traits\SiteMigrationDispatcher;

class DatabaseSearchReplaceCompleted
{
    use SiteMigrationDispatcher;

    private Site $site;
    private SiteMigration $siteMigration;

    public function __construct(int $siteid)
    {
        $this->site = Site::find($siteid);
        $this->siteMigration = $this->site->siteMigration;
    }

    function handle(Task $task): void
    {
        if ($task->successful()) {
            //fetching is completed, now we can start the migration
            $jobs = [
                ServerMigrating::CONFIGURING_HTTPS => new EnableSsl($this->site, reloadNginx: false),
                ServerMigrating::CONFIGURING_FULL_PAGE_CACHE => new EnableFullPageCaching($this->site),
                ServerMigrating::CONFIGURING_REDIS_CACHE => new EnableRedisObjectCaching($this->site),
                ServerMigrating::SETUP_STAGING_DOMAIN => new CreateStagingEnvUsingCloudflare($this->site),
                ServerMigrating::VERIFYING_DNS => new MarkAsServerMigratedJob($this->site),
            ];

            $this->dispatchSiteMigrationChainJob($jobs);


        } else if (!$this->siteMigration->isCanceled()) {
            //if migration is not canceled mark it as failed
            $this->siteMigration->markAsMigrationFailure('Site fetching failed');
        }


    }
}
