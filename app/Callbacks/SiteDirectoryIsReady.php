<?php

namespace App\Callbacks;

use App\Jobs\Site\MakingDatabaseReady;
use App\Models\Site;
use App\Models\SiteMigration;
use App\Models\Task;
use App\Services\Migration\SiteMigrating;
use App\Traits\SiteMigrationDispatcher;

class SiteDirectoryIsReady
{
    use SiteMigrationDispatcher;

    private Site $site;
    private $siteMigration;

    public function __construct(int $siteid)
    {
        $this->site = Site::find($siteid);
        $this->siteMigration = $this->site->siteMigration;
    }

    function handle(Task $task): void
    {
        if ($task->successful()) {

            //fetching is completed, now we can start the migration
            $jobs = [
                SiteMigrating::MAKING_DATABASE_READY => new MakingDatabaseReady($this->siteMigration, $this->site),
            ];

            $this->dispatchSiteMigrationChainJob($jobs);


        } else if (!$this->siteMigration->isCanceled()) {
            //if migration is not canceled mark it as failed
            $this->siteMigration->markAsMigrationFailure('Site fetching failed');
        }


    }
}
