<?php

namespace App\Callbacks;

use App\Jobs\Site\ImportRemoteDatabaseJob;
use App\Models\ServerMigration;
use App\Models\Site;
use App\Models\SiteMigration;
use App\Models\Task;
use App\Services\Migration\ServerMigrating;
use App\Traits\SiteMigrationDispatcher;

class DatabaseFetchingCompleted
{
    use SiteMigrationDispatcher;

    private Site $site;
    private SiteMigration $siteMigration;
    private int $serverMigrationId;

    public function __construct(int $serverMigrationId, int $siteid)
    {
        $this->site = Site::find($siteid);
        $this->siteMigration = $this->site->siteMigration;
        $this->serverMigrationId = $serverMigrationId;
    }

    function handle(Task $task): void
    {
        if ($task->successful()) {
            //fetching is completed, now we can start the migration
            $jobs = [
                ServerMigrating::IMPORTING_DATABASE => new ImportRemoteDatabaseJob(ServerMigration::find($this->serverMigrationId), $this->site),
            ];

            $this->dispatchSiteMigrationChainJob($jobs);


        } else if (!$this->siteMigration->isCanceled()) {
            //if migration is not canceled mark it as failed
            $this->siteMigration->markAsMigrationFailure('Site fetching failed');
        }


    }
}
