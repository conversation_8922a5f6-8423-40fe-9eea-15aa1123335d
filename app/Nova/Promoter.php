<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\File;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\URL;

class Promoter extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Promoter>
     */
    public static $model = \App\Models\Promoter::class;
    public static $group = 'Affiliate Program';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'promoter_id', 'pref', 'email', 'name'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Promoter', function () {
                return "<a class='link-default' target='_blank' href='https://xcloud.firstpromoter.com/promoters/{$this->resource->promoter_id}'> {$this->resource->promoter_id} </a>";
            })->asHtml(),

            Text::make('name')->sortable()->nullable()->readonly(),
            Text::make('Profile ID')->copyable()->nullable()->readonly(),
            Text::make('Status')->nullable()->sortable()->nullable()->readonly(),
            Text::make('Customer', 'cust_id')->sortable()->nullable()->readonly(),
            Text::make('Email')->sortable(),
            Text::make('Temporary Password', 'temp_password')->hide()->copyable()->hideFromIndex()->readonly()->nullable(),
            Text::make('Default Promotion ID', 'default_promotion_id')->copyable()->hideFromIndex()->nullable(),
            Text::make('Preference', 'pref')->hideFromIndex()->copyable()->nullable(),
            Text::make('Default Reference ID', 'default_ref_id')->copyable()->hideFromIndex()->nullable(),
            Textarea::make('Note')->hideFromIndex()->nullable(),
            Text::make('W8 Form URL')->hideFromIndex()->copyable()->nullable()->readonly(),
            Text::make('W9 Form URL')->hideFromIndex()->copyable()->nullable()->readonly(),

            Text::make('Parent Promoter', function () {
                return "<a class='link-default' target='_blank' href='https://xcloud.firstpromoter.com/promoters/{$this->resource->parent_promoter_id}'> {$this->resource->parent_promoter_id} </a>";
            })->asHtml(),

            Currency::make('Earnings Balance')->nullable(),
            Currency::make('Current Balance')->nullable(),
            Currency::make('Paid Balance')->nullable(),
            Text::make('Auth Token')->hideFromIndex()->nullable(),
            Code::make('Profile')->hideFromIndex()->json()->onlyOnDetail()->readonly(),
            Code::make('Meta')->hideFromIndex()->json()->readonly()->onlyOnDetail(),
            HasMany::make('Promotions', 'promotions', Promoter::class),
            BelongsToMany::make('Campaigns', 'campaigns', Campaign::class),
            DateTime::make('Created At')->hideFromIndex()->sortable(),
            DateTime::make('Updated At')->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->isSupportLevel2();
    }
}
