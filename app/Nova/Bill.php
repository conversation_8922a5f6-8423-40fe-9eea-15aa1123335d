<?php

namespace App\Nova;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\BillType;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Nova\Actions\ForceDeleteBill;
use App\Nova\Lenses\NotGeneratedRecurringBills;
use App\Nova\Lenses\UnmatchProductBillWithxCloudManaged;
use App\Nova\Metrics\BillsHasOffer;
use App\Nova\Metrics\BillsPaidUsingPlan;
use App\Nova\Metrics\BillsRefundable;
use App\Nova\Metrics\BillsUnpaidByUser;
use App\Nova\Metrics\TotalPackageBills;
use App\Nova\Metrics\TotalPackageSales;
use App\Nova\Metrics\TotalProductBills;
use Illuminate\Http\Request;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphTo;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Bill extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Bill::class;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $with = [
        'team', 'invoice', 'manualInvoice', 'billingPlan', 'package', 'product'
    ];

    public static $group = 'Payment Gateway';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'title', 'team.email', 'team.name', 'invoice.invoice_number', 'manualInvoice.invoice_number'
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->orderBy('bill_from'); // Change 'asc' to 'desc' if you want descending order
    }

    public function filters(NovaRequest $request) : array
    {
        return [
            new Filters\SourceChargeIs___BillingAmount(),
            new Filters\SourceChargeIs___ApplicationFeeAmount(),
            new Filters\FilterBillsByPlan(),
            new Filters\BillStatusFilter(),
            new Filters\BillHasOfferFilter(),
            new Filters\BillPeriodFilter(),
            new Filters\BillType(),
            new Filters\BillsByMonth(),
            new Filters\BillFrom(),
            new Filters\BillTo(),
            new Filters\BillProductFilter(),
            new Filters\BillPackageFilter(),
        ];
    }

    public function cards(NovaRequest $request)
    {
        return [
            (new BillsHasOffer)->width('1/3'),
            (new BillsUnpaidByUser)->width('1/3'),
            (new BillsRefundable)->width('1/3'),
            (new BillsPaidUsingPlan)->width('1/3'),
            (new TotalProductBills)->width('1/3'),
            (new TotalPackageBills)->width('1/3')
        ];
    }


    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new Actions\ChangeOfferStatusForBills())
                ->canSee(function ($request) {
                    return $request->user()->isSuperAdmin();
                })
                ->canRun(function ($request, $model) {
                    return $request->user()->isSuperAdmin();
                })
                ->confirmText('Are you sure you want to update the status of selected invoices?'),
            (new Actions\ExportBillsToCSV())
                ->canSee(function ($request) {
                    return $request->user()->isSuperAdmin();
                }),
            (new Actions\ForceInactiveBill())
                ->canSee(function ($request) {
                    return $request->user()->canAccessBillsThroughNova();
                })
                ->canRun(function ($request, $model) {
                    return $request->user()->canAccessBillsThroughNova();
                })
                ->confirmText('Are you sure you want to deactivate the selected bills?'),
            (new Actions\ForceActiveBill())
                ->canSee(function ($request) {
                    return $request->user()->canAccessBillsThroughNova();
                })
                ->canRun(function ($request, $model) {
                    return $request->user()->canAccessBillsThroughNova();
                })
                ->confirmText('Are you sure you want to activate the selected bills?'),
            (new ForceDeleteBill())
                ->canSee(function ($request) {
                    return $request->user()->isSuperAdmin();
                })
                ->canRun(function ($request, $model) {
                    return $request->user()->isSuperAdmin();
                }),
            (new Actions\FixBilling())
                ->canSee(function ($request) {
                    return $request->user()->canAccessBillsThroughNova();
                })
                ->canRun(function ($request, $model) {
                    return $request->user()->canAccessBillsThroughNova();
                }),
        ];
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Unique Hash')->exceptOnForms()->onlyOnDetail()->sortable(),

            Text::make('Title')->nullable(),

            Text::make('Description')->onlyOnDetail()->nullable(),

            Date::make('Bill From')->hideFromIndex()->nullable(),

            Text::make('Bill From', function ($billFrom) {
                return $billFrom->bill_from->format('d/m/Y').' ('.$billFrom->bill_from->format('M').')';
            })->onlyOnIndex()->nullable(),

            Boolean::make('Is Active', 'service_is_active')->exceptOnForms()->sortable(),

            Text::make('How the bill calculated?', function () {
                return "<a class='link-default' target='_blank' href=".route('user.bill-calculator', [
                            'billing_amount' => $this->resource?->billing_amount,
                            'renewal_period' => $this->resource?->renewal_period,
                            'bill_from' => $this->resource->created_at?->format('Y-m-d H:i:s'),
                            'bill_to' => $this->resource->next_billing_date?->format('Y-m-d H:i:s'),
                        ])."> Go To Calculator </a>";
            })->onlyOnDetail()->asHtml(),

            Currency::make('Charge', 'source_bill_amount')->readonly()->sortable(),

//            Currency::make('Source Bill Amount', 'source_bill_amount')->sortable(),

            Currency::make('Bill', 'billing_amount')->sortable(),

            Currency::make('To Pay', 'amount_to_pay')->sortable(),

            Currency::make('Adjustable', 'adjustable_amount')->sortable(),

            Currency::make('Actual Application Fee')->step(0.000001)->nullable()->hideFromIndex(),

            Currency::make('App Fee', 'application_fee_to_charge')->step(0.000001)->nullable(),

            Currency::make('Adjustable Application Fee')->step(0.000001)->nullable()->hideFromIndex(),

            // Currency::make('Transparent Cost', 'transparent_cost')->exceptOnForms(),
            Currency::make('Transparent Cost', 'transparent_cost')->displayUsing(function(){
                return format_billing($this->resource->transparent_cost);
            })->onlyOnDetail(),

            Currency::make('Refundable', 'refundable_amount')->rules([
                function ($attribute, $value, $fail) use ($request) {
                    if ($value > $request->input('amount_to_pay')) {
                        $fail('The Refundable Amount cannot be greater than the Amount to Pay.');
                    }
                },
            ])->sortable(),

            Number::make('Usage Hours', 'get_usage_hours')->exceptOnForms(),

            Currency::make('Cost Per Hour')->step(0.000001)->nullable()->hideFromIndex()
                ->displayUsing(function ($value) {
                    return '$'.number_format($value, 6);  // Format the value with 6 decimal places
                })->readonly(function () {
                    $calculateData = optional($this->resource->meta['calculation_data'] ?? null);

                    $billingIsSame = floatval($calculateData['billingAmount']) === floatval($this->resource->billing_amount);

                    return !empty($calculateData['costPerHour']) && $billingIsSame;
                }),

            Currency::make('Additional Usage Charge')->nullable(),

            Boolean::make('Adjust with Previous')->onlyOnDetail(),

            Select::make('Status')->options([
                BillingStatus::Unpaid->value => BillingStatus::Unpaid->name,
                BillingStatus::Paid->value => BillingStatus::Paid->name,
                BillingStatus::Refundable->value => BillingStatus::Refundable->name,
                BillingStatus::Refunded->value => BillingStatus::Refunded->name
            ])->displayUsingLabels(),

            BelongsTo::make('Generated By User', 'generatedBy', 'App\Nova\User')->rules([
                'required'
            ])->showOnIndex(false)->readonly()->nullable(),

            BelongsTo::make('Updated By User', 'updatedBy', 'App\Nova\User')
                ->showOnIndex(false)
                ->readonly()
                ->nullable(),

            Date::make('Next Billing', 'next_billing_date')->hideFromIndex()->nullable(),

            Text::make('Next Billing', function ($billFrom) {
                if ($billFrom->next_billing_date) {
                    return $billFrom->next_billing_date->format('d/m/Y').' ('.$billFrom->next_billing_date->format('M').')';
                }
                return null;
            })->onlyOnIndex()->nullable(),

            BelongsTo::make('Team', 'team', 'App\Nova\Team')->rules([
                'required'
            ])->readonly(function (){
                return !auth()->user()?->canAccessBillsThroughNova();
            })->searchable()->nullable(),

            Text::make('Team ID', 'team_id')->default($this->resource->team_id)->onlyOnDetail()->readonly(),

            BelongsTo::make('Bill Forwarded To', 'billForwardedTo', self::class)->onlyOnDetail()->nullable(),

            Select::make('Currency')->options([
                BillingCurrency::USD->value => 'USD',
            ])->readonly()->showOnIndex(false)->displayUsingLabels(),

            MorphTo::make('Generator')->types([
                'App\Nova\Server' => 'Server',
                'App\Nova\EmailProvider' => 'Email Provider',
            ])->showOnIndex(false)->readonly(),

            Text::make('Generator ID', 'generator_id')->default($this->resource->generator_id)->readonly(function ($request) {
                return $request->isUpdateOrUpdateAttachedRequest();
            })->showOnCreating(),

            Text::make('Generator Type', 'generator_type')->default($this->resource->generator_type)->readonly(function ($request) {
                return $request->isUpdateOrUpdateAttachedRequest();
            })->showOnCreating(),

            Text::make('Call Trace', 'call_trace')->placeholder('No trace found')->showOnIndex(false)->readonly(),

            Code::make('Meta')->exceptOnForms()->onlyOnDetail()->json(),

            Select::make('Type')->options([
                BillType::Prepaid->value => 'Prepaid',
                BillType::Postpaid->value => 'Postpaid',
            ])->displayUsingLabels(),

            Select::make('Service', 'service')->rules([
                'required'
            ])->options(BillingServices::toSelectArray())
                ->readonly(function () use ($request) {
                    return $request->isUpdateOrUpdateAttachedRequest();

//                    $creationFields = str_contains($request->path(), 'nova-api/bills/creation-fields');
//                    $creationMode = $request->query->get('editMode') === 'create';
//
//                    $canCreate = $creationFields && $creationMode;
//
//                    return !$canCreate;
                })->hideFromIndex()
                ->displayUsingLabels(),

            Boolean::make('Has Offer')->sortable(),

            Select::make('Renew', 'renewal_period')
                ->readonly(function () {
                    return !auth()->user()->canAccessBillsThroughNova();
                })->options(BillRenewalPeriod::asValueLabel())
                ->displayUsingLabels(),

//            Select::make('Billing Plan', 'plan_id')->options(function () {
//                return \App\Models\BillingPlan::all()->pluck('name_as_title', 'id')->toArray();
//            })->onlyOnForms()->nullable(),
//
//            Select::make('Package', 'package_id')->options(
//                \App\Models\Package::all()->pluck('name', 'id')->toArray()
//            )->displayUsingLabels()->onlyOnForms()->nullable(),
//
//            Select::make('Product', 'product_id')->options(function () {
//                return \App\Models\Product::all()->map(function ($product) {
//                    $product->title = $product->title . ' - ' . $product->service_type->toReadableSentence() . ' - (' . $product->slug . ')';
//                    return $product;
//                })->pluck('title', 'id')->toArray();
//            })->onlyOnForms()->nullable(),

            HasMany::make('Previous Bills', 'previousBills', Bill::class),

            HasMany::make('Related Bills', 'relatedBills', Bill::class),

            BelongsTo::make('Billing Plan', 'billingPlan', BillingPlan::class)->sortable()->nullable(),

            BelongsTo::make('Package', 'package', Package::class)->sortable()->nullable(),

            BelongsTo::make('Product', 'product', Product::class)
                ->readonly(function () {
                    $team = $this->resource->team;

                    if (!$team) {
                        return false;
                    }

                    return (bool)$team->white_label_id;
                })->sortable()->nullable(),

            BelongsTo::make('White Label Product', 'whiteLabelProduct', WhiteLabelProduct::class)
                ->readonly(function () {

                    $team = $this->resource->team;

                    if (!$team) {
                        return false;
                    }

                    return !$team->white_label_id;
                })->onlyOnForms()->sortable()->nullable(),

            BelongsTo::make('General Invoice', 'invoice', GeneralInvoice::class)->showOnDetail(function () {
                return $this->resource?->invoice?->type === InvoiceTypesEnum::General;
            })->showOnUpdating(function () {
                return $this->resource?->invoice?->type === InvoiceTypesEnum::General;
            })->hideFromIndex()->readonly(function (){
                return !auth()->user()->canAccessBillsThroughNova();
            })->searchable()->nullable(),

            BelongsTo::make('Manual Invoice', 'manualInvoice', ManualInvoice::class)->showOnDetail(function () {
                return $this->resource?->invoice?->type === InvoiceTypesEnum::Manual;
            })->showOnUpdating(function () {
                return $this->resource?->invoice?->type === InvoiceTypesEnum::Manual;
            })->hideFromIndex()->readonly(function (){
                return !auth()->user()->isSuperAdmin();
            })->searchable()->nullable(),

            Date::make('Due On')->showOnIndex(false)->nullable(),

            DateTime::make('Paid On')->showOnIndex(false)->readonly(),

            DateTime::make('Deactivated On', 'service_deactivated_from')->showOnIndex(false)->readonly(),

            DateTime::make('Created At')->showOnIndex(),

            DateTime::make('Updated At')->showOnIndex(false)->readonly(),
        ];
    }

    public function lenses(NovaRequest $request)
    {
        return [
            new NotGeneratedRecurringBills,
            new UnmatchProductBillWithxCloudManaged
        ];
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()->canAccessBillsThroughNova();
    }

    public static function authorizedToCreate(Request $request): bool
    {
        $isCreationFields = str_contains($request->path(), 'nova-api/bills/creation-fields');

        $isCreatingRequest = $request->query->get('editMode') === 'create';

        return $request->user() && $request->user()->canAccessBillsThroughNova() && ($isCreationFields || $isCreatingRequest);
    }

    public function authorizedToForceDelete(Request $request): bool
    {
        return $request->user()->canAccessBillsThroughNova();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user() && $request->user()->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user() && $request->user()->isAdmin();
    }

    /*public static function authorizedToViewAny(Request $request): bool
    {
        return $request->user()?->isAdmin() || $request->user()?->hasAnySupportRole();
    }*/

    public function authorizedToRunAction(NovaRequest $request, Action $action): bool
    {
        return $request->user()?->isSuperAdmin();
    }
}
