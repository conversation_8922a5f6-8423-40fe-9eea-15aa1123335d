<?php

namespace App\Nova;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\Currency;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\MorphTo;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class UtmVisitor extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\UtmVisitor>
     */
    public static $model = \App\Models\UtmVisitor::class;
    public static $group = 'Affiliate Program';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'ip';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'ip', 'url', 'useragent', 'headers', 'request', 'session_id', 'created_at'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make('Utm'),
            BelongsTo::make('User'),
            Text::make(__('IP'), 'ip')->rules([
                'required', 'ip'
            ])->copyable(),

            Text::make(__('URL'), function(){
                return "<a href='{$this->url}' target='_blank'>".Str::limit($this->url,20)."</a>";
            })->asHtml()->onlyOnIndex(),

            Text::make(__('URL'), 'url')->rules([
                'required', 'url'
            ])->copyable()->onlyOnDetail(),

            Text::make(__('Useragent'), function(){
                return Str::limit($this->useragent,20);
            })->onlyOnIndex(),

            Text::make(__('Useragent'), 'useragent')->rules([
                'required', 'string'
            ])->onlyOnDetail(),

            Code::make(__('Headers'), 'headers')->rules([
                'required', 'string'
            ])->onlyOnDetail(),

            Text::make(__('Request'), function(){
                return Str::limit($this->request,20);
            })->onlyOnIndex(),
            Code::make(__('Request'), 'request')->rules([
                'required', 'string'
            ])->onlyOnDetail(),
            Text::make(__('Session ID'), 'session_id')->rules([
                'required', 'string'
            ])->onlyOnDetail(),

            Currency::make(__('Total Purchase Amount'), 'purchase_amount')->exceptOnForms()->sortable(),

            Text::make('Source Type', 'model_type')->onlyOnDetail()->sortable(),
            Text::make('Source ID', 'model_id')->onlyOnDetail()->sortable(),

            MorphTo::make('Source Sell', 'sourceSell')->exceptOnForms(),

            Text::make(__('Created At'), function (){
                return $this->created_at->format('Y-m-d H:i:s');
            })->rules([
                'required', 'date'
            ]),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user()?->isSuperAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->hasAnySupportRole();
    }
}
