<?php

namespace App\Nova;

use App\Policies\Nova\ServerMonitorPolicy;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class ServerMonitor extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ServerMonitor>
     */
    public static $model = \App\Models\ServerMonitor::class;

    public static $policy = ServerMonitorPolicy::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Server', 'server', Server::class),

            // Add other fields as needed
            Code::make('Cpu','cpu')->json(),
            Code::make('Memory','memory')->json(),
            Code::make('Disks','disk')->json(),
            Code::make('Services','services')->json(),
            Code::make('Site States','site_states')->json(),
            Date::make('Created At', 'created_at'),
            Date::make('Updated At', 'updated_at'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    /*public static function authorizedToViewAny(Request $request): bool
    {
        return $request->user()?->isAdmin() || $request->user()?->isSupportLevel2();
    }*/

    public static function authorizedToCreate(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }
}
