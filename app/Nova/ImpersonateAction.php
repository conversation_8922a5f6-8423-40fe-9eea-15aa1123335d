<?php

namespace App\Nova;

use App\Policies\Nova\ImpersonateActionPolicy;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use PhpParser\Node\Scalar\String_;

class ImpersonateAction extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ImpersonateAction>
     */
    public static $model = \App\Models\ImpersonateAction::class;

    public static $policy = ImpersonateActionPolicy::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'impersonator.email',
        'impersonated.email',
        'impersonator.name',
        'impersonated.name'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make('Impersonator', 'impersonator', User::class),
            BelongsTo::make('Impersonated', 'impersonated', User::class),
            Text::make('Status'),
            Boolean::make('Is Running', 'is_running')->exceptOnForms()->readonly(),
            Text::make('Duration', 'duration_seconds', function (){
                return $this->duration_seconds ? format_billing($this->duration_seconds/60).' min' : null;
            })->readonly(),
            Text::make('Action ID')->hideFromIndex()->copyable()->readonly(),
            Text::make('Session ID')->copyable(),
            DateTime::make('Started At'),
            DateTime::make('Stopped At'),
            Code::make('Actions')->json(),
            DateTime::make('Created At')->hideFromIndex(),
            DateTime::make('Updated At')->hideFromIndex(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function authorizedToRestore(Request $request)
    {
        return false;
    }

    public function authorizedToView(Request $request)
    {
        return $request->user()?->isAdmin() || $request->user()?->isSupportLevel2();
    }

    /*public static function authorizedToViewAny(Request $request): bool
    {
        return $request->user()?->isAdmin() || $request->user()?->isSupportLevel2();
    }*/
}
