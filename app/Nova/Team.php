<?php

namespace App\Nova;

use App\Enums\XcloudBilling\BillType;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Enums\XcloudBilling\TeamBillingStatus;
use App\Models\Cloudflare;
use App\Nova\Actions\DowngradeTeamToFreePlan;
use App\Nova\Actions\UpdateTeam;
use App\Nova\Actions\GenerateInvoiceForForUnpaidBills;
use App\Nova\Actions\MonthlyBillCheckerForTeam;
use App\Nova\Actions\SyncWithFluentCRM;
use App\Nova\Filters\ActivePlanFilterForTeam;
use App\Nova\Lenses\BillNotGeneratedTeams;
use App\Nova\Lenses\ExtendedTimeForTeamsBilling;
use App\Nova\Lenses\ActiveBillingLockedTeams;
use App\Nova\Lenses\PayableButFreePlan;
use App\Nova\Lenses\TeamWithMostServers;
use App\Nova\Lenses\MostValuableTeams;
use App\Nova\Lenses\TeamWithMostSites;
use App\Nova\Lenses\UsingSystemWithoutPayingTeam;
use App\Nova\Metrics\TeamOfferBillsInCurrentMonth;
use App\Nova\Metrics\TeamOfferBillsInPreviousMonth;
use App\Nova\Metrics\TeamOfferBillsInTotal;
use App\Nova\Metrics\TeamPaidBillsInCurrentMonth;
use App\Nova\Metrics\TeamPaidBillsInPreviousMonth;
use App\Nova\Metrics\TeamPaidBillsInTotal;
use App\Nova\Metrics\TeamUnpaidBillsInCurrentMonth;
use App\Nova\Metrics\TeamUnpaidBillsInPreviousMonth;
use App\Nova\Metrics\TeamUnpaidBillsInTotal;
use Illuminate\Http\Request;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasManyThrough;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class Team extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Team::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    public static $group = 'User';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name', 'email', 'id'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            BelongsTo::make('Has White Label', 'whiteLabel', 'App\Nova\WhiteLabel')->nullable(),

            BelongsTo::make('Owned White Label', 'ownedWhiteLabel', WhiteLabel::class)->exceptOnForms(),

            Text::make('Created At', function ($user) {
                return $user->created_at->diffForHumans();
            })->sortable(),

            BelongsToMany::make('Package', 'packages', Package::class)
                ->allowDuplicateRelations()->fields(function () {
                    return [
                        DateTime::make('Attached At', 'attached_at')
                            ->default(now())
                            ->rules('required')
                    ];
                }),

            //Servers
            Text::make('Servers',function () {
                return $this->servers()->count();
            })->exceptOnForms(),

            //            BelongsTo::make('User','user_id')->searchable()->onlyOnForms(),

            Text::make('Email')
                ->rules('required', 'email', 'max:254'),

            Text::make('Fluent CRM', function () {

                if ($this->resource->fluent_crm_contact_id) {
                    return "<a class='link-default'
                        target='_blank'
                        href=".config('app.fluent_crm_hosted_wp').'/wp-admin/admin.php?page=fluentcrm-admin#/subscribers/'.$this->resource->fluent_crm_contact_id.">
                        Check Customer #{$this->resource->fluent_crm_contact_id}
                    </a>";
                }

                return 'N/A';

            })->onlyOnDetail()->asHtml(),

            Code::make('permissions')->json()
                ->height('150px')->onlyOnDetail(),

            Code::make('Availed Offers')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()
                ->height('150px')->hideFromIndex(),

            Code::make('Meta')->json()->readonly(function () {
                return true; // !auth()->user()->isSuperAdmin();
            })->hideFromIndex(),

            Code::make('Elastic Email', 'meta->elastic_email')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),

            Number::make('Max member count', 'meta->max_member_count')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->default(function (){
                return \App\Models\Team::DEFAULT_TEAM_COUNT;
            })->hideFromIndex(),

            Text::make('Stripe Customer ID', 'stripe_customer_id')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->onlyOnForms(),

            Text::make('Stripe Customer', function () {
                if ($this->stripe_customer_id) {
                    if($this->whiteLabel){
                        return "<a class='link-default' target='_blank' href='https://dashboard.stripe.com/connect/accounts/{$this->whiteLabel->connectedAccount->stripe_account_id}/customers/{$this->stripe_customer_id}'>
                            $this->stripe_customer_id
                        </a>";
                    }

                    return "<a class='link-default' target='_blank' href='https://dashboard.stripe.com/customers/{$this->stripe_customer_id}'>
                        $this->stripe_customer_id
                    </a>";
                }
                return 'N/A';
            })->asHtml()->exceptOnForms(),


            Number::make('Cloudflare Integration Limit', 'meta->cloudflare_integration_limit')
                ->readonly(function () {
                    return !auth()->user()->isSuperAdmin();
                })
                ->withMeta(['value' => \Arr::get($this->meta, 'cloudflare_integration_limit') ?? Cloudflare::MAX_INTEGRATION_LIMIT])
                ->min(5)
                ->max(15)
                ->help('Max allowed cloudflare integration : 15')
                ->hideFromIndex(),

            // Add field to assign from BillingPlans model
            //            Select::make('Active Plan', 'active_plan_id')->options(
            //                \App\Models\BillingPlan::all()->pluck('name', 'id')->toArray()
            //            )->onlyOnForms()->nullable()->help('must be sync with App\Enums\PlansEnum')->displayUsingLabels(),

            Select::make('Billing Status', 'billing_status')->options(
                TeamBillingStatus::asValueLabel()
            )->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->displayUsingLabels(),

            Select::make('Billing Type')->options(
                BillType::asValueLabel()
            )->hideFromIndex()->readonly(function (){
                return !auth()->user()->isSuperAdmin();
            })->displayUsingLabels(),

            BelongsTo::make('Active Plan', 'activePlan2', BillingPlan::class),

            DateTime::make('Billing Extension Time', 'extension_time_for_billing')->nullable()->readonly(function () {
                return !auth()->user()->isAdmin();
            }),

            Textarea::make('Billing Comment', 'billing_comment')->nullable()->readonly(function () {
                return !auth()->user()->isAdmin();
            })->help('This comment will be shown as notice on top of the application, for this specific team'),

            Code::make('Billing comments by various admins', 'meta->admin_comments')->json()->readonly(function () {
                return !auth()->user()->isAdmin();
            })->readonly()->hideFromIndex(),

            Code::make('availed_offers')->json()
                ->height('150px')->onlyOnDetail(),


            //Is it a personal team?
            Boolean::make('Personal Team'),

            Text::make('Affiliate Expiry At')
                ->sortable()
                ->readonly()
                ->nullable(),

            Text::make('Owner', function () {
                return "{$this->owner?->name} ({$this->owner?->email})";
            })->exceptOnForms(),

            HasOne::make('Owner', 'owner', User::class),

            BelongsToMany::make('Other Users', 'users', User::class),

            HasMany::make('Team User Permissions', 'teamUserPermissions', TeamUser::class),

            HasMany::make('Servers', 'servers'),

            HasManyThrough::make('Sites', 'sites', Site::class),

            HasMany::make('Email Providers', 'emailProviders', EmailProvider::class),

            BelongsToMany::make('Product', 'products', 'App\Nova\Product')
                ->allowDuplicateRelations()->fields(function () {
                    return [
                        DateTime::make('Attached At', 'attached_at')
                            ->default(now())
                            ->rules('required')
                    ];
                }),

            BelongsToMany::make('Subscriptions', 'subscriptions', SubscriptionProduct::class),
            //                ->allowDuplicateRelations()->fields(function () {
            //                    return [
            //                        DateTime::make('Attached At', 'attached_at')
            //                            ->default(now())
            //                            ->rules('required')
            //                    ];
            //                }),

            Text::make('Latest Payment Method', function () {
                return $this->paymentMethods()->where('default_card', true)->latest()->first()?->card_no;
            })->onlyOnIndex()->asHtml(),

            HasMany::make('Bills'),
            HasMany::make('Cloudflare Integrations'),
            HasMany::make('Refunds'),
            HasMany::make('General Invoices'),
            HasMany::make('Manual Invoices'),
            HasMany::make('Payment Methods'),
            HasMany::make('Webhooks'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            // (new TeamUnpaidBillsInPreviousMonth())->width('1/3'),
            // (new TeamUnpaidBillsInTotal())->width('1/3'),
            // (new TeamUnpaidBillsInCurrentMonth())->width('1/3'),
            // (new TeamPaidBillsInPreviousMonth())->width('1/3'),
            // (new TeamPaidBillsInTotal())->width('1/3'),
            // (new TeamPaidBillsInCurrentMonth())->width('1/3'),
            // (new TeamOfferBillsInPreviousMonth())->width('1/3'),
            // (new TeamOfferBillsInTotal())->width('1/3'),
            // (new TeamOfferBillsInCurrentMonth())->width('1/3'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new Filters\ActivePlanFilterForTeam(),
            new Filters\HasPaymentMethodOnTeam(),
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [
            new MostValuableTeams,
            new TeamWithMostServers,
            new TeamWithMostSites,
            new UsingSystemWithoutPayingTeam,
            new BillNotGeneratedTeams,
            new ExtendedTimeForTeamsBilling,
            new ActiveBillingLockedTeams,
            new PayableButFreePlan
        ];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            new SyncWithFluentCRM(),
            (new GenerateInvoiceForForUnpaidBills())->canRun(function ($request) {
                return $request->user()->isAdmin() || $request->user()->isSupportLevel2();
            }),
            new MonthlyBillCheckerForTeam(),
            (new UpdateTeam())->canRun(function ($request) {
                return $request->user()->isAdmin() || $request->user()->isSupportLevel2();
            }),
            (new DowngradeTeamToFreePlan())->canRun(function ($request) {
                return $request->user()->isAdmin();
            }),
        ];
    }

    public function authorizedToForceDelete(Request $request)
    {
        return $request->user()?->isSuperAdmin();
    }

    public function authorizedToDelete(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToRestore(Request $request)
    {
        return $request->user()?->isSuperAdmin();
    }

    public static function authorizedToCreate(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->hasAnySupportRole();
    }

    public function authorizedToRunAction(NovaRequest $request, Action $action): bool
    {
        return $request->user()?->isSuperAdmin();
    }
}
