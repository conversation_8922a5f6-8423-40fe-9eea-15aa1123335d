<?php

namespace App\Nova;

use App\Enums\XcloudBilling\ProductSources;
use App\Enums\XcloudBilling\RefundStatuses;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Currency;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class Refund extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Refund::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';
    public static $group = 'Payment Gateway';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'title'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make('Title')->nullable(),
            Currency::make('Amount')->readonly()->required()->sortable(),
            Select::make('Status')
                ->rules('required')
                ->options(
                    RefundStatuses::asValueLabel(true)
                )->help(
                    'The status of the refund'
                )->sortable(),
            Boolean::make('Is Deleted')->onlyOnDetail()->sortable(),
            DateTime::make('Deleted At')->onlyOnDetail()->sortable(),
            DateTime::make('Created At')->onlyOnDetail()->sortable(),
            DateTime::make('Updated At')->onlyOnDetail()->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->isSupportLevel2();
    }

    public static function authorizedToCreate(Request $request)
    {
        return $request->user()?->isAdmin();
    }
}
