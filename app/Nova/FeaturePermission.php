<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Heading;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Panel;

class FeaturePermission extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\FeaturePermission>
     */
    public static string $model = \App\Models\FeaturePermission::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        // Load permissions from JSON file
        $jsonFilePath = base_path('resources/json/featurePermissions.json');
        $json = file_get_contents($jsonFilePath);
        $features = json_decode($json, true);

        // If JSON is not valid, return an empty array
        if (is_null($features)) {
            return [];
        }

        // Sync JSON entries with the database using firstOrCreate
        foreach ($features as $category => $permissions) {
            foreach ($permissions as $featureKey => $description) {
                \App\Models\FeaturePermission::firstOrCreate(
                    ['feature_key' => $featureKey],
                    ['is_enabled_for_whitelabel' => false] // Default value for new entries
                );
            }
        }

        // Create lookup tables for feature descriptions and categories
        $featureDetails = [];
        foreach ($features as $category => $permissions) {
            foreach ($permissions as $featureKey => $description) {
                $featureDetails[$featureKey] = [
                    'category' => ucfirst($category), // Capitalize category name
                    'description' => $description,
                ];
            }
        }

        // Enrich the database records with category and description from JSON
        $permissions = \App\Models\FeaturePermission::all()->map(function ($permission) use ($featureDetails) {
            $featureKey = $permission->feature_key;
            $permission->category = $featureDetails[$featureKey]['category'] ?? 'No category available';
            $permission->description = $featureDetails[$featureKey]['description'] ?? 'No description available';
            return $permission;
        });

        // Sort permissions by category alphabetically
        $sortedPermissions = $permissions->sortBy('category')->values();

        return [
            ID::make()->sortable(),

            Text::make('Feature Key')
                ->sortable()
                ->readonly(), // Display the feature key as a read-only text field

            Text::make('Category', function () use ($sortedPermissions) {
                // Fetch the category for the current model
                $featureKey = $this->feature_key;
                $permission = $sortedPermissions->firstWhere('feature_key', $featureKey);
                return $permission->category;
            })
                ->onlyOnIndex() // Show the category only on the index page (optional)
                ->sortable(), // Make the category sortable if desired

            Text::make('Description', function () use ($sortedPermissions) {
                // Fetch the description for the current model
                $featureKey = $this->feature_key;
                $permission = $sortedPermissions->firstWhere('feature_key', $featureKey);
                return $permission->description;
            })
                ->onlyOnIndex() // Show the description only on the index page (optional)
                ->sortable(), // Make the description sortable if desired

            Boolean::make('Is Enabled for Whitelabel', 'is_enabled_for_whitelabel')
                ->sortable()
                ->default(false), // Display the boolean field for 'is_enabled_for_whitelabel'
        ];
    }




    public static function authorizedToCreate(Request $request): bool
    {
        return false; // Disables the ability to create new rows
    }




    /**
     * Get the cards available for the request.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }
}
