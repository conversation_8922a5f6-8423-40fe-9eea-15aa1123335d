<?php

namespace App\Nova;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Nova\Filters\AmountFilter;
use App\Nova\Filters\InvoiceByMonth;
use App\Nova\Filters\InvoiceHasBills;
use App\Nova\Filters\InvoiceHasCartFormFilter;
use App\Nova\Filters\InvoiceHasNoBillsOrCartForm;
use App\Nova\Metrics\GeneralInvoiceCancelled;
use App\Nova\Metrics\GeneralInvoiceFailed;
use App\Nova\Metrics\GeneralInvoicePaid;
use App\Nova\Metrics\GeneralInvoiceRefunded;
use App\Nova\Metrics\GeneralInvoiceRefundRequested;
use App\Nova\Metrics\GeneralInvoiceUnpaid;
use App\Nova\Metrics\InvoiceBillsByServices;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class GeneralInvoice extends Resource
{
    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->where('type', InvoiceTypesEnum::General);
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\GeneralInvoice::class;


    public static $group = 'Invoices';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'invoice_number';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'invoice_number', 'gateway_invoice_or_intent_id', 'title', 'reference_no', 'customer_email', 'team.email', 'team.name'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make('Reference No')->onlyOnDetail(),

            Text::make('Invoice Number', function (){
                return '<a target="_blank" href="'.route('user.invoice.preview-download', $this->invoice_number).'" class="link-default">'.$this->invoice_number.'</a>';
            })->readonly()->asHtml()->exceptOnForms(),

            Text::make('Title')->nullable(),

            Textarea::make('Description')->rules([
                'nullable',
                'max:256'
            ])->hideFromIndex()->nullable(),

            Textarea::make('Notes', 'note')->rules([
                'nullable',
                'max:600'
            ])->hideFromIndex()->nullable(),

            Currency::make('Amount')->readonly(function () {
                return $this->resource->exists;
            })->showOnIndex(function () use ($request){
                if($request->user()->isSuperAdmin()){
                    return true;
                }
                return $request->search && ($request->user()->isAdmin() || $request->user()->isSupportLevel2()) && Str::length($request->search) >= 20;
            })->currency($this->resource?->currency?->value ?: BillingCurrency::USD->value)->sortable(),

            Currency::make('Application Fee', 'application_fee_amount')->readonly(function () use ($request) {
                if ($request->user()->isSuperAdmin()) {
                    return false;
                }
                return $this->resource->exists;
            })->showOnIndex(function () use ($request){
                if($request->user()->isSuperAdmin()){
                    return true;
                }
                return $request->search && ($request->user()->isAdmin() || $request->user()->isSupportLevel2()) && Str::length($request->search) >= 20;
            })->currency($this->resource?->currency?->value ?: BillingCurrency::USD->value)->sortable(),

            Textarea::make('Application Fee Log', 'meta->application_fee_log')
                ->readonly()
                ->exceptOnForms()
                ->nullable(),

            Currency::make('Refundable Amount', 'refundable_amount')
                ->showOnIndex(function () use ($request){
                    return $request->search && ($request->user()->isAdmin() || $request->user()->isSupportLevel2()) && Str::length($request->search) >= 20;
                })->currency($this->resource?->currency?->value ?: BillingCurrency::USD->value)->nullable(),

            Currency::make('Refunded Amount', 'refunded_amount')
                ->showOnIndex(function () use ($request){
                    return $request->search && ($request->user()->isAdmin() || $request->user()->isSupportLevel2()) && Str::length($request->search) >= 20;
                })->currency($this->resource?->currency?->value ?: BillingCurrency::USD->value)->nullable(),

            Text::make('Gateway Link', function () {

                if ($this->payment_gateway === PaymentGateway::Stripe) {
                    if (app()->environment('production')) {

                        if($this->team->whiteLabel){
                            return "<a class='link-default'
                                target='_blank'
                                href='https://dashboard.stripe.com/connect/accounts/".$this->team->whiteLabel->connectedAccount->stripe_account_id."/payments/".$this->resource->gateway_invoice_or_intent_id."'>
                                Go to Stripe
                            </a>";
                        }

                        return "<a class='link-default'
                            target='_blank'
                            href='https://dashboard.stripe.com/payments/".$this->resource->gateway_invoice_or_intent_id."'>
                            Go to Stripe
                        </a>";
                    }

                    if($this->team->whiteLabel){
                        return "<a class='link-default'
                            target='_blank'
                            href='https://dashboard.stripe.com/connect/accounts/".$this->team->whiteLabel->connectedAccount->stripe_account_id."/test/payments/".$this->resource->gateway_invoice_or_intent_id."'>
                            Go to Stripe
                        </a>";
                    }

                    return "<a class='link-default'
                        target='_blank'
                        href='https://dashboard.stripe.com/test/payments/".$this->resource->gateway_invoice_or_intent_id."'>
                        Go to Stripe
                    </a>";
                }

                return 'N/A';

            })->onlyOnDetail()->asHtml(),

            Currency::make('Discount')->readonly(function () {
                return $this->resource->exists;
            })->showOnIndex(function () use ($request){
                return $request->search && ($request->user()->isAdmin() || $request->user()->isSupportLevel2()) && Str::length($request->search) >= 20;
            })->currency($this->resource?->currency?->value ?: BillingCurrency::USD->value)->nullable(),

            Select::make('Currency')->options(
                BillingCurrency::asValueLabel()
            )->showOnIndex(false)->sortable()->default(BillingCurrency::USD),
//        ->readonly(function () {
//                return $this->resource->exists;
//            })->showOnIndex(false)->sortable()->default(BillingCurrency::USD),

            Select::make('Status')->options(
                BillingStatus::asValueLabel()
            )->readonly(function (){
                return !auth()->user()->isSuperAdmin();
            })->default(BillingStatus::Unpaid->value),

            Select::make('Source')->options(
                InvoiceSourceEnum::asValueLabel()
            )->nullable()->default(InvoiceSourceEnum::SinglePurchase->value),

            DateTime::make('Purchased At', 'created_at')->readonly(function () {
                return $this->resource->exists;
            })->sortable(),

            Select::make('Payment Gateway')->options(
                PaymentGateway::asValueLabel()
            )->readonly(function () {
                return $this->resource->exists;
            })->showOnIndex(false)->default(PaymentGateway::Stripe),

            BelongsTo::make('Team')->readonly(function () {
                return $this->resource->exists;
            })->searchable()->nullable(),

            HasMany::make('Bills')->nullable(),

            HasMany::make('Cart Form')->nullable(),

            Text::make('Customer Email')->readonly(function () {
                return $this->resource->exists;
            })->copyable()->sortable()->nullable(),

            BelongsTo::make('Payment Method')->readonly(function () {
                return $this->resource->exists;
            })->nullable(),

            Select::make('Type')->options(
                InvoiceTypesEnum::asValueLabel()
            )->readonly(function () use ($request) {
                return !$request->user()->isSuperAdmin();
            })->hideFromIndex(),

            Text::make('Gateway Customer ID')->hideFromIndex()->readonly(function () {
                return !auth()->user()->isSuperAdmin() && $this->resource->exists;
            })->help('Be careful on edit')->copyable()->nullable(),

            Text::make('Gateway Invoice or Intent ID')->readonly(function () {
                return !auth()->user()->isSuperAdmin() && $this->resource->exists;
            })->help('Be careful on edit')->copyable()->hideFromIndex()->nullable(),

            Text::make('Gateway Payment Method ID')->readonly(function () {
                return !auth()->user()->isSuperAdmin() && $this->resource->exists;
            })->help('Be careful on edit')->copyable()->hideFromIndex()->nullable(),

            Code::make('Meta')->readonly()->json()->nullable(),

            Code::make('Logs')->readonly()->json()->nullable(),

            Text::make('Call Trace', 'call_trace')->placeholder('No trace found')->showOnIndex(false)->readonly(),

            Date::make('Due Date')->nullable(),

            BelongsTo::make('Coupon')->nullable(),

            HasMany::make('Bills', 'bills', Bill::class),

            DateTime::make('Created At')->hideFromIndex(),

            DateTime::make('Updated At')->readonly(function () {
                return $this->resource->exists;
            })->hideFromIndex()->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new InvoiceBillsByServices)->onlyOnDetail(),
            (new GeneralInvoicePaid())->canSee(fn() => $request->user()->isSuperAdmin()),
            (new GeneralInvoiceUnpaid())->canSee(fn() => $request->user()->isSuperAdmin()),
            (new GeneralInvoiceRefunded())->canSee(fn() => $request->user()->isSuperAdmin()),
            (new GeneralInvoiceFailed())->canSee(fn() => $request->user()->isSuperAdmin()),
            (new GeneralInvoiceCancelled())->canSee(fn() => $request->user()->isSuperAdmin()),
            (new GeneralInvoiceRefundRequested())->canSee(fn() => $request->user()->isSuperAdmin())
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new Filters\InvoiceByStatus(),
            new InvoiceByMonth(),
            new InvoiceHasCartFormFilter(),
            new InvoiceHasBills(),
            new InvoiceHasNoBillsOrCartForm(),
            AmountFilter::make('Amount')
                ->range(0, 2000)
                ->marks([
                    '0' => '🙂',
                    '500' => '😄',
                    '1000' => '🥳',
                    '1500' => '😎',
                    '2000' => '🤑'
                ])->canSee(fn() => $request->user()->isSuperAdmin()),
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new Actions\ResetInvoiceForRetryPayment())->canSee(fn() => $request->user()->isAdmin()),
            (new Actions\PermitRefundInvoices())->canSee(fn() => $request->user()->isSuperAdmin()),
            (new Actions\RequestForPermitToRefundInvoices())->canSee(fn() => $request->user()->isAdmin() || $request->user()->isSupportLevel2()),
            (new Actions\ForceCancelInvoice())->canSee(fn() => $request->user()->isSuperAdmin()),
            (new Actions\FetchPaymentWebhookForGeneralInvoice())->canSee(fn() => $request->user()->isAdmin() || $request->user()->isSupportLevel2()),
            (new Actions\InspectInvoice())->canSee(fn() => $request->user()->isAdmin()),
        ];
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function authorizedToRunAction(NovaRequest $request, Action $action)
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public function authorizedToUpdate(Request $request)
    {
        return $request->user() && ($request->user()->isAdmin() || $request->user()->isSupportLevel2());
    }

    public function authorizedToView(Request $request)
    {
        return $request->user()?->isAdmin() || $request->user()?->hasAnySupportRole();
    }

    public function authorizedToDelete(Request $request)
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    /*public static function authorizedToViewAny(Request $request): bool
    {
        return $request->user()?->isAdmin() || $request->user()?->hasAnySupportRole();
    }*/
}
