<?php

namespace App\Nova;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\TeamBillingStatus;
use App\Nova\Actions\SiteDeleteAction;
use App\Nova\Metrics\Sites;
use App\Nova\Metrics\SitesPerDay;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasOne;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Fields\URL;
use Laravel\Nova\Http\Requests\NovaRequest;
use Naoray\NovaJson\JSON;

class Site extends Resource
{
    /**
     * The number of resources to show per page via relationships.
     *
     * @var int
     */
    public static $perPageViaRelationship = 10;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static string $model = \App\Models\Site::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name', 'title', 'type', 'server.public_ip'
    ];

    public static $with = [
        'server', 'server.team'
    ];

    public static $group = 'Main';

    /**
     * Get the fields displayed by the resource.
     *
     * @param Request $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),

            Text::make('Title', 'title', fn() => "<a href='".route('site.show', [$this->server_id, $this->id])."'
                    class='no-underline dim text-primary font-bold'
                    target=_blank>{$this->name}</a>")
                ->sortable()
                ->rules('required', 'max:255')->asHtml()->exceptOnForms(),

            Text::make('Name', 'name')->onlyOnDetail(),

            Text::make('Title', 'title')
                ->sortable()
                ->rules('required', 'max:255')->asHtml()->onlyOnForms(),

            Text::make('Type')->onlyOnForms(),
            Text::make('Type', 'type', fn() =>
                "<img height='20px' width='20px' src=".asset('img/'.$this->type->value.'.svg').">")->asHtml()->exceptOnForms(),

            Text::make('SSL Provider', 'ssl_provider')->hideFromIndex(),

            HasOne::make('SSL Certificate', 'sslCertificate', SslCertificate::class)->exceptOnForms(),
            HasMany::make('SSL Certificates', 'sslCertificates', SslCertificate::class)->exceptOnForms(),

            BelongsToMany::make('SSH Key Paris', 'sshKeyParis', SshKeyPair::class)->exceptOnForms(),

            Text::make('Team', fn() =>
                "<a class='no-underline font-bold text-amber-700'
                    href='".config('nova.path')."/resources/teams/{$this->server?->team?->id}'>".$this->server?->team?->name."</a>")
                ->asHtml()->exceptOnForms(),

            Text::make('Status','status'),
            Select::make('Environment', 'environment')->options(
                [
                    'production' => \App\Models\Site::PRODUCTION,
                    'staging' => \App\Models\Site::STAGING,
                    'staging_with_own_domain' => \App\Models\Site::STAGING_WITH_OWN_DOMAIN,
                    'demo' => \App\Models\Site::DEMO,
                ]
            )->displayUsingLabels(),
            Text::make('DB Provider', 'database_provider')->onlyOnForms(),
            Text::make('Database Name', 'database_name')->onlyOnForms(),
            Text::make('Database User', 'database_user')->onlyOnForms(),
            Text::make('Database Password', 'database_password')->onlyOnForms(),
            Text::make('Database Host', 'database_host')->onlyOnForms(),
            Text::make('Database Port', 'database_port')->onlyOnForms(),
            Text::make('PHP Version', 'php_version'),
            Text::make('WP Version', 'wordpress_version'),
            Text::make('Site User', 'site_user')->onlyOnForms(),
            Text::make('Admin', 'admin_user')->onlyOnForms(),
            Text::make('SSH Mode', 'site_ssh_authentication_mode')->onlyOnDetail(),

            Code::make('Additional Domains', 'additional_domains')->json()->hideFromIndex(),

            DateTime::make('Created At')->onlyOnDetail(),

            JSON::make('Meta', [
                Boolean::make('Edit X Frame', 'modify_x_frame_options')->hideFromIndex(),
                Text::make('X frame Value', 'x_frame_options')->rules([
                    'nullable',
                    'required_if:modify_x_frame_options,1',
                    'string',
                    function ($attribute, $value, $fail) {
                        if (preg_match('/^ALLOW-FROM\s+(https?:\/\/\*|https?:\/\/[a-z0-9.-]+(?:\s+https?:\/\/[a-z0-9.-]+)*)$/i', $value)) {
                            return;
                        }
                        if (!in_array(strtoupper($value), ['SAMEORIGIN', 'DENY', 'ALLOWALL'])) {
                            $fail('Invalid value for X-Frame-Options. Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com');
                        }
                    },
                ])->hideFromIndex(),
                Boolean::make('Disable Nginx Regeneration', 'disable_nginx_config_regeneration')->hideFromIndex(),
                Boolean::make('Enable PHP Execution on Upload Directory', 'enable_php_execution_on_upload_directory')->hideFromIndex(),
            ])->nullable(),

            BelongsTo::make('Server'),

            Boolean::make('Is Disabled', 'is_disabled')->rules([
                'boolean',
            ])->hideFromIndex(),

            DateTime::make('Disabled At', 'disabled_at')->rules([
                'nullable',
                'date',
            ])->hideFromIndex(),

            DateTime::make('Last Visited', 'last_visited_at')->rules([
                'nullable',
                'date',
            ])->hideFromIndex(),

            BelongsTo::make('Disabled By', 'disabledBy', User::class)->rules([
                'exists:users,id',
            ])->nullable()->hideFromIndex(),

            HasOne::make('Site Migration', 'SiteMigration'),

            HasMany::make('Tasks'),

            HasMany::make('BackupFiles'),
//
//            URL::make($this->resource?->server?->team?->name ?: 'Team', 'server_id', function () {
//                // $server = \App\Models\Server::where('id', $server_id)->first();
//                // dump($server);
//                return config('nova.path')."/resources/teams/{$this->resource?->server?->team_id}";
//            })->onlyOnDetail(),

            Code::make('Site Meta', 'meta')->json(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param Request $request
     * @return array
     */
    public function cards(NovaRequest $request): array
    {
        return [
            (new Sites)->width('1/2'),
            (new SitesPerDay)->width('1/2')
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param Request $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new Actions\RegenerateNginxConfigForSites())
                ->confirmText('Are you sure you want to regenerate the Nginx config for selected sites? This action is irreversible.')
                ->confirmButtonText('Regenerate')
                ->cancelButtonText("Cancel")
                ->canSee(fn() => $request->user()->isSuperAdmin()),
            (new Actions\ReinstallSiteMonitoring())
                ->confirmText('Are you sure you want to reinstall monitoring for selected sites? This action is irreversible.')
                ->confirmButtonText('Reinstall')
                ->cancelButtonText("Cancel")
                ->canSee(fn() => $request->user()->isSuperAdmin()),
            (new Actions\RetrySiteProvision())
                ->confirmText('Are you sure you want to retry Provisioning?')
                ->confirmButtonText('Retry Site Provisioning')
                ->cancelButtonText("Cancel")
                ->canSee(fn() => $request->user()->isSuperAdmin()),
            (new Actions\RetrySiteCloning())
                ->confirmText('Are you sure you want to retry site cloning?')
                ->confirmButtonText('Retry Cloning')
                ->cancelButtonText("Cancel")
                ->canSee(fn() => $request->user()->isSuperAdmin()),

            (new Actions\Site\ConfigureRedisObjectCachePro())
                ->confirmText('Are you sure you want to configure Redis Object Cache Pro?')
                ->confirmButtonText('Configure Redis Object Cache Pro')
                ->cancelButtonText("Cancel")
                ->onlyOnDetail()
                ->canSee(fn() => $request->user()->isAdmin() || $request->user()->isSupportLevel2()),

            (new Actions\Site\DeleteRemoteExpireBackupFiles())
                ->confirmText('Are you sure you want to delete remote expired backup files?')
                ->confirmButtonText('Delete')
                ->cancelButtonText("Cancel")
                ->onlyOnDetail()
                ->canSee(fn() => $request->user()->isAdmin()),

            (new SiteDeleteAction())
                ->confirmText('Are you sure you want to delete this site? This action is irreversible.')
                ->confirmButtonText('Delete Site')
                ->cancelButtonText("Cancel")
                ->canSee(fn() => $request->user()->isSuperAdmin()),
        ];
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request)
    {
        return $request->user()?->isAdmin();
    }
}
