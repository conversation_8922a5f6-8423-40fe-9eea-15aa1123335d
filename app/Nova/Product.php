<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use App\Enums\ProductProvider;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\ProductSources;
use App\Enums\XcloudBilling\XcloudProductType;
use App\Nova\Filters\ProductServiceType;
use App\Nova\Filters\ProductTypeFilter;
use App\Services\CloudServices\Fetchers\Providers\VultrFetcher;
use App\Traits\XcSoftDelete;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Lara<PERSON>\Nova\Fields\MultiSelect;
use <PERSON><PERSON>\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class Product extends Resource
{

    use XcSoftDelete;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Product::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';
    public static $group = 'Payment Gateway';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'title', 'slug', 'sku'
    ];

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->whereNull('white_label_id');
    }

    public function title()
    {
        return $this->title . ' ~ ['.($this->currency?->symbol()).$this->price. ' - ' . $this->service_type?->toShortIdentifier() . ']';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()
                ->sortable(),

            Text::make('Title')
                ->rules('required')
                ->help('The title will be shown on the checkout page')
                ->sortable(),

            Currency::make('Price')
                ->rules('required', 'numeric', 'min:0')
                ->nullable(),

            BelongsTo::make('Source', 'source', Product::class)
                ->nullable()
                ->onlyOnDetail()
                ->help('The source product will be used to identify the product')
                ->sortable(),

            BelongsTo::make('Dependent On', 'dependency', Product::class)
                ->nullable()
                ->help('The dependent product will be used to identify the product')
                ->sortable(),

            Textarea::make('Description')->rules('required')->nullable(),

            Select::make('Type')
                ->rules('nullable')
                ->options(
                    XcloudProductType::asValueLabel(true)
                )->help(
                    'The type will be used to determine the product type when the user purchases this package.
                        Keep empty if you do not want to set the type.'
                )->sortable(),

            Text::make('Slug')
                //->rules('required', 'alpha_dash')
                ->rules(function (NovaRequest $request) {
                    $rules = ['required', 'alpha_dash'];

                    if ($request->input('provider') === ProductProvider::Vultr->value) {
                        $rules[] = function ($attribute, $value, $fail) {
                            $vultrSizes = collect((new VultrFetcher(config('services.xvultr.token')))->getServerTypes())->flatten(1)->pluck('slug')->toArray();

                            if (!in_array($value, $vultrSizes)) {
                                $fail('The selected slug is invalid. Please ensure the slug matches one of the Vultr sizes.');
                            }
                        };
                    }

                    return $rules;
                })
                ->help('The slug will be used to identify the product')
                ->sortable(),

            Text::make('Sku')
                ->rules('required', 'alpha_dash')
                ->help('The sku will be used to identify the product')
                ->sortable(),

            Select::make('Source Model')
                ->rules('required')
                ->options([
                    \App\Models\Server::class => \App\Models\Server::class,
                    \App\Models\EmailProvider::class => \App\Models\EmailProvider::class,
                    \App\Models\PatchstackVulnerability::class => \App\Models\PatchstackVulnerability::class,
                    \App\Models\AddonStorageProvider::class => \App\Models\AddonStorageProvider::class,
                ])
                ->help('The source model will be used to identify the product')
                ->default(\App\Models\Server::class)->sortable(),

            Select::make('Provider')
                ->rules('required')
                ->options(
                    ProductProvider::asValueLabel(true)
                )->help('The provider will be used to determine the product provider. Most of the cases we are taking from Vultr.
                              If there is no 3rd party provider please choose xCloud as own provider.'
                )->default(ProductProvider::Vultr)->sortable(),

            Text::make('Flag')
                ->rules('nullable')
                ->hideFromIndex()
                ->help('The flag will be used to identify the product')
                ->sortable(),

            Number::make('Unit')
                ->default(1)
                ->rules('required', 'integer', 'min:1')
                ->help('The unit will be used to determine the product unit when the user purchases this product.')
                ->sortable(),

            Text::make('Product Image URL')
                ->rules('url', 'nullable')
                ->hideFromIndex()
                ->help('The profile photo will be shown on the checkout page')
                ->nullable(),

            Text::make('Checkout URL')
                ->rules('url', 'nullable')
                ->hideFromIndex()
                ->help('The checkout URL will be used to redirect the user to the payment gateway')
                ->readonly()
                ->nullable(),

            Text::make('Checkout With Access Code', 'checkout_url')
                ->resolveUsing(function ($value) {
                    if ($this->resource->secret_code) {
                        return $value . '&access_key=' . $this->resource->secret_code;
                    }

                    if ($this->resource->coupons) {
                        return $value . '&access_key=' . implode('/', $this->resource->coupons->pluck('code')->toArray());
                    }
                })
                ->rules('url', 'nullable')
                ->hideFromIndex()
                ->help('The checkout URL will be used to redirect the user to the payment gateway')
                ->readonly()
                ->nullable()
                ->copyable(),

            Boolean::make('Is Active')->sortable(),

            Boolean::make('Allow White Label', 'available_for_white_label')->sortable(),

            Boolean::make('Allow Purchase Secretly', 'allow_secret_purchase')->sortable(),

            Text::make('Secret Code', 'secret_code')
                ->rules('nullable')
                ->hideFromIndex()
                ->help('The secret code will be used to identify the product while it\'s not allowed to purchase directly' )
                ->sortable(),

            Boolean::make('Show On Display')->default(true)->sortable(),

            Select::make('Expected Product Source')
                ->hideFromIndex()
                ->rules('nullable')
                ->options(
                    ProductSources::asValueLabel(true)
                )->help(
                    'The expected product source will be used to determine the product source when the user
                          purchases this package. Keep empty if you do not want to set the expected product source.'
                )->sortable(),

            Select::make('Renewal Type')
                ->rules('required')
                ->options(
                    BillRenewalPeriod::asValueLabel()
                )->sortable(),

            Select::make('Service Type')
                ->rules('required')
                ->hideFromIndex()
                ->options(
                    BillingServices::asValueLabel()
                )->sortable(),

            DependencyContainer::make([
                Number::make('Unit')
                    ->default(1)
                    ->canSee(function () {
                        return $this->resource->service_type === BillingServices::EmailProvider->value;
                    })
                    ->rules('nullable', 'integer', 'min:1')
                    ->help('The unit will be used to determine the product unit when the user purchases this product.'),
            ])->dependsOn('service_type', BillingServices::EmailProvider->value),

            Number::make('Max Purchase Limit', 'max_purchase_limit')
                ->rules('integer', 'min:1', 'nullable')
                ->placeholder('e.g. 1, 2, 3, 4, 5, 6, 7, 8, 9, 10')
                ->help('The maximum number of times a team can purchase this product. Keep empty if there is no limit.')
                ->hideFromIndex()
                ->nullable(),

            MultiSelect::make('Required Any Of Package Purchase', 'required_purchase_any_of_packages')
                ->options(function () {
                    return \App\Models\Package::pluck('name', 'id')->toArray();
                })->nullable()->help('If this package requires another package to be purchased, select the package here. Keep empty if there is no required package.'),

            MultiSelect::make('Required Any Of Product Purchase', 'required_purchase_any_of_products')
                ->options(function () {
                    return \App\Models\Product::whereNull('white_label_id')->pluck('title', 'id')->toArray();
                })->nullable()->help('If this package requires another product to be purchased, select the product here. Keep empty if there is no required product.'),

            BelongsTo::make('Requires Billing Plan', 'requiredBillingPlan', BillingPlan::class)
                ->nullable()
                ->readonly(function () {
                    return $this->resource->teams->count() > 0;
                })
                ->help('If this package requires a billing plan, select the billing plan here. Unmodifiable unless there is no team attached to this package.'),

            BelongsToMany::make('Teams', 'teams', Team::class)->fields(function () {
                return [
                    DateTime::make('Attached At', 'attached_at')
                        ->default(now())
                        ->rules('required')
                ];
            })->allowDuplicateRelations(),

            HasMany::make('Dependents', 'dependents', Product::class)
                ->onlyOnDetail()
                ->sortable(),

            HasMany::make('Bills', 'bills', Bill::class),

            DateTime::make('Created At')->onlyOnDetail(),

            DateTime::make('Updated At')->exceptOnForms(),
            DateTime::make('Deleted At')->hideFromIndex()->exceptOnForms(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new ProductTypeFilter(),
            new ProductServiceType()
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request) : bool
    {
        if ($this->resource->teams->count() > 0) {
            return false;
        }

        return $request->user() && $request->user()->isAdmin();
    }

    public function authorizedToRestore(Request $request) : bool
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public static function authorizedToCreate(Request $request) : bool
    {
        return $request->user() && $request->user()->isAdmin();
    }

    public function authorizedToUpdate(Request $request) : bool
    {
        return $request->user() && $request->user()->isAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->isSupportLevel2();
    }

//    public function authorizedToAttach(NovaRequest $request, $model)
//    {
//        return app()->environment('production') ? ($request->user() && $request->user()->isSuperAdmin()) : true;
//    }
//
//    public function authorizedToDetach(NovaRequest $request, $model)
//    {
//        return app()->environment('production') ? ($request->user() && $request->user()->isSuperAdmin()) : true;
//    }
//
//    public function authorizedToAttachAny(NovaRequest $request, $model)
//    {
//        return app()->environment('production') ? ($request->user() && $request->user()->isSuperAdmin()) : true;
//    }
}
