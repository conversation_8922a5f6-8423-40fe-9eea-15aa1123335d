<?php

namespace App\Nova\Metrics;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\Bill;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Value;

class BillsRefundable extends Value
{
    public $icon = 'scale';
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->sum($request, Bill::where('has_offer', false)->where('status', BillingStatus::Paid), 'refundable_amount', 'created_at')
            ->format('0,0.00')->prefix('$');
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            'ALL' => __('All'),
            30 => __('30 Days'),
            60 => __('60 Days'),
            365 => __('365 Days'),
            'TODAY' => __('Today'),
            'MTD' => __('Month To Date'),
            'QTD' => __('Quarter To Date'),
            'YTD' => __('Year To Date'),
        ];
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return  \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'bills-refundable';
    }
}
