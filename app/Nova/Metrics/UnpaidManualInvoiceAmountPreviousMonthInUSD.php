<?php

namespace App\Nova\Metrics;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\ManualInvoice;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use <PERSON><PERSON>\Nova\Nova;

class UnpaidManualInvoiceAmountPreviousMonthInUSD extends Value
{
    public $icon = 'currency-dollar';

    public function name()
    {
        return 'Unpaid amount in USD for '. now()->subMonth()->format('F');
    }

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->sum($request, ManualInvoice::where('created_at', '>=', now()->subMonth())->where('status', '!=', BillingStatus::Paid), 'amount', 'created_at')
                    ->format('0,0.00')
                    ->prefix('$');
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            'ALL' => 'ALL'
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
}
