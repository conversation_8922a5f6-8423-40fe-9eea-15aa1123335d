<?php

namespace App\Nova\Metrics;

use App\Models\Bill;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use <PERSON>vel\Nova\Nova;

class TeamUnpaidBillsInPreviousMonth extends Value
{
    public $icon = 'cash';

    public $onlyOnDetail = true;

    public $name = 'Unpaid Bills In Previous Month';

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->sum($request, Bill::where('team_id', $request->resourceId)->where('bill_from',  now()->startOfMonth()->subMonth()->toDateString())->unpaid()->withoutOffer(), 'amount_to_pay')->prefix('$')->suffix(' USD');
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            'ALL' => Nova::__('All Time'), // Add this line to the ranges array
            30 => Nova::__('30 Days'),
            60 => Nova::__('60 Days'),
            365 => Nova::__('365 Days'),
            'TODAY' => Nova::__('Today'),
            'MTD' => Nova::__('Month To Date'),
            'QTD' => Nova::__('Quarter To Date'),
            'YTD' => Nova::__('Year To Date'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
}
