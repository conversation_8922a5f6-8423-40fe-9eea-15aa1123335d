<?php

namespace App\Nova\Metrics;

use App\Enums\Stack;
use App\Models\Server;
use App\Models\Site;
use App\Models\Team;
use App\Services\SitePlayGround\RequestToJoinTeam;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class SiteCountByEnvironment extends Partition
{
    public $name = 'Site Environment';

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        // return $this->count($request, Site::class, 'environment');

        $stacks = [
            'Production' => Site::where('environment', 'production')->count(),
            'Demo' => Site::where('environment', 'demo')->count(),
            'Staging' => Site::where('environment', 'staging')->count(),
            'Playground' => Team::where('email', RequestToJoinTeam::playgroundTeamOwnerUserEmail)?->first()?->sites()?->count() ?: 0,
        ];

        return $this->result($stacks);
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'site-count-by-type';
    }
}
