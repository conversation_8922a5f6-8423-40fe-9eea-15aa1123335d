<?php

namespace App\Nova\Metrics;

use App\Enums\Stack;
use App\Models\Server;
use App\Models\Site;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class StackPerSite extends Partition
{
    public $name = 'Site Count by Stack';

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $stacks = [
            Stack::Nginx->toReadableString() => Site::whereHas('server', fn($query) => $query->where('stack', Stack::Nginx))->count(),
            Stack::OpenLiteSpeed->toReadableString() => Site::whereHas('server', fn($query) => $query->where('stack', Stack::OpenLiteSpeed))->count(),
        ];

        return $this->result($stacks);
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'stack-per-site';
    }
}
