<?php

namespace App\Nova\Metrics;

use App\Enums\CloudProviderEnums;
use App\Models\BillingPlan;
use App\Models\Product;
use App\Models\Server;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Partition;

class BillCountPerProduct extends Partition
{
    public $name = 'Bill Count Per Product';

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $data = [];

        foreach (Product::where('source_model', 'App\Models\Server')->get() as $product) {
            $billCount = $product->bills()->where("service_is_active", true)->where('generator_type', 'App\Models\Server')->distinct(['generator_type', 'generator_id'])->count();
            if ($billCount > 0) {
                if (!isset($data[$product->title])) {
                    $data[$product->title] = 0;
                }

                $data[$product->title] += $billCount;
            }
        }

        arsort($data);

        return $this->result($data);
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'bill-count-per-billing-product';
    }
}
