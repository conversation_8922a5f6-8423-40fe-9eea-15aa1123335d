<?php

namespace App\Nova\Metrics;

use App\Enums\PatchstackVulnerabilityStatus;
use App\Models\PatchstackVulnerability;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Partition;

class PatchstackAddonPerSite extends Partition
{
    public $name = 'Patchstack Addon Count by Status';

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $addons = [
            PatchstackVulnerabilityStatus::CONNECTED->toReadableString() => PatchstackVulnerability::where('status', PatchstackVulnerabilityStatus::CONNECTED)->count(),
            PatchstackVulnerabilityStatus::NOT_CONNECTED->toReadableString() => PatchstackVulnerability::where('status', PatchstackVulnerabilityStatus::NOT_CONNECTED)->count(),
        ];

        return $this->result($addons);
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
         return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'patchstack-addon-status';
    }
}
