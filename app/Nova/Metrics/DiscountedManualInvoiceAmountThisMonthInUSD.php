<?php

namespace App\Nova\Metrics;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Models\ManualInvoice;
use Carbon\Carbon;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;
use <PERSON>vel\Nova\Nova;

class DiscountedManualInvoiceAmountThisMonthInUSD extends Value
{
    public function name()
    {
        return 'Discounted amount in USD for '. now()->format('F');
    }

    public $icon = 'currency-dollar';

    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->sum($request, ManualInvoice::where('currency', BillingCurrency::USD)->where('created_at', '>=', now()), 'discount', 'created_at')
                    ->format('0,0.00')
                    ->prefix('$');
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            'ALL' => Nova::__('All Time')
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
}
