<?php

namespace App\Nova\Actions\Site;

use App\Enums\XcloudBilling\BillingStatus;
use App\Jobs\Site\ActiveRedisObjectCacheProJob;
use App\Models\Site;
use App\Models\StorageProvider;
use App\Scripts\InlineScript;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class DeleteAllBackups extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param Collection $models
     * @return \Laravel\Nova\Actions\ActionResponse
     */
    public function handle(ActionFields $fields, Collection $models): \Laravel\Nova\Actions\ActionResponse
    {
        if ($models->isEmpty()) {
            return Action::danger('No sites selected.');
        }
        $isLocal = $fields->get('is_local');
        $site = Site::find($models->first()?->id);
        $total_files = $site->backupFiles()->where(['is_local'=>$isLocal])->count();
        $log = "DeleteBackupFiles: Try to delete $total_files ". ($isLocal ? 'local' : 'remote') . " backup files of site $site->name";
        $site->log($log);
        if ($isLocal){
            $delete_script = new InlineScript("rm -rf ".$site->backupFilesPath()." && rm -rf ".$site->incrementalPath(),"Delete Local Backups of ({$site->name} on {$site->server->name})");
            $task = $site->runInline(script: $delete_script);
            if (!$task->successful()){
                return Action::danger('Failed to delete local backup files');
            }
        }else{
            //remove files from bucket
            $storageProviderIds = $site->backupFiles()
                ->where('is_local', $isLocal)
                ->distinct()
                ->pluck('backup_files.storage_provider_id');
            $storageProviders = StorageProvider::whereIn('id', $storageProviderIds)->get();

            $storageProviders
                ->each(fn ($storageProvider) =>
                    $storageProvider->deleteDirectory(dir:  $site->backupDirName())
                );

        }
        $site->backupFiles()->where(['is_local'=>$isLocal])->delete();

        return Action::message("Deleted $total_files ". ($isLocal ? 'local' : 'remote') . " backup files.");
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
           Select::make('Backup Type', 'is_local')
                ->options([
                    1 => 'Local',
                    0 => 'Remote',
                ])
                ->default(1)
                ->displayUsingLabels()
                ->rules('required'),

        ];
    }
}
