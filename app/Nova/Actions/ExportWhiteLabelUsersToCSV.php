<?php

namespace App\Nova\Actions;

use App\Enums\XcloudBilling\CSVExportType;
use App\Exports\WhiteLabelUsersExport;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Maatwebsite\Excel\Facades\Excel;

class ExportWhiteLabelUsersToCSV extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        try {
            $exportType = $fields->type ?? 'xlsx';
            $timezoneOffset = Carbon::now()->format('P');
            $fileName = 'XC-EXPORTED-WHITELABEL-USERS-' . Carbon::now()->format('Y-m-d,g:i:sA') . '(' . $timezoneOffset . ').' . $exportType;
            $filePath = 'whitelabel-users/' . $fileName;

            // Create the export
            Excel::store(new WhiteLabelUsersExport($models), $filePath, 's3');

            // Get the URL of the stored file
            $temporarySignedUrl = Storage::disk('s3')->temporaryUrl($filePath, now()->addHours(1));

            return Action::openInNewTab($temporarySignedUrl);
        } catch (\Exception $e) {
            \Log::error('Error exporting WhiteLabel users: ' . $e->getMessage(), [
                'exception' => $e,
                'user_count' => $models->count(),
            ]);

            return Action::danger('An error occurred while exporting users: ' . $e->getMessage());
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Type')
                ->options([
                    CSVExportType::XLSX->value => 'XLSX',
                    CSVExportType::CSV->value => 'CSV'
                ])
                ->help('Select the type you want to export.')
                ->displayUsingLabels()
                ->rules('required')
                ->default(CSVExportType::XLSX->value),
        ];
    }
}
