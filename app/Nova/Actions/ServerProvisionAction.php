<?php

namespace App\Nova\Actions;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Jobs\Server\DeleteServer;
use App\Jobs\Server\DeleteServerBills;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Task;
use App\Notifications\SendManualInvoicePaidNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Symfony\Component\Console\Command\Command as CommandAlias;

class ServerProvisionAction extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        if ($models->count() !== 1) {
            return Action::danger('You can only provision one server at a time.');
        }

        /** @var \App\Models\Server $server */
        $server = $models->first();

        Log::info('ServerProvisionAction: '.$fields->force_run.' by '.auth()->user()->email);

        if ($server) {
            if (!$fields->force_run && ($server->ipAddress() || $server->isProvisioned())) {
                return Action::danger("Only new server are allowed to provision. If you want to provision anyway, check 'Force Run' box.");
            }

            $server->provision();

            return Action::message($server->name.' provisioning initiated');
        }

        return Action::danger('Server not found');
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Boolean::make("Force Run", 'force_run')
                ->default(false)
                ->help('By default, only new server are not allowed to provision. If you want to provision anyway, check this box.')
                ->rules('boolean')->hideFromIndex(),
        ];
    }
}
