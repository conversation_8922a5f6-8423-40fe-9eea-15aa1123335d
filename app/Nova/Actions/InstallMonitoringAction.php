<?php

namespace App\Nova\Actions;

use App\Jobs\Server\InstallServerMonitorJob;
use App\Models\Server;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
class InstallMonitoringAction extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $servers
     */
    public function handle(ActionFields $fields, Collection $servers): ActionResponse|Action
    {
        if ($servers->count() == 0) {
            return Action::danger('Server not found');
        }
        /** @var Server $server */
        foreach ($servers as $server){
            Log::info('Installing monitoring to server: '.$server->id.' : '.$server->name.'  by '.auth()->user()->email);
            InstallServerMonitorJob::dispatch($server);
        }
        return Action::message($servers->count().' servers monitoring installation initiated');
    }

}
