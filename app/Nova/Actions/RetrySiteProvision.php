<?php

namespace App\Nova\Actions;

use App\Console\Commands\Site\SiteCloneStart;
use App\Enums\SiteStatus;
use App\Jobs\Site\ReInstallMonitoring;
use App\Models\Site;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Http\Requests\NovaRequest;

class RetrySiteProvision extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        if (!$fields->force_run) {
            return Action::danger('You must check the "Force Run" box to provision the site.');
        }
        if ($models->count() > 1) {
            return Action::danger('Only one site can be provisioned at a time.');
        }
        foreach ($models as $site) {
            if(!$site->isProvisioned()) {
                $site->provision();
                return Action::message('Site provisioning started.');
            } else {
                return Action::danger('Site is already provisioned.');
            }
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Boolean::make("Force Run", 'force_run')
                ->default(false)
                ->help('Sites are not allowed to provision. If you want to provision anyway, check this box.')
                ->rules('boolean')->hideFromIndex(),
        ];
    }
}
