<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ChangeOfferStatusForBills extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        if ($models->count() > 5) {
            return Action::danger('You can only update 5 bills at a time.');
        }

        foreach ($models as $bill) {
            $bill->has_offer = $fields->status;
            $bill->save();
        }

        return Action::message('Offer status changed successfully!');
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Status')->options([
                1 => 'Has Offer',
                0 => 'No Offer'
            ])->displayUsingLabels()->rules('required'),
        ];
    }


    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new Actions\Manu())
                ->canSee(function ($request) {
                    return $request->user()->isSuperAdmin();
                })
                ->canRun(function ($request, $model) {
                    return $request->user()->isSuperAdmin();
                })
                ->confirmText('Are you sure you want to update the status of selected invoices?')
        ];
    }
}
