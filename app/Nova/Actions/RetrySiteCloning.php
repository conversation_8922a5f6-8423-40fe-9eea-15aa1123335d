<?php

namespace App\Nova\Actions;

use App\Console\Commands\Site\SiteCloneStart;
use App\Enums\SiteStatus;
use App\Jobs\Site\ReInstallMonitoring;
use App\Models\Site;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;

class RetrySiteCloning extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $site) {
            if($site->status === SiteStatus::CLONING || $site->status === SiteStatus::CLONE_FAILED) {
                \Artisan::call('site:clone', ['site_id' => $site->id]);

                return Action::message('Site cloning started.');
            } else {
                return Action::danger('Site is not in cloning state.');
            }
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [];
    }
}
