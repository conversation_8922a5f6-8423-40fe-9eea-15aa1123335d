<?php

namespace App\Nova\Actions;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Jobs\Server\DeleteServer;
use App\Jobs\Server\DeleteServerBills;
use App\Jobs\Site\DeleteSiteJob;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Task;
use App\Notifications\SendManualInvoicePaidNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\ServerDeletedNotification;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class SiteDeleteAction extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        if ($models->count() !== 1) {
            return Action::danger('You can only delete one site at a time.');
        }

        $site = $models->first();

        if ($site) {
            DeleteSiteJob::dispatch($site->server, $site, true, true, true, true);
            return Action::message('Site delete job has been dispatched');
        }

        return Action::danger('Site not found');
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
        ];
    }
}
