<?php

namespace App\Nova\Actions;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class GenerateInvoiceForForUnpaidBills extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $message = 'Bills output ';
        // Model must be team
        $models->each(function ($model) use ($fields, &$message) {
            $bills = $model->bills->where('status', BillingStatus::Unpaid)->where('has_offer', false);

            if ($bills->isNotEmpty()) {
                // Generate invoice for team
                InvoiceGenerator::team($model)
                    ->bills($bills)
                    ->source(InvoiceSourceEnum::fromValue($fields->source))
                    ->title($fields->title ?: 'Invoice for '.Carbon::now()->format('F'))
                    ->sendNotification($fields->send_email)
                    ->currency(BillingCurrency::USD)
                    ->generate();
            } else {
                $message .= ', No unpaid bills #'.$model->id;
            }
        });

        return Action::message($message);
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make('Title'),
            Textarea::make('Description'),
            Select::make('Source')->help('For Email')->default(InvoiceSourceEnum::SinglePurchase->value)->options(InvoiceSourceEnum::asValueLabel()),
            Boolean::make('Send Email', 'send_email')->default(true),
        ];
    }
}
