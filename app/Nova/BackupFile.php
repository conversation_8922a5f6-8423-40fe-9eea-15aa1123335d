<?php

namespace App\Nova;

use App\Models\BackupSetting;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class BackupFile extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\BackupFile::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'file_name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),

            Text::make('File','file_name',function (){
                if ($this->status === BackupSetting::FAILED){
                    return "<span class='inline-block text-red-500'>$this->note</span>";
                }
                return $this->file_name;
            })->asHtml(),

            Boolean::make('Remote','is_remote')->sortable(),
            BelongsTo::make('User'),

            Text::make('Site', function () {
                $url = config('nova.path')."/resources/sites/{$this->resource?->backupSetting?->site_id}";
                $site = $this->resource?->backupSetting?->site;
                return "<a class='link-default' target='_blank' href=".$url.">$site?->name</a>";
            })->asHtml(),
        ];
    }


    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->isSupportLevel2();
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToRunAction(NovaRequest $request, Action $action): bool
    {
        return $request->user()?->isSuperAdmin();
    }

    public function filters(NovaRequest $request)
    {
        return [
            new \App\Nova\Filters\BackupStatus,
        ];
    }
}
