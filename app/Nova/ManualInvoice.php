<?php

namespace App\Nova;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Nova\Filters\InvoiceByMonth;
use App\Nova\Filters\InvoiceHasBills;
use App\Nova\Filters\InvoiceHasCartFormFilter;
use App\Nova\Filters\InvoiceHasNoBillsOrCartForm;
use App\Nova\Metrics\DiscountedManualInvoiceAmountInUSD;
use App\Nova\Metrics\DiscountedManualInvoiceAmountPreviousMonthInUSD;
use App\Nova\Metrics\DiscountedManualInvoiceAmountThisMonthInUSD;
use App\Nova\Metrics\InvoiceBillsByServices;
use App\Nova\Metrics\PaidManualInvoiceAmountInUSD;
use App\Nova\Metrics\PaidManualInvoiceAmountPreviousMonthInUSD;
use App\Nova\Metrics\PaidManualInvoiceAmountThisMonthInUSD;
use App\Nova\Metrics\UnpaidManualInvoiceAmountInUSD;
use App\Nova\Metrics\UnpaidManualInvoiceAmountPreviousMonthInUSD;
use App\Nova\Metrics\UnpaidManualInvoiceAmountThisMonthInUSD;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Testing\Browser\Pages\Attach;

class ManualInvoice extends Resource
{
    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->where('type',InvoiceTypesEnum::Manual);
    }

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ManualInvoice>
     */
    public static $model = \App\Models\ManualInvoice::class;


    public static $group = 'Invoices';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'invoice_number';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'invoice_number', 'gateway_invoice_or_intent_id', 'title', 'reference_no', 'team.email', 'team.name'
    ];


    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make('Reference No')->onlyOnDetail(),

            Text::make('Invoice Number', function (){
                return '<a target="_blank" href="'.route('user.invoice.manual.preview-download', $this->invoice_number).'" class="link-default">'.$this->invoice_number.'</a>';
            })->readonly()->asHtml()->exceptOnForms(),

            Textarea::make('Note')->hideFromIndex()->nullable(),
            Text::make('Title')->nullable(),
            Text::make('Description')->hideFromIndex()->nullable(),
            Currency::make('Amount In USD', 'amount')->sortable(),

            Currency::make('Discount Amount In USD', 'discount')->nullable(),

            Select::make('Billed On Currency', 'currency')->options(
                BillingCurrency::asValueLabel()
            )->showOnIndex(false)->default(BillingCurrency::USD->value)->readonly(),

            Select::make('Payment Gateway')->options(
                PaymentGateway::asValueLabel()
            )->default(PaymentGateway::Cash->value)->hideFromIndex(),

            Currency::make('Amount Received', 'amount_received')
                ->default($this->resource?->amount ?: 0)
                ->currency(BillingCurrency::USD->value)->hideFromIndex()->nullable(),

            Currency::make('Refundable Amount', 'refundable_amount')
                ->currency(BillingCurrency::USD->value)->hideFromIndex()->nullable(),

            Currency::make('Refunded Amount', 'refunded_amount')
                ->currency(BillingCurrency::USD->value)->hideFromIndex()->nullable(),

            Select::make('Status')->options(
                BillingStatus::asValueLabel()
            )->default(BillingStatus::Unpaid),

            Select::make('Source')->options(
                InvoiceSourceEnum::asValueLabel()
            )->nullable()->default(InvoiceSourceEnum::SinglePurchase->value),

//            Select::make('Payment Gateway')->options(
//                PaymentGateway::asValueLabel()
//            )->default(PaymentGateway::Stripe),

            DateTime::make('Purchased At', 'created_at')->readonly(function () {
                return $this->resource->exists;
            })->sortable(),

            BelongsTo::make('Team')->searchable()->nullable(),

            BelongsTo::make('Manually Created By', 'manuallyCreatedUser', User::class)->exceptOnForms()->nullable(),

            BelongsTo::make('Manually Updated By', 'manuallyUpdatedUser', User::class)->exceptOnForms()->nullable(),

            HasMany::make('Bills')->nullable(),

            Text::make('Customer Email')->copyable()->sortable()->nullable(),

            Select::make('Type')->options(
                InvoiceTypesEnum::asValueLabel()
            ),

            Textarea::make('Additional Note After Pay')->nullable(),

            Text::make('Gateway Customer ID')->hideFromIndex()->copyable()->nullable(),

            Text::make('Gateway Invoice or Intent ID')->copyable()->hideFromIndex()->nullable(),

            Text::make('Gateway Payment Method ID')->copyable()->hideFromIndex()->nullable(),

            Code::make('Meta')->json()->nullable(),

            Code::make('Logs')->json()->nullable(),

            Text::make('Call Trace', 'call_trace')->placeholder('No trace found')->showOnIndex(false)->readonly(),

            Date::make('Due Date')->nullable(),

            DateTime::make('Updated At')->hideFromIndex()->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new InvoiceBillsByServices)->onlyOnDetail(),
            (new UnpaidManualInvoiceAmountPreviousMonthInUSD()),
            (new UnpaidManualInvoiceAmountInUSD()),
            (new UnpaidManualInvoiceAmountThisMonthInUSD()),
            (new PaidManualInvoiceAmountPreviousMonthInUSD()),
            (new PaidManualInvoiceAmountInUSD()),
            (new PaidManualInvoiceAmountThisMonthInUSD()),
            (new DiscountedManualInvoiceAmountPreviousMonthInUSD()),
            (new DiscountedManualInvoiceAmountInUSD()),
            (new DiscountedManualInvoiceAmountThisMonthInUSD()),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new Filters\InvoiceByStatus(),
            new InvoiceByMonth(),
            new InvoiceHasCartFormFilter(),
            new InvoiceHasBills(),
            new InvoiceHasNoBillsOrCartForm(),
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new Actions\ManualInvoiceAction())
            ->canSee(function ($request) {
                return $request->user()->isSuperAdmin();
            })
            ->canRun(function ($request, $model) {
                return $request->user()->isSuperAdmin();
            })
            ->confirmText('Are you sure you want to update the status of selected invoices?')
        ];
    }

    public static function authorizedToCreate(Request $request)
    {
        return $request->user() && ($request->user()->isAdmin() || $request->user()->isSupportLevel2());
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function authorizedToView(Request $request)
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public function authorizedToUpdate(Request $request)
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public function authorizedToDelete(Request $request)
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->isSupportLevel2();
    }
}
