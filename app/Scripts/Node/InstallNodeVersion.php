<?php

namespace App\Scripts\Node;

use App\Models\Server;
use App\Scripts\Script;

class InstallNodeVersion extends Script
{
    /**
     * Server we are dealing with
     * @var Server
     */
    private $server;

    /**
     * Version of Node.js to install
     *
     * @var string
     */
    private $nodeVersion;

    /** * @var bool */
    private mixed $setDefault;

    /**
     * @param  Server  $server
     * @param $nodeVersion
     * @param  bool  $setDefault
     */
    public function __construct(Server $server, $nodeVersion, bool $setDefault = false)
    {
        $this->server = $server;
        $this->nodeVersion = $nodeVersion;
        $this->setDefault = $setDefault;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Installing Node.js version {$this->nodeVersion} on {$this->server->name}";
    }

    function script()
    {
        return view('scripts.node.installVersion', [
            'server' => $this->server,
            'nodeVersion' => $this->nodeVersion,
            'setDefault' => $this->setDefault,
        ])->render();
    }

    function timeout()
    {
        return 300;
    }
}
