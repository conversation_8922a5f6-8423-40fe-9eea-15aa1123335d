<?php

namespace App\Scripts\Node;

use App\Models\Server;
use App\Scripts\Script;

class PullNodeVersions extends Script
{
    /**
     * Server we are dealing with
     * @var Server
     */
    private $server;

    /**
     * @param  Server  $server
     */
    public function __construct(Server $server)
    {
        $this->server = $server;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Pulling installed Node.js versions from {$this->server->name}";
    }

    function script()
    {
        return view('scripts.node.pullVersions', [
            'server' => $this->server,
        ])->render();
    }

    function timeout()
    {
        return 30;
    }
}
