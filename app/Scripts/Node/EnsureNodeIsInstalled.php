<?php

namespace App\Scripts\Node;

use App\Models\Server;
use App\Scripts\Script;

class EnsureNodeIsInstalled extends Script
{
    /**
     * Server we are dealing with
     * @var Server
     */
    private $server;

    /**
     * Version of Node.js to check
     *
     * @var string
     */
    private $nodeVersion;

    /**
     * @param  Server  $server
     * @param $nodeVersion
     */
    public function __construct(Server $server, $nodeVersion)
    {
        $this->server = $server;
        $this->nodeVersion = $nodeVersion;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Checking if Node.js version {$this->nodeVersion} is installed on {$this->server->name}";
    }

    function script()
    {
        return view('scripts.node.ensureInstalled', [
            'server' => $this->server,
            'nodeVersion' => $this->nodeVersion,
        ])->render();
    }

    function timeout()
    {
        return 30;
    }
}
