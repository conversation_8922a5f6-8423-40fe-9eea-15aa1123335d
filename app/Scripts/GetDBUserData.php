<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;

class GetDBUserData extends Script
{
    /**
     * @var Server $server
     * @var $user
     */
    private Server $server;

    private String $user;

    public function __construct(Server $server, String $user)
    {
        $this->server = $server;
        $this->user = $user;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Reading databases user details of ({$this->server->name})";
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return view('scripts.database.getDatabasesUserData', [
            'script' => $this,
            'server' => $this->server,
            'user' => $this->user
        ])->render();
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 30;
    }
}
