<?php

namespace App\Scripts\Monitoring;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class InstallSiteMonitoring extends Script
{
    public function __construct(public Server $server, public Site $site) {}

    public function name(): string
    {
        if ($this->site->isWordPress()) {
            return "Installing Monitoring inside WordPress Site ({$this->site->name} on {$this->server->name})";
        }
        return "Installing Monitoring inside Site ({$this->site->name} on {$this->server->name})";
    }

    public function script(): string
    {
        return view('scripts.monitoring.installSiteMonitoring', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 600;
    }
}
