<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class RepairPm2Process extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Reparing ".$this->site->type->getDisplayName()." PM2 Process for {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.node.repairPm2Process', [
            'site' => $this->site
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
