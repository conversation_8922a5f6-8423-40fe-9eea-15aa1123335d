<?php

namespace App\Scripts\Site;

use App\Models\CustomNginx;
use App\Models\Server;
use App\Models\Site;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use App\Scripts\Script;
use Illuminate\Support\Facades\Log;

class DeleteCustomNginxConfig extends Script
{
    public function __construct(public Site $site, public CustomNginx $customNginx)
    {
    }

    public function name(): string
    {
        return "Deleting Custom Nginx Config on {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.site.deleteCustomNginxConf', [
            'site' => $this->site,
            'customNginx' => $this->customNginx
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
