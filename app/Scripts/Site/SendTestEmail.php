<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class SendTestEmail extends Script
{
    public function __construct(public Site $site, public string $to, public string $body)
    {
    }

    public function name(): string
    {
        return 'Send Test Email to ' . $this->to.' from '.$this->site->name;
    }

    public function script(): string
    {
        return view('scripts.site.sendTestEmail', [
            'site' => $this->site,
            'to' => $this->to,
            'subject' => 'Test Email from ' . $this->site->name,
            'body' => $this->body,
        ])->render();
    }
}
