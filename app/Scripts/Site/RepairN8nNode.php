<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class RepairN8nNode extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Repairing Node Installation for {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.node.installVersion', [
            'site' => $this->site,
            'server' => $this->site->server,
            'nodeVersion' => $this->site->server->nodeVersion,
            'setDefault' => true,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 300;
    }
}
