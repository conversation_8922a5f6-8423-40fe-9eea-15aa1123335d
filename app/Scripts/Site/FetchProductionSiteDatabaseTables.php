<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class FetchProductionSiteDatabaseTables extends Script
{
    public function __construct(public Site $stagingSite)
    {
    }

    public function name(): string
    {
        return "Fetch database list from production site({$this->stagingSite->productionSite->name})";
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        return view('scripts.site.fetchProductionSiteDatabaseTables', [
            'productionSite' => $this->stagingSite,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
