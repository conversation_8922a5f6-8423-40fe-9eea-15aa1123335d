<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class InstallRedisObjectCache extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Installing Redis Object Cache";
    }

    public function script(): string
    {
        return view('scripts.site.cache.installRedisObjectCache', [
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 120;
    }
}
