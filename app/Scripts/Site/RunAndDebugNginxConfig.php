<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use App\Scripts\Script;
use Illuminate\Support\Facades\Log;

class RunAndDebugNginxConfig extends Script
{
    public function __construct(public Site $site, public string $type, public string $file, public string $content, public bool $runAndDebug = false)
    {
    }

    public function name(): string
    {
        return "Run and Debug Nginx Config on {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.site.runAndDebugCustomNginxConf', [
            'site' => $this->site,
            'type' => $this->type,
            'file' => $this->file,
            'content' => $this->content,
            'runAndDebug' => $this->runAndDebug
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
