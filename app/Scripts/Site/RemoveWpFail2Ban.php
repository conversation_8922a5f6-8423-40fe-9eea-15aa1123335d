<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;
use Throwable;

class RemoveWpFail2Ban extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Remove WP Fail2Ban {$this->site->name}";
    }

    /**
     * @throws Throwable
     */
    public function script(): string
    {
        return view('scripts.security.wp-fail2ban.uninstall', [
            'site' => $this->site
        ])->render();
    }

    public function timeout(): ?int
    {
        return 300;
    }
}
