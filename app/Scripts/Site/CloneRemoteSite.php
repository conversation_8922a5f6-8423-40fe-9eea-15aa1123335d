<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class CloneRemoteSite extends Script
{
    public function __construct(public Site $sourceSite, public Site $destinationSite)
    {
    }

    public function name(): string
    {
        return "Cloning remote Site";
    }

    public function script(): string
    {
        return view('scripts.site.cloneRemoteSite', [
            'sourceSite' => $this->sourceSite,
            'destinationSite' => $this->destinationSite,
            'privateKeyPath' => $this->destinationSite->siteClone->getPrivateKeyFilePath(),
            'destinationServer' => $this->destinationSite->server,
            'sourceServer' => $this->sourceSite->server,
        ])->render();
    }

    public function timeout(): ?int
    {
        return -1;
    }
}
