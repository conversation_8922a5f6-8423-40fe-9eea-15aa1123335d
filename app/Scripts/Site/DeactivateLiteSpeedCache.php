<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class DeactivateLiteSpeedCache extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Deactivating LiteSpeed Cache";
    }

    public function script(): string
    {
        return view('scripts.site.cache.disableLiteSpeedCache', [
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 120;
    }
}
