<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class WpDebug<PERSON>hecker extends Script
{
    public function __construct(public Server $server, public Site $site)
    {
    }

    public function name(): string
    {
        return "Checking WP Debug ({$this->site->name} on {$this->server->name})";
    }

    public function script(): string
    {
        return view('scripts.site.wpDebugChecker', [
            'script' => $this,
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 15;
    }
}
