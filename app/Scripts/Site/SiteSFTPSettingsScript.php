<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class SiteSFTPSettingsScript extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return $this->site->isDisable() ? "Disable SFTP" : "Enable SFTP";
    }

    public function script(): string
    {
        return view('scripts.site.SiteSFTPSettings', [
            'site' => $this->site,
        ])->render();
    }
}
