<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Models\StorageProvider;
use App\Scripts\Script;

class RestoreIncrementalBackup extends Script
{
    public function __construct(
        public Site $site,
        public Server $server,
        public ?StorageProvider $storageProvider,
        public ?string $file,
        public ?string $sqlFile,
        public ?string $backupDomain,
        public bool $is_local,
        public ?string $filePath=null,
        public ?string $restoreTime=null)
    {

    }

    public function name(): string
    {
        return "Restoring Backup for {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.site.backup.restoreIncrementalScript', [
            'site' => $this->site,
            'storageProvider' => $this->storageProvider,
            'server' => $this->server,
            'file' => $this->file,
            'sqlFile' => $this->sqlFile,
            'is_local' => $this->is_local,
            'backupDomain' => $this->backupDomain,
            'filePath' => $this->filePath,
            'restoreTime' => $this->restoreTime,
        ])->render();
    }

    public function timeout(): ?int
    {
        return -1;
    }
}
