<?php

namespace App\Scripts\Site;

use App\Models\BackupSetting;
use App\Models\Server;
use App\Models\Site;
use App\Models\StorageProvider;
use App\Scripts\Script;
use Illuminate\Database\Eloquent\Model;

class UpdateSiteUrl extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Update Site URL";
    }

    public function script(): string
    {
        return view('scripts.wordpress.updateSiteUrl', [
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 120;
    }
}
