<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Models\StorageProvider;
use App\Scripts\Script;

class RestoreBackup extends Script
{
    public function __construct(public Site $site, public Server $server, public ?StorageProvider $storageProvider, public ?string $file, public ?string $sqlFile, public ?int $required_size,public ?string $backupDomain, public ?string $s3Profile, public bool $is_local = false,public ?string $filePath=null)
    {
    }

    public function name(): string
    {
        return "Restoring Backup for {$this->site->name}";
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        if ($this->storageProvider?->isGDrive()){
            return view('scripts.site.backup.restore.gdriveRestoreScript', [
                'site' => $this->site,
                'storageProvider' => $this->storageProvider,
                'server' => $this->server,
                'file' => $this->file,
                'sqlFile' => $this->sqlFile,
                'backupDomain' => $this->backupDomain,
                'required_size' => $this->required_size,
                'filePath' => $this->filePath,
                'fileID' => $this->file ? $this->storageProvider->getFileId($this->file) : null,
                'sqlFileID' => $this->sqlFile ? $this->storageProvider->getFileId($this->sqlFile) : null
            ])->render();
        }
        return view('scripts.site.backup.restore.restoreScript', [
            'site' => $this->site,
            'storageProvider' => $this->storageProvider,
            'server' => $this->server,
            'file' => $this->file,
            'sqlFile' => $this->sqlFile,
            'is_local' => $this->is_local,
            'backupDomain' => $this->backupDomain,
            's3Profile' => $this->s3Profile,
            'required_size' => $this->required_size,
            'filePath' => $this->filePath
        ])->render();
    }

    public function timeout(): ?int
    {
        return -1;
    }
}
