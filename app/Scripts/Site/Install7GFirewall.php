<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class Install7GFirewall extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Install 7G Firewall on {$this->site->name}";
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        return view('scripts.wordpress.7g-firewall.install-7g-firewall', [
            'site' => $this->site
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
