<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use App\Scripts\Script;
use Illuminate\Support\Facades\Log;

class GitConnectionCheck extends Script
{
    public function __construct(public Site $site, public $sshKeyPair, public $repository, public $branch, public ?bool $tempPath = true)
    {
    }

    public function name(): string
    {
        return "Git Connection Check for {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.site.checkGitConnection', [
            'site' => $this->site,
            'sshKeyPair' => $this->sshKeyPair,
            'repository' => $this->repository,
            'branch' => $this->branch,
            'tempPath' => $this->tempPath,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
