<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class InstallLiteSpeedCache extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Installing LiteSpeed Cache";
    }

    public function script(): string
    {
        return view('scripts.site.cache.installLiteSpeedCache', [
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 120;
    }
}
