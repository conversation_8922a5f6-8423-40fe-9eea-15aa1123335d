<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class DeactivateRedisObjectCacheProScript extends Script
{
    public function __construct(public Site $site,public string $redis_user)
    {
    }

    public function name(): string
    {
        return "Deactivate Redis Object Cache Pro for {$this->site->name}";
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        return view('scripts.site.deactivateRedisObjectCachePro', [
            'site' => $this->site,
            'redis_user' => $this->redis_user,
        ])->render();
    }


}
