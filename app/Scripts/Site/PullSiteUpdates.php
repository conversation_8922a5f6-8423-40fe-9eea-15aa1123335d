<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class PullSiteUpdates extends Script
{
    public function __construct(public Server $server, public Site $site) {}

    public function name(): string
    {
        return "Pulling Updates inside ({$this->site->name} on {$this->server->name})";
    }

    public function script(): string
    {
        return view('scripts.monitoring.site.pullMonitoringUpdates', [
            'script' => $this,
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 600;
    }
}
