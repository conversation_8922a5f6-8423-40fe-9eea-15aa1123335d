<?php

namespace App\Scripts;

class GetCurrentDirectory extends <PERSON>ript
{
    /**
     * The displayable name of the script.
     *
     * @var string
     */
    public $name = 'Testing Connection';

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return 'pwd';
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 10;
    }
}
