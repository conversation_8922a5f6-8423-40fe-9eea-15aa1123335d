<?php

namespace App\Scripts;


use App\Models\Server;
use App\Models\Site;

class PurgeObjectCachePro extends Script
{

     public function __construct(public Server $server, public Site $site) {}

    public function name(): string
    {
        return 'Purge Object Cache Pro';
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        return view('scripts.site.cache.purgeRedisObjectCachePro', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
        ])->render();
    }

    function timeout(): int
    {
        return 30;
    }
}
