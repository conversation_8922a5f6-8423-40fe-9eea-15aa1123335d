<?php

namespace App\Scripts\ServerMigration;

use App\Models\Server;
use App\Models\ServerMigration;
use App\Models\Site;
use App\Scripts\Script;

class CopyCpanelSite extends Script
{
    public function __construct(public Server $server, public Site $site, public string $directory)
    {
    }

    public function name(): string
    {
        return "Copying site directory for {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.server-migration.copyCpanelSite', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
            'directory' => $this->directory,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 600;
    }
}
