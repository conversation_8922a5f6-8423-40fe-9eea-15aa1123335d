<?php

namespace App\Scripts\ServerMigration;

use App\Models\Server;
use App\Models\ServerMigration;
use App\Models\Site;
use App\Scripts\Script;

class FetchCpanelBackup extends Script
{
    public function __construct(public Server $server, public Site $site, public ServerMigration $serverMigration)
    {
    }

    public function name(): string
    {
        return "Fetching Cpanel Backup for {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.server-migration.fetchCpanelBackup', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
            'serverMigration' => $this->serverMigration,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 3540;
    }
}
