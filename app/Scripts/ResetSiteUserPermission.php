<?php

namespace App\Scripts;

use App\Models\Site;
use Throwable;

class ResetSiteUserPermission extends Script
{
    public $name = 'Reset Site User Permission';

    function __construct(public Site $site)
    {

    }

    public function script(): string
    {
        return view('scripts.site.resetSiteDirectoryPermission', [
            'site' => $this->site
        ])->render();
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 20;
    }
}
