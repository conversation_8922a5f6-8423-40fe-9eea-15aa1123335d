<?php

namespace App\Scripts;

use App\Models\Site;
use Exception;

class UpdateSiteEnv extends Script
{
    public $name = 'Update environment of a site';

    private $site;

    private $envBody;

    public function __construct(Site $site, string $envBody)
    {
        $this->site = $site;
        $this->envBody = $envBody;
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     * @throws Exception
     */
    public function script(): string
    {
        return 'cat > '.$this->site->manager()->getEnvironmentFilePath().' << \'EOF\'
'.$this->envBody.'
EOF';

    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 10;
    }
}
