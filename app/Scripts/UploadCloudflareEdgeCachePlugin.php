<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class UploadCloudflareEdgeCachePlugin extends Script
{
    const XCLOUD_CLOUDFLARE_HELPER_VERSION = '1.0.0';

    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Updating Cloudflare Edge Cache Helper ({$this->site->name} on {$this->site->server->name})";
    }

    public function script(): string
    {
        return view('scripts.site.cloudflare-edge-cache.uploadScript', [
            'script' => $this,
            'server' => $this->site->server,
            'site' => $this->site,
            'pluginBody' => $this->getPluginBody(),
        ])->render();
    }

    /**
     * @return string
     * @throws \Throwable
     */
    private function getPluginBody(): string
    {
        $body = view('scripts.site.cloudflare-edge-cache.cloudflare-edge-cache-helper', [
            'site' => $this->site,
            'server' => $this->site->server,
            'team' => $this->site->server->team,
            'whitelabel' => $this->site->server->whiteLabel(),
            'XCLOUD_URL' => callback_url('/api/site/cloudflare-edge-cache/purge'),
            'XCLOUD_CLOUDFLARE_HELPER_VERSION' => self::XCLOUD_CLOUDFLARE_HELPER_VERSION,
        ])->render();

        $body = '<?php ' . $body;

        return $body;
    }

    public function timeout(): ?int
    {
        return 30;
    }
}
