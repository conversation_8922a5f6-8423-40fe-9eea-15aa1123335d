<?php

namespace App\Scripts\Server;

use App\Models\Server;
use App\Scripts\Script;
use Str;

class BanIpAddress extends Script
{
    public function __construct(public Server $server, public string $ipAddresses)
    {
    }
    public function name(): string
    {
        return "Ban IP Addresses " . Str::limit($this->ipAddresses, 100) . " on Server {$this->server->name}";
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        return view('scripts.server.banIpAddress',[
            'server' => $this->server,
            'ipAddresses' => $this->ipAddresses,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
