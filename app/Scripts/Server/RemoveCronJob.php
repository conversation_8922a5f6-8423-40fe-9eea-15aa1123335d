<?php

namespace App\Scripts\Server;

use App\Models\CronJob;
use App\Models\Server;
use App\Scripts\Script;

class RemoveCronJob extends Script
{
    public function __construct(public Server $server, public CronJob $cronJob)
    {
    }

    public function name(): string
    {
        return "Remove Cron Job on {$this->server->name}";
    }

    public function script(): string
    {
        return view('scripts.server.removeCronJob', [
            'server' => $this->server,
            'cronJob' => $this->cronJob,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
