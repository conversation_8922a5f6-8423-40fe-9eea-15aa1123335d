<?php

namespace App\Scripts\Server;

use App\Models\Server;
use App\Scripts\Script;

class InstallHtaccessWatcherIntoOlsServer extends Script
{
    public function __construct(public Server $server)
    {
    }

    public function name(): string
    {
        return "Installing .htaccess watcher";
    }

    public function script(): string
    {
        return file_get_contents(resource_path('views/scripts/openlitespeed/htaccessWatcher.bash'));
    }

    public function timeout(): ?int
    {
        return 120;
    }
}
