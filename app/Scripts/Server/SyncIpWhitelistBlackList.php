<?php

namespace App\Scripts\Server;

use App\Enums\IPAddressType;
use App\Models\IpAddress;
use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;
use Throwable;

class SyncIpWhitelistBlackList extends Script
{
    public function __construct(public Server $server)
    {
    }

    public function name(): string
    {
        return "Sync IP Whitelist/Blacklist {$this->server->name}";
    }

    /**
     * @throws Throwable
     */
    public function script(): string
    {
        $siteIds = Site::where("server_id", $this->server->id)->pluck("id");

        return view('scripts.security.ip-management', [
            'server' => $this->server,
            'whitelist' => IpAddress::where("ipable_type", Site::class)->whereIn("ipable_id", $siteIds)->where("type", IPAddressType::WHITELIST)->pluck('ip_address'),
            'blacklist' => IpAddress::where("ipable_type", Site::class)->whereIn("ipable_id", $siteIds)->where("type", IPAddressType::BLACKLIST)->pluck('ip_address'),
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
