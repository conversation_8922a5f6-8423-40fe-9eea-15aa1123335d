<?php

namespace App\Scripts\Server;

use App\Models\CronJob;
use App\Models\Server;
use App\Scripts\Script;

class RemoveMagicLogin extends Script
{
    public function __construct(public Server $server)
    {
    }

    public function name(): string
    {
        return "Remove Magic Login On Server {$this->server->name}";
    }

    public function script(): string
    {
        return view('scripts.server.removeMaginLogin', [
            'server' => $this->server,
            'sites' => $this->server->sites()->pluck('name')->toArray(),
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
