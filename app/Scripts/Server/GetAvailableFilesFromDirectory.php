<?php

namespace App\Scripts\Server;

use App\Scripts\Script;

class GetAvailableFilesFromDirectory extends Script
{
    public function __construct(public string $path)
    {
    }

    public function name(): string
    {
        return "Get available files in {$this->path}";
    }

    public function script(): string
    {
        return view('scripts.server.getAvailableFiles', [
            'directory' => $this->path,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 120;
    }
}
