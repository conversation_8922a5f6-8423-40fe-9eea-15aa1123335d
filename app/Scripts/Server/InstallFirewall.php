<?php

namespace App\Scripts\Server;

use App\Models\FirewallRule;
use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class InstallFirewall extends Script
{
    public function __construct(public Server $server, public FirewallRule $firewallRule)
    {

    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Installing firewall rule #{$this->firewallRule->name} on Server#{$this->server->name}";
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return view('scripts.server.firewallRule', [
            'script' => $this,
            'server' => $this->server,
            'firewallRule' => $this->firewallRule,
        ])->render();
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 15;
    }
}
