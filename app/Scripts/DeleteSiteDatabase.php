<?php

namespace App\Scripts;


use App\Models\Server;
use App\Models\Site;

class DeleteSiteDatabase extends Script
{

    public function __construct(
        public Server $server,
        public Site $site
    ) {
    }

    public function name(): string
    {
        return "Deleting Site Database for ({$this->site->name} on {$this->server->name})";
    }

    public function script(): string
    {
        return view('scripts.site.deleteSite.deleteSiteDatabase', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site
        ])->render();
    }

    function timeout()
    {
        return 300;
    }
}
