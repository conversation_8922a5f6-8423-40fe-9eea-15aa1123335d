<?php

namespace App\Scripts;


use App\Models\SudoUser;

class UpdateSudoUser extends Script
{
    public function __construct(public SudoUser $sudoUser)
    {
    }

    public function name()
    {
        return "Updating '{$this->sudoUser->sudo_user}' sudo user";
    }

    public function script()
    {
        return view('scripts.provisionable.updateSudoUser', [
            'sudoUser' => $this->sudoUser,
        ])->render();
    }

    public function timeout()
    {
        return 10;
    }
}
