<?php

namespace App\Scripts;

class StartService extends <PERSON>ript
{

    /**
     * The displayable name of the script.
     *
     * @var string
     */
    public $name = 'Enabling a service';

    /** * @var */
    private $service;

    public function __construct($service)
    {
        $this->service = $service;
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return 'service '.$this->service.' start';
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 10;
    }
}
