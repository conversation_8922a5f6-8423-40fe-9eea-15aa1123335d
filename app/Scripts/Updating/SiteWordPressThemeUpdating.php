<?php

namespace App\Scripts\Updating;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class SiteWordPressThemeUpdating extends Script
{
    public function __construct(public Server $server, public Site $site,public array $themes) {}

    public function name(): string
    {
        $themes = count($this->themes);

        return "Updating WordPress Theme ({$themes}) inside WordPress Site ({$this->site->name} on {$this->server->name})";
    }

    public function script(): string
    {
        return view('scripts.site.updating.siteThemeUpdating', [
            'script' => $this,
            'site' => $this->site,
            'server' => $this->server,
            'themes' => $this->themes
        ])->render();
    }

    public function timeout(): ?int
    {
        return 600;
    }
}
