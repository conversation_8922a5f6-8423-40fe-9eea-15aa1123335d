<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;

class DeleteDatabaseUser extends Script
{
    private Server $server;
    private string $databaseUser;

    public function __construct(Server $server, string $databaseUser)
    {
        $this->server = $server;
        $this->databaseUser = $databaseUser;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Deleting database user: {$this->databaseUser}";
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return view('scripts.database.deleteDatabaseUser', [
            'script' => $this,
            'server' => $this->server,
            'databaseUser' => $this->databaseUser
        ])->render();
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 300;
    }
}
