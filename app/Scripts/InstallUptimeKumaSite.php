<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;
use App\Models\Task;

class InstallUptimeKumaSite extends Script
{
    public function __construct(public Server $server, public Site $site) {}

    public function name(): string
    {
        return "Installing Uptime Kuma";
    }

    public function script(): string
    {
        return view('scripts.oneclick.uptime-kuma.createSite', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 600;
    }
}
