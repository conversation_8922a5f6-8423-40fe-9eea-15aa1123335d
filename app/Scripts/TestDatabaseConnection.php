<?php

namespace App\Scripts;

class TestDatabaseConnection extends Script
{
    public function __construct(
        private string $host,
        private string $port,
        private string $database,
        private string $username,
        private string $password,
    ) {
    }

    public function name(): string
    {
        return 'Testing database connection';
    }

    public function script(): string
    {
        return view('scripts.database.testDatabaseConnection', [
            'script' => $this,
            'host' => $this->host,
            'port' => $this->port,
            'database' => $this->database,
            'username' => $this->username,
            'password' => $this->password,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 15;
    }
}
