<?php

namespace App\Scripts;

class StopService extends <PERSON>ript
{
    /**
     * The user that the script should be run as.
     *
     * @var string
     */
    public string $sshAs = 'root';

    /**
     * The displayable name of the script.
     *
     * @var string
     */
    public $name = 'Disabling a service';

    /** * @var */
    private $service;

    public function __construct($service)
    {
        $this->service = $service;
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return 'service '.$this->service.' stop';
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 10;
    }
}
