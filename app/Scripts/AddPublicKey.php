<?php

namespace App\Scripts;

class AddP<PERSON>lic<PERSON>ey extends Script
{
    public $name = 'Add Public Key to a server for root user and enable root login if not enabled';

    /** * @var string */
    private $publicKey;
    private $enableRootLogin = false;

    public function __construct(string $publicKey, bool $enableRootLogin = false)
    {
        $this->publicKey = rtrim($publicKey);
        $this->enableRootLogin = $enableRootLogin;
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script()
    {
        $script = [];

        if (!empty($this->publicKey)) {
            $script[] = "sudo mkdir -p /root/.ssh/";
            $script[] = "sudo grep -q -F '{$this->publicKey}' /root/.ssh/authorized_keys || echo '{$this->publicKey}' | sudo tee -a /root/.ssh/authorized_keys";
        }

        if ($this->enableRootLogin) {
            // Modify sshd_config to set PermitRootLogin to prohibit-password
            $script[] = "sudo sed -i 's/^#*\\s*PermitRootLogin.*/PermitRootLogin prohibit-password/' /etc/ssh/sshd_config";

            // Remove the restrictive line from authorized_keys
            $script[] = "sudo sed -i '0,/ssh-rsa/s/.*ssh-rsa/ssh-rsa/' /root/.ssh/authorized_keys";

            // Restart the SSH service to apply changes
            $script[] = "sudo systemctl restart sshd";
        }

        return implode(" && ", $script);
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout()
    {
        return 5;
    }
}
