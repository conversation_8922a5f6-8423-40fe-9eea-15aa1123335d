<?php

namespace App\Scripts;

use App\Models\Site;
use Exception;

class ReadSiteNginxConf extends Script
{
    private Site $site;

    public string $name = 'Reading Nginx Conf File of a site';


    public function __construct(Site $site)
    {
        $this->site = $site;
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     * @throws Exception
     */
    public function script(): string
    {
        return 'cat '.$this->site->manager()->getNginxConfPath();
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 10;
    }
}
