<?php

namespace App\Scripts;

use App\Models\Server;
use App\Services\Provisioning\ServerProvisioning;

class ProvisionxCloudServer extends Script
{
    public $server;

    public function __construct(Server $server)
    {
        $this->server = $server;
    }

    public function name(): string
    {
        if ($this->server->isXCloud()) {
            return "Provisioning xCloud Server ({$this->server->name})";
        }
        return "Provisioning {$this->server->team->get_brand_name} Server ({$this->server->name})";
    }

    public function script(): string
    {
        return view('scripts.provisionable.provisionxCloud', [
            'script' => $this,
            'server' => $this->server,
            'customScripts' => [],
        ])->render();
    }

    function timeout()
    {
        return -1;
    }
}
