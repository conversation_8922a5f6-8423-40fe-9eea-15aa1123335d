<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;

class UpdateAdminerCallback extends Script
{

    public function __construct(public Server $server, public Site $site)
    {
    }

    public function name(): string
    {
        return "Configure Adminer for ({$this->site->name} on {$this->server->name})";
    }

    public function script(): string
    {
        return view('scripts.site.adminer.configure', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
            'callback_url' => callback_url('/api/site/xcloud-adminer')
        ])->render();
    }


    public function timeout(): ?int
    {
        return 120;
    }
}
