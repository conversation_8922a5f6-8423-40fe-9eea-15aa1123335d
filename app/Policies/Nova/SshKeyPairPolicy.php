<?php

namespace App\Policies\Nova;

use App\Models\SshKeyPair;
use App\Models\SudoUser;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SshKeyPairPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isAdmin() || $user->isSupportLevel2();
    }

    public function view(User $user, SshKeyPair $sshKeyPair): bool
    {
        return $user->isAdmin() || $user->isSupportLevel2();
    }
}
