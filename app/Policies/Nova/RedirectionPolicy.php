<?php

namespace App\Policies\Nova;

use App\Models\Redirection;
use App\Models\SudoUser;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class RedirectionPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isAdmin() || $user->isSupportLevel2();
    }

    public function view(User $user, Redirection $redirection): bool
    {
        return $user->isAdmin() || $user->isSupportLevel2();
    }
}
