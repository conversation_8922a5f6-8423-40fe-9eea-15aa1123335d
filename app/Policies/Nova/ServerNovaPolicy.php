<?php

namespace App\Policies\Nova;

use App\Models\Server;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ServerNovaPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isAdmin() || $user->hasAnySupportRole();
    }

    public function view(User $user, Server $server): bool
    {
        return $user->isAdmin() || $user->hasAnySupportRole();
    }

    public function update(User $user, Server $server): bool
    {
        return $user->isAdmin();
    }
}
