<?php

namespace App\Policies\Nova;

use App\Models\PhpVersion;
use App\Models\SudoUser;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PhpVersionPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user): bool
    {
        return $user->isAdmin() || $user->isSupportLevel2();
    }

    public function view(User $user, PhpVersion $phpVersion): bool
    {
        return $user->isAdmin() || $user->isSupportLevel2();
    }
}
