<?php

namespace App\Policies;

use App\Models\AddonStorageProvider;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AddonStorageProviderPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any addon storage providers.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasTeamPermission($user->currentTeam, 'account:manage-integrations');
    }

    /**
     * Determine whether the user can view the addon storage provider.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AddonStorageProvider  $addonStorageProvider
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, AddonStorageProvider $addonStorageProvider)
    {
        return $user->hasTeamPermission($user->currentTeam, 'account:manage-integrations') &&
               $user->belongsToTeam($addonStorageProvider->team);
    }

    /**
     * Determine whether the user can create addon storage providers.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasTeamPermission($user->currentTeam, 'account:manage-integrations');
    }

    /**
     * Determine whether the user can update the addon storage provider.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AddonStorageProvider  $addonStorageProvider
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, AddonStorageProvider $addonStorageProvider)
    {
        return $user->hasTeamPermission($user->currentTeam, 'account:manage-integrations') &&
               $user->belongsToTeam($addonStorageProvider->team);
    }

    /**
     * Determine whether the user can delete the addon storage provider.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AddonStorageProvider  $addonStorageProvider
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, AddonStorageProvider $addonStorageProvider)
    {
        return $user->hasTeamPermission($user->currentTeam, 'account:manage-integrations') &&
               $user->belongsToTeam($addonStorageProvider->team);
    }

    /**
     * Determine whether the user can restore the addon storage provider.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AddonStorageProvider  $addonStorageProvider
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, AddonStorageProvider $addonStorageProvider)
    {
        return $user->hasTeamPermission($user->currentTeam, 'account:manage-integrations') &&
               $user->belongsToTeam($addonStorageProvider->team);
    }

    /**
     * Determine whether the user can permanently delete the addon storage provider.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\AddonStorageProvider  $addonStorageProvider
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, AddonStorageProvider $addonStorageProvider)
    {
        return $user->hasTeamPermission($user->currentTeam, 'account:manage-integrations') &&
               $user->belongsToTeam($addonStorageProvider->team);
    }
}
