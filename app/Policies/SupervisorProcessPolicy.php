<?php

namespace App\Policies;

use App\Models\SupervisorProcess;
use App\Models\Server;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SupervisorProcessPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can manage the supervisor process.
     *
     * @param User $user
     * @param SupervisorProcess $supervisorProcess
     * @return bool
     */
    public function manage(User $user, SupervisorProcess $supervisorProcess): bool
    {
        return $user->canAccessServer($supervisorProcess->server) && 
               $user->can('manageSupervisorProcess', $supervisorProcess->server->team);
    }
}
