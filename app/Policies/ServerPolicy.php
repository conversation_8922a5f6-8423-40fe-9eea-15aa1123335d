<?php

namespace App\Policies;

use App\Models\Membership;
use App\Models\Server;
use App\Models\Team;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;

class ServerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): Response|bool
    {
        return $user->hasAnyRole($user->currentTeam, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Server $server
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Server $server): Response|bool
    {
        return $user->isAdmin() || $user->hasAnyRole($server->team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->canAccessServer($server);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): Response|bool
    {
       return $user->can('addServer', $user->currentTeam);
    }

    public function retTryPayment(User $user, Server $server): Response|bool
    {
       return $user->can('addServer', $user->currentTeam) && $server->isPaymentFailed();
    }

    public function progress(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) &&  $user->hasAnyRole($user->currentTeam, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]);

    }

    public function createFreeServer(User $user): bool
    {
        return $user->can('addFreeServer', $user->currentTeam);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Server $server
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->hasAnyRole($user->currentTeam, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($server->team, 'server:delete');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Server $server
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server, allowSupport: false) && $user->can('deleteServer', $server->team);
    }


    /**
     * Determine whether the user can add site the server.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Server $server
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function addSite(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->can('addSite', $server->team);
    }

    public function cloneServer(User $user, Server $server): Response|bool
    {
        //cloning server is actually creating current sites on another server
        return $user->canAccessServer($server,allowSupport: false) && $user->can('addSite', $server->team);
    }

    public function viewSite(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->can('viewSite', $server->team);
    }

    public function reStartServer(User $user, Server $server): bool
    {
        return $user->canAccessServer($server, allowSupport:  $user->isSupportLevel2()) && $user->can('manageServerService', $server->team,'');
    }

    public function reStartNginx(User $user, Server $server): bool
    {
        return $user->canAccessServer($server,allowSupport: $user->isSupportLevel2()) && $user->can('manageServerService', $server->team);
    }

    public function reStartMySql(User $user, Server $server): bool
    {
        return $user->canAccessServer($server,allowSupport:  $user->isSupportLevel2()) && $user->can('manageServerService', $server->team);
    }
    public function archive(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('archiveServer', $server->team);
    }

    public function createDB(User $user, Server $server): bool
    {
        return $user->canAccessServer($server,allowSupport: $user->isSupportLevel2()) && $user->can('createDB', $server->team);
    }

    public function viewDB(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('createDB', $server->team);
    }

    public function deleteDB(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('deleteDB', $server->team);
    }

    public function accessDB(User $user, Server $server): bool
    {
        return $this->createDB($user, $server) || $this->deleteDB($user, $server);
    }


    public function access(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('serverAccess', $server->team);
    }

    public function createEditSudoUser(User $user, Server $server): bool
    {
        return $user->canAccessServer($server,allowSupport: $user->isSupportLevel2()) && $user->can('serverAccess', $server->team);
    }
    public function mangeCronJob(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('mangeCronJob', $server->team);
    }
    public function createCronJob(User $user, Server $server): bool
    {
        return $user->canAccessServer($server,allowSupport: false) && $user->can('mangeCronJob', $server->team);
    }

    public function manageSupervisorProcess(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageSupervisorProcess', $server->team);
    }

    public function createSupervisorProcess(User $user, Server $server): bool
    {
        return $user->canAccessServer($server,allowSupport: false) && $user->can('manageSupervisorProcess', $server->team);
    }

    public function settings(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageServerService', $server->team);
    }

    public function manageFirewall(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageFirewallRule', $server->team);
    }
    public function addFirewallRule(User $user, Server $server): bool
    {
        return $user->canAccessServer($server,allowSupport: $user->isSupportLevel2()) && $user->can('manageFirewallRule', $server->team);
    }

    public function monitors(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageServerService', $server->team);
    }

    public function service(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageServerService', $server->team);
    }

    public function managePhp(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('managePhp', $server->team);
    }

    public function manageNode(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('managePhp', $server->team);
    }
    public function manageLogs(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageLogs', $server->team);
    }
    public function clearLogs(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('clearServerLogs', $server->team);
    }
    public function events(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('serverEvents', $server->team);
    }
    /**
     * Determine whether the user can restore the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Server $server
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->can('deleteServer', $server->team) && $user->currentTeam->servers()->where(['id' => $server->id])->onlyTrashed()->exists();
    }

    public function resize(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->can('resizeServer', $server->team) && !isWhiteLabel();
    }

    public function manageProviderBackup(User $user, Server $server): Response|bool
    {
//        check if user is allowed to enable or disable backup settings on provider end of the server
        return $user->canAccessServer($server) && $user->can('manageProviderServerBackup', $server->team) && $server->cloudProvider?->isProviderBackupManageable();
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Server $server
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->can('deleteServer', $server->team) && $user->currentTeam->servers()->where(['id' => $server->id])->onlyTrashed()->exists();
    }

    public function reStartService(User $user, $server,$service): bool
    {
        return $user->can('manageServerService', $server->team);
    }

    public function customCommandRunner(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->hasAnyRole($user->currentTeam, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN],) && $user->hasTeamPermission($server->team, 'server:custom-command-runner');
    }

    public function vulnerabilitySettings(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->can('vulnerabilitySettings', $server->team);
    }
    public function securityUpdate(User $user, Server $server): Response|bool
    {
        return $user->canAccessServer($server) && $user->can('securityUpdate', $server->team);
    }

    public function startServer(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageServerActions', $server->team);
    }

    public function stopServer(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageServerActions', $server->team);
    }

    public function rebootServer(User $user, Server $server): bool
    {
        return $user->canAccessServer($server) && $user->can('manageServerActions', $server->team);
    }

}
