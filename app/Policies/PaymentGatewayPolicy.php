<?php

namespace App\Policies;

use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PaymentGatewayPolicy
{
    use HandlesAuthorization;
    public function addPaymentMethod(User $user): bool
    {
        // user can add only his payment id
        return $user->current_team_id == auth()->user()->current_team_id;
    }

    public function update(User $user, PaymentMethod $paymentMethod): bool
    {
        return $user->current_team_id == $paymentMethod->team_id;
    }

    public function delete(User $user, PaymentMethod $paymentMethod): bool
    {
        return $user->current_team_id == $paymentMethod->team_id;
    }

    public function canUse(User $user, PaymentMethod $paymentMethod): bool
    {
        return $user->current_team_id == $paymentMethod->team_id;
    }
}
