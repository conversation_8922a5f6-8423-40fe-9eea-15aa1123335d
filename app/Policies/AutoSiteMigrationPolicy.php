<?php

namespace App\Policies;

use App\Models\AutoSiteMigration;
use App\Models\SiteBackupRestoreMigration;
use App\Models\SiteMigration;
use App\Models\User;
use App\Policies\helpers\SiteMigrationPolicyHelper;
use Illuminate\Auth\Access\HandlesAuthorization;

class AutoSiteMigrationPolicy extends SiteMigrationPolicyHelper
{
    use HandlesAuthorization;

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function hasAccessToModifyDestination(User $user, SiteMigration $siteMigration): bool
    {
        return $siteMigration->team_id == $user->current_team_id && $user->can('addSite', $siteMigration->server->team);
    }

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function canSetupDomain(User $user, SiteMigration $siteMigration): bool
    {
        return $siteMigration->team_id == $user->current_team_id;
    }

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function canSetupSettings(User $user, SiteMigration $siteMigration): bool
    {
        return $this->haveFilled([
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS
            ], $siteMigration->form) &&
            ($siteMigration->team_id == $user->current_team_id);
    }

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function canUsePlugin(User $user, SiteMigration $siteMigration): bool
    {
        return $this->haveFilled([
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS
            ], $siteMigration->form) &&
            ($siteMigration->team_id == $user->current_team_id);
    }

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function canUseGitRepo(User $user, SiteMigration $siteMigration): bool
    {
        return $this->haveFilled([
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS
            ], $siteMigration->form) &&
            ($siteMigration->team_id == $user->current_team_id);
    }

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function canSetupDatabase(User $user, SiteMigration $siteMigration): bool
    {
        return $this->haveFilled([
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
                AutoSiteMigration::PLUGIN
            ], $siteMigration->form) &&
            ($siteMigration->team_id == $user->current_team_id);
    }

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function confirmation(User $user, SiteMigration $siteMigration): bool
    {
        if ($siteMigration->isGit()) {
            $fields = [
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
                // SiteMigration::DATABASE
            ];
        } elseif ($siteMigration->isRestoreBackup()) {
            $fields = [
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
                SiteBackupRestoreMigration::SOURCE,
                SiteBackupRestoreMigration::BACKUPS
            ];
        } elseif ($siteMigration->isManual() && !$siteMigration->site->isWordpress()) {
            $fields = [
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
            ];
        }else{
            $fields = [
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
                AutoSiteMigration::PLUGIN,
                SiteMigration::DATABASE
            ];
        }
        return $this->haveFilled($fields, $siteMigration->form) &&
            ($siteMigration->team_id == $user->current_team_id);
    }

    /**
     * @param  User  $user
     * @param  SiteMigration  $siteMigration
     * @return bool
     */
    public function canProcess(User $user, SiteMigration $siteMigration): bool
    {
        if ($siteMigration->isAuto() || $siteMigration->isSingle() || $siteMigration->isCpanel()) {
            return ($siteMigration->team_id == $user->current_team_id);
        }
        if($siteMigration->isGit()){
            $fields = [
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
                // AutoSiteMigration::PLUGIN,
                // SiteMigration::DATABASE,
                // SiteMigration::CONFIRM // TODO: @MISHUK
            ];
        }elseif($siteMigration->isRestoreBackup()){
            $fields = [
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
                SiteBackupRestoreMigration::SOURCE,
                SiteBackupRestoreMigration::BACKUPS
            ];
        }else{
            $fields = [
                SiteMigration::DESTINATION,
                SiteMigration::DOMAINS,
                SiteMigration::SETTINGS,
                AutoSiteMigration::PLUGIN,
                SiteMigration::DATABASE,
                // SiteMigration::CONFIRM // TODO: @MISHUK
            ];
        }
        if ($siteMigration->site->hasDatabase() && $siteMigration->isManual()){
            $fields[] = SiteMigration::DATABASE;
        }

        return $this->haveFilled($fields, $siteMigration->form) && ($siteMigration->team_id == $user->current_team_id);
    }
}
