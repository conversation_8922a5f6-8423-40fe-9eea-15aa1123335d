<?php

namespace App\Rules;

use App\Validator\Domain;
use App\Rules\StagingDemoSitesOneLevelDomainRule;
use Illuminate\Validation\Rule;

class SiteNameRule
{
    static function rules(int $serverId, int $ignoreSiteId = null): array
    {
        return [
            'required',
            'max:255',
            new Domain(),
            Rule::unique('sites', 'name')->where('server_id', $serverId)->ignore($ignoreSiteId),
        ];
    }

    /**
     * Get validation rules for reserved domains
     *
     * @param string $method Domain parking method ('go_live' or 'staging_env')
     * @param bool $includeUniqueness Whether to include the uniqueness check
     * @return array
     */
    static function reservedDomainsRules(string $method, bool $includeUniqueness = true): array
    {
        $reservedDomainsPattern = implode('|', array_map('preg_quote', getReservedDomains()));

        if ($method === 'go_live') {
            return [
                'not_regex:/\\b(?:' . $reservedDomainsPattern . ')\\b/'
            ];
        } else {
            $rules = [
                new StagingDemoSitesOneLevelDomainRule()
            ];

            if ($includeUniqueness) {
                $rules[] = 'unique:sites,name';
            }

            return $rules;
        }
    }

    /**
     * Get validation rules for the name field based on domain parking method
     *
     * @param string|null $domainParkingMethod The domain parking method ('go_live' or 'staging_env')
     * @param bool $nameIsFilled Whether the name field is filled
     * @param int $serverId The server ID
     * @param int|null $ignoreSiteId The site ID to ignore in uniqueness check
     * @return array
     */
    static function nameRules(?string $domainParkingMethod, bool $nameIsFilled, int $serverId, ?int $ignoreSiteId = null): array
    {
        if ($domainParkingMethod === 'go_live' || $nameIsFilled) {
            return array_merge(
                self::rules($serverId, $ignoreSiteId),
                self::reservedDomainsRules($domainParkingMethod)
            );
        }

        return ['nullable'];
    }
}
