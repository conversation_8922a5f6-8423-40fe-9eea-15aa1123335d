<?php
namespace App\Services\StorageProvider;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BackBlazeService
{
    /**
     * API version constants
     */
    private const API_VERSION_V2 = 'v2';
    private const API_VERSION_V3 = 'v3';

    /**
     * Cache constants
     */
    private const CACHE_KEY_AUTH = 'backblaze_authorization';
    private const CACHE_TTL_MINUTES = 180;

    /**
     * Default capabilities for application keys
     */
    private const DEFAULT_CAPABILITIES = [
        "listAllBucketNames",
        "listBuckets",
        "readBuckets",
        "readBucketRetentions",
        "writeBucketRetentions",
        "readBucketEncryption",
        "writeBucketEncryption",
        "writeBucketNotifications",
        "listFiles",
        "readFiles",
        "shareFiles",
        "writeFiles",
        "deleteFiles",
        "readBucketNotifications",
        "readFileLegalHolds",
        "writeFileLegalHolds",
        "readFileRetentions",
        "writeFileRetentions",
        "bypassGovernance"
    ];

    /**
     * @var string Backblaze Key ID
     */
    protected string $keyID;

    /**
     * @var string Backblaze Application Key
     */
    protected string $applicationKey;

    /**
     * @var string Backblaze API endpoint
     */
    protected string $apiEndpoint = 'https://api.backblazeb2.com';

    protected string $region = 'us-west-004';

    protected string $endpoint = 'https://s3.us-west-004.backblazeb2.com';

    private string $adminAccountId;
    private string $adminGroupId;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->keyID = config('backblaze.keyID');
        $this->applicationKey = config('backblaze.applicationKey');
        $this->adminAccountId = config('backblaze.accountId');
        $this->adminGroupId = config('backblaze.adminGroupID');
    }

    public function setApplicationUser($keyId,$applicationKey): static
    {
        $this->keyID = $keyId;
        $this->applicationKey = $applicationKey;
        return $this;
    }

    /**
     * Get authorization data from cache or fetch new authorization
     *
     * @return array Authorization data
     * @throws \Exception If authorization fails
     */
    private function getAuthorization(): array
    {
        return Cache::remember(self::CACHE_KEY_AUTH.$this->keyID, self::CACHE_TTL_MINUTES, function () {
            try {
                $response = Http::withBasicAuth($this->keyID, $this->applicationKey)
                    ->acceptJson()
                    ->asJson()
                    ->get($this->apiEndpoint . "/b2api/" . self::API_VERSION_V3 . "/b2_authorize_account");

                if ($response->failed()) {
                    throw new \Exception('Failed to authorize Backblaze B2 account: ' . $response->body());
                }

                return $response->json();
            } catch (\Exception $e) {
                Log::error('Backblaze authorization failed', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }

    /**
     * Get authorization token
     *
     * @return string Authorization token
     * @throws \Exception If authorization fails
     */
    private function getAuthorizationToken(): string
    {
        return data_get($this->getAuthorization(), 'authorizationToken');
    }

    /**
     * Get account ID
     *
     * @return string Account ID
     * @throws \Exception If authorization fails
     */
    public function getAccountId(): string
    {
        return data_get($this->getAuthorization(), 'accountId');
    }

    /**
     * Get S3 API endpoint
     *
     * @return string S3 API endpoint
     * @throws \Exception If authorization fails
     */
    public function getS3Endpoint(): string
    {
        return data_get($this->getAuthorization(), 'apiInfo.storageApi.apiUrl');
    }

    /**
     * Make an API request to Backblaze B2
     *
     * @param string $endpoint API endpoint
     * @param array|null $params Request parameters
     * @param string $method HTTP method (GET or POST)
     * @return Response HTTP response
     * @throws \Exception If authorization fails
     */
    public function makeApiRequest(string $endpoint, ?array $params = null, string $method = "GET"): Response
    {
        try {
            $baseUrl = $this->getS3Endpoint();

            $request = Http::withHeaders(['Authorization' => $this->getAuthorizationToken()])
                ->baseUrl($baseUrl)
                ->acceptJson()
                ->asJson();

            if (strtoupper($method) === "GET") {
                return $request->get($endpoint, $params);
            } else {
                return $request->withHeaders(['Content-Type' => 'application/json'])
                    ->post($endpoint, $params);
            }
        } catch (\Exception $e) {
            Log::error('Backblaze API request failed', [
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * List all buckets for an account
     *
     * @param string|null $accountId Account ID (defaults to authorized account)
     * @return Response HTTP response
     * @throws \Exception If API request fails
     */
    public function listBuckets(?string $accountId = null)
    {
        $accountId = $accountId ?? $this->getAccountId();

        $response = $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_list_buckets',
            ['accountId' => $accountId]
        );
        return  $response->json();

    }

    /**
     * List all groups for an account
     *
     * @return Response HTTP response
     * @throws \Exception If API request fails
     */
    public function listGroups(): Response
    {
        return $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_list_groups',
            ['adminAccountId' => $this->getAccountId()]
        );
    }

    /**
     * List members of a group
     *
     * @param string $groupId Group ID
     * @return Response HTTP response
     * @throws \Exception If API request fails
     */
    public function listGroupMembers()
    {
        $response = $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_list_group_members',
            [
                'groupId' => $this->adminGroupId,
                'adminAccountId' => $this->getAccountId(),
            ]
        );
        return $response->json();
    }

    /**
     * Create a new bucket
     *
     * @param string $bucketName Bucket name
     * @param string $region Region (default: us-east-005)
     * @param string $bucketType Bucket type (default: allPrivate)
     * @return Response HTTP response
     * @throws \Exception If API request fails
     */
    public function createBucket(string $bucketName, string $region = 'us-east-005', string $bucketType = 'allPrivate')
    {
        return $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_create_bucket',
            [
                'bucketName' => $bucketName,
                'accountId' => $this->getAccountId(),
                'bucketType' => $bucketType,
                'bucketInfo' => [
                    'region' => $region
                ]
            ],
            'POST'
        );
    }

    /**
     * Delete an existing bucket
     *
     * @param string $bucketId Bucket ID
     * @return Response HTTP response
     * @throws \Exception If API request fails
     */
    public function deleteBucket(string $bucketId): Response
    {
        return $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_delete_bucket',
            [
                'bucketId' => $bucketId,
                'accountId' => $this->getAccountId(),
            ],
            'POST'
        );
    }

    /**
     * Create a new application key
     *
     * @param string $keyName Key name
     * @param string|null $bucketId Bucket ID (optional)
     * @param array $capabilities Key capabilities
     * @return Response HTTP response
     * @throws \Exception If API request fails
     */
    public function createKey(
        string $keyName,
        ?string $bucketId = null,
        array $capabilities = self::DEFAULT_CAPABILITIES
    ): Response {
        $params = [
            'keyName' => $keyName,
            'accountId' => $this->getAccountId(),
            'capabilities' => $capabilities,
        ];

        if ($bucketId) {
            $params['bucketId'] = $bucketId;
        }

        return $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_create_key',
            $params,
            'POST'
        );
    }

    /**
     * Delete an application key
     *
     * @param string $applicationKeyId Application key ID
     * @return bool True if successful
     * @throws \Exception If API request fails
     */
    public function deleteKey(string $applicationKeyId): bool
    {
       $response = $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_delete_key',
            [
                'applicationKeyId' => $applicationKeyId,
            ],
            'POST'
        );

        return $response->successful();
    }

    public function addGroupMember($memberEmail,$region)
    {
        $response =  $this->makeApiRequest(
            '/b2api/' . self::API_VERSION_V3 . '/b2_create_group_member',
            [
                'adminAccountId' => $this->adminAccountId,
                'groupId'=> $this->adminGroupId,
                'memberEmail' => $memberEmail,
                'region' => $region
            ],
            'POST'
        );
        if ($response->successful()) {
            return $response->json();
        }
        throw new \Exception('Failed to add group member: ' . $response->body());

    }

}
