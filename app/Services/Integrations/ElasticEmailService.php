<?php

namespace App\Services\Integrations;

use Arr;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Log;
use function Pest\Laravel\json;

class ElasticEmailService
{
    CONST BASE_URL = 'https://api.elasticemail.com/v4/';
    CONST BASE_URL_V2 = 'https://api.elasticemail.com/v2/';
    public Client $client, $clientV2;
    public $apiKey;
    public function __construct($apiKey)
    {
        $this->apiKey = $apiKey;
        $this->client = new Client([
            'base_uri' => self::BASE_URL,
            'headers' => [
                'X-ElasticEmail-ApiKey' => $this->apiKey,
            ],
        ]);

        $this->clientV2 = new Client([
            'base_uri' => self::BASE_URL_V2,
        ]);
    }

    public function getDomainsOnElasticEmail()
    {
        try {
            $response = $this->clientV2->get('domain/list' . '?apikey=' . $this->apiKey);
            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::error('Failed to get domains: ' . $e->getMessage());
            return ['error' => 'Failed to get domains: ' . $e->getMessage()];
        }
    }

    public function removeAllDomains()
    {
        $domains = $this->getDomainsOnElasticEmail();
        if (!empty(Arr::get($domains, 'data'))) {
            foreach (Arr::get($domains, 'data') as $domain) {
                $domainName = Arr::first(explode(' ', Arr::get($domain, 'domain')));
                Log::info('Deleting domain from elastic email subaccount: ' . $domainName);
                // skip default domain(xcloud.email)
                if (Arr::get($domain, 'domain') === 'xcloud.email (<EMAIL>)') {
                    continue;
                }
                $this->deleteDomain($domainName);
            }
        }
    }

    public function verifyDomainDkimRecord($domain)
    {
        try {
            $response = $this->clientV2->get('domain/verifydkim' . '?apikey=' . $this->apiKey . '&domain=' . $domain);
            if ($response->getStatusCode() === 200) {
                $response = json_decode($response->getBody()->getContents(), true);
                if(Arr::get($response, 'data') === 'OK') {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (GuzzleException $e) {
            Log::warning('Failed to verify domain dkim record: ' . $e->getMessage());
            return ['error' => 'Failed to verify domain dkim record: ' . $e->getMessage()];
        }
    }

    public function verifyDomainSpfRecord($domain)
    {
        try {
            $response = $this->clientV2->get('domain/verifyspf' . '?apikey=' . $this->apiKey . '&domain=' . $domain);
            if ($response->getStatusCode() === 200) {
                $response = json_decode($response->getBody()->getContents(), true);
                if(Arr::get($response, 'data.isvalid')) {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (GuzzleException $e) {
            Log::warning('Failed to verify domain SPF record: ' . $e->getMessage());
            return ['error' => 'Failed to verify domain SPF record: ' . $e->getMessage()];
        }
    }

    public function verifyDomainCnameRecord($domain)
    {
        try {
            $response = $this->clientV2->get('domain/verifytracking' . '?apikey=' . $this->apiKey . '&domain=' . $domain);
            if ($response->getStatusCode() === 200) {
                $response = json_decode($response->getBody()->getContents(), true);
                if(Arr::get($response, 'data') === 'OK') {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (GuzzleException $e) {
            Log::warning('Failed to verify domain SPF record: ' . $e->getMessage());
            return ['error' => 'Failed to verify domain SPF record: ' . $e->getMessage()];
        }
    }

    public function verifyDomainDmarcRecord($domain)
    {
        try {
            $response = $this->clientV2->get('domain/verifydmarc' . '?apikey=' . $this->apiKey . '&domain=' . $domain);
            if ($response->getStatusCode() === 200) {
                $response = json_decode($response->getBody()->getContents(), true);
                if(Arr::get($response, 'data') === 'OK') {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (GuzzleException $e) {
            Log::warning('Failed to verify domain DMARC record: ' . $e->getMessage());
            return ['error' => 'Failed to verify domain DMARC record: ' . $e->getMessage()];
        }
    }

    /**
     * @throws GuzzleException
     */
    public function sendEmail($data): array|string
    {
        try {
            $response = $this->clientV2->post('email/send', [
                'form_params' => $data,
            ]);

            return $response->getBody()->getContents();
        } catch (\Exception $e) {
            return ['error' => 'Failed to send email: ' . $e->getMessage()];
        }
    }

    public function getSubAccounts()
    {
        try {
            $response = $this->client->get('subaccounts');
            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::error('Failed to get subaccounts: ' . $e->getMessage());
            return ['error' => 'Failed to get subaccounts: ' . $e->getMessage()];
        }
    }

    public function getSubAccount($email)
    {
        try {
            $response = $this->client->get('subaccounts/' . $email);
            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::error('Failed to get subaccount for email#' . $email . ' : ' . $e->getMessage());
            return ['error' => 'Failed to get subaccount for email#' . $email . ' : ' . $e->getMessage()];
        }
    }

    public function createSubAccount($data, $credit = 100)
    {
        try {
            $response = $this->clientV2->post("account/addsubaccount", [
                'query' => $data
            ]);

            if ($response->getStatusCode() === 200) {
                $subaccount = json_decode($response->getBody()->getContents(), true);
                // assign credit to subaccount
                $subAccountEmail = Arr::get($data, 'email');
                $subAccountApiKey = Arr::get($subaccount, 'data');

                // set monthly refill credits to handle recurring credit refills
                $this->client->put("subaccounts/{$subAccountEmail}/settings/email", [
                    'json' => [
                        'MonthlyRefillCredits' => $credit,
                        'EmailSizeLimit' => 10,
                        'RequiresEmailCredits' => true,
                    ],
                ]);

                $this->client->patch("subaccounts/{$subAccountEmail}/credits", [
                    'json' => [
                        'Credits' => $credit,
                        'Notes' => 'Assign ' . $credit . ' credits to this sub account.',
                    ],
                ]);

                return $subaccount;
            }else{
                return json_encode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::error('Failed to create subaccount for email#' . Arr::get($data, 'email') . ' : ' . $e->getMessage());
            return [
                'error' => 'Failed to create subaccount for email#' . Arr::get($data, 'email') . ' : ' . $e->getMessage()
            ];
        }
    }

    public function deleteSubAccount($email)
    {
        try {
            $response = $this->clientV2->get('account/deletesubaccount' . '?apikey=' . $this->apiKey . '&subAccountEmail=' . $email);
            Log::info('Response: ' , [
                'response' => json_decode($response->getBody()->getContents(), true)
            ]);
            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::error('Failed to delete subaccount for email#' . $email . ' : ' . $e->getMessage());
            return ['error' => 'Failed to delete subaccount for email#' . $email . ' : ' . $e->getMessage()];
        }
    }

    public function updateCreditOnSubAccount($subAccountEmail, $credit)
    {
        try {
            $response = $this->client->patch("subaccounts/{$subAccountEmail}/credits", [
                'json' => [
                    'Credits' => $credit,
                    'Notes' => ($credit < 0 ? 'Subtracted' : 'Assigned ') . $credit . ' credits.',
                ],
            ]);

            $subAccount = $this->getSubAccount($subAccountEmail);
            // set monthly refill credits to handle recurring credit refills
            $this->client->put("subaccounts/{$subAccountEmail}/settings/email", [
                'json' => [
                    'MonthlyRefillCredits' => Arr::get($subAccount, 'EmailCredits'),
                    'EmailSizeLimit' => 10,
                    'RequiresEmailCredits' => true
                ],
            ]);

            return $response;
        }catch (GuzzleException $e) {
            Log::error('Failed to update credit on subaccount for email#' . $subAccountEmail . ' : ' . $e->getMessage());
            return ['error' => 'Failed to update credit on subaccount for email#' . $subAccountEmail . ' : ' . $e->getMessage()];
        }
    }

    public function addDomain($domain)
    {
        try {
            $response = $this->clientV2->post("domain/add", [
                'query' => [
                    'apikey' => $this->apiKey,
                    'domain' => $domain,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::error('Failed to add domain#' . $domain . ' : ' . $e->getMessage());
            return ['error' => 'Failed to add domain#' . $domain . ' : ' . $e->getMessage()];
        }
    }

    public function deleteDomain($domain)
    {
        try {
            $response = $this->clientV2->post("domain/delete", [
                'query' => [
                    'apikey' => $this->apiKey,
                    'domain' => $domain,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::warning('Failed to delete domain#' . $domain . ' : ' . $e->getMessage());
            return ['error' => 'Failed to delete domain#' . $domain . ' : ' . $e->getMessage()];
        }
    }

    public function domainList()
    {
        try {
            $response = $this->clientV2->get("domain/list", [
                'query' => [
                    'apikey' => $this->apiKey,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::warning('Failed to get domain list: ' . $e->getMessage());
            return ['error' => 'Failed to get domain list: ' . $e->getMessage()];
        }
    }

    public function getAccountDetails()
    {
        try {
            $response = $this->clientV2->get("account/overview", [
                'query' => [
                    'apikey' => $this->apiKey,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::warning('Failed to get account details: ' . $e->getMessage());
            return ['error' => 'Failed to get account details: ' . $e->getMessage()];
        }
    }

    public function setDefaultEmailForDomain($email)
    {
        try {
            $response = $this->clientV2->post("domain/setdefault", [
                'query' => [
                    'apikey' => $this->apiKey,
                    'email' => $email,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            }
        } catch (GuzzleException $e) {
            Log::error('Failed to set default email#' . $email . ' : ' . $e->getMessage());
            return ['error' => 'Failed to set default email#' . $email . ' : ' . $e->getMessage()];
        }
    }
}

