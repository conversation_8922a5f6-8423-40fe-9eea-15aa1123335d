<?php

namespace App\Services\Migration\Server;

use App\Models\ServerMigration;
use App\Services\Integrations\CloudflareService;
use App\Services\Site\StagingDomainGenerator;
use Arr;
use Illuminate\Support\Str;

class SiteMigrator
{
    public function __construct(public StagingDomainGenerator $stagingDomainGenerator)
    {
    }
    public function storeSitesToMigrate(ServerMigration $serverMigration, array $selectedSites): void
    {
        $sitesToMigrate = [];
        $siteCollection = collect($serverMigration->sites);
        foreach ($selectedSites as $site) {
            foreach ($siteCollection as $siteItem) {
                if ($siteItem['domain_name'] === $site) {
                    //generate staging domain name
                    $domain = config('services.cloudflare_updated.active');
                    $domainName =  $this->stagingDomainGenerator->generateStagingDomain($siteItem['domain_name'], $serverMigration->id, Str::lower(Str::random(3))). '.' . $domain;
                    $siteItem['old_domain_name'] = $siteItem['domain_name'];
                    $siteItem['useLiveDomain'] = false;
                    $siteItem['additional_domains'] = [];
                    $siteItem['ssl_private_key'] = "";
                    $siteItem['ssl_provider'] = "";
                    $siteItem['ssl_certificate'] = "";
                    $siteItem['staging_domain_name'] = $domainName;

                    // check if domain active on cloudflare
//                    foreach ($serverMigration->team->cloudflareIntegrations as $cloudflareIntegration){
//                        // extracting subdomain and site name from domain
//                        $subdomainAndDomain = extractSubdomainAndSiteNameFromDomain('https://' . $siteItem['domain_name']);
//
//                        $subdomain = Arr::get($subdomainAndDomain, 'subdomainPart');
//                        $siteName = Arr::get($subdomainAndDomain, 'siteName');
//
//                        $response = (new CloudflareService($cloudflareIntegration))->checkDomainExists($siteName);
//
//                        if(Arr::get($response, 'domain_active_on_cloudflare')){
//                            $siteItem['cloudflare']['cloudflare_integration_id'] = $cloudflareIntegration->id;
//                            $siteItem['cloudflare']['domain_active_on_cloudflare'] = Arr::get($response, 'domain_active_on_cloudflare');
//                            $siteItem['cloudflare']['zone_id'] = Arr::get($response, 'zone_id');
//                            $siteItem['cloudflare']['account_id'] = Arr::get($response, 'account_id');
//                            $siteItem['cloudflare']['subdomain'] = $subdomain;
//                            $siteItem['cloudflare']['site_name'] = $siteName;
//                            break;
//                        }
//                    }
                    $sitesToMigrate[] = $siteItem;
                }
            }
        }

        $serverMigration->update([
            'sites_to_migrate' => $sitesToMigrate,
        ]);
    }

    public function storeCpanelSitesToMigrate(ServerMigration $serverMigration, array $selectedSites): void
    {
        $sitesToMigrate = [];
        foreach ($selectedSites as $site) {
            //generate staging domain name
            $domain = config('services.cloudflare_updated.active');

            //if domain name contains https or http, remove it
            if (Str::startsWith($site['domain'], 'https://')) {
                $site['domain'] = str_replace('https://', '', $site['domain']);
            } elseif (Str::startsWith($site['domain'], 'http://')) {
                $site['domain'] = str_replace('http://', '', $site['domain']);
            }

            //domain/somepath , remove /somepath
            if (Str::contains($site['domain'], '/')) {
                $site['domain'] = explode('/', $site['domain'])[0];
            }

            $domainName = $this->stagingDomainGenerator->generateStagingDomain($site['domain'],
                    $serverMigration->id, Str::lower(Str::random(3))).'.'.$domain;
            $siteItem['old_domain_name'] = $site['domain'];
            $siteItem['useLiveDomain'] = false;
            $siteItem['additional_domains'] = [];
            $siteItem['ssl_private_key'] = "";
            $siteItem['ssl_provider'] = "";
            $siteItem['ssl_certificate'] = "";
            $siteItem['staging_domain_name'] = $domainName;

            //check site['domain'] starts with http or https, if not add https
            // if (!Str::startsWith($site['domain'], 'http')) {
            //     $siteItem['domain_name'] = 'https://'.$site['domain'];
            // }else{
            //     $siteItem['domain_name'] = $site['domain'];
            // }

            // // check if domain active on cloudflare
            // foreach ($serverMigration->team->cloudflareIntegrations as $cloudflareIntegration) {
            //     // extracting subdomain and site name from domain
            //     $subdomainAndDomain = extractSubdomainAndSiteNameFromDomain($siteItem['domain_name']);
            //
            //     $subdomain = Arr::get($subdomainAndDomain, 'subdomainPart');
            //     $siteName = Arr::get($subdomainAndDomain, 'siteName');
            //
            //     $response = (new CloudflareService($cloudflareIntegration))->checkDomainExists($siteName);
            //
            //     if (Arr::get($response, 'domain_active_on_cloudflare')) {
            //         $siteItem['cloudflare']['cloudflare_integration_id'] = $cloudflareIntegration->id;
            //         $siteItem['cloudflare']['domain_active_on_cloudflare'] = Arr::get($response,
            //             'domain_active_on_cloudflare');
            //         $siteItem['cloudflare']['zone_id'] = Arr::get($response, 'zone_id');
            //         $siteItem['cloudflare']['account_id'] = Arr::get($response, 'account_id');
            //         $siteItem['cloudflare']['subdomain'] = $subdomain;
            //         $siteItem['cloudflare']['site_name'] = $siteName;
            //         break;
            //     }
            // }
            $sitesToMigrate[] = $siteItem;

        }

        $serverMigration->update([
            'sites_to_migrate' => $sitesToMigrate,
        ]);
    }
}

