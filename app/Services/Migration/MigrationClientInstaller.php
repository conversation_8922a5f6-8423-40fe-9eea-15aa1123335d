<?php

namespace App\Services\Migration;

use App\Callbacks\MarkAsMigrated;
use App\Models\Site;
use App\Scripts\RunxCloudMigrationCli;
use App\Scripts\xCloudMigrationScript;
use Exception;

class MigrationClientInstaller
{
    public MigrationConnector $connector;

    public function __construct(public Site $site)
    {
        $this->connector = new MigrationConnector($this->site->siteMigration);
    }

    /**
     * @throws Exception
     */
    function handle()
    {
        $task = $this->site->upload(new xCloudMigrationScript($this->site, $this->connector), [
            'path' => '/root/.xcloud/site-'.$this->site->id.'-migration.php'
        ]);

        if (!$task->successful()) {
            throw new Exception('Failed to upload migration script');
        }

        $task = $this->site->runInBackground(new RunxCloudMigrationCli($this->site), [
            'skipOutput' => true,
            'then' => [
                new MarkAsMigrated($this->site->id)
            ],
        ]);

        $this->site->update(['meta->xcloud_migration_task_id' => $task->id]);
    }
}
