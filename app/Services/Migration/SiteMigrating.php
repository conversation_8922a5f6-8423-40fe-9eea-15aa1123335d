<?php

namespace App\Services\Migration;

use App\Models\AutoSiteMigration;
use App\Models\SiteMigration;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Lang;

class SiteMigrating
{
    const PROGRESS_MAX = 31;

    const CONFIGURING_SSL = 1;
    const CHECKING_STATUS = 2;
    const GENERATE_SSH_KEY = 3;
    const INSTALLING_PHP = 4;
    const INSTALLING_DATABASE = 5;

    const INSTALLING_SITE = 6;
    const CREATE_SITE_DIRECTORY = 7;
    const MAKING_SITE_DIRECTORY_READY = 8;
    const MAKING_DATABASE_READY = 9;

    const FETCHING_SITE_URL = 10;
    const UPDATING_PERMISSIONS = 11;
    const DEPLOY_SCRIPT = 12;

    const SCANING_FILESYSTEM = 13;
    const FOUND_FILES = 14;
    const MIGRATING_FILES = 15;
    const RE_VERIFYING_FILES = 16;
    const RETRYING_FAILED_FILES = 17;
    const FILE_MIGRATION_FINISHED = 18;

    const SCANING_DB = 19;
    const FOUND_DB = 20;
    const MIGRATING_DB = 21;
    const DB_MIGRATION_FINISHED = 22;

    const VERIFYING_DNS = 23;
    const CONFIGURING_HTTPS = 24;
    const CONFIGURING_FULL_PAGE_CACHE = 25;
    const CONFIGURING_REDIS_CACHE = 26;
    const CONFIGURING_NGINX = 27;

    const INSTALLING_MONITORING = 28;
    const INSTALLING_WP_CRON_JOB = 29;

    const REMOVE_DEBUG_LOG = 30;

    const HANDLE_SITE_INDEXING = 31; #Indexing should be 31 because 30 is already taken (REMOVE_DEBUG_LOG)

    const FINISHING_UP = 32;


    static function get(SiteMigration $siteMigration): array
    {
        $steps = [];
        if ($siteMigration->isManual()) {
            if ($siteMigration->site->hasDatabase()){
                if (Arr::get($siteMigration->form,'database.database')){
                    $steps = [
                        ...Lang::get('migration.manual_init')
                    ];
                }else{
                    $steps = [
                        ...Lang::get('migration.manual_optional_db')
                    ];
                }
            }else{
                $steps = [
                    ...Lang::get('migration.manual_no_db_init')
                ];
            }
        }else if ($siteMigration->isGit() && !$siteMigration->site->hasDatabase()){
            $steps = [
                ...Lang::get('migration.no_db_init')
            ];
        } else {
            $steps = [
                ...Lang::get('migration.init')
            ];
        }

        if ($siteMigration->isAuto()) {
            $steps = [
                ...$steps,
                ...Lang::get('migration.files'),
            ];
        }


        if ($siteMigration->shouldMigrateDatabase() && $siteMigration->isAuto()) {
            $steps = [
                ...$steps,
                ...Lang::get('migration.db')
            ];
        }
        if($siteMigration->site->hasDatabase() && Arr::get($siteMigration->form,'database.database')) {
            $steps = [
                ...$steps,
                ...Lang::get('migration.config')
            ];
        }else{
            $steps = [
                ...$steps,
                ...Lang::get('migration.no_db_config')
            ];
        }

        return $steps;
    }
}
