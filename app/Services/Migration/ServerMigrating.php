<?php

namespace App\Services\Migration;

use App\Models\AutoSiteMigration;
use App\Models\SiteMigration;
use Illuminate\Support\Facades\Lang;

class ServerMigrating
{
    const PROGRESS_MAX = 21;

    const CHECKING_STATUS = 1;
    const INSTALLING_PHP = 2;
    const INSTALLING_DATABASE = 3;
    const ESTABLISHING_CONNECTION = 4;
    const INSTALLING_SITE = 5;
    const MAKING_API_DATA_READY = 6;
    const FETCH_CPANEL_BACKUP = 7;
    const EXTRACTING_CPANEL_BACKUP = 8;
    const MAKING_CPANEL_SITE_READY = 9;
    const FETCHING_DATABASE = 10;
    const IMPORTING_DATABASE = 11;
    const MAKING_DATABASE_READY = 12;

    const SETUP_STAGING_DOMAIN = 13;

    const VERIFYING_DNS = 14;
    const CONFIGURING_HTTPS = 15;
    const CONFIGURING_FULL_PAGE_CACHE = 16;
    const CONFIGURING_REDIS_CACHE = 17;
    const CONFIGURING_NGINX = 18;


    const INSTALLING_MONITORING = 19;
    const INSTALLING_WP_CRON_JOB = 20;

    const FINISHING_UP = 21;

    static function get(SiteMigration $siteMigration): array
    {
        $steps = [
            ...Lang::get('server-migration.init')
        ];

        if ($siteMigration->serverMigration->getMeta('hosting_provider') === 'cPanel') {
            $steps = [
                ...Lang::get('server-migration.init_cpanel')
            ];
        } else {
            $steps = [
                ...Lang::get('server-migration.init')
            ];
        }


        $steps = [
            ...$steps,
            ...Lang::get('server-migration.config')
        ];

        return $steps;
    }
}
