<?php

namespace App\Services\PaymentGateway\API;

use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Models\SubscriptionProduct;
use App\Models\WhiteLabel;
use App\Repository\StripePaymentRepository;
use App\Services\PaymentGateway\InvoiceServices\Invoiceable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\Subscription;

class InvoicePaymentService
{
    /**
     * Process invoice payment
     *
     * @param GeneralInvoice $invoice
     * @param PaymentMethod|null $paymentMethod
     * @return array
     * @throws ApiErrorException
     */
    public static function processPayment(GeneralInvoice $invoice, PaymentMethod $paymentMethod = null): array
    {
        // Initialize Stripe
        Stripe::setApiKey(config('services.stripe.secret_key'));

        // Fetch payment webhook from stripe
        if ($invoice->status->isFailed() && $invoice?->gateway_invoice_or_intent_id) {
            $invoice->fetchPaymentWebhookFromStripe();
        }

        // Check if invoice is already paid from stripe
        if ($invoice?->gateway_invoice_or_intent_id && $invoice->isPaid()) {
            return [
                'message' => 'Invoice already paid!',
                'type' => 'success',
                'invoice' => $invoice->toArray(),
                'status' => $invoice->status
            ];
        }

        // Check if the invoice is paid without payment intent
        if (!$invoice?->gateway_invoice_or_intent_id && $invoice->status->isPaid()) {
            return [
                'message' => 'Invoice already paid manually. Please contact support if you need any assistance.',
                'type' => 'success',
                'invoice' => $invoice->toArray(),
                'status' => $invoice->status
            ];
        }

        // Check if the invoice amount is less than the minimum amount
        if ($invoice->amount < Invoiceable::MIN_AMOUNT) {
            return [
                'message' => 'Invoice amount is less than '.$invoice->currency->symbol().Invoiceable::MIN_AMOUNT.'. Please contact support if you need any assistance.',
                'type' => 'error',
                'invoice' => $invoice->toArray(),
                'status' => $invoice->status
            ];
        }

        $usingDefaultPaymentMethod = false;

        if (!$paymentMethod) {
            $paymentMethod = $invoice->team->activePaymentMethod()->first();
            $usingDefaultPaymentMethod = true;
        }

        if (!$paymentMethod) {
            return [
                'message' => 'Please select a valid payment method.',
                'type' => 'error',
                'invoice' => $invoice->toArray(),
                'status' => $invoice->status
            ];
        }

        // switch card with the specified payment method
        $invoice->switchToPaymentMethod($paymentMethod);

        $paymentIntent = null;
        $PaymentIntendResetLogMessage = null;

        // Check if the payment intent is canceled from the stripe
        if ($invoice->gateway_invoice_or_intent_id) {
            $paymentIntent = PaymentIntent::retrieve($invoice->gateway_invoice_or_intent_id);
        }

        if($paymentIntent && $paymentIntent->status === PaymentIntent::STATUS_CANCELED) {
            $PaymentIntendResetLogMessage = 'Payment intent cancelled from stripe. Resetting invoice for retry payment';
        }

        if ($paymentIntent && $paymentIntent->status === PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD) { // This action set by stripe
            try {

                (new StripePaymentRepository())->cancelPaymentIntent($invoice);

            } catch (\Exception $e) {
                Log::warning('Payment intent cancellation failed '.$invoice->invoice_number, [
                    'invoice_id' => $invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_intent_status' => $paymentIntent->status,
                    'xcloud_team_id' => $invoice->team_id,
                    'xcloud_payment_method_id' => $invoice->payment_method_id,
                    'error' => $e->getMessage()
                ]);
            }

            $PaymentIntendResetLogMessage = 'Payment has issue from stripe. The status was '.$paymentIntent->status.'. Resetting invoice for retry payment';
        }

        if ($invoice->gateway_customer_id !== $paymentMethod->customer_id) {
            $PaymentIntendResetLogMessage = 'Payment method customer id mismatch. Resetting invoice for retry payment';
        }

        if($PaymentIntendResetLogMessage){
            $invoice->update([
                'gateway_invoice_or_intent_id' => null,
                'logs->reset_invoice' => $PaymentIntendResetLogMessage. ', action taken ~ by '.auth()->user()->email.' at '. now()->toDateTimeString(),
            ]);
        }

        // If payment intent is not created yet
        if (!$invoice->gateway_invoice_or_intent_id) {
            $paymentIntent = (new StripePaymentRepository())->createPaymentIntent($invoice);

            $invoice->update([
                'gateway_invoice_or_intent_id' => $paymentIntent->id,
                'gateway_customer_id' => $paymentIntent->customer,
                'gateway_payment_method_id' => $paymentIntent->payment_method
            ]);
        }

        // Todo: Need refactor with @Faisal vai
        if ($paymentMethod->security_protocol) {
            // v2 payment method(update existing payment method instead of creating new one)
            $stripeAccount = $invoice->team->whiteLabel ? $invoice->team->whiteLabel->connectedAccount->stripe_account_id : null;

            if ($paymentIntent->payment_method !== Arr::get($paymentMethod->meta, 'stripe.payment_method')) {
                $updateParams = [
                    'payment_method' => Arr::get($paymentMethod->meta, 'stripe.payment_method'),
                ];

                $paymentIntent = PaymentIntent::update($paymentIntent->id, $updateParams, $stripeAccount ? [
                    'stripe_account' => $stripeAccount
                ] : []);
            }
        }

        // take charge with the selected payment method
        try {
            $paymentIntent = $paymentIntent->confirm();
        }catch (\Exception $e){
            Log::warning('Confirming payment intent failed', [
                'invoice_id' => $invoice->id,
                'payment_intent_id' => $paymentIntent->id,
                'payment_intent_status' => $paymentIntent->status,
                'xcloud_team_id' => $invoice->team_id,
                'xcloud_payment_method_id' => $invoice->payment_method_id,
                'error' => $e->getMessage()
            ]);
        }

        if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION) {
            if (!$paymentMethod->security_protocol) {
                $paymentMethod->markAs3dsCard();
            }

            $invoice->setStatusRequiresAction();

            // @Todo: Refactor with @Faisal vai
            if($invoice->isWhiteLabelPurchase()){
                $whiteLabelID = Arr::get($invoice->meta, 'whitelabel_id');
                if($whiteLabelID){
                    $subscriptionProductID = Arr::get($invoice->meta, 'subscription_product_id');
                    $stripeSubscriptionID = Arr::get($invoice->meta, 'stripe_subscription_id');

                    if($subscriptionProductID && $stripeSubscriptionID){
                        $subscriptionProduct = SubscriptionProduct::findOrFail($subscriptionProductID);
                        if($invoice->team->whiteLabel){
                            $stripeSubscription = Subscription::retrieve($stripeSubscriptionID, [
                                'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
                            ]);
                        }else{
                            $stripeSubscription = Subscription::retrieve($stripeSubscriptionID);
                        }

                        return [
                            'message' => 'Payment requires 3D secure authentication',
                            'type' => 'warning',
                            'invoice' => $invoice->toArray(),
                            'redirect' => route('stripe.subscription-product.checkout.requires3dsecure', [
                                'affiliateId' => null,
                                'paymentIntentId' => $paymentIntent->id,
                                'cartId' => null,
                                'invoiceId' => $invoice->id,
                                'nextRoute' => 'white-label.onboarding.brand-setup',
                                'routeParam' => [
                                    'model' => WhiteLabel::class,
                                    'id' => $whiteLabelID
                                ],
                                'subscriptionProductId' => $subscriptionProduct->id,
                                'stripeSubscriptionId' => $stripeSubscription->id,
                            ])
                        ];
                    }
                }
            }

            return [
                'message' => 'Payment requires 3D secure authentication',
                'type' => 'warning',
                'invoice' => $invoice->toArray(),
                'status' => $invoice->status,
                'redirect' => route('stripe.checkout.requires3dsecure', [
                    'paymentIntentId' => $paymentIntent->id,
                    'sessionId' => $paymentMethod->session_id,
                    'affiliateId' => $invoice->team->getMeta('affiliate.affiliate_code'),
                    'invoiceId' => $invoice->id,
                ])
            ];
        }

        $confirmPaymentIntent = StripePaymentRepository::confirmPayment($invoice);

        if($confirmPaymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
            return [
                'message' => 'Payment successfully processed.',
                'type' => 'success',
                'invoice' => $invoice->toArray()
            ];
        }

        $invoice->setStatusPaymentFailed();

        $errorResponse = [
            'message' => $confirmPaymentIntent?->last_payment_error?->message. ' Payment failed, please try again with a different payment method.',
            'type' => 'error',
            'status' => $invoice->status,
            'invoice' => $invoice->toArray()
        ];

        if ($usingDefaultPaymentMethod) {
            $errorResponse['redirect'] = route('invoice.pay', $invoice->invoice_number);
        }

        return $errorResponse;
    }
}
