<?php

namespace App\Services\Database;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

/**
 * This class can be deleted. It will be replaced by App\Enums\DatabaseType;
 */
class DatabaseEngine
{
    const DEFAULT = 'mysql8';

    const MYSQL_8 = 'mysql8';
    const MARIADB_10 = 'mariadb10';

    const VERSIONS = [
        'none',
        'mysql8',
        'mariadb10',
    ];

    function getVersions(): array
    {
        return self::VERSIONS;
    }

    static function getVersionList(): array
    {
        return [
            [
                'value' => 'none',
                'label' => 'None',
                'disabled' => true,
            ],
            [
                'value' => 'mysql8',
                'label' => 'MySQL 8.0'
            ],
            [
                'value' => 'mariadb10',
                'label' => 'MariaDB 10.6',
            ]
        ];
    }

    /**
     * @return In
     */
    static function asRule(): In
    {
        return Rule::in(self::VERSIONS);
    }
}
