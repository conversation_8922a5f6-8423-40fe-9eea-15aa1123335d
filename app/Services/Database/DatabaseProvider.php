<?php

namespace App\Services\Database;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

class DatabaseProvider
{
    const IN_SERVER = 'in_server';
    const FROM_PROVIDER = 'from_provider';
    const CUSTOM = 'custom';
    const SQLITE = 'sqlite';

    const DEFAULT = self::IN_SERVER;
    const NULL = 'null';

    const VERSIONS = [
        self::IN_SERVER,
        self::FROM_PROVIDER,
        self::CUSTOM,
    ];


    /**
     * @return In
     */
    static function asRule($acceptNull=false): In
    {
        if ($acceptNull) {
            return Rule::in(array_merge(self::VERSIONS, [self::NULL]));
        }
        return Rule::in(self::VERSIONS);
    }
}
