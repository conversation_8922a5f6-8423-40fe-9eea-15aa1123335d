<?php

namespace App\Services\Database;

use App\Models\Site;
use App\Services\Database\DatabaseNameGenerator as NameGenerator;
use Illuminate\Support\Str;

class DatabaseNameGenerator
{
    /**
     * Generate a database name.
     *
     * @param  string  $name
     * @param  int  $limit
     * @return string
     */
    static function generate(string $name, int $limit = 16): string
    {
        $name = Str::of($name)
            ->explode('.')
            ->reduce(function ($carry, $part) {
                return strlen($carry) > 4 ? $carry : $carry.'_'.$part;
            }, '');

        return (string) Str::of($name)
            ->lower()
            ->replace([' ', '.', '-'], '_')
            ->slug('_')
            ->limit($limit, '')
            ->trim('_');
    }
    /**
     * Generate a database name.
     *
     * @param  string  $name
     * @param  int  $limit
     * @return string
     */
    static function generateDomain(string $name, int $limit = 16): string
    {
        $name = Str::of($name)
            ->split('/[.\s\-_]/')
            ->reduce(function ($carry, $part) {
                return strlen($carry) > 4 ? $carry : $carry.'-'.$part;
            }, '');

        return (string) Str::of($name)
            ->lower()
            ->replace([' ', '.', '_'], '-')
            ->slug()
            ->limit($limit, '')
            ->trim('-');
    }

    static function generateDOClusterName(string $name, int $limit = 16): string
    {
        $name = Str::of($name)
            ->explode('.')
            ->reduce(function ($carry, $part) {
                return strlen($carry) > 4 ? $carry : $carry.'-'.$part;
            }, '');

        return (string) 'do-db-c-'.strtolower(Str::random(4)).'-'.Str::of($name)
            ->lower()
            ->replace([' ', '.', '-'], '-')
            ->slug('-')
            ->limit($limit, '')
            ->trim('-');
    }

    static function generateName(Site $site): string
    {
        return self::generate('s'.$site->id.'_'.$site->name);
    }

    static function generateUser(Site $site): string
    {
        return self::generate('u'.$site->id.'_'.$site->name);
    }
}
