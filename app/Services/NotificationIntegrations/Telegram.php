<?php

namespace App\Services\NotificationIntegrations;

use App\Enums\NotificationIntegrationStatus;
use App\Interfaces\NotificationIntegrationInterface;
use App\Models\NotificationIntegration;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class Telegram implements NotificationIntegrationInterface
{
    public function __construct(public NotificationIntegration $integration)
    {

    }

    public function disconnect(NotificationIntegration $notificationIntegration): bool
    {
        try {
            $notificationIntegration->update([
                'status' => NotificationIntegrationStatus::DISCONNECTED,
                'is_connected' => false
            ]);

            if (!blank($notificationIntegration) && isset($notificationIntegration->meta['message'])) {
                $chatId = $notificationIntegration->meta['message']['chat']['id'];
                $this->sendDisconnectNotification($chatId);
            }

            $notificationIntegration->delete();

            return true;
        } catch (Exception $e) {
            Log::error($e);
            return false;
        }
    }

    public function uniqueKey(): string
    {
        return $this->integration?->unique_id;
    }

    public function isConnected(): bool
    {
        return $this->integration && $this->integration->is_connected;
    }

    private function sendDisconnectNotification($chatId)
    {
        try {
            $client = new Client();
            $response = $client->post("https://api.telegram.org/bot" . config('xcloud-notification.telegram.token') . "/sendMessage", [
                'json' => [
                    'chat_id' => $chatId,
                    'text' => 'You have successfully disconnected your Telegram account from xCloudBot.',
                ],
            ]);

            Log::info('Telegram disconnected successfully. Response: '.$response->getBody()->getContents());
        } catch (Exception $e) {
            Log::info('Telegram connection disconnected.');
            return false;
        }
    }
}
