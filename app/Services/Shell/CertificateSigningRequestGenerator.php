<?php

namespace App\Services\Shell;

use App\Models\Site;
use Illuminate\Support\Str;
use Symfony\Component\Process\Process;

class CertificateSigningRequestGenerator
{
    public static function generate(Site $site): array
    {
        $uniqueId = time();
        $keyFile = "site_{$site->id}_{$uniqueId}.key";
        $csrFile = "site_{$site->id}_{$uniqueId}.csr";
        $subject = "/C=US/ST=Delaware/L=/O=/CN=www.{$site->name}";

        $openSslBin="/usr/bin/openssl";  # (run "which openssl" to get the path)

        (Process::fromShellCommandline(
            "{$openSslBin} req -new -newkey rsa:2048 -nodes -keyout {$keyFile} -out {$csrFile} -subj {$subject}",
            '/tmp'
        ))->mustRun();

        [$keyFile, $csrFile] = [
            file_get_contents('/tmp/' . $keyFile),
            file_get_contents('/tmp/' . $csrFile),
        ];

        @unlink('/tmp/' . $keyFile);
        @unlink('/tmp/' . $csrFile);

        return (array) compact('keyFile', 'csrFile');
    }
}