<?php

namespace App\Services\Clone;

use App\Models\AutoSiteMigration;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use Illuminate\Support\Facades\Lang;

class SiteCloning
{
    const PROGRESS_MAX = 13;

    const CHECKING_STATUS = 1;
    const INSTALLING_PHP = 2;
    const SYNCING_PHP_SETTINGS = 3;

    const INSTALLING_DATABASE = 4;
    const CLONING_SITE = 5;

    const CONFIGURING_SSL = 6;
    const CONFIGURING_HTTPS = 7;
    const CONFIGURING_FULL_PAGE_CACHE = 8;
    const CONFIGURING_REDIS_CACHE = 9;
    const CONFIGURING_NGINX = 10;
    const DEPLOY_SCRIPT = 11;
    const INSTALLING_MONITORING = 12;
    const INSTALLING_WP_CRON_JOB = 13;

    const FINISHING_UP = 14;


    static function get(SiteClone $siteClone): array
    {
        $steps = [
            ...Lang::get('clone.init')
        ];

        return [
            ...$steps,
            ...Lang::get('clone.config')
        ];
    }
}
