<?php

namespace App\Services\DNS;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CloudflareDns
{
    public PendingRequest $client;

    private $activeDomain, $token, $zoneID, $accountID;

    public CONST BASE_URL = 'https://api.cloudflare.com';

    function __construct($url = null)
    {
        if($url){
            $urlComponents = parse_url($url);
            $hostParts = explode('.', $urlComponents['path']);
            if (count($hostParts) >= 2) {
                $this->activeDomain = $hostParts[count($hostParts) - 2] . '.' . $hostParts[count($hostParts) - 1];
            }
        }

        $this->activeDomain = $this->activeDomain ?? config('services.cloudflare_updated.active');

        $this->token = $this->getCloudflareToken();

        if (app()->isLocal()) {
            Log::info('CloudflareDns::__construct() $this->activeDomain: '.$this->activeDomain);
            Log::info('CloudflareDns::__construct() $this->token: '.$this->token);
        }

        $this->zoneID = $this->getCloudflareZoneID();
        $this->accountID = $this->getCloudflareAccountID();

        // TODO: Need to verify this code by Faisal Vai
        $this->client = Http::withHeaders([
            'X-Auth-Email' => config('services.cloudflare_updated.account_email'),
            'X-Auth-Key' => $this->token,
        ])
            ->timeout(180)
            ->baseUrl(self::BASE_URL)
            ->acceptJson();
        /*$this->client = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])
            ->timeout(180)
            ->baseUrl(self::BASE_URL)
            ->acceptJson();*/
    }

    private function getCloudflareToken()
    {
        return match ($this->activeDomain){
            'x-cloud.app' => config('services.cloudflare_updated.xcloud_app.dns.token') ?? config('services.cloudflare.dns.token'),
            'xcloud.name' => config('services.cloudflare_updated.xcloud_name.dns.token') ?? config('services.cloudflare.dns.token'),
            'wp1.site' => config('services.cloudflare_updated.wp1_site.dns.token') ?? config('services.cloudflare.dns.token'),
            'wp1.sh' => config('services.cloudflare_updated.wp1sh_site.dns.token') ?? config('services.cloudflare.dns.token'),
            'wp1.host' => config('services.cloudflare_updated.wp1_host.dns.token') ?? config('services.cloudflare.dns.token'),
            '1wp.site' => config('services.cloudflare_updated.onewp_site.dns.token') ?? config('services.cloudflare.dns.token'),
            'tryxcloud.cc' => config('services.cloudflare_updated.tryxcloud_cc.dns.token') ?? config('services.cloudflare.dns.token'),
            'xsql.app' => config('services.cloudflare_updated.xsql_app.dns.token') ?? config('services.cloudflare.dns.token'),
            default => config('services.cloudflare_updated.default.dns.token') ?? config('services.cloudflare.dns.token'),
        };
    }

    private function getCloudflareZoneID()
    {
        return match ($this->activeDomain){
            'x-cloud.app' => config('services.cloudflare_updated.xcloud_app.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            'xcloud.name' => config('services.cloudflare_updated.xcloud_name.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            'wp1.site' => config('services.cloudflare_updated.wp1_site.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            'wp1.sh' => config('services.cloudflare_updated.wp1sh_site.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            'wp1.host' => config('services.cloudflare_updated.wp1_host.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            '1wp.site' => config('services.cloudflare_updated.onewp_site.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            'tryxcloud.cc' => config('services.cloudflare_updated.tryxcloud_cc.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            'xsql.app' => config('services.cloudflare_updated.xsql_app.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
            default => config('services.cloudflare_updated.default.dns.zone_id') ?? config('services.cloudflare.dns.zone_id'),
        };
    }

    private function getCloudflareAccountID()
    {
        return match ($this->activeDomain){
            'x-cloud.app' => config('services.cloudflare_updated.xcloud_app.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            'xcloud.name' => config('services.cloudflare_updated.xcloud_name.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            'wp1.site' => config('services.cloudflare_updated.wp1_site.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            'wp1.sh' => config('services.cloudflare_updated.wp1sh_site.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            'wp1.host' => config('services.cloudflare_updated.wp1_host.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            '1wp.site' => config('services.cloudflare_updated.onewp_site.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            'tryxcloud.cc' => config('services.cloudflare_updated.tryxcloud_cc.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            'xsql.app' => config('services.cloudflare_updated.xsql_app.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
            default => config('services.cloudflare_updated.default.dns.account_id') ?? config('services.cloudflare.dns.account_id'),
        };
    }

    function getZone()
    {
        return $this->client->get('/client/v4/zones/'. $this->zoneID )->json();
    }

    function status()
    {
        return $this->client->get('/client/v4/user/tokens/verify')->json();
    }

    function updateOrCreateRecord(string $name, string $content)
    {
        dump('updateOrCreateRecord'.' : '.$name.' : '.$content);

        dump('Api Key: ' . $this->token , 'zoneId: ' . $this->zoneID);

        $record = $this->dnsRecords(name: $name);

        if ($id = Arr::get($record, 'result.0.id')) {
            return $this->updateDnsRecord($id, $name, $content);
        } else {
            return $this->addDnsRecord($name, $content);
        }
    }

    function dnsRecords($type = 'A', $name = null)
    {
        // dump('dnsRecords'.' : '.$type.' : '.$name);

        $query = [
            'type' => $type,
        ];

        if ($name) {
            $query['name'] = $name;
        }

        return $this->client->get(
            '/client/v4/zones/'. $this->zoneID .'/dns_records',
            $query
        )->json();
    }

    function addDnsRecord(
        $name,
        $content,
        $type = 'A',
        $proxied = true,
        $comment = 'Domain verification record added via API',
    ) {
        dump('addDnsRecord'.' : '.$type.' : '.$name.' : '.$content);

        return $this->client->post('/client/v4/zones/'. $this->zoneID .'/dns_records', [
            'type' => $type,
            'name' => $name,
            'content' => $content,
            'proxied' => $proxied,
            'comment' => $comment,
            'priority' => 10,
            'tags' => [],
            'ttl' => 3600,
        ])->json();
    }

    function updateDnsRecord($id, $name, $content)
    {
        dump('updateDnsRecord'.' : '.$id.' : '.$name.' : '.$content);

        return $this->client->patch('/client/v4/zones/'. $this->zoneID .'/dns_records/'.$id, [
            'name' => $name,
            'content' => $content,
        ])->json();
    }

    function delete(string $name)
    {
        $record = $this->dnsRecords(name: $name);

        if ($id = Arr::get($record, 'result.0.id')) {
            // dump('deleting dns record'.' : '.$id.' : '.$name);
            return $this->client->delete('/client/v4/zones/' . $this->zoneID . '/dns_records/' . $id);
        }
    }

    function saveAllDnsRecordsToTxtFile(): void
    {
        $page = 1;
        $per_page = 100;
        $total_pages = 1; // Initialize with 1 to enter the loop

        $records = [];
        while ($page <= $total_pages) {
            $response = $this->client->get(
                '/client/v4/zones/'. $this->zoneID .'/dns_records?page=' . $page . '&per_page=' . $per_page
            )->json();

            // Save the DNS records to a file
            $dnsRecords = Arr::get($response, 'result', []);
            $dnsRecords = collect($dnsRecords)->map(function ($record) {
                return $record['name'] . PHP_EOL;
            })->toArray();

            $records[] = $dnsRecords;

            // Update pagination
            $total_pages = $response['result_info']['total_pages'] ?? $total_pages;
            $page++; // Move to the next page
        }

        $dnsRecords = Arr::flatten($records);

        file_put_contents(storage_path('cloudflare-dns/cloudflare-dns-records.txt'), $dnsRecords);
    }
}
