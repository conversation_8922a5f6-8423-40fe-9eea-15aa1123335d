<?php

namespace App\Services\Slack;

use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SlackNotificationService
{
    private string $slackPostMessageUrl;
    private string $slackOAuthToken;
    private string $slackChannelId;
    private string $reportingTimezone;
    private StatisticsFormatter $formatter;

    public function __construct(
        string $slackPostMessageUrl,
        string $slackOAuthToken,
        string $slackChannelId,
        string $reportingTimezone = 'Asia/Dhaka',
        StatisticsFormatter $formatter = null
    ) {
        $this->slackPostMessageUrl = $slackPostMessageUrl;
        $this->slackOAuthToken = $slackOAuthToken;
        $this->slackChannelId = $slackChannelId;
        $this->reportingTimezone = $reportingTimezone;
        $this->formatter = $formatter ?? new StatisticsFormatter();
    }

    public function createInitialBlock(): array
    {
        $currentDate = Carbon::now($this->reportingTimezone);
        $formattedDate = $currentDate->format('jS F, Y (l)');
        return $this->formatter->createSimpleSection("Here's the xCloud Daily Status Report for the `$formattedDate`:");
    }

    public function sendToSlack(array $blocks): void
    {
        try {
            Http::withHeaders([
                'Authorization' => "Bearer $this->slackOAuthToken",
            ])->asJson()->post($this->slackPostMessageUrl, [
                'channel' => $this->slackChannelId,
                'blocks' => $blocks,
            ]);
        } catch (\Exception $e) {
            Log::error('Slack notification sending failed', [
                'message' => $e->getMessage()
            ]);
        }
    }
}
