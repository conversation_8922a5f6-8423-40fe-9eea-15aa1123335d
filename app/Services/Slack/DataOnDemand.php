<?php

namespace App\Services\Slack;
use App\Enums\XcloudBilling\BillingServices;
use App\Models\Server;
use App\Services\CloudServices\Fetchers\Providers\VultrFetcher;

class DataOnDemand extends SlackBase
{
    protected VultrFetcher $vultr;

    public function __construct(string $vultrApiKey, string $slackPostMessageUrl, string $slackOAuthToken, string $slackChannelId)
    {
        parent::__construct($slackPostMessageUrl, $slackOAuthToken, $slackChannelId);
        $this->vultr = new VultrFetcher($vultrApiKey);
    }


    public function extractServerId($tags): int
    {
        try {
            foreach ($tags as $tag) {
                // Using regular expression to specifically match "server-" followed by an integer
                if (preg_match('/^server-(\d+)$/', $tag, $matches)) {
                    // Returning the integer part as an integer
                    return (int)$matches[1];
                }
            }
            return -1;
        } catch (\Exception $e) {
            return -1;
        }
    }

    public function matchServerTag($tags, $targetedTag): bool
    {
        foreach ($tags as $tag) {
            if ($tag == $targetedTag) {
                return true;
            }
        }
        return false;
    }


    public function getVultrInstanceCounts(): array
    {
        $allInstances = $this->vultr->getAllExistingServers();

        // Initialize counters and collections
        $counts = [
            'xcloud_managed' => ['count' => 0, 'server_ids' => []],
            'xcloud_provider' => ['count' => 0, 'server_ids' => []],
            'whitelabel' => ['count' => 0, 'server_ids' => []],
            'outside' => ['count' => 0, 'server_ids' => []],
        ];
        $serverIds = [];

        foreach ($allInstances as $instance) {
            $tags = $instance->getTags();
            $serverId = $this->extractServerId($tags);

            // Determine server type
            $type = match (true) {
                $this->matchServerTag($tags, BillingServices::xCloudProviderHosting->asSlug()) => 'xcloud_provider',
                $this->matchServerTag($tags, BillingServices::ManagedHosting->asSlug()) => 'whitelabel',
                $this->matchServerTag($tags, BillingServices::xCloudManagedHosting->asSlug()) => 'xcloud_managed',
                default => 'xcloud_managed', // some of the servers were created before the tag was introduced
            };

            // Increment counters and store server IDs
            if ($serverId == -1) {
                $counts['outside']['server_ids'][] = $instance->getId();
                $counts['outside']['count']++;
            } else {
                $counts[$type]['server_ids'][] = $serverId;
                $serverIds[] = $serverId;
                $counts[$type]['count']++;
            }
        }

        // Prepare final output
        return [
            'all_time_count' => count($allInstances),
            'server_ids' => $serverIds,
            'xcloud_managed_count' => $counts['xcloud_managed']['count'],
            'managed_server_ids' => $counts['xcloud_managed']['server_ids'],
            'xcloud_provider_count' => $counts['xcloud_provider']['count'],
            'provider_server_ids' => $counts['xcloud_provider']['server_ids'],
            'whitelabel_count' => $counts['whitelabel']['count'],
            'whitelabel_server_ids' => $counts['whitelabel']['server_ids'],
            'outside_servers_count' => $counts['outside']['count'],
            'outside_server_ids' => $counts['outside']['server_ids'],
        ];
    }

    private function findArrayDifferences(array $vultrServerIds, array $xCloudDatabaseServerIds): array
    {
        return [
            'in_vultr_not_in_xcloud' => array_diff($vultrServerIds, $xCloudDatabaseServerIds),
            'in_xcloud_not_in_vultr' => array_diff($xCloudDatabaseServerIds, $vultrServerIds),
        ];
    }

    private function createServerFieldsSection(Server $server, array $vultrData): array
    {
        // Usage for XCLOUD MANAGED
        $vultrManagedIds = $vultrData['managed_server_ids'];
        $xCloudManagedIds = $server->countXCloudHosted('managed', true, false);

        $managedDifferences = $this->findArrayDifferences($vultrManagedIds, $xCloudManagedIds);

        // Usage for XCLOUD PROVIDER
        $vultrProviderIds = $vultrData['provider_server_ids'];
        $xCloudProviderIds = $server->countXCloudHosted('provider', true, false);

        $providerDifferences = $this->findArrayDifferences($vultrProviderIds, $xCloudProviderIds);


        $sections = [
            'TOTAL UNDER XCLOUD (All TYPE)' => [
                'Vultr' => $vultrData['all_time_count'],
                'xCloud Database' => $server->countXCloudHosted(),
            ],
            'XCLOUD MANAGED' => [
                'Vultr' => $vultrData['xcloud_managed_count'],
//                'Vultr Server IDs' => implode(', ', $vultrData['managed_server_ids']),
                'xCloud Database' => $server->countXCloudHosted('managed'),
//                'xCLOUD Database Server IDs' => implode(', ', $server->countXCloudHosted('managed', true, false)),
//                'Unmatched Server IDs' => implode(', ', $server->findServersAndMissingIds($vultrData['managed_server_ids'])),
                'XCLOUD MANAGED Differences' => json_encode($managedDifferences),
            ],
            'XCLOUD PROVIDER' => [
                'Vultr Server Count' => $vultrData['xcloud_provider_count'],
//                'Vultr Server IDs' => implode(', ', $vultrData['provider_server_ids']),
                'xCloud Database' => $server->countXCloudHosted('provider'),
//                'xCLOUD Database Server IDs' => implode(', ', $server->countXCloudHosted('provider', true, false)),
//                'Unmatched Server IDs' => implode(', ', $server->findServersAndMissingIds($vultrData['provider_server_ids']))
                'XCLOUD PROVIDER Differences' => json_encode($providerDifferences),
            ],
            'WHITE LABEL' => [
                'Vultr' => $vultrData['whitelabel_count'],
                'xCloud Database' => $server->countXCloudHosted('whitelabel'),
                'Unmatched Server IDs' => implode(', ', $server->findServersAndMissingIds($vultrData['whitelabel_server_ids']))
            ],
//            'OUTSIDE SERVERS' => [
//                'Vultr' => $vultrData['outside_servers_count'],
//                'Instance IDs' => implode(', ', $vultrData['outside_server_ids']),
//            ],
        ];

        $serverFields = [];

        foreach ($sections as $title => $data) {
            // Format each section's details
            $content = "*{$title}: *\n\n";
            foreach ($data as $label => $value) {
                $content .= "• *{$label}:* {$value}\n\n";
            }

            // Add the section and divider
            $serverFields[] = $this->createSimpleSection($content);
            $serverFields[] = ["type" => "divider"];
        }

        // Remove the last unnecessary divider
        array_pop($serverFields);

        return $serverFields;
    }


    private function findDuplicateServerIds($serverIds): array
    {
        // Count the occurrences of each server ID in the array
        $idCounts = array_count_values($serverIds);

        // Filter out server ids that occur more than once
        $duplicateServerIDs = array_filter($idCounts, function($count) {
            return $count > 1;
        });

        // Get the array keys (original values) that are duplicates
        return array_keys($duplicateServerIDs);
    }

    private function generateServerDetails(Server $server, array $vultrData): array
    {
        $serverDetails = [
            $this->createHeaderSection("SERVER UPDATES :"),
            ...$this->createServerFieldsSection($server, $vultrData),
        ];

        // Helper to add sections for different server statuses
        $this->addServerStatusSection($serverDetails, [
            'Removed from xCloud but Present in Vultr' => $server->findServersAndMissingIds($vultrData['server_ids']),
            'Duplicate Server IDs on Vultr' => $this->findDuplicateServerIds($vultrData['server_ids']),
            'Servers not created by xCloud' => $vultrData['outside_server_ids'],
            'Disconnected xCloud Servers' => $server->getDisconnected()->pluck('id')->toArray(),
            'Deletion Failed xCloud Servers' => $server->getDeletionFailed()->pluck('id')->toArray(),
        ]);

        return $serverDetails;
    }

    private function addServerStatusSection(array &$details, array $statusData): void
    {
        foreach ($statusData as $label => $ids) {
            if (!empty($ids)) {
                $serversList = implode(', ', $ids);
                $details[] = ["type" => "divider"];
                $details[] = $this->createSimpleSection("*{$label}:* `{$serversList}` \n\n");
            }
        }
    }

    public function generateReport(): array
    {
        $vultrData = $this->getVultrInstanceCounts();

        $server = (new Server());
        $serverDetails = $this->generateServerDetails($server, $vultrData);

        // returning the combined array
        return array_merge(
            $serverDetails,
            [["type" => "divider"]],
        );

    }

    public function sendToSlack(array $blocks = []): void
    {
        // Generate the report if no blocks are passed
        if (empty($blocks)) {
            $blocks = $this->generateReport();
        }
        parent::sendToSlack($blocks);
    }

}

