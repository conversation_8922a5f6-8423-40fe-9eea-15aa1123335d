<?php

namespace App\Services\Slack;

use App\Models\Site;
use Carbon\Carbon;

class SiteStatisticsService extends AbstractStatisticsService
{
    private StatisticsFormatter $formatter;
    private Site $site;

    public function __construct(StatisticsFormatter $formatter, string $reportingTime = '08:00', string $reportingTimezone = 'Asia/Dhaka')
    {
        parent::__construct($reportingTime, $reportingTimezone);
        $this->formatter = $formatter;
        $this->site = (new Site())->setReportingParams($this->reportingTime, $this->reportingTimezone);
    }

    public function getNonSoftDeletedData($model, $emojiCreated='', $emojiDeleted=''): array
    {
        $modelName = class_basename($model); // Extracts the class name without the namespace
        $prefix = strtolower($modelName); // making it lowercase

        // Data of today from database
        $createdToday = $model->applyTimezoneAndTime()->count();
        $totalCreated = $model->count();

        // Data of last day from the cache
        $totalDeleted = $this->getFromCache($prefix . '_total_deleted');
        $totalCreatedLastDay = $this->getFromCache($prefix . '_total_created');

        $deletedToday = max(0,$totalCreatedLastDay + $createdToday - $totalCreated);
        $allTimeDeleted = $totalDeleted + $deletedToday;

        // Cache the updated values
        $this->updateCacheItem($prefix . '_total_deleted', $allTimeDeleted);
        $this->updateCacheItem($prefix . '_total_created', $totalCreated);

        $counts = [
            'created' => ['allTime' => $totalCreated, 'aDay' => $createdToday],
            'deleted' => ['allTime' => $allTimeDeleted, 'aDay' => $deletedToday],
        ];

        foreach ($counts as $key => &$value) {
            $value['displayADay'] = $this->formatDisplayCount($value['aDay']);
        }

        $result = "• {$emojiCreated} {$modelName} Created: {$counts['created']['allTime']} {$counts['created']['displayADay']} \n" .
            "• {$emojiDeleted} {$modelName} Deleted: {$counts['deleted']['allTime']} {$counts['deleted']['displayADay']}";

        return $this->formatter->createSimpleSection($result);
    }

    private function getUniqueDomainCount(): array
    {
        $reportingTime = Carbon::createFromFormat('H:i', $this->reportingTime, $this->reportingTimezone);
        $cutoffTime = $reportingTime->copy()->subDay()->setTimezone('UTC');

        $allTimeCount = Site::getUniqueDomainCount();

        $dayCount = $this->site->applyTimezoneAndTime()
            ->select('base_name')
            ->distinct()
            ->whereNotIn('base_name', function ($query) use ($cutoffTime) {
                $query->select('base_name')
                    ->from('sites')
                    ->where('created_at', '<', $cutoffTime);
            })
            ->count('base_name');
    
        $displayCount = $this->formatDisplayCount($dayCount);

        return $this->formatter->createSimpleSection("• :unicorn_face: Unique Domain Count: {$allTimeCount} {$displayCount}");
    }

    public function generateSiteDetails(): array
    {
        return [
            $this->formatter->createHeaderSection("SITE UPDATES:"),
            $this->getNonSoftDeletedData($this->site, ":page_facing_up:", ":small_red_triangle_down:"),
            $this->getUniqueDomainCount()
        ];
    }
}
