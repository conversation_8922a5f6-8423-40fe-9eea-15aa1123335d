<?php

namespace App\Services\Slack;

use Illuminate\Support\Facades\Cache;

abstract class AbstractStatisticsService
{
    protected string $reportingTime;
    protected string $reportingTimezone;
    protected array $cacheItems;

    public function __construct(string $reportingTime = '08:00', string $reportingTimezone = 'Asia/Dhaka')
    {
        $this->reportingTime = $reportingTime;
        $this->reportingTimezone = $reportingTimezone;
        $this->cacheItems = Cache::get('daily_statistics_previous_day_data', []);
    }

    protected function getFromCache($key)
    {
        if (array_key_exists($key, $this->cacheItems)) {
            return $this->cacheItems[$key];
        }
        return 0;
    }

    protected function formatDisplayCount(int $count): string
    {
        return $count > 0 ? "(`+{$count}`)" : "";
    }

    public function getCacheItems(): array
    {
        return $this->cacheItems;
    }

    public function setCacheItems(array $cacheItems): void
    {
        $this->cacheItems = $cacheItems;
    }

    public function updateCacheItem(string $key, $value): void
    {
        $this->cacheItems[$key] = $value;
    }
}
