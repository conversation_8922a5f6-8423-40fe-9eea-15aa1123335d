<?php

namespace App\Services;

use App\Contracts\Loggable;
use App\Models\Campaign;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class Logger
{
    private string $logDriver = 'daily';

    public function __construct(public string $requestId, public Loggable|null $model = null)
    {
        $modelKey = str(get_class($model))->explode('\\')->last().':'.$model?->getKey();

        if (str($requestId)->explode('\\')->last() == $modelKey) {
            $this->requestId = $modelKey;
        } else {
            $this->requestId = str($requestId)->explode('\\')->last().':'.$modelKey;
        }
    }

    function log($message)
    {
        if (is_array($message)) {
            $message = json_encode($message);
        }

        if (app()->runningInConsole()) {
            echo $this->requestId.': '.$message.PHP_EOL;
        }

        Log::log('info', $this->requestId.': '.$message);
    }
}
