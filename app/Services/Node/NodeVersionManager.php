<?php

namespace App\Services\Node;

use App\Models\NodeVersion;
use App\Models\Server;
use App\Scripts\Node\EnsureNodeIsInstalled;
use App\Scripts\Node\InstallNodeVersion;
use App\Scripts\Node\SetDefaultNodeVersion;
use App\Scripts\Node\UninstallNodeVersion;

class NodeVersionManager
{
    /**
     * Server we are working with
     *
     * @var Server
     */
    private $server;

    /**
     * @param  Server  $server
     */
    public function __construct(Server $server)
    {
        $this->server = $server;
    }

    function install($version, $setDefault = false): \App\Models\Task
    {
        $nodeVersion = $this->server->nodeVersions()->updateOrCreate([
            'node_version' => $version
        ], [
            'status' => 'installing',
            'node_version_updated_at' => now()->toDateTimeString(),
        ]);

        return $this->server->runInBackground(new InstallNodeVersion($this->server, $version, $setDefault), [
            'then' => [
                new \App\Callbacks\UpdateNodeVersionInstallStatus($this->server->id, $version),
            ],
        ]);
    }

    function installSync($version, $setDefault)
    {
        $nodeVersion = $this->server->nodeVersions()->updateOrCreate([
            'node_version' => $version
        ], [
            'status' => 'installing',
            'node_version_updated_at' => now()->toDateTimeString(),
        ]);

        $task = $this->server->run(new InstallNodeVersion($this->server, $version, $setDefault));

        $nodeVersion->update([
            'status' => $task->successful() ? 'installed' : 'failed'
        ]);
    }

    function uninstall($version)
    {
        $nodeVersion = $this->server->nodeVersions()->updateOrCreate([
            'node_version' => $version
        ], [
            'status' => 'uninstalling',
            'node_version_updated_at' => now()->toDateTimeString(),
        ]);

        $this->server->runInBackground(new UninstallNodeVersion($this->server, $version), [
            'then' => [
                new \App\Callbacks\UpdateNodeVersionUninstallStatus($this->server->id, $version),
            ],
        ]);
    }

    function isInstalled($version): bool
    {
        return $this->server->node_version === $version || $this->server->nodeVersions()->where('node_version', $version)->where('status', 'installed')->exists();
    }

    function ensureInstalled($version): bool
    {
        return $this->isInstalled($version) && $this->server->runInline(new EnsureNodeIsInstalled($this->server, $version))->successful();
    }

    function update($version, $status)
    {
        $this->server->nodeVersions()->updateOrCreate([
            'node_version' => $version
        ], [
            'status' => $status,
            'node_version_updated_at' => now()->toDateTimeString(),
        ]);
    }

    function setDefault($version)
    {
        if ($this->server->runInline(new SetDefaultNodeVersion($this->server, $version))->successful()) {
            $this->server->update([
                'node_version' => $version,
            ]);
        }
    }

    function remove($version)
    {
        if ($nodeVersion = $this->server->nodeVersions()->where('node_version', $version)->first()) {
            $nodeVersion->delete();
        }
    }
}
