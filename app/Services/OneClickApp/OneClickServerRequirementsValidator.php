<?php

namespace App\Services\OneClickApp;

use App\Enums\SiteType;
use App\Enums\XcloudBilling\PlansEnum;
use App\Models\Server;
use Illuminate\Http\RedirectResponse;

class OneClickServerRequirementsValidator
{
    /**
     * Validate if the server meets the requirements for one-click apps
     *
     * @param  Server  $server  The server to validate
     * @return RedirectResponse|null Returns a redirect response if validation fails, null otherwise
     */
    public function validate(Server $server): ?RedirectResponse
    {
        // Check Ubuntu version requirement
        if ($response = $this->isOneClickAppActive($server)) {
            return $response;
        }

        // Check Ubuntu version requirement
        if ($response = $this->validateUbuntuVersion($server)) {
            return $response;
        }

        // Check if server is on a free plan
        if ($response = $this->validateNotFreePlan($server)) {
            return $response;
        }

        // Check if phpMyAdmin is already installed when trying to install another one
        if ($response = $this->validatePhpMyAdminNotInstalled($server)) {
            return $response;
        }

        // Check if Node.js version is compatible with n8n
        if ($response = $this->validateNodeVersionForN8n($server)) {
            return $response;
        }

        // All validations passed
        return null;
    }

    /**
     * Validate that the server is running a supported Ubuntu version
     *
     * @param  Server  $server  The server to validate
     * @return RedirectResponse|null Returns a redirect response if validation fails, null otherwise
     */
    protected function validateUbuntuVersion(Server $server): ?RedirectResponse
    {
        if (version_compare($server->ubuntu_version, '22.04', '<')) {
            return back()->with('flash', value: [
                'message' => 'Unsupported Ubuntu Version',
                'body' => "Your server is running an older version of Ubuntu. To use one-click apps, please use a server with Ubuntu 22.04 or newer.",
                'type' => 'error',
                'popup' => true
            ]);
        }

        return null;
    }

    /**
     * Validate that the server is not on a free plan
     *
     * @param  Server  $server  The server to validate
     * @return RedirectResponse|null Returns a redirect response if validation fails, null otherwise
     */
    protected function validateNotFreePlan(Server $server): ?RedirectResponse
    {
        $latestBill = $server->bills()->where('service', $server->getDefaultBillingService())->orderBy('id', 'desc')->first();

        if ($latestBill && $latestBill->billingPlan->name === PlansEnum::Free) {
            return back()->with('flash', value: [
                'message' => 'Unavailable on Free Plan',
                'body' => "One-click apps are not available on the free plan. Please upgrade to a paid plan to use this feature.",
                'type' => 'error',
                'popup' => true
            ]);
        }

        return null;
    }

    /**
     * Validate that the server doesn't already have a phpMyAdmin site installed
     * when trying to install another one
     *
     * @param  Server  $server  The server to validate
     * @return RedirectResponse|null Returns a redirect response if validation fails, null otherwise
     */
    protected function validatePhpMyAdminNotInstalled(Server $server): ?RedirectResponse
    {
        // Get the requested app slug from the request
        $requestedAppSlug = request()->input('app_slug');

        // Only perform this validation if the user is trying to install phpMyAdmin
        if ($requestedAppSlug === 'phpmyadmin') {
            // Check if phpMyAdmin is already installed on this server
            $existingPhpMyAdmin = $server->getPhpMyAdminSite();

            if ($existingPhpMyAdmin) {
                return back()->with('flash', value: [
                    'message' => 'phpMyAdmin Already Installed',
                    'body' => "This server already has phpMyAdmin installed. You can only have one phpMyAdmin installation per server.",
                    'type' => 'info',
                    'popup' => true
                ]);
            }
        }

        return null;
    }

    /**
     * Validate that the server has a compatible Node.js version for n8n
     * n8n requires Node.js version 18 or higher
     *
     * @param  Server  $server  The server to validate
     * @return RedirectResponse|null Returns a redirect response if validation fails, null otherwise
     */
    protected function validateNodeVersionForN8n(Server $server): ?RedirectResponse
    {
        // Get the requested app slug from the request
        $requestedAppSlug = request()->input('app_slug');

        // Only perform this validation if the user is trying to install n8n
        if ($requestedAppSlug === 'n8n') {
            // If node_version is null, try to get it from the server
            if ($server->node_version === null) {
                $server->getNodeVersions();
            }

            // Check if Node.js version is compatible with n8n (needs version 18 or higher)
            if ($server->node_version === null || version_compare($server->node_version, '18', '<')) {
                return back()->with('flash', value: [
                    'message' => 'Incompatible Node.js Version',
                    'body' => "n8n requires Node.js version 18 or higher. Your server is currently using ".
                        ($server->node_version ?? 'an unknown')." version of Node.js. Please update your Node.js version before installing n8n.",
                    'type' => 'error',
                    'popup' => true
                ]);
            }
        }

        return null;
    }

    /**
     * Check if the one-click app feature is active for the server
     *
     * @param  Server  $server  The server to check
     * @return RedirectResponse|null Returns a redirect response if the feature is not active, null otherwise
     */
    protected function isOneClickAppActive(Server $server): ?RedirectResponse
    {
        if (!collect(SiteType::getOneClickApps())->where('is_active', true)->where('slug', request()->input('app_slug'))->first()) {
            return back()->with('flash', value: [
                'message' => 'One-Click App Not Available',
                'body' => "The requested one-click app is not available on this server. Please check the app slug or contact support for assistance.",
                'type' => 'error',
                'popup' => true
            ]);
        }

        return null;
    }
}
