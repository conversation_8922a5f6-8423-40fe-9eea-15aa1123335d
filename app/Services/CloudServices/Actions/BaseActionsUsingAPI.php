<?php

namespace App\Services\CloudServices\Actions;

use App\Enums\ServerModificationActions;
use App\Enums\ServerStatus;
use App\Jobs\Server\ServerWaitingForModification;
use App\Models\Task;
use App\Services\Server\Api\ServerModification\ServerModificationActionFactory;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

abstract class BaseActionsUsingAPI implements ActionsUsingAPIInterface {

    protected Model $model;
    protected Task $task;
    protected $providerClient;

    public function __construct(Model $model, ?Task $task = null) {
        $this->model = $model;
        $this->task = $task;
        $this->providerClient = $this->getCloudProviderClient();

    }

    abstract public function getCloudProviderClient();
    public function addTaskLog($text): void
    {
        // This will be visible to admins, not users on Event log
        $this->task?->update([
            'script' => ($this->task->script ? $this->task->script . "\n" : "") . $text
        ]);
    }

    public function addTaskOutput($text): void
    {
        // This will be visible to users on Event log
        $this->task?->update([
            'output' => ($this->task->output ? $this->task->output . "\n" : "") . $text
        ]);
    }

    public function getName(): string
    {
        return $this->model->name_slug.'-'.$this->model->id;
    }

    public function getSize(): string
    {
        return $this->model->size;
    }

    public function getRegion(): string
    {
        return $this->model->region;
    }

    public function getZone(): string
    {
        return $this->model->zone;
    }

    public function getBackupSetting(): bool
    {
        return $this->model?->backups;
    }

    public function getSSHKeyData($username = null): array
    {
        return [
            'key_username' => $username ?? 'xcloud_s'.$this->model->id,
            'public_ssh_key' => $this->model->getPublicSshKey(),
        ];

    }

    public function getSudoPassword(): string
    {
        return $this->model?->sudo_password;
    }

    public function getTags(): array
    {
        $tags = $this->model->tags->pluck('name')->merge([
            "xCloud",
            $this->model->getDefaultBillingService()->asSlug(),
            "team-{$this->model->team_id}",
            "user-{$this->model->user_id}",
            "server-{$this->model->id}"
        ])->toArray();

        if (!app()->environment('production')  && !app()->runningUnitTests()) {
            $tags[] = gethostname();
            $tags[] = app()->environment(). (app()->environment('local') ? ' (DB:'. DB::connection()->getDatabaseName().')' : '');
            $tags[] = $this->model?->user?->email;
            $tags[] = Config::get('app.url');
        }

        if (empty($tags))  return [];

        return $tags;
    }


    public function getResourceId() : ?string
    {
        return $this->model?->resource_id;
    }


    /**
     * @throws Exception
     */
    public function performAction(string $actionMethod, array $actionStatus): void
    {
        if (!method_exists($this, $actionMethod)) {
            throw new Exception("Method not found: " . $actionMethod);
        }
        $this->updateData(['status' => data_get($actionStatus, 'start')]);
        try {
            $this->$actionMethod(); // Dynamically calling the action method
        } catch (\Exception $e) {
            $this->errorHandlingPart($e, data_get($actionStatus, 'error'));
        }
    }

    public function errorHandlingPart(Exception $e, $status): void
    {
        $errorMessageForUser = $this->model->isXCloudOrWhiteLabel() ? 'Something went wrong, please contact support' :  $e->getMessage();
        // Server status and log update
        $this->updateData([
            'status' => $status,
            'log' => $errorMessageForUser
        ]);

        // Event log for user
        $this->addTaskOutput($errorMessageForUser);

        // Event log for admin
        $this->addTaskLog(sprintf(
            'LINE: %d - [METHOD: %s]\nERROR:[%s]\nException thrown in %s on line %d',
            __LINE__,
            __METHOD__,
            json_encode($e->getMessage(), true),
            $e->getFile(),
            $e->getLine()
        ));
    }


    public function updateData(array $data): void
    {
        if(!empty($data)) $this->model->update($data);
    }

    public function getImage(): string
    {
        return $this->model->server_image;
    }

    abstract protected function serverCreate(): void;

    /**
     * @throws Exception
     */
    protected function serverUpdate(): void
    {
//        it will be called on performAction() dynamically

        $metaData = Arr::get($this->model->meta, 'serverModification');
        foreach($metaData as $key => $actionData){
            $actionEnum = ServerModificationActions::tryFrom($key);
            $action = ServerModificationActionFactory::getService($actionEnum, $this->model);
            try {
                $actionFunction = $action->getFunctionName();
                $this->$actionFunction(); // we are getting function name dynamically from respective action class

                // if this action  needs async wait then we will dispatch the job otherwise work will be done here immediately
                if($action->isAsync()){
                    $this->addTaskLog('Dispatching job that waits and checks for the instance status: ' . $actionEnum->value);
                    // dispatch job that will wait and check for the instance status and update server status accordingly
                    ServerWaitingForModification::dispatch($this->model, $this->getCredential() , $action);
                } else {
                    // immediate work
                    $action->onSuccessWork();
                    $this->updateData(['status' => ServerStatus::PROVISIONED]);
                    $this->addTaskLog('Successfully done modifying the instance : ' . $actionEnum->value);
                }
            } catch (Exception $e) {
                // on immediate error/exception
                $serverStatus = $action->onFailedWork();
                $this->errorHandlingPart($e, $serverStatus);
            }
        }
    }

    abstract protected function serverResize(): void;

    abstract protected function serverBackup(): void;

    abstract protected function serverDelete(): void;

    abstract protected function databaseCreate(): void;
    abstract protected function databaseUpdate(): void;
    abstract protected function databaseDelete(): void;

    abstract protected function databaseClusterCreate(): void;
    abstract protected function databaseClusterUpdate(): void;
    abstract protected function databaseClusterDelete(): void;

}
