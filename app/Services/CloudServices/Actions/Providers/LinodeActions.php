<?php

namespace App\Services\CloudServices\Actions\Providers;

use App\Enums\CloudProviderEnums;
use App\Enums\ServerStatus;
use App\Models\Server;
use App\Models\Task;
use App\Providers\CloudProviders\OAuthProviderFactory;
use App\Services\CloudServices\Actions\BaseActionsUsingAPI;
use App\Services\CloudServices\Fetchers\Providers\LinodeFetcher;
use Exception;
use Linode\Entity\Linode;
use Linode\Exception\LinodeException;
use Linode\LinodeClient;

class LinodeActions extends BaseActionsUsingAPI {

    private Server $server;
    private LinodeFetcher $linodeFetcher;

    public function __construct(Server $server, ?Task $task = null) {
        $this->server = $server;
        $this->linodeFetcher = (new LinodeFetcher($this->getCredential()));
        parent::__construct($server, $task);
    }

    public function getCloudProviderClient(): LinodeClient
    {
        return new LinodeClient($this->getCredential());
    }

    public function getCredential(): string
    {
        return $this->server->cloudProvider?->getAccessToken();
    }

    /**
     * @throws Exception
     */
    protected function serverCreate(): void
    {
        $this->addTaskLog('In serverCreate()');

        $sshKeyData = $this->getSSHKeyData();

        $serverCreate = $this->providerClient->linodes->create([
            Linode::FIELD_LABEL => $this->getName(),
            Linode::FIELD_TYPE => $this->getSize(),
            Linode::FIELD_IMAGE => $this->getImage(),
            Linode::FIELD_REGION => $this->getRegion(),
            Linode::FIELD_ROOT_PASS => $this->getSudoPassword(),
//            Linode::FIELD_AUTHORIZED_USERS => [$this->server->name_slug."@xcloud"],
            Linode::FIELD_AUTHORIZED_KEYS => [$sshKeyData['public_ssh_key']],
            Linode::FIELD_BACKUPS_ENABLED => $this->getBackupSetting(),
            Linode::FIELD_TAGS => $this->getTags(),
        ]);

        $instanceId = $serverCreate->id;

        // hold and wait till server starts
        $ip = $this->waitUntilInstanceSettles($instanceId);

        $data = [
            'server_info' => $serverCreate->toArray(),
            'resource_id' => $instanceId,
            'public_ip' => $ip,
//            'private_ip' => $serverCreate->getInternalIp(),
            'status' => ServerStatus::CREATED
        ];

        $this->addTaskLog('Saving Server Details in DB : ' . json_encode($data));
        $this->updateData($data);
    }


    /**
     * @throws Exception
     */
    private function waitUntilInstanceSettles(string $instanceId): mixed
    {
        $this->addTaskLog('In waitUntilInstanceSettles()');
        $startTime = time();
        $timeout = 600; // 10 minutes in seconds

        while (true) {
            // checking if we have exceeded our timeout (10 minutes)
            if (time() - $startTime > $timeout) {
                throw new Exception('Timeout reached while waiting for the instance to settle.');
            }

            $isInstanceRunning = $this->linodeFetcher->isInstanceRunning($instanceId);
            // checking for the fully started status
            if($isInstanceRunning) {
                // fetching current instance details
                $instanceInfo = $this->linodeFetcher->getInstanceDetails($instanceId);
                $this->addTaskLog('Instance has started InstanceInfo : ' . json_encode($instanceInfo));
                // Instance has fully started, returning the ip
                return $instanceInfo->ipv4[0];
            }

            $this->addTaskLog('Waiting for the server to get settled');

            // If not fully started, wait for 10 seconds before trying again
            sleep(10);
        }
    }

    protected function serverResize(): void
    {
        // TODO: Implement serverResize() method.
    }

    protected function serverBackup(): void
    {
        // TODO: Implement serverBackup() method.
    }


    /**
     * @throws Exception
     */
    protected function serverDelete(): void
    {
        $this->addTaskLog('In serverDelete()');
        try {
            $this->deleteAction();
        } catch (LinodeException $e) {
            try {
                $oauthProvider = (new OAuthProviderFactory)->getProvider(CloudProviderEnums::LINODE);
                $oauthProvider->checkAuthTokenAndRefresh($e, $this->server->cloudProvider);
                $this->providerClient = $this->getCloudProviderClient(); // updating the provider client with new access token
                $this->deleteAction(); // now deleting the instance
                $this->handleGeneralException($e);
            } catch (LinodeException $e) {
                // if it still has issues then throw the exception
                $this->handleGeneralException($e);
            }
        }
    }

    /**
     * @throws LinodeException
     */
    private function deleteAction(): void
    {
        $this->providerClient->linodes->delete($this->getResourceId());
        $this->addTaskLog('Deleting the server from the Provider ');
        $this->linodeFetcher->getInstanceDetails($this->getResourceId());

    }

    /**
     * Handles exceptions during server deletion.
     *
     * @param Exception $e
     */
    private function handleGeneralException(Exception $e): void
    {
        $this->addTaskLog('Error When Deleting ' . json_encode($e->getCode()));

        if ($e->getCode() === 404) {
            $this->addTaskLog('Not found! Deleted already');
            $this->updateData(['status' => ServerStatus::DELETED]);
        }
    }

    protected function databaseCreate(): void
    {
        // TODO: Implement databaseCreate() method.
    }

    protected function databaseUpdate(): void
    {
        // TODO: Implement databaseUpdate() method.
    }

    protected function databaseDelete(): void
    {
        // TODO: Implement databaseDelete() method.
    }

    protected function databaseClusterCreate(): void
    {
        // TODO: Implement databaseClusterCreate() method.
    }

    protected function databaseClusterUpdate(): void
    {
        // TODO: Implement databaseClusterUpdate() method.
    }

    protected function databaseClusterDelete(): void
    {
        // TODO: Implement databaseClusterDelete() method.
    }
}
