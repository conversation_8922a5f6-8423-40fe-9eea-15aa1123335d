<?php

namespace App\Services\CloudServices\Validations\Providers;

use App\Services\CloudServices\Validations\BaseValidationsUsingAPI;
use DigitalOceanV2\Client as DigitalOceanClient;
use DigitalOceanV2\Exception\ExceptionInterface;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class DigitalOceanValidation extends BaseValidationsUsingAPI {
    use AuthorizesRequests;

    /**
     * @throws ExceptionInterface
     */
    public function validateClient(string $credential): DigitalOceanClient
    {
        $client = new DigitalOceanClient();
        $client->authenticate($credential);
        $client->droplet()->getAll();
        return $client;
    }

}
