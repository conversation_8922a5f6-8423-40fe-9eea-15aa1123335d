<?php

namespace App\Services\CloudServices\Validations\Providers;

use App\Enums\CloudProviderAuthStatus;
use App\Models\CloudProvider;
use App\Services\CloudServices\Validations\BaseValidationsUsingAPI;
use Exception;
use Google\ApiCore\ApiException;
use Google\Cloud\Compute\V1\ProjectsClient;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class GCPValidation extends BaseValidationsUsingAPI {
    use AuthorizesRequests;


    /**
     * Overrides BaseValidationsUsingAPI::validateSaveOrUpdateRequest(): array
     * This is validating request parameter when saving or updating GCP Credential.
     *
     * @return array
     * @throws ValidationException
     */
    protected function validateSaveOrUpdateRequest(): array
    {
//        this is validating request parameter when saving or updating an API key for a provider
        $rules = [
            'label' => ['regex:/^[a-z][-a-z0-9]+$/'], // here label is project_id in GCP Credential
            'api_key' => ['required'],   // it is base64 encoded GCP Credential, a JSON string
            'provider_id' => ['nullable', 'integer'],
            'doNotRedirect' => ['nullable', 'boolean'], // had to use it to add credential from provider credential
            // managing page this will help to prevent redirection and return success message in json
        ];

        return Validator::make($this->request->all(), $rules)->validate();
    }


    /**
     * Overrides BaseValidationsUsingAPI::validateClient(string $credential): void
     * This method is used to validate the GCP client with credential.
     *
     * @param string $credential
     * @return void
     * @throws ValidationException
     * @throws \Google\ApiCore\ValidationException|ApiException
     *
     */
    public function validateClient(string $credential): void
    {
        $gcpCredential = json_decode($credential, true);

        $gcpValidationException = ValidationException::withMessages([
            'title' => 'Authorization Failed',
            'message' => 'Unable to connect to Google Cloud. Please check your API token and try again.',
            'api_token' => 'Authorization Failed.'
        ]);

        if (json_last_error() === JSON_ERROR_NONE && isset($gcpCredential['project_id'])) {
            // it is service account credentials
            $projectId = $gcpCredential['project_id'];

            $projectClient = new ProjectsClient([
                'credentials' => $gcpCredential
            ]);

            if(!$projectClient->get($projectId)->getName()){
                throw $gcpValidationException;
            }
        } else {
            throw $gcpValidationException;
        }
    }


    /**
     *
     * Overrides BaseValidationsUsingAPI::validationSaveOrUpdateAction(array $validated): RedirectResponse
     * Main logic, it will be used call validation and save/update the GCP Credential and then return the response accordingly.
     *
     * @param array $validated
     * @return RedirectResponse
     * @throws ValidationException
     */
    protected function validationSaveOrUpdateAction(array $validated): RedirectResponse
    {
//        Main logic, it will be used to validate and save an API key for the first time for a provider.
        try {
            $provider = $this->determineProvider($validated);
//            if provider_is is not given then are working on newly given API Key
            $apiKey = $provider ? $provider->gcp_access : $validated['api_key']; // here is the main difference with base class, all the other provider is using 'api_key but GCP is using gcp_access

            $apiKey = base64_decode($apiKey); // decoding the base64 encoded API Key
            $validated['api_key'] = json_decode($apiKey); // updating the validated array with decoded API Key
            //if $apiKey is empty, then it will throw an exception
            if (!$apiKey) throw $this->handleExceptionWeb("API Key is empty");

            $this->validateClient($apiKey); // Validate the client with the API key.

            if (!$provider) {
//                we are saving the new API Key into DB and getting the object of CloudProvider Object
                $provider = $this->createOrUpdateProvider($validated);
            }

            // if return back is needed. Check $validated['doNotRedirect'] and return back
            if (isset($validated['doNotRedirect']) && $validated['doNotRedirect']) {
               return back();
            }

            return $this->successResponseWeb($provider);
        } catch (Exception $e) {
            throw $this->handleExceptionWeb($e->getMessage());
        }
    }

    /**
     * Overrides BaseValidationsUsingAPI::createOrUpdateProvider(array $validated): CloudProvider
     * This method is used to create or update the GCP Provider.
     *
     * @param array $validated
     * @return CloudProvider
     */
    protected function createOrUpdateProvider(array $validated): CloudProvider {

        return CloudProvider::updateOrCreate([
                'provider' => $this->cloudProviderEnum,
                'name' => $validated['label'] // here label is project_id in GCP Credential
            ], [
                'gcp_access' => $validated['api_key'], // whole GCP Credential in api_key
                'user_id' => user()->id,
                'auth_status' => CloudProviderAuthStatus::AUTHENTICATED
            ]
        );
    }

}
