<?php

namespace App\Services\CloudServices\Validations\Providers;

use App\Models\CloudProvider;
use App\Services\CloudServices\Validations\BaseValidationsUsingAPI;
use Exception;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use LKDev\HetznerCloud\APIException;
use LKDev\HetznerCloud\HetznerAPIClient;

class HetznerValidation extends BaseValidationsUsingAPI {
    use AuthorizesRequests;

    /**
     * @throws APIException
     * @throws Exception
     */
    public function validateClient(string $credential): HetznerAPIClient
    {
        // main validation logic for specific provider
        $hetznerClient = new HetznerAPIClient($credential);
        $hetznerClient->prices()->all();  // Validation step
        return $hetznerClient;
    }

    protected function parseErrorMessage(string $message): string
    {
        // Pattern to match JSON objects; adjusts according to the actual JSON structure
        $jsonPattern = '/\{(?:[^{}]|(?R))*}/';

        // Attempt to find JSON within the string
        if (preg_match($jsonPattern, $message, $matches)) {
            // Attempt to decode the first matching JSON object
            $decoded = json_decode($matches[0], true);

            // Check if 'error' and 'message' keys exist in the decoded JSON
            if (isset($decoded['error']['message']) && is_array($decoded)) {
                return ucfirst($decoded['error']['message']);
            }
        }

        return $message;
    }

    //    JSON API Starts from here
    /**
     * @throws ValidationException
     * @throws AuthorizationException
     * @throws Exception
     */
    public function checkCredentialValidity(): RedirectResponse {
        //        This will be called from other classes to ONLY validate and return json Success
        $validated = $this->checkCredentialValidityRequest();
        $provider = CloudProvider::where('id', $validated['provider_id'])->where('team_id', team()->id)->firstOrFail();
        $this->authorize('view', $provider);

        if ($validated['is_edit']) {
            return $this->checkEditValidityAction($validated['api_key']);
        }

        return $this->checkValidityAction($provider);
    }

    /**
     * @throws ValidationException
     */
    protected function checkCredentialValidityRequest(): array
    {
        //        only to check if existing credential is working
        $rules = [
            'provider_id' => ['required', 'exists:cloud_providers,id'],
            'api_key' => 'required|max:255',
            'is_edit' => 'nullable|boolean'
        ];

        return Validator::make($this->request->all(), $rules)->validate();
    }
}
