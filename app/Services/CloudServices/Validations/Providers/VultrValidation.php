<?php

namespace App\Services\CloudServices\Validations\Providers;

use App\Models\CloudProvider;
use App\Services\CloudServices\Validations\BaseValidationsUsingAPI;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Vultr\VultrPhp\Services\Instances\InstanceException;
use Vultr\VultrPhp\VultrClient;

class VultrValidation extends BaseValidationsUsingAPI {
    use AuthorizesRequests;

    /**
     * @throws InstanceException
     */
    protected function validateClient(string $credential): VultrClient
    {
//        main validation logic for specific provider
        $vultrClient = VultrClient::create($credential);
        $vultrClient->instances->getInstances(); // Validation step
        return $vultrClient;
    }

    protected function parseErrorMessage(string $message): string
    {
//        we are using vultr for xcloud managed and provider, that's why we are customizing the error message
        $prefix = 'GET failed:';
        $startPos = strpos($message, $prefix);
        if ($startPos !== false) {
            $errorMessage = trim(substr($message, $startPos + strlen($prefix)));
        } else {
            $errorMessage = 'Invalid API Token';
        }

        if (str_contains($errorMessage, "Unauthorized IP address")) {
            $additionalInfo = "Please make sure you have added both \"Any IPv4\" and \"Any IPv6\" under Access Control section on Vultr.";
            $errorMessage .= ". " . $additionalInfo;
        }

        return $errorMessage;
    }

    //    JSON API Starts from here
    /**
     * @throws ValidationException
     * @throws AuthorizationException
     * @throws Exception
     */
    public function checkCredentialValidity(): RedirectResponse {
        //        This will be called from other classes to ONLY validate and return json Success
        $validated = $this->checkCredentialValidityRequest();
        $provider = CloudProvider::where('id', $validated['provider_id'])->where('team_id', team()->id)->firstOrFail();
        $this->authorize('view', $provider);

        if ($validated['is_edit']) {
            return $this->checkEditValidityAction($validated['api_key']);
        }

        return $this->checkValidityAction($provider);
    }

    /**
     * @throws ValidationException
     */
    protected function checkCredentialValidityRequest(): array
    {
        //        only to check if existing credential is working
        $rules = [
            'provider_id' => ['required', 'exists:cloud_providers,id'],
            'api_key' => 'required|max:255',
            'is_edit' => 'nullable|boolean'
        ];

        return Validator::make($this->request->all(), $rules)->validate();
    }
}
