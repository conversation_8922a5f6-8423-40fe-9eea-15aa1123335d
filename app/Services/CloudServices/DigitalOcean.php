<?php

namespace App\Services\CloudServices;

use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class DigitalOcean
{

    /**
     * @throws RequestException
     */
    function getSizes(): array
    {
        $response = Http::digitalocean()->get('/sizes', [
            'per_page' => 200
        ]);
        $response->throw();

        array_filter($response['sizes'], function ($v, $k) {
            return $v['available'];
        }, ARRAY_FILTER_USE_BOTH);

        return $response['sizes'];
    }

    /**
     * @throws RequestException
     */
    function getDatabases(): array
    {
        $response = Http::digitalocean()->get('/databases/options');
        $response->throw();

        return $response['options'];
    }

    function getRegions(): array
    {
        $response = Http::digitalocean()->get('/regions', [
            'per_page' => 200
        ]);
        $response->throw();

        $availableRegions = array_filter($response['regions'], function ($v, $k) {
            return $v['available'];
        }, ARRAY_FILTER_USE_BOTH);

        $regionList = [];
        foreach ($availableRegions as $region) {
            $regionList[$region['slug']] = $region['name'];
        }
        return $regionList;

    }

    function getServerDetails()
    {
        return Cache::remember('serverDBDetailsDO', now()->addMonth(), function () {
            return app(DigitalOcean::class)->prepareCacheData();
        });
    }

    function getAllExistingDatabaseClusters($doClusters){
        return $doClusters->getAllExistingDatabaseClusters();
    }

    function prepareCacheData(): array
    {
        $dropletSizes = $this->getSizes();
        $regions = $this->getRegions();

        // only taking MySQL from now
        $mysqlDatabases = $this->getDatabases()['mysql'];

        // merging all droplets regions into one array, the array will contain duplicate values
        $dropletRegions = [];
        foreach ($dropletSizes as $size) {
            $dropletRegions = array_merge($dropletRegions, $size['regions']);
        }

        // getting common regions between droplets and databases so that we can ensure db can be created in droplet region
        $commonRegionsDropletDB = array_intersect(array_unique($dropletRegions), $mysqlDatabases['regions']);

        $finalRegions = array_intersect_key($regions, array_flip($commonRegionsDropletDB));

        $finalDropletDetails = [];

        foreach ($dropletSizes as $size) {
            $currentDropletRegions = array_intersect_key($finalRegions, array_flip($size['regions']));
            if ($currentDropletRegions) {
                $finalDropletDetails[$size['description']][] = [
                    'region' => $currentDropletRegions,
                    'slug' => $size['slug'],
                    'memory' => $size['memory'],
                    'disk' => $size['disk'],
                    'cpu' => $size['vcpus'],
                    'price' => '$' . ((int) $size['price_monthly']).'/month',
                    'raw_price' => (int) $size['price_monthly'],
                    'backupCost' => ceil($size['price_monthly'] * .20), // 20% of server cost
                    'title' => ($size['memory'] / 1024).'GB / '
                        .$size['vcpus'].
                        ($size['vcpus'] == 1 ? ' CPU' : ' CPUs').' / '.
                        $size['disk'].' GB SSD - $'.((int) $size['price_monthly']).'/month',
                ];
            }
        }

        $finalDatabaseDetails = [
            'db' => [
                'group_name' => 'Basic'
            ],
            'so1_5' => [
                'group_name' => 'Storage Optimized'
            ],
            'gd' => [
                'group_name' => 'General Purpose'
            ]
        ];

        foreach ($mysqlDatabases['layouts'] as $database) {
            foreach ($database['sizes'] as $size) {
                $splitSize = explode("-", $size);
                $memory = array_pop($splitSize);
                $cpu = (int) filter_var(array_pop($splitSize), FILTER_SANITIZE_NUMBER_INT);

                $finalDatabaseDetails[$splitSize[0]]['db_list'][] = [
                    'num_nodes' => $database['num_nodes'],
                    'size' => $size,
                    'title' => strtoupper($memory).' / '.$cpu.($cpu == 1 ? ' CPU' : ' CPUs').' / '.$database['num_nodes'].($database['num_nodes'] == 1 ? ' Node' : ' Nodes'),
                ];
            }

        }

        return [
            'droplets' => $finalDropletDetails,
            'databases' => $finalDatabaseDetails,
        ];

    }

}
