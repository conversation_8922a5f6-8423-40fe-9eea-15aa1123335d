<?php

namespace App\Services\CloudServices\Fetchers\Providers;

use App\Services\CloudServices\Fetchers\BaseFetcherUsingAPI;
use Illuminate\Support\Str;
use LKDev\HetznerCloud\HetznerAPIClient;

class HetznerFetcher extends BaseFetcherUsingAPI {

    protected HetznerAPIClient $hetznerClient;
    function __construct(string $apiToken)
    {
        $this->hetznerClient = new HetznerAPIClient($apiToken);
    }

    public function getRegions(): array
    {
        $allLocations = $this->hetznerClient->locations()->all();
        $regionByGroup = [];

        foreach ($allLocations as $location) {
            $explodeZone = explode('-', $location->network_zone);

            $code = $explodeZone[0];
            $zoneLocation = ucfirst($explodeZone[1]);

            $regionByGroup[getCountryOrContinentFullName($code)][] = [
                'id' => $location->id, //int: 1
                'name' => $location->name,  // unique string that people remember: 'fsn1'
                'description' => $location->description, // 'Falkenstein DC Park 1'
                'country' => $location->country, // 'DE'
                'area' => $location->city,  // 'Falkenstein'
                'city' => $location->city,  // 'Falkenstein'
                'zone_area' => $zoneLocation, // '1'
                'network_name' => $location->network_zone // 'eu-central'
            ];
        }

        return $regionByGroup;
    }

    public function getServerTypes(): array
    {
        $allServerTypes = [];
        $allTypes = $this->hetznerClient->serverTypes()->all();

        foreach ($allTypes as $type){
            // handling pricing for different locations
            $pricingDetail = [];
            foreach ($type->prices as $price){
                $pricingDetail[] = [
                    $price->location => [  // 'fsn1
                        'monthly' => $price->price_monthly->gross, // "1.1900000000000000"
                        'hourly' => $price->price_hourly->gross, // "1.0000000000"
                        'title' => '€' . round($price->price_monthly->gross, 2) . '/Month'
                    ]
                ];
            }

            if (Str::startsWith($type->name, 'cx')) {
                $cpuType = ucfirst($type->cpuType).' Intel (Group CX)';
            } elseif (Str::startsWith($type->name, 'cax')) {
                $cpuType = ucfirst($type->cpuType).' Amphere (Group CAX)';
            } elseif (Str::startsWith($type->name, 'cpx')) {
                $cpuType = ucfirst($type->cpuType).' AMD (Group CPX)';
            } elseif (Str::startsWith($type->name, 'ccx')) {
                $cpuType = ucfirst($type->cpuType).' AMD (Group CCX)';
            } else {
                $cpuType = ucfirst($type->cpuType);
            }

            // there are two type CPUs - 'shared' and 'dedicated' we are putting them in different index
            $allServerTypes[$cpuType][] = [
                'id' => $type->id, // int: 1
                'slug' => $type->name, // unique string that people remember: cx11; 'slug' is using in other providers as well so keeping this common
                'description' => $type->description,  // in caps: CX11
                'cores' => $type->cores, // int: 1
                'disk' => $type->disk, // int: 24 (in GB)
//                    'bandwidth' => $type->included_traffic, // int: 654321 (in bytes, per month)
                'memory' => $type->memory, // int: 1 (in GB)
                'prices' => $pricingDetail // formatted, indexed by locations
            ];
        }

        // Filter out any empty cpuType arrays
        foreach ($allServerTypes as $cpuType => $serverList) {
            if (empty($serverList)) {
                unset($allServerTypes[$cpuType]);
            }
        }

        return $allServerTypes;
    }

    public function getFirewalls(): array
    {
        // TODO: Implement getFirewalls() method.
    }

    public function isInstanceRunning(string $instanceId): bool
    {
        // TODO: Implement isInstanceRunning() method.
    }
}
