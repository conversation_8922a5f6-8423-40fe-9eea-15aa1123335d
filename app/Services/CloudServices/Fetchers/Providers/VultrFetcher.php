<?php

namespace App\Services\CloudServices\Fetchers\Providers;

use App\Services\CloudServices\Fetchers\BaseFetcherUsingAPI;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Vultr\VultrPhp\Services\Instances\InstanceException;
use Vultr\VultrPhp\Services\Plans\VPSPlan;
use Vultr\VultrPhp\Util\ListOptions;
use Vultr\VultrPhp\VultrClient;

class VultrFetcher extends BaseFetcherUsingAPI {

    protected VultrClient $vultrClient;
    function __construct(string $apiKey)
    {
        $this->vultrClient = VultrClient::create($apiKey);
    }

    public function getRegions(): array
    {

    }

    public function getFirewalls(): array
    {
        // TODO: Implement getFirewalls() method.
    }


    public function getAllExistingServers(): array
    {
        try {
            $instances = [];
            $options = new ListOptions();
            while (true) {
                foreach ($this->vultrClient->instances->getInstances(null, $options) as $instance) {
                    $instances[] = $instance;
                }

                // The loop has reached the end
                if ($options->getNextCursor() == '') {
                    break;
                }
                // setting the next cursor as current cursor
                $options->setCurrentCursor($options->getNextCursor());
            }
            return $instances;
        } catch (Exception $e) {
            return [];
        }
    }

    protected function getHumanReadablePlanCategory($category): ?string
    {
        $planCategory = [
            'all' => 'All Types',
            'vc2' => 'Cloud Compute',
            'vdc' => 'Dedicated Cloud',
            'vhf' => 'High Frequency Compute',
            'vhp' => 'High Performance',
            // 'voc' => 'All Optimized Cloud types',
            'voc-g' => 'General Purpose Optimized Cloud',
            'voc-c' => 'CPU Optimized Cloud',
            'voc-m' => 'Memory Optimized Cloud',
            'voc-s' => 'Storage Optimized Cloud',
//            'vcg' => 'Cloud GPU'
        ];

        return $planCategory[$category] ?? null;
    }
    public function getServerTypes(): array
    {
//        only VPS Plans
        return Cache::remember('vultr_instance_sizes', now()->addDay(), function () {

            try {
                $allPlans = [];
                $options = new ListOptions();
                while (true) {
                    foreach ($this->vultrClient->plans->getVPSPlans(null, null, $options) as $plan) {
                        $planCategory = $this->getHumanReadablePlanCategory($plan->getType());

                        if ($planCategory === null || $plan->getRam() < 1024) {
                            continue;
                        }

                        $locations = [];
                        foreach ($plan->getLocations() as $location) {

                            $locations[] = [
                                'id' => $location->getId(),
                                'city' => $location->getCity(),
                                'country' => $location->getCountry(),
                                'continent' => $location->getContinent(),
                                'options' => $location->getOptions()
                            ];
                        }

                        // if the price is 0, then it is skip
                        if ($plan->getMonthlyCost() == 0) {
                            continue;
                        }

                        $allPlans[$planCategory][] = [
                            'slug' => $plan->getId(),
                            'cpu' => $plan->getVcpuCount(),
                            'memory' => $plan->getRam(),
                            'disk' => $plan->getDisk(),
                            'diskCount' => $plan->getDiskCount(),
                            'bandwidth' => $plan->getBandwidth(),
                            'price' => $plan->getMonthlyCost(),
                            'type' => $plan->getType(),
                            'locations' => $locations,
                            'title' => $plan->getVcpuCount().($plan->getVcpuCount() == 1 ? ' vCPU' : ' vCPUs').' / '.
                                mb_to_gb($plan->getRam()).' GB RAM / '.
                                $plan->getDisk().' GB NVMe / '.
                                mb_to_gb($plan->getBandwidth()).' TB Bandwidth - '.
                                '$'.$plan->getMonthlyCost().'/month',
                        ];
                    }

                    // The loop has reached the end
                    if ($options->getNextCursor() == '') {
                        break;
                    }
                    // setting the next cursor as current cursor
                    $options->setCurrentCursor($options->getNextCursor());
                }
                return $allPlans;
            } catch (Exception $e) {
                return [];
            }

        });
    }

    public function getUpgradableServerTypes(string $serverId): array
    {
        try {
            $allUpgrades = $this->vultrClient->instances->getAvailableUpgrades($serverId);
            // type: all, applications, os and plans - we only need 'plans'
            $allUpgradablePlans = array_values(array_filter($allUpgrades, function ($object) {
                return $object instanceof VPSPlan;
            }));

            $allPlans = [];
            foreach ($allUpgradablePlans as $plan) {
                $planCategory = $this->getHumanReadablePlanCategory($plan->getType());

                if ($planCategory === null || $plan->getRam() < 1024) {
                    continue;
                }

                $allPlans[$planCategory][] = [
                    'slug' => $plan->getId(),
                    'cpu' => $plan->getVcpuCount(),
                    'memory' => $plan->getRam(),
                    'disk' => $plan->getDisk(),
                    'diskCount' => $plan->getDiskCount(),
                    'bandwidth' => $plan->getBandwidth(),
                    'price' => $plan->getMonthlyCost(),
                    'type' => $plan->getType(),
                    'title' => $plan->getVcpuCount().($plan->getVcpuCount() == 1 ? ' vCPU' : ' vCPUs').' / '.
                        mb_to_gb($plan->getRam()).' GB RAM / '.
                        $plan->getDisk().' GB NVMe / '.
                        mb_to_gb($plan->getBandwidth()).' TB Bandwidth - '.
                        '$'.$plan->getMonthlyCost().'/month',
                ];
            }
            return $allPlans;
        } catch (Exception $e) {
            return [];
        }
    }

    public function getAvailableOS(): array
    {
        return Cache::remember('vultr_available_os', now()->addMonth(), function () {

            try {
                $allOS = [];
                $options = new ListOptions();
                while (true) {
                    foreach ($this->vultrClient->operating_system->getOperatingSystems($options) as $os) {
                        $allOS[] = [
                            'id' => $os->getId(),
                            'name' => $os->getName(),
                            'arch' => $os->getArch(),
                            'family' => $os->getFamily()
                        ];
                    }

                    // The loop has reached the end
                    if ($options->getNextCursor() == '') {
                        break;
                    }
                    // setting the next cursor as current cursor
                    $options->setCurrentCursor($options->getNextCursor());
                }
                return $allOS;
            } catch (Exception $e) {
                return [];
            }

        });
    }

    public function getInstanceDetails(string $instanceId): array
    {
        try {
            $instance = $this->vultrClient->instances->getInstance($instanceId);
            return [
                'id' => $instance->getId(),
                'internal_ip' => $instance->getInternalIp(),
                'main_ip' => $instance->getMainIp(),
                'status' => $instance->getStatus(),
                'power_status' => $instance->getPowerStatus(),
                'server_status' => $instance->getServerStatus(),
                'plan' => $instance->getPlan()
//                add based on your need
            ];

        } catch (Exception $e) {
            return [];
        }

    }

    public function isInstanceRunning(string $instanceId): bool
    {
        $instanceInfo = $this->getInstanceDetails($instanceId);

        // Check if all necessary keys exist
        $requiredKeys = ['status', 'power_status', 'main_ip'];
        foreach ($requiredKeys as $key) {
            if (!array_key_exists($key, $instanceInfo)) {
                return false;
            }
        }

        // Check if the instance is active, running and has a valid IP
        return $instanceInfo['status'] === 'active' &&
            $instanceInfo['power_status'] === 'running' &&
            $instanceInfo['main_ip'] !== "0.0.0.0";
    }

    public function getInstanceBackups(string $instanceId): array
    {
        try {
            $backups = $this->vultrClient->backups->getBackups($instanceId);

            $allBackups = [];

            foreach ($backups as $backup){
                $createdDateTime = Carbon::parse($backup->getDateCreated())->format('d F, Y - g:i a (T)');
                $allBackups[] = [
                    'id' => $backup->getId(),
                    'date_created' => $createdDateTime,
                    'description' => $backup->getDescription(),
                    'size' => $backup->getSize(),
                    'status' => $backup->getStatus()
                ];
            }
            return $allBackups;
        } catch (Exception $e) {
            return [];
        }

    }


    public function getInstanceBackupSchedule(string $instanceId): array
    {
        try {
            return $this->vultrClient->instances->getBackupSchedule($instanceId)->toArray();
        } catch (Exception $e) {
            return [];
        }
    }


    /**
     * @throws InstanceException
     */
    public function getInstanceBandwidthDetails(string $instanceId): array
    {
        // for 30 days from current date
        $bandwidth = $this->vultrClient->instances->getBandwidth($instanceId);
        $totalOutgoingBytes = array_sum(array_column($bandwidth, 'outgoing_bytes'));

        // Convert total outgoing bytes to terabytes (1 TB = 1024^4 bytes)
        $totalOutgoingTB = $totalOutgoingBytes / (1024 ** 4);

        // Set the price per GB for usage beyond 2TB
        $pricePerGB = 0.01;

        // Calculate excess bandwidth and price if it exceeds 2TB
        $excessBandwidthGB = 0;
        $excessPrice = 0.0;

        if ($totalOutgoingTB > 2) {
            // Calculate excess in GB (1 TB = 1024 GB)
            $excessBandwidthGB = ($totalOutgoingTB - 2) * 1024;
            // Calculate the price for the excess bandwidth
            $excessPrice = $excessBandwidthGB * $pricePerGB;
        }

        return [
            'totalOutgoingBytes' => $totalOutgoingBytes,
            'totalOutgoingTB' => $totalOutgoingTB,
            'excessBandwidthGB' => $excessBandwidthGB,
            'excessPrice' => $excessPrice,
        ];
    }

    public function getSnapshot(string $snapshotId): array
    {
        try {
            return $this->vultrClient->snapshots->getSnapshot(snapshot_id: $snapshotId)->toArray();
        } catch (Exception $e) {
            return [];
        }
    }

    public function getAllBillingInvoices(): array
    {
        try {
            $bills = [];
            $options = new ListOptions();
            while (true) {
                foreach ($this->vultrClient->billing->getBillingHistory($options) as $bill) {
                    if($bill->getType() == "invoice") {
                        $bills[] = $bill;
                    }
                }

                // The loop has reached the end
                if ($options->getNextCursor() == '') {
                    break;
                }
                // setting the next cursor as current cursor
                $options->setCurrentCursor($options->getNextCursor());
            }
            return $bills;
        } catch (Exception $e) {
            return [];
        }

    }

    public function getAllInvoiceItems(int $invoiceId): array
    {
        try {
            $items = [];
            $options = new ListOptions();
            while (true) {
                foreach ($this->vultrClient->billing->getInvoiceItems($invoiceId, $options) as $item) {
                    $items[] = $item;
                }

                // The loop has reached the end
                if ($options->getNextCursor() == '') {
                    break;
                }
                // setting the next cursor as current cursor
                $options->setCurrentCursor($options->getNextCursor());
            }
            return $items;
        } catch (Exception $e) {
            return [];
        }

    }

    public function getAllPendingCharges(): array
    {
        try {
           return $this->vultrClient->billing->getPendingCharges();
        } catch (Exception $e) {
            return [];
        }

    }

}
