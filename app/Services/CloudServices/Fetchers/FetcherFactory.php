<?php

namespace App\Services\CloudServices\Fetchers;

use App\Enums\CloudProviderEnums;
use App\Services\CloudServices\Fetchers\Providers\HetznerFetcher;
use App\Services\CloudServices\Fetchers\Providers\VultrFetcher;
use InvalidArgumentException;

class FetcherFactory {
    public static function getService(CloudProviderEnums $cloudProviderEnum, string $credential): FetchCloudProviderInterface
    {
        return match ($cloudProviderEnum) {
            // credential is being pass from VultrActions.php where we differentiated Vultr and Xcloud's Vultr credential
            CloudProviderEnums::VULTR, CloudProviderEnums::XCLOUD_PROVIDER, CloudProviderEnums::XCLOUD_VULTR => new VultrFetcher($credential),
//            EnumsCloudProvider::GCP => new GCPServerCreationService($request, $cloudProvider),
//            EnumsCloudProvider::DIGITALOCEAN, EnumsCloudProvider::XCLOUD => new DigitalOceanServerCreationService($request, $cloudProvider),
//            EnumsCloudProvider::AWS => new AWSServerCreationService($request, $cloudProvider),
            CloudProviderEnums::HETZNER => new HetznerFetcher($credential),
            default => throw new InvalidArgumentException("Unsupported provider: {$cloudProviderEnum->value}"),
        };
    }
}
