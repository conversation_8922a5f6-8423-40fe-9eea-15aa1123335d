<?php

namespace App\Services\CloudServices;

use Aws\Credentials\Credentials;
use Aws\Ec2\Ec2Client;
use Aws\Exception\AwsException;
use Aws\S3\S3Client;
use Illuminate\Support\Facades\Cache;

class AWS
{
    // https://packagist.org/packages/aws/aws-sdk-php-laravel
    // https://docs.aws.amazon.com/sdk-for-php/v3/developer-guide/guide_configuration.html

    private Credentials $credentials;
    private string $region;

    function __construct($key, $secret, $region)
    {
        $this->credentials = new Credentials($key, $secret);
        $this->region = $region;
    }

    function verifyAWSCredentials(): bool
    {
        try {
            $s3 = new S3Client([
                'version' => 'latest',
                'region'  => $this->region,
                'credentials' => $this->credentials,
            ]);
            $result = $s3->listBuckets();
            return !empty($result);
        } catch (AwsException $e) {
            return false;
        }
    }

    function getRegions(): array
    {
        // return all regions
        $client = new EC2Client([
            'credentials' => $this->credentials,
            'region' => $this->region,
            'version' => 'latest',
        ]);

        $regions = $client->describeRegions();
        return $regions->get('Regions');

    }

    function getCachedRegions(): array
    {
        return Cache::remember('regionsAWS', now()->addMonth(), function () {
            return $this->getRegions();
        });
    }

    function getInstanceTypes($region): array
    {
        // return all instance types
        $client = new EC2Client([
            'credentials' => $this->credentials,
            'region' => $region,
            'version' => 'latest',
        ]);

        $instanceTypes = $client->describeInstanceTypes();
        return $instanceTypes->get('InstanceTypes');
    }

}
