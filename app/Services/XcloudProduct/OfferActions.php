<?php

namespace App\Services\XcloudProduct;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\PlansEnum;

abstract class OfferActions
{
    public abstract static function get(PlansEnum $plan): array;
    public abstract static function getOfferModels(): array;

    public abstract static function getBillingServicesByModel(string $model) : string;

    public abstract static function match(BillingServices $offerAbleServices, PlansEnum $plan): array;

    public abstract static function matchGetQuantity(BillingServices $offerAbleServices, PlansEnum $plan) : int;
}
