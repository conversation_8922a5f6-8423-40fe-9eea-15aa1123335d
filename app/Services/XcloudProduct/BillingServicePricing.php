<?php

namespace App\Services\XcloudProduct;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\PlansEnum;
use App\Models\BillingPlan;
use App\Models\Team;
use Exception;

class BillingServicePricing
{
    /**
     * Calculate the price per unit for the given number of servers.
     *
     * @param Team $team
     * @param BillingServices $billingService
     * @return float in usd
     * @throws Exception
     */
    public static function get(Team $team, BillingServices $billingService, BillRenewalPeriod $renewalPeriod) : float
    {
        if (app()->runningUnitTests()) return 0;

        $activePlan = $team->activePlan;

        if (!$activePlan) {
            throw new Exception(
                'Active plan is required to calculate the price per unit. Issued on Team: '. $team->id.'
                with billing service: '. $billingService->value.' and active plan: '. $activePlan?->name?->value .' and
                renewal period: '. $renewalPeriod->value
            );
        }

        $planToUse = PlansEnum::getPlanByServerCount($team->countSelfManagedHostingForPlanBasedBilling());

        $planToUse = BillingPlan::where('name', $planToUse)->first();

        $servicePricing = $planToUse?->service_pricing ?: [];

        if ($planToUse->requires_billing) {
            if (!array_key_exists($renewalPeriod->value, $servicePricing) || !array_key_exists($billingService->value, $servicePricing[$renewalPeriod->value])) {
                throw new Exception(
                    'Service pricing not found for ' . $billingService->value. ' on Team: '. $team->id.' with
                    billing service: '. $billingService->value.' and active plan: '. $planToUse?->name?->value.' and
                    renewal period: '. $renewalPeriod->value. ' and service pricing: '. json_encode($servicePricing).' '.
                    json_encode(BillingPlan::all()->toArray())
                );
            }
        }

        return isset($servicePricing[$renewalPeriod->value]) ? $servicePricing[$renewalPeriod->value][$billingService->value] ?? 0 : 0;
    }

    /**
     * @throws Exception
     */
    public static function getOfferPrice(Team $team, BillingServices $billingService, BillRenewalPeriod $renewalPeriod): float
    {
        if (!$team->activePlan) {
            throw new Exception(
                'Active plan is required to calculate the price per unit. Issued on Team: '. $team->id.' with
                billing service: '. $billingService->value.' and active plan: '. $team->activePlan?->name?->value .' and
                renewal period: '. $renewalPeriod->value
            );
        }

        $canAvailOffer = $billingService->canAvailOffer($team);

        if (!$canAvailOffer) {
            return self::get($team, $billingService, $renewalPeriod);
        }

        return 0;
    }
}
