<?php

namespace App\Services\XcloudProduct;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\PlansEnum;
use App\Models\Server;
use App\Models\Site;

final class ActiveOffersForAdmin extends OfferActions
{
    public static function get(PlansEnum $plan): array
    {
        return [
            BillingServices::xCloudManagedHosting->value => 5,
            BillingServices::SelfManagedHosting->value   => 20,
            BillingServices::Site->value                 => 500
        ];
    }

    public static function getOfferModels(): array
    {
        return [
            BillingServices::xCloudManagedHosting->value => Server::class,
            BillingServices::SelfManagedHosting->value   => Server::class,
            BillingServices::Site->value                 => Site::class
        ];
    }

    public static function getBillingServicesByModel(string $model): string
    {
        return !array_search($model, self::getOfferModels()) ? '' : array_search($model, self::getOfferModels());
    }

    public static function match(BillingServices $offerAbleServices, PlansEnum $plan): array
    {
        // $offers = self::get();
        // return isset($offers[$offerAbleModel]) ? [$offerAbleModel => $offers[$offerAbleModel]] : [];

        return [];
    }

    public static function matchGetQuantity(BillingServices $offerAbleServices, PlansEnum $plan): int
    {
        // $offers = self::get();
        //
        // return $offers[$offerAbleModel] ?? 0;

        return 0;
    }
}
