<?php

namespace App\Services\Server\Web\Providers;

use App\Enums\XcloudBilling\BillingServices;
use App\Models\CloudProvider;
use App\Models\Tag;
use App\Rules\DatabasePassword;
use App\Services\CloudServices\DigitalOcean;
use App\Services\Database\DatabaseEngine;
use App\Services\Server\Web\BaseCloudProviderService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;

// We no longer use Digital Ocean as our default provider, it was used before. <PERSON>ult<PERSON> is our brother now
class XCloudDigitalOceanService extends BaseCloudProviderService {

    /**
     * @throws \Exception
     */
    function __construct() {
        $this->billingServices = BillingServices::xCloudManagedHosting;
        $this->checkingPaymentMethod();
    }

    public function viewChooseCredentials(): RedirectResponse
    {
        // No need to show credential choosing page, we are using our own credential
        return redirect()->route('server.create');
    }
    public function viewCreateServer(CloudProvider $cloudProvider): Response
    {
        $tags = Tag::associatedTags('servers');

        $parameters = [
            'serverDBDetailsDO' => (new DigitalOcean())->getServerDetails(),
            'title' => 'Set Up Your Server With xCloud',
            'database_type' => DatabaseEngine::MYSQL_8,
            'database_password' => DatabasePassword::randomPassword(),
            'tags' => $tags,
        ];

        return $this->buildInertiaRender('Server/Create/Xcloud/DigitalOcean', $parameters);
    }
}
