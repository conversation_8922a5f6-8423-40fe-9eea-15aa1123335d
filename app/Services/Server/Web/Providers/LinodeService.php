<?php

namespace App\Services\Server\Web\Providers;

use App\Enums\CloudProviderEnums;
use App\Enums\XcloudBilling\BillingServices;
use App\Models\CloudProvider;
use App\Providers\CloudProviders\OAuthProviderFactory;
use App\Services\CloudServices\Fetchers\Providers\LinodeFetcher;
use App\Services\Database\DatabaseEngine;
use App\Services\Server\Web\BaseCloudProviderService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;
use Linode\Exception\LinodeException;


class LinodeService extends BaseCloudProviderService {

    /**
     * @throws \Exception
     */
    function __construct() {
        $this->billingServices = BillingServices::SelfManagedHosting;
        $this->checkingPaymentMethod();
    }

    public function viewChooseCredentials(): Response
    {
        $providers = team()->cloudProviders()
            ->where('provider', CloudProviderEnums::LINODE)
            ->select([
                'id',
                'name',
                'provider',
                'api_key',
                'created_at',
                'updated_at'
            ])->get();

        $parameters = [
            'cloud_provider_name' => CloudProviderEnums::LINODE,
            'providers' => $providers,
            'provider' => $providers->count() > 0 ? '' : 'new',
            'can_add_provider' => user()->can('create', CloudProvider::class),
        ];

        return $this->buildInertiaRender('Server/Create/Linode/ChoseProvider', $parameters);
    }


    /**
     * @throws LinodeException
     */
    private function linodeCredentialCheck(CloudProvider $cloudProvider): RedirectResponse|Response
    {
        $linodeFetcher = new LinodeFetcher($cloudProvider->getAccessToken());
        // just to check the credential, server types and regions do not need token
        $linodeFetcher->getAllInstances();

        $parameters = [
            'sizes' => $linodeFetcher->getServerTypes(),
            'regions' => $linodeFetcher->getRegions(),
            'provider' => $cloudProvider,
            'can_create_demo'=> user()->canCreateDemoServer(),
            'database_type' => DatabaseEngine::MYSQL_8,
            'database_type_list' => DatabaseEngine::getVersionList(),
        ];

        return $this->buildInertiaRender('Server/Create/Linode/Index', $parameters);

    }

    public function viewCreateServer(CloudProvider $cloudProvider): RedirectResponse|Response
    {

        try {
            return $this->linodeCredentialCheck($cloudProvider);
        } catch (\Exception $e) {
            return $this->handleCredentialException($e, $cloudProvider);
        }
    }

    /**
     * Handle credential exception and attempt token refresh if needed.
     */
    private function handleCredentialException(\Exception $e, CloudProvider $cloudProvider): RedirectResponse|Response
    {
        try {
            $oauthProvider = (new OAuthProviderFactory)->getProvider(CloudProviderEnums::LINODE);
            $oauthProvider->checkAuthTokenAndRefresh($e, $cloudProvider);
            return $this->linodeCredentialCheck($cloudProvider);
        } catch (\Exception $retryException) {
            return $this->redirectWithError($retryException->getMessage());
        }
    }

    /**
     * Redirect to credential error route with a flash message.
     */
    private function redirectWithError(string $message): RedirectResponse
    {
        return to_route('server.choose.credential', CloudProviderEnums::LINODE)->with('flash', [
            'type' => 'error',
            'message' => $message,
        ]);
    }

}
