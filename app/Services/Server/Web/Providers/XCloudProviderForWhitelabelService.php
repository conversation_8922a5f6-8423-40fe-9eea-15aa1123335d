<?php

namespace App\Services\Server\Web\Providers;

use App\Enums\XcloudBilling\BillingServices;
use App\Models\CloudProvider;
use App\Models\Product;
use App\Models\Server;
use App\Models\Tag;
use App\Repository\XCloudVultrServerTypeRepository;
use App\Services\Database\DatabaseEngine;
use App\Services\Server\Web\BaseCloudProviderService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;

class XCloudProviderForWhitelabelService extends BaseXCloudVultr
{
    protected string $inertiaComponent;

    function __construct() {
        $this->billingServices = BillingServices::xCloudManagedHosting;
        $this->inertiaComponent = 'Server/Create/Xcloud/XcloudForWhitelabel';
    }
    public function viewChooseCredentials(): Response|RedirectResponse
    {
        // No need to show credential choosing page, we are using our own credential
        return abort(404);
    }

    public function viewCreateServer(CloudProvider $cloudProvider): Response|RedirectResponse
    {
        $serverTypeRepository = new XCloudVultrServerTypeRepository();
        $products = $serverTypeRepository->getXCloudProductTypesByBillingServiceForWhitelabel($this->billingServices);
        $serverDetails = $serverTypeRepository->getXCloudServerTypes($products);

        $tags = Tag::associatedTags('servers')->pluck('label', 'value')->toArray();
        $billingActive = team()->billingIsActive();
        $availableProducts = (new Server)->getAvailableProductsToUse($this->billingServices, $serverDetails['sizes']->pluck('*.slug')->flatten()->toArray(), team());

        $productName = request('name');
        if (empty($productName)) {
            abort(404, 'Product not found!');
        }

//        $whiteLabel = currentWhiteLabel();
//        if(empty($whiteLabel)){
//            abort(404, 'Service not found!');
//        }
//
//        $product = $whiteLabel->activeProducts()->whereTitle($productName)->get();
//
//        if($product->isEmpty()){
//            abort(404, 'Product not found!');
//        }

        $selectedSize = $serverDetails['selectedSize'];

        $parameters = [
            'supportManualBilling' => team()?->activePlan?->support_manual_billing,
            'sizes' => $serverDetails['sizes']->toArray(),
            'regions' => $serverDetails['regions'],
            'provider' => '',
            'selectedSize' => $selectedSize,
            'tagList' => $tags,
            'can_create_demo' => user()->canCreateDemoServer(),
            'billingActive' => $billingActive,
            'availableProducts' => $availableProducts->pluck('slug')->values()->toArray(),
            'database_type' => DatabaseEngine::MYSQL_8,
            'database_type_list' => DatabaseEngine::getVersionList(),
            'whiteLabel' => team()->whiteLabel
        ];

        return $this->buildInertiaRender($this->inertiaComponent, $parameters);
    }
}
