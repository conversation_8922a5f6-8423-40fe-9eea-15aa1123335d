<?php

namespace App\Services\Server\Web\Providers;

use App\Enums\CloudProviderEnums;
use App\Enums\XcloudBilling\BillingServices;
use App\Models\CloudProvider;
use App\Services\CloudServices\Fetchers\Providers\VultrFetcher;
use App\Services\Database\DatabaseEngine;
use App\Services\Server\Web\BaseCloudProviderService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Arr;
use Inertia\Response;

class VultrService extends BaseCloudProviderService {

    /**
     * @throws \Exception
     */
    function __construct() {
        $this->billingServices = BillingServices::SelfManagedHosting;
        $this->checkingPaymentMethod();
    }

    public function viewChooseCredentials(): Response
    {
        $providers = team()->cloudProviders()
            ->where('provider', CloudProviderEnums::VULTR)
            ->select([
                'id',
                'name',
                'provider',
                'api_key',
                'created_at',
                'updated_at'
            ])->get();

        $parameters = [
            'cloud_provider_name' => CloudProviderEnums::VULTR,
            'providers' => $providers,
            'provider' => $providers->count() > 0 ? '' : 'new',
            'can_add_provider' => user()->can('create', CloudProvider::class),
        ];

        return $this->buildInertiaRender('Server/Create/Vultr/ChoseProvider', $parameters);
    }

    public function viewCreateServer(CloudProvider $cloudProvider): RedirectResponse|Response
    {
        $vultrFetcher = new VultrFetcher($cloudProvider->getAccessToken());

        $collection = collect($vultrFetcher->getServerTypes());

        $sizes = $collection->map(function ($items) {
            return Arr::map($items, function ($item) {
                return Arr::except($item, 'locations');
            });
        })->toArray();

        $regions = [];

        foreach ($collection as $items) {
            foreach ($items as $item) {
                $regions[$item['slug']] = $item['locations'];
            }
        }

        $parameters = [
            'sizes' => $sizes,
            'regions' => $regions,
            'provider' => $cloudProvider,
            'can_create_demo'=> user()->canCreateDemoServer(),
            'database_type' => DatabaseEngine::DEFAULT,
            'database_type_list' => DatabaseEngine::getVersionList(),
        ];

        return $this->buildInertiaRender('Server/Create/Vultr/Index', $parameters);
    }
}
