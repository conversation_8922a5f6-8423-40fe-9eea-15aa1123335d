<?php

namespace App\Services\Server\Web;

use App\Enums\XcloudBilling\BillingServices;
use App\Models\CloudProvider;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

abstract class BaseCloudProviderService implements CloudProviderServiceInterface {
    protected BillingServices $billingServices;

    /**
     * @throws \Exception
     */
    public function checkingPaymentMethod(): ?RedirectResponse
    {
        // we will call this in constructor/functions of child classes where payment method should be mandatory
        // Currently for adding credentials or server all Cloud Providers need a method

        if(!team()->canAccessWithBilling($this->billingServices)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Please add a payment method to create more servers.'
            ]);
        }

        return null;
    }

    public abstract function viewChooseCredentials(): Response | RedirectResponse;

    public abstract function viewCreateServer(CloudProvider $cloudProvider): Response | RedirectResponse;

    protected function buildInertiaRender(string $component, array $parameters): Response | RedirectResponse
    {
        return Inertia::render($component, $parameters);
    }
}
