<?php

namespace App\Services\Server\Web;

use App\Enums\CloudProviderEnums as EnumsCloudProvider;
use App\Enums\XCloudCloudProviders;
use App\Services\Server\Web\Providers\AWSService;
use App\Services\Server\Web\Providers\DigitalOceanService;
use App\Services\Server\Web\Providers\GCPService;
use App\Services\Server\Web\Providers\HetznerService;
use App\Services\Server\Web\Providers\LinodeService;
use App\Services\Server\Web\Providers\VultrService;
use App\Services\Server\Web\Providers\XCloudDigitalOceanService;
use App\Services\Server\Web\Providers\XCloudProviderForWhitelabelService;
use App\Services\Server\Web\Providers\XCloudProviderService;
use App\Services\Server\Web\Providers\XCloudVultrService;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CloudProviderServiceFactory {
    public static function getService($enum): CloudProviderServiceInterface
    {
        return match ($enum) {
            EnumsCloudProvider::XCLOUD => CloudProviderServiceFactory::getXCloudProvider(),
            EnumsCloudProvider::VULTR => new VultrService(),
//            We have only XCLOUD and XCLOUD Provider, not XCLOUD_VULTR option for user end (I mean user won't see the text 'vultr' in URL), XCLOUD will do for XCLOUD_VULTR Part
//            EnumsCloudProvider::XCLOUD_VULTR => new XCloudVultrService(),
            EnumsCloudProvider::XCLOUD_PROVIDER => new XCloudProviderService(),
            EnumsCloudProvider::GCP => new GCPService(),
            EnumsCloudProvider::DIGITALOCEAN => new DigitalOceanService(),
            EnumsCloudProvider::AWS => new AWSService(),
            EnumsCloudProvider::HETZNER => new HetznerService(),
            EnumsCloudProvider::LINODE => new LinodeService(),
            default => throw new NotFoundHttpException,
        };
    }

    public static function getXCloudProvider(): CloudProviderServiceInterface
    {
        $whiteLabel = currentWhiteLabel();
        if ($whiteLabel) {
            return new XCloudProviderForWhitelabelService();
        }
        if (config('services.xcloud_cloud_provider') === XCloudCloudProviders::VULTR->value) {
            return new XCloudVultrService();
        }
        return new XCloudDigitalOceanService();

    }
}
