<?php

namespace App\Services\Server\Api\ServerCreation\Providers;

use App\Enums\CloudProviderEnums;
use App\Enums\Stack;
use App\Models\CloudProvider;
use App\Models\Server;
use App\Services\Database\DatabaseEngine;
use App\Services\Server\Api\ServerCreation\BaseServerCreationService;
use App\Validator\ServerNameValidation;
use App\Validator\TagValidation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;


class VultrServerCreationService extends BaseServerCreationService {

    protected CloudProvider $cloudProvider;

    public function __construct(Request $request, CloudProvider $cloudProvider)
    {
        parent::__construct($request);
        $this->cloudProvider = $cloudProvider;
    }


    protected function validateRequest(): array {
        $rules = [
            'name' => ServerNameValidation::serverName(),
            'size' => ['required', 'max:255'],
            'region' => ['required', 'max:255'],
            'database_type' => ['string', DatabaseEngine::asRule()],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'backups' => ['boolean'],
            'consent' => ['required', 'accepted'],
            'consent_documentation' => ['required', 'accepted']
        ];

        $messages = [
            ...TagValidation::tagMessage(),
            'consent.required' => 'Please accept this consent before creating server.',
            'consent.accepted' => 'Please accept this consent before creating server.',
        ];

        $validatedData = Validator::make($this->request->all(), $rules, $messages)->validate();

        // Unsetting fields not required for server creation
        unset($validatedData['consent'], $validatedData['consent_documentation']);

        // default authentication mode
        $validatedData['ssh_authentication_mode'] = 'public_key';
        $validatedData['ssh_port'] = '22';
        $validatedData['ssh_username'] = 'root';
        $validatedData['ubuntu_version'] = '22.04';
        $validatedData['server_image'] = 'Ubuntu 22.04 LTS x64';
        $validatedData['redis_version'] = Server::REDIS_SEVEN;
        $validatedData['redis_password'] ??= Str::random(32);
        return $validatedData;
    }

    protected function getCloudProvider(array &$validated): void
    {
        $validated['cloud_provider_id'] = $this->cloudProvider->id;
    }
}
