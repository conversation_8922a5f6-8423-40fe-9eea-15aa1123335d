<?php

namespace App\Services\Server\Api\ServerModification\Actions;

use App\Enums\ServerStatus;
use App\Services\Server\Api\ServerModification\BaseServerModificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use App\Enums\ServerModificationActions;
use Illuminate\Http\Request;

class ServerProviderBackupSchedule extends BaseServerModificationService
{
    /**
     * @throws \Exception
     */
    public function modify(Request $request): RedirectResponse|JsonResponse
    {
        if ($response = $this->isServerModifying()) {
            return $response;
        }

        if (!$this->server->backups) {
            return back()->with('flash', ['message' => 'Backups is turned off for this server', 'type' => 'warning']);
        }

        $rules = [
            'backup_type' => 'required|string|in:daily,weekly,monthly,daily_alt_even,daily_alt_odd',
            'hour' => 'required|integer|between:0,23',
            'day_of_week' => 'nullable|required_if:backup_type,weekly|integer|between:0,6',
            'day_of_month' => 'nullable|required_if:backup_type,monthly|integer|between:1,28'
        ];

        $validatedData = $this->validateServerRequest(request: $request, rules: $rules);

        // Temporarily saving data for modification
        $metaData = array_merge([
            'day_of_week' => null,
            'day_of_month' => null,
            'message' => 'Initiating setting server backup schedule on provider'
        ], $validatedData); // type and hour are included here, dow and dom are optional

        $this->saveNewDataToMeta(action: ServerModificationActions::SCHEDULE_BACKUP, data: $metaData);

        return $this->dispatchTask('BackupScheduleServer', 'Server Backup Scheduling Initiated');

    }

    public function getFunctionName(): string
    {
        return 'serverBackupSchedule';
    }

    public function onSuccessWork(): void
    {
        // Need to remove the data from the meta json column otherwise this action will be performed again
        $this->server->deleteServerModificationData(ServerModificationActions::SCHEDULE_BACKUP);
    }

    public function onFailedWork(): ServerStatus
    {
        // This operation is not making trouble to the server so failing this should not stop the server from other operations.
        return ServerStatus::PROVISIONED;
    }

    public function isAsync(): bool
    {
        return false; // it doesn't take long, it changes the status immediately
    }
}
