<?php

namespace App\Services\Server\Api\ServerModification\Actions;

use App\Enums\ServerStatus;
use App\Models\Product;
use App\Services\Server\Api\ServerModification\BaseServerModificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use App\Enums\ServerModificationActions;
use Illuminate\Http\Request;
use App\Events\ServerResizeFailed;
use App\Events\ServerResizeSuccess;

class ServerResize extends BaseServerModificationService
{
    /**
     * @throws \Exception
     */
    public function modify(Request $request): RedirectResponse|JsonResponse
    {
        if ($response = $this->isServerModifying()) {
            return $response;
        }

        $rules = [
            'resize_confirmation' => ['required', 'string'],
            'size' => ['required', 'string'],
        ];

        $validatedData = $this->validateServerRequest(request: $request, rules: $rules);

        if ($validatedData['resize_confirmation'] !== $this->server->name) {
            if (request()->expectsJson()) {
                return response()->json([
                    'message' => 'Server name does not match confirmation'
                ], 422);
            }

            back()->with('flash', ['message' => 'Server name does not match confirmation', 'type' => 'warning']);
        }

        if ($validatedData['size'] == $this->server->size) {
            return back()->with('flash', ['message' => 'Server is already the selected size', 'type' => 'warning']);
        }

        // Temporarily saving data for modification
        $metaData = [
            'oldSize' => $this->server->size,
            'newSize' => $validatedData['size'],
            'message' => 'Initiating server resize'
        ];

        $this->saveNewDataToMeta(action: ServerModificationActions::RESIZE, data: $metaData);

        return $this->dispatchTask('ResizeServer', 'Server Resize Initiated');

    }

    public function getFunctionName(): string
    {
        return 'serverResize';
    }

    public function onSuccessWork(): void
    {
        $resizeData = $this->server->getServerModificationData(ServerModificationActions::RESIZE);
        $this->server->update(['size' => $resizeData['newSize']]);

        ServerResizeSuccess::dispatch($this->server);

        // Need to remove the data from the meta json column otherwise this action will be performed again
//        $this->server->deleteServerModificationData(ServerModificationActions::RESIZE); // putting this in Notifications/ServerResizeSuccess event otherwise it will be deleted before the event is dispatched

        $this->server->pullMonitoring();

        if($this->server->isUnderXCloudOrWhiteLabelVultr()){
            $this->server->bills->where('service', $this->server->getDefaultBillingService())->last()->update([
                'product_id' => Product::where('service_type', $this->server->getDefaultBillingService())->where('slug', $resizeData['newSize'])->first()->id,
            ]);
        }
    }

    public function onFailedWork(): ServerStatus
    {
        // Sending notification to the user
        ServerResizeFailed::dispatch($this->server);

        return ServerStatus::MODIFICATION_FAILED;
    }

    public function isAsync(): bool
    {
        return true; // server resizing is time-consuming operation and server will be unreachable during the operation
    }
}
