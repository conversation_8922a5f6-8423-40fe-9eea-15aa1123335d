<?php

namespace App\Services\Server\Api\ServerModification;

use App\Enums\ServerStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

interface ServerModificationControllerInterface {

    /**
     * Modify server settings based on the provided laravel request.
     *
     * This method handles request data - validate and save to meta column, checks server status and then initiate the task in the background.
     *
     * @param Request $request The request instance containing all necessary data for modification.
     * @return RedirectResponse|JsonResponse Returns a response that may either redirect the user or provide a JSON response.
     */
    public function modify(Request $request): RedirectResponse|JsonResponse;

    /**
     * The function name for different cloud provider base on modification type like "ServerResize", "ServerBackupSchedule"
     *
     * This method should return a string that uniquely identifies the function for specific modification type and
     * it will be available all providers Action class
     *
     * @return string The name of the function based on modification action.
     */
    public function getFunctionName(): string;

    /**
     * Check if the modification needs to check server status.
     *
     * This method should return a boolean value that indicates if the modification needs to check server status in the background.
     *
     * @return bool Returns true if the modification is async, false otherwise.
     */
    public function isAsync(): bool;

    /**
     * Perform the work when the modification is successful.
     *
     * This method should perform the necessary work when the modification is successful.
     */
    public function onSuccessWork(): void;

    /**
     * Perform the work when the modification is failed.
     *
     * This method should perform the necessary work when the modification is failed.
     *
     * @return ServerStatus The server status after the modification is failed. Some failed modification may require different server status.
     */
    public function onFailedWork(): ServerStatus;
}
