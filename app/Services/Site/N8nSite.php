<?php

namespace App\Services\Site;

use App\Contracts\SiteContract;
use App\Models\Site;

class N8nSite extends SiteManager
{
    public function geLogFilePaths(): string
    {
        return $this->siteBasePath().'/logs/n8n.log';
    }

    public function getEnvironmentFilePath(): string
    {
        return $this->siteBasePath().'/start.sh';
    }

    public function siteBasePath(): string
    {
        return $this->siteBasePathWithWebRoot();
    }
}
