<?php

namespace App\Services\Site;

use App\Enums\CustomNginxEnum;
use App\Events\SiteSSLStatusChanged;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Models\Task;
use App\Scripts\InstallCustomSslCertificate;
use App\Scripts\ObtainSslCertificate;
use App\Services\Integrations\CloudflareService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Log;

class SslManager
{
    public function __construct(public Site $site)
    {
    }

    /**
     * @throws \Exception
     */
    public function obtain(): bool
    {
        // Log::info('SslManager::obtain()');
        // Log::info('SslManager::obtain() $this->site->ssl_provider: '.$this->site->ssl_provider);

        if ($this->site->ssl_provider === SslCertificate::PROVIDER_XCLOUD) {
            if($this->site->domainActiveOnCloudflare()){
                $this->syncDnsRecordOnCloudflare();
                // remove cloudflare integration from this site
                $this->site->removeCloudflareIntegration();
            }

            return $this->obtainWithXCloud();
        }

        if ($this->site->ssl_provider === SslCertificate::PROVIDER_STAGING) {
            $this->createStagingCertificate($this->site->name);
            return $this->installCustomCertificate()->successful();
        }

        if ($this->site->ssl_provider === SslCertificate::PROVIDER_CUSTOM) {
            dump('Installing custom SSL certificate');
            // We are using a custom SSL certificate is provided by the user
            if (!$this->site->sslCertificate) {
                return false;
            }

            if($this->site->domainActiveOnCloudflare()){
                // remove cloudflare integration from this site
                $this->site->removeCloudflareIntegration();
            }

            return $this->installCustomCertificate()->successful();
        }

        if($this->site->ssl_provider === SslCertificate::PROVIDER_CLOUDFLARE){
            try {
                $this->syncDnsRecordOnCloudflare();

                $this->createCloudflareCertificate();

                // if site domain is in cloudflare, proxied needs to be true otherwise SSL won't work
                $this->markDomainAsProxied();
                return $this->installCustomCertificate()->successful();
            }catch (\Exception $e){
                $this->site->sslCertificate?->update(['status' => SslCertificate::STATUS_FAILED]);
                Log::error($e->getMessage());
                return false;
            }
        }

        return false;
    }

    private function obtainWithXCloud(): bool
    {
        dump('Obtaining SSL certificate with xCloud...');

        // for ols to generate a certificate it needs to have the correct nginx config, so we regenerate it early
        if ($this->site->server->stack->isOpenLiteSpeed()) {
            $this->site->regenerateNginxConf();
        }

        $certificate = $this->site->sslCertificates()->firstOrCreate(
            [
                'provider' => SslCertificate::PROVIDER_XCLOUD
            ],
            [
                'status' => SslCertificate::STATUS_NEW
            ]
        );

        SiteSSLStatusChanged::dispatch($certificate);

        $task = $this->site->run(new ObtainSslCertificate($certificate));

        $certificate->updateStatusFromTask($task);
        dump('SSL certificate obtained');
        SiteSSLStatusChanged::dispatch($certificate);

        return $certificate->status === SslCertificate::STATUS_OBTAINED;
    }

    /**
     * @throws \Exception
     */
    private function createCloudflareCertificate(): void
    {
        // Log::info(__METHOD__ . '.. Create Cloudflare Origin Certificate');

        foreach ($this->site->team()->cloudflareIntegrations as $cloudflareIntegration) {
            // setting ssl encryption mode to Full(strict) to avoid redirect loop in Cloudflare
            $response = (new CloudflareService($cloudflareIntegration))
                ->setSslEncryptionMode(Arr::get($this->site->meta,'cloudflare_integration.zone_id'), 'strict');

            Log::info(__METHOD__ . '.. setSslEncryptionMode: Mode set to ' . Arr::get($response, 'result.value'));

            $response = $this->site->createCloudflareOriginCertificate();

            if(!empty(Arr::get($response, 'errors'))){
                $errorMessage = '';
                foreach (Arr::get($response, 'errors') as $error){
                    // get error messages
                    $errorMessage .= $error['message'] . '\n';
                }

                if($errorMessage){
                    $this->site->log('Error creating origin certificate: '. $errorMessage);
                    throw new \Exception('Error creating origin certificate: '.$errorMessage);
                }
            }

            if(is_null($response)){
                $this->site->log(__METHOD__ . '.. No certificate was created. Maybe the zones were not found.');
            }

            $this->site->sslCertificate()->updateOrCreate(
                [
                    'site_id' => $this->site->id,
                    'provider' => SslCertificate::PROVIDER_CLOUDFLARE
                ], [
                    'status' => SslCertificate::STATUS_OBTAINED,
                    'obtained_from' => SslCertificate::PROVIDER_CLOUDFLARE,
                    'ssl_certificate' => Arr::get($response, 'result.certificate'),
                    'ssl_private_key' => Arr::get($response, 'result.privateKey'),
                    'expires_at' => Carbon::parse(Arr::get($response, 'result.expires_on'))->toDateTimeString(),
                ]
            );

            if(Arr::get($response, 'success')){
                // certificate is created on cloudflare
                Log::info(__METHOD__ . '.. Certificate created on Cloudflare');
                break;
            }
        }
    }

    public function syncDnsRecordOnCloudflare(): void
    {
        Log::info(__METHOD__ . '.. Sync DNS record on Cloudflare');

        $dnsRecordAvailableOnCloudflare = false;
        foreach ($this->site->team()->cloudflareIntegrations as $cloudflareIntegration) {
            // check if domain is in cloudflare
            $response = (new CloudflareService($cloudflareIntegration))
                ->dnsRecords(
                    zoneId: $this->site->getMeta('cloudflare_integration.zone_id'),
                    name: $this->site->name
                );

            if(Arr::get($response, 'success') && Arr::get($response, 'result.0.id')){
                Log::info(__METHOD__ . '.. DNS record found on Cloudflare');
                $dnsRecordAvailableOnCloudflare = true;
                // check if dns ip address is matched with server ip
                if(Arr::get($response, 'result.0.content') !== $this->site->server->public_ip) {
                    Log::info(__METHOD__ . '.. IP address changed.');
                    $response = (new CloudflareService($cloudflareIntegration))
                        ->updateDnsRecord(
                            zoneId: $this->site->getMeta('cloudflare_integration.zone_id'),
                            id: Arr::get($response, 'result.0.id'),
                            name: $this->site->name,
                            content: $this->site->server->public_ip
                        );
                }

                ## if using xcloud managed service from cloudflare service, mark domain as not proxied(main domain + additional domains)
                // mark main domain as not proxied
                $response = (new CloudflareService($cloudflareIntegration))
                    ->updateDnsProxyStatus(
                        zoneId: $this->site->getMeta('cloudflare_integration.zone_id'),
                        id: Arr::get($response, 'result.0.id'),
                        proxied: !($this->site->ssl_provider === SslCertificate::PROVIDER_XCLOUD)
                    );

                // mark additional domains as not proxied
                if(!empty($this->site->additional_domains)){
                    $response = (new CloudflareService($cloudflareIntegration))
                        ->dnsRecords(
                            zoneId: $this->site->getMeta('cloudflare_integration.zone_id')
                        );

                    $additionalDomains = collect($this->site->additional_domains)->pluck('value')->toArray();

                    foreach (Arr::get($response, 'result') as $record){
                        if(in_array($record['name'], $additionalDomains)){
                            $response = (new CloudflareService($cloudflareIntegration))
                                ->updateDnsProxyStatus(
                                    zoneId: $this->site->getMeta('cloudflare_integration.zone_id'),
                                    id: $record['id'],
                                    proxied: !($this->site->ssl_provider === SslCertificate::PROVIDER_XCLOUD)
                                );
                        }
                    }
                }

                // remove origin certificate from cloudflare
                $originCertificateId = $this->site->getMeta('cloudflare_integration.origin_certificate.certificate_id');
                if(!empty($originCertificateId)){
                    $response = (new CloudflareService($cloudflareIntegration))->getOriginCertificate($originCertificateId);

                    if(Arr::get($response, 'success') && Arr::get($response, 'result.id') == $originCertificateId){
                        $response = (new CloudflareService($cloudflareIntegration))->revokeOriginCertificate($originCertificateId);
                        if(Arr::get($response, 'success')){
                            Log::info('origin certificate: ' . $originCertificateId .  ' revoked from cloudflare');
                        }
                    }
                }
                break;
            }
        }

        if(!$dnsRecordAvailableOnCloudflare){
            Log::info(__METHOD__ . '.. DNS record not found on Cloudflare. Creating DNS record on Cloudflare');
            foreach ($this->site->team()->cloudflareIntegrations as $cloudflareIntegration) {
                $response = (new CloudflareService($cloudflareIntegration))
                    ->updateOrCreateRecord(
                        name: $this->site->name,
                        content: $this->site->server->public_ip,
                        zoneId: $this->site->getMeta('cloudflare_integration.zone_id')
                    );

                if(Arr::get($response, 'success')){
                    Log::info(__METHOD__ . '.. DNS record added on Cloudflare');
                    break;
                }
            }
        }
    }

    private function createStagingCertificate($url=null): void
    {
        dump('Obtaining default SSL certificate');
        $active_domain = config('services.cloudflare_updated.active');
        if ($url){
            $urlComponents = parse_url($url);
            $hostParts = explode('.', $urlComponents['path']);
            if (count($hostParts) >= 2) {
                $active_domain = $hostParts[count($hostParts) - 2] . '.' . $hostParts[count($hostParts) - 1];
            }
        }


        Log::info('SslManager::createStagingCertificate() $active_domain: '.$active_domain);

        $sslList = json_decode(File::get(resource_path('json/ssl-staging.json')), true);
        $ssl = Arr::first($sslList, fn ($value, $key) =>$value['domain'] == $active_domain);
        Log::info('SslManager::createStagingCertificate() $ssl: '.json_encode($ssl));
        $this->site->sslCertificates()->firstOrCreate(
            [
                'provider' => SslCertificate::PROVIDER_STAGING
            ], [
                'status' => SslCertificate::STATUS_NEW,
                'obtained_from' => "Let's Encrypt",
                'ssl_certificate' => Arr::get($ssl, 'ssl_certificate'),
                'ssl_private_key' => Arr::get($ssl, 'ssl_private_key')
            ]
        );
    }

    /**
     * @return Task
     */
    private function installCustomCertificate(): Task
    {
        $task = $this->site->run(new InstallCustomSslCertificate($this->site->sslCertificate));

        if ($task->successful()) {
            $this->site->sslCertificate->update(['status' => SslCertificate::STATUS_INSTALLED]);
        } else {
            $this->site->sslCertificate->update(['status' => SslCertificate::STATUS_FAILED]);
        }

        return $task;
    }

    public function markDomainAsProxied(): void
    {
        foreach ($this->site->team()->cloudflareIntegrations as $cloudflareIntegration) {
            $cloudflareService = new CloudflareService($cloudflareIntegration);

            $response = $cloudflareService->dnsRecords(
                zoneId: $this->site->getMeta('cloudflare_integration.zone_id'),
                name: $this->site->name
            );

            if(Arr::get($response, 'success') && Arr::get($response, 'result.0.id')){
                Log::info('dns record found. Enabling proxy'.' : '.Arr::get($response, 'result.0.id').' : '. $this->site->name);
                $response = $cloudflareService->updateDnsProxyStatus(
                    zoneId: $this->site->getMeta('cloudflare_integration.zone_id'),
                    id: Arr::get($response, 'result.0.id'),
                    proxied: true
                );

                break;
            }
        }
    }
}
