<?php

namespace App\Services\Site;

use App\Contracts\SiteContract;
use App\Models\Site;

class PhpMyAdminSite extends SiteManager
{
    public function geLogFilePaths(): string
    {
        return $this->siteBasePath().'/tmp/phpmyadmin.log';
    }

    public function getEnvironmentFilePath(): string
    {
        return $this->siteBasePath().'/config.inc.php';
    }

    public function siteBasePath(): string
    {
        return $this->siteBasePathWithWebRoot();
    }
}
