<?php

namespace App\Services\Site;

use App\Contracts\SiteContract;
use App\Models\Site;

class MauticSite extends SiteManager
{
    public function geLogFilePaths(): string
    {
        return $this->siteBasePath().'/var/logs/mautic.log';
    }

    public function getEnvironmentFilePath(): string
    {
        return $this->siteBasePath().'/.env';
    }

    public function siteBasePath(): string
    {
        return $this->siteBasePathWithWebRoot();
    }

    public function siteDocumentRoot(): string
    {
        return $this->siteBasePathWithWebRoot();
    }
}
