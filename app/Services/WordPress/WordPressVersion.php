<?php

namespace App\Services\WordPress;

use Illuminate\Support\Facades\Http;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

class WordPressVersion
{
    const LATEST = 'latest';
    const UNCHANGED = 'unchanged';

    /**
     * Available versions of PHP in the system
     *
     * @url https://api.wordpress.org/stats/wordpress/1.0/
     * @url http://api.wordpress.org/core/stable-check/1.0/
     *
     * @var array
     */
    const VERSIONS = [
        self::LATEST,
        self::UNCHANGED,
    ];

    const DEFAULT = 'latest';

    /**
     * Get all version
     *
     * @return array
     */
    static function getVersions(): array
    {
        $versions = [
            self::LATEST,
            "6.8", "6.7", "6.6", "6.5", "6.4", "6.3", "6.2", "6.1", "6.0", "5.9", "5.8", "5.7", "5.6", "5.5", "5.4", "5.3", "5.2", "5.1", "5.0", "4.9", "4.8", "4.7", "4.6", "4.5", "4.4", "4.3", "4.2", "4.1",
            self::UNCHANGED
        ];

        $liveVersions = cache()->remember('xcloud:wordpress-versions', now()->addDay(), function () {
            return Http::get("https://api.wordpress.org/core/stable-check/1.0/")
                ->collect()
                ->keys()
                ->filter(function ($version) {
                    // Filter out versions older than 4.0
                    preg_match('/^(\d+\.\d+)(?:\..*)?$/', $version, $matches);
                    return isset($matches[1]) && floatval($matches[1]) >= 4.0;
                })
                ->map(function ($version) {
                    // Extract the major.minor version from the full version string
                    return preg_replace('/^(\d+\.\d+)(?:\..*)?$/', '$1', $version);
                })
                ->unique() // Keep only unique major.minor versions
                ->reverse()
                ->values();
        });

        if (count($liveVersions) > 0) {
            $versions = [
                self::LATEST,
                ...$liveVersions,
                self::UNCHANGED,
            ];
        }

        return $versions;
    }

    /**
     * Get the list of available versions of PHP inside Rule::in
     *
     * @return In
     */
    static function asRule(): In
    {
        return Rule::in(self::getVersions());
    }
}
