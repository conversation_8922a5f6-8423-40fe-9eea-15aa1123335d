<?php

namespace App\Services\Mailbox;

use App\Services\DNS\DnsChecker;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Log;

class QboxMailService
{
    public PendingRequest $client;
    public CONST BASE_URL = 'https://api.qboxmail.com/api';

    public function __construct()
    {
        $this->client = Http::withHeaders([
            'X-Api-Token' => config('services.qboxmail.api_token'),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])
            ->timeout(180)
            ->baseUrl(self::BASE_URL)
            ->acceptJson();
    }

    public function checkDomainExists(string $domain): bool
    {
        return !empty(Arr::get($this->client->get('/domains?query=' . $domain)->json(), 'resources')) ?? true;
    }

    public function addNewDomain($data)
    {
        return $this->client->post('/domains', [
            'name' => $data['domain'],
            'postmaster_password' => $data['password'],
            'postmaster_password_confirmation' => $data['confirm_password'],
        ])->json();
    }

    public function getDomainDetails($domainCode)
    {
        return $this->client->get('/domains/' . $domainCode)->json();
    }

    public function updateDomainPlan($domainCode, $maxEmailQuota)
    {
        return $this->client->put('/domains/' . $domainCode, [
            'max_email_quota' => $maxEmailQuota,
        ])->json();
    }

    public function checkDnsOwnership($domainCode): bool
    {
        $domainDetails = $this->getDomainDetails($domainCode);

        if(!empty(Arr::get($domainDetails, 'error'))){
            Log::error('Error getting domain details: ' . Arr::get($domainDetails, 'error'));
            return false;
        }

        $domainFromQboxMail = Arr::get($domainDetails, 'resources');
        dd($domainFromQboxMail);
        if($domainDetails['is_healthy']){
            return true;
        }

        dd($domainDetails);

        $response = $this->client->put('/domains/'.$domainCode.'/dns_ownership_check');

        return $response->status() !== 204 && $response->status() !== 401 && $response->status() !== 500;

        // run host command to check A record(i.e host $domainCode.domain, for example, host d789319310.faisalibnaziz.com)

        return DnsChecker::check($domainCode . '.' . $domain, $ip);

        return $dnsOwnershipVerified;

    }

    public function checkMxRecord($domainCode)
    {
        $response = $this->client->put('/domains/' . $domainCode . '/dns');

        return true;
        return $response->status() !== 204 && $response->status() !== 401 && $response->status() !== 500;
    }

    public function addEmailAccount($domainCode, $data)
    {
        return $this->client->post('/domains/'.$domainCode.'/email_accounts', [
            'name' => $data['name'],
            'password' => $data['password'],
            'password_confirmation' => $data['confirm_password'],
            'firstname' => $data['firstname'],
        ])->json();
    }

    public function checkEmailStatus($domainCode, $emailAccountCode)
    {
        $response = $this->client->get('/domains/'.$domainCode.'/email_accounts/'.$emailAccountCode)->json();

        return $response;
    }

    public function getAllEmailAccountsForADomain($domainCode)
    {
        $response = $this->client->get('/domains/'.$domainCode.'/email_accounts')->json();

        return $response;
    }

    public function addNewEmailAlias($domainCode, $data)
    {
        $response = $this->client->post('/domains/'.$domainCode.'/alias_email_accounts', [
            'name' => $data['name'],
            'destinations' => $data['destinations'],
        ])->json();

        return $response;
    }
}
