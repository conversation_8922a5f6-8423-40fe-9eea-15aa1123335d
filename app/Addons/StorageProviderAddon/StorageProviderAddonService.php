<?php

namespace App\Addons\StorageProviderAddon;

use App\Addons\Contracts\AddonInterface;
use App\Addons\Traits\BillableAddonTrait;
use App\Models\AddonStorageProvider;

class StorageProviderAddonService implements AddonInterface
{

    use BillableAddonTrait;
    /**
     * @throws \Exception
     */
    public function create(array $data): AddonStorageProvider
    {
      return AddonStorageProvider::create($data);
    }

    public function enable(): void
    {
        // TODO: Implement enable() method.
    }

    public function disable(): void
    {
        // TODO: Implement disable() method.
    }

    public function getBillableModelClass(): string
    {
        return AddonStorageProvider::class;
    }
}
