<?php

namespace App\Addons\Enum\MailboxEnums;

use App\Traits\EnumHelper;

enum MailboxPlans: string
{
    use EnumHelper;

    case MAILBOX_1GB_PLAN = 'mailbox_1gb_plan';
    case MAILBOX_5GB_PLAN = 'mailbox_5gb_plan';
    case MAILBOX_8GB_PLAN = 'mailbox_8gb_plan';
    case MAILBOX_10GB_PLAN = 'mailbox_10gb_plan';
    case MAILBOX_20GB_PLAN = 'mailbox_20gb_plan';
    case MAILBOX_25GB_PLAN = 'mailbox_25gb_plan';
    case MAILBOX_50GB_PLAN = 'mailbox_50gb_plan';

    public static function getPlanPrice($plan): int
    {
        return match ($plan) {
            self::MAILBOX_5GB_PLAN->value, self::MAILBOX_8GB_PLAN->value => 1,
            self::MAILBOX_10GB_PLAN->value => 2,
            self::MAILBOX_20GB_PLAN->value => 3,
            self::MAILBOX_25GB_PLAN->value => 4,
            self::MAILBOX_50GB_PLAN->value => 10,
            default => 0,
        };
    }

    public static function getPlanLimit($plan): int
    {
        return match ($plan) {
            self::MAILBOX_1GB_PLAN->value => 1, // in GB
            self::MAILBOX_5GB_PLAN->value => 5,
            self::MAILBOX_8GB_PLAN->value => 8,
            self::MAILBOX_10GB_PLAN->value => 10,
            self::MAILBOX_20GB_PLAN->value => 20,
            self::MAILBOX_25GB_PLAN->value => 25,
            self::MAILBOX_50GB_PLAN->value => 50,
            default => 0,
        };
    }

    public static function getReadablePlanName(): array
    {
        return [
            self::MAILBOX_1GB_PLAN->value => '1GB ($1/month)',
            self::MAILBOX_5GB_PLAN->value => '5GB ($1/month)',
            self::MAILBOX_8GB_PLAN->value => '8GB ($1/month)',
            self::MAILBOX_10GB_PLAN->value => '10GB ($2/month)',
            self::MAILBOX_20GB_PLAN->value => '20GB ($3/month)',
            self::MAILBOX_25GB_PLAN->value => '25GB ($4/month)',
            self::MAILBOX_50GB_PLAN->value => '50GB ($10/month)',
        ];
    }

    public static function getFreePlanName(): array
    {
        return [
            self::MAILBOX_1GB_PLAN->value => '1GB(Free)',
        ];
    }

    public static function getPlanName($plan): string
    {
        return self::getReadablePlanName()[$plan] ?? 'Unknown Plan';
    }

    public static function getPlanSize($plan): string
    {
        return self::getPlanLimit($plan) . ' GB';
    }

    public static function getPlanSizeInBytes($plan): int
    {
        return self::getPlanLimit($plan) * 1024 * 1024 * 1024; // Convert GB to bytes
    }

    public static function getSku(): array
    {
        return [
            self::MAILBOX_1GB_PLAN->value => 'mbx-1gb-1',
            self::MAILBOX_5GB_PLAN->value => 'mbx-5gb-1',
            self::MAILBOX_8GB_PLAN->value => 'mbx-8gb-1',
            self::MAILBOX_10GB_PLAN->value => 'mbx-10gb-1',
            self::MAILBOX_20GB_PLAN->value => 'mbx-20gb-1',
            self::MAILBOX_25GB_PLAN->value => 'mbx-25gb-1',
            self::MAILBOX_50GB_PLAN->value => 'mbx-50gb-1',
        ];
    }

    public static function getHighestPlan(array $plans): string
    {
        $highestPlan = self::MAILBOX_1GB_PLAN->value;

        foreach ($plans as $plan) {
            if (self::getPlanLimit($plan) > self::getPlanLimit($highestPlan)) {
                $highestPlan = $plan;
            }
        }

        return $highestPlan;
    }

}
