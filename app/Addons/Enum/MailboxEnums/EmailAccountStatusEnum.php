<?php

namespace App\Addons\Enum\MailboxEnums;

use App\Traits\EnumHelper;

enum EmailAccountStatusEnum: string
{
    use EnumHelper;

    case NEW = 'new';
    case PENDING_VERIFICATION = 'pending_verification';
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case FAILED = 'failed';

    public static function readableStatus($status): string
    {
        return match ($status) {
            self::NEW->value => 'New',
            self::PENDING_VERIFICATION->value => 'Pending Verification',
            self::ACTIVE->value => 'Active',
            self::INACTIVE->value => 'Inactive',
            self::FAILED->value => 'Failed',
        };
    }
}
