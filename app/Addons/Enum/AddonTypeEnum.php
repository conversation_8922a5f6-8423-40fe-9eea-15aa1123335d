<?php

namespace App\Addons\Enum;

use App\Traits\EnumHelper;

enum AddonTypeEnum: string
{
    use EnumHelper;
    case SMTP_EMAIL = 'smtp_email';
    case MAILBOX = 'mailbox';
    case STORAGE_PROVIDER = 'storage_provider';

    public function label(): string
    {
        return match ($this) {
            self::MAILBOX => 'Mailbox Addon',
            self::SMTP_EMAIL => 'SMTP Addon',
            self::STORAGE_PROVIDER => 'Storage Provider Addon',
        };
    }
}
