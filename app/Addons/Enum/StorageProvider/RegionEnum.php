<?php

namespace App\Addons\Enum\StorageProvider;

use App\Traits\EnumHelper;

enum RegionEnum: string
{
    use EnumHelper;

    case EU_CENTRAL = 'eu-central';
    case US_WEST = 'us-west';
    case US_EAST = 'us-east';

    public function label(): string
    {
        return match ($this) {
            self::EU_CENTRAL => 'EU Central',
            self::US_WEST => 'US West',
            self::US_EAST => 'US East',
        };
    }
}
