<?php

namespace App\Addons\Contracts;

use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Models\Product;

interface BillableAddonInterface extends AddonInterface
{
    /**
     * Handle payment for the addon
     * 
     * @param array $data
     * @return array
     */
    public function handlePayment(array $data);
    
    /**
     * Get the product for the addon
     * 
     * @param string $plan
     * @return Product|null
     */
    public function getProduct(string $plan): ?Product;
    
    /**
     * Generate invoice for the addon
     * 
     * @param InvoiceSourceEnum $source
     * @param bool $notification
     * @return mixed
     */
    public function generateInvoice(InvoiceSourceEnum $source, bool $notification = false);
    
    /**
     * Check if service is provided after payment
     * 
     * @return bool
     */
    public function serviceProvidedAfterPayment(): bool;
    
    /**
     * Provide service after payment
     * 
     * @return bool
     */
    public function provideService(): bool;
}
