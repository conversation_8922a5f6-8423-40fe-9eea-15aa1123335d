<?php

namespace App\Addons;

use App\Addons\Contracts\AddonInterface;
use App\Addons\Enum\AddonTypeEnum;
use App\Addons\MailboxAddon\MailboxAddonService;
use App\Addons\StorageProviderAddon\StorageProviderAddonService;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Jobs\TakePaymentFromInvoiceJob;
use App\Models\CartForm;
use App\Models\Product;
use App\Models\Team;
use InvalidArgumentException;
use Stripe\Checkout\Session;
use Stripe\Customer;
class AddonsManager
{
    private Product $product;
    private Team $team;
    public function resolve(AddonTypeEnum $type): AddonInterface
    {
        return match ($type) {
            AddonTypeEnum::MAILBOX => new MailboxAddonService(),
            AddonTypeEnum::STORAGE_PROVIDER => new StorageProviderAddonService(),
            default => throw new InvalidArgumentException("Unsupported addon type."),
        };
    }

    public function setTeam(Team $team){
        $this->team = $team;
        return $this;
    }

    public function setProduct(Product $product){
        $this->product = $product;
        return $this;
    }

    public function takePayment()
    {
        $uniqueId = uniqid();

        $customer = $this->createStripeCustomer(uniqueId:$uniqueId);
        $session = $this->getPaymentSession(customer: $customer);
        $cart = $this->prepareCart(
            session: $session,
            customer: $customer,
            uniqueId: $uniqueId
        );


        // create a cart
    }

    private function createStripeCustomer(string $uniqueId)
    {
        return Customer::create([
            'name' => $this->team->owner->name,
            'email' => explode('@', $this->team->email)[0] . '_' . $uniqueId . '@' . explode('@', $this->team->email)[1],
            'description' => 'Customer ' . $this->team->name . ' , added when purchasing Email Provider ',
        ]);
    }

    private function prepareCart(Session $session, Customer $customer,string $uniqueId)
    {
        return CartForm::create([
            'model' => $this->product->service_type->getServiceModel(),
            'email'=> explode('@', $this->team->email)[0] . '_' . $uniqueId . '@' . explode('@', $this->team->email)[1],
            'status'=> CartFormStatuses::Pending,
            'service' => $this->product->service_type,
            'product_id' => $this->product->id,
            'checkout_session_id' => $session->id,
            'meta->stripe->checkout_session' => [
                'customer_id' => $customer->id,
                'checkout_url' => $session->url,
            ],
            'team_id' => $this->team->id,
        ]);
    }

    private function getPaymentSession(Customer $customer): Session
    {

        $queryParams = "session_id={CHECKOUT_SESSION_ID}&product_id={$this->product->id}";

        return Session::create([
            'payment_method_types' => ['card'],
            'mode' => 'setup',
            'customer' => $customer->id,
            'success_url' => route('stripe.product.checkout.success', [], true) . "?{$queryParams}",
            'cancel_url' =>  route('stripe.product.checkout.cancelled')
        ]);

    }


}
