<?php

namespace App\Addons\Traits;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Models\Product;
use App\Repository\AddonBillingRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

/**
 * Trait BillableAddonTrait
 * 
 * This trait provides standard implementation for BillableAddonInterface
 * to be used by addon service classes.
 */
trait BillableAddonTrait
{
    /**
     * Get the billable model class name
     * 
     * @return string
     */
    abstract public function getBillableModelClass(): string;
    
    /**
     * Get the billable model
     * 
     * @param int $id
     * @return Model
     */
    public function getBillableModel(int $id): Model
    {
        $modelClass = $this->getBillableModelClass();
        return $modelClass::findOrFail($id);
    }

    /**
     * Handle payment for the addon
     *
     * @param array $data
     * @return array
     * @throws \Exception
     */
    public function handlePayment(array $data): array
    {
        $modelId = Arr::get($data, 'model_id');
        
        if (!$modelId) {
            throw new \InvalidArgumentException("Missing required parameter: {$modelId}");
        }
        
        // Get the billable model
        $billableModel = $this->getBillableModel($modelId);

        $team = $billableModel->team;

        if(!$team){
            throw new \Exception('Team is missing with this billable model: ' . $billableModel::class);
        }

        $repository = new AddonBillingRepository($billableModel, $team);

        // if product is empty, throw error
        if (!$repository->getProduct()) {
            throw new \Exception('No product found for this addon');
        }

        // If a product is specified, set it
        if (isset($data['product_id'])) {
            $product = Product::findOrFail($data['product_id']);
            $repository->setProduct($product);
        }

        if(empty($repository->getProduct())){
            throw new \Exception('No product found for this addon');
        }

        // Handle the payment
        return $repository->handlePayment($data);
    }

    public function getProduct(string $plan): ?Product
    {
        // TODO: Implement getProduct() method.
    }

    public function generateInvoice(InvoiceSourceEnum $source, bool $notification = false)
    {
        // TODO: Implement generateInvoice() method.
    }

    public function serviceProvidedAfterPayment(): bool
    {
        // TODO: Implement serviceProvidedAfterPayment() method.
    }

    public function provideService(): bool
    {
        // TODO: Implement provideService() method.
    }
}
