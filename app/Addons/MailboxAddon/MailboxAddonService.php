<?php

namespace App\Addons\MailboxAddon;

use App\Addons\AddonsManager;
use App\Addons\Contracts\BillableAddonInterface;
use App\Addons\Enum\AddonTypeEnum;
use App\Addons\Enum\MailboxEnums\EmailAccountStatusEnum;
use App\Addons\Enum\MailboxEnums\MailboxPlans;
use App\Addons\Enum\MailboxEnums\MailboxStatus;
use App\Addons\Traits\BillableAddonTrait;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Models\Addons\EmailAccount;
use App\Models\Addons\MailboxDomain;
use App\Models\Product;
use App\Models\Team;
use App\Repository\AddonBillingRepository;
use App\Services\Mailbox\QboxMailService;
use Illuminate\Support\Arr;

class MailboxAddonService implements BillableAddonInterface
{
    use BillableAddonTrait;

    /**
     * @throws \Exception
     */
    public function create(array $data): array
    {
        $manager = new AddonsManager();
        $addon = $manager->resolve(AddonTypeEnum::MAILBOX);

        $errors = [];
        if(empty($data['mailboxes'])){
            $errors[] = 'No mailboxes provided';
            return [
                'status' => 'error',
                'message' => $errors,
            ];
        }

        $domains = [];
        foreach($data['mailboxes'] as $mailbox) {
            $domains[] = explode('@', $mailbox['email'])[1];
        }

        $domains = array_unique($domains);

        foreach($domains as $domain) {
            ## Domain Creation ##
            $mailboxesUnderDomain = array_filter($data['mailboxes'], function($mailbox) use ($domain) {
                return explode('@', $mailbox['email'])[1] === $domain;
            });

            $plansForDomain = collect($mailboxesUnderDomain)->pluck('selected_plan')->toArray();

            $highestPlanForDomain = MailboxPlans::getHighestPlan($plansForDomain);

            // check if the domain already exists
            $mailboxDomain = MailboxDomain::where('domain', $domain)
                ->first();

            if($mailboxDomain){
                // take the highest plan among the existing domain plans
                if(MailboxPlans::getPlanLimit($mailboxDomain->plan->value) > MailboxPlans::getPlanLimit($highestPlanForDomain)){
                    $highestPlanForDomain = $mailboxDomain->plan;
                }
            }

            $mailboxDomain = MailboxDomain::updateOrCreate([
                'domain' => $domain,
            ], [
                'team_id' => team()->id,
                'password' => \Str::random(10),
                'status' => MailboxStatus::NEW,
                'plan' => $highestPlanForDomain,
            ]);

            // add domain to qboxmail via API
            $domainData = [
                'domain' => $domain,
                'password' => $mailboxDomain->password,
                'confirm_password' => $mailboxDomain->password,
            ];


            $domainExists = (new QboxMailService())->checkDomainExists($domain);

            if(!$domainExists){
                $response = (new QboxMailService())->addNewDomain($domainData);
                $resourceCode = Arr::get($response, 'resource_code');

                if(empty($resourceCode)){
                    $mailboxDomain->markAsFailed();
                    $errors[] = 'Unable to create domain: ' . $domain . ' via API.';
                    return [
                        'status' => 'error',
                        'message' => $errors,
                    ];
                }else{
                    $mailboxDomain->update([
                        'domain_code' => $resourceCode,
                        'status' => MailboxStatus::PENDING_VERIFICATION,
                    ]);
                }
            }

            ## Email Account Creation ##
            foreach($mailboxesUnderDomain as $mailbox) {
                $emailAccount = $mailboxDomain->emailAccounts()->updateOrCreate([
                    'email' => $mailbox['email'],
                ],[
                    'team_id' => team()->id,
                    'password' => $mailbox['password'],
                    'plan' => $mailbox['selected_plan'],
                    'status' => EmailAccountStatusEnum::PENDING_VERIFICATION,
                    'mailbox_size' => MailboxPlans::getPlanSize($mailbox['selected_plan']),
                ]);

                // handle payment for each mailbox
                $paymentResponse = $addon->handlePayment([
                    'model_id' => $emailAccount->id,
                    'team_id' => $emailAccount->team->id,
                ]);
            }
        }

        return [
            'status' => 'success',
            'message' => 'Mailbox created successfully.'
        ];
    }

    public function enable(): void
    {
        // TODO: Implement enable() method.
    }

    public function disable(): void
    {
        // TODO: Implement disable() method.
    }

    public function attachEmailAccounts(MailboxDomain $mailboxDomain): void
    {
        foreach ($mailboxDomain->emailAccounts as $emailAccount) {
            if(empty($emailAccount->email_account_code)){
                $response = (new QboxMailService())->addEmailAccount($mailboxDomain->domain_code, [
                    'name' => explode('@', $emailAccount->email)[0],
                    'password' => $emailAccount->password,
                    'confirm_password' => $emailAccount->password,
                    'firstname' => explode('@', $emailAccount->email)[0],
                ]);

                if(Arr::get($response, 'resource_code')){
                    $emailAccount->update([
                        'email_account_code' => Arr::get($response, 'resource_code'),
                        'status' => EmailAccountStatusEnum::ACTIVE,
                    ]);
                }else{
                    $emailAccount->markAsFailed();
                }
            }
        }
    }

    /**
     * Get the billable model class name
     *
     * @return string
     */
    public function getBillableModelClass(): string
    {
        return EmailAccount::class;
    }
}