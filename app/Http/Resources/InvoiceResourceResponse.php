<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\GeneralInvoice
 */
class InvoiceResourceResponse extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->only([
            'invoice_number', 'title', 'description',
            'amount', 'currency', 'status', 'type', 'source', 'team_id',
            'due_date', 'created_at', 'updated_at', 'status_readable', 'number'
        ]);
    }
}
