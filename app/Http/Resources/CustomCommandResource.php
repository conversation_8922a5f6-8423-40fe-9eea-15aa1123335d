<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomCommandResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'user' => $this->user,
            'status' => $this->status,
            'type' => $this->type,
            'command' => $this->command,
            'initiatedBy' => $this->initiatedBy?->only(['id', 'name', 'profile_photo_url']),
            'created_at' => $this->created_at?->format('M jS, g:i:s A')
        ];
    }
}
