<?php

namespace App\Http\Resources\Addons;

use Illuminate\Http\Resources\Json\JsonResource;

class EmailAccountResource extends JsonResource
{

    /**
     * Class EmailAccountResource
     * @mixin  \App\Models\Addons\EmailAccount
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'mailbox_domain_id' => $this->mailbox_domain_id,
            'email' => $this->email,
            'password' => $this->password,
            'masked_password' => $this->masked_password,
            'plan' => $this->plan,
            'mailbox_size' => $this->mailbox_size,
            'forwarding_emails' => $this->forwarding_emails,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
