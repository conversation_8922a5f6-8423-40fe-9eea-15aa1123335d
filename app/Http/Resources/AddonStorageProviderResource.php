<?php

namespace App\Http\Resources;

use Aws\S3\S3Client;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AddonStorageProviderResource extends JsonResource
{
    private function s3Agent(): ?S3Client
    {
        $storageProvider = $this->resource->storageProvider;
        return $storageProvider ?  $storageProvider->s3Client() : null;
    }

    public function getTotalUsageByAgent(): float|int
    {
        $agent = $this->s3Agent(); // This is your Backblaze S3-compatible client
        if (!$agent) return 0;

        $totalSize = 0;
        $continuationToken = null;
        do {
            $params = [
                'Bucket' => $this->resource->provider_bucket,
                'MaxKeys' => 1000,
            ];

            if ($continuationToken) {
                $params['ContinuationToken'] = $continuationToken;
            }

            $result = $agent->listObjectsV2($params);

            if (isset($result['Contents'])) {
                foreach ($result['Contents'] as $object) {
                    $totalSize += $object['Size'];
                }
            }

            $continuationToken = $result['IsTruncated'] ? $result['NextContinuationToken'] : null;

        } while ($continuationToken);
        //convert to GB
        return round($totalSize / **********, 2); // 1 GB = 1024^3 bytes
    }

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        // Get the latest report for this provider
        $latestReport = $this->resource->backblazeReport;

        // Calculate usage percentage
        $bucketLimit = $this->product->unit ?? 0;
        if ($latestReport) {
            $storedGB = (float) $latestReport->stored_gb;
        }else{
            $storedGB = cache()->remember('storage_provider'.$this->id.'_usage', now()->addHour(), function () {
                return $this->getTotalUsageByAgent();
            });
        }
        $usagePercentage = $bucketLimit > 0 ? min(round(($storedGB / $bucketLimit) * 100), 100) : 0;
        $usageData = [
            'stored_gb' => $storedGB,
            'usage_percentage' => $usagePercentage,
            'bucket_limit' => $bucketLimit,
            'has_data' => (bool) $latestReport,
        ];
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'bucket_name' => $this->bucket_name,
            'bucket_id' => $this->bucket_id,
            'region' => $this->region,
            'product_id' => $this->product_id,
            'product' => $this->whenLoaded('product'),
            'usage_data' => $usageData,
        ];
    }
}
