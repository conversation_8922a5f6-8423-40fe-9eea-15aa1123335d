<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class AddonStorageProviderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // Get the latest report for this provider
        $latestReport = $this->backblazeReports()
            ->orderBy('date', 'desc')
            ->first();
            
        // Calculate usage percentage
        $usagePercentage = 0;
        $storedGB = 0;
        $bucketLimit = $this->product->unit ?? 0;
        
        if ($latestReport) {
            $storedGB = (float) $latestReport->stored_gb;
            $usagePercentage = $bucketLimit > 0 ? min(round(($storedGB / $bucketLimit) * 100), 100) : 0;
            
            // Get monthly totals
            $startOfMonth = now()->startOfMonth()->format('Y-m-d');
            $endOfMonth = now()->format('Y-m-d');
            
            $monthlyReports = $this->backblazeReports()
                ->whereBetween('date', [$startOfMonth, $endOfMonth])
                ->get();
                
            $usageData = [
                'stored_gb' => $storedGB,
                'usage_percentage' => $usagePercentage,
                'bucket_limit' => $bucketLimit,
                'uploaded_gb' => $monthlyReports->sum('uploaded_gb'),
                'downloaded_gb' => $monthlyReports->sum('downloaded_gb'),
                'api_transactions' => $monthlyReports->sum('api_txn_class_a') + 
                                     $monthlyReports->sum('api_txn_class_b') + 
                                     $monthlyReports->sum('api_txn_class_c'),
                'last_updated' => $latestReport->date,
                'has_data' => true
            ];
        } else {
            // No reports available
            $usageData = [
                'stored_gb' => 0,
                'usage_percentage' => 0,
                'bucket_limit' => $bucketLimit,
                'uploaded_gb' => 0,
                'downloaded_gb' => 0,
                'api_transactions' => 0,
                'has_data' => false
            ];
        }
        
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'bucket_name' => $this->bucket_name,
            'bucket_id' => $this->bucket_id,
            'region' => $this->region,
            'product_id' => $this->product_id,
            'product' => $this->whenLoaded('product'),
            'usage_data' => $usageData,
        ];
    }
}
