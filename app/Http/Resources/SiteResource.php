<?php

namespace App\Http\Resources;

use App\Enums\SiteStatus;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use function PHPUnit\Framework\isEmpty;

/**
 * @mixin \App\Models\Site
 */
class SiteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if (! $this->relationLoaded('tags')) {
            $this->load(['tags' => fn($query) => $query->select('name', 'tags.id')]);
        }

        $array = $this->only([
            'id', 'name','title', 'site_url', 'environment', 'type', 'web_root',
            'site_admin_url', 'cloud_provider', 'email_provider_id',
            'size', 'region', 'tags', 'wordpress_version', 'php_version',
            'status', 'is_disabled', 'site_user','wp_updates',
            'database_name', 'created_at', 'parent_site_id'
        ]);
        $array['has_vulnerability_scan'] = $this->isVulnerabilityScanEnable();

        if ($this->patchstackVulnerability()->where(['is_purchase' => true])->exists()) {
            $array['vulnerabilities_count'] = empty($this->vulnerabilities_count) ?  $this->patchstackVulnerabilitySite()->where('ignored',false)->count() : $this->vulnerabilities_count;
            $array['is_patchstack'] = true;
        } else {
            $array['vulnerabilities_count'] = empty($this->vulnerabilities_count) ?  $this->vulnerabilities()->where('ignored',false)->count() : $this->vulnerabilities_count;
            $array['is_patchstack'] = false;
        }

        $array['is_backup_enabled'] = is_null($this->is_backup_enabled) ? $this->backupSettings()->where(['auto_backup'=>true])->exists() : $this->is_backup_enabled;
        $array['has_local_backup']  = is_null($this->has_local_backup) ? $this->backupFiles()->where(['is_remote'=>false])->exists() : $this->has_local_backup;

        $backup_progress = $this->getMeta('backup_task_running',false);
        $array['backup_task_running'] = $backup_progress && Carbon::parse($backup_progress)->diffInMinutes(now()) < 10;

        if ($this->relationLoaded('server')) {
            $array['server'] = ServerResource::globalResource($this->server);
            $site_state = ServerResource::monitor($this->server)?->site_states;
               if ($site_state){
                   $array['site_size'] =  Arr::first($site_state, function ($value, $key) use ($array) {
                       return array_key_exists('site_name',$value) && $value['site_name'] == $array['name'];
                   });
               }
        }

        $array['last_update_check'] = ($array['wp_updates']['datetime'] ?? null) ? Carbon::parse($this->wp_updates['datetime'])->diffForHumans() : null;
        $array['has_https'] = $this->hasSslCertificate();
        $array['has_caching'] = $this->hasFullPageCaching();
        $array['state'] = $this->state;
        $array['status'] = $this->status->value;
        $array['status_readable'] = title($this->status->value);
        $array['status_color'] = $this->getStateColor();
        $array['has_basic_auth'] = $this->hasBasicAuthEnabled();
        $array['theme_updates'] = $this->theme_updates();
        $array['plugin_updates'] = $this->plugin_updates();
        $array['wp_core_updates'] = $this->wp_core_updates();
        $array['meta'] = $this->meta;
        $array['is_git'] = $this->getMeta('is_git', false);
        $array['permissions'] = $this->permissions();
        $array['has_database'] = $this->hasDatabase();
        $array['is_wordpress'] = $this->isWordpress();
        $array['is_node_app'] = $this->isNodeApp();
        $array['is_one_click_app'] = $this->isOneClickApp();
        $array['has_migration'] = $this->siteMigration !== null;
        $array['provision_percentage'] = $this->getMeta('provisioningStatusPercentage', 0);
        $array['migration_percentage'] = $this->siteMigration !== null ? $this->siteMigration->getMeta('migratingStatusPercentage', 0) : 0;
        $array['created_at_readable'] = Carbon::parse($this->created_at)->format('jS F, Y');
        $array['is_domain_updating'] = $this->isDomainUpdating();
        $array['ssl_provider'] = $this->ssl_provider;
        $array['is_ip_site'] = $this->isIPSite();
        $array['is_testing'] = $this->isTestingSite();

        $array['can_view_server'] = user()->can('view', $this->server);

        if($this->hasStagingEnvironment()){
            $array['domain_parking_method'] = Site::STAGING;
            $array['temporary_site_url'] = $this->getTemporarySiteUrl();
        }

        if($this->isPhpMyAdmin()){
            $array['site_url'] = route('server.phpmyadmin-login', $this->server_id);
        }

        if($this->isMultiSite()){
            $array['is_multisite'] = $this->isMultiSite();
            $array['multisite_subdomain'] = $this->isMultiSiteSubdomain();
        }

        return $array;
    }

    public static function globalResource($resource){

        $cacheKey = get_class($resource).":".$resource->id;

        return $GLOBALS[$cacheKey] ?? ($GLOBALS[$cacheKey] = self::make($resource));
    }

    public function serverMonitor($server)
    {
       return ServerResource::monitor($server);
    }

    public static function minimal($site) : array
    {
        return [
            'id' => $site->id,
            'name' => $site->name,
            'is_disabled' => $site->is_disabled,
            'is_connected' => $site->is_connected,
            'cloud_provider' => $site->server?->cloudProvider?->only(['id', 'name', 'provider']),
            'status' => $site->status,
            'environment' => $site->environment,
            'type' => $site->type,
            'server' => [
                'id' => $site->server->id,
                'name' => $site->server->name,
                'public_ip' => $site->server->public_ip,
                'status' => $site->server->status,
                'provider' => [
                    'id' => $site->server?->cloudProvider?->id,
                    'name' => $site->server?->cloudProvider?->name,
                    'provider' => $site->server?->cloudProvider?->provider,
                    'img' => $site->server?->cloudProvider?->getDefinedAttribute['img'] ?? null,
                    'defined_name' => $site->server?->cloudProvider?->getDefinedAttribute['name'] ?? null,
                    'description' => $site->server?->cloudProvider?->getDefinedAttribute['description'] ?? null,
                ]
            ],
            'created_at_formatted' => $site->created_at->format('jS F, Y h:i:s A') . ' UTC',
            'updated_at_formatted' => $site->updated_at->format('jS F, Y h:i:s A') . ' UTC',
            'created_at' => $site->created_at,
            'updated_at' => $site->updated_at
        ];
    }
}
