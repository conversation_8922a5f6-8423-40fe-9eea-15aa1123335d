<?php

namespace App\Http\Resources;

use App\Models\CloudProvider;
use Illuminate\Http\Resources\Json\JsonResource;
use Str;

/** @mixin CloudProvider */
class CloudProviderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'do_token' => Str::limit($this->do_token, 10),
        ];
    }
}
