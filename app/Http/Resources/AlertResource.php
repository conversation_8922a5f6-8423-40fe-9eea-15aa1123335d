<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AlertResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'created_at'=>$this->created_at,
            'id' => $this->id,
            'level' => $this->level,
            'meta' => $this->meta,
            'related_id' => $this->related_id,
            'related_type' => $this->related_type,
            'notifiable_id' => $this->notifiable_id,
            'notifiable_type' => $this->notifiable_type,
            'type' => $this->type,
            'updated_at' => $this->updated_at,
            'is_read' => auth()->check() && auth()->user()->getAlertLastSeenId() >= $this->id,
        ];
    }
}
