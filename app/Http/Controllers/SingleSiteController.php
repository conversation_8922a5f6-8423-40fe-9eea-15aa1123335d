<?php

namespace App\Http\Controllers;

use App\Callbacks\MarkAsSiteDeleted;
use App\Enums\BackupType;
use App\Enums\CustomNginxEnum;
use App\Enums\Integration\PluginIntegrationType;
use App\Enums\IPAddressType;
use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Enums\TaskType;
use App\Enums\WPCachePlugins;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Events\SiteDeleted;
use App\Http\Requests\StoreIpAddressRequest;
use App\Http\Resources\CustomCommandResource;
use App\Http\Resources\ServerResource;
use App\Http\Resources\SiteResource;
use App\Http\Resources\TaskResource;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\DeploymentLog;
use App\Models\EmailProvider;
use App\Models\PatchstackVulnerability;
use App\Models\PhpVersion;
use App\Models\Product;
use App\Models\Redirection;
use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Models\StorageProvider;
use App\Models\Tag;
use App\Models\Task;
use App\Models\WordfenceVulnerability;
use App\Models\Team;
use App\Repository\SiteRepository;
use App\Services\Vulnerability\PatchstackService;
use App\Services\WordPress\FullPageCaching;
use Arr;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Laravel\Jetstream\Role;

class SingleSiteController extends Controller
{

    function show(?Server $server, ?Site $site)
    {
        $this->authorize('view', $site);

        return to_route('site.overview', ['server' => $site->server, 'site' => $site]);
    }

    function progress(Server $server, Site $site)
    {
        $this->authorize('view', $site);

        return Inertia::render('SiteProgress', $site->getProgress());
    }

    /**
     * @throws AuthorizationException
     */
    public function overview(Server $server, Site $site)
    {
        $this->authorize('view', $site);

        $page = 'Site/Overview';
        if ($site->showMigrationBanner()) {
            $page = 'Site/MigrationBanner';
            $site->saveMeta('show_migration_banner', false);
        }

        $site->load(['tags' => fn($query) => $query->select('name', 'tags.id')]);

        $site->updateQuietly([
            'last_visited_at' => now()
        ]);

        $lastVisitedSites = user()->getMeta('latest_visited_sites_for_team_'.team()->id, []);

        if (!in_array($site->id, $lastVisitedSites)) {
            array_unshift($lastVisitedSites, $site->id);
            user()->saveMeta('latest_visited_sites_for_team_'.team()->id, $lastVisitedSites);
        }

        $task_events = $site
            ->tasks()
            ->with([
                'server:id,name',
                'site:id,name',
                'initiatedBy:id,name,email',
                'team',
            ])
            ->latest()
            ->paginate(5)
            ->through(fn($task) => TaskResource::make($task));

        return Inertia::render($page, [
            'server' => ServerResource::make($server),
            'additional_domains' => $site->additional_domains,
            'has_page_cache' => $site->hasPageCache(),
            'has_ssl_certificate' => $site->hasSslCertificate(),
            'has_basic_auth_enabled' => $site->hasBasicAuthEnabled(),
            'enable_php_execution_on_upload_directory' => $site->enablePhpExecutionOnUploadDirectory(),
            'enable_xml_rpc' => $site->enableXmlRpc(),
            'taskEvents' => $task_events,
            'database_provider' => $site->database_provider,
            'site' => SiteResource::make($site),
            'mail_alert'=> $site->getMeta('mail_alert',true) && $server->team->whiteLabel()->doesntExist(),
            'hasIntegrityChecksumError' => $site->hasIntegrityChecksumError(),
        ]);
    }

    public function updates(Server $server, Site $site)
    {
        $this->authorize('update', $site);
        return Inertia::render('Site/Updates', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'additional_domains' => $site->additional_domains,
            'has_remote_backup_settings' => $site->backupSettings()->where(['is_local'=>false])->exists()
        ]);
    }

    public function vulnerabilityScan(Server $server, Site $site)
    {
      //  abort_unless($site->isVulnerabilityScanEnable(), 403, 'Vulnerability scan is not available for this site.');
        $this->authorize('siteVulnerabilityScan', $site);
        if(team()->isFreePlan()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $scan_result = WordfenceVulnerability::scanVulnerability($site->wp_updates);
        $wordpress_vulnerabilities = collect(collect($scan_result)->get('wordpress'))->pluck('vulnerabilities')->flatten(1)->first();
        $last_scan_check = $site->getMeta('last_scan_check');
        $ignored_vulnerabilities = $site->vulnerabilities()->where('ignored', true)->pluck('slug')->toArray();
        $product = Product::where('service_type', BillingServices::PatchstackAddon)->first();

        $paymentMethods = team()->paymentMethods()->select([
            'id',
            'card_no',
            'default_card',
            'payment_gateway',
            'status',
            'meta->stripe->expiry_month as expiry_month',
            'meta->stripe->expiry_year as expiry_year',
            'meta->stripe->brand as brand',
        ])->orderBy('default_card', 'DESC')->get();
        $defaultCard = team()->activePaymentMethod()->first();

        if ($site->patchstackVulnerability()->where(['is_purchase' => true])->first()) {
            $ignored_vulnerabilities = $site->patchstackVulnerabilitySite()->where('ignored', true)->pluck('slug')->toArray();
            $lastPatchstackScan = $site->getMeta('last_patchstack_scan') ?? null;

            return Inertia::render('Site/PatchstackVulnerabilities', [
                'server' => ServerResource::make($server),
                'site' => SiteResource::make($site),
                'patchstack_vulnerability' => $site->patchstackVulnerability()->first(),
                'ignored_vulnerabilities' => $ignored_vulnerabilities,
                'last_patchstack_scan' => $lastPatchstackScan ? Carbon::parse($lastPatchstackScan)->diffForHumans() : null
            ]);
        } else {
            return Inertia::render('Site/Vulnerabilities', [
                'server' => ServerResource::make($server),
                'site' => SiteResource::make($site),
                'scan_result' => $scan_result,
                'wordpress_vulnerabilities' => $wordpress_vulnerabilities,
                'last_scan_check' => $last_scan_check,
                'ignored_vulnerabilities' => $ignored_vulnerabilities,
                'patchstack_vulnerability' => $site->patchstackVulnerability()->where(['is_purchase' => true])->first(),
                'paymentMethods' => $paymentMethods,
                'defaultCard' => $defaultCard,
                'productPrice' => $product?->price
            ]);
        }
    }

    public function git(Server $server, Site $site)
    {
        $this->authorize('update', $site);
        abort_unless($site->getMeta('is_git', false), 403);
        $git_deployment_url =  route('git-site.deploy', [hashid_encode($site->id)]);
        return Inertia::render('Site/GitSettings', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'git_info' =>$site->getMeta('git_info'),
            'git_deployment_url' => $git_deployment_url,
            'deploy_script' => $site->getMeta('deploy_script'),
            'title' => "Git Settings - {$site->name}"
        ]);
    }

    public function sslHttps(Server $server, Site $site)
    {
        // abort_if($site->isIPSite(), 403, 'SSL is not available for IP sites.');

        $this->authorize('ssl', $site);
        $certificate = $site->sslCertificate;

        if ($certificate) {
            $certificateData = [
                'site_id' => $certificate->site_id,
                'provider' => !$site->hasStagingEnvironment() || !$site->hasDemoEnvironment() ? $certificate->provider : null,
                'obtained_from' => !$site->hasStagingEnvironment() || !$site->hasDemoEnvironment() ? $certificate->obtained_from : null,
                'status' => !$site->hasStagingEnvironment() || !$site->hasDemoEnvironment() ? $certificate->status : null,
                'ssl_certificate' => !$site->hasStagingEnvironment() || !$site->hasDemoEnvironment() ? $certificate->ssl_certificate : null,
                'ssl_private_key' => !$site->hasStagingEnvironment() || !$site->hasDemoEnvironment() ? $certificate->ssl_private_key : null,
                'expires_at' => !$site->hasStagingEnvironment() || !$site->hasDemoEnvironment() ? optional($certificate->expires_at)->format('Y-m-d') : null,
                'is_expired' => $certificate->expires_at && $certificate->expires_at->isPast(),
                'renew_date' => !$site->hasStagingEnvironment() || !$site->hasDemoEnvironment()
                    ? ($certificate->expires_at ? $certificate->expires_at->subWeek()->format('Y-m-d') : null)
                    : null,
            ];

            if ($certificate->expires_at && $certificate->expires_at->isPast()){
                $certificateData['status'] = 'expired';
            }
        } else {
            $certificateData = null;
        }

        return Inertia::render('Site/SslHttps', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'ssl_provider' => $site->ssl_provider,
            'ssl_certificate' => $certificateData,
            'title' => 'SSL & HTTPS',
            'multisite_subdomain_enabled' => $site->isMultiSite() && $site->isMultiSiteSubdomain()
        ]);
    }

    public function fullPageCaching(Server $server, Site $site)
    {
        $this->authorize('manageCache', $site);
        $data = [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'cache_plugins' => WPCachePlugins::all(),
            'has_page_caching' => (bool) $site->getMeta('has_fullpage_caching') || !empty($site->getMeta('wp_cache_plugin')),
            'has_fullpage_caching' => (bool)$site->getMeta('has_fullpage_caching'),
            'wp_cache_plugin' => (bool)$site->getMeta('wp_cache_plugin'),
            'cache_plugin_name' => Arr::get(Arr::get(WPCachePlugins::all(), $site->getMeta('wp_cache_plugin')), 'plugin'),
            'cache_plugin_slug' => $site->getMeta('wp_cache_plugin'),
            'has_redis_object_caching' => (bool)$site->getMeta('has_redis_object_caching'),
            'cache_duration' => $site->getMeta('cache_duration') ?: FullPageCaching::DEFAULT_CACHE_DURATION,
            'cache_duration_unit' => $site->getMeta('cache_duration_unit') ?: FullPageCaching::DEFAULT_CACHE_DURATION_UNIT,
            'cache_exclusion_http_rules' => $site->getMeta('cache_exclusion_http_rules') ?: str(FullPageCaching::DEFAULT_HTTP)->explode('|')->implode(PHP_EOL),
            'cache_exclusion_cookie_rules' => $site->getMeta('cache_exclusion_cookie_rules') ?: str(FullPageCaching::DEFAULT_COOKIE)->explode('|')->implode(PHP_EOL),
            'redis_password' => $site->redis_password,
            'redis_object_cache_key' => $site->redis_object_cache_key,
            'object_cache_pro' => $site->pluginIntegrations()->where('type', PluginIntegrationType::OBJECT_CACHE_PRO)->first(),
            'cloudflare_edge_cache' => [
                'enable' => $site->getMeta('cloudflare_edge_cache.enable'),
                'can_enable' => $site->ssl_provider === SslCertificate::PROVIDER_CLOUDFLARE,
                'error_message' => $site->ssl_provider === SslCertificate::PROVIDER_CLOUDFLARE ? null : trans('Please configure Cloudflare SSL on this site to enable edge cache.'),
                'cache_exclusion_http_rules' => $site->getMeta('cloudflare_edge_cache.cache_exclusion_http_rules') ?: collect(FullPageCaching::CLOUDFLARE_HTTP_RULES)->implode(PHP_EOL),
                'cache_exclusion_cookie_rules' => $site->getMeta('cloudflare_edge_cache.cache_exclusion_cookie_rules') ?: collect(FullPageCaching::CLOUDFLARE_COOKIE_RULES)->implode(PHP_EOL),
            ],
        ];

        if ($server->stack->isNginx()){
            return Inertia::render('Site/FullPageCaching', $data);
        }else{
            return Inertia::render('Site/LiteSpeedCaching', $data);
        }
    }

    public function sshFtp(Server $server, Site $site)
    {
        $this->authorize('siteSSHsFTP', $site);
        return Inertia::render('Site/SshSftp', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'site_user' => $site->site_user,
            'site_path' => $site->manager()->siteBasePath(),
            'site_ssh_authentication_mode' => $site->site_ssh_authentication_mode ?: 'public_key',
            'selected_ssh_keypair_ids' => $site->sshKeyParis()->pluck('id')->toArray(),
            'keys' => team()->sshKeyParis()->select(['id as value', 'name as label'])->where('name', 'not like', '%migration%')->get(), // do not show system generated ssh keys to users
            'can_create_ssh_key' => user()->can('createEditSudoUser',$server),
            'hasDatabase' => $site->hasDatabase(),
            'database_info' => [
                'database_name' => $site->database_name,
                'database_user' => $site->database_user,
                'database_host' => $site->database_host,
                'database_port' => $site->database_port,
                'database_password' => $site->database_password,
                'site_ssh_password' => $site->site_ssh_password
            ]
        ]);
    }

    public function wpConfig(Server $server, Site $site)
    {
        $this->authorize('wpConfig', $site);
        return Inertia::render('Site/WpConfig', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'title' => $site->name,
            'can_edit_wpconfig' => user()->belongsToTeam(team: $server->team,allowSupport: false)
        ]);
    }

    public function log(Server $server, Site $site)
    {
        $this->authorize('logs', $site);

        $default_log_file = $server->stack->isNginx() ? 'nginx' : 'lsws';

        if ($site->isWordpress()) {
            $default_log_file = 'wp-debug-log';
        }

        if ($site->isLaravel()) {
            $default_log_file = 'laravel-log';
        }

        return Inertia::render('Site/Log', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'title' => $site->name . ' - Log',
            'default_log_file' => $default_log_file,
            'can_clear_logs' => user()->can('clearLogs', $site),
        ]);
    }

    public function events(Server $server, Site $site)
    {
        $this->authorize('events', $site);
        return Inertia::render('Site/Events', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'taskEvents' => $site
                ->tasks()
                ->with(['server:id,name', 'site:id,name', 'initiatedBy:id,name,email', 'team'])
                ->latest()
                ->paginate(10)
                ->through(fn($task) => TaskResource::make($task)),
            'is_site_admin' => user()->hasTeamRole(team(), Team::SITE_ADMIN)
        ]);
    }

    public function commandRunner(Server $server, Site $site)
    {
        $this->authorize('customCommandRunner', $site);
        $commands = $site
            ->tasks()
            ->where('type', TaskType::CUSTOM->value)
            ->latest()
            ->paginate(10)
            ->through(fn($command) => CustomCommandResource::make($command));

        return Inertia::render('Site/CustomCommand', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'commands' => $commands,
            'is_site_admin' => user()->hasTeamRole(team(), Team::SITE_ADMIN)
        ]);
    }

    /**
     * @throws \Throwable
     */
    public function application(Server $server, Site $site)
    {
        abort_unless(user()->can('settings', $site), 403);
        abort_unless($site->isLaravel(), 403, 'This page is only available for Laravel sites.');

        return Inertia::render('Site/Application', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'horizonProcessStatus' => $site->supervisorProcesses()->where('command', 'like', '%artisan horizon%')->first()?->status
        ]);
    }

    public function settings(Server $server, Site $site)
    {
        abort_unless(user()->can('settings', $site) || user()->can('delete', $site), 403);
        $tags = Tag::associatedTags('sites');
        $enable_wp_cron = $site->getMeta('enable_wp_cron', Site::DEFAULT_WP_CRON);
        $wp_cron_interval = $site->getMeta('wp_cron_interval', Site::DEFAULT_CRON_INTERVAL);
        $disable_site_cron = $site->getMeta('disable_site_cron', false);
        $disable_html = view('scripts.site.error.503_error')->render();
        return Inertia::render('Site/Settings', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'tags' => $tags,
            'show_php_settings' => !$site->isNodeApp(),
            'show_node_settings' => $site->isNodeApp(),
            'php_version' => $site->php_version ?: PhpVersion::DEFAULT,
            'availablePhpVersions' => PhpVersion::getVersions($server),
            'defaultEnvVars' => PhpVersion::getDefaultEnvVars(),
            'enable_php_execution_on_upload_directory' => $site->enablePhpExecutionOnUploadDirectory(),
            'enable_xml_rpc' => $site->enableXmlRpc(),
            'enable_wp_cron' => (bool) $enable_wp_cron,
            'wp_cron_interval' => $wp_cron_interval,
            'wp_cron_intervals' => Site::WP_CRON_INTERVALS,
            'modify_x_frame_options' => $site->allowedToModifyXFrameOptions(),
            'x_frame_options' => $site->getMeta('x_frame_options', ''),
            'disable_nginx_config_regeneration' => $site->disableNginxAutoRegeneration(),
            'conf_file_name' => $site->manager()->getNginxConfFileName(),
            'can_access_server' => user()->can('view', $server),
            'has_basic_auth' => $site->getMeta('has_basic_auth') ?: false,
            'username' => $site->getMeta('basic_auth_username') ?: '',
            'enable_7g_firewall' => $site->getMeta('enable_7g_firewall', false),
            'enable_8g_firewall' => $site->getMeta('enable_8g_firewall', false),
            'disable_html' => $disable_html,
            'disable_site_cron' => $disable_site_cron,
            'is_playground'=> $server->team->isPlayGround(),
            'is_admin_user' => user()->isAdmin(),
            'can_perform_support_level_action' => $server->team->allowSupportOneAction(user: user()),
            'search_engine_visibility' => $site->getMeta('disable_search_engine_visibility', false),
        ]);
    }

    public function backup(Server $server, Site $site)
    {
        $this->authorize('settings', $site);
        if($server->team->isFreePlan()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $storage_providers = StorageProvider::whereTeamId($server->team_id)->select('provider', 'bucket', 'id')->get()->toArray();
        $backup_settings = BackupSetting::where(["site_id" => $site->id])->get()->toArray();
        $backup_types = BackupType::toSelectArray();
        #currently we support database backup for laravel sites only
        if (!$site->type->filesBackupSupported()){
            unset($backup_types[BackupType::INCREMENTAL->value]);
        }
        return Inertia::render('Site/BackupSettings', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'storage_providers' => $storage_providers,
            'backup_settings' => $backup_settings,
            'backup_types' => $backup_types,
            'files_backup_supported' => $site->type->filesBackupSupported(),
        ]);
    }

    public function backups(Server $server, Site $site)
    {
        $this->authorize('settings', $site);
        if($server->team->isFreePlan()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $backup_settings = BackupSetting::with(['storageProvider', 'backupFiles'])
            ->selectRaw('*,DATE_FORMAT(backup_settings.last_backup_at, "%d %b %Y %h:%i %p") as last_backup_at_format')
            ->where(["site_id" => $site->id])->get();
        $remote_backup_settings = $backup_settings->where('is_local', false)->first();
        $local_backup_settings = $backup_settings->where('is_local', true)->first();
        if (!$local_backup_settings) {
            $local_backup_settings = BackupSetting::create([
                'site_id' => $site->id,
                'is_local' => true,
                'database' => true,
                'files' => true,
                'auto_backup' => false,
                'auto_delete' => false,
                'user_id' => user()->id,
                'status' => BackupSetting::PENDING,
            ]);
        }
        $remote_backup_files = $site->backupFiles()
            ->selectRaw('*,DATE_FORMAT(backup_files.server_datetime, "%d %b %Y %h:%i %p") as date_format, DATE_FORMAT(backup_files.date, "%d %b %Y %h:%i %p") as last_backup')
            ->with(['storageProvider:id,bucket', 'user:id,name'])
            ->where(['is_remote' => true])
            ->where(function($query){
                $query->where('backup_files.type','!=',BackupFile::INCREMENTAL_BACKUP)
                    ->orWhere('backup_files.status',BackupSetting::FAILED);
            })
            ->latest('backup_files.id')->paginate(20, ['*'], 'remote_page');
        $local_backup_files = $site->backupFiles()
            ->selectRaw('*,DATE_FORMAT(backup_files.server_datetime, "%d %b %Y %h:%i %p") as date_format, DATE_FORMAT(backup_files.date, "%d %b %Y %h:%i %p") as last_backup')
            ->with(['user:id,name'])
            ->where(['is_remote' => false])
            ->where(function($query){
                $query->where('backup_files.type','!=',BackupFile::INCREMENTAL_BACKUP)
                    ->orWhere('backup_files.status',BackupSetting::FAILED);
            })
            ->latest('backup_files.id')->paginate(20, ['*'], 'local_page');
        return Inertia::render('Site/Backups', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'remote_backup_files' => $remote_backup_files->groupBy('date_format'),
            'remote_backup_settings' => $remote_backup_settings,
            'local_backup_settings' => $local_backup_settings,
            'local_backup_files' => $local_backup_files->groupBy('date_format'),
            'remote_backup_files_pagination' => $remote_backup_files,
            'local_backup_files_pagination' => $local_backup_files,
        ]);
    }

    public function downloadBackup(Server $server, Site $site, BackupFile $backupFile)
    {
        $this->authorize('settings', $site);
        abort_if(!$backupFile->is_remote, 403);

        try {
            $storageProvider = $backupFile->storageProvider;
            if ($storageProvider && $storageProvider?->hasStableConnection()) {
                return redirect()->away($backupFile->download($site->backupDirName()));
            }
        } catch (\Exception $e) {
            Log::error('Backup download failed: ' . $e->getMessage());
        }
        return redirect()->back()->with('flash', [
            'message' => 'Failed to download backup file. Please try again later.',
            'type' => 'error'
        ]);
    }

    public function domain(Server $server, Site $site)
    {
        $this->authorize('manageDomain', $site);

        // If site has demo or staging environment, redirect to "go live" page
        if($site->hasDemoEnvironment() || $site->hasStagingEnvironment() || $site->hasStagingWithCustomDomainEnvironment()){
            return redirect()->route('site.staging.environment', ['server' => $server, 'site' => $site]);
        }

        if ($site->additional_domains === '[]' || empty($site->additional_domains)) {
            $site->additional_domains = [];
        }

        return Inertia::render('Site/Domain', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'sslProvider' => $site->ssl_provider,
            'additional_domains' => $site->additional_domains,
            'cloudflare_integration' => Arr::get($site->meta, 'cloudflare_integration.domain_active_on_cloudflare') ?? false,
            'hasCloudflareIntegration' => team()->hasCloudflareIntegration(),
            'multisite_subdomain_enabled' => $site->isMultiSite() && $site->isMultiSiteSubdomain()
        ]);
    }

    public function emailProvider(Server $server, Site $site)
    {
        $this->authorize('emailProvider', $site);

        $email_providers = team()->emailProviders()
            ->when(isWhiteLabel(), function ($q) {
                $q->where(function ($query) {
                    $query->where('plan', '!=', xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS)
                        ->orWhereNull('plan');
                });
            })
            ->get();

        return Inertia::render('Site/EmailProvider', [
            'server' => ServerResource::make($server),
            'email_provider_data' => $site->email_provider_data,
            'site' => SiteResource::make($site),
            'title' => $site->name . ' - Email Provider',
            'selected_provider' => $site->emailProvider ?? null,
            'email_providers' => $email_providers,
            'subAccountEmail' => team()->getElasticEmailSubAccountEmail(),
        ]);
    }

    public function redirection(Server $server, Site $site)
    {
        $this->authorize('redirect', $site);

        return Inertia::render('Site/Redirection', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'title' => $site->name . ' - Redirection',
            'redirection_types' => Redirection::ALLOWED_REDIRECT_LABELS,
            'redirections' => $site->redirections()->paginate(),
            'siteRedirectManage'=> user()->can('redirectManage', $site)
        ]);
    }

    public function monitoring(Server $server, Site $site)
    {
        $this->authorize('monitors', $site);
        if($server->team->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        return Inertia::render('Site/Monitoring', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'monitoringData' => (new SiteRepository($site))->getMonitoringData()
        ]);
    }

    public function stagingEnvironment(Server $server, Site $site)
    {
        $this->authorize('view', $site);

        if($site->hasProductionEnvironment()){
            // redirect to domain update page
            return redirect()->route('site.domain', ['server' => $server, 'site' => $site]);
        }

        return Inertia::render('Site/StagingEnvironment', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'cloudflare_integration' => Arr::get($site->meta, 'cloudflare_integration.domain_active_on_cloudflare') ?? false,
            'hasCloudflareIntegration' => team()->hasCloudflareIntegration(),
            'multisite_subdomain_enabled' => $site->isMultiSite() && $site->isMultiSiteSubdomain()
        ]);
    }

    public function stagingManagement(Server $server, Site $site)
    {
        $this->authorize('manageStaging', $site);

        if($site->hasProductionEnvironment()){
            // redirect to domain update page
            return redirect()->route('site.domain', ['server' => $server, 'site' => $site]);
        }

        $latestDeploymentLog = DeploymentLog::query()
            ->whereSourceSiteId($site->id)
            ->orderBy('id', 'DESC')
            ->limit(1)
            ->first();

        $fetchPullDeploymentLogs = DeploymentLog::query()
            ->where('destination_site_id', $site->id)
            ->with(['sourceSite:id,name,ssl_provider', 'destinationSite:id,name,ssl_provider'])
            ->orderBy('id', 'DESC')
            ->get();

        $fetchPushDeploymentLogs = DeploymentLog::query()
            ->where('source_site_id', $site->id)
            ->with(['sourceSite:id,name,ssl_provider', 'destinationSite:id,name,ssl_provider'])
            ->orderBy('id', 'DESC')
            ->get();

        // merge pull and push deployment logs
        $deploymentLogs = array_values(collect($fetchPullDeploymentLogs->merge($fetchPushDeploymentLogs))
            ->sortBy('id', SORT_REGULAR, true)
            ->take(5)
            ->map(function($log) {
            return [
                'id' => $log->id,
                'source_site_id' => $log->sourceSite->id,
                'source_site' => $log->sourceSite->name,
                'source_site_url' => $log->sourceSite->ssl_provider ? 'https://' . $log->sourceSite->name : 'http://' . $log->sourceSite->name,
                'destination_site_id' => $log->destinationSite->id,
                'destination_site' => $log->destinationSite->name,
                'destination_site_url' => $log->destinationSite->ssl_provider ? 'https://' . $log->destinationSite->name : 'http://' . $log->destinationSite->name,
                'status' => $log->status,
                'action' => $log->action,
                'initiated_by' => $log->initiatedBy->name,
                'created_at' => $log->created_at->diffForHumans(),
                'updated_at' => $log->updated_at->diffForHumans(),
            ];
        })->toArray());

        return Inertia::render('Site/StagingManagement', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'productionSite' => $site->productionSite,
            'is_playground'=> $server->team->isPlayGround(),
            'latestDeploymentLog' => $latestDeploymentLog,
            'logs' => $deploymentLogs
        ]);
    }

    public function viewSite(Server $server, $siteName)
    {
        $this->authorize('view', $server);
        $site = $server->sites()->where('name', $siteName)->firstOrFail();

        return redirect()->route('site.overview', ['server' => $server, 'site' => $site]);
    }

    public function database(Server $server,  Site $site)
    {
        $this->authorize('database', $site);
        $enable_adminer = $site->getMeta('enable_adminer', false);
        $adminerInterval = team()->getSettings('feature_customization->disable_adminer');
        $disabledAdminerTime = $adminerInterval ? $server->team->disableTime($site->getMeta('adminer_enable_time'), 'adminer_interval'): null;

        if ($disabledAdminerTime && ($disabledAdminerTime->gt(now()) || $disabledAdminerTime->diffInHours(now()) > 1)) {
            $disabledAdminerTime = $disabledAdminerTime->addHour()->diffForHumans();
        } elseif ($disabledAdminerTime) {
            $disabledAdminerTime = '1 hour';
        }

        // Check if phpMyAdmin is enabled on the server
        $phpMyAdminSite = $server->sites()->where('type', SiteType::PHPMYADMIN)->first();

        if ($phpMyAdminSite){
            $phpMyAdminSite = SiteResource::make($phpMyAdminSite);
        }

        return Inertia::render('Site/Adminer', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'enable_adminer' => (bool) $enable_adminer && !$site->isNodeApp(),
            'is_adminer_available' => !$site->isNodeApp(),
            'enable_7g_firewall' => $site->getMeta('enable_7g_firewall', false),
            'db_name' => $site->database_name,
            'db_user' => $site->database_user,
            'db_password' => $site->database_password,
            'disabled_adminer_time' => $disabledAdminerTime,
            'phpMyAdminSite' => $phpMyAdminSite,
            'hasDatabase' => $site->hasDatabase(),
            'isNodeApp' => $site->isNodeApp(),
        ]);
    }

    public function getWpDebug(Server $server, Site $site)
    {
        $this->authorize('settings', $server);

        return response()->json([
            'enable_wp_debug' => $site->isWpDebugEnabled()
        ]);
    }
    public function fileManager(Server $server,  Site $site)
    {
        $this->authorize('fileManager', $site);

        // Redirect Node.js applications with a message
        if ($site->isNodeApp()) {
            return redirect()->route('site.overview', [$server, $site])->with('flash', [
                'type' => 'error',
                'message' => 'File Manager is not compatible with Node.js applications'
            ]);
        }

        $fileManagerInterval = team()->getSettings('feature_customization->disable_file_manager');

        $disabledFileManagerTime = $fileManagerInterval
            ? $server->team->disableTime($site->getMeta('file_manager_enable_time'), 'file_manager_interval')
            : null;

        if ($disabledFileManagerTime && ($disabledFileManagerTime->gt(now()) || $disabledFileManagerTime->diffInHours(now()) > 1)) {
            $disabledFileManagerTime = $disabledFileManagerTime->addHour()->diffForHumans();
        } elseif ($disabledFileManagerTime) {
            $disabledFileManagerTime = '1 hour';
        }

        return Inertia::render('Site/FileManager', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'enable_file_manager' => (bool) $site->getMeta('file_manager', false),
            'disabled_file_manager_time' => $disabledFileManagerTime,
        ]);
    }

    public function nginx(Server $server,  Site $site)
    {
        $this->authorize('customNginx', $site);

        abort_unless($server->stack->isNginx(), 403, 'This is only available for Nginx servers.');

        return Inertia::render('Site/Nginx', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'types' => CustomNginxEnum::getTypes(),
            'templates' => CustomNginxEnum::getTemplates($site),
            'customNginxConfigs' => $site->customNginxConfigs()
                ->selectRaw('custom_nginxes.*, date_format(custom_nginxes.updated_at, "%d %b %Y") as updated')
                ->orderBy('id', 'DESC')->get(),
        ]);
    }

    public function webServerSecurity(Server $server,  Site $site)
    {
        abort_unless(user()->can('settings', $site) || user()->can('delete', $site), 403);

        return Inertia::render('Site/NginxSecurity', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'enable_php_execution_on_upload_directory' => $site->enablePhpExecutionOnUploadDirectory(),
            'enable_xml_rpc' => $site->enableXmlRpc(),
            'modify_x_frame_options' => $site->allowedToModifyXFrameOptions(),
            'x_frame_options' => $site->getMeta('x_frame_options', ''),
            'disable_nginx_config_regeneration' => $site->disableNginxAutoRegeneration(),
            'enable_7g_firewall' => $site->getMeta('enable_7g_firewall', false),
            'enable_8g_firewall' => $site->getMeta('enable_8g_firewall', false),
            'enable_ai_bot_blocker' => $site->getMeta('enable_ai_bot_blocker', false),
            'enable_custom_robots_txt' => $site->getMeta('enable_custom_robots_txt', false),
            'wp_fail2ban' => $site->getMeta('wp_fail2ban', [
                "enable" => false,
                "log_failed_logins" => true,
                "block_common_usernames" => true,
                "block_user_enumeration" => true,
                "protect_comments" => true,
                "block_spam" => true,
                "guard_password_resets" => true,
                "guard_pingbacks" => true,
            ]),
            'can_edit_security_settings' => user()->can('editSiteSecuritySettings', $site),
        ]);
    }

    public function basicAuthentication(Server $server,  Site $site)
    {
        abort_unless(user()->can('settings', $site) || user()->can('delete', $site), 403);

        return Inertia::render('Site/BasicAuthentication', [
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'can_access_server' => user()->can('view', $server),
            'has_basic_auth' => $site->getMeta('has_basic_auth') ?: false,
            'username' => $site->getMeta('basic_auth_username') ?: '',
            'can_edit_basic_auth' => $server->team->allowSupportOneAction(user: user()),
        ]);
    }

    public function ipManagement(Server $server,  Site $site)
    {
        abort_unless(user()->can('settings', $site) || user()->can('delete', $site), 403);

        $ipList = $site->ipAddresses()->paginate(10);
        $types = IPAddressType::asValueLabel();
        return Inertia::render('Site/IPList', [
            'title' => $site->name . ' - IP Management',
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'ipList' => $ipList,
            'types' => $types,
            'create_or_edit_ip'=> $server->team->allowSupportOneAction(user: user())
        ]);
    }
    public function integrityMonitor(Server $server,  Site $site)
    {
        $this->authorize('monitors', $site);
        abort_unless($site->isWordpress(), 404);
        $integrityChecksum = $site->integrityChecksum;
        return Inertia::render('Site/Integrity/IntegrityMonitor', [
            'title' => $site->name.' - Integrity Monitor',
            'server' => ServerResource::make($server),
            'site' => SiteResource::make($site),
            'integrityChecksum' => $integrityChecksum,
            'lastChecked' => $integrityChecksum?->updated_at->diffForHumans(),
        ]);
    }

}
