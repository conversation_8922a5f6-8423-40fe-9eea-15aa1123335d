<?php

namespace App\Http\Controllers;

use App\Enums\Integration\PluginIntegrationType;
use App\Models\PluginIntegration;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PluginIntegrationController extends Controller
{
    public function integrationPlugins()
    {
        $this->authorize('manageCache',team());
        $plugins = PluginIntegration::where('team_id', team()->id)->paginate(10);
        $pluginTypes = PluginIntegrationType::toSelectArray();
        return Inertia::render('Integration/Plugins', [
            'title' => 'Item Integrations',
            'plugins' => $plugins,
            'pluginTypes' => $pluginTypes,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
        ]);
    }

    public function storeIntegrationPlugins(Request $request)
    {
        $this->authorize('manageCache',team());
        $request->validate([
            'type' => 'required|string|in:' . implode(',', PluginIntegrationType::toArray()),
            'name' => 'required|string',
            'license' =>['required','string',
                function ($attribute, $value, $fail) {
                    $headers = @get_headers("https://objectcache.pro/plugin/object-cache-pro.zip?token=$value");
                    if (!str_contains($headers[0], '200')) {
                        $fail('The '.$attribute.' is invalid.');
                    }
                },
            ],
        ]);

        PluginIntegration::create([
            'team_id' => team()->id,
            'type' => $request->type,
            'name' => $request->name,
            'config' => json_encode(['license' => $request->license]),
        ]);

        return redirect()->route('integration.plugins')->with('flash', [
            'message' => 'Plugin integration created successfully',
        ]);
    }

    public function updateIntegrationPlugins(Request $request,PluginIntegration $pluginIntegration)
    {
        $this->authorize('manageCache',team());
        $request->validate([
            'type' => 'required|string|in:' . implode(',', PluginIntegrationType::toArray()),
            'name' => 'required|string',
            'license' =>['required','string',
                function ($attribute, $value, $fail) {
                    $headers = @get_headers("https://objectcache.pro/plugin/object-cache-pro.zip?token=$value");
                    if (!str_contains($headers[0], '200')) {
                        $fail('The '.$attribute.' is invalid.');
                    }
                },
            ],
        ]);

        $pluginIntegration->update([
            'type' => $request->type,
            'name' => $request->name,
            'config' => json_encode(['license' => $request->license]),
        ]);
        return redirect()->route('integration.plugins')->with('flash', [
            'message' => 'Plugin integration updated successfully',
        ]);
    }

    public function searchIntegrationPlugins(Request $request)
    {
        $this->authorize('manageCache',team());
        $plugins = PluginIntegration::where('team_id', team()->id)
            ->when($request->filled('query'), fn ($query, $search) => $query->where('name', 'like', "%$search%"))
            ->select(['id','name'])
            ->take(10)
            ->get()
            ->toArray();
        return response()->json($plugins);
    }
    public function destroy(PluginIntegration $pluginIntegration)
    {
        $this->authorize('manageCache',team());
        $pluginIntegration->delete();
        return redirect()->route('integration.plugins')->with('flash', [
            'message' => 'Plugin integration deleted successfully',
        ]);
    }
}
