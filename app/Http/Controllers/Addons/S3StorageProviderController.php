<?php

namespace App\Http\Controllers\Addons;

use App\Addons\AddonsManager;
use App\Addons\Enum\AddonTypeEnum;
use App\Addons\Enum\StorageProvider\RegionEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Addons\StorageProviderStorageRequest;
use App\Http\Resources\AddonStorageProviderResource;
use App\Models\AddonStorageProvider;
use App\Models\BackblazeMember;
use App\Models\BackupFile;
use App\Models\Product;
use App\Models\StorageProvider;
use App\Services\StorageProvider\BackBlazeService;
use Aws\S3\S3Client;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class S3StorageProviderController extends Controller
{
    public function index()
    {
        $this->authorize('viewAny', AddonStorageProvider::class);
        $regions = RegionEnum::toSelectArray();
        $products = Product::active()->s3StorageProvider()->pluck('title', 'id')->toArray();

        // Get storage providers with their products and transform using resource
        $storageProviders = team()->addonStorageProviders()
            //with report latest of many
            ->with(['product:id,title,price,unit','backblazeReport'])
            ->paginate()
            ->through(fn ($storageProvider) =>
                AddonStorageProviderResource::make($storageProvider)
            );
        return inertia('Addons/StorageProvider', [
            'title' => 'xCloud Storage Provider',
            'regions' => $regions,
            'products' => $products,
            'storageProviders' => $storageProviders,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
        ]);
    }

    /**
     * Store a new storage provider
     *
     * @param StorageProviderStorageRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StorageProviderStorageRequest $request)
    {
        $this->authorize('create',  AddonStorageProvider::class);

        try {
            if(app()->isProduction()){
                $memberEmail = sprintf('<EMAIL>', team()->id, $request->region);
            }else{
                $memberEmail = sprintf('%<EMAIL>', config('app.env'), team()->id, $request->region);
            }
            $addonManager = app(AddonsManager::class)->resolve(AddonTypeEnum::STORAGE_PROVIDER);

            $member = team()->backblazeMembers()->where('account_email', $memberEmail)->first();
            if (is_null($member)){
                $response = app(BackBlazeService::class)->addGroupMember($memberEmail,$request->region);

                $member = BackblazeMember::create([
                    'team_id' => team()->id,
                    'account_id' => Arr::get($response, 'groupMember.accountId'),
                    'account_email' => $memberEmail,
                    'group_id' => Arr::get($response, 'groupMember.groupId'),
                    'group_name' => Arr::get($response, 'groupMember.groupName'),
                    'application_key_id' => $response['applicationKeyId'],
                    'application_key' => $response['applicationKey'],
                    'region' =>  Arr::get($response, 'groupMember.region'),
                    's3_endpoint' => Arr::get($response, 'groupMember.s3Endpoint'),
                    'cluster_num' => Arr::get($response, 'groupMember.clusterNum'),
                ]);
            }


            // Get validated data
            $validatedData = $request->validated();

            // Resolve the storage provider addon service
             $validatedData['backblaze_member_id'] = $member->id;

            // Create the storage provider with validated data
            $addon = $addonManager->create($validatedData);

            // Handle payment for the storage provider
            $this->processPayment($addonManager, $addon);

            // Create a team storage provider
            $this->createTeamStorageProvider($addon);

            // Return success response
            return redirect()->back()->with('flash', [
                'message' => 'Storage Provider Created Successfully.',
                'type' => 'success'
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Process payment for the storage provider
     *
     * @param mixed $addonManager The addon manager instance
     * @param AddonStorageProvider $addon The created addon storage provider
     * @return array Payment response
     * @throws \Exception If payment fails
     */
    private function processPayment($addonManager, AddonStorageProvider $addon): array
    {
        $response = $addonManager->handlePayment([
            'model_id' => $addon->id,
            'team_id' => team()->id,
        ]);
        // Check if payment was successful
        if (!isset($response['is_paid']) || !$response['is_paid']) {
            throw new \Exception('Payment failed for storage provider');
        }

        // Enable the storage provider
        $addon->update(['is_enabled' => true]);
        $addon->refresh();

        return $response;
    }

    /**
     * Create a team storage provider linked to the addon storage provider
     *
     * @param AddonStorageProvider $addon The addon storage provider
     * @return \App\Models\StorageProvider The created storage provider
     * @throws \Exception If key generation fails
     */
    private function createTeamStorageProvider(AddonStorageProvider $addon): \App\Models\StorageProvider
    {
        try {
            // Generate credentials key
            $credentials = $addon->generateKey();

            // Create the storage provider
            return $addon->team->storageProviders()->create([
                'provider' => StorageProvider::xCloud_S3_Provider,
                'bucket' => $addon->bucket_name,
                'addon_storage_provider_id' => $addon->id,
                'credentials' => $credentials,
                'user_id' => user()->id,
                'region' => $addon->region
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create team storage provider', [
                'addon_id' => $addon->id,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to create team storage provider: ' . $e->getMessage());
        }
    }

    /**
     * Handle exceptions during storage provider creation
     *
     * @param \Exception $e The exception
     * @return \Illuminate\Http\RedirectResponse
     */
    private function handleException(\Exception $e): \Illuminate\Http\RedirectResponse
    {
        // Log the error for debugging
        Log::error('Failed to create storage provider', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'user_id' => user()->id,
            'team_id' => team()->id
        ]);

        // Return error response
        return redirect()->back()->with('flash', [
            'message' => 'Failed to create storage provider: ' . ($e->getMessage() ?: 'Unknown error'),
            'type' => 'error'
        ]);
    }

    /**
     * Delete the addon storage provider and its associated team storage provider.
     *
     * @param AddonStorageProvider $addonStorageProvider
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(AddonStorageProvider $addonStorageProvider)
    {
        $this->authorize('delete', $addonStorageProvider);
        try {
            return DB::transaction(function () use ($addonStorageProvider) {
                // Find the associated team storage provider
                $storageProvider = $addonStorageProvider->storageProvider;

                // Check if there are any backup settings using this storage provider
                if ($storageProvider && ($storageProvider->backupSettings()->exists() || $storageProvider->backupFiles()->exists())) {
                    throw new \Exception('It is being used by backup settings.');
                }


                // Delete Backblaze resources
                $this->deleteBackblazeResources($addonStorageProvider);

                // Delete the team storage provider if it exists
                if ($storageProvider) {
                    // Delete any backup files associated with this storage provider
                    $storageProvider->backupFiles()->delete();

                    // Delete the storage provider
                    $storageProvider->delete();
                }

                // Delete the addon storage provider
                $addonStorageProvider->delete();

                // Return success response
                return redirect()->back()->with('flash', [
                    'message' => 'Storage Provider deleted successfully.',
                    'type' => 'success'
                ]);
            });
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Failed to delete storage provider', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'team' => team()?->id
            ]);
            $message = extractJsonFromString(string: $e->getMessage(), key: 'message',default: "Failed to delete storage provider.");
            return redirect()->back()->withErrors([
                'error' => $message
            ]);
        }
    }

    /**
     * Display the file manager for a specific storage provider
     *
     * @param AddonStorageProvider $addonStorageProvider
     * @return \Inertia\Response
     */
    public function fileManager(AddonStorageProvider $addonStorageProvider)
    {
        $this->authorize('view', $addonStorageProvider);

        return inertia('Addons/S3FileManager', [
            'title' => 'S3 File Manager: ' . $addonStorageProvider->bucket_name,
            'storageProvider' => $addonStorageProvider,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
        ]);
    }

    /**
     * List files in a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return JsonResponse
     */
    public function listFiles(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'prefix' => 'nullable|string',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $prefix = $request->prefix ?? '';
            $storageProvider = $addonStorageProvider->storageProvider;

            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }
            $s3Client = $storageProvider->s3Client();

            $prefix = rtrim($prefix, '/');
            if ($prefix && !str_ends_with($prefix, '/')) {
                $prefix .= '/';
            }
            $params = [
                'Bucket' => $storageProvider->getBucket(),
                'Delimiter' => '/',
                'Prefix' => $prefix,
            ];
            $result = $s3Client->listObjectsV2($params);

            $files = [];

            // Process folders (CommonPrefixes)
            if (isset($result['CommonPrefixes'])) {
                foreach ($result['CommonPrefixes'] as $commonPrefix) {
                    $files[] = [
                        'key' => $commonPrefix['Prefix'],
                        'type' => 'folder',
                        'size' => 0,
                    ];
                }
            }

            // Process files (Contents)
            if (isset($result['Contents'])) {
                foreach ($result['Contents'] as $object) {
                    // Skip the directory itself
                    if ($object['Key'] === $prefix) {
                        continue;
                    }

                    // Skip objects that end with / (folders)
                    if (str_ends_with($object['Key'], '/')) {
                        continue;
                    }

                    $files[] = [
                        'key' => $object['Key'],
                        'type' => 'file',
                        'size' => $object['Size'],
                        'lastModified' => $object['LastModified']->format('c'),
                        'etag' => trim($object['ETag'], '"'),
                        'storageClass' => $object['StorageClass'] ?? null,
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'files' => $files,
            ]);
        } catch (\Exception $e) {
            Log::error('Error listing files', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to list files: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload a file to a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return JsonResponse
     */
    public function uploadFile(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'file' => 'required|file',
                'path' => 'nullable|string',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $storageProvider = $addonStorageProvider->storageProvider;
            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }

            $file = $request->file('file');
            $path = $request->path ?? '';
            $path = rtrim($path, '/');
            if ($path && !str_ends_with($path, '/')) {
                $path .= '/';
            }

            $key = $path . $file->getClientOriginalName();
            $s3Client = $storageProvider->s3Client();

            $s3Client->putObject([
                'Bucket' => $storageProvider->getBucket(),
                'Key' => $key,
                'Body' => fopen($file->getRealPath(), 'r'),
                'ContentType' => $file->getMimeType(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'key' => $key,
            ]);
        } catch (\Exception $e) {
            Log::error('Error uploading file', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a folder in a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return JsonResponse
     */
    public function createFolder(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'key' => 'required|string',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $storageProvider = $addonStorageProvider->storageProvider;
            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }

            $key = $request->key;
            // Ensure the key ends with a slash
            if (!str_ends_with($key, '/')) {
                $key .= '/';
            }

            $s3Client = $storageProvider->s3Client();

            $s3Client->putObject([
                'Bucket' => $storageProvider->getBucket(),
                'Key' => $key,
                'Body' => '',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Folder created successfully',
                'key' => $key,
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating folder', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to create folder: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download a file from a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return StreamedResponse|JsonResponse
     */
    public function downloadFileByStream(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'key' => 'required|string',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $storageProvider = $addonStorageProvider->storageProvider;
            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }

            $key = $request->key;
            $s3Client = $storageProvider->s3Client();

            $result = $s3Client->getObject([
                'Bucket' => $storageProvider->getBucket(),
                'Key' => $key,
            ]);

            $filename = basename($key);
            $contentType = $result['ContentType'] ?? 'application/octet-stream';

            return response()->streamDownload(function () use ($result) {
                echo $result['Body'];
            }, $filename, [
                'Content-Type' => $contentType,
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);
        } catch (\Exception $e) {
            Log::error('Error downloading file', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to download file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download a file from a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return StreamedResponse|JsonResponse
     */
    public function downloadFile(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'key' => 'required|string',
            ]);

            $this->authorize('view', $addonStorageProvider);
            $storageProvider = $addonStorageProvider->storageProvider;

            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }
            $key = $request->key;
            $filename = basename($key);

            $url = StorageProvider::storage($storageProvider->getConfig())->temporaryUrl(
                path: $key,
                expiration: now()->addHours(BackupFile::TEMPORARY_URL_EXPIRATION),
                options: [
                    'ResponseContentDisposition' => 'attachment; filename="'.$filename.'"',
                ]
            );

            return response()->json([
                'success' => true,
                'url' => $url,
            ]);
        } catch (\Exception $e) {
            Log::error('Error downloading file', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to download file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Rename a file or folder in a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return JsonResponse
     */
    public function renameFile(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'old_key' => 'required|string',
                'new_key' => 'required|string',
                'is_folder' => 'required|boolean',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $storageProvider = $addonStorageProvider->storageProvider;
            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }

            $oldKey = $request->old_key;
            $newKey = $request->new_key;
            $isFolder = $request->is_folder;
            $s3Client = $storageProvider->s3Client();

            if ($isFolder) {
                // For folders, we need to copy all objects with the prefix
                $params = [
                    'Bucket' => $storageProvider->getBucket(),
                    'Prefix' => $oldKey,
                ];

                $result = $s3Client->listObjectsV2($params);

                if (isset($result['Contents'])) {
                    foreach ($result['Contents'] as $object) {
                        $sourceKey = $object['Key'];
                        $targetKey = str_replace($oldKey, $newKey, $sourceKey);

                        // Copy the object to the new key
                        $s3Client->copyObject([
                            'Bucket' => $storageProvider->getBucket(),
                            'CopySource' => $storageProvider->getBucket() . '/' . $sourceKey,
                            'Key' => $targetKey,
                        ]);

                        // Delete the old object
                        $s3Client->deleteObject([
                            'Bucket' => $storageProvider->getBucket(),
                            'Key' => $sourceKey,
                        ]);
                    }
                }
            } else {
                // For files, just copy and delete
                $s3Client->copyObject([
                    'Bucket' => $storageProvider->getBucket(),
                    'CopySource' => $storageProvider->getBucket() . '/' . $oldKey,
                    'Key' => $newKey,
                ]);

                $s3Client->deleteObject([
                    'Bucket' => $storageProvider->getBucket(),
                    'Key' => $oldKey,
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'File renamed successfully',
                'old_key' => $oldKey,
                'new_key' => $newKey,
            ]);
        } catch (\Exception $e) {
            Log::error('Error renaming file', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to rename file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a file or folder from a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return JsonResponse
     */
    public function deleteFile(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'key' => 'required|string',
                'is_folder' => 'required|boolean',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $storageProvider = $addonStorageProvider->storageProvider;
            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }

            $key = $request->key;
            $isFolder = $request->is_folder;
            $s3Client = $storageProvider->s3Client();

            if ($isFolder) {
                // For folders, we need to delete all objects with the prefix
                $params = [
                    'Bucket' => $storageProvider->getBucket(),
                    'Prefix' => $key,
                ];

                $result = $s3Client->listObjectsV2($params);

                if (isset($result['Contents']) && count($result['Contents']) > 0) {
                    $objects = [];
                    foreach ($result['Contents'] as $object) {
                        $objects[] = ['Key' => $object['Key']];
                    }

                    $s3Client->deleteObjects([
                        'Bucket' => $storageProvider->getBucket(),
                        'Delete' => [
                            'Objects' => $objects,
                        ],
                    ]);
                }
            } else {
                // For files, just delete the object
                $s3Client->deleteObject([
                    'Bucket' => $storageProvider->getBucket(),
                    'Key' => $key,
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully',
                'key' => $key,
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting file', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get the content of a file from a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return JsonResponse
     */
    public function getFileContent(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'key' => 'required|string',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $storageProvider = $addonStorageProvider->storageProvider;
            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }

            $key = $request->key;
            $s3Client = $storageProvider->s3Client();

            $result = $s3Client->getObject([
                'Bucket' => $storageProvider->getBucket(),
                'Key' => $key,
            ]);

            $content = (string) $result['Body'];

            return response()->json([
                'success' => true,
                'content' => $content,
                'key' => $key,
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting file content', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to get file content: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the content of a file in a bucket
     *
     * @param Request $request
     * @param AddonStorageProvider $addonStorageProvider
     * @return JsonResponse
     */
    public function updateFileContent(Request $request, AddonStorageProvider $addonStorageProvider)
    {
        try {
            $request->validate([
                'key' => 'required|string',
                'content' => 'required|string',
            ]);

            $this->authorize('view', $addonStorageProvider);

            $storageProvider = $addonStorageProvider->storageProvider;
            if (!$storageProvider) {
                return response()->json([
                    'success' => false,
                    'message' => 'Storage provider not found'
                ], 404);
            }

            $key = $request->key;
            $content = $request->input('content');
            $s3Client = $storageProvider->s3Client();

            // Get the content type of the existing file
            $headResult = $s3Client->headObject([
                'Bucket' => $storageProvider->getBucket(),
                'Key' => $key,
            ]);

            $contentType = $headResult['ContentType'] ?? 'text/plain';

            $s3Client->putObject([
                'Bucket' => $storageProvider->getBucket(),
                'Key' => $key,
                'Body' => $content,
                'ContentType' => $contentType,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File content updated successfully',
                'key' => $key,
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating file content', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update file content: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete Backblaze resources (bucket and key)
     *
     * @param AddonStorageProvider $addonStorageProvider
     * @throws \Exception
     */
    private function deleteBackblazeResources(AddonStorageProvider $addonStorageProvider): void
    {
        try {
            $service = app(BackBlazeService::class)
                ->setApplicationUser(keyId: $addonStorageProvider->backblazeMember->application_key_id,applicationKey:  $addonStorageProvider->backblazeMember->application_key);
            // Delete bucket
            $response = $service->deleteBucket(bucketId: $addonStorageProvider->bucket_id);
            if (!$response->successful()) {
                throw new \Exception('Failed to delete bucket from S3: ' . json_encode($response->json()));
            }

            // Get application key ID safely
            $applicationKeyId = $addonStorageProvider->getCredentialKey('applicationKeyId');

            // Delete key if we have a valid application key ID
            if ($applicationKeyId) {
                $service->deleteKey(applicationKeyId: $applicationKeyId);
            }
        } catch (\Exception $e) {
            Log::error('Error deleting Backblaze resources', [
                'error' => $e->getMessage(),
                'addon_storage_provider_id' => $addonStorageProvider->id
            ]);
            throw $e;
        }
    }
}
