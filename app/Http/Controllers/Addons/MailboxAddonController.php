<?php

namespace App\Http\Controllers\Addons;

use App\Addons\Enum\MailboxEnums\EmailAccountStatusEnum;
use App\Addons\Enum\MailboxEnums\MailboxPlans;
use App\Http\Controllers\Controller;
use App\Http\Resources\Addons\EmailAccountResource;
use App\Http\Resources\ServerResource;

class MailboxAddonController extends Controller
{
    public function index()
    {
        ## TODO: Add policy

        $emailAccounts = team()->emailAccounts()->select([
            'id',
            'team_id',
            'mailbox_domain_id',
            'email',
            'password',
            'plan',
            'status',
            'mailbox_size',
            'forwarding_emails'
        ])
            ->with(['mailboxDomain:id,team_id,domain,domain_code,status'])
            ->paginate()
            ->transform(function ($emailAccount) {
                $emailAccount->masked_pwd = $emailAccount->masked_password;
                $emailAccount->status = EmailAccountStatusEnum::readableStatus($emailAccount->status);
                return $emailAccount;
            })->paginate(10);

        return inertia('Addons/Mailbox/MailboxAddon', [
            'emailAccounts' => $emailAccounts,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'mailboxPlans' => MailboxPlans::getReadablePlanName(),
        ]);
    }
}