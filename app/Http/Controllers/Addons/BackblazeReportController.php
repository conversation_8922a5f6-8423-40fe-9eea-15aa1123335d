<?php

namespace App\Http\Controllers\Addons;

use App\Http\Controllers\Controller;
use App\Models\AddonStorageProvider;
use App\Models\BackblazeReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BackblazeReportController extends Controller
{
    /**
     * Display the bucket usage report for a specific storage provider
     *
     * @param AddonStorageProvider $addonStorageProvider
     * @param Request $request
     * @return \Inertia\Response
     */
    public function bucketReport(AddonStorageProvider $addonStorageProvider, Request $request)
    {
        $this->authorize('view', $addonStorageProvider);

        // Get date range from request or default to last 30 days
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))
            : Carbon::now()->subDays(30);

        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))
            : Carbon::now();

        // Get reports for the date range
        $reports = $addonStorageProvider->backblazeReports()
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->orderBy('date')
            ->get();

        // Calculate summary statistics
        $totalStoredGB = $reports->last() ? $reports->last()->stored_gb : 0;
        $totalUploadedGB = $reports->sum('uploaded_gb');
        $totalDownloadedGB = $reports->sum('downloaded_gb');
        $totalApiTransactions = $reports->sum('api_txn_class_a') +
                               $reports->sum('api_txn_class_b') +
                               $reports->sum('api_txn_class_c');

        // Calculate over-usage if applicable
        $overUsageData = $addonStorageProvider->monthlyOverUsage($startDate, $endDate);

        // Handle actual report data
        if (!$reports->isEmpty()) {
            // Format dates for better display and ensure numeric values
            $reports->transform(function($report) {
                $report->formatted_date = Carbon::parse($report->date)->format('M d, Y');

                // Ensure all numeric fields are properly cast
                $report->stored_gb = (float) $report->stored_gb;
                $report->uploaded_gb = (float) $report->uploaded_gb;
                $report->downloaded_gb = (float) $report->downloaded_gb;
                $report->api_txn_class_a = (int) $report->api_txn_class_a;
                $report->api_txn_class_b = (int) $report->api_txn_class_b;
                $report->api_txn_class_c = (int) $report->api_txn_class_c;

                return $report;
            });

            // Prepare chart data with explicit casting
            $chartLabels = $reports->pluck('formatted_date')->toArray();
            $storedGBData = $reports->pluck('stored_gb')->map(function($value) {
                return round((float)$value, 2);
            })->values()->toArray();
            $uploadedGBData = $reports->pluck('uploaded_gb')->map(function($value) {
                return round((float)$value, 2);
            })->values()->toArray();
            $downloadedGBData = $reports->pluck('downloaded_gb')->map(function($value) {
                return round((float)$value, 2);
            })->values()->toArray();
            $apiTransactionsData = $reports->map(function($report) {
                return (int)$report->api_txn_class_a + (int)$report->api_txn_class_b + (int)$report->api_txn_class_c;
            })->values()->toArray();
        } else {
            // Initialize empty data for empty reports
            $chartLabels = [];
            $storedGBData = [];
            $uploadedGBData = [];
            $downloadedGBData = [];
            $apiTransactionsData = [];

            // Set summary data to zeros
            $totalStoredGB = 0;
            $totalUploadedGB = 0;
            $totalDownloadedGB = 0;
            $totalApiTransactions = 0;
        }
        return Inertia::render('Addons/BackblazeReport', [
            'title' => 'Bucket Usage Report: ' . $addonStorageProvider->bucket_name,
            'storageProvider' => $addonStorageProvider->load('product'),
            'reports' => $reports,
            'summary' => [
                'total_stored_gb' => $totalStoredGB,
                'total_uploaded_gb' => $totalUploadedGB,
                'total_downloaded_gb' => $totalDownloadedGB,
                'total_api_transactions' => $totalApiTransactions,
                'bucket_limit' => $addonStorageProvider->product->unit ?? 0,
                'over_usage' => $overUsageData['over_usage_gb'] ?? 0,
                'over_usage_charge' => $overUsageData['total_charge'] ?? 0,
            ],
            'chartData' => [
                'labels' => $chartLabels,
                'stored_gb' => $storedGBData,
                'uploaded_gb' => $uploadedGBData,
                'downloaded_gb' => $downloadedGBData,
                'api_transactions' => $apiTransactionsData,
            ],
            'hasData' => !$reports->isEmpty(),
            'dateRange' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
            ],
            'access_permissions' => team()->permissions(['account:', 'billing:', 'server:', 'site:']),
        ]);
    }
}
