<?php

namespace App\Http\Controllers;

use App\Enums\AvailableStagingDomainEnum;
use App\Enums\ServerStatus;
use App\Enums\SiteType;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\PlansEnum;
use App\Http\Resources\ServerResource;
use App\Http\Resources\SiteResource;
use App\Models\BluePrint;
use App\Models\PhpVersion;
use App\Models\Server;
use App\Models\Site;
use App\Models\Tag;
use App\Models\User;
use App\Services\Database\DatabaseNameGenerator;
use App\Services\SitePlayGround\RequestToJoinTeam;
use App\Services\WordPress\WordPressVersion;
use App\Services\XcloudProduct\ActiveOffers;
use Faker\Factory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Inertia\Inertia;

class SiteController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('viewAny', Site::class);

        $defaultSortType = $request->user()->getMeta('sort_type.site', 'Sort By');
        $requestSortBy = $request->sortBy ? $request->sortBy : $defaultSortType;
        $sortByName = ($requestSortBy === 'Name (A-Z)' || $requestSortBy === 'Name (Z-A)') ? 'Name' : $requestSortBy;
        $sortOrder = $requestSortBy === 'Name (A-Z)' ? 'asc' : 'desc';

        $sites = team()
            ->sites()
            ->accessFilter()
            ->forResource()
            ->orderBy($requestSortBy && $requestSortBy !== 'Sort By' ? str_replace(' ', '_', strtolower($sortByName)) : 'id', $sortOrder)
            ->filter($request)
            ->sortByServer($request)
            ->paginate(10)
            ->through(fn($site) => SiteResource::globalResource($site));

        $selected_servers = user()->serversByInvitation()->wherePivotIn('server_id', team()->servers->pluck('id')->toArray())->pluck('server_id')->toArray();

        $servers = team()->servers()
            ->when($selected_servers && $selected_servers > 0, function ($query) use ($selected_servers) {
                $query->whereIn('id', $selected_servers);
            })
            ->select('id', 'name', 'public_ip')
            ->latest()
            ->take(10)
            ->get();

        if ($request->has('sortByServer') && !$servers->contains('id', $request->sortByServer)) {
            $server = team()->servers()
                ->select('id', 'name', 'public_ip')
                ->where('id', $request->sortByServer)
                ->first();

            if ($server) {
                $servers->push($server);
            }
        }

        if (!blank($request->get('site_sort_type'))) {
            $request->user()->saveMeta('sort_type.site', $request->get('site_sort_type'));
        }

        return Inertia::render('Site/Index', [
            'sites' => $sites,
            'servers' => $servers,
            'tags' => $request->user()->siteTags(),
            'filter' => $request->filter ?? 'All Sites',
            'sortBy' => $request->sortBy ?? $defaultSortType,
            'sortOrder' => $request->sortOrder ?? 'desc',
            'sortByServer' => (int)$request->sortByServer ?? '',
            'can_create_site' => $request->user()->can('create', Site::class),
            'can_view_server' => $request->user()->can('viewAny', Server::class),
        ]);
    }

    /**
     * @throws \Exception
     */
    function chooseServer(Request $request)
    {
        if(!team()->canAccessWithBilling(BillingServices::Site)){
            ## TODO: Extract this if condition to user/team model later(@faisal)
            // user is not in free plan but didn't add payment method
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        $this->authorize('create', Site::class);
        if (team()->servers()->count() < 1) {
            return redirect()->route('server.create');
        }

        $servers = team()->servers()->accessFilter()->provisioned()
            ->doesntHaveLowDiskSpace()
//            ->select(['id as value', 'name as label'])
            ->get()
            ->map(fn($server) => ServerResource::globalResource($server));

        return Inertia::render('Site/New/ChooseServer', [
            'servers' => $servers,
            'appTypes' => Site::getSiteTypes(),
        ]);
    }

    function create(Server $server)
    {
        return $this->createWordPress($server);
    }

    function createWordPress(Server $server)
    {
        $this->authorize('addSite', $server);

        if (!team()->canAccessWithBilling(BillingServices::Site) && !app()->runningUnitTests()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        // if($server->sites()->count() === 1 && $server->team->billingIsActive() && !$server->team->hasActivePaymentMethod()){
        //     return redirect()->route('user.bills-payment')->with('flash', [
        //         'type' => 'warning',
        //         'message' => 'Add a payment method to active your account.'
        //     ]);
        // }

        if (!$server->isProvisioned() || !$server->isConnected() || !in_array($server->status,
                ServerStatus::successStates(), true)) {
            return back()->with('flash', [
                'message' => 'Server is not in a Provisioned state, current status is : '.title($server->isConnected() ? $server->status->value : 'Disconnected'),
                'type' => 'error'
            ]);
        }

        $back_url = request('from') == 'server' ? route('server.index') : route('site.create.server-choose');

        $latestBill = $server->bills()->where('service', $server->getDefaultBillingService())->orderBy('id', 'desc')->first();

        $canAvailInTotal = 0;

        if ($latestBill && $latestBill->billingPlan->name === PlansEnum::Free) {
            $quantityAvailed = $server->sites()->count(); //$server->team->availed_offers[BillingServices::Site->value] ?? 0;

            $canAvailInTotal = ActiveOffers::matchGetQuantity(BillingServices::Site, $latestBill->billingPlan?->name);

            if ($quantityAvailed >= $canAvailInTotal) {
                return redirect()->to($back_url)->with('flash', [
                    'type' => 'warning',
                    'message' => $canAvailInTotal.' site limit reached, please consider upgrading the server bill from free to paid plan to avail more sites.'
                ]);
            }
        }

        $server['provider_name'] = $server['cloud_provider_name'];
        $siteTypes = SiteType::getTypesWithIcon();
        return Inertia::render('Site/New/Choose', [
            'server' => $server->only(['id', 'name', 'cloud_provider_name', 'provider_name', 'cloud_provider_readable']),
            'latestBill' => $latestBill,
            'canAvailInTotal' => $canAvailInTotal,
            'monitoring' => $server->getServerMonitoringInfo(),
            'back_url' => $back_url,
            'siteTypes' => $siteTypes,
        ]);
    }

    function installWordPress(Server $server)
    {
        $this->authorize('addSite', $server);

        if (!team()->canAccessWithBilling(BillingServices::Site) && !app()->runningUnitTests()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }
        if (!$server->isProvisioned() || !$server->isConnected() || !in_array($server->status,
                ServerStatus::successStates(), true)) {
            return back()->with('flash', [
                'message' => 'Server is not in a Provisioned state, current status is : '.title($server->isConnected() ? $server->status->value : 'Disconnected'),
                'type' => 'error'
            ]);
        }

        $server->load(['tags' => fn($query) => $query->select('name', 'tags.id')]);
        $server->provider = $server->cloudProvider?->only('id', 'name', 'provider');

        $tags = Tag::associatedTags('sites');
        $staging_domain = config('services.cloudflare_updated.active');
        $faker = Factory::create();
        $temporaryDomain = Str::slug($faker->city . '-' . $faker->colorName) . '-' . Str::random(5) . '.x-cloud.app';
        $blueprints = BluePrint::where(function($q) use ($server){
            $q->where('team_id', $server->team_id)->orWhere('is_public', true);
        })->orderByDesc('is_default')->get();
        $availableDomains = AvailableStagingDomainEnum::getDomains();

        return Inertia::render('Site/New/InstallWordPress', [
            'server' => $server->only(['id', 'name', 'public_ip', 'tags', 'cloudProvider', 'cloud_provider_name', 'cloud_provider_readable', 'stack']),
            'availablePhpVersions' => PhpVersion::getVersions($server),
            'availableWordPressVersions' => WordPressVersion::getVersions(),
            'tags' => $tags,
            'prefix_id' => $server->sites()->count() + 1,
            'can_manage_blueprints' => user()->can('manageBluePrint', $server->team),
            'siteDefault' => Site::getDefaultConfig([
                'admin_email' => user()?->email,
                'admin' => Str::slug(user()->name),
                'site_user' => DatabaseNameGenerator::generate($temporaryDomain),
                'database_name' => DatabaseNameGenerator::generate('s_'. $temporaryDomain),
                'database_user' => DatabaseNameGenerator::generate('u_'. $temporaryDomain),
                'prefix' => randomLowerString(4,'_'),
                'managed_database_options' => [
                    'do_cluster_size' => '',
                    'do_cluster_node' => '',
                    'do_cluster_name' => '',
                    'do_cluster_region' => '',
                    'do_cluster_id' => '',
                    'do_existing_database' => ''
                ],
            ], $server),
            'hasCloudflareIntegration' => team()->hasCloudflareIntegration(),
            'staging_domain' => $staging_domain,
            'blueprints' => $blueprints,
            'availableDomains' => $availableDomains
        ]);
    }

    function installWordPressToPlayground()
    {
        //abort(403, 'Playground is unavailable temporarily.');

        if ((auth()->check() && auth()->user()->personalTeam()?->billingIsActive()) || currentWhiteLabel() !== null) {
            abort(404);
        }

        $user = User::where('email', RequestToJoinTeam::playgroundTeamOwnerUserEmail)->firstOrFail();

        $server = $user->servers()
                        ->where('status', ServerStatus::PROVISIONED)
                        ->inRandomOrder()
                        ->firstOrFail();

        $server->load([
            'tags' => fn($query) => $query->select('name', 'tags.id')
        ]);

        $server->provider = $server->cloudProvider?->only('id', 'name', 'provider');

        $tags = Tag::associatedTags('sites');
        $staging_domain = config('services.cloudflare_updated.active_playground');
        $faker = Factory::create();
        $temporaryDomain = Str::slug($faker->city . '-' . $faker->colorName) . '-' . Str::random(5) . '.x-cloud.app';
        $blueprints = BluePrint::where('team_id', $server->team_id)->orderByDesc('is_default')->get();

        return Inertia::render('Site/New/InstallWordPressToPlayground', [
            'server' => $server->only(['id', 'name', 'public_ip', 'tags', 'cloudProvider', 'cloud_provider_readable', 'stack']),
            'availablePhpVersions' => PhpVersion::getVersions($server),
            'availableWordPressVersions' => WordPressVersion::getVersions(),
            'tags' => $tags,
            'prefix_id' => $server->sites()->count() + 1,
            'siteDefault' => Site::getDefaultConfig([
                'admin_email' => user()?->email,
                'admin' => Str::slug(user()->name),
                'site_user' => DatabaseNameGenerator::generate($temporaryDomain),
                'database_name' => DatabaseNameGenerator::generate('s_'. $temporaryDomain),
                'database_user' => DatabaseNameGenerator::generate('u_'. $temporaryDomain),
                'managed_database_options' => [
                    'do_cluster_size' => '',
                    'do_cluster_node' => '',
                    'do_cluster_name' => '',
                    'do_cluster_region' => '',
                    'do_cluster_id' => '',
                    'do_existing_database' => ''
                ],
            ], $server),
            'blueprints' => $blueprints,
            'hasCloudflareIntegration' => team()->hasCloudflareIntegration(),
            'staging_domain' => $staging_domain,
        ]);
    }

    function playgroundProgress(Site $site)
    {
        if (!user()->can('view',$site) && $site->server->team->teamInvitations()->where('email', user()->email)->exists()) {
           return Inertia::render('Site/playground/AccessBlocked', [
                'site' => $site->only(['id', 'name', 'public_ip', 'status', 'server_id']),
                'team' => $site->server->team->only(['id', 'name', 'email']),
            ]);
        } elseif (user()->sitesByInvitation->where('id', $site->id)->isEmpty()) {
            abort(404);
        }

        return Inertia::render('SiteProgress', $site->getProgress());
    }

    function migrateWordPress(Server $server)
    {
        $this->authorize('addSite', $server);

        return Inertia::render('Site/New/MigrateWordPress', [
            'server' => $server->only(['id', 'name', 'public_ip']),
        ]);
    }

    function uploadWordPress(Server $server)
    {
        $this->authorize('addSite', $server);

        return Inertia::render('Site/New/UploadWordPress', [
            'server' => $server->only(['id', 'name', 'public_ip']),
        ]);
    }

    public function siteSortByServer(Request $request)
    {
        $this->authorize('viewAny', Site::class);

        $selected_servers = user()->serversByInvitation()->wherePivotIn('server_id', team()->servers->pluck('id')->toArray())->pluck('server_id')->toArray();
        $query = team()->servers()
            ->when(team()->isPlayGround(), function ($query) {
                $query->where('user_id', auth()->id());
            })
            ->when($selected_servers && $selected_servers > 0, function ($query) use ($selected_servers) {
                $query->whereIn('id', $selected_servers);
            })
            ->select('id', 'name', 'public_ip')
            ->latest();

        $search = $request->get('server_name');
        if (!blank($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('public_ip', 'like', '%' . $search . '%');
            });
        }

        $servers = $query->take(10)->get();
        return response()->json($servers);
    }
}
