<?php

namespace App\Http\Controllers;

use App\Models\Server;
use App\Models\Site;
use App\Models\SiteBackupRestoreMigration;
use App\Models\SiteMigration;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SiteBackupRestoreMigrationController extends SiteMigrationController
{
    /**
     * @throws AuthorizationException
     * @throws Exception
     */
    public function destination($model = null,$siteMigration=null)
    {
        $this->authorize('create', Site::class);
        $server = Server::find($model);
        $this->authorize('addSite', $server);

        if (!$server->canMigrateSite()) {
            return back()->with('flash', [
                'message' => 'Server is not in a Provisioned state, current status is : '.title($server->isConnected() ? $server->status->value : 'Disconnected'),
                'type' => 'error'
            ]);
        }
        $siteMigration = intval($siteMigration)>0 ? SiteBackupRestoreMigration::find($siteMigration) : new SiteBackupRestoreMigration();
        return parent::destination($siteMigration);
    }

        /**
     * @throws AuthorizationException
     */
    public function domains(Server $server,Request $request)
    {

        $this->authorize('addSite', $server);
        $siteMigration= app(SiteBackupRestoreMigration::class);
        if ($request->filled('siteMigration') &&  intval($request->siteMigration) > 0){
            $siteMigration = SiteBackupRestoreMigration::findOrFail($request->siteMigration);
            $this->authorize('canSetupDomain', $siteMigration);
            $siteMigration->load('site');
        }

        return Inertia::render('Migration/Domain', [
            'site_migration' => $siteMigration,
            'steps' => app(SiteBackupRestoreMigration::class)->getFormSteps(),
            'post_route' => app(SiteBackupRestoreMigration::class)->getStepPostRouteNames()[SiteMigration::DOMAINS],
            'previous_route' =>route('site.migrate.backup-restore', [$server, $siteMigration->id ?? 'new']),
            'previous_step' => SiteMigration::DESTINATION,
            'current_step' => SiteMigration::DOMAINS,
            'next_step' => SiteMigration::SETTINGS,
            'server' => $server,
            'is_git'=>true,
            'form' => optional($siteMigration)->form[SiteMigration::DOMAINS] ?? null,
            'hasCloudflareIntegration' => team()->hasCloudflareIntegration()
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function source(Server $server, SiteBackupRestoreMigration $siteMigration)
    {
        $this->authorize('addSite', $server);
        abort_unless($siteMigration->team_id == team()->id, 403, 'You are not allowed to access this page');
        return Inertia::render('Migration/RestoreBackupSource', [
            'site_migration' => $siteMigration,
            'steps' => $siteMigration->getFormSteps(),
            'previous_step' => SiteMigration::DOMAINS,
            'current_step' => SiteBackupRestoreMigration::SOURCE,
            'next_step' => SiteBackupRestoreMigration::BACKUPS,
            'post_route' => $siteMigration->getStepPostRouteNames()[SiteBackupRestoreMigration::SOURCE],
            'server' => $server,
            'previous_route' =>route('site.migrate.backup_restore.domains', ['server'=>$server, 'siteMigration' =>$siteMigration->id ?? 'new']),
            'form' => optional($siteMigration)->form[SiteBackupRestoreMigration::SOURCE] ?? null,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function backups(Server $server, SiteBackupRestoreMigration $siteMigration)
    {
        $this->authorize('addSite', $server);
        abort_unless($siteMigration->team_id == team()->id, 403, 'You are not allowed to access this page');
        abort_unless($siteMigration->isFilled(SiteBackupRestoreMigration::SOURCE), 403, 'You are not allowed to access this page');
        $sites = $server->sites()->select(['id','name'])->get();
        $backups =  $siteMigration->siteBackupFiles();
        return Inertia::render('Migration/RestoreBackups', [
            'site_migration' => $siteMigration,
            'steps' => $siteMigration->getFormSteps(),
            'previous_step' => SiteMigration::SETTINGS,
            'current_step' => SiteBackupRestoreMigration::BACKUPS,
            'next_step' => SiteMigration::DATABASE,
            'post_route' => $siteMigration->getStepPostRouteNames()[SiteBackupRestoreMigration::BACKUPS],
            'server' => $server,
            'previous_route' =>route('site.migrate.backup_restore.source', [$server, $siteMigration->id ?? 'new']),
            'form' => optional($siteMigration)->form[SiteBackupRestoreMigration::BACKUPS] ?? null,
            'sites' => $sites,
            'backup_files' => $backups ? $backups?->groupBy('date_format') : null,
            'backups' => $backups,
            'is_bucket' => $siteMigration->isImportTypeBucket(),
        ]);
    }

}
