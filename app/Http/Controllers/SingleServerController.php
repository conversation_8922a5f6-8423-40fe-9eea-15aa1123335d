<?php

namespace App\Http\Controllers;


use App\Enums\CronJobFrequency;
use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Enums\TaskType;
use App\Http\Resources\CustomCommandResource;
use App\Http\Resources\SingleServerResource;
use App\Http\Resources\SiteResource;
use App\Http\Resources\TaskResource;
use App\Models\CronJob;
use App\Models\NodeVersion;
use App\Models\PhpVersion;
use App\Models\SupervisorProcess;
use App\Models\Server;
use App\Models\SudoUser;
use App\Models\Tag;
use App\Models\Task;
use App\Repository\ServerRepository;
use App\Scripts\UpdatePhpSettings;
use App\Services\Server\Monitoring\DiskUsage;
use Carbon\Carbon;
use Google\ApiCore\ApiException;
use Google\ApiCore\ValidationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Inertia\Inertia;
use Inertia\Response;

class SingleServerController extends Controller
{
    function show(Server $server)
    {
        $this->authorize('view', $server);

        $server->update([
            'last_visited_at' => now()
        ]);

        $lastVisitedServers = user()->getMeta('latest_visited_servers_for_team_'.team()->id, []);

        if (!$lastVisitedServers) {
            $lastVisitedServers = [];
        }

        if (!in_array($server->id, $lastVisitedServers)) {
            array_unshift($lastVisitedServers, $server->id);
            user()->saveMeta('latest_visited_servers_for_team_'.team()->id, $lastVisitedServers);
        }

        return to_route('server.sites', $server);
    }

    function sites(Server $server)
    {
        $this->authorize('view', $server);

        $sites = $server->sites()->with([
                'server',
                'siteMigration',
                'server.team',
                'server.vulnerabilitySetting',
                'server.latestMonitor',
                'tags:id,name',
                'server.tags:id,name',
            ])
            ->withCount([
                'vulnerabilities' => fn($q) => $q->where('ignored', false),
            ])
            ->withExists([
                'backupSettings as is_backup_enabled' => function ($query) {
                    $query->where('auto_backup', true);
                },
                'backupFiles as has_local_backup' => function ($query) {
                    $query->where('is_remote', false);
                }
            ])
            ->paginate(10)
            ->through(fn ($site) => SiteResource::globalResource($site));

        return Inertia::render('Server/Sites', [
            'server' => SingleServerResource::make($server),
            'title' => $server->name.' Sites',
            'sites' =>$sites,
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'siteTypes' => SiteType::getTypesWithIcon(),
            'oneClickApps' => SiteType::getOneClickApps($server),
            'defaultType' => request()->has('type') ? request()->get('type') : SiteType::WORDPRESS->value,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    function database(Server $server)
    {
        $this->authorize('viewDB', $server);

        $server->loadCount(['sites']);
        $dbInfoLastPulledAt = Arr::get($server->database_info, 'last_pulled_at')
            ? Carbon::parse(Arr::get($server->database_info, 'last_pulled_at') )->diffForHumans(now(), true) . ' ago'
            : 'N/A';

        // Check if phpMyAdmin is enabled on this server (any status)
        $phpMyAdminSite = $server->getPhpMyAdminSite();

        if ($phpMyAdminSite){
            $phpMyAdminSite = SiteResource::make($phpMyAdminSite);
        }

        return Inertia::render('Server/Database', [
            'server' => SingleServerResource::make($server),
            'databases' => $server->getDatabases(),
            'databaseUsers' => $server->getDatabaseUsers(),
            'dbInfoLastPulledAt' => $dbInfoLastPulledAt,
            'phpMyAdminSite' => $phpMyAdminSite,
            'can_create_db' => user()->can('createDB', $server),
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
        ]);
    }

    function sudoUsers(Server $server)
    {
        $this->authorize('access', $server);

        $server->loadCount(['sites']);

        return Inertia::render('Server/SudoUsers', [
            'server' => SingleServerResource::make($server),
            'sudo_users' => $server->sudoUsers()->withCount(['sshKeyParis'])->paginate(),
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'can_create_edit_sudo_user' => user()->can('createEditSudoUser', $server),
        ]);
    }

    function createSudoUser(Server $server)
    {
        $this->authorize('createEditSudoUser', $server);

        return Inertia::render('Server/CreateSudoUsers', [
            'server' => SingleServerResource::make($server),
            'keys' => team()->sshKeyParis()->where('type','team_key')->select(['id as value', 'name as label'])->get(),
            'sudo_users' => $server->sudoUsers()->select(['id', 'server_id', 'sudo_user'])->paginate(),
            'can_create_ssh_key' => user()->can('access', $server)
        ]);
    }

    function editSudoUser(Server $server, SudoUser $sudoUser)
    {
        $this->authorize('createEditSudoUser', $server);
        abort_if($sudoUser->server_id !== $server->id, 403,"You don't have permission to access this page.");

        return Inertia::render('Server/EditSudoUsers', [
            'server' => SingleServerResource::make($server),
            'keys' => team()->sshKeyParis()->select(['id as value', 'name as label'])->get(),
            'sudo_user' => $sudoUser,
            'selected_keys' => $sudoUser->sshKeyParis()->pluck('id')->toArray(),
            'can_create_ssh_key' => user()->can('access', $server)
        ]);
    }

    function metaData(Server $server)
    {
        $this->authorize('service', $server);

        $server->loadCount(['sites'])->load('tags:name,id');

        $tags = Tag::associatedTags('servers');

        return Inertia::render('Server/MetaData', [
            'server' => SingleServerResource::make($server),
            'tags' => $tags,
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
        ]);
    }

    function settings(Server $server)
    {
        $this->authorize('settings', $server);

        $server->loadCount(['sites']);
        return Inertia::render('Server/Settings', [
            'server' => SingleServerResource::make($server),
            'timezone_list' => timezone_list(),
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'is_xcloud' => $server->isXCloud(),
            'is_xcloud_or_white_label' => $server->isXCloudOrWhiteLabel(),
            'database_password' =>  user()->can('accessDB', $server) ? $server->database_password : null,
        ]);
    }

    public function firewallManagement(Server $server)
    {
        $this->authorize('manageFirewall', $server);
        return Inertia::render('Server/FirewallManagement', [
            'server' => SingleServerResource::make($server),
            'firewallRules' => $server->firewallRules()->get(),
            'can_add_firewall_rule' => user()->can('addFirewallRule', $server),
        ]);
    }

    public function vulnerabilitySettings(Server $server)
    {
        $this->authorize('vulnerabilitySettings', $server);
        if($server->team->isFreePlan()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        return Inertia::render('Server/VulnerabilitySettings', [
            'server' => SingleServerResource::make($server),
            'vulnerability_scan' => $server->getMeta('vulnerability_scan'),
            'vulnerability_setting' => $server->vulnerabilitySetting()->first(),
            'can_perform_support_level_action' => $server->team->allowSupportOneAction(user: user())
        ]);
    }

    public function securityUpdate(Server $server)
    {
        $this->authorize('securityUpdate', $server);
        $settings= $server->getServerInfo('security_settings',[
            'reboot_time' => '00:00',
            'automatic_reboot' => false,
        ]);
        return Inertia::render('Server/AutoSecurityUpdate', [
            'server' => SingleServerResource::make($server),
            'timezone' => $server->getTimeZoneAttribute(),
            'settings' => $settings,
        ]);
    }

    function monitoring(Server $server)
    {
        $this->authorize('service', $server);
        if($server->team->isFreePlan()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $server->loadCount(['sites']);
        $ram_cpu_usages = $server->monitors()
            ->select(['cpu->usedPercent as cpu_usage','memory->percent as ram_usage'])
            ->where('created_at', '>=', Carbon::now()->subHours(24))
            ->selectRaw('DATE_FORMAT(created_at, "%h:%i %p") AS time_at')
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();


        $serverMonitoring = $server->getServerHealth();
        $diskUsage = $serverMonitoring ? new DiskUsage($serverMonitoring?->disk) : null;
        $totalServerRamSize = $serverMonitoring ? (int) ($serverMonitoring?->memory['total'] ?? 1) : null;
        $topProcesses = $serverMonitoring ? ($serverMonitoring->others['top_processes'] ?? null) : null;


        return Inertia::render('Server/Monitoring', [
            'server' => SingleServerResource::make($server),
            'server.health' => $serverMonitoring,
            'server.disk_usage' => [
                'used' => $diskUsage?->getTotalUsed(),
                'total' => $diskUsage?->getTotalSize(),
                'available' => $diskUsage?->getAvailable(),
                'used_percentage' => $diskUsage?->getUsedPercentage(),
            ],
            'server.ram_size' => $totalServerRamSize,
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'ram_cpu_usages' => array_reverse($ram_cpu_usages),
            'top_processes' => $topProcesses,
        ]);
    }


    /**
     * @throws ValidationException
     * @throws ApiException
     */
    function management(Server $server)
    {
        $this->authorize('settings', $server);

        $server->loadCount(['sites']);
        $unusedBill = format_billing($server->getMonthlyUnusedBillAmount());

        $usageHoursOnThisMonth = $server->getMonthlyActualUsedHours();
        $usageHoursOnThisMonth = $usageHoursOnThisMonth === 0 ? 1 : $usageHoursOnThisMonth;

        $costPerHourMonthly = $server->getMonthlyCostPerHour();
        $getMonthlyPaid = $server->getMonthlyTotalActiveBilling();

        $returnableAmount = format_billing($getMonthlyPaid - ($usageHoursOnThisMonth * $costPerHourMonthly));

        $serverRepository = new ServerRepository($server);
//        using ajax to load the upgradable server types
//        $upgradableServerTypes = $serverRepository->getUpgradableServerTypes();
        $currentServerTypeInfo = $serverRepository->getServerInfoByType($server->getSize());
        $hasBackupOnCurrentServer = $server->backups;

        return Inertia::render('Server/Management', [
            'server' => SingleServerResource::make($server),
//            'upgradableServerTypeList' => $upgradableServerTypes,
            'currentServerTypeInfo' => $currentServerTypeInfo,
            'hasBackupOnCurrentServer' => $hasBackupOnCurrentServer,
            'usageHoursOnThisMonth' => $usageHoursOnThisMonth,
            'costPerHourMonthly' => $costPerHourMonthly,
            'getMonthlyPaid' => $getMonthlyPaid,
            'returnableAmount' => $returnableAmount,
            'unusedAmount' => $unusedBill,
            'openDeleteModal' => request()->has('openDeleteModal'),
            'openArchiveModal' => request()->has('openArchiveModal'),
            'isResizable' => (bool) $server->cloudProvider?->isResizable() && !isWhiteLabel(),
            'canUpgradeServer' => user()->can('resize', $server),
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'is_xcloud' => $server->isXCloud(),
            'is_xcloud_or_white_label' => $server->isXCloudOrWhiteLabel(),
            'is_any_provider' => $server->isAnyProvider(),
            'hasCloudflareIntegration' => team()->hasCloudflareIntegration(),
            'database_type' => $server->database_type->readable(),
        ]);
    }

    public function phpSettings(Server $server)
    {
        $this->authorize('managePhp', $server);

        if(is_null($server->node_version)){
            $server->getNodeVersions();
        }

        return Inertia::render('Server/PhpSettings', [
            'server' => SingleServerResource::make($server),
            'title' => $server->name.' - PHP Settings',
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'available_php_versions' => PhpVersion::getVersions($server),
            'server_php_version' => $server->php_version,
        ]);
    }

    public function nodeSettings(Server $server)
    {
        $this->authorize('manageNode', $server);

        if(is_null($server->node_version)){
            $server->getNodeVersions();
        }

        return Inertia::render('Server/NodeSettings', [
            'server' => SingleServerResource::make($server),
            'title' => $server->name.' - Node Configuration',
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'available_node_versions' => NodeVersion::getVersions(),
            'server_node_version' => $server->node_version,
        ]);
    }

    //update php settings
    public function updatePhpSettings(Server $server, Request $request)
    {
        $this->authorize('managePhp', $server);

        $php_settings = $request->validate([
            'php_version' => 'required|string|in:'.implode(',', array_keys($server->getServerInfo('php_settings'))),
            'upload_max_filesize' => 'required|numeric',
            'max_execution_time' => 'required|numeric',
            'max_input_time' => 'required|numeric',
            'memory_limit' => 'required|numeric', // max:2048|min:128
            'max_input_vars' => 'required|numeric',
            'post_max_size' => 'required|numeric',
            'session_gc_maxlifetime' => 'required|numeric',
            'opcache_enabled' => 'required|boolean',
        ]);

        $server->runInline(new UpdatePhpSettings($server, $php_settings));

        return redirect()->back()->with('flash', [
            'message' => 'PHP Settings Updated.',
            'body' => 'Settings will be reflected in your server in a while.',
            'type' => 'success'
        ]);
    }

    public function updatePhpOPCache(Server $server, Request $request)
    {
        $this->authorize('managePhp', $server);

        $php_settings = $request->validate([
            'php_opcache_enabled' => 'required|boolean',
        ]);

        $server->update([
            'server_info->php_settings->php_opcache_enabled' => $php_settings['php_opcache_enabled'],
        ]);

        return redirect()->back()->with('flash', [
            'message' => 'PHP Settings Updated.',
            'body' => 'Settings will be reflected in your server in a while.',
            'type' => 'success'
        ]);
    }

    public function logs(Server $server)
    {
        $this->authorize('manageLogs', $server);

        $server->loadCount(['sites']);

        return Inertia::render('Server/Logs', [
            'server' => SingleServerResource::make($server),
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'can_clear_logs' => user()->can('clear-logs', $server),
        ]);
    }

    public function events(Server $server)
    {
        $this->authorize('events', $server);

        $server->loadCount(['sites']);

        return Inertia::render('Server/Events', [
            'server' => SingleServerResource::make($server),
            'taskEvents' => $server
                ->tasks()
                ->with(['server:id,name', 'site:id,name', 'initiatedBy:id,name,email', 'team'])
                ->latest()
                ->paginate(10)
                ->through(fn($task) => TaskResource::make($task)),
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'is_admin' => user()->isAdmin(),
        ]);
    }

    public function commandRunner(Server $server)
    {
        $this->authorize('customCommandRunner', $server);

        $server->loadCount(['sites']);
        $commands = $server
            ->tasks()
            ->where('type', TaskType::CUSTOM->value)
            ->latest()
            ->paginate(10)
            ->through(fn($command) => CustomCommandResource::make($command));

        return Inertia::render('Server/CustomCommand', [
            'server' => SingleServerResource::make($server),
            'commands' => $commands,
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'is_admin' => user()->isAdmin(),
        ]);
    }

    function progress(Server $server)
    {
        $this->authorize('progress', $server);
        if ($server->isModifying()) {
            return Inertia::render('ServerProgress', $server->modifyingProgressPageData());
        }
        return Inertia::render('ServerProgress', $server->progressPageData());
    }

        /**
         */
    function migration(Server $server) : Response
    {
        $this->authorize('update', $server);

        return Inertia::render('Server/Migration', [
            'server' => SingleServerResource::make($server),
            'migrations' => $server->serverMigration
        ]);
    }

    function supervisor(Server $server)
    {
        $this->authorize('manageSupervisorProcess', $server);
        $server->loadCount(['sites']);
        $supervisor_processes = $server->supervisorProcesses()->orderBy('id', 'desc')->paginate(10);

        return Inertia::render('Server/Supervisor', [
            'title' => $server->name.' - Supervisor Processes',
            'server' => SingleServerResource::make($server),
            'supervisor_processes' => $supervisor_processes,
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'can_create_supervisor_process' => user()->can('createSupervisorProcess', $server),
        ]);
    }

    function createSupervisorProcess(Server $server)
    {
        $this->authorize('createSupervisorProcess', $server);
        return Inertia::render('Server/CreateSupervisorProcess', [
            'server' => SingleServerResource::make($server),
        ]);
    }

    function editSupervisorProcess(Server $server, SupervisorProcess $supervisorProcess)
    {
        $this->authorize('manage', $supervisorProcess);
        abort_if($supervisorProcess->server_id !== $server->id, 403, "You don't have permission to access this page.");

        return Inertia::render('Server/EditSupervisorProcess', [
            'server' => SingleServerResource::make($server),
            'supervisor_process' => $supervisorProcess,
        ]);
    }

    function cronJob(Server $server)
    {
        $this->authorize('mangeCronJob', $server);
        $server->loadCount(['sites']);
        $cron_jobs = $server->cronJobs()->orderBy('id', 'desc')->paginate(10);
        return Inertia::render('Server/CronJob', [
            'title' => $server->name.' - Cron Jobs',
            'server' => SingleServerResource::make($server),
            'cron_jobs' => $cron_jobs,
            'can_add_server' => user()->can('addServer', user()->currentTeam),
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'can_create_cron_job' => user()->can('createCronJob', $server),
        ]);
    }

    function createCronJob(Server $server)
    {
        $this->authorize('createCronJob', $server);
        return Inertia::render('Server/CreateCronJob', [
            'server' => SingleServerResource::make($server),
            'frequency_lists' => CronJobFrequency::getFrequencyList()
        ]);
    }

    function editCronJob(Server $server, CronJob $cronJob)
    {
        $this->authorize('mangeCronJob', $server);
        abort_if($cronJob->server_id !== $server->id, 403,"You don't have permission to access this page.");

        if (!$cronJob->frequency->isCustom()) {
            $cronJob->pattern = null;
        }
        return Inertia::render('Server/EditCronJob', [
            'server' => SingleServerResource::make($server),
            'frequency_lists' => CronJobFrequency::getFrequencyList(),
            'cron_job' => $cronJob,
        ]);
    }

    /**
     * @throws ValidationException
     * @throws AuthorizationException
     * @throws ApiException
     */
    function backup(Server $server): Response
    {
        $this->authorize('manageProviderBackup', $server);

        $backupEnabled = $server->backups;
        $isUnderXCloud = $server->isUnderXCloudVultr();
        $isWhiteLabel = !is_null($server?->team?->whiteLabel);
        $backupCharge = (new ServerRepository($server))->getBackupCost();

        return Inertia::render('Server/Backup', [
            'server' => SingleServerResource::make($server),
            'backupEnabled' => $backupEnabled,
            'isXCloud' => $isUnderXCloud,
            'isWhiteLabel' => $isWhiteLabel,
            'isProviderBackupManageable' => $server->cloudProvider?->isProviderBackupManageable(),
            'backupCharge' => $backupCharge,
            // these are for top bar's permission check - common for all pages
            'can_delete_server' => user()->can('delete', $server),
            'can_add_site' => user()->can('addSite', $server),
            'can_restart_server' => user()->can('reStartServer', $server),
            'can_restart_nginx_server' => user()->can('reStartNginx', $server),
            'can_restart_mysql_server' => user()->can('reStartMySql', $server),
            'can_clone_server' => user()->can('cloneServer',$server),
            'can_perform_backup' => $server->team->allowSupportOneAction(user: user())
        ]);
    }



    // TODO: remove me.
    // function createDatabase(Request $request)
    // {
    //     ## TODO: will replace this route with Bash script later
    //
    //     // Get the contents of the JSON file
    //     $json = file_get_contents(base_path('resources/json/server_users_and_databases.json'));
    //
    //     // Decode the JSON data into a PHP associative array
    //     $data = json_decode($json, true);
    //
    //     $data['databases'][] = [
    //         "db_name" => $request->get('db_name'),
    //         "db_user" => $request->get('db_user'),
    //         "db_password" => $request->get('db_password'),
    //     ];
    //
    //     // Write the updated JSON string to a file
    //     file_put_contents(base_path('resources/json/server_users_and_databases.json'), json_encode($data));
    //
    //     return back()->with('flash', [
    //         'type' => 'success',
    //         'message' => 'The database has been added to your server.'
    //     ]);
    // }
    //
    // function createDatabaseUser(Request $request)
    // {
    //     ## TODO: will replace this route with Bash script later
    //
    //     // Get the contents of the JSON file
    //     $json = file_get_contents(base_path('resources/json/server_users_and_databases.json'));
    //
    //     // Decode the JSON data into a PHP associative array
    //     $data = json_decode($json, true);
    //
    //     $data['users'][] = [
    //         "id" => rand(),
    //         "username" => $request->get('username'),
    //         "password" => $request->get('password'),
    //         'selected_databases' => $request->get('selected_databases')
    //     ];
    //
    //     // Write the updated JSON string to a file
    //     file_put_contents(base_path('resources/json/server_users_and_databases.json'), json_encode($data));
    //
    //     return back()->with('flash', [
    //         'type' => 'success',
    //         'message' => 'The user has been added to database.'
    //     ]);
    // }
}
