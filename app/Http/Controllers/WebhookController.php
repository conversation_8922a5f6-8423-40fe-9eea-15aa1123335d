<?php

namespace App\Http\Controllers;

use App\Enums\FluentCRM\TagTypes;
use App\Enums\WebhookTypes;
use App\Enums\XcloudBilling\PlansEnum;
use App\Http\Requests\StoreWebhookRequest;
use App\Http\Requests\UpdateWebhookRequest;
use App\Jobs\SubscribeUserToFluentCRM;
use App\Jobs\UpdateContactOnFluentCRM;
use App\Models\BillingPlan;
use App\Models\Package;
use App\Models\Team;
use App\Models\User;
use App\Models\Utm;
use App\Models\Webhook;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use function Aws\map;

class WebhookController extends Controller
{
    public function handle(Request $request, string $gateway)
    {
        $webhook = Webhook::where('id', request()->route('webhook'))->firstOrFail();

        $webhook->service->handle();

        return response()->json(['message' => 'Webhook handled successfully']);
    }

    public function handleSplitPayment(Request $request)
    {
        $apiSecret = config('services.split_payment.api_secret');

        // Create Webhook
        $webhook = Webhook::create([
            'type' => WebhookTypes::SplitPayment,
            'config' => $request->all(),
        ]);

        if($apiSecret !== $request->get('api_secret')){
            return response()->json(['message' => 'Invalid API secret'], 401);
        }

        if(empty($request->get('account_email'))){
            return response()->json(['message' => 'Account email is required'], 400);
        }

        $servicePlanId = $request->get('service_plan');

        $packageName = match($servicePlanId){
            0, "0" => "GROWTH",
            1, "1" => "PERFORMANCE",
            2, "2" => "SUPREME",
            default => null,
        };

        // if(app()->environment('production')){
        //     $packageIds = [4, 5, 6];
        // }else if(app()->environment('staging')){
        //     $packageIds = [7, 8, 9];
        // }else{
        //     // for local environment
        //     $packageIds = [4, 5, 6];
        // }

        // create user
        $user = User::firstOrCreate([
            'email' => $request->get('account_email')
        ], [
            'name' => $request->get('account_name'),
            'password' => Hash::make($request->get('account_password')),
            'is_active' => true,
        ]);

        if(!$user){
            return response()->json(['message' => 'User not found'], 404);
        }

        if(!$user->is_active){
            $user->update(['is_active' => true]);
        }

        // create team
        $splitPaymentPlan = BillingPlan::where('name', PlansEnum::SplitPayment)->first();

        if($user->wasRecentlyCreated){
            $team = $user->ownedTeams()->save(Team::forceCreate([
                'user_id' => $user->id,
                'email' => $user->email,
                'name' => explode(' ', $user->name, 2)[0] . "'s Team",
                'personal_team' => true,
                'active_plan_id' => $splitPaymentPlan?->id,
            ]));
        }else{
            $team = $user->ownedTeams()->first();
        }
        if ($team->hasActivePaymentMethod() && $team->whiteLabel) {
            $team->update([
                'active_plan_id' => BillingPlan::where('name', PlansEnum::WebHosting)->first()?->id,
            ]);
        } elseif($team->hasActivePaymentMethod()){
            $starterPlan = BillingPlan::where('name', PlansEnum::Starter)->first();
            $team->update([
                'active_plan_id' => $starterPlan?->id,
            ]);
        } else{
            $team->update([
               'active_plan_id' => $splitPaymentPlan?->id
            ]);
        }

        if (!$team->hasDuesToPay()) {
            $team->setBillingStatusActive();
        }

        $webhook->update([
           'team_id' => $team->id,
        ]);

        $package = Package::where('name', $packageName)->first();

        // get the package that has $servicePlan matched with Package id
        if($package){
            if($team->packages->contains($package)){
                return response()->json(['message' => 'User already has the package'], 400);
            }
            $team->attachPackage($package);
        }else{
            logger()->error('WebhookController: Invalid service plan', ['service_plan' => $servicePlanId, 'request' => $request->all()]);
            return response()->json(['message' => 'Invalid service plan'], 400);
        }

        if(Utm::getUtmIdFromCookie()){
            $team->setUtmSource(utmId: Utm::getUtmIdFromCookie(),sessionId: optional(session())->getId(),ip: request()->ip());
            Utm::clearUtmCookie();
        }

        UpdateContactOnFluentCRM::dispatch($team, [
            TagTypes::SplitPayment->value,
        ]);

        //Set user current team
        if (!$user->current_team_id) {
            $user->update(['current_team_id' => $team->id]);
        }

        // save split payment info on team meta
        $team->update([
           'meta->split_payment' => [
               'split_payment_enabled' => true,
           ]
        ]);

        return response()->json(['message' => 'Split Payment Processed Successfully']);
    }
}
