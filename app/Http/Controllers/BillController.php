<?php

namespace App\Http\Controllers;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Http\Resources\BillResource;
use App\Models\Bill;
use App\Models\GeneralInvoice;
use App\Repository\StripePaymentRepository;
use App\Services\XcloudProduct\CalculateBill;
use Arr;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Inertia\Inertia;
use Illuminate\Http\Request;
use PHPUnit\Util\Exception;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\PaymentIntent;

class BillController extends Controller
{
    public function bills()
    {
        $this->authorize('createBilling', team());

        $bills = team()->bills()->latest()->paginate()->through(fn($bill) => BillResource::make($bill));
        return Inertia::render('Profile/Bills', [
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:']),
            'plan' => team()->getPlan(),
            'bills' => $bills
        ]);
    }

    public function billCalculator()
    {
        if(!auth()->user()->isAdmin()){
            abort(404);
        }

        return Inertia::render('Bill/BillCalculator', [
            'renewalPeriods' => BillRenewalPeriod::asValueLabel(),
            'billData' => request()->all() ?? []
        ]);
    }

    public function calculateBill(Request $request)
    {
        if(!auth()->user()->isAdmin()){
            abort(404);
        }

        // validate request
        $request->validate([
            'bill_amount' => 'required|numeric',
            'renewal_period' => 'required|in:'.implode(',', BillRenewalPeriod::asValue()),
            'billing_date' => 'required|date'
        ]);

        $billingDate = $request->get('billing_date');
        if(Carbon::hasFormat($billingDate, 'Y-m-d H:i:s')){
            // contains second
            $billingDate = Carbon::createFromFormat('Y-m-d H:i:s', $billingDate);
        }else{
            if(Carbon::hasFormat($billingDate, 'Y-m-d\TH:i:s')){
                $billingDate = Carbon::createFromFormat('Y-m-d\TH:i:s', $billingDate);
            }else{
                $billingDate = Carbon::createFromFormat('Y-m-d\TH:i', $billingDate);
            }
        }

        $data = CalculateBill::amount(
            amount: $request->get('bill_amount'),
            calculationForDate: $billingDate,
            renewalPeriod: BillRenewalPeriod::from($request->get('renewal_period'))
        );

        return back()->with('flash', [
            'billData' => $data
        ]);
    }

    /**
     * @throws ApiErrorException
     * @throws CardException
     * @throws GuzzleException
     */
    public function payBills(Request $request)
    {
        $this->authorize('createBilling', team());

        throw new Exception('Bill payment is disabled for now! Need to adjust this with invoice payment');

        if(team()->activePaymentMethod()->isEmpty()){
            return back()->with('flash', [
                'error' => 'No active payment method found!'
            ]);
        }

        $invoiceId = Arr::get($request->get('selectedBill'), 'invoice_id');

        if($invoiceId) {
            $invoice = GeneralInvoice::find($invoiceId);
            if($invoice->status === BillingStatus::Paid){
                return back()->with('flash', [
                    'success' => 'Invoice already paid!'
                ]);
            }
            $unpaidBills = $invoice->unpaidBills()->get();

            if($invoice->status !== BillingStatus::Paid){
                if($invoice->team->activePaymentMethod()->isEmpty()){
                    return back()->with('flash', [
                        'error' => 'No active payment method found!'
                    ]);
                }

                if($invoice->team->activePaymentMethod()->first()->payment_gateway === PaymentGateway::Stripe)
                {
                    try {
                        $paymentIntent = (new StripePaymentRepository())->takePayment($invoice);

                        if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
                            $invoice->setStatusPaid();

                            $unpaidBills->each(function (Bill $bill) {
                                $bill->setStatusPaid();
                            });
                        }

                        if(!$invoice->team->billingIsActive() && !$invoice->team->hasDuesToPay()){
                            $invoice->team->setBillingStatusActive();
                        }

                        // send email
                        $invoice->team->sendInvoiceEmail($invoice);

                        return back()->with('flash', [
                            'success' => 'Payment successful'
                        ]);
                    }catch (CardException|InvalidRequestException|ApiErrorException $e) {
                        $invoice->setStatusPaymentFailed();
                        $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), $e->getMessage());
                        $unpaidBills->each(function (Bill $bill) {
                            $bill->setStatusUnpaid();
                        });

                        return back()->with('flash', [
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
        }else{
            $unpaidBill = Bill::find(Arr::get($request->get('selectedBill'), 'id'));

            if(!$unpaidBill){
                return back()->with('flash', [
                    'error' => 'No bill found!'
                ]);
            }

            $amountToPay = Arr::get($request->get('selectedBill'), 'billing_amount');

            if($amountToPay <= 0){
                return back()->with('flash', [
                    'warning' => 'No amount to pay!'
                ]);
            }

            $invoice =  GeneralInvoice::create([
                'reference_no' => uniqid(),
                'title' => 'Invoice for ' . Arr::get($request->get('selectedBill'), 'title'),
                'description' => Arr::get($request->get('selectedBill'), 'description'),
                'amount' => $amountToPay,
                'currency' => BillingCurrency::USD,
                'status' => BillingStatus::Unpaid,
                'team_id' => team()->id,
                'payment_method_id' => team()->activePaymentMethod()->first()->id,
                'gateway_payment_method_id' => Arr::get(team()->activePaymentMethod()->first()->meta, 'stripe.payment_method'),
                'gateway_customer_id' => team()->activePaymentMethod()->first()->customer_id,
                'customer_email' => team()->activePaymentMethod()->first()->getMeta('customer.email') ?: team()->owner->email
            ]);

            $unpaidBill->update([
                'invoice_id' => $invoice->id
            ]);

            // take payment
            if($invoice->team->activePaymentMethod()->first()->payment_gateway === PaymentGateway::Stripe) {
                try {
                    $paymentIntent = (new StripePaymentRepository())->takePayment($invoice);

                    if ($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED) {
                        $invoice->setStatusPaid();

                        $unpaidBill->setStatusPaid();
                    }

                    if(!$invoice->team->billingIsActive() && !$invoice->team->hasDuesToPay()){
                        $invoice->team->setBillingStatusActive();
                    }

                    // send email
                    $invoice->team->sendInvoiceEmail($invoice);

                    return back()->with('flash', [
                        'success' => 'Payment successful'
                    ]);
                } catch (CardException|InvalidRequestException|ApiErrorException $e) {
                    $invoice->setStatusPaymentFailed();
                    $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), $e->getMessage());

                    $unpaidBill->setStatusPaymentFailed();

                    return back()->with('flash', [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }
}
