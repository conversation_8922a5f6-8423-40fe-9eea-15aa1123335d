<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DebugController extends Controller
{
    public function index(Request $request)
    {
        abort_unless(config('app.debug') || ($request->get('token') && ($request->get('token') === config('app.debug_token'))), 403);

        return [
            'request' => $request->except('token'),
            'host' => get_request_host(),
            'url' => url()->current(),
            'url_path' => url('/path'),
            'url_route' => route('terms-and-services'),
            'callback_url' => callback_url('terms-and-services'),
            'whitelabel' => currentWhiteLabel(),
            'user' => auth()->user(),
            'team' => auth()->user()?->currentTeam,
            'headers' => $request->headers->all(),
        ];
    }

    public function api(Request $request)
    {
        return $this->index($request);
    }
}
