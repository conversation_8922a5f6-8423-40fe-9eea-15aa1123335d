<?php

namespace App\Http\Controllers;

use App\Http\Resources\ServerResource;
use App\Models\AutoSiteClone;
use App\Models\ManualSiteMigration;
use App\Models\PhpVersion;
use App\Models\Server;
use App\Models\Site;
use App\Models\AutoSiteMigration;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use App\Services\Database\DatabaseNameGenerator as Generator;
use App\Services\WordPress\WordPressVersion;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Arr;
use Inertia\Inertia;

class SiteCloneController extends Controller
{
    /**
     * @throws AuthorizationException
     * @throws Exception
     */
    public function destination($model = null)
    {
        $serversQuery = team()->servers()->accessFilter()->provisioned()
            ->doesntHaveLowDiskSpace();

        $siteCloneQuery = team()->siteClones();

        $servers = $serversQuery->get()->map(fn($server) => ServerResource::globalResource($server));

        if ($servers->isEmpty()) {
            return redirect()->route('server.create');
        }

        $siteClone = null;
        $newClone = false;

        $selectedServer = $serversQuery->where('id', request('server'))->firstOrFail();

        if ((request('server') == 'select' || $selectedServer) && request('siteClone') == 'new') {
            $newClone = true;
        }

        if (!$newClone && request('siteClone')) {
            $siteClone = $siteCloneQuery->where('id', request('siteClone'))
                ->where('server_id', request('server'))
                ->firstOrFail();

            $cloneFromSite = Site::findOrFail(Site::findOrFail($siteClone->site_id)->getMeta('cloneInfo.cloneFromSiteId'));
            $this->authorize('view', $cloneFromSite);
            $fromServerName = $cloneFromSite->server->name;
        }

        if ($siteClone && !$newClone) {
            $this->authorize('hasAccessToModifyCloneDestination', $siteClone);
        }

        $siteId = request()->has('site') ? request('site') : null;

        if ($siteId) {
            $this->authorize('view', Site::findOrFail($siteId));
        }

        return Inertia::render('Clone/Destination', [
            'steps' => $siteClone ? $siteClone->getFormSteps() : $model->getFormSteps(),
            'previous_step' => null,
            'current_step' => SiteClone::DESTINATION,
            'next_step' => SiteClone::DOMAINS,
            'site_clone' => $siteClone,
            'form' => optional($siteClone)->form[SiteClone::DESTINATION] ?? [],
            'servers' => $servers,
            'selected_server' => $selectedServer,
            'site' => $siteId,
            'fromServerName' => $fromServerName ?? null
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function settings(Server $server, Site $site, AutoSiteClone $siteClone)
    {
        $this->authorize('canSetupCloneSettings', $siteClone);

        return Inertia::render('Clone/Settings', [
            'site_clone' => $siteClone,
            'steps' => $siteClone->getFormSteps(),
            'previous_step' => SiteClone::DATABASE,
            'current_step' => SiteClone::SETTINGS,
            'next_step' => SiteClone::CONFIRM,
            'server' => $server,
            'form' => optional($siteClone)->form[SiteClone::SETTINGS] ?? null,
//            'needPrefix' => !$siteMigration->isManual(),
            'php_version' => PhpVersion::DEFAULT,
            'php_versions' => PhpVersion::getVersions($server),
            'wordpress_versions' => WordPressVersion::getVersions(),
            'default_site_config' => Site::getDefaultConfig([
                'database_name' => Generator::generateName($siteClone->site),
                'database_user' => Generator::generateUser($siteClone->site),
                'prefix' => Generator::generate($siteClone->domain_name).'_',
                'site_user' => Generator::generate($siteClone->domain_name),
            ], $server),
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function confirm(Server $server, Site $site, AutoSiteClone $siteClone)
    {
        $this->authorize('cloneConfirmation', $siteClone);

        return Inertia::render('Clone/Confirm', [
            'site_clone' => $siteClone,
            'steps' => $siteClone->getFormSteps(),
            'previous_step' => SiteMigration::SETTINGS,
            'current_step' => SiteMigration::CONFIRM,
            'next_step' => null,
            'form' => optional($siteClone)->form[SiteClone::CONFIRM] ?? null,
            'filled_data' => $siteClone->form,
            'notification_mails' => $siteClone->notification_mails,
            'suggestion_message' => '',
            'domain_parking_method' => Arr::get($siteClone, 'form.domains.domain_parking_method'),
            'post_route' => app(AutoSiteClone::class)->getStepPostRouteNames()[SiteClone::CONFIRM],
            'server_name' => $server->name . '(' . ($site->cloneToRemote() ? 'other server' : 'same server') . ')',
            'has_database' => $site->hasDatabase(),
        ]);
    }
}
