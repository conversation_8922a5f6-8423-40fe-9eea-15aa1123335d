<?php

namespace App\Http\Controllers;

use App\Enums\CloudProviderEnums;
use App\Enums\WhiteLabel\ProductStatus;
use App\Enums\XcloudBilling\BillingServices;
use App\Models\CloudProvider;
use App\Models\Product;
use App\Models\Server;
use App\Models\SshKeyPair;
use App\Models\Tag;
use App\Repository\XCloudVultrServerTypeRepository;
use App\Rules\DatabasePassword;
use App\Services\Database\DatabaseEngine;
use App\Services\Providers\Providers;
use App\Services\Server\Web\CloudProviderServiceFactory;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class ServerCreationController extends Controller
{
    /**
     * @throws \Exception
     */
    function chooseProvider()
    {
        $this->authorize('create', Server::class);
        if (isWhiteLabel()){
            # If the user is a white label user, then redirect to the  server creation page
            return redirect()->route('white-label-server.create');
        }
        // Shows the all provider page like Vultr, GCP, Digital Ocean and xCloud Managed/Provide

        return Inertia::render('Server/Create/ChooseServer', [
            'providersEnum' => CloudProviderEnums::asValue(),
            'selfServiceProviders' => collect(Providers::getProviders())->where('billing_service', BillingServices::SelfManagedHosting)->values()->toArray(),
            'xcloudManagedHosting' => collect(Providers::getProviders())->where('billing_service', BillingServices::xCloudManagedHosting)->values()->toArray(),
            'xcloudProviderHosting' => collect(Providers::getProviders())->where('billing_service', BillingServices::xCloudProviderHosting)->values()->toArray(),
            'billingActive' => team()->billingIsActive(),
            'showCardActiveNotice' => !team()?->activePlan?->support_manual_billing && !team()->billingIsActive(),
            'activePlan' => team()?->getPlan()?->value,
            'startingPrice' => team()->getStartingPrice(),
            'hasLtdPackage' => team()->hasLtdPackage(),
            'canCreateWithOther' => team()->activePlan && team()->canAccessWithBilling(BillingServices::SelfManagedHosting),
            'canCreateWithXcloud' => team()->activePlan && team()->canAccessWithBilling(BillingServices::xCloudManagedHosting),
            'availablePackages' => team()->getAvailablePackagesToUse(BillingServices::SelfManagedHosting, team()),
            'usedPackageQuantity' => team()->getUsedPackageQuantity(team()),
            'totalPackageQuantity' => team()->getTotalPackageQuantity(team()),
            'availablePackageQuantity' => team()->getAvailablePackageQuantity(team()),
        ]);
    }

    function addWithCustomProvider()
    {
        if (isWhiteLabel()){
            # If the user is a white label user, then redirect to the  server creation page
            return redirect()->route('white-label-server.create');
        }
        // Server create form for Any - different from others
        $this->authorize('create', Server::class);
        return Inertia::render('Server/Create/Any', [
            'keypair' => SshKeyPair::getSystemDefaulPublictKey(),
            'title' => 'Add a Custom Server',
            'database_type' => DatabaseEngine::MYSQL_8,
            'database_type_list' => DatabaseEngine::getVersionList(),
            'database_password' => DatabasePassword::randomPassword(),
            'tags' => Tag::associatedTags('servers'),
            'can_create_demo'=> user()->canCreateDemoServer(),
        ]);
    }


    /**
     * @throws AuthorizationException|ValidationException
     */
    public function viewChooseCredentials($cloudProviderSlug): Response | RedirectResponse
    {
        abort_if(isWhiteLabel(),404);
//        getting slug from the url and then using that slug to determine the targeted provider's service class
        $this->authorize('create', CloudProvider::class);

//        NOTE: I think we should allow user to create provider credentials even if they don't have server creation access, so commenting it out
//        $this->authorize('create', Server::class);

        $validator = Validator::make(['cloudProviderSlug' => $cloudProviderSlug], [
            'cloudProviderSlug' => ['required', Rule::in(CloudProviderEnums::asSlugValues())]
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $validatedSlug = $validator->validated()['cloudProviderSlug'];
        $cloudProviderEnum = CloudProviderEnums::fromSlug($validatedSlug);

        $service = CloudProviderServiceFactory::getService($cloudProviderEnum);
        return $service->viewChooseCredentials();
    }

    /**
     * @throws AuthorizationException
     * @throws ValidationException
     */
    public function createWithProvider($cloudProvider): Response | RedirectResponse
    {
        abort_if(isWhiteLabel(),404);

        // if $cloudProvider is numeric then it will be db id of CloudProvider model, otherwise enum of xcloud managed/provider

        // Check if the provided value is numeric, assuming IDs are numeric
        if (is_numeric($cloudProvider)) {
            $cloudProvider = CloudProvider::find($cloudProvider);
            if (!$cloudProvider) {
                abort(404, 'Cloud provider not found.');
            }
            $this->authorize('view', $cloudProvider);
            $this->authorize('create', Server::class);
            $service = CloudProviderServiceFactory::getService($cloudProvider->provider);

            return $service->viewCreateServer($cloudProvider);
        }

        // If the provider is not numeric, treat it as an enum slug
        $this->authorize('create', Server::class);
        $validator = Validator::make(['cloudProviderSlug' => $cloudProvider], [
            'cloudProviderSlug' => ['required', Rule::in([CloudProviderEnums::XCLOUD->asSlug(), CloudProviderEnums::XCLOUD_PROVIDER->asSlug(), CloudProviderEnums::GCP->asSlug()])],
        ]);

        if ($validator->fails()) {
            abort(404, 'Invalid cloud provider slug.');
        }

        $validatedSlug = $validator->validated()['cloudProviderSlug'];
        $cloudProviderEnum = CloudProviderEnums::fromSlug($validatedSlug);
        $service = CloudProviderServiceFactory::getService($cloudProviderEnum);

        return $service->viewCreateServer(new CloudProvider()); // blank cloud provider object to maintain the structure
    }

    public function whiteLabelServer()
    {
        abort_unless(isWhiteLabel(), 404);
        $serverTypeRepository = new XCloudVultrServerTypeRepository();
        $products =  currentWhiteLabel()->activeProducts()->with('dependents')?->latest()?->get();
        $serverDetails = $serverTypeRepository->getXCloudServerTypes($products);
        $tags = Tag::associatedTags('servers')->pluck('label', 'value')->toArray();
        $billingActive = team()->billingIsActive();

        $availableProducts = (new Server)->getAvailableProductsToUse(BillingServices::ManagedHosting, $serverDetails['sizes']->pluck('*.slug')->flatten()->toArray(), team());

        if (request()->has('slug') && $availableProducts->where('slug', request('slug'))->isEmpty()) {
            $selectedSize = $serverDetails['sizes']->flatten(1)->first(fn($item) => $item['slug'] === request('slug'));
        } else{
            $selectedSize = $serverDetails['sizes']->flatten(1)->first(fn($item) => $products->contains('slug', $item['slug']));
        }

        // Todo should be moved to Bashar bro inerfaces

        return inertia('Server/Create/WhiteLabel', [
            'supportManualBilling' => (bool) team()?->activePlan?->support_manual_billing,
            'sizes' => $serverDetails['sizes']->toArray(),
            'regions' => $serverDetails['regions'],
            'provider' => '',
            'selectedSize' => $selectedSize,
            'tagList' => $tags,
            'can_create_demo' => user()->canCreateDemoServer(),
            'billingActive' => $billingActive,
            'availableProducts' => $availableProducts->pluck('slug'),
            'database_type' => DatabaseEngine::MYSQL_8,
            'database_type_list' => DatabaseEngine::getVersionList(),
            'purchasedProducts' => $availableProducts
        ]);
    }

}
