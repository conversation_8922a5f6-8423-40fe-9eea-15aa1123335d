<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class ArchiveServerController extends Controller
{
    public function index(Request $request)
    {
         return Inertia::render('Server/Index', [
            'servers' => $request->user()
                ->servers()
                ->withCount('sites')
                ->onlyTrashed()
                ->paginate(10),
        ]);
    }
}
