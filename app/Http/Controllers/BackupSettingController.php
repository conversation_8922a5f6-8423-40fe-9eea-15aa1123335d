<?php

namespace App\Http\Controllers;

use App\Models\BackupSetting;
use Illuminate\Http\Request;

class BackupSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\BackupSetting  $backupSetting
     * @return \Illuminate\Http\Response
     */
    public function show(BackupSetting $backupSetting)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\BackupSetting  $backupSetting
     * @return \Illuminate\Http\Response
     */
    public function edit(BackupSetting $backupSetting)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BackupSetting  $backupSetting
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, BackupSetting $backupSetting)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\BackupSetting  $backupSetting
     * @return \Illuminate\Http\Response
     */
    public function destroy(BackupSetting $backupSetting)
    {
        //
    }
}
