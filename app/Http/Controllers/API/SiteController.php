<?php

namespace App\Http\Controllers\API;

use App\Callbacks\ChecksumVerificationUpdated;
use App\Callbacks\SiteBackupDeleted;
use App\Callbacks\SitePullActionCompleted;
use App\Enums\BackupVersion;
use App\Enums\CustomNginxEnum;
use App\Enums\DeploymentLogsStatusEnum;
use App\Enums\DomainChallengeStatusEnum;
use App\Enums\EmailProvider;
use App\Enums\PatchstackVulnerabilityStatus;
use App\Enums\ServerStatus;
use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Enums\XcloudBilling\PlansEnum;
use App\Events\DomainChallengeStatusChanged;
use App\Events\SiteBackupStateUpdate;
use App\Events\SiteSSLStatusChanged;
use App\Events\SiteStagingPullPushStatusChanged;
use App\Http\Controllers\Controller;
use App\Http\Requests\BackupSettingsRequest;
use App\Http\Requests\DeployStagingRequest;
use App\Http\Requests\SiteCreationRequest;
use App\Http\Requests\SitePlayGroundCreationRequest;
use App\Http\Requests\OneClickAppCreationRequest;
use App\Http\Requests\StackConfigureUpdateRequest;
use App\Http\Requests\StoreIpAddressRequest;
use App\Http\Requests\UpdateIpAddressRequest;
use App\Jobs\CreateStagingEnvUsingCloudflare;
use App\Jobs\SiteDeploymentJob;
use App\Jobs\Server\SyncIpWhitelistBlackListJob;
use App\Jobs\Site\ActiveRedisObjectCacheProJob;
use App\Jobs\Site\BackupRestore;
use App\Jobs\Site\CreateNginxConf;
use App\Jobs\Site\DeleteSiteJob;
use App\Jobs\Site\EnablePatchstackVulnerabilityJob;
use App\Jobs\Site\EnableSsl;
use App\Jobs\Site\HandleSiteIndexingJob;
use App\Jobs\Site\InstallMonitoring;
use App\Jobs\Site\InstallPhpMyAdmin;
use App\Jobs\Site\InstallN8n;
use App\Jobs\Site\InstallUptimeKuma;
use App\Jobs\Site\RescueSiteJob;
use App\Jobs\Site\PushToProductionJob;
use App\Jobs\Site\RemoveMonitoring;
use App\Jobs\Site\SendTestMailJob;
use App\Jobs\Site\SiteBackupNow;
use App\Jobs\Site\SiteSFTPToggleJob;
use App\Jobs\Site\SiteSMTPSettings;
use App\Jobs\Site\SiteStateUpdateJob;
use App\Jobs\Site\TakeSiteBackupJob;
use App\Jobs\Site\TakeInstantSiteBackupJob;
use App\Jobs\Site\UpdateBackupSettings;
use App\Jobs\Site\UpdateDomainName;
use App\Jobs\Site\UpdateSitePhpVersion;
use App\Jobs\SiteClone;
use App\Models\AutoSiteClone;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Bill;
use App\Models\CustomNginx;
use App\Models\DeploymentLog;
use App\Models\DomainChallenge;
use App\Models\GeneralInvoice;
use App\Models\PhpVersion;
use App\Models\Product;
use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Models\StorageProvider;
use App\Models\Task;
use App\Models\User;
use App\Models\WordfenceVulnerability;
use App\Rules\DatabasePassword;
use App\Rules\DomainComparisonRule;
use App\Rules\SiteNameRule;
use App\Scripts\InlineScript;
use App\Scripts\PurgeObjectCache;
use App\Scripts\PurgeObjectCachePro;
use App\Scripts\PurgeRedisObjectCache;
use App\Scripts\Server\SyncIpWhitelistBlackList;
use App\Scripts\Site\ActiveRedisObjectCacheProScript;
use App\Scripts\Site\DeactivateRedisObjectCacheProScript;
use App\Scripts\Site\DeleteBackup;
use App\Scripts\Site\DeleteCustomNginxConfig;
use App\Scripts\Site\GetDisableSiteHtml;
use App\Scripts\Site\GetDomainChallengeLog;
use App\Scripts\Site\GetSiteIndexingSettings;
use App\Scripts\Site\GitConnectionCheck;
use App\Scripts\Site\HandleSiteIndexingScript;
use App\Scripts\Site\Install7GFirewall;
use App\Scripts\Site\Install8GFirewall;
use App\Scripts\Site\InstallAIBotBlocker;
use App\Scripts\Site\InstallWpFail2Ban;
use App\Scripts\Site\PullFromProductionScript;
use App\Scripts\Site\PullSiteUpdates;
use App\Scripts\Site\PushToProductionScript;
use App\Scripts\Site\Remove7GFirewall;
use App\Scripts\Site\Remove8GFirewall;
use App\Scripts\Site\RemoveAIBotBlocker;
use App\Scripts\Site\RemoveWpFail2Ban;
use App\Scripts\Site\RepairN8nNode;
use App\Scripts\Site\RestartPm2Process;
use App\Scripts\Site\RestoreBackup;
use App\Scripts\Site\RestoreIncrementalBackup;
use App\Scripts\Site\RunAndDebugNginxConfig;
use App\Scripts\Site\DomainChallengeScript;
use App\Scripts\Site\WpChecksumScan;
use App\Scripts\TestDatabaseConnection;
use App\Services\Database\DatabaseNameGenerator;
use App\Services\Database\DatabaseNameGenerator as Generator;
use App\Services\Database\DatabaseProvider;
use App\Services\DNS\DnsRecordChecker;
use App\Services\Integrations\ElasticEmailService;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use App\Services\SitePlayGround\RequestToJoinTeam;
use App\Services\Vulnerability\PatchstackService;
use App\Services\WordPress\WordPressVersion;
use App\Services\XcloudProduct\ActiveOffers;
use App\Validator\AliasesValidation;
use App\Validator\DnsValidation;
use App\Validator\Domain;
use App\Validator\ReservedDomainValidationForAdditionalDomains;
use App\Validator\TagValidation;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Bus\Batch;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Laravel\Jetstream\Contracts\AddsTeamMembers;
use PHPUnit\Framework\Error;
use Throwable;
use function Pest\Laravel\options;

class SiteController extends Controller
{
    public function index(Server $server)
    {
        $this->authorize('viewSite', $server);

        return $server->sites;
    }

    public function storeToPlayground(SitePlayGroundCreationRequest $request)
    {
        //abort(403, 'Playground is unavailable temporarily.');

        $playGroundUser = User::where('email', RequestToJoinTeam::playgroundTeamOwnerUserEmail)->first();

        if (!$playGroundUser) {
            Log::error("Playground Site Log - Playground user not found");
            throw new AuthorizationException('You are not authorized to create a site on a playground server.');
        }

        $user = user();

        $server = $playGroundUser->servers()
            ->whereIn('status', ServerStatus::successStates())
            ->inRandomOrder()
            ->first();

        if (!$server) {
            Log::error("Playground Site Log - No Playground server found");

            throw new AuthorizationException('You are not authorized to create a site on a playground server.');
        }


        $siteData = $request->getSiteData();

        Log::info("Playground Site Log - Data ", $siteData);

        if ($request->get('domain_active_on_cloudflare')) {
            $siteData['ssl_provider'] = SslCertificate::PROVIDER_CLOUDFLARE;
            Log::info("Playground Site Log - SSL provider set to cloudflare");
        }

        if ($request->input('database_provider') === DatabaseProvider::CUSTOM) {
            Log::info("Playground Site Log - Testing custom database connection");
            $testCustomDatabaseConnection = $server->runInline(new TestDatabaseConnection(
                $request->input('database_host'),
                $request->input('database_port'),
                $request->input('database_name'),
                $request->input('database_user'),
                $request->input('database_password')
            ))->successful();

            if (!$testCustomDatabaseConnection) {
                Log::error("Playground Site Log - Custom database connection test failed");
                throw ValidationException::withMessages([
                    'db_connection_error' => 'Unable to connect to database. Please check your credentials.',
                ]);
            }
        }

        $site = $server->sites()->create($siteData);

        // If not already exitst in team
        if ($playGroundUser->personalTeam()->users()->where('email', $user->email)->doesntExist()) {
            $site->server->team->users()->attach(
                auth()->user(), ['role' => 'site-admin', 'permissions' => [
//                    "permissions" => [
                        "site:delete",
                        "site:manage-database",
                        "site:manage-update",
                        "site:manage-monitoring",
                        "site:manage-redirects",
                        "site:manage-email-providers",
                        "site:manage-caching",
                        "site:manage-events",
                        "site:manage-authentication",
                        "site:settings",
                        "site:access-magic-login",
                        "site:custom-magic-login",
                        "site:manage-file-manager",
                        "server:cron-job",
                        "site:manage-custom-nginx",
                        "site:blueprint-manage",
                        "site:disable",
                        "site:deploy-staging",
                        "site:manage-staging",
                        "site:custom-command-runner",
                        "site:vulnerability-scan"
//                    ],
//                    "site_access" => "choose",
//                    "selected_sites" => [$site->id]
                ]]
            );
        }


        if ($site) {
            Log::info("Playground Site Log - Site {$site->id} created successfully");
        } else {
            Log::error("Playground Site Log - Site creation failed");
        }

        // update site domain with staging site name
        $domainName = $request->input('name');
        $site->updateDomain($domainName);

        Log::info("Playground Site Log - Domain name updated to {$domainName}");

        // update with new domain name
        $site->update([
            'ssl_provider' => SslCertificate::PROVIDER_STAGING,
            'environment' => Site::DEMO,
            'meta->is_playground' => true
        ]);

        Log::info("Playground Site Log - SSL provider set to staging");

        if ($site->ssl_provider && $site->ssl_provider !== SslCertificate::PROVIDER_STAGING && $site->ssl_provider !== SslCertificate::PROVIDER_CLOUDFLARE) {
            $site->sslCertificates()->create($request->sslCertificate());
            Log::info("Playground Site Log - SSL certificate created");
        }

        if ($request->get('domain_active_on_cloudflare')) {
            Log::info("Playground Site Log - Domain active on cloudflare");
            $site->update([
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        if ($request->has('tags')) {
            Log::info("Playground Site Log - Tags found");
            $site->syncTags($request->get('tags'));
        }

        if ($request->boolean('enable_redis_object_caching')) {
            Log::info("Playground Site Log - Redis object caching enabled");
            $site->update([
                'meta->has_redis_object_caching' => true,
            ]);
        }

        if ($request->boolean('enable_full_page_cache')) {
            Log::info("Playground Site Log - Full page caching enabled");
            $site->update([
                'meta->has_fullpage_caching' => true,
            ]);
        }

        if ($request->filled('deploy_script')) {
            Log::info("Playground Site Log - Deploy script found");
            $site->saveMeta('deploy_script', $request->input('deploy_script'));
        }

        RequestToJoinTeam::toUser(user())->forSite($site)->send();

        Log::info("Playground Site Log - Invite sent to user {$user->email} for site {$site->id}");

        $site->provision();

        Log::info("Playground Site Log - Site {$site->id} provisioned successfully");

        if ($request->expectsJson()) {
            return response()->json($site, 201);
        }

        return to_route('site.playground.progress', [$site, 'switchToTeam' => $playGroundUser->personalTeam()->id])->with('flash', [
            'message' => 'Site is creating in your playground.',
            'body' => 'Provision script initialized. It may take upto 3 minutes to complete.'
        ]);
    }

    /**
     * @throws \Exception
     */
    public function updateDomain(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageDomain', $site);

        // Convert the array into a string suitable for the regular expression
        $reservedDomainsPattern = implode('|', array_map('preg_quote', getReservedDomains()));

        $rules = [
            'name' => [
                'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/',
                ...SiteNameRule::rules(serverId: $server->id, ignoreSiteId: $site->id),
//                new DomainComparisonRule($request->get('previousDomain'))
            ],
            'additional_domains' => [
                'nullable',
                new AliasesValidation,
                new ReservedDomainValidationForAdditionalDomains
            ],
        ];

        // skip dns checking for cloudflare managed dns
        if (!$request->get('domain_active_on_cloudflare')) {
            $rules['name'][] = new DnsValidation($site);
            $rules['additional_domains'][] = new DnsValidation($site);
        }

        $validated = $request->validate($rules);

        if ($request->get('domain_active_on_cloudflare')){
            $site->update([
                'ssl_provider' => canGenerateSslCertificateOnCloudflare($request->get('name'))
                    ? SslCertificate::PROVIDER_CLOUDFLARE : SslCertificate::PROVIDER_XCLOUD,
                'environment' => Site::PRODUCTION,
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        $jobs = [];

        if (!($request->get('name') === $site->name)) {
            $site->saveMeta('script_initiate_by', auth()->id());

            $jobs[] = new RemoveMonitoring(site: $site);

            if (!empty($validated['additional_domains'])) {
                $site->update([
                    'additional_domains' => $validated['additional_domains'],
                ]);
            }

            $jobs[] = new UpdateDomainName(site: $site, newDomainName: $request->get('name'));

        }else{
            if (!empty($validated['additional_domains'])) {
                $site->update([
                    'additional_domains' => $validated['additional_domains'],
                ]);

                if ($site->getMeta('cloudflare_integration.domain_active_on_cloudflare')) {
                    $jobs[] = new CreateStagingEnvUsingCloudflare($site);
                }
                $jobs[] = new EnableSsl($site, false);
                $jobs[] = new CreateNginxConf($site);
            }
        }

        if ($site->backupSettings()->exists()){
            $site->backupSettings()->update(['user_id' => auth()->id()]);
        }

        Bus::chain($jobs)
            ->catch(fn (Throwable $e) => $site?->markAsDomainUpdateFailure($e->getMessage()))
            ->dispatch();

        if ($request->expectsJson()) {
            return response()->json($site);
        }

        return back()->with('flash', [
            'message' => 'Domain will be updated soon.',
            'body' => 'Changes will be applied soon.'
        ]);
    }

    public function provision(Server $server, Site $site): JsonResponse
    {
        $this->authorize('addSite', $server);

        if (!$site->isNew()) {
            throw ValidationException::withMessages([
                'status' => ['Provision script is only meant to run once per server.'],
            ]);
        }

        $site->provision();

        return response()->json([
            'message' => 'Provision script initialized. It may take upto 3 mins'
        ]);
    }

    public function store(Server $server, SiteCreationRequest $request)
    {
        $this->authorize('addSite', $server);
        $siteData = $request->getSiteData();
        if ($request->get('domain_active_on_cloudflare')) {
            if (canGenerateSslCertificateOnCloudflare($request->get('name'))) {
                $siteData['ssl_provider'] = SslCertificate::PROVIDER_CLOUDFLARE;
            } else {
                $siteData['ssl_provider'] = SslCertificate::PROVIDER_XCLOUD; // অক্ষম cloudflare can't handle ssl for this multi level domain
            }
        }

        if ($request->input('database_provider') === DatabaseProvider::CUSTOM) {
            $testCustomDatabaseConnection = $server->runInline(new TestDatabaseConnection(
                $request->input('database_host'),
                $request->input('database_port'),
                $request->input('database_name'),
                $request->input('database_user'),
                $request->input('database_password')
            ))->successful();

            if (!$testCustomDatabaseConnection) {
                throw ValidationException::withMessages([
                    'db_connection_error' => 'Unable to connect to database. Please check your credentials.',
                ]);
            }
        }

        $site = $server->sites()->create($siteData);

        // update site domain with staging site name
        if ($request->get('domain_parking_method') === 'staging_env') {
            // $domainName = $site->generateStagingSiteName(prefix: DatabaseNameGenerator::generateDomain($site->title));
            $domainName = $request->input('name');
            $site->updateDomain($domainName);

            // update with new domain name
            $site->update([
                'ssl_provider' => $request->get('domain_parking_method') === 'staging_env'
                    ? SslCertificate::PROVIDER_STAGING
                    : null,
                'environment' => $request->get('domain_parking_method') === 'staging_env' ? Site::DEMO : Site::PRODUCTION,
            ]);
        }

        if ($site->ssl_provider && $site->ssl_provider !== SslCertificate::PROVIDER_STAGING && $site->ssl_provider !== SslCertificate::PROVIDER_CLOUDFLARE) {
            $site->sslCertificates()->create($request->sslCertificate());
        }

        if ($request->get('domain_active_on_cloudflare')) {
            $site->update([
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        if ($request->get('use_xcloud_default_email_provider')) {
            $site->update([
                'meta->useXcloudDefaultEmailProvider' => $request->get('use_xcloud_default_email_provider') ?? true,
            ]);
        }

        if ($request->has('tags')) {
            $site->syncTags($request->get('tags'));
        }

        if ($request->boolean('enable_redis_object_caching')) {
            $site->update([
                'meta->has_redis_object_caching' => true,
            ]);
        }
        if ($request->boolean('enable_full_page_cache')) {
            $site->update([
                'meta->has_fullpage_caching' => true,
            ]);
        }

        if ($request->filled('deploy_script')) {
            $site->saveMeta('deploy_script', $request->input('deploy_script'));
        }

        $site->saveMeta('disable_search_engine_visibility', $request->get('disable_search_engine_visibility'));

        $site->provision();

        if ($request->expectsJson()) {
            return response()->json($site, 201);
        }

        return to_route('site.show', [$server, $site])->with('flash', [
            'message' => 'Site is creating in your server.',
            'body' => 'Provision script initialized. It may take upto 3 minutes to complete.'
        ]);
    }

    public function show(Server $server, Site $site): JsonResponse
    {
        $this->authorize('viewSite', $server);
        return response()->json($site->load('server'));
    }

    public function verifyDns(Server $server, Request $request)
    {
        $validated = $request->validate([
            'domain' => ['required', new Domain],
            'additional_domains' => ['nullable', new AliasesValidation]
        ]);

        // dns verification for primary domain
        $isDnsUpdated = $server->hasDnsRecord($validated['domain']);

        // dns verification for additional domains
        $additionalDomains = [];
        if (!empty(Arr::get($validated, 'additional_domains'))) {
            $additionalDomains = Arr::get($validated, 'additional_domains');
            foreach ($additionalDomains as $key => $additional_domain) {
                if(isset($additional_domain['value'])){
                    $additionalDomains[$key]['validDns'] = $server->hasDnsRecord($additional_domain['value']);
                }
            }
        }

        $invalidAdditionalDomains = collect($additionalDomains)->filter(function ($additional_domain) {
            return isset($additional_domain['value']) && $additional_domain['validDns'] === false;
        })->values();

        return response()->json([
            'domain' => $validated['domain'],
            'success' => $isDnsUpdated,
            'additionalDomains' => $additionalDomains ?? [],
            'type' => $isDnsUpdated && $invalidAdditionalDomains->isEmpty() ? 'success' : 'error',
            'message' => $isDnsUpdated && $invalidAdditionalDomains->isEmpty() ? 'DNS record verified successfully!' : 'DNS Verification Has Failed.',
            'body' => $isDnsUpdated && $invalidAdditionalDomains->isEmpty() ? '' : 'Unfortunately your domain is not pointing at your server yet. Please try again later or skip this step for now.',
        ]);
    }

    public function verifyDnsForEmail(Server $server, Site $site, Request $request)
    {
        $validated = $request->validate([
            'domain' => ['required', new Domain],
        ]);

        $elasticEmailService = new ElasticEmailService($site->team()->getElasticEmailSubAccountApiKey());

        $dkimRecordValid = $spfRecordValid = $cnameRecordValid = $dmarcRecordValid = false;
        if ($elasticEmailService->verifyDomainSpfRecord($validated['domain'])) {
            $spfRecordValid = true;
        }

        if ($elasticEmailService->verifyDomainDkimRecord($validated['domain'])) {
            $dkimRecordValid = true;
        }

        if ($elasticEmailService->verifyDomainCnameRecord($validated['domain'])) {
            $cnameRecordValid = true;
        }

        if ($elasticEmailService->verifyDomainDmarcRecord($validated['domain'])) {
            $dmarcRecordValid = true;
        }

        if(!$cnameRecordValid){
            // try to add the domain on elastic email
            $response = $elasticEmailService->addDomain($validated['domain']);

            if (Arr::get($response, 'success')) {
                // set default email for the domain
                $response = (new ElasticEmailService($site->team()->getElasticEmailSubAccountApiKey()))
                    ->setDefaultEmailForDomain('info@' . $request->get('domain'));

                $cnameRecordValid = true;
            }
        }

        if ($spfRecordValid && $dkimRecordValid && $cnameRecordValid && $dmarcRecordValid) {
            return response()->json([
                'domain' => $validated['domain'],
                'success' => true,
                'dkimRecordValid' => $dkimRecordValid,
                'spfRecordValid' => $spfRecordValid,
                'cnameRecordValid' => $cnameRecordValid,
                'dmarcRecordValid' => $dmarcRecordValid,
                'type' => 'success',
                'message' => 'DNS record verified successfully!',
                'body' => '',
            ]);
        } else {
            return response()->json([
                'domain' => $validated['domain'],
                'success' => false,
                'dkimRecordValid' => $dkimRecordValid,
                'spfRecordValid' => $spfRecordValid,
                'cnameRecordValid' => $cnameRecordValid,
                'dmarcRecordValid' => $dmarcRecordValid,
                'type' => 'error',
                'message' => 'Domain Verification Has Failed.',
                'body' => 'Please add the required records to your domain.(It may take some time to reflect)',
            ]);
        }
    }

    public function purgeObjectCache(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageCache', $site);

        if ($site->getMeta('cloudflare_edge_cache.enable')) {
            \App\Jobs\Cloudflare\PurgeCloudflareEdgeCacheJob::dispatchForSite($site);
        }

        $task = $site->runInline(new PurgeObjectCache($server, $site));

        if ($request->expectsJson()) {
            return response()->json($site);
        }

        if ($task->successful()) {
            return back()->with('flash', [
                'message' => 'Cache Purged Successfully.',
                'body' => 'Changes will be applied soon.'
            ]);
        }

        return back()->with('flash', [
            'type' => 'error',
            'message' => 'Failed to Purge Cache.',
        ]);
    }

    public function purgeRedisObjectCache(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageCache', $site);

        $task = $site->runInline(new PurgeRedisObjectCache($server, $site));

        if ($request->expectsJson()) {
            return response()->json($site);
        }

        if ($task->successful()) {
            return back()->with('flash', [
                'message' => 'Redis Object Cache Purged Successfully.',
                'body' => 'Changes will be applied soon.'
            ]);
        }

        return back()->with('flash', [
            'type' => 'error',
            'message' => 'Failed to Purge Redis Object Cache.',
        ]);
    }


    /**
     * Update the site's tags
     *
     * @throws AuthorizationException
     */
    public function updateTags(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);

        $request->validate([
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
        ], [
            ...TagValidation::tagMessage(),
        ]);

        $site->syncTags($request->get('tags'));

        return redirect()->back()->with('flash', ['message' => 'Site Tags updated successfully', 'type' => 'success']);
    }

    public function getPhpVersionSettings(Server $server, Site $site)
    {
        try{
            return $site->getPhpVersionSettings();
        }catch (\Exception $e){
            return response()->json(['message' => $e->getMessage(), 'type' => 'error']);
        }
    }

    /**
     * Update the site's tags
     *
     * @throws AuthorizationException
     */
    public function updatePhpVersion(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);

        $rules = [
            'php_version' => ['required', PhpVersion::asRule(server: $server)],
            'max_execution_time' => 'required|numeric',
            'max_input_time' => 'required|numeric',
            'memory_limit' => 'required|numeric',
            'max_input_vars' => 'required|numeric',
            'post_max_size' => 'required|numeric',
            'upload_max_filesize' => 'required|numeric',
            'session_gc_maxlifetime' => 'required|numeric',
            'pm_max_children' => 'nullable|numeric|min:3',
        ];

        $messages = [
            'upload_max_filesize.lte' => 'The Max File Upload Size must be less than or equal to the Post Max Size.',
        ];

        $data = $request->validate($rules, $messages);

        if ($data['post_max_size'] > 0) {
            $rules['upload_max_filesize'] .= '|lte:post_max_size';
            $data = $request->validate($rules, $messages);
        }

        $data['session.gc_maxlifetime'] = $data['session_gc_maxlifetime'];
        $data['pm.max_children'] = $data['pm_max_children'];

        $envVars = collect($data)
            ->only(['upload_max_filesize', 'max_execution_time','max_input_time', 'memory_limit', 'max_input_vars','post_max_size', 'session.gc_maxlifetime', 'pm.max_children'])
            ->map(function ($value, $key) {
                if (in_array($key, ['upload_max_filesize', 'memory_limit','post_max_size'])) {
                    return $value . 'M';
                }
                return $value;
            })
            ->filter(function ($value, $key) {
                if (str_starts_with($key, 'pm.') && is_null($value)) { // ols server won't have pm. settings
                    return false;
                }
                return true;
            })
            ->toArray();

        $existingVersion = $site->php_version;

        $site->update([
            'php_version' => $request->get('php_version'),
        ]);

        $task = Task::create([
            'name' => "Updating PHP Version from {$existingVersion} to {$request->get('php_version')}",
            'site_id' => $site->id,
            'server_id' => $server->id,
            'team_id' => $server->team_id,
            'status' => 'pending',
        ]);

        UpdateSitePhpVersion::dispatch($task, $site, $existingVersion, $envVars);

        return redirect()->back()->with('flash', [
            'message' => 'PHP Version updated successfully',
            'body' => 'It will take a while to take effect',
            'type' => 'success'
        ]);
    }

    public function updateWpCron(Server $server, Site $site, Request $request)
    {
        $this->authorize('wpConfig', $site);
        $request->validate([
            'enable_wp_cron' => ['required', 'boolean'],
            'wp_cron_interval' => ['required_if:wp_cron,1', Rule::in(array_keys(Site::WP_CRON_INTERVALS))],
        ]);
        // update wp_cron and wp_cron_interval on site meta data
        $site->update([
            'meta->enable_wp_cron' => $request->get('enable_wp_cron'),
            'meta->wp_cron_interval' => $request->get('wp_cron_interval'),
        ]);

        $site->installWpCronJob();

        return redirect()->back()->with('flash', [
            'message' => 'WP Cron updated successfully',
            'body' => 'It will take a while to take effect',
            'type' => 'success'
        ]);
    }

    public function getWpCron(Server $server, Site $site, Request $request)
    {
        $this->authorize('wpConfig', $site);
        return response()->json([
            'wp_cron' => $site->readWpOption('DISABLE_WP_CRON',false),
            'type' => 'success'
        ]);
    }

    public function getSiteIndexingSettings(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);

        return response()->json([
            'searchEngineVisibility' => (bool) !$site->runInline(new GetSiteIndexingSettings($site))->output,
            'type' => 'success'
        ]);
    }

    public function updateSiteIndexingSettings(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);

        $request->validate([
            'search_engine_visibility' => 'required|boolean',
        ]);

        $site->saveMeta('disable_search_engine_visibility', $request->get('search_engine_visibility'));

        $site->runInline(new HandleSiteIndexingScript($site));

        return redirect()->back()->with('flash', [
            'message' => 'Search Engine Visibility updated successfully',
            'type' => 'success'
        ]);
    }

    public function getDisableHtml(Server $server, Site $site, Request $request)
    {
        $this->authorize('disable', $site);
        $task = $site->runInline(new GetDisableSiteHtml($site));
        return response()->json([
            'html' => $task->output,
            'type' => 'success'
        ]);
    }

    /**
     * Update the site's tags
     *
     * @throws AuthorizationException
     */
    public function updateEmailProvider(Server $server, Site $site, Request $request)
    {
        $this->authorize('emailProvider', $site);

        $request->validate([
            'email_provider' => ['required'],
            'from_name' => ['required_if:email_provider,', 'required_with:email_provider'],
            'from_email' => ['required_if:email_provider,', 'required_with:email_provider', ($request->filled('email_provider') ? 'email' : '')],
            'domainTypeForEmailProvider' => ['in:xcloud_managed_domain,own_managed_domain'],
            'domain' => ['required_if:domainTypeForEmailProvider,own_managed_domain'],
        ]);

        if ($request->get('domainTypeForEmailProvider') === 'own_managed_domain') {
            // add the domain on elastic email subaccount
            $response = (new ElasticEmailService($site->team()->getElasticEmailSubAccountApiKey()))
                ->addDomain($request->get('domain'));

            if (Arr::get($response, 'success')) {
                // set default email for the domain
                $response = (new ElasticEmailService($site->team()->getElasticEmailSubAccountApiKey()))
                    ->setDefaultEmailForDomain('info@' . $request->get('domain'));
            }
        }

        $site->update([
            'email_provider_data' => [
                'from_name' => $request->get('from_name'),
                'from_email' => $request->get('from_email'),
                'domain_type' => $request->get('domainTypeForEmailProvider'),
                'domain' => $request->get('domainTypeForEmailProvider') === 'own_managed_domain'
                    ? $request->get('domain')
                    : null,
            ],
        ]);

        $emailProvider = team()->emailProviders()->find(Arr::get($request->get('email_provider'), 'id'));
        $site->emailProvider()->associate($emailProvider)->save();

        $providerData = [
            'from_name' => $request->get('from_name'),
            'from_email' => $request->get('from_email')
        ];

        if ($emailProvider->provider === EmailProvider::XCLOUD_EMAIL_PROVIDER) {
            $providerData['api_key'] = $site->team()->getElasticEmailSubAccountApiKey();

            if (empty($providerData['api_key'])) {
                return redirect()->back()->with('flash', [
                    'message' => 'Failed to add email provider.',
                    'body' => 'Your email provider was not setup properly. Please integrate your email provider again.',
                    'type' => 'error'
                ]);
            }
        }

        SiteSMTPSettings::dispatch(
            site: $site,
            emailProvider: $emailProvider,
            providerData: $providerData
        );

        return redirect()->back()->with('flash', [
            'message' => 'Your email provider will be updated shortly.',
            'body' => 'Please wait for the changes to take effect.',
            'type' => 'success'
        ]);
    }

    public function clearEmailProvider(Server $server, Site $site, Request $request)
    {
        $this->authorize('emailProvider', $site);

        $site->update([
            'email_provider_id' => null,
            'email_provider_data' => []
        ]);

        SiteSMTPSettings::dispatch(
            site: $site,
            emailProvider: null,
            providerData: []
        );

        return redirect()->back()->with('flash', [
            'message' => 'Your email provider will be removed from this site soon.',
            'body' => 'Please wait for the changes to take effect.',
            'type' => 'success'
        ]);
    }

    public function updateBackup(Server $server, Site $site, BackupSettingsRequest $request)
    {
        $this->authorize('settings', $site);
        if ($server->team->isFreePlan()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        try {
            $data = $request->validated();
            $data['time'] = data_get($data, 'time', '01:00 AM');
            $data['version'] = BackupVersion::VERSION_TWO;
            $data['user_id'] = user()->id;
            #currently we don't support files backup for laravel sites
            if(!$site->type->filesBackupSupported()){
                $data['files'] = false;
            }

            $backupSetting = BackupSetting::updateOrCreate([
                'site_id' => $site->id,
                'is_local' => $request->boolean('is_local')],
                $data
            );
            UpdateBackupSettings::dispatch(site: $site,backupSetting:  $backupSetting, user: user());;
            return redirect()->back()->with('flash', [
                'message' => 'Backup settings updated successfully',
                'body' => 'Changes will be applied soon.',
                'type' => 'success'
            ]);
        } catch (ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        }
    }

    public function backupNow(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        if (team()->isTrailMode()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        if ($request->boolean('is_local') && $site->backupSettings()->where(['is_local' => true])->doesntExist()) {
            BackupSetting::create([
                'site_id' => $site->id,
                'is_local' => true,
                'database' => true,
                'files' => true,
                'auto_backup' => false,
                'auto_delete' => false,
                'user_id' => user()->id,
                'status' => BackupSetting::PENDING,
                'version' => BackupVersion::VERSION_TWO,
            ]);
        }
        if (false === $request->boolean('is_local')) {
            $backupSetting = $site->backupSettings()->where(['is_local' => false])->first();
            if (!$backupSetting->storageProvider->hasStableConnection()) {
                return redirect()->back()->withErrors([
                    'storage_provider_id' => 'Storage provider connection failed, please check your storage provider connection.'
                ]);
            }
        }
        $site->backupNow($request->boolean('is_local'),$request->input('type'),user());
        return redirect()->back()->with('flash', [
            'message' => 'Site backup in progress',
            'body' => 'Changes will be applied soon.',
            'type' => 'success'
        ]);
    }

    public function restoreNow(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        if (team()->isTrailMode()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $data = $request->validate([
            'identifier' => 'required|array',
            'identifier.*' => 'required|array',
            'identifier.*.backup_setting_id' => 'required|exists:backup_settings,id',
            'identifier.*.file_name' => 'required|string|ends_with:sql,gz,zip,manifest',
            'identifier.*.is_sql' => 'required|boolean',
            'identifier.*.id' => 'required|exists:backup_files,id',
            'identifier.*.taken_size' => 'nullable|numeric',
            'identifier.*.date' => 'nullable',
            'restore_type' => 'required|string|in:both,database,file',
            'restore_method' => 'required_if:restore_type,files|string|in:remove,replace',
        ],[
            'identifier.*.file_name.ends_with' => 'File type is not supported',
        ]);
        $backup_setting_id = collect($data['identifier'])->pluck('backup_setting_id')->first();
        $backupSetting = BackupSetting::query()->find($backup_setting_id);
        $files = BackupFile::query()
            ->whereIn('id', collect($data['identifier'])->pluck('id'))
            ->when($data['restore_type'] === 'database', function ($query) {
                $query->where('is_sql', true);
            })
            ->when($data['restore_type'] === 'file', function ($query) {
                $query->where('is_sql', false);
            })->get();
        $storage_provider = StorageProvider::whereHas('backupSettings', fn($query) => $query->where('id', $backup_setting_id))->first();
        if (!$backupSetting->is_local && !$storage_provider?->hasStableConnection()) {
            return redirect()->back()->withErrors([
                'storage_provider_id' => 'Storage provider connection failed, please check your storage provider connection.'
            ]);
        }
        event(new SiteBackupStateUpdate($site));
        BackupRestore::dispatch(server: $server, site: $site, backupSetting: $backupSetting, files: $files, storageProvider: $storage_provider, restoreMethod: $data['restore_method']);

        return redirect()->back()->with('flash', [
            'message' => 'Restoring started successfully',
            'body' => 'Changes will be applied soon.',
            'type' => 'success'
        ]);
    }


    public function incrementalFiles(Server $server, Site $site, Request $request)
    {
        $request->validate([
            'file' => 'required|exists:backup_files,id',
        ]);
        $backupFile = BackupFile::with(['backupSetting'])->find($request->input('file'));
        return $backupFile->backupSetting->incrementalFiles($backupFile)->groupBy('date_format');
    }

    public function deleteBackup(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        if (team()->isTrailMode()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $this->validate($request,[
            'is_local' => 'required|boolean',
            'files' => 'required|array',
            'files.*.backup_setting_id' => 'required|exists:backup_settings,id',
            'files.*.file_name' => 'nullable|string',
            'files.*.is_sql' => 'required|boolean',
            'files.*.date' => 'required|string',
            'files.*.id' => 'required|exists:backup_files,id',
            'files.*.storage_provider_id' => 'nullable|required_if:is_local,0|exists:storage_providers,id',
        ]);
        $backupSettingId = $request->input('files.*.backup_setting_id');

        $backupSetting = BackupSetting::with(['storageProvider'])->where(['id' => $backupSettingId])->first();

        $backupFileIds = $request->input('files.*.id');
        $backupFiles = $backupSetting->backupFiles()->whereIn('id', $backupFileIds)->get();
        $files = $backupFiles->pluck('file_name')->toArray();
        $backupSetting->backupFiles->where('is_sql',true)->pluck('file_name')->toArray();
        $incrementalTimeStamps = [];
        $incFile = $backupFiles->firstWhere('type',BackupFile::INCREMENTAL_FULL);
        # Check if the backup is incremental and not an SQL file
        if ($incFile) {
             $backupSetting
                ->incrementalFiles($incFile,fn($query)=> $query->where('is_sql',false))
                ->pluck('file_name')
                ->each(function($file) use (& $bucketFiles) {
                    str($file)
                        ->when(str($file)->startsWith('duplicity-full.'),function($str) use (&$bucketFiles){
                            preg_match('/(\d{8}T\d{6}Z)/',$str->toString(), $matches);
                            $bucketFiles[] =$matches;
                        })
                        ->when(str($file)->startsWith('duplicity-inc.'),function($str) use (&$bucketFiles){
                            preg_match('/(\d{8}T\d{6}Z)\.to\.(\d{8}T\d{6}Z)/', $str->toString(), $matches);
                            $bucketFiles[] =$matches;
                        });
                });
            $incrementalTimeStamps = collect($bucketFiles)->flatten()->unique()->toArray();
            $incrementalFiles = StorageProvider::s3IncrementalFiles($backupSetting,$site->backupDirName(),$incrementalTimeStamps);
            $otherFiles = $backupSetting
                            ->incrementalFiles($incFile,fn($query)=> $query->whereNotIn('file_name',$incrementalFiles))
                            ->pluck('file_name')
                            ->toArray();
            $files = array_merge($otherFiles,$incrementalFiles);
        }
        if ($backupSetting->is_local) {
            if ($incFile){
                $backupFiles =  $backupSetting->incrementalFiles($incFile);
                $files = $backupFiles->pluck('file_name')->toArray();
            }
            $site->runInBackground(new DeleteBackup($site, $server, $backupFiles,$incrementalTimeStamps), [
                'then' => [
                    new SiteBackupDeleted($site, $backupSetting, $files),
                ],
            ]);
        } else {
            # check if the storage provider connection is valid
            if ($backupSetting->storageProvider->hasStableConnection() && $backupSetting->storageProvider->deleteRemoteFiles($files, $site->backupDirName())) {
                $backupSetting->backupFiles()->whereIn('file_name', $files)->delete();
            } else {
                return redirect()->back()->with('flash', [
                    'type' => 'error',
                    'message' => 'Backup deletion failed',
                    'body' => 'Please check your storage provider connection.',
                ]);
            }
        }

        return redirect()->back()->with('flash', [
            'message' => 'Backup deleted successfully',
            'body' => 'Changes will be applied soon.',
            'type' => 'success',
        ]);

    }

    public function removeBackup(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        if (team()->isTrailMode()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $request->validate([
            'is_local' => 'required|boolean',
            'files' => 'required|array',
            'files.*' => 'required|array',
            'files.*.backup_setting_id' => 'required|exists:backup_settings,id',
            'files.*.id' => 'required|exists:backup_files,id',
        ]);
        $files = $request->input('files.*.id');
        $backup_setting_id = $request->input('files.0.backup_setting_id');
        $site->backupFiles()->where([
            'backup_files.backup_setting_id' => $backup_setting_id,
            'backup_files.status' => BackupSetting::FAILED,
        ])->whereIn('backup_files.id', $files)->delete();
        return redirect()->back()->with('flash', [
            'message' => 'Backup deleted successfully',
            'body' => 'Changes will be applied soon.',
            'type' => 'success'
        ],);
    }

    public function updateNote(Request $request, Server $server, Site $site)
    {
        $this->authorize('settings', $site);
        $data = $request->validate([
            'user_note' => 'nullable|string|max:255',
            'file_id' => 'required|exists:backup_files,id',
        ]);

        BackupFile::where('id', $data['file_id'])->update(['user_note' => $data['user_note']]);

        return redirect()->back()->with('flash', [
            'message' => 'Note updated successfully',
            'type' => 'success'
        ]);
    }

    public function hideMailAlert(Server $server, Site $site)
    {
        $site->update([
            'meta->mail_alert' => false,
        ]);
        return response()->json(['message' => 'Mail alert hidden successfully']);
    }

    public function ignoreChecksumAlert(Server $server, Site $site)
    {
        $this->authorize('monitors', $site);
        $site->update([
            'meta->ignore_checksum_alert_till' => now()->addMonth(),
        ]);
        return response()->json(['message' => 'Checksum alert hidden successfully']);
    }

    #send test mail to check smtp settings
    public function sendTestMail(Server $server, Site $site, Request $request)
    {
        return 'This feature is disabled for now on. User will test his email from fluentsmtp plugin.';

        $request->validate([
            'to' => 'required|email',
            'body' => 'required|string',
            'provider' => 'required|exists:email_providers,id',
        ]);
        dispatch(new SendTestMailJob($site, $request->get('to'), $request->get('body')));
        return redirect()->back()->with('flash', [
            'message' => 'Test email sending initiated successfully',
            'body' => 'Please check your inbox.',
            'type' => 'success'
        ]);
    }

    public function updateNginxConfig(Server $server, Site $site, StackConfigureUpdateRequest $request)
    {
        $this->authorize('editSiteSecuritySettings', $site);
        // Validate the request data
        $data = $request->validated();

        // Handle firewall installations/removals
        $firewallMappings = [
            'enable_7g_firewall' => [
                'install' => new Install7GFirewall($site),
                'remove' => new Remove7GFirewall($site),
            ],
            'enable_8g_firewall' => [
                'install' => new Install8GFirewall($site),
                'remove' => new Remove8GFirewall($site),
            ],
            'enable_ai_bot_blocker' => [
                'install' => new InstallAIBotBlocker($site),
                'remove' => new RemoveAIBotBlocker($site),
            ],
            'wp_fail2ban.enable' => [
                'install' => new InstallWpFail2Ban($site, $data['wp_fail2ban'] ?? []),
                'remove' => new RemoveWpFail2Ban($site),
            ],
        ];

        foreach ($firewallMappings as $key => $actions) {
            $isCurrentlyEnabled = $site->getMeta($key);
            $shouldBeEnabled = Arr::get($data, $key, false);

            $shouldRemove = $isCurrentlyEnabled && !$shouldBeEnabled;
            $shouldInstall = !$isCurrentlyEnabled && $shouldBeEnabled;

            if ($key === 'wp_fail2ban.enable'){
                // compare the wp_fail2ban array to see if any of the other wp_fail2ban settings changed
                if ($shouldBeEnabled && collect($site->getMeta('wp_fail2ban'))->sortKeys()->toArray() !== collect($data['wp_fail2ban'])->sortKeys()->toArray()) {
                    $shouldInstall = true;
                    $shouldRemove = false;
                }
            }

            if ($shouldRemove) {
                $site->runInline($actions['remove']);
            } elseif ($shouldInstall) {
                $site->runInline($actions['install']);
            }
        }

        // Update site meta
        $meta = array_merge($site->meta, [
            'enable_php_execution_on_upload_directory' => $data['enable_php_execution_on_upload_directory'] ?? false,
            'enable_xml_rpc' => $data['enable_xml_rpc'] ?? false,
            'modify_x_frame_options' => $data['modify_x_frame_options'] ?? false,
            'enable_custom_robots_txt' => $data['enable_custom_robots_txt'] ?? false,
            'x_frame_options' => $data['modify_x_frame_options'] ? strtoupper($data['x_frame_options']) : null,
            'enable_7g_firewall' => $data['enable_7g_firewall'] ?? false,
            'enable_8g_firewall' => $data['enable_8g_firewall'] ?? false,
            'enable_ai_bot_blocker' => $data['enable_ai_bot_blocker'] ?? false,
            'wp_fail2ban' => $data['wp_fail2ban'] ?? false,
        ]);

        #Remove all false values
        $meta = array_filter($meta);

        $site->update(['meta' => $meta]);

        // Regenerate Nginx configuration
        $site->regenerateNginxConf();

        $message = $server->stack->isNginx() ? 'Nginx Config updated successfully' : 'OpenLiteSpeed Config updated successfully';
        return back()->with('flash', [
            'message' => $message,
            'type' => 'success'
        ]);
    }

    public function updateSiteState(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        $data = $request->validate([
            'is_disable' => 'required|boolean',
            'disable_html' => 'required_if:is_disable,true|nullable|string',
            'disable_site_cron' => 'nullable|boolean'
        ]);
        $site->update([
            'is_disabled' => $data['is_disable'],
            'disabled_at' => $data['is_disable'] ? now() : null,
            'disabled_by' => $data['is_disable'] ? auth()->id() : null,
            'meta->disable_site_cron' =>  $data['is_disable'],
        ]);

        SiteStateUpdateJob::dispatch(site: $site,data: $data);

        return back()->with('flash', [
            'message' => 'Site state updated successfully',
            'type' => 'success'
        ]);
    }

    public function updateWpDebug(Server $server, Site $site, Request $request)
    {
        $this->authorize('wpConfig', $site);
        $request->validate(['enable_wp_debug' => 'required|boolean']);
        $site->updateWpDebug($request->get('enable_wp_debug'));
        return redirect()->back()->with('flash', [
            'message' => 'WP Debug updated successfully',
            'body' => 'Changes will be applied soon.',
            'type' => 'success'
        ]);
    }

    public function switchRegenerateConf(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        $request->validate(['disable_nginx_config_regeneration' => 'required|boolean']);
        $site->saveMeta('disable_nginx_config_regeneration', $request->boolean('disable_nginx_config_regeneration'));
        $message = $server->stack->isNginx() ? 'Nginx Config updated successfully' : 'OpenLiteSpeed Config updated successfully';
        if ($request->wantsJson()) {
            return response()->json(['message' => $message]);
        }
        return back()->with('flash', [
            'message' => $message,
            'type' => 'success'
        ]);

    }

    public function getNginxConfig(Server $server, Site $site)
    {
        $this->authorize('settings', $site);

        if (!$server->isConnected()) {
            return response()->json(['message' => 'Server is not connected'], 422);
        }

        return response()->json(['content' => $site->server->stack->isNginx() ? $site->readNginxConf() : $site->readHtaccess()]);
    }

    /**
     * Delete the site
     * @throws AuthorizationException
     */
    public function destroy(Server $server, Site $site, Request $request)
    {
        $this->authorize('delete', $site);

        // check back route
        $referer = $request->header('referer');

        if (!Str::contains($referer, route('site.show',[$site->server_id, $site->id]))) {
            session()->put('url.intended', $referer);
        }

        $data = $request->validate([
            'delete_files' => 'required|boolean',
            'delete_database' => 'required|boolean',
            'delete_user' => 'required|boolean',
            'delete_local_backups' => 'nullable|boolean',
            'delete_dns_record' => 'nullable|boolean',
        ]);

        $server->update(['meta->script_initiate_by' => auth()->id()]);

        $site->update([
            'status' => SiteStatus::DELETING,
        ]);

        $vulnerability_setting =  $server->vulnerabilitySetting()->first();
        if ($vulnerability_setting){
            $sites = collect($vulnerability_setting->sites);
            if($sites->contains($site->id)){
                $vulnerability_setting->update([
                    'sites' => $sites->reject(fn($value) => $site->id === $value)->values()->all()
                ]);
            }
        }

        //Remove Patchstack Data
        $patchastackVulnerability = $site->patchstackVulnerability()->first();
        if (!blank($patchastackVulnerability)) {
            $patchastackSite = app(PatchstackService::class)->patchstackSiteDelete($patchastackVulnerability->patchstack_site_id);
            if ($patchastackSite) {
                //Uninstall & delete Patchstack plugin
                $site->deletePatchstack($patchastackVulnerability);
                $site->patchstackVulnerabilitySite()->delete();
                $patchastackVulnerability->delete();
            }
        }

        if($data['delete_dns_record'] ?? false){
            $site->saveMeta('delete_dns_record', true);
        }
        // delete staging sites
        if($site->hasProductionEnvironment()){
            foreach ($site->stagingSites as $stagingSite){
                // delete site
                $stagingSite->update([
                    'status' => SiteStatus::DELETING,
                ]);
            }
        }

        // if no need to delete files or databases or database user, then just delete the site model
        #Disable this because we need to remove wp/backup cron and other things
        /*if (!$data['delete_files'] && !$data['delete_database'] && !($data['delete_user'] ?? false) && !($data['delete_local_backups'] ?? false)) {
            #remove backup settings files
            $site->backupFiles()->delete();
            $site->backupSettings()->delete();
            $site->delete();
            return redirect()->back()->with('flash', ['message' => 'Site deleted successfully', 'type' => 'success']);
        }*/

        $jobs = [];

        $jobs[] = new DeleteSiteJob($server, $site, $data['delete_files'], $data['delete_database'], $data['delete_user'], $data['delete_local_backups'] ?? false);

        foreach ($site->stagingSites as $stagingSite){
            $jobs[] = new DeleteSiteJob($server, $stagingSite, $data['delete_files'], $data['delete_database'], $data['delete_user'], $data['delete_local_backups'] ?? false);
        }

        Bus::chain($jobs)->dispatch();

        if ($request->expectsJson()) {
            return response()->json(['message' => 'Site delete initiated successfully']);
        }
        #if it has intended url then redirect to intended url
        if (session()->has('url.intended')) {
            return redirect()->intended()->with('flash', ['message' => 'Site delete initiated successfully', 'type' => 'success']);
        }

        return redirect()->route('server.show', $site->server_id)->with('flash', ['message' => 'Site delete initiated successfully', 'type' => 'success']);
    }

    /**
     * @throws Throwable
     */
    public function pullAndDeploy(string $hashid)
    {
        $site = Site::findOrFail(hashid_decode($hashid));

        if (!$site->getMeta('git_info')) {
            return response()->json(['message' => 'Git Info not found']);
        }

        SiteDeploymentJob::dispatch($site);

        return response()->json(['message' => 'Deployment Job has been initiated.']);
    }

    /**
     * Pull and deploy for Laravel sites
     *
     * @param Server $server
     * @param Site $site
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function pullAndDeployLaravel(Server $server, Site $site)
    {
        $this->authorize('update', $site);

        if ($site->type !== SiteType::LARAVEL) {
            return response()->json(['message' => 'This endpoint is only for Laravel sites'], 400);
        }

        if (!$site->getMeta('git_info')) {
            return response()->json(['message' => 'Git Info not found'], 400);
        }

        SiteDeploymentJob::dispatch($site);

        return response()->json(['message' => 'Laravel site deployment has been initiated.']);
    }

    public function goLiveDomain(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageDomain', $site);

        // Convert the array into a string suitable for the regular expression
        $reservedDomainsPattern = implode('|', array_map('preg_quote', getReservedDomains()));

        $validated = $request->validate([
            'name' => [
                'required',
                'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/',
                ...SiteNameRule::rules(serverId: $server->id),
                new DnsValidation($site),
                new DomainComparisonRule($request->get('previousDomain'))
            ],
            'additional_domains' => ['nullable', new AliasesValidation, new DnsValidation($site)],
            'ssl_provider' => [
                'nullable', 'in:xcloud,custom,cloudflare', function ($attribute, $value, $fail) use ($server, $request) {
                    if ($value == 'xcloud' && !$server->hasDnsRecord($request->get('name'))) {
                        $fail(__('dns.verification_failed'));
                    }
                }
            ],
            'ssl_certificate' => ['required_if:ssl_provider,custom'],
            'ssl_private_key' => ['required_if:ssl_provider,custom'],
        ]);

        if(empty($request->get('ssl_provider'))){
            // if https is toggled off, then set ssl_provider to null, otherwise it keeps staging ssl provider.
            $site->update([
               'ssl_provider' => null
            ]);
        }

        if ($request->get('ssl_provider') && $request->get('ssl_provider') !== SslCertificate::PROVIDER_STAGING
            && $request->get('ssl_provider') !== SslCertificate::PROVIDER_CLOUDFLARE) {
            $site->sslCertificates()->create([
                'status' => 'new',
                'provider' => $validated['ssl_provider'],
                'ssl_certificate' => $validated['ssl_certificate'],
                'ssl_private_key' => $validated['ssl_private_key'],
            ]);
        }
        if ($site->isWordpress()){
            $site->saveMeta('disable_search_engine_visibility', $request->get('disable_search_engine_visibility'));
        }
        // detach staging site when going live
        if($site->isStagingSite()){
            $site->update([
                'parent_site_id' => null,
            ]);
        }

        $jobs = [];
        if ($request->get('domain_parking_method') === 'go_live') {
            $jobs[] = new RemoveMonitoring(site: $site);

            if (!empty($validated['additional_domains'])) {
                $site->update([
                    'additional_domains' => $validated['additional_domains'],
                ]);
            }

            if (!empty($validated['ssl_provider'])) {
                $site->update([
                    'ssl_provider' => $validated['ssl_provider'],
                ]);
            }

            if ($request->get('domain_active_on_cloudflare')) {
                $site->update([
                    'ssl_provider' => canGenerateSslCertificateOnCloudflare($request->get('name'))
                        ? SslCertificate::PROVIDER_CLOUDFLARE : SslCertificate::PROVIDER_XCLOUD,
                    'environment' => Site::PRODUCTION,
                    'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                    'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                    'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                    'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                    'meta->cloudflare_integration->site_name' => $request->get('site_name'),
                ]);
            }

            if ($site->backupSettings()->exists()){
                $site->backupSettings()->update(['user_id' => auth()->id()]);
            }

            $jobs[] = new UpdateDomainName(site: $site, newDomainName: $request->get('name'));


            Bus::chain($jobs)->catch(function (Throwable $e) use ($site) {
                $site?->markAsDomainUpdateFailure($e->getMessage());
            })->dispatch();
        }

        if ($patchastackVulnerability = $site->patchstackVulnerability()->where('is_purchase', true)->first()) {
            app(PatchstackService::class)->patchstackSiteUpdate($site, $patchastackVulnerability->patchstack_site_id);
        }

        if ($request->expectsJson()) {
            return response()->json($site);
        }

        if ($site->environment === Site::DEMO) {
            return back()->with('flash', [
                'message' => 'Domain update request is sent for processing.',
                'body' => 'Your domain will go live soon.'
            ]);
        } else {
            return redirect()->route('site.domain', [$server, $site])->with('flash', [
                'message' => 'Domain update request is sent for processing.',
                'body' => 'Your domain will go live soon.'
            ]);
        }
    }

    public function reProvision(Server $server, Site $site)
    {
        $this->authorize('view', $site);
        $this->authorize('addSite', $server);
        Artisan::queue("site:provision", ['site_id' => $site->id, '--force' => true]);
        return redirect()->back()->with('flash', [
            'message' => 'Site is being re provisioned.',
            'type' => 'success'
        ]);

    }

    /**
     * @throws AuthorizationException
     */
    public function gitUpdate(Server $server, Site $site, Request $request)
    {
        $this->authorize('update', $site);
        abort_unless($site->getMeta('is_git', false), 403);
        $data = $request->validate([
            'git_branch' => 'required|string|max:255',
            'deploy_script' => 'required_if:run_after_deployment,1',
            'enable_push_deploy' => 'boolean',
            'run_after_deployment' => 'required',
            'pull_now' => 'required|boolean',
        ]);

        $repository = $site->getMeta('git_info.git_repository');
        $currentBranch = $site->getMeta('git_info.git_branch');

        // Only check the connection if the branch was changed
        if ($currentBranch !== $request->input('git_branch')) {
            $task = $site->runInline(new GitConnectionCheck($site, $site->sshKeyPair, $repository, $request->input('git_branch'), false));

            if (!$task->successful()) {
                //throw git_repository validation error
                return throw ValidationException::withMessages([
                    'git_branch' => 'Failed to establish a connection with this branch.',
                ]);
            }
        }

        if ($request->filled('deploy_script')) {
            $site->saveMeta('deploy_script', $request->input('deploy_script'));
        }
        $data['git_repository'] = $repository;
        $site->saveMeta('git_info', Arr::except($data, ['deploy_script']));
        if ($request->boolean('pull_now')) {
            SiteDeploymentJob::dispatch($site);
        }
        if ($request->wantsJson()) {
            return response()->json(['message' => 'Git info updated successfully']);
        }
        return redirect()->back()->with('flash', [
            'message' => 'Git info updated successfully',
            'body' => 'Changes will be applied soon.',
        ]);
    }

    public function checkDomainExists(Request $request)
    {
        $data = $request->validate([
            'domain' => ['required', new Domain],
        ]);

        $domain = $data['domain'];

        $domainExists = Site::where('name', $domain)->exists();

        return response()->json([
            'domain' => $domain,
            'exists' => $domainExists,
            'type' => $domainExists ? 'error' : 'success',
            'message' => $domainExists ? 'Domain already exists.' : 'Domain is available.',
            'body' => $domainExists ? 'Please try another domain.' : '',
        ]);
    }

    public function updateAdminer(Server $server, Site $site, Request $request)
    {
        $this->authorize('database', $site);

        if ($site->isNodeApp() && $request->boolean('enable_adminer')) {
            return redirect()->back()->with('flash', [
                'message' => 'Adminer is not compatible with Node.js applications',
                'type' => 'error'
            ]);
        }

        $request->validate(['enable_adminer' => 'required|boolean']);
        if ($request->boolean('enable_adminer')) {
            $site->update([
                'meta->enable_adminer' => 'adminer-' . randomLowerString(15, '.php'),
                'meta->adminer_enable_time' => now()
            ]);
            $site->installAdminer();
        } else {
            if ($site->removeSiteFile($site->getMeta('enable_adminer'))) {
                $site->update([
                    'meta->enable_adminer' => false,
                    'meta->adminer_enable_time' => null
                ]);
            }
        }
        return redirect()->back()->with('flash', [
            'message' => 'Adminer updated successfully',
            'body' => 'Changes will be applied soon.',
            'type' => 'success'
        ]);
    }
    public function updateFileManager(Server $server, Site $site, Request $request)
    {
        $this->authorize('fileManager', $site);

        // Block enabling file manager for Node.js applications
        if ($site->isNodeApp() && $request->boolean('enable_file_manager')) {
            return redirect()->back()->with('flash', [
                'message' => 'File Manager is not compatible with Node.js applications',
                'type' => 'error'
            ]);
        }

        $request->validate(['enable_file_manager' => 'required|boolean']);
        if ($request->boolean('enable_file_manager')) {
            $site->saveMeta([
                'file_manager' => 'file-manager-'.randomLowerString(15,'.php'),
                'file_manager_enable_time' => now()
            ]);
            $site->installFileManager();
        }else{
            if ($site->removeSiteFile($site->getMeta('file_manager'))){
                $site->saveMeta([
                    'file_manager' => false,
                    'file_manager_enable_time' => null
                ]);
            }
        }
        return redirect()->back()->with('flash', [
            'message' => 'File Manager updated successfully',
            'body' => 'Changes will be applied soon.',
            'type' => 'success'
        ]);
    }

    public function updateUserIsolation(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        IsolationPhpSock::dispatch(site: $site);
        return response()->json(['message' => 'Site user isolation settings is being updating']);
    }

    public function nginxConfigRunAndDebug(Server $server, Site $site, Request $request)
    {
        $this->authorize('customNginx', $site);

        abort_unless($server->stack->isNginx(), 403, 'This is only available for Nginx servers.');

        $validator = Validator::make($request->except('content'), [
            'type' => ['required', Rule::in(CustomNginxEnum::asValue())],
            'file' => ['required', 'string', 'max:30'],
//            'content' => ['required', 'string'],
            'template' => ['required']
        ]);

        if(empty($request->get('content'))){
            $validator->errors()->add('content', 'Content is required');
        }

        // file does not contain any space and extension
        $file = $request->get('file');
        if (strpos($file, ' ') !== false || strpos($file, '.') !== false){
            $validator->errors()->add('file', 'File name must not contain any space and any extension. Example: my-custom-config');
        }

        if (!empty($validator->errors()->messages())) {
            return back()->withErrors($validator);
        }

//        if(!$request->get('runAndDebug') && $request->get('isEditing')){
//            // Check if the file already exists
//            $customNginx = $site->customNginxConfigs()->where('file', $request->get('file'))->first();
//            if ($customNginx) {
//                return back()->withErrors([
//                    'file' => 'File name already exists. Please use a different file name.'
//                ])->withInput();
//            }
//        }

        $validated = $validator->validated();

        if ($site->getMeta('disable_nginx_config_regeneration')){
            return back()->with('flash', [
                'message' => 'Please enable nginx regeneration from your site settings',
                'response' => [
                    'Please enable nginx regeneration from your site settings'
                ],
                'type' => 'error'
            ]);
        }

        if($site->getMeta('nginx_config_version') !== CustomNginxEnum::NGINX_CONFIG_VERSION){
            $site->regenerateNginxConf();
        }

        $task = $site->run(new RunAndDebugNginxConfig(
            $site,
            $validated['type'],
            $validated['file'],
            $request->get('content'),
            $request->get('runAndDebug')
        ));


        $lines = explode("\n", $task->output);

        // Filter out the protocol options redefined warning message
        $filteredLines = array_filter(array_filter($lines, function($line) {
            return strpos($line, "nginx: [warn] protocol options redefined for") === false;
        }));

        // Check if the test is successful
        $testSuccessful = false;
        foreach ($filteredLines as $line) {
            if (strpos($line, "nginx: configuration file /etc/nginx/nginx.conf test is successful") !== false) {
                $testSuccessful = true;
                break;
            }
        }

        if(!$request->get('runAndDebug')){
            if(!$testSuccessful){
                return back()->with('flash', [
                    'message' => 'Nginx configuration file is not valid. Please run and debug to check the error.',
                    'response' => $filteredLines,
                    'type' => 'error',
                    'success' => false
                ]);
            }

            $site->customNginxConfigs()->updateOrCreate([
                'type' => $validated['type'],
                'file' => $validated['file'],
            ],[
                'template' => $validated['template'],
                'content' => $request->get('content'),
                'status' => $testSuccessful ? 'success' : 'failed',
                'response' => json_encode($filteredLines)
            ]);
        }
        return back()->with('flash', [
            'message' => $request->get('runAndDebug') ? 'Nginx Config Ran and Debugged' : 'Nginx Config Saved',
            'response' => $filteredLines,
            'type' => 'success',
            'success' => $testSuccessful
        ]);
    }

    public function deleteCustomNginxConfig(Server $server, Site $site, CustomNginx $customNginx, Request $request)
    {
        $this->authorize('customNginx', $site);

        abort_unless($server->stack->isNginx(), 403, 'This is only available for Nginx servers.');

        $site->runInline(new DeleteCustomNginxConfig($site, $customNginx));

        $customNginx->delete();

        return back()->with('flash', [
            'message' => 'Custom Nginx Config deleted successfully',
            'type' => 'success'
        ]);
    }

    public function initiateDomainChallenge(Server $server, Request $request)
    {
        if ($request->get('siteId')) {
            $site = Site::findOrFail($request->get('siteId'));
        } else {
            abort(404);
        }

        $this->authorize('ssl', $site);

        $domainChallenge = DomainChallenge::create([
            'status' => DomainChallengeStatusEnum::PROCESSING->value,
            'site_id' => $site?->id ?? null,
        ]);

        DomainChallengeStatusChanged::dispatch($domainChallenge);

        // check if another instance of certbot is running
        $isCertbotRunning = $server->runInline(new InlineScript('sudo certbot certificates'))->output;
        if(str_contains($isCertbotRunning, 'Another instance of Certbot is already running.')){
            $domainChallenge->markAsFailed();
            return response()->json([
                'message' => 'Another instance of Certbot is already running. Please try again after 15 minutes.',
                'challengeFailed' => true
            ]);
        }

        $domain = $site?->name ?? $request->get('domain');

        $site->runInBackground(new DomainChallengeScript(server: $server, domainChallenge: $domainChallenge, domain: $domain));

        $domainLog = $site->runInline(new GetDomainChallengeLog(server: $server, domainChallenge: $domainChallenge))->output;

        if(!str_contains($domainLog, 'DNS_NAME')){
            $domainChallenge->markAsFailed();

            DomainChallengeStatusChanged::dispatch($domainChallenge);

            return response()->json([
                'message' => 'Domain Challenge failed',
                'domainChallenge' => $domainChallenge,
                'challengeFailed' => true
            ]);
        }

        $site->update([
            'ssl_provider' => SslCertificate::PROVIDER_XCLOUD
        ]);

        $certificate = $site->sslCertificates()->firstOrCreate(
            [
                'provider' => SslCertificate::PROVIDER_XCLOUD
            ],
            [
                'status' => SslCertificate::STATUS_NEW
            ]
        );

        // Extract DNS_NAME
        $matches = [];
        preg_match('/DNS_NAME:([^\s]+)/', $domainLog, $matches);
        $domainName = $matches[1] ?? null;

        // Extract DNS_TXT_RECORD
        $matches = [];
        preg_match('/DNS_TXT_RECORD:"([^"]+)"/', $domainLog, $matches);
        $domainTxtRecord = $matches[1] ?? null;

        // Check if output contains "Challenge failed for domain"
        $challengeFailed = str_contains($domainLog, 'Challenge failed for domain');

        $domainChallenge->update([
            'status' => $challengeFailed ? DomainChallengeStatusEnum::FAILED->value : DomainChallengeStatusEnum::RECORD_ADDING->value,
            'domain' => $domainName,
            'record' => $domainTxtRecord,
            'ssl_certificate_id' => $certificate->id
        ]);

        if($challengeFailed){
            $domainChallenge->markAsFailed();
            $domainChallenge->sslCertificate->markAsFailed();
            SiteSSLStatusChanged::dispatch($domainChallenge->sslCertificate);
        }

        DomainChallengeStatusChanged::dispatch($domainChallenge);

        if (request()->expectsJson()) {
            return response()->json([
                'domainChallenge' => $domainChallenge,
                'challengeFailed' => $challengeFailed
            ]);
        }

        return response()->json([
            'domainChallenge' => $domainChallenge,
            'challengeFailed' => $challengeFailed
        ]);
    }

    public function domainChallenged(Server $server, Request $request)
    {
        $domainChallengeId = $request->get('domainChallenge');

        if(!$domainChallengeId){
            return response()->json(['message' => 'Domain Challenge not found'], 404);
        }

        $domainChallenge = DomainChallenge::findOrFail($domainChallengeId);

        $this->authorize('ssl', $domainChallenge->site);

        $domainChallenge->markAsCheckingDns();
        DomainChallengeStatusChanged::dispatch($domainChallenge);

        $dnsPropagated = DnsRecordChecker::check(name:$domainChallenge->domain, value: $domainChallenge->record, record: DNS_TXT);

        $domainChallenge->saveMeta('dns_propagated', $dnsPropagated);

        if($dnsPropagated){
            $domainChallenge->markAsDnsPropagated();
            DomainChallengeStatusChanged::dispatch($domainChallenge);
        }

        $domainChallenge->markAsChallenged();
        DomainChallengeStatusChanged::dispatch($domainChallenge);

        if (request()->expectsJson()) {
            return response()->json([
                'domainChallenge' => $domainChallenge,
                'message' => 'Domain Challenged successfully'
            ]);
        }

        return response()->json([
            'domainChallenge' => $domainChallenge,
            'message' => 'Domain Challenged successfully'
        ]);
    }

    public function recueSite(Server $server, Site $site, Request $request)
    {
        abort_unless($server->isConnected(), 403, 'Server is not connected');
        $this->authorize('settings', $site);

        $data = $request->validate([
            'isolate_user' => 'required|boolean',
            'directory_permissions' => 'required|boolean',
            'regenerate_nginx' => 'required|boolean',
            'restart_nginx' => 'required|boolean',
            'reinstall_php' => 'required|boolean',
            'repair_node' => 'required|boolean',
            'repair_pm2' => 'required|boolean',
            'restart_pm2' => 'required|boolean',
        ]);

        # check if all options are disabled
        if (collect($data)->every(fn($value) => $value === false)) {
            return response()->json([
                'message' => 'Please select at least one option to rescue the site',
                'type' => 'error'
            ]);
        }

        RescueSiteJob::dispatch(site: $site, data: $data);

        return response()->json(['message' => 'Site rescue job has been initiated.']);
    }

    public function updateVulnerabilityScan(Server $server, Site $site, Request $request)
    {
        $this->authorize('siteVulnerabilityScan', $site);

        $validated = $request->validate([
            'vulnerability_scan' => ['required', 'boolean'],
        ]);

        $site->updateVulnerabilityScan($validated['vulnerability_scan']);

        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Vulnerability Scan Updated Successfully'
            ]);
        }

        return redirect()->back()->with('flash', [
            'message' => 'Vulnerability scan updated successfully',
            'type' => 'success'
        ]);
    }

    public function deployStaging(Server $server, Site $site, DeployStagingRequest $request)
    {
        $this->authorize('deployStaging', $site);

        // check eligibility
        $task = $site->checkSiteStorageAvailability( oldSiteDomain: $site->name);

        if($task->output !== 'Server has enough storage to clone site'){
            return redirect()->back()->with('flash', [
                'message' => $task->output,
                'type' => 'error'
            ]);
        }

        // site limit check on current plan
        $latestBill = $server->bills()->where('service', $server->getDefaultBillingService())->orderBy('id', 'desc')->first();

        if ($latestBill && $latestBill->billingPlan->name === PlansEnum::Free) {
            $quantityAvailed = $server->sites()->count(); //$server->team->availed_offers[BillingServices::Site->value] ?? 0;

            $canAvailInTotal = ActiveOffers::matchGetQuantity(BillingServices::Site, $latestBill->billingPlan?->name);

            if ($quantityAvailed >= $canAvailInTotal) {
                return redirect()->back()->with('flash', [
                    'type' => 'warning',
                    'message' => $canAvailInTotal.' site limit reached, please consider upgrading the server bill from free to paid plan to avail more sites.'
                ]);
            }
        }

        $domainName = $request->get('name');

        // prepare deploy site data
        $siteData = [];
        $siteData['name'] = $domainName;
        $siteData['title'] = $domainName;
        $siteData['type'] = SiteType::WORDPRESS;
        $siteData['ssl_provider'] = $request->get('staging_env_type') === 'own_domain' ? $request->get('ssl_provider', null) : SslCertificate::PROVIDER_STAGING;
        $siteData['environment'] = $request->get('staging_env_type') === 'own_domain' ? Site::STAGING_WITH_OWN_DOMAIN : Site::STAGING;
        $siteData['database_provider'] = $site->database_provider;
        $siteData['database_password'] = DatabasePassword::randomPassword();
        $siteData['database_host'] = 'localhost';
        $siteData['database_port'] = 3306;
        $siteData['admin_user'] = Str::random(40);
        $siteData['admin_password'] = Str::random(40);
        $siteData['managed_database_options']['do_cluster_name'] = DatabaseNameGenerator::generateDOClusterName($domainName);
        $siteData['php_version'] = $site->php_version;
        $siteData['wp_updates'] = $site->wp_updates;
        $siteData['prefix'] = Generator::generate($domainName) . '_';
        $siteData['wordpress_version'] = WordPressVersion::DEFAULT;
        $siteData['redis_password'] ??= Str::random(32);

        $siteData['meta->blueprint_id'] = Arr::get($site->meta, 'meta->blueprint_id');
        $siteData['meta->admin_email'] = Arr::get($site->meta, 'admin_email', 'admin@' . $domainName);
        $siteData['meta->enable_multisite'] = Arr::get($site->meta, 'enable_multisite', false);
        $siteData['meta->multisite_subdomain'] = Arr::get($site->meta, 'multisite_subdomain', false);
        $siteData['meta->user_id'] = user()?->id;
        $siteData['meta->cloneInfo.cloneFromSiteId'] = $site->id;
        $siteData['meta->enable_wp_cron'] =  Arr::get($site->meta, 'enable_wp_cron', false);
        $siteData['meta->has_redis_object_caching'] =  Arr::get($site->meta, 'has_redis_object_caching', false);
        $siteData['meta->disable_search_engine_visibility'] =  true;

        if ($request->get('domain_active_on_cloudflare')) {
            $siteData['meta->cloudflare_integration.domain_active_on_cloudflare'] = $request->get('domain_active_on_cloudflare');
            $siteData['meta->cloudflare_integration.account_id'] = $request->get('cloudflare_account_id');
            $siteData['meta->cloudflare_integration.zone_id'] = $request->get('cloudflare_zone_id');
            $siteData['meta->cloudflare_integration.subdomain'] = $request->get('subdomain');
            $siteData['meta->cloudflare_integration.site_name'] = $request->get('site_name');
        }

        // create staging site
        $stagingSite = $server->sites()->create($siteData);

        $stagingSite->update([
            'status' => SiteStatus::CLONE_INIT,
            'database_user' => Generator::generateName($stagingSite),
            'database_name' => Generator::generateName($stagingSite),
            'prefix' => Generator::generate($stagingSite->name) . '_',
            'site_user' => Generator::generate($stagingSite->name),
            'parent_site_id' => $site->id,
        ]);

        // create site clone
        $siteClone = AutoSiteClone::create([
            'site_id' => $stagingSite->id,
            'type' => \App\Models\SiteClone::DEFAULT,
            'user_id' => auth()->id(),
            'team_id' => team()->id,
            'server_id' => $server->id,
            'existing_site_url' => $site->name,
            'domain_name' => $domainName,
        ]);

        $siteClone->saveMeta('cloneInfo', [
            'cloneFromSiteId' => $site->id
        ]);

        // start cloning
        $stagingSite->update(['status' => SiteStatus::CLONING]);
        SiteClone::dispatch($siteClone);

        return redirect()->route('site.progress', [
            'server' => $server->id,
            'site' => $stagingSite->id,
        ]);
    }

    public function pullFromProduction(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageStaging', $site);

        $request->validate([
            'files' => 'nullable|boolean',
            'file_choice' => 'required_with:files|in:overwrite,incremental',
            'database' => 'nullable|boolean',
            'database_choice' => 'required_with:database|in:full,selected_tables',
            'selected_tables' => 'required_if:database_choice,selected_tables|array',
            'take_backup' => 'nullable|boolean',
        ]);

        // create deployment log
        $deploymentLog = DeploymentLog::create([
            'production_site_id' => $site->productionSite->id ?? $site->id,
            'source_site_id' => $site->parent_site_id,
            'destination_site_id' => $site->id,
            'status' => DeploymentLogsStatusEnum::PENDING,
            'action' => DeploymentLogsStatusEnum::PULL->value,
            'initiated_by' => user()->id,
        ]);

        SiteStagingPullPushStatusChanged::dispatch($site, $deploymentLog);

        $site->runInBackground(new PullFromProductionScript(stagingSite: $site, deploymentLog: $deploymentLog, options: $request->all()), [
            'then' => [
                new SitePullActionCompleted($site, $deploymentLog),
            ],
        ]);

        return back()->with('flash', [
            'message' => 'Site pulling initiated successfully. Please wait for a few minutes.',
            'type' => 'success'
        ]);
    }

    public function pushToProduction(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageStaging', $site);

        $request->validate([
            'files' => 'nullable|boolean',
            'file_choice' => 'required_with:files|in:overwrite,incremental',
            'database' => 'nullable|boolean',
            'database_choice' => 'required_with:database|in:full,selected_tables',
            'selected_tables' => 'required_if:database_choice,selected_tables|array',
            'take_backup' => 'nullable|boolean',
        ]);

        // create deployment log
        $deploymentLog = DeploymentLog::create([
            'production_site_id' => $site->productionSite->id ?? $site->id,
            'source_site_id' => $site->id,
            'destination_site_id' => $site->parent_site_id,
            'status' => DeploymentLogsStatusEnum::PENDING,
            'action' => DeploymentLogsStatusEnum::PUSH->value,
            'initiated_by' => user()->id,
        ]);

        SiteStagingPullPushStatusChanged::dispatch($site, $deploymentLog);

        $pushToProductionJobs = [];
        if($request->get('take_backup')){
            $backupSetting = $site->productionSite->backupSettings()->orderBy('is_local')->first();
            if ($backupSetting){
                $pushToProductionJobs[] =  new SiteBackupNow(site: $site->productionSite, backupSetting: $backupSetting,user: user());
            }else{
                $pushToProductionJobs[] = new TakeSiteBackupJob(
                    server:$server,
                    site: $site->productionSite,
                    user: user()
                );
            }

        }

        $pushToProductionJobs[] = new PushToProductionJob(stagingSite: $site, deploymentLog: $deploymentLog, options: $request->all(), backupSetting: $remoteBackupSetting ?? $localBackupSettings ?? null);

        Bus::chain($pushToProductionJobs)
            ->catch(function (Batch $batch, Throwable $e){
                Log::error($e->getMessage());
            })
            ->dispatch();

        return back()->with('flash', [
            'message' => 'Site push initiated successfully. Please wait for a few minutes.',
            'type' => 'success'
        ]);
    }

    public function fetchProductionDatabaseTables(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageStaging', $site);

        $output = $site->fetchProductionSiteDatabaseTables()->output;

        // Split the output string into lines
        $array = explode("\n", $output);

        // Filter out the warning message
        $array = array_filter($array, function($item) {
            return !str_starts_with($item, 'mysql: [Warning]');
        });

        if (request()->expectsJson()) {
            return response()->json([
                'tables' => array_values($array),
                'message' => 'Table fetched successfully'
            ]);
        }

        return response()->json([
            'tables' => array_values($array),
            'message' => 'Table Fetched successfully'
        ]);
    }

    public function fetchDeploymentLogs(Server $server, Site $site, Request $request)
    {
        $this->authorize('manageStaging', $site);

        $fetchPullDeploymentLogs = DeploymentLog::query()
            ->where('destination_site_id', $site->id)
            ->with(['sourceSite:id,name,ssl_provider', 'destinationSite:id,name,ssl_provider'])
            ->orderBy('id', 'DESC')
            ->get();

        $fetchPushDeploymentLogs = DeploymentLog::query()
            ->where('source_site_id', $site->id)
            ->with(['sourceSite:id,name,ssl_provider', 'destinationSite:id,name,ssl_provider'])
            ->orderBy('id', 'DESC')
            ->get();

        // merge pull and push deployment logs
        $deploymentLogs = array_values(collect($fetchPullDeploymentLogs->merge($fetchPushDeploymentLogs))->sortBy('id', SORT_REGULAR, true)->map(function($log) {
            return [
                'id' => $log->id,
                'source_site_id' => $log->sourceSite->id,
                'source_site' => $log->sourceSite->name,
                'source_site_url' => $log->sourceSite->ssl_provider ? 'https://' . $log->sourceSite->name : 'http://' . $log->sourceSite->name,
                'destination_site_id' => $log->destinationSite->id,
                'destination_site' => $log->destinationSite->name,
                'destination_site_url' => $log->destinationSite->ssl_provider ? 'https://' . $log->destinationSite->name : 'http://' . $log->destinationSite->name,
                'status' => $log->status,
                'action' => $log->action,
                'initiated_by' => $log->initiatedBy->name,
                'created_at' => $log->created_at->diffForHumans(),
                'updated_at' => $log->updated_at->diffForHumans(),
            ];
        })->toArray());

        if (request()->expectsJson()) {
            return response()->json([
                'deploymentLogs' => $deploymentLogs,
                'message' => 'Deployment Logs fetched successfully'
            ]);
        }

        return response()->json([
            'deploymentLogs' => $deploymentLogs,
            'message' => 'Deployment Logs fetched successfully'
        ]);
    }

    public function fetchStagingAndProductionSites(Server $server, Site $site, Request $request)
    {
        $this->authorize('viewSite', $server);

        $site->load([
            'productionSite' => function ($query) {
                $query->select('id','name','title','status', 'server_id', 'environment', 'parent_site_id');
            },
            'stagingSites' => function ($query) {
                $query
                    ->select('id','name','title','status', 'server_id', 'environment', 'parent_site_id')
                    ->where('status', SiteStatus::PROVISIONED)
                    ->where('environment', Site::STAGING)
                    ->orWhere('environment', Site::STAGING_WITH_OWN_DOMAIN);
            }
        ]);

        if (request()->expectsJson()) {
            return response()->json([
                'production_site' => $site->productionSite,
                'staging_sites' => $site->stagingSites,
                'message' => 'Deployment Logs fetched successfully'
            ]);
        }

        return response()->json([
            'production_site' => $site->productionSite,
            'staging_sites' => $site->stagingSites,
            'message' => 'Deployment Logs fetched successfully'
        ]);
    }

    public function ignoreVulnerability(Server $server, Site $site, Request $request)
    {
        $this->authorize('siteVulnerabilityScan', $site);
        $slug = $request->input('slug');
        $is_ignored = $request->boolean('is_ignored');
        $site->ignoreVulnerability($slug,$is_ignored);
    }

    public function scanVulnerability(Server $server, Site $site)
    {
        $this->authorize('update', $site);

        $task = $site->runInline(new PullSiteUpdates($server, $site));
        $data = $task->output_json;

        if (!isset($data['wp_updates'])) {
            return response()->json(['error' => 'No updates found'], 404);
        }
        $wpUpdates = $data['wp_updates'];
        $site->update([
            'wordpress_version' => $wpUpdates['wp_version'] ?? $site->wordpress_version,
            'wp_updates' => [
                ...$wpUpdates,
                'datetime' => now(),
                'refreshing' => false
            ]
        ]);

        $vulnerabilities = WordfenceVulnerability::scanVulnerability($wpUpdates);
        $site->syncVulnerabilities(vulnerabilities: $vulnerabilities,auto_update: false);
        return response()->json(['scan_result' => $vulnerabilities, 'last_update_check' => now()->diffForHumans()]);
    }

    public function storeIPAddress(Server $server,  Site $site, StoreIpAddressRequest $request)
    {
        $ipAddress = $site->ipAddresses()->create($request->validated());
        SyncIpWhitelistBlackListJob::dispatch($site);
        return response()->json(['message' => 'IP Address added successfully', 'ipAddress' => $ipAddress]);
    }

    public function updateIPAddress(Server $server, Site $site, UpdateIpAddressRequest $request)
    {
        $ipAddress = $site->ipAddresses()->whereId($request->id)->first();
        $ipAddress->ip_address = $request->ip_address;
        $ipAddress->type = $request->type;
        if ($ipAddress->isDirty('ip_address','type')) {
            $ipAddress->save();
            SyncIpWhitelistBlackListJob::dispatch($site);
        }
        return response()->json(['message' => 'IP Address updated successfully', 'ipAddress' => $ipAddress]);
    }

    public function removeIpAddress(Server $server, Site $site, Request $request)
    {
        $this->authorize('settings', $site);
        $data = $request->validate(['id' => 'required|exists:ip_addresses,id']);
        $site->ipAddresses()->whereId($data['id'])->delete();
        SyncIpWhitelistBlackListJob::dispatch($site);
        return response()->json(['message' => 'IP Address removed successfully']);
    }

    public function updateObjectCachePro(Server $server, Site $site, Request $request)
    {
        $this->authorize('manage-cache', $site);
        $request->validate([
            'isEnable' => 'required|boolean',
            'debug_mode' => 'required|boolean',
            'plugin_id' => 'required|exists:plugin_integrations,id',
        ]);
        if ($request->isEnable) {
            if ($server->stack->isNginx()){
                $site->update(['meta->has_redis_object_caching' => false]);
            }
            if ($site->pluginIntegrations()->wherePivot('plugin_integration_id', $request->plugin_id)->exists()) {
                $site->pluginIntegrations()
                    ->wherePivot('plugin_integration_id', $request->plugin_id)
                    ->updateExistingPivot($request->plugin_id, [
                        'debug_mode' => $request->debug_mode,
                    ]);
            } else {
                $site->pluginIntegrations()
                    ->attach($request->plugin_id,[
                        'debug_mode' => $request->debug_mode,
                        'username' => (string) Str::of($site->name)->slug()->limit(20, ''),
                        'password' => Str::random(20),
                        'prefix' => str(str_pad(mt_rand(1, 9), 8, mt_rand(0, 9)))->append($site->id)->toString()
                    ]);
            }
            $integratedPlugin = $site->pluginIntegrations()->first();
            $site->runInBackground(new ActiveRedisObjectCacheProScript(site: $site, licenseKey: $integratedPlugin->getObjectCacheProLicense(), redisDb: 0, debug:$request->debug_mode));
        } else {
            $redis_user = $site->getRedisObjectCacheProUser();
            $site->pluginIntegrations()
                ->detach($request->plugin_id);
            $site->runInBackground(new DeactivateRedisObjectCacheProScript(site: $site, redis_user: $redis_user));
        }
        return redirect()->back()->with('flash', ['message' => 'Object Cache Pro updated successfully', 'type' => 'success']);

    }

    public function purgeObjectCachePro(Server $server,Site $site)
    {
        $this->authorize('manage-cache', $site);
        if ($site->pluginIntegrations()->exists()){
            $task = $site->runInline(new PurgeObjectCachePro(server: $server, site: $site));
            if ($task->successful()){
                return redirect()->back()->with('flash', ['message' => 'Object Cache Pro purged successfully', 'type' => 'success']);
            }
            return redirect()->back()->with('flash', ['message' => 'Failed to purge Object Cache Pro', 'type' => 'error']);
        }
        return redirect()->back()->with('flash', ['message' => 'Object Cache Pro is not enabled', 'type' => 'error']);
    }

    public function updateWebRoot(Server $server,Site $site, Request $request)
    {
        //web_root validation must be a directory string and '' is allowed
        $request->validate([
            'web_root' => 'nullable|string|max:255|regex:/^[a-z0-9_-]+$/',
        ]);
        if (trim($request->web_root) !== $site->web_root){
            $site->update(['web_root' => trim($request->web_root)]);
            $site->regenerateNginxConf();
        }
        return redirect()->back()->with('flash', ['message' => 'Web Root updated successfully', 'type' => 'success']);
    }

    public function scanWpChecksum(Server $server,Site $site, Request $request)
    {
        $this->authorize('monitors', $site);
        abort_unless($site->isWordpress(), 404);
        $site->runInBackground(script: new WpChecksumScan($site),options:[
            'then' => [
                new ChecksumVerificationUpdated($site),
            ],
        ]);
        return response()->json([
            'message' => 'WordPress checksum scan initiated successfully',
        ]);
    }

    public function getWpChecksumResult(Server $server,Site $site)
    {
        $this->authorize('monitors', $site);
        return response()->json([
            'integrityChecksum' => $site->integrityChecksum,
            'last_scan' => $site->integrityChecksum?->updated_at?->diffForHumans(),
            'message' => 'WordPress checksum scanned successfully'
        ]);
    }

    public function storeOneClickApp(Server $server, OneClickAppCreationRequest $request)
    {
        $this->authorize('addSite', $server);

        $siteData = $request->getSiteData();

        $site = $server->sites()->create($siteData);

        if ($request->has('tags')) {
            $site->syncTags($request->get('tags'));
        }

        $site->provision();

        if ($request->expectsJson()) {
            return response()->json($site, 201);
        }

        return to_route('site.show', [$server, $site])->with('flash', [
            'message' => 'One Click App is being installed on your server.',
            'body' => 'Provision script initialized. It may take up to 3 minutes to complete.'
        ]);
    }

    public function generatePatchstackInvoice(Server $server, Site $site)
    {
        $this->authorize('update', $site);

        $product = Product::where('service_type', BillingServices::PatchstackAddon->value)->first();
        app(PatchstackService::class)->addPatchstack($site);

        $patchstackVulnerability = $site->patchstackVulnerability()->first();
        if (blank($patchstackVulnerability)) {
            return response()->json(['error' => 'Patchstack vulnerability not found'], 404);
        }

        $existingBill = Bill::where('team_id', $server->team->id)
            ->where('generator_id', $patchstackVulnerability->id)
            ->where('generator_type', get_class($patchstackVulnerability))
            ->where('service', BillingServices::PatchstackAddon)
            ->where('status', '!=', BillingStatus::Paid)
            ->orderBy('id', 'desc')
            ->first();

        if ($existingBill) {
            return response()->json($existingBill->invoice);
        }

        $bill = $patchstackVulnerability->cost($product->price)
            ->title($patchstackVulnerability->getBillingName(BillingServices::PatchstackAddon))
            ->prepaid()
            ->useProduct($product)
            ->service($patchstackVulnerability->getDefaultBillingService())
            ->description($patchstackVulnerability->getBillingShortDescription(BillingServices::PatchstackAddon))
            ->renewMonthly()
            ->generateBill();

        $invoice = $patchstackVulnerability->generateInvoice(InvoiceSourceEnum::SinglePurchase);

        //$invoice = InvoiceGenerator::bills($bill)->generate();

        return response()->json($invoice);
    }

    public function enablePatchstackVulnerability(Server $server, Site $site, Request $request)
    {
        $this->authorize('update', $site);

        $patchstack = $site->patchstackVulnerability()->first();
        if (!blank($patchstack)) {
            $site->patchstackVulnerability()->update([
                'is_purchase' => true
            ]);
        }

        EnablePatchstackVulnerabilityJob::dispatch($site);

        return response()->json([
            'message' => 'Patchstack activation started.',
            'patchstack_vulnerability' => $site->patchstackVulnerability()->first()
        ], 200);
    }

    public function cancelPatchstackVulnerability(Server $server, Site $site, Request $request)
    {
        $this->authorize('update', $site);

        $patchastackVulnerability = $site->patchstackVulnerability()->first();
        if (blank($patchastackVulnerability)) {
            return back()->with('error', 'Patchstack vulnerability not enabled.');
        } else {
            $cancelPatchstack = app(PatchstackService::class)->cancelPatchstack($site);
            if ($cancelPatchstack) {
                return response()->json(['message' => 'Patchstack subscription canceled successfully.'], 200);
            }

            return response()->json(['message' => 'Failed to cancel Patchstack subscription. Please try again later.'], 404);
        }
    }

    public function scanPatchstackVulnerability(Server $server, Site $site)
    {
        $this->authorize('update', $site);

        $patchastackVulnerability = $site->patchstackVulnerability()->first();
        if ($patchastackVulnerability && $patchastackVulnerability->patchstack_site_id) {
            $patchastackVulnerabilities = app(PatchstackService::class)->patchstackVulnerabilities($patchastackVulnerability->patchstack_site_id);
            if ($patchastackVulnerabilities) {
                $site->patchstackVulnerability()->update([
                    'vulnerabilities' => $patchastackVulnerabilities
                ]);

                $site->update([
                    'meta->last_patchstack_scan' => now()
                ]);

                $site->syncPatchstackVulnerabilities(vulnerabilities: $patchastackVulnerabilities, auto_update: false);

                $patchastackVulnerability->refresh();

                return response()->json(['patchstack_vulnerability' => json_decode($patchastackVulnerabilities), 'last_update_check' => now()->diffForHumans()]);
            }
        }

        Log::error('Failed to scan Patchstack vulnerability. Missing patchstack site id.');
        return response()->json(['error' => 'Failed to scan Patchstack vulnerability.'], 404);
    }

    public function ignorePatchstackVulnerability(Server $server, Site $site, Request $request)
    {
        $this->authorize('siteVulnerabilityScan', $site);
        $slug = $request->input('slug');
        $is_ignored = $request->boolean('is_ignored');
        $site->ignorePatchstackVulnerability($slug, $is_ignored);
    }

    public function hidePatchstackVulnerability(Server $server, Site $site, Request $request)
    {
        $this->authorize('siteVulnerabilityScan', $site);
        $isHide = $request->boolean('is_hide');
        $site->saveMeta('hide_shield_pro_banner', $isHide);

        return response()->json([
            'message' => 'Shield pro banner hidden successfully'
        ]);
    }
}
