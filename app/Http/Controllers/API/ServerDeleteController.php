<?php

namespace App\Http\Controllers\API;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Http\Controllers\Controller;
use App\Jobs\Server\DeleteServer;
use App\Jobs\Server\ModifyServer;
use App\Jobs\Site\CancelPatchstackSites;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Server;
use App\Models\Task;
use App\Repository\ServerBillingRepository;
use App\Repository\ServerRepository;
use App\Services\Vulnerability\PatchstackService;
use Illuminate\Http\Request;

class ServerDeleteController extends Controller
{

    public function archive(Server $server)
    {
        $this->authorize('archive', $server);

        $validated = request()->validate([
            'archive_confirmation' => 'required|string'
        ]);

        if ($validated['archive_confirmation'] !== $server->name) {
            if (request()->expectsJson()) {
                return response()->json([
                    'message' => 'Server name does not match confirmation'
                ], 422);
            }
            // return error to error bag
            return back()->withErrors([
                'archive_confirmation' => 'Server name does not match confirmation'
            ]);
        }
        $name = $server->name;

        $server->archive();

        if (request()->expectsJson()) {
            return response()->json($server);
        }

        return redirect(route('server.index'))->with('flash', ['message' => $name.' Archived Successfully']);
    }

    public function restore(Server $server)
    {
        $this->authorize('restore', $server);

        $server->restore();

        if (request()->expectsJson()) {
            return response()->json($server);
        }

        return back()->with('flash', ['message' => 'Server Restored Successfully']);
    }

    public function delete(Server $server, Request $request)
    {
        $this->authorize('delete', $server);

        // if the server has unpaid bills then it cannot be deleted
        $server->bills()->withoutOffer()->unpaid()->get()->each(function($bill) {
            abort_unless($bill->status === BillingStatus::Unpaid, 403, 'Cannot delete server with unpaid please contact with support');
        });

        abort_unless($server->isProvisioned() || $server->isPaymentFailed(), 403, 'Cannot delete xcloud server from here');
        $validated = $request->validate([
            'delete_confirmation' => 'required|string|regex:(^'.$server->name.'$)',
            'delete_from_provider' => 'nullable|boolean',
            'delete_dns_records' => 'nullable|boolean',
        ],[
            'delete_confirmation.regex' => 'Server name does not match confirmation'
        ]);

        if($validated['delete_dns_records'] ?? false){
            $server->saveMeta('delete_dns_records', true);
        }

        $name = $server->name;
        #get sites:id of the server
        $sites = $server->sites()->pluck('id')->toArray();
        if (count($sites) > 0) {
            #delete(from db) all the backup files of the sites
            BackupFile::whereHas('backupSetting', fn($q) => $q->whereIn('site_id', $sites))->delete();
            #delete all the backup settings of the sites
            BackupSetting::whereIn('site_id', $sites)->delete();
        }
        #remove vulnerability settings of the server
        $server->vulnerabilitySetting()->delete();

        #Remove Patchstack Vulnerability
        CancelPatchstackSites::dispatch($server);

        (new ServerRepository($server))->removeCloudflareDomainsAndSSLCertificateOnDeletingServer();

        //if the server provider is xcloud or digital ocean need to delete the server from the provider
        if ($server->cloudProvider?->isXCloudOrWhiteLabel() || ($validated['delete_from_provider'] ?? false)) {
            $task = Task::create([
                'name' => 'DeleteServer',
                'status' => 'pending',
                'server_id' => $server->id,
                'initiated_by' => auth()->id(),
                'team_id' => $server->team_id,
            ]);

            DeleteServer::dispatchTask($task, $server);
            return redirect(route('server.index'))->with('flash', ['message' => $name.' Server Delete Initiated']);
        }

         $server->forceDelete();

        if (request()->expectsJson()) {
            return response()->json($server);
        }

        return redirect(route('server.index'))->with('flash', ['message' => $name.' Deleted Successfully']);
    }
}

?>
