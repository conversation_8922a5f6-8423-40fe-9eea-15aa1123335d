<?php

namespace App\Http\Controllers\API;

use App\Models\User;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\Request;
use Illuminate\Routing\Pipeline;
use Illuminate\Support\Facades\Password;
use <PERSON><PERSON>\Fortify\Actions\AttemptToAuthenticate;
use Laravel\Fortify\Actions\CompletePasswordReset;
use Laravel\Fortify\Actions\EnsureLoginIsNotThrottled;
use Laravel\Fortify\Actions\PrepareAuthenticatedSession;
use Laravel\Fortify\Actions\RedirectIfTwoFactorAuthenticatable;
use <PERSON><PERSON>\Fortify\Contracts\FailedPasswordResetResponse;
use <PERSON><PERSON>\Fortify\Contracts\LoginResponse;
use <PERSON><PERSON>\Fortify\Contracts\LoginViewResponse;
use <PERSON>vel\Fortify\Contracts\PasswordResetResponse;
use Laravel\Fortify\Contracts\ResetsUserPasswords;
use <PERSON>vel\Fortify\Features;
use Laravel\Fortify\Fortify;
use <PERSON><PERSON>\Fortify\Http\Controllers\AuthenticatedSessionController;
use <PERSON><PERSON>\Fortify\Http\Requests\LoginRequest;

class UserLoginController extends AuthenticatedSessionController
{
    public function store(LoginRequest $request)
    {
        // check if user credentials are correct
        $user = User::where('email', $request->email)->first();

        if ($user && \Hash::check($request->password, $user->password)) {
            $whiteLabel = currentWhiteLabel();
            if($user->white_label_id && !$whiteLabel){
                // means the user is client user and trying to login to main app
                return redirect()->back()->withErrors([
                    'email' => 'User credentials are incorrect'
                ]);
            }
        }

        return $this->loginPipeline($request)->then(function ($request) {
            return app(LoginResponse::class);
        });
    }

    protected function loginPipeline(LoginRequest $request)
    {
        if (Fortify::$authenticateThroughCallback) {
            return (new Pipeline(app()))->send($request)->through(array_filter(
                call_user_func(Fortify::$authenticateThroughCallback, $request)
            ));
        }

        if (is_array(config('fortify.pipelines.login'))) {
            return (new Pipeline(app()))->send($request)->through(array_filter(
                config('fortify.pipelines.login')
            ));
        }

        return  (new Pipeline(app()))->send($request)->through(array_filter([
            config('fortify.limiters.login') ? null : EnsureLoginIsNotThrottled::class,
            Features::enabled(Features::twoFactorAuthentication()) ? RedirectIfTwoFactorAuthenticatable::class : null,
            AttemptToAuthenticate::class,
            PrepareAuthenticatedSession::class,
        ]));
    }
}
