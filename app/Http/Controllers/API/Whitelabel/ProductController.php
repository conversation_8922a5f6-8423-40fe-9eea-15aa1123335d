<?php

namespace App\Http\Controllers\API\Whitelabel;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $products = Product::allowedForWhiteLabel()
            ->where('type', $request->get('type'))
            ->active()
            ->withoutDependency()
            ->usableForWhiteLabel()
            ->paginate($request->get('per_page', 100))->through(function ($product) {

                $buildDescription = function ($product) {
                    if (empty($product->description)) {
                        return [];
                    }

                    $buildDescription = [];
                    $descriptions = explode("\n", trim($product->description));

                    foreach ($descriptions as $line) {
                        $line = trim($line);
                        if (empty($line)) continue;

                        if (str_contains($line, 'RAM')) {
                            $buildDescription['memory'] = trim(str_replace('RAM -', '', $line));
                        } elseif (str_contains($line, 'SSD')) {
                            $buildDescription['disk'] = trim(str_replace('SSD -', '', $line));
                        } elseif (str_contains($line, 'vCPU')) {
                            $buildDescription['cpu'] = trim(str_replace('vCPU -', '', $line));
                        } elseif (str_contains($line, 'Bandwidth')) {
                            $buildDescription['bandwidth'] = trim(str_replace('Bandwidth -', '', $line));
                        }
                    }

                    $buildDescription['raw'] = empty($buildDescription) ? $product->description : null;

                    return $buildDescription;
                };

                return [
                    'id' => $product->id,
                    'title' => $product->title,
                    'slug' => $product->slug,
                    'price' => $product->price,
                    'currency' => $product->currency,
                    'is_active' => $product->is_active,
                    'show_on_display' => $product->show_on_display,
                    'renewal_type' => $product->renewal_type,
                    'service_type' => $product->service_type,
                    'type' => $product->type,
                    'description' => $buildDescription($product)
                ];

            });

        return response()->json($products);
    }
}
