<?php

namespace App\Http\Controllers\API\Whitelabel;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Http\Controllers\Controller;
use App\Models\CartForm;
use App\Models\ConnectedAccount;
use App\Models\SubscriptionProduct;
use App\Models\Utm;
use App\Models\WhiteLabel;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Arr;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Log;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\PaymentIntent;
use Stripe\Product;
use Stripe\Stripe;
use Stripe\StripeClient;
use Stripe\Subscription;

class BillingController extends Controller
{
    /**
     * @throws ApiErrorException
     * @throws GuzzleException
     * @throws CardException
     */
    public function processPayment(WhiteLabel $whiteLabel, Request $request)
    {
        $this->authorize('managePayments', $whiteLabel);

        if (Utm::getUtmIdFromCookie()){
            team()->setUtmSource(Utm::getUtmIdFromCookie(), optional(session())->getId(), $request?->ip());
        }

        if (team()->id !== $whiteLabel->owner_team_id) {
            abort(404);
        }

        if(team()->activePaymentMethod()->isEmpty()){
            return redirect()->route('user.bills-payment')
                ->with('flash', [
                    'message' => 'No payment method found. Please add a payment method first.',
                    'type' => 'error'
                ]);
        }

        // Set Stripe API Key
        Stripe::setApiKey(config('services.stripe.secret_key'));

        $subscriptionProduct = SubscriptionProduct::where('uuid', $request->get('subscription_product'))->firstOrFail();

        $cart = CartForm::create([
            'model' => $subscriptionProduct->service_type->getServiceModel(),
            'email'=> team()->email,
            'status'=> CartFormStatuses::Pending,
            'service' => $subscriptionProduct->service_type,
            'subscription_product_id' => $subscriptionProduct->id
        ]);

        $subscription = Subscription::create([
            'customer' => team()->activePaymentMethod()->first()->customer_id,
            'items' => [
                [
                    'price' => $subscriptionProduct->stripe_price_id
                ],
            ],
            'default_payment_method' => team()->activePaymentMethod()->first()->getPaymentMethod(),
        ]);

        $stripeInvoice = \Stripe\Invoice::retrieve($subscription->latest_invoice);

        $paymentIntent = PaymentIntent::retrieve($stripeInvoice->payment_intent);

        $invoice = InvoiceGenerator::cartForm($cart)
            ->team(team())
            ->ignoreStripePIGeneration()
            ->source(InvoiceSourceEnum::WhitelabelPurchase)
            ->paymentMethod(team()->activePaymentMethod()->first())
            ->title('Whitelabel Purchase: ' . $subscriptionProduct->name)
            ->description('Whitelabel Purchase: ' . $subscriptionProduct->name)
            ->amount($subscriptionProduct->price)
            ->status(BillingStatus::Paid)
            ->currency(BillingCurrency::USD)
            ->generate();

        $invoice->update([
            'meta->whitelabel_id' => $whiteLabel->id,
            'meta->subscription_product_id' => $subscriptionProduct->id,
            'meta->stripe_subscription_id' => $subscription->id,
        ]);

        if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
            $invoice->setStatusRequiresAction();

            $cart->update([
                'status' => CartFormStatuses::Pending,
                'meta' => [
                    'subscription_id' => $subscription->id,
                    'stripe_invoice_id' => $subscription->latest_invoice,
                    'invoice_id' => $invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => PaymentIntent::STATUS_REQUIRES_ACTION,
                ]
            ]);

            return redirect()->route('stripe.subscription-product.checkout.requires3dsecure', [
                'affiliateId' => null,
                'paymentIntentId' => $paymentIntent->id,
                'cartId' => $cart->id,
                'invoiceId' => $invoice->id,
                'nextRoute' => 'white-label.onboarding.brand-setup',
                'routeParam' => [
                    'model' => WhiteLabel::class,
                    'id' => $whiteLabel->id
                ],
                'subscriptionProductId' => $subscriptionProduct->id,
                'stripeSubscriptionId' => $subscription->id,
            ]);
        }

        if ($subscription->status === Subscription::STATUS_ACTIVE) {
            $cart->update([
                'status' => CartFormStatuses::Paid,
                'meta' => [
                    'subscription_id' => $subscription->id,
                    'stripe_invoice_id' => $subscription->latest_invoice,
                    'invoice_id' => $invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => PaymentIntent::STATUS_SUCCEEDED,
                ]
            ]);

            $whiteLabel->update([
                'payment_info' => [
                    'invoice_id' => $invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => PaymentIntent::STATUS_SUCCEEDED,
                ],
                'billing_activated_from' => now()
            ]);

            $subscriptionProduct->teams()->attach(team(), [
                'team_id' => team()->id,
                'subscription_product_id' => $subscriptionProduct->id,
                'stripe_subscription_id' => $subscription->id,
            ]);

            // affiliate commission
            if ($invoice->type->isAffiliatable()) {
                if ($invoice?->team?->promoter && !$invoice->promoter_id) {
                    if(!$paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED){
                        \Illuminate\Support\Facades\Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_intent_status' => $paymentIntent->status,
                        ]);
                    }else{
                        $invoice->handleAffiliateCommission();
                    }
                }
            }

            // Utm sales tracking
            if(Utm::ENABLE_UTM_CALCULATION) {
                if (!($paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED)) {
                    Log::warning('Failed to take payment. Utm sales tracking was not generated', [
                        'invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'payment_intent_id' => $paymentIntent->id,
                        'payment_intent_status' => $paymentIntent->status,
                    ]);
                } else {
                    $invoice->team->addUtmSourceSell($invoice->amount, $invoice);
                }
            }

            return redirect()->route('white-label.onboarding.brand-setup', [$whiteLabel])->with('flash', [
                'message' => 'Payment successful!',
                'type' => 'success'
            ]);
        }

        return back()->with('flash', [
            'message' => 'Payment failed! Please try again.',
            'type' => 'error'
        ]);
    }

    public function claimOffer(WhiteLabel $whiteLabel, Request $request)
    {
        if (team()->id !== $whiteLabel->owner_team_id) {
            abort(404);
        }

        $subscriptionProduct = SubscriptionProduct::where('uuid', $request->get('subscription_product'))->firstOrFail();

        if ($subscriptionProduct->canAvailFree(team())) {
            $subscriptionProduct->teams()->attach(team(), [
                'team_id' => team()->id,
                'subscription_product_id' => $subscriptionProduct->id,
                'ends_at' => null
            ]);

            $whiteLabel->update([
                'payment_info' => [
                    'invoice_id' => null,
                    'payment_intent_id' => null,
                    'payment_status' => PaymentIntent::STATUS_SUCCEEDED,
                ],
                'billing_activated_from' => now()
            ]);
        }

        return back()->with('flash', [
            'message' => 'Offer claimed successfully!',
            'type' => 'success'
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function connectToStripe(WhiteLabel $whiteLabel, Request $request)
    {
        $this->authorize('managePayments', $whiteLabel);

        $stripe = new StripeClient(config('services.stripe.secret_key'));

        $account = $stripe->accounts->create([
            'type' => 'standard',
            'metadata' => [
                'white_label_id' => hashid_encode($whiteLabel->id),
            ]
        ]);

        $accountLink = $stripe->accountLinks->create([
            'account' => $account->id,
            'refresh_url' => route('api.whitelabel-billing.stripe-connect-refresh' , [
                'whiteLabel' => $whiteLabel,
                'callFrom' => $request->get('callFrom')
            ]),
            'return_url' => route('api.whitelabel-billing.stripe-connect-callback', [
                'whiteLabel' => $whiteLabel,
                'callFrom' => $request->get('callFrom')
            ]),
            'type' => 'account_onboarding',
        ]);


        $connectedAccount = ConnectedAccount::firstOrCreate(
            [
                'white_label_id' => $whiteLabel->id,
                'stripe_account_id' => $account->id,
                'account_activated' => $account->charges_enabled,
            ],
            [
                'account_email' => $account->email,
            ]
        );

        $whiteLabel->update([
            'connected_account_id' => $connectedAccount->id,
        ]);

        return back()->with('flash', [
            'url' => $accountLink->url
        ]);
    }

    public function stripeConnectRefresh(WhiteLabel $whiteLabel, $callFrom)
    {
        $stripe = new StripeClient(config('services.stripe.secret_key'));

        return back();
    }


    public function stripeConnectCallback(WhiteLabel $whiteLabel, $callFrom)
    {
        $this->authorize('managePayments', $whiteLabel);

        $stripe = new StripeClient(config('services.stripe.secret_key'));

        $connectedAccount = $whiteLabel->connectedAccount()->latest()->first();

        try {
            $stripeAccount = $stripe->accounts->retrieve($connectedAccount->stripe_account_id);
        }catch (ApiErrorException $e){
            return redirect()->route('white-label.onboarding.payment-setup', [$whiteLabel])->with('flash', [
                'message' => 'Stripe account not found! Please try again.',
                'type' => 'error'
            ]);
        }

        $connectedAccount->update([
            'account_activated' => ($stripeAccount->charges_enabled && $stripeAccount->payouts_enabled) ?? false,
            'account_email' => $stripeAccount->email,
            'country' => $stripeAccount->country,
            'currency' => $stripeAccount->default_currency,
            'account_activated_at' => Carbon::createFromTimestamp($stripeAccount->created),
            'billing_details->stripe->bank_account->id' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->id : null,
            'billing_details->stripe->bank_account->last4' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->last4 : null,
            'billing_details->stripe->bank_account->bank_name' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->bank_name : null,
            'billing_details->stripe->bank_account->account_holder_name' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->account_holder_name : null,
            'billing_details->stripe->bank_account->account_holder_type' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->account_holder_type : null,
            'billing_details->stripe->bank_account->country' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->country : null,
            'billing_details->stripe->bank_account->routing_number' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->routing_number : null,
            'billing_details->stripe->bank_account->status' => isset($stripeAccount->external_accounts->data[0]) ? $stripeAccount->external_accounts->data[0]->status : null,
        ]);

        if (!$connectedAccount->account_activated) {
            $route = $callFrom === 'whitelabel_onboarding' ? 'white-label.onboarding.payment-setup' : 'white-label.payment.settings';
            return redirect()->route($route, [$whiteLabel])->with('flash', [
                'message' => 'Stripe account not activated! Please activate your account before proceeding!',
                'type' => 'warning'
            ]);
        }

        return redirect()->route('white-label.onboarding.payment-setup', [$whiteLabel])->with('flash', [
            'message' => 'Stripe account connected successfully!',
            'type' => 'success'
        ]);
    }

    public function checkAccountIsActivated(WhiteLabel $whiteLabel, Request $request)
    {
        $this->authorize('managePayments', $whiteLabel);

        $stripe = new StripeClient(config('services.stripe.secret_key'));

        $connectedAccount = $whiteLabel->connectedAccount()->latest()->first();

        try {
            $stripeAccount = $stripe->accounts->retrieve($connectedAccount->stripe_account_id);
        }catch (ApiErrorException $e){
            return [
                'status' => false,
                'message' => 'Stripe account not found! Please try again.',
                'account_activated' => false
            ];
        }

        $connectedAccount->update([
            'account_activated' => ($stripeAccount->charges_enabled && $stripeAccount->payouts_enabled) ?? false,
        ]);

        return [
            'account_activated' => ($stripeAccount->charges_enabled && $stripeAccount->payouts_enabled) ?? false,
            'message' => ($stripeAccount->charges_enabled && $stripeAccount->payouts_enabled)
                ? 'Stripe account connected successfully!'
                : 'Stripe account not activated! Please wait while your account is connecting.',
        ];
    }

    public function upgradeSubscription(Request $request)
    {
        $request->validate([
            'subscription_product' => 'required|exists:subscription_products,uuid',
        ]);

        $whiteLabel = team()->ownedWhiteLabel;

        $this->authorize('managePayments', $whiteLabel);

        // Set Stripe API Key
        Stripe::setApiKey(config('services.stripe.secret_key'));

        $subscriptionProduct = SubscriptionProduct::where('uuid', $request->input('subscription_product'))->firstOrFail();

        $currentSubscription = team()->subscriptions()->where('uuid', $request->input('active_subscription_product'))
            ->where('service_type', BillingServices::WhiteLabelSubscription)->active()->first();


        $cart = CartForm::create([
            'model' => $subscriptionProduct->service_type->getServiceModel(),
            'email'=> team()->email,
            'status'=> CartFormStatuses::Pending,
            'service' => $subscriptionProduct->service_type,
            'subscription_product_id' => $subscriptionProduct->id
        ]);

        $subscription = Subscription::create([
            'customer' => team()->activePaymentMethod()->first()->customer_id,
            'items' => [
                [
                    'price' => $subscriptionProduct->stripe_price_id
                ],
            ],
            'default_payment_method' => team()->activePaymentMethod()->first()->getPaymentMethod(),
        ]);

        $stripeInvoice = \Stripe\Invoice::retrieve($subscription->latest_invoice);

        $paymentIntent = PaymentIntent::retrieve($stripeInvoice->payment_intent);

        $invoice = InvoiceGenerator::cartForm($cart)
            ->team(team())
            ->ignoreStripePIGeneration()
            ->source(InvoiceSourceEnum::WhitelabelPurchase)
            ->paymentMethod(team()->activePaymentMethod()->first())
            ->title('Whitelabel Purchase: ' . $subscriptionProduct->name)
            ->description('Whitelabel Purchase: ' . $subscriptionProduct->name)
            ->amount($subscriptionProduct->price)
            ->status(BillingStatus::Paid)
            ->currency(BillingCurrency::USD)
            ->generate();

        $invoice->update([
            'meta->whitelabel_id' => $whiteLabel->id,
            'meta->subscription_product_id' => $subscriptionProduct->id,
            'meta->stripe_subscription_id' => $subscription->id,
        ]);

        if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
            $invoice->setStatusRequiresAction();

            $cart->update([
                'status' => CartFormStatuses::Pending,
                'meta' => [
                    'subscription_id' => $subscription->id,
                    'stripe_invoice_id' => $subscription->latest_invoice,
                    'invoice_id' => $invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => PaymentIntent::STATUS_REQUIRES_ACTION,
                ]
            ]);

            return redirect()->route('stripe.subscription-product.checkout.requires3dsecure', [
                'affiliateId' => null,
                'paymentIntentId' => $paymentIntent->id,
                'cartId' => $cart->id,
                'invoiceId' => $invoice->id,
                'nextRoute' => 'white-label.onboarding.brand-setup',
                'routeParam' => [
                    'model' => WhiteLabel::class,
                    'id' => $whiteLabel->id
                ],
                'subscriptionProductId' => $subscriptionProduct->id,
                'stripeSubscriptionId' => $subscription->id,
            ]);
        }

        if ($subscription->status === Subscription::STATUS_ACTIVE) {
            $cart->update([
                'status' => CartFormStatuses::Paid,
                'meta' => [
                    'subscription_id' => $subscription->id,
                    'stripe_invoice_id' => $subscription->latest_invoice,
                    'invoice_id' => $invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => PaymentIntent::STATUS_SUCCEEDED,
                ]
            ]);

            $whiteLabel->update([
                'payment_info' => [
                    'invoice_id' => $invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => PaymentIntent::STATUS_SUCCEEDED,
                ],
                'billing_activated_from' => now()
            ]);

            $subscriptionProduct->teams()->attach(team(), [
                'team_id' => team()->id,
                'subscription_product_id' => $subscriptionProduct->id,
                'stripe_subscription_id' => $subscription->id,
            ]);


            // Cancel the subscription
            Subscription::update($currentSubscription->pivot->stripe_subscription_id, [
                'cancel_at_period_end' => true
            ]);

            // Remove the subscription from the team or pivot
            $currentSubscription->pivot->delete();

            // affiliate commission
            if ($invoice->type->isAffiliatable()) {
                if ($invoice?->team?->promoter && !$invoice->promoter_id) {
                    if(!$paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED){
                        Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_intent_status' => $paymentIntent->status,
                        ]);
                    }else{
                        $invoice->handleAffiliateCommission();
                    }
                }
            }

            // Utm sales tracking
            if(Utm::ENABLE_UTM_CALCULATION) {
                if (!($paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED)) {
                    Log::warning('Failed to take payment. Utm sales tracking was not generated', [
                        'invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'payment_intent_id' => $paymentIntent->id,
                        'payment_intent_status' => $paymentIntent->status,
                    ]);
                } else {
                    $invoice->team->addUtmSourceSell($invoice->amount, $invoice);
                }
            }

            return redirect()->route('white-label.onboarding.brand-setup', [$whiteLabel])->with('flash', [
                'message' => 'Payment successful!',
                'type' => 'success'
            ]);
        }

        return back()->with('flash', [
            'message' => 'Payment failed! Please try again.',
            'type' => 'error'
        ]);
    }

    public function savePublishableKey(Request $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        $this->authorize('managePayments', $whiteLabel);

        $connectedAccount = $whiteLabel->connectedAccount;

        if(!$connectedAccount){
            return back()->with('flash', [
                'message' => 'Stripe account not found! Please connect your stripe account first.',
                'type' => 'error'
            ]);
        }

        if($request->get('pb_key')){
            $connectedAccount->update([
                'publishable_key' => $request->get('pb_key')
            ]);
        }

        return back()->with('flash', [
            'message' => 'Publishable key saved successfully!',
            'type' => 'success'
        ]);
    }
}
