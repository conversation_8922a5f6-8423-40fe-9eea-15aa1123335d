<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Cloudflare\PurgeCloudflareEdgeCacheJob;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CloudflareEdgeCachePluginController extends Controller
{
    /**
     * Verify and process the purge request from WordPress plugin
     *
     * @param  Request  $request
     * @return array
     */
    public function purge(Request $request)
    {
        Log::info('CloudflareEdgeCache Purge Request: ' . json_encode($request->all()));

        try {
            // Validate the request
            if (empty($request->input('auth_token')) || empty($request->input('verify_token'))) {
                return ['success' => false, 'message' => 'Missing required tokens'];
            }

            // Decode the site ID from the auth token
            $siteId = hashid_decode($request->input('auth_token'));
            if (!$siteId) {
                return ['success' => false, 'message' => 'Invalid auth token'];
            }

            // Find the site
            $site = Site::find($siteId);
            if (!$site) {
                return ['success' => false, 'message' => 'Site not found'];
            }

            // Verify the token
            try {
                $expectedToken = "xCloudCloudflareEdgeCache:{$site->server_id}:{$site->id}";
                if (decrypt($request->input('verify_token')) !== $expectedToken) {
                    return ['success' => false, 'message' => 'Invalid verification token'];
                }
            } catch (\Exception $e) {
                Log::error('Failed to decrypt verify token: ' . $e->getMessage());
                return ['success' => false, 'message' => 'Invalid verification token'];
            }

            // Check if edge cache is enabled for this site
            if (!$site->getMeta('cloudflare_edge_cache.enable')) {
                return ['success' => false, 'message' => 'Cloudflare Edge Cache is not enabled for this site'];
            }

            // Dispatch job to purge the Cloudflare cache
            PurgeCloudflareEdgeCacheJob::dispatchForSite($site);

            Log::info("Purge Cloudflare Edge Cache requested from WordPress plugin for site: {$site->name}");

            return [
                'success' => true,
                'message' => 'Cache purge request has been queued successfully',
                'site_id' => $site->id,
                'site_url' => $site->site_url,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to process Cloudflare Edge Cache purge request: ' . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while processing the request'];
        }
    }
}
