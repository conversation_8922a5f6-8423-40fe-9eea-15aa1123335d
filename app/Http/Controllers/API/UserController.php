<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Server\SitesUpdateJob;
use App\Jobs\TeamSitesUpdateJob;
use App\Models\Server;
use App\Models\Team;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    //Get user all teams
    public function getTeams(Request $request)
    {
        $teams = Team::without(['activePlan','tags'])->whereUserId($request->user()->id)->orWhereHas('memberships', function ($query) use ($request) {
            $query->where('user_id', $request->user()->id);
        })->orderBy('name')->get();

        return response()->json($teams);
    }

    public function checkUser(Request $request)
    {
//        $request->validate([
//            'email' =>
//                'required|email|unique:users,email'
//        ]);

        $whiteLabel = currentWhiteLabel();

        $request->validate([
            'email' => [
                'required',
                'email',
                // Apply 'unique:users,email' only if the user is not associated with a whitelabel
                Rule::when(!$whiteLabel, [
                    'unique:users,email'
                ]),
                // Apply the unique rule with a 'white_label_id' condition if the user is associated with a whitelabel
                Rule::when((bool) $whiteLabel, function ($rule) use ($whiteLabel) {
                    return Rule::unique('users', 'email')
                        ->where('white_label_id', $whiteLabel->id);
                })
            ],
        ]);
        return response()->json(['message' => 'User not exists']);
    }

    public function getUserEmail(Request $request)
    {
        if (!$request->expectsJson()) {
            return redirect()->back()->with('flash', ['message' => "Invalid request.", 'type' => 'error']);
        }

        return response()->json(['email' => auth()->user()->email]);
    }

    public function updateUserEmail(Request $request)
    {
        $whiteLabel = currentWhiteLabel();

        $request->validate([
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                // Apply 'unique:users,email' only if the user is not associated with a whitelabel
                Rule::when(!$whiteLabel, [
                    'unique:users,email,' . auth()->id()
                ]),
                // Apply the unique rule with a 'white_label_id' condition if the user is associated with a whitelabel
                Rule::when((bool) $whiteLabel, function ($rule) use ($whiteLabel, $request) {
                    // dd($whiteLabel, $rule, $request->all());
                    return Rule::unique('users', 'email')
                        ->where('white_label_id', $whiteLabel->id)
                        ->ignore(auth()->id());
                })
            ],
        ]);

        try {
            $user = auth()->user();
            $user->update([
                'email' => $request->email
            ]);

            return response()->json(['message' => 'Email updated.']);
        } catch (Exception $e) {
            return response()->json(['message' => 'Email cannot updated.'], 400);
        }
    }

    public function scanVulnerability()
    {
        $this->authorize('vulnerabilityScan', team());
        $job = [];
        team()->servers()
            ->accessFilter()
            ->where([
                'is_provisioned' => true,
                'is_connected' => true
            ])
            ->whereHas('vulnerabilitySetting')
            ->each(function (Server $server) use (&$job){
                $job[] = new SitesUpdateJob($server);
            });
        $job[] = new TeamSitesUpdateJob(team());
        Bus::chain($job)
            ->dispatch();
        return response()->json(['message' => 'Vulnerability Scan started.']);
    }
}
