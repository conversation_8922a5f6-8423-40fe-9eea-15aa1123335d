<?php

namespace App\Http\Controllers\API\Addons;

use App\Addons\AddonsManager;
use App\Addons\Enum\AddonTypeEnum;
use App\Addons\Enum\MailboxEnums\EmailAccountStatusEnum;
use App\Addons\MailboxAddon\MailboxAddonService;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Addons\MailboxCreateRequest;
use App\Models\Addons\EmailAccount;
use App\Models\Addons\MailboxDomain;
use App\Services\Mailbox\QboxMailService;
use Illuminate\Http\Request;
use Arr;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;

class MailboxAddonsAPIController extends Controller
{
    public function create(MailboxCreateRequest $request)
    {
        // TODO: Add policy

        $manager = new AddonsManager();
        $addon = $manager->resolve(AddonTypeEnum::MAILBOX);
        $response = $addon->create($request->validated());

        return redirect()->back()->with('flash', [
            'message' =>  Arr::get($response, 'message'),
            'status' =>  Arr::get($response, 'status'),
            'errors' => Arr::get($response, 'errors'),
        ]);
    }

    public function verifyMailboxDnsRecords(Request $request, MailboxDomain $mailboxDomain)
    {
        // TODO: Add policy

        dd(__METHOD__);
        $dnsOwnershipVerified = (new QboxMailService())->checkDnsOwnership(
            domainCode: $mailboxDomain->domain_code,
            domain: $mailboxDomain->domain,
            ip: '*************'
        );

        if(!$dnsOwnershipVerified){
            $mxRecordVerified = false;
        }else{
            $mxRecordVerified = (new QboxMailService())->checkMxRecord(
                domainCode: $mailboxDomain->domain_code,
            );

            (new MailboxAddonService())->attachEmailAccounts($mailboxDomain);

            $mailboxDomain->markAsActive();
        }

        return response()->json([
            'dns_ownership_verified' => $dnsOwnershipVerified,
            'mx_record_verified' => true,
            'success' => $dnsOwnershipVerified,
            'message' => $dnsOwnershipVerified ? 'DNS Ownership and MX Record verified successfully.' : 'DNS Ownership and MX Record verification failed.',
        ]);
    }
}
