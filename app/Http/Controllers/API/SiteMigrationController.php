<?php

namespace App\Http\Controllers\API;

use App\Enums\SiteType;
use App\Enums\SiteMigrationStatus;
use App\Enums\SiteStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\DomainStoreRequest;
use App\Http\Requests\SiteMigration\BackupSourceRequest;
use App\Models\GitSiteMigration;
use App\Models\ManualSiteMigration;
use App\Models\Server;
use App\Models\Site;
use App\Models\AutoSiteMigration;
use App\Models\SiteBackupRestoreMigration;
use App\Models\SiteMigration;
use App\Models\SshKeyPair;
use App\Models\SslCertificate;
use App\Models\StorageProvider;
use App\Rules\DatabasePassword;
use App\Rules\DoManagedDatabaseValidation;
use App\Rules\SiteUserRule;
use App\Rules\ValidateCommaSeperatedMails;
use App\Scripts\Site\SetupDeploySShKey;
use App\Scripts\Site\GitConnectionCheck;
use App\Scripts\TestDatabaseConnection;
use App\Services\Database\DatabaseProvider;
use App\Services\Migration\MigrationConnector;
use App\Models\PhpVersion;
use App\Validator\Domain;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class SiteMigrationController extends Controller
{
    /**
     * @param  Request  $request
     * @param  SiteMigration|null  $siteMigration
     * @return JsonResponse|RedirectResponse
     */
    function storeDestination(Request $request, ?SiteMigration $siteMigration = null)
    {
        if ($siteMigration) {
            $this->authorize('hasAccessToModifyDestination', $siteMigration);
            if ($siteMigration->isAuto()){
                #white label can't clone site
                abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $siteMigration->server->team->white_label_id == team()->ownedWhiteLabel?->id),404);
            }
        }

        $data = $this->validate($request, ['destination_server' => ['required', 'integer', 'exists:servers,id,team_id,'.team()->id]]);

        $server = Server::findOrFail($data['destination_server']);

        $this->authorize('view', $server);

        if ($siteMigration) {
            $siteMigration = $siteMigration instanceof SiteMigration ? $siteMigration : SiteMigration::findOrFail($siteMigration);
            $siteMigration->update([
                'server_id' => $server->id,
                'form->'.SiteMigration::DESTINATION => ['server' => $server->id],
                'form->next_step' => SiteMigration::DOMAINS,
            ]);
            if ($siteMigration->isGit()) {
                $siteMigration->stepGetRouteNames = app(GitSiteMigration::class)->stepGetRouteNames;
            }
            if ($siteMigration->isRestoreBackup()) {
                $siteMigration->stepGetRouteNames = app(SiteBackupRestoreMigration::class)->stepGetRouteNames;
            }
            $route = $siteMigration->getStepRouteNames()[SiteMigration::DOMAINS] ?? app(AutoSiteMigration::class)->getStepRouteNames()[SiteMigration::DOMAINS];

            if ($siteMigration->isManual()) {
                $route = app(ManualSiteMigration::class)->getStepRouteNames()[SiteMigration::DOMAINS];
            }
        } else {
            if (Str::contains($request->headers->get('referer'), 'gitMigrate')) {
                $type = SiteMigration::GIT;
            }
            else if (Str::contains($request->headers->get('referer'), 'backup-restore')) {
                $type = SiteMigration::BACKUP_RESTORE;
            } else {
                $type = Str::contains($request->headers->get('referer'), 'manual') ? SiteMigration::MANUAL : SiteMigration::DEFAULT;
            }

            $route = app(AutoSiteMigration::class)->getStepRouteNames()[SiteMigration::DOMAINS];

            if ($type == SiteMigration::MANUAL) {
                $route = app(ManualSiteMigration::class)->getStepRouteNames()[SiteMigration::DOMAINS];
            }
            if ($type == SiteMigration::GIT) {
                $route = app(GitSiteMigration::class)->getStepRouteNames()[SiteMigration::DOMAINS];
            }
            if ($type == SiteMigration::BACKUP_RESTORE) {
                $route = app(SiteBackupRestoreMigration::class)->getStepRouteNames()[SiteMigration::DOMAINS];
            }
        }
        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::DESTINATION,
            'status' => SiteMigrationStatus::FILLING->value,
        ]) : redirect()->route($route, [
            'server' => $server->id,
            'siteMigration' => $siteMigration->id ?? 'new',
            'siteType' => $request->get('site_type',SiteType::WORDPRESS) ?? null,
        ]);
    }


    /**
     * @throws AuthorizationException
     * @throws BindingResolutionException
     */
    function storeAutoDomain(Request $request, Server $server)
    {
        #white label can't clone site
        abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $server->team->white_label_id == team()->ownedWhiteLabel?->id),404);
        $this->authorize('addSite', $server);

        $siteMigration = $request->get('site_migration_id') ? SiteMigration::findOrFail($request->get('site_migration_id')) : null;

        // Convert the array into a string suitable for the regular expression
        $reservedDomainsPattern = implode('|', array_map('preg_quote', getReservedDomains()));

        if ($request->get('domain_parking_method') === 'staging_env') {
            validator()->make(['existing_site_url' => get_domain($request->get('existing_site_url'))], [
                'existing_site_url' => [
                    'required',
                    'max:255',
                    new Domain,
                    $request->get('domain_parking_method') === 'migrate_into_new_domain'
                        ? 'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/'
                        : null
                ]
            ])->validate();
        }

        $domainName = '';
        if ($request->get('domain_parking_method') !== 'staging_env') {
            $domainName = $request->get('domain_name');
        }

        $request->request->add(['domain_name' => $domainName ?? null]); // updating domain_name for request validation

        $data = $this->validate($request, [
            'existing_site_url' => 'required|string|max:255|url',
            'domain_parking_method' => 'required|string|in:staging_env,migrate_into_new_domain',
            'site_title' => 'required_if:domain_parking_method,migrate_into_new_domain|string|max:255',
            'domain_name' => [
                'required_if:domain_parking_method,migrate_into_new_domain',
                'nullable',
                'max:255',
                new Domain,
                Rule::unique('sites', 'name')
                    ->where('server_id', $server->id)
                    ->ignore(optional($siteMigration)->site_id),
                $request->get('domain_parking_method') === 'migrate_into_new_domain'
                    ? 'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/'
                    : null
            ],
            'ssl_provider' => [
                'nullable', 'in:xcloud,custom,cloudflare', function ($attribute, $value, $fail) use ($server, $request, $domainName) {
                    if ($value == 'xcloud' && !$server->hasDnsRecord($domainName)) {
                        $fail(__('dns.verification_failed'));
                    }
                }
            ],
            'ssl_certificate' => 'required_if:ssl_provider,custom|string|nullable',
            'ssl_private_key' => 'required_if:ssl_provider,custom|string|nullable'
        ]);

        if($request->get('domain_active_on_cloudflare')){
            if(canGenerateSslCertificateOnCloudflare($request->get('domain_name'))){
                $data['ssl_provider'] = SslCertificate::PROVIDER_CLOUDFLARE;
            }else{
                // Add data to the request
                $data['ssl_provider'] = SslCertificate::PROVIDER_XCLOUD; // অক্ষম cloudflare can't handle ssl for this multi level domain
            }

            $data['cloudflare_account_id'] = $request->get('cloudflare_account_id');
            $data['cloudflare_zone_id'] = $request->get('cloudflare_zone_id');
            $data['subdomain'] = $request->get('subdomain');
            $data['site_name'] = $request->get('site_name');
        }
        $site = Site::updateOrCreate(
            ['id' => optional($siteMigration)->site_id],
            [
                'name' => 'migration@'.$server->name_slug,
                'title' => 'migration@'.$server->name_slug,
                'type' => SiteType::WORDPRESS,
                'server_id' => $server->id,
                'status' => SiteStatus::MIGRATION_INIT,
                'php_version' => PhpVersion::DEFAULT,
                'redis_password' =>  $server->isRedisSeven() ? Str::random(32) : null
            ]
        );
        // update site domain with staging site name
        if ($request->get('domain_parking_method') === 'staging_env') {
            $domainName = $site->generateStagingSiteName();
            $site->updateDomain($domainName);
        }

        $siteMigration = AutoSiteMigration::updateOrCreate(['site_id' => $site->id], [
            'type' => SiteMigration::DEFAULT,
            'user_id' => auth()->id(),
            'team_id' => team()->id,
            'server_id' => $server->id,
            'existing_site_url' => rtrim($data['existing_site_url'], '/'),
            'domain_name' => rtrim($domainName, '/'),
            'form->'.SiteMigration::DOMAINS => $data,
            'form->next_step' => SiteMigration::SETTINGS,
        ]);

        $siteMigration->updateSite();

        if($request->get('domain_active_on_cloudflare')){
            $site->update([
                'ssl_provider' => $data['ssl_provider'],
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::DOMAINS,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.settings', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    function storeGitDomain(DomainStoreRequest $request, Server $server)
    {
        $this->authorize('addSite', $server);
        $siteMigration = $request->get('site_migration_id') ? SiteMigration::findOrFail($request->get('site_migration_id')) : null;

        $domainName = '';

        if ($request->get('domain_parking_method') !== 'staging_env') {
            $domainName = $request->get('domain_name');
        }

        if($request->get('domain_active_on_cloudflare')){
            if(canGenerateSslCertificateOnCloudflare($request->get('domain_name'))){
                $request['ssl_provider'] = SslCertificate::PROVIDER_CLOUDFLARE;
            }else{
                // Add data to the request
                $request['ssl_provider'] = SslCertificate::PROVIDER_XCLOUD; // অক্ষম cloudflare can't handle ssl for this multi level domain
            }
            $request['cloudflare_account_id'] = $request->get('cloudflare_account_id');
            $request['cloudflare_zone_id'] = $request->get('cloudflare_zone_id');
            $request['subdomain'] = $request->get('subdomain');
            $request['site_name'] = $request->get('site_name');
        }

        $siteType = $siteMigration?->site?->type->value
            ?? $request->input('site_type', SiteType::WORDPRESS->value);

        if (!in_array($siteType, SiteType::asValue(), true)) {
            $siteType = SiteType::WORDPRESS->value;
        }

        $site = Site::updateOrCreate(
            ['id' => optional($siteMigration)->site_id],
            [
                'name' => 'migration@'.$server->name_slug,
                'title' => 'migration@'.$server->name_slug,
                'type' => $siteType,
                'server_id' => $server->id,
                'status' => SiteStatus::MIGRATION_INIT,
                'php_version' => PhpVersion::DEFAULT,
                'redis_password' =>  $server->isRedisSeven() ? Str::random(32) : null,
                'meta' => [
                    'is_git' => true
                ]
            ]
        );

        // update site domain with staging site name
        if ($request->get('domain_parking_method') === 'staging_env') {
            $domainName = $site->generateStagingSiteName();
            $site->updateDomain($domainName);
        }

        $siteMigration = GitSiteMigration::updateOrCreate(['site_id' => $site->id], [
            'type' => SiteMigration::GIT,
            'user_id' => auth()->id(),
            'team_id' => team()->id,
            'server_id' => $server->id,
            'domain_name' => $domainName,
            'form->'.SiteMigration::DOMAINS => array_merge($request->validated(),
                $request->get('domain_active_on_cloudflare') ? [
                    'ssl_provider' => $request['ssl_provider'],
                    'cloudflare_account_id' => $request->get('cloudflare_account_id'),
                    'cloudflare_zone_id' => $request->get('cloudflare_zone_id'),
                    'subdomain' => $request->get('subdomain'),
                    'site_name' => $request->get('site_name'),
                ] : []
            ),
            'form->next_step' => SiteMigration::SETTINGS,
        ]);

        $siteMigration->updateSite();

        if($request->get('domain_active_on_cloudflare')){
            $site->update([
                'ssl_provider' => $request['ssl_provider'],
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::DOMAINS,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.settings', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    function storeBackupRestoreDomain(DomainStoreRequest $request, Server $server)
    {
        $this->authorize('addSite', $server);
        $siteMigration = $request->get('site_migration_id') ? SiteMigration::findOrFail($request->get('site_migration_id')) : null;

        $domainName = '';
        if ($request->get('domain_parking_method') !== 'staging_env') {
            $domainName = $request->get('domain_name');
        }

        if($request->get('domain_active_on_cloudflare')){
            if(canGenerateSslCertificateOnCloudflare($request->get('domain_name'))){
                $request['ssl_provider'] = SslCertificate::PROVIDER_CLOUDFLARE;
            }else{
                // Add data to the request
                $request['ssl_provider'] = SslCertificate::PROVIDER_XCLOUD; // অক্ষম cloudflare can't handle ssl for this multi level domain
            }
            $request['cloudflare_account_id'] = $request->get('cloudflare_account_id');
            $request['cloudflare_zone_id'] = $request->get('cloudflare_zone_id');
            $request['subdomain'] = $request->get('subdomain');
            $request['site_name'] = $request->get('site_name');
        }

        $site = Site::updateOrCreate(
            ['id' => optional($siteMigration)->site_id],
            [
                'name' => 'migration@'.$server->name_slug,
                'title' => 'migration@'.$server->name_slug,
                'type' => SiteType::WORDPRESS,
                'server_id' => $server->id,
                'status' => SiteStatus::MIGRATION_INIT,
                'php_version' => PhpVersion::DEFAULT,
                'redis_password' =>  $server->isRedisSeven() ? Str::random(32) : null,
            ]
        );

        // update site domain with staging site name
        if ($request->get('domain_parking_method') === 'staging_env') {
            $domainName = $site->generateStagingSiteName();
            $site->updateDomain($domainName);
        }

        $siteMigration = SiteBackupRestoreMigration::updateOrCreate(['site_id' => $site->id], [
            'type' => SiteMigration::BACKUP_RESTORE,
            'user_id' => auth()->id(),
            'team_id' => team()->id,
            'server_id' => $server->id,
            'domain_name' => $domainName,
            'form->'.SiteMigration::DOMAINS => array_merge($request->validated(),
                $request->get('domain_active_on_cloudflare') ? [
                    'ssl_provider' => $request['ssl_provider'],
                    'cloudflare_account_id' => $request->get('cloudflare_account_id'),
                    'cloudflare_zone_id' => $request->get('cloudflare_zone_id'),
                    'subdomain' => $request->get('subdomain'),
                    'site_name' => $request->get('site_name'),
                ] : []
            ),
            'form->next_step' => SiteBackupRestoreMigration::SOURCE,
        ]);
        $siteMigration->updateSite();

        if($request->get('domain_active_on_cloudflare')){
            $site->update([
                'ssl_provider' => $request['ssl_provider'],
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::DOMAINS,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.backup_restore.source', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    function storeManualDomain(Request $request, ?Server $server)
    {
        $this->authorize('addSite', $server);

        $siteMigration = $request->get('siteMigration') ? SiteMigration::findOrFail($request->get('siteMigration')) : null;

        $domainName = '';
        if ($request->get('domain_parking_method') !== 'staging_env') {
            $domainName = $request->get('domain_name');
        }

        $request->request->add(['domain_name' => $domainName ?? null]); // updating domain_name for request validation

        // Convert the array into a string suitable for the regular expression
        $reservedDomainsPattern = implode('|', array_map('preg_quote', getReservedDomains()));
        $data = $this->validate($request, [
            'domain_parking_method' => 'required|string|in:staging_env,migrate_into_new_domain',
            'site_title' => 'required_if:domain_parking_method,migrate_into_new_domain|nullable|string|max:255',
            'domain_name' => [
                'required_if:domain_parking_method,migrate_into_new_domain',
                'max:255',
                new Domain,
                Rule::unique('sites', 'name')
                    ->where('server_id', $server->id)
                    ->ignore(optional($siteMigration)->site_id),
                $request->get('domain_parking_method') === 'migrate_into_new_domain'
                    ? 'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/'
                    : null
            ],
            'ssl_provider' => [
                'nullable', 'in:xcloud,custom,cloudflare', function ($attribute, $value, $fail) use ($server, $request) {
                    if ($value == 'xcloud' && !$server->hasDnsRecord($request->get('domain_name'))) {
                        $fail(__('dns.verification_failed'));
                    }
                }
            ],
            'ssl_certificate' => 'required_if:ssl_provider,custom|string|nullable',
            'ssl_private_key' => 'required_if:ssl_provider,custom|string|nullable'
        ],[
            'site_title.required_if' => 'The site title field is required when migrating into a new domain.',
            'domain_name.required_if' => 'The domain name field is required when migrating into a new domain.',
        ]);

        if($request->get('domain_active_on_cloudflare')){
            if(canGenerateSslCertificateOnCloudflare($request->get('name'))){
                $data['ssl_provider'] = SslCertificate::PROVIDER_CLOUDFLARE;
            }else{
                $data['ssl_provider'] = SslCertificate::PROVIDER_XCLOUD; // অক্ষম cloudflare can't handle ssl for this multi level domain
            }
            $data['cloudflare_account_id'] = $request->get('cloudflare_account_id');
            $data['cloudflare_zone_id'] = $request->get('cloudflare_zone_id');
            $data['subdomain'] = $request->get('subdomain');
            $data['site_name'] = $request->get('site_name');
        }

        $siteType = $siteMigration?->site?->type->value
            ?? $request->input('site_type', SiteType::WORDPRESS->value);

        if (!in_array($siteType, SiteType::asValue(), true)) {
            $siteType = SiteType::WORDPRESS->value;
        }

        $site = Site::updateOrCreate(
            ['id' => optional($siteMigration)->site_id],
            [
                'name' => 'migration@'.$server->name_slug,
                'title' => 'migration@'.$server->name_slug,
                'type' => $siteType,
                'server_id' => $server->id,
                'status' => SiteStatus::MIGRATION_INIT,
                'php_version' => PhpVersion::DEFAULT,
                'redis_password' => $server->isRedisSeven() ? Str::random(32) : null,
            ]);

        // update site domain with staging site name
        if ($request->get('domain_parking_method') === 'staging_env') {
            $domainName = $site->generateStagingSiteName();
            $site->updateDomain($domainName);
        }

        $siteMigration = ManualSiteMigration::updateOrCreate(['site_id' => $site->id], [
            'type' => SiteMigration::MANUAL,
            'user_id' => auth()->id(),
            'team_id' => team()->id,
            'server_id' => $server->id,
            'form->'.SiteMigration::DESTINATION => ['server' => $server->id],
            'domain_name' => $domainName,
            'form->'.SiteMigration::DOMAINS => $data,
            'form->next_step' => SiteMigration::SETTINGS,
        ]);


        $siteMigration->updateSite();

        if($request->get('domain_active_on_cloudflare')){
            $site->update([
                'ssl_provider' => $data['ssl_provider'],
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::DOMAINS,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.settings', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    function storeSettings(Request $request, SiteMigration $siteMigration)
    {
        if ($siteMigration->isAuto()){
            #white label can't clone site
            abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $siteMigration->server->team->white_label_id == team()->ownedWhiteLabel?->id),404);
        }
        $this->authorize('canSetupSettings', $siteMigration);
        $data = Validator::make(
            $request->all(),
            [
                'php_version' => ['required', PhpVersion::asRule()],
                'site_user' => SiteUserRule::rules($siteMigration->server_id, $siteMigration->site_id),
                'database_provider' => ['required', DatabaseProvider::asRule(acceptNull: $siteMigration->isGit() || !$siteMigration->site->isWordpress())],
                //database_name is request if database_provider is not 'null'
                'database_name' => [ Rule::when(fn () => $request->input('database_provider') === DatabaseProvider::NULL, 'nullable','required|string|max:32|lowercase|alpha_dash')],
                'database_user' => [
                    Rule::when(fn () => $request->input('database_provider') === DatabaseProvider::NULL, 'nullable','required|string|max:32|lowercase|alpha_dash'),
                ],
                'database_password' => [
                    Rule::when(fn () => $request->input('database_provider') === DatabaseProvider::NULL, 'nullable',DatabasePassword::rules()),
                ],
                'database_host' => [
                    Rule::when(fn () => $request->input('database_provider') === DatabaseProvider::NULL, 'nullable','required_if:database_provider,custom|string|max:255'),
                ],
                'database_port' => [
                    Rule::when(fn () => $request->input('database_provider') === DatabaseProvider::NULL, 'nullable','required_if:database_provider,custom|max:80'),
                ],

                'enable_full_page_cache' => 'nullable|boolean',
                'enable_redis_object_caching' => 'nullable|boolean',
                'managed_database_mode' => '',
                'managed_database_options.do_cluster_name' => new DoManagedDatabaseValidation($request->input('database_provider'), $request->input('managed_database_mode')),
                'managed_database_options.do_cluster_size' => new DoManagedDatabaseValidation($request->input('database_provider'), $request->input('managed_database_mode')),
                'managed_database_options.do_cluster_node' => new DoManagedDatabaseValidation($request->input('database_provider'), $request->input('managed_database_mode')),
                'managed_database_options.do_cluster_region' => new DoManagedDatabaseValidation($request->input('database_provider'), $request->input('managed_database_mode')),
                'managed_database_options.do_cluster_id' => new DoManagedDatabaseValidation($request->input('database_provider'), $request->input('managed_database_mode')),
                'managed_database_options.do_existing_database' => new DoManagedDatabaseValidation($request->input('database_provider'), $request->input('managed_database_mode')),
                'web_root' => 'nullable|string|max:255|regex:/^[a-z0-9_-]+$/',
            ],
            [
                ...DatabasePassword::messages(),
                ...SiteUserRule::messages()
            ]
        )->validate();
        if ($request->input('database_provider') === DatabaseProvider::CUSTOM) {
            $testCustomDatabaseConnection = $siteMigration->server->runInline(new TestDatabaseConnection(
                $data['database_host'],
                $data['database_port'],
                $data['database_name'],
                $data['database_user'],
                $data['database_password'] ?? DatabasePassword::randomPassword()
            ))->successful();

            if (!$testCustomDatabaseConnection) {
                throw ValidationException::withMessages([
                    'db_connection_error' => 'Unable to connect to database. Please check your credentials.',
                ]);
            }
        }

        if ($siteMigration->isAuto()) {
            $prefixData = $this->validate($request, [
                'prefix' => ['required', 'ends_with:_', 'max:32',],
            ]);
            $data['prefix'] = $prefixData['prefix'];
        }
        $nextStep = $siteMigration->isManual() ? ManualSiteMigration::FILES : AutoSiteMigration::PLUGIN;

        $route = $siteMigration->isManual() ? 'site.migrate.files' : 'site.migrate.plugin';
        if (isset($data['web_root'])){
            $siteMigration->site->update([
                'web_root' => $data['web_root'],
            ]);
        }
        if ($siteMigration->isGit()) {
            $nextStep = GitSiteMigration::GIT_REPO;
            $route = 'site.migrate.git_repo';
        }
        if ($siteMigration->isRestoreBackup()) {
            $nextStep = SiteMigration::CONFIRM;
            $route = 'site.migrate.confirm';
        }

        $siteMigration->update([
            'form->'.SiteMigration::SETTINGS => $data,
            'form->next_step' => $nextStep,
        ]);
        $siteMigration->updateSite();

        $siteMigration->site->saveMeta('disable_search_engine_visibility', $request->get('disable_search_engine_visibility'));

        if ($request->boolean('enable_redis_object_caching')) {
            $siteMigration->site->update([
                'meta->has_redis_object_caching' => true,
            ]);
        }
        if ($request->boolean('enable_full_page_cache')) {
            $siteMigration->site->update([
                'meta->has_fullpage_caching' => true,
            ]);
        }
        if ($siteMigration->isGit() && is_null($siteMigration->site->ssh_key_pair_id)) {
            $sshKeyPair = SshKeyPair::createSiteDeployKey(keyName: Str::slug($siteMigration->site->name).'@xcloud');
            $siteMigration->site->update([
                'ssh_key_pair_id' => $sshKeyPair->id,
            ]);
            $siteMigration->site->runInline(new SetupDeploySShKey($siteMigration->site));
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::SETTINGS,
            'status' => $siteMigration->status,
        ]) : redirect()->route($route, [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function configurePlugin(Request $request, AutoSiteMigration $siteMigration)
    {
        #white label can't clone site
        abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $siteMigration->server->team->white_label_id == team()->ownedWhiteLabel?->id),404);
        $this->authorize('canUsePlugin', $siteMigration);

        $data = $this->validate($request, [
            'active_tab' => 'required|string|max:255',
            'confirmed' => 'required|accepted'
        ]);

        try {
            $response = (new MigrationConnector($siteMigration))->get('/abspath');
        } catch (Exception $e) {
            throw ValidationException::withMessages([
                'verification_message' => [
                    'Migration Token Verification failed! '.$e->getMessage()
                ],
            ]);
        }

        if (!isset($response['abspath'])) {
            throw ValidationException::withMessages([
                'verification_message' => [
                    'Migration Token Verification failed! Please make sure you have correctly copied and pasted
                    token in your source site’s '.title(AutoSiteMigration::WP_PLUGIN_SLUG).' plugin. Make sure you\'ve
                    the latest version of our plugin and read our documentation.'
                ],
            ]);
        }

        if (empty($response['plugin_version']) || version_compare($response['plugin_version'], AutoSiteMigration::WP_PLUGIN_VERSION, '<')) {
            throw ValidationException::withMessages([
                'verification_message' => [
                    "Outdated plugin version detected. Please make sure you have the latest version of our plugin installed on your source site."
                ],
            ]);
        }

        $status = $siteMigration->update([
            'form->'.AutoSiteMigration::PLUGIN => $data,
            'form->next_step' => AutoSiteMigration::DATABASE,
        ]);

        return $request->expectsJson() ? response()->json([
            'completed_step' => AutoSiteMigration::PLUGIN,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.auto.database', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function configureFiles(Request $request, ManualSiteMigration $siteMigration)
    {
        $this->authorize('canUseFiles', $siteMigration);

        $data = $this->validate($request, [
            'active_tab' => 'required|string|max:255',
            'file' => 'required',
        ], [
            'file.required' => 'Please upload your site as zip or tar here'
        ]);

        //start the migration here

        $status = $siteMigration->update([
            'form->'.ManualSiteMigration::FILES => $data,
            'form->next_step' => SiteMigration::DATABASE,
        ]);


        return $request->expectsJson() ? response()->json([
            'completed_step' => ManualSiteMigration::FILES,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.manual.database', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function configureGitInfo(Request $request, GitSiteMigration $siteMigration)
    {
        $this->authorize('canUseGitRepo', $siteMigration);

        $data = $this->validate($request, [
            'git_repository' => 'required|string|max:255|starts_with:git@|ends_with:.git',
            'git_branch' => 'required|string|max:255',
            'deploy_script' => 'required_if:run_after_deployment,1',
            'enable_push_deploy' => 'boolean',
            'run_after_deployment' => 'boolean',
        ]);

        // check git connection
        // $this->site,$this->site->sshKeyPair,$this->site->getMeta('git_info')

        $task = $siteMigration->site->runInline(
            new GitConnectionCheck(
                $siteMigration->site,
                $siteMigration->site->sshKeyPair,
                $request->input('git_repository'),
                $request->input('git_branch')
            )
        );

        if (!$task->successful()) {
            //throw git_repository validation error
            return throw ValidationException::withMessages([
                'git_repository' => 'Failed to establish a connection with the Git repository.',
            ]);
        }

        if (!Str::contains($task->output, $request->input('git_branch'))) {
            //throw git_repository validation error
            return throw ValidationException::withMessages([
                'git_branch' => 'The specified branch in the Git repository could not be located.',
            ]);
        }

        if ($request->filled('deploy_script')) {
            $siteMigration->site->saveMeta('deploy_script', $request->input('deploy_script'));
        }

        $siteMigration->site->saveMeta('git_info', Arr::except($data, ['deploy_script']));
        // start the migration here

        $siteMigration->update([
            'form->'.GitSiteMigration::GIT_REPO => $data,
            'form->next_step' => SiteMigration::CONFIRM,
        ]);


        return $request->expectsJson() ? response()->json([
            'completed_step' => ManualSiteMigration::FILES,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.confirm', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function setupDatabase(Request $request, SiteMigration $siteMigration)
    {
        if ($siteMigration->isAuto()){
            #white label can't clone site
            abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $siteMigration->server->team->white_label_id == team()->ownedWhiteLabel?->id),404);
        }
        $this->authorize('canSetupDatabase', $siteMigration);

        $data = $this->validate($request, [
            'database_migrate_type' => 'required|string|max:255'
        ]);

        $status = $siteMigration->update([
            'form->'.SiteMigration::DATABASE => $data,
            'form->next_step' => SiteMigration::CONFIRM,
        ]);

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::DATABASE,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.confirm', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function setupManualDatabase(Request $request, ManualSiteMigration $siteMigration)
    {
        $this->authorize('canSetupDatabase', $siteMigration);

        $data = $this->validate($request, [
            'active_tab' => 'required|string|max:255',
            'database' => Rule::requiredIf($siteMigration->site->isWordpress()),
        ], [
            'database.required' => 'Please upload your database as zip here'
        ]);

        $status = $siteMigration->update([
            'form->'.SiteMigration::DATABASE => $data,
            'form->next_step' => SiteMigration::CONFIRM,
        ]);

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::DATABASE,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.confirm', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function confirmMigration(Request $request, SiteMigration $siteMigration)
    {
        if ($siteMigration->isAuto()){
            #white label can't clone site
            abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $siteMigration->server->team->white_label_id == team()->ownedWhiteLabel?->id),404);
        }
        $this->authorize('confirmation', $siteMigration);

        $data = $this->validate($request, [
            'notification_mails' => ['nullable', new ValidateCommaSeperatedMails],
        ]);

        if (isset($data['notification_mails'])) {
            $data['notification_mails'] = is_array($data['notification_mails']) ? $data['notification_mails'] : explode(',', $data['notification_mails']);
        }

        $siteMigration->update([
            'form->'.SiteMigration::CONFIRM => $data,
            'notification_mails' => $data['notification_mails'] ?? null,
        ]);

        $siteMigration->startMigration();

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteMigration::CONFIRM,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.processing', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    public function uploadFile(Request $request, ManualSiteMigration $siteMigration)
    {
        $this->authorize('canUseFiles', $siteMigration);

        Validator::make($request->all(), [
            //only allow zip,wpress files
            'key' => ['required', 'string'],
            'extension' => ['nullable', 'in:zip,gz'], //add tar.gz
        ])->validate();

        //check if the file is zip or sql
        if (!in_array($request->input('extension'), ['zip', 'gz'])) {
            //throw validation error
            return response()->json(['error' => 'Only zip files are allowed.']);
        }
        $extension = $request->input('extension');
        if ($extension === 'gz') {
            $extension = 'tar.gz';
        }

        $file_name = Str::random().'.'.$extension;
        $file_path = 'site-migration-zips/'.$file_name;

        Storage::disk('s3')->copy($request->input('key'), $file_path);
        $file_url = Storage::disk('s3')->url($file_path);

        //save the migration file path to meta
        $siteMigration->saveMeta('migration_file_path', $file_path);
        $siteMigration->saveMeta('migration_file_name', $file_name);
        $siteMigration->saveMeta('migration_file_url', $file_url);

        return response()->json(['success' => 'You have successfully upload file.', 'file_path' => $file_path]);
    }


    public function uploadDatabase(Request $request, ManualSiteMigration $siteMigration)
    {
        $this->authorize('canSetupDatabase', $siteMigration);

        Validator::make($request->all(), [
            //only allow zip and sql files
            'key' => ['required', 'string'],
            'extension' => ['nullable', 'in:zip,sql'],
        ])->validate();

        //check if the file is zip or sql
        if (!in_array($request->input('extension'), ['zip', 'sql'])) {
            //throw validation error
            return response()->json(['error' => 'Only zip and sql files are allowed.']);
        }


        $file_name = Str::random().'.'.$request->input('extension');
        $file_path = 'site-migration-sqls/'.$file_name;

        Storage::disk('s3')->copy($request->input('key'), $file_path);
        $file_url = Storage::disk('s3')->url($file_path);

        //save the migration file path to meta
        $siteMigration->saveMeta('migration_database_path', $file_path);
        $siteMigration->saveMeta('migration_database_name', $file_name);
        $siteMigration->saveMeta('migration_database_url', $file_url);


        return response()->json(['success' => 'You have successfully upload file.', 'database_path' => $file_path]);
    }

    /**
     */
    public function storeBackupSource(SiteBackupRestoreMigration $siteMigration, BackupSourceRequest $request)
    {
        $data = $request->getSanitizeData();
        $nextStep = SiteBackupRestoreMigration::BACKUPS;
        $route = 'site.migrate.backup_restore.backups';
        $siteMigration->update([
            'form->'.SiteBackupRestoreMigration::SOURCE => $data,
            'form->next_step' => $nextStep,
        ]);

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteBackupRestoreMigration::SOURCE,
            'status' => $siteMigration->status,
        ]) : redirect()->route($route, [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }

    public function cancelMigration(SiteMigration $siteMigration)
    {
        $this->authorize('delete', $siteMigration->site);

        $siteMigration->markAsMigrationCancelled('Migration has been cancelled by '. auth()->user()->name);

        return redirect()->back()->with('flash',
            ['message' => 'Migration has been cancelled successfully.', 'type' => 'success']);

    }

    public function getBackupFiles(SiteBackupRestoreMigration $siteMigration)
    {
        abort_unless($siteMigration->team_id == team()->id, 403, 'You are not allowed to access this page');
        return response()->json($siteMigration->getBackups(request('nextContinuationToken')));
    }

    public function storeBackupFiles(Request $request,SiteBackupRestoreMigration $siteMigration)
    {
        abort_unless($siteMigration->team_id == team()->id, 403, 'You are not allowed to access this page');
        $data = $this->validate($request, [
            'backup_files' => [
                'required',
                'array',
                'size:2',
                Rule::when($siteMigration->isSourceBackupTypeRemote(),[
                    function ($attribute, $value, $fail) use ($siteMigration,$request) {
                        $storageProvider = StorageProvider::whereHas('backupFiles',fn($q)=>$q->whereIn('id',$request->input('backup_files.*.id')))->first();
                        if ($storageProvider && !$storageProvider->hasStableConnection()) {
                            $fail('Unable to connect to the storage provider. Please check your credentials.');
                        }
                    },
                ]),
                function ($attribute, $value, $fail) use($siteMigration) {
                    if (!$siteMigration->isImportTypeBucket()) {
                        return;
                    }

                    $types = collect($value)->pluck('is_sql')->unique();

                    if ($types->count() !== 2) {
                        $fail(($types->first() ? 'Non SQL file' : 'SQL file') . ' is missing.');
                    }
                }
            ],
            'backup_files.*.id' => [
                Rule::when(fn () => $siteMigration->isBucket(), 'nullable'),
                Rule::when(fn () => !$siteMigration->isBucket(), [
                    'required',
                    'exists:backup_files,id',
                ]),
            ],
            'backup_files.*.Key' => [
                Rule::when(fn () => $siteMigration->isBucket(), 'required'),
                Rule::when(fn () => !$siteMigration->isBucket(), 'nullable'),
            ],
        ]);
        if ($siteMigration->isBucket()){
            $file = $request->collect('backup_files')->filter(fn($file) => !Arr::get($file,'is_sql',false))->first();
            if (Str::contains($file['Key'], 'duplicity-')) {
                $data['incremental_files'] = $siteMigration->getDependedFiles($file);
            }
        }
        $siteMigration->update([
            'form->'.SiteBackupRestoreMigration::BACKUPS => $data,
            'form->next_step' => SiteMigration::SETTINGS,
        ]);
        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteBackupRestoreMigration::BACKUPS,
            'status' => $siteMigration->status,
        ]) : redirect()->route('site.migrate.settings', [
            'server' => $siteMigration->server->id,
            'siteMigration' => $siteMigration->id,
        ]);
    }
}
