<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Server;
use App\Scripts\Reboot;
use App\Scripts\RestartService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class RestartServiceController extends Controller
{
    public function __invoke(Server $server, Request $request): JsonResponse|RedirectResponse
    {
        $this->authorize('service', $server);

        $validator = Validator::make($request->all(), [
            'service' => ['required', Rule::in($server->stack->getServices())]
        ]);

        if ($validator->fails()) {
            return back()->with('flash', ['message' => 'Invalid parameters: '.$validator->errors()->first(), 'type' => 'error']);
        }

        $validated = $validator->validated();

        if ($validated['service'] == 'php') {
            $validated['service'] = "php{$server->php_version}-fpm";
        }

        $task = $server->runInline(new RestartService($validated['service']));

        $message = $task->successful() ? 'Service restarted successfully' : 'Failed to restart '.$validated['service'];
        $type = $task->successful() ? 'success' : 'error';

        if ($task->successful()) {
            $server->update([
                'services->'.$request->service => 'active'
            ]);
        }

        if (request()->expectsJson()) {
            return response()->json([
                'message' => $message,
                'type' => $type
            ]);
        }

        return back()->with('flash', ['message' => $message, 'type' => $type]);
    }
}
