<?php

namespace App\Http\Controllers\API;

use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Http\Controllers\Controller;
use App\Jobs\Site\DeleteSiteJob;
use App\Jobs\Site\InstallPhpMyAdmin;
use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Services\Database\DatabaseNameGenerator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Str;

class PhpMyAdminController extends Controller
{
    /**
     * Enable phpMyAdmin on the server.
     *
     * @param  Server  $server
     * @return \Illuminate\Http\JsonResponse
     */
    public function enable(Server $server)
    {
        $this->authorize('viewDB', $server);

        // Check if phpMyAdmin is already enabled
        $existingPhpMyAdmin = $server->getPhpMyAdminSite();

        if ($existingPhpMyAdmin) {
            return response()->json($existingPhpMyAdmin);
        }

        // Create a new phpMyAdmin site
        $siteName = strtolower('phpmyadmin-'.Str::random(8).'.'.config('services.cloudflare_updated.active'));

        $site = $server->sites()->create([
            'name' => $siteName,
            'title' => 'phpMyAdmin',
            'type' => SiteType::PHPMYADMIN,
            'status' => SiteStatus::NEW,
            'environment' => Site::STAGING,
            'ssl_provider' => SslCertificate::PROVIDER_STAGING,
            'site_user' => strtolower('phpmyadmin_'.Str::random(8)),
            'php_version' => $server->php_version,
        ]);

        $site->update([
            'name' => strtolower(
                'pma-'.$site->id.'-'.Str::random(4).'.'.config('services.cloudflare_updated.active')
            ),
            'site_user' => strtolower('phpmyadmin_'.$site->id),
        ]);

        $site->provision();

        return response()->json($site);
    }

    /**
     * Disable phpMyAdmin on the server.
     *
     * @param  Server  $server
     * @return \Illuminate\Http\JsonResponse
     */
    public function disable(Server $server)
    {
        $this->authorize('viewDB', $server);

        // Find the phpMyAdmin site
        $phpMyAdminSite = $server->getPhpMyAdminSite();

        if (!$phpMyAdminSite) {
            return response()->json(['message' => 'phpMyAdmin is not enabled on this server.']);
        }

        if (!$phpMyAdminSite->hasStagingEnvironment() && !$phpMyAdminSite->hasDemoEnvironment()) {
            // delete dns from cloudflare   
            $phpMyAdminSite->update([
                'meta->delete_dns_record' => true,
            ]);
        }

        DeleteSiteJob::dispatch($server, $phpMyAdminSite, true, true, true, true);

        $phpMyAdminSite->update([
            'status' => SiteStatus::DELETING,
        ]);

        return response()->json(['message' => 'phpMyAdmin has been disabled successfully.']);
    }
}
