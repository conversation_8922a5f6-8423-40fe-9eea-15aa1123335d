<?php

namespace App\Http\Controllers\API;

use App\Enums\FirewallProtocolEnum;
use App\Http\Controllers\Controller;
use App\Models\FirewallRule;
use App\Models\Server;
use App\Scripts\Server\DisableFirewall;
use App\Scripts\Server\InstallFirewall;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FirewallRuleController extends Controller
{
    public function create(Server $server, Request $request)
    {
        $this->authorize('addFirewallRule', $server);

        $validator = Validator::make($request->all(), [
            'name' => ['required', 'max:255'],
            'port' => ['nullable', 'max:255'],
            'ip_address' => ['nullable', 'max:255'],
            'protocol' => ['required', 'in:any,tcp,udp'],
            'traffic' => ['required', 'in:allow,deny'],
        ]);
        // check if port contains a range(using colon), if contains a range, then protocol can not be all
        if (str_contains($request->input('port'), ':') && $request->input('protocol') === 'any') {
            $validator->errors()->add('protocol', 'Protocol must be TCP/UDP when port contains a range');
        }

        // valid port format: number, number:number, empty
        if (!preg_match('/^(\d+|(\d+:\d+)|)$/', $request->input('port'))) {
            $validator->errors()->add('port', 'Invalid port format. Valid format: number, number:number, empty');
        }

        ## If IP address is given, but port is empty, protocol can not be all ##
        if (!empty($request->input('ip_address')) && empty($request->input('port'))
            && $request->input('protocol') === FirewallProtocolEnum::ALL->value) {
            $validator->errors()->add('protocol', 'Protocol must be TCP/UDP when IP address is given and port is empty');
        }

        // ## IP Address Validation ## //
        // valid ip format: ip, ip, ip/subnet, empty
        $ip_address = $request->input('ip_address'); // Assuming $firewallRuleForm is an associative array

        // Check if the field is empty first
        if (empty($ip_address)) {
            $ipIsValid = true;
        } else {
            $ips = explode(',', $ip_address);
            $ipIsValid = true;

            foreach ($ips as $ip) {
                $ip = trim($ip); // Remove any whitespace
                if (preg_match('/^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/', $ip)) {
                    // If it matches the regex, further validate the IP part
                    $parts = explode('/', $ip);
                    if (!filter_var($parts[0], FILTER_VALIDATE_IP)) {
                        $ipIsValid = false;
                        break;
                    }
                    // validate CIDR(Subnet must be between 0 and 32)
                    if (isset($parts[1])) {
                        if ($parts[1] < 0 || $parts[1] > 32) {
                            $ipIsValid = false;
                            break;
                        }
                    }

                } else {
                    $ipIsValid = false;
                    break;
                }
            }
        }

        if (!$ipIsValid) {
            $validator->errors()->add('ip_address', 'Invalid IP address format. Valid format: ip, ip, ip/subnet, empty');
        }

        if (!empty($validator->errors()->messages())) {
            return back()->withErrors($validator)->withInput();
        }

        $validated = $validator->validated();

        $firewallRule = $server->firewallRules()->firstOrCreate([
            'name' => $validated['name'],
            'port' => $validated['port'],
            'ip_address' => $validated['ip_address'],
        ],[
            'protocol' => $validated['protocol'],
            'traffic' => $validated['traffic'],
            'is_active' => true,
        ]);

        $task = $server->runInline(
            new InstallFirewall($server, $firewallRule)
        );

        // Check if the task has an error
        $pattern = '/ERROR: (.+)/';
        if (preg_match($pattern, $task->output, $matches)) {
            $errorMessage = $matches[1]; // Extract the error message
        }

        if(isset($errorMessage)) {
            $firewallRule->setAsInactive();

            return back()->with('flash', [
                'message' => $errorMessage,
                'type' => 'error',
            ]);
        }

        if ($request->expectsJson()) {
            return response()->json($firewallRule, 201);
        }

        return back()->with('flash', [
            'message' => 'Added new firewall rule',
        ]);
    }

    public function disable(Server $server, FirewallRule $firewallRule)
    {
        $this->authorize('update', $firewallRule);

        $firewallRule->update([
            'is_active' => false,
        ]);

        $task = $server->runInline(
            new DisableFirewall($server, $firewallRule)
        );

        // Check if the task has an error
        $pattern = '/ERROR: (.+)/';
        if (preg_match($pattern, $task->output, $matches)) {
            $errorMessage = $matches[1]; // Extract the error message
        }

        if(isset($errorMessage)) {
            $firewallRule->setAsInactive();

            return back()->with('flash', [
                'message' => $errorMessage,
                'type' => 'error',
            ]);
        }

        if (request()->expectsJson()) {
            return response()->json($firewallRule, 200);
        }

        return back()->with('flash', [
            'message' => 'Disabled firewall rule',
        ]);
    }

    public function enable(Server $server, FirewallRule $firewallRule)
    {
        $this->authorize('update', $firewallRule);

        $firewallRule->update([
            'is_active' => true,
        ]);

        $task = $server->runInline(
            new InstallFirewall($server, $firewallRule)
        );

        // Check if the task has an error
        $pattern = '/ERROR: (.+)/';
        if (preg_match($pattern, $task->output, $matches)) {
            $errorMessage = $matches[1]; // Extract the error message
        }

        if(isset($errorMessage)) {
            $firewallRule->setAsInactive();

            return back()->with('flash', [
                'message' => $errorMessage,
                'type' => 'error',
            ]);
        }

        if (request()->expectsJson()) {
            return response()->json($firewallRule, 200);
        }

        return back()->with('flash', [
            'message' => 'Enabled firewall rule',
        ]);
    }

    public function delete(Server $server, FirewallRule $firewallRule)
    {
        $this->authorize('delete', $firewallRule);

        $server->runInline(
            new DisableFirewall($server, $firewallRule)
        );

        $firewallRule->delete();

        if (request()->expectsJson()) {
            return response()->json(null, 204);
        }

        return back()->with('flash', [
            'message' => 'Deleted firewall rule',
        ]);
    }
}
