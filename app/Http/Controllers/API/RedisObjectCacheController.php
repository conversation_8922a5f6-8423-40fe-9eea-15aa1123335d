<?php

namespace App\Http\Controllers\API;

use App\Enums\Integration\PluginIntegrationType;
use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Scripts\Site\DeactivateRedisObjectCache;
use App\Scripts\Site\InstallRedisObjectCache;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RedisObjectCacheController extends Controller
{
    function enable(Site $site, Request $request)
    {
        $this->authorize('manageCache', $site);

        if ($site->pluginIntegrations()->where('type', PluginIntegrationType::OBJECT_CACHE_PRO)->doesntExist()){
            $site->update([
                'meta->has_redis_object_caching' => true,
                'redis_password' => $site->redis_password ?? Str::random(32)
            ]);
            $task = $site->run(new InstallRedisObjectCache($site));
            $success = $task->successful();
            $message = $success ? 'Redis Object Caching enabled' : 'Failed to enable Redis Object Caching';
        }else{
            $message = 'This site is using Object Cache Pro. To enable Redis Object Cache, you need to disable Object Cache Pro first.';
            $success = false;
        }
        if ($request->expectsJson()) {
            return response()->json(['message' => $message]);
        }

        return back()->with('flash', ['message' => $message, 'type' => $success ? 'success' : 'error']);
    }

    function disable(Site $site, Request $request)
    {
        $this->authorize('manageCache', $site);

        $site->update(['meta->has_redis_object_caching' => false]);

        $task = $site->run(new DeactivateRedisObjectCache($site));

        $success = $task->successful();

        $message = $success ? 'Redis Object Caching disabled' : 'Failed to disable Redis Object Caching';

        if ($request->expectsJson()) {
            return response()->json(['message' => $message]);
        }

        return back()->with('flash', ['message' => $message, 'type' => $success ? 'success' : 'error']);
    }
}
