<?php

namespace App\Http\Controllers\API;

use App\Enums\SiteType;
use App\Http\Controllers\Controller;
use App\Models\Server;
use App\Models\Site;
use App\Models\User;
use App\Scripts\ConfigurePhpMyAdminAuth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PhpMyAdminLoginController extends Controller
{
    /**
     * Redirect to phpMyAdmin with a secure token.
     *
     * @param Request $request
     * @param Server $server
     * @return \Illuminate\Http\RedirectResponse
     */
    public function redirect(Request $request, Server $server)
    {
        $this->authorize('viewDB', $server);

        // Check if phpMyAdmin is enabled on this server
        $phpMyAdminSite = $server->sites()->where('type', SiteType::PHPMYADMIN)->first();

        if (!$phpMyAdminSite) {
            return redirect()->route('server.database', $server)->with('flash', [
                'type' => 'error',
                'message' => 'phpMyAdmin is not enabled on this server. Please enable it first.'
            ]);
        }

        if (!$server->isConnected()) {
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Server is not connected'
            ]);
        }

        // Generate a secure token
        $secret = Str::random(30);

        // Create an encrypted token with necessary data
        $token = encrypt(json_encode([
            'token' => $secret,
            'server' => $server->id,
            'site' => $phpMyAdminSite->id,
            'user' => user()->id,
        ]));

        // Store the token in cache for a short time (5 minutes)
        Cache::put('PhpMyAdmin:' . user()->id . ':' . $server->id . ':', $secret, 300);

        // Ensure the auth script is installed
        $task = $phpMyAdminSite->runInline(new ConfigurePhpMyAdminAuth($phpMyAdminSite));

        if (!$task->successful()) {
            Log::error('Failed to configure phpMyAdmin authentication: ' . $task->output);
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Failed to configure phpMyAdmin authentication'
            ]);
        }

        // Log the success for debugging
        Log::info('phpMyAdmin authentication configured successfully for site: ' . $phpMyAdminSite->id);

        // Redirect to the phpMyAdmin gateway script with the token (no site_specific parameter)
        // Also add auth_type parameter to clearly identify the type of authentication
        $url = $phpMyAdminSite->getSiteUrlAttribute() . '/pma-gateway.php?xcloud-token=' . $token . '&debug=' . time() . '&site_specific=0&auth_type=server';
        return redirect()->away($url);
    }

    /**
     * Verify the token and return database credentials.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        try {
            $token = json_decode(decrypt(request('token')), true);
        } catch (\Exception $e) {
            Log::error('PhpMyAdmin token decryption failed: ' . $e->getMessage());
            return response()->json(['success' => false]);
        }

        if (empty($token['token']) || empty($token['server']) || empty($token['site']) || empty($token['user'])) {
            return response()->json(['success' => false, 'message' => 'Invalid token format']);
        }

        $server = Server::find($token['server']);
        $site = Site::find($token['site']);
        $user = User::find($token['user']);

        if (is_null($server) || is_null($site) || is_null($user) || Gate::forUser($user)->denies('addSite', $server)) {
            return response()->json(['success' => false, 'message' => 'Invalid permissions']);
        }

        $_token = Cache::get('PhpMyAdmin:' . $user->id . ':' . $server->id . ':');
        $tries = Cache::get("{$_token}:try", 0);

        if ($_token == $token['token'] && $tries < 2) {
            Cache::put("{$_token}:try", $tries + 1, 30);

            // Return the database credentials
            $data = [
                'host' => 'localhost',
                'username' => 'root',
                'password' => $server->database_password,
            ];

            if ($tries == 1) {
                Cache::forget("{$_token}:try");
                Cache::forget('PhpMyAdmin:' . $user->id . ':' . $server->id . ':');
            }

            Log::info('PhpMyAdmin login successful for user: ' . $user->id . ' to server: ' . $server->id);

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);
        }

        Log::warning('PhpMyAdmin login failed: Invalid or expired token for user: ' . ($user->id ?? 'unknown'));
        return response()->json(['success' => false, 'message' => 'Invalid or expired token']);
    }
}
