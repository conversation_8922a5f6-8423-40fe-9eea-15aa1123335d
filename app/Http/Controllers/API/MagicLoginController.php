<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Server;
use App\Models\Site;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MagicLoginController extends Controller
{
    function redirect(Request $request, Site $site)
    {
        $this->authorize('magicLogin', $site);

        abort_unless($site->server->isMagicLoginEnabled(), 403, 'Magic Login is not enabled');

        if (!$site->server->isConnected()) {
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Server is not connected'
            ]);
        }

        [$uploadSuccess, $recentlyUploaded] = $site->uploadMagicLoginPlugin();

        if (!$uploadSuccess) {
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Remote Login failed'
            ]);
        }

        $loginUser = null;

        if (user()->hasTeamPermission($site->getTeam(), 'site:access-magic-login') && $request->input('ref')){
            $loginUser = base64_decode($request->input('ref'));
        }

        $secret = Str::random(30);

        $data = [
            'token' => $secret,
            'site' => $site->id,
            'user' => user()->id,
            'try' => 1,
            'login_user' => $loginUser,
        ];

        $token = encrypt(json_encode($data));

        Cache::put('MagicLoginTo:'.user()->id.':'.$site->id.':'.$site->admin_user, $secret, 600);

        // for some very unknown reason, the auth token is taking a bit of time to be working once the plugin is uploaded
        // adding a sleep for now
        if ($recentlyUploaded){
            sleep(1);
        }

        $redirectUrl = $site->getSiteUrlAttribute().'/wp-login.php?'.http_build_query([
            'xcloud_magic_login_token' => $token,
            'auth_token' => hashid_encode($site->id),
            'v' => time(),
        ]);

        if ($request->wantsJson()){
            return response()->json(['url' => $redirectUrl]);
        }

        return redirect()->away($redirectUrl);
    }

    function verify(Request $request)
    {
        // dump('Request Token: '.request('token'));

        try {
            $token = json_decode(decrypt(request('token')), true);
        } catch (\Exception $e) {
            return ['success' => false];
        }

        // dump('verify:', $token);

        if (empty($token) || empty($token['token']) || empty($token['site']) || empty($token['user']) || empty(request('auth_token')) || empty(request('verify_token'))) {
            return ['success' => false];
        }

        $site = Site::find($token['site']);
        $user = User::find($token['user']);

        if (is_null($site) || is_null($user) || Gate::forUser($user)->denies('magicLogin', $site)) {
            return ['success' => false];
        }

        try {
            if (hashid_decode(request('auth_token')) != $site->id) {
                return ['success' => false];
            }

            if (decrypt(request('verify_token')) != "xCloudMagicLogin:$site->server_id:$site->id") {
                return ['success' => false];
            }
        } catch (\Exception $e) {
            return ['success' => false];
        }

        $_token = Cache::get('MagicLoginTo:'.$user->id.':'.$site->id.':'.$site->admin_user);

        if ($_token == $token['token']) {
            return [
                'site_id' => $site->id,
                'site_url' => $site->site_url,
                'success' => true,
                'user' => $site->admin_user,
                'email' => $user->email,
                'login_user' => $token['login_user'] ?? null,
            ];
        }

        return ['success' => false];
    }

    function failed(Request $request, Site $site)
    {
        return redirect()->route('site.index')->with('flash', [
            'type' => 'error',
            'message' => 'Remote Login failed'
        ]);
    }
}
