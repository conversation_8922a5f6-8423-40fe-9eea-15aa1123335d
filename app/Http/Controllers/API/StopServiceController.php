<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Server;
use App\Scripts\StopService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class StopServiceController extends Controller
{
    public function __invoke(Server $server, Request $request): JsonResponse|RedirectResponse
    {
        $this->authorize('service',$server);

        $validator = Validator::make($request->all(), [
            'service' => ['required', Rule::in($server->stack->getServices())]
        ]);

        if ($validator->fails()) {
            return back()->with('flash', ['message' => 'Invalid parameters: '.$validator->errors()->first(), 'type' => 'error']);
        }

        $validated = $validator->validated();

        if ($validated['service'] == 'php') {
            $validated['service'] = "php{$server->php_version}-fpm";
        }

        $task = $server->runInline(new StopService($validated['service']));

        $message = $task->successful() ? $validated['service'] . ' Service stopped successfully' : 'Failed to stop '. $validated['service'];

        if($task->successful()) {
            $server->update([
                'services->'.$request->service => 'inactive'
            ]);
        }
        $type = $task->successful() ? 'success' : 'error';

        if (request()->expectsJson()) {
            return response()->json([
                'message' => $message,
                'type' => $type
            ]);
        }

        return back()->with('flash', ['message' => $message, 'type' => $type]);
    }
}
