<?php

namespace App\Http\Controllers\API;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Http\Controllers\Controller;
use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Services\PaymentGateway\API\InvoicePaymentService;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Stripe\Exception\ApiErrorException;

class InvoiceController extends Controller
{
    public function getInvoice(Request $request, $invoiceId)
    {
        $invoice = GeneralInvoice::findOrFail($invoiceId);

        $this->authorize('view', $invoice);

        return response()->json($invoice->toArray());
    }

    /**
     * @throws ApiErrorException
     */
    public function payInvoice(Request $request, GeneralInvoice $invoice) : JsonResponse
    {
        // Authorize the user to pay the invoice
        $this->authorize('can-pay', $invoice);

        // Authorize payment method usage
        $paymentMethodId = $request->get('paymentMethodId') ?: team()->activePaymentMethod()->first()?->id;

        // If no payment method is found, return an error
        if (!$paymentMethodId) {
            return response()->json([
                'message' => 'No payment method found!',
                'type' => 'error'
            ]);
        }

        // Get the payment method
        $paymentMethod = PaymentMethod::find($paymentMethodId);

        // If payment method is found, authorize its usage
        if ($paymentMethod) {
            $this->authorize('can-use', $paymentMethod);
        }

        // Use the shared service to process payment
        $result = InvoicePaymentService::processPayment($invoice, $paymentMethod);

        return response()->json($result);
    }

    public function cancelInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('createBilling', team());

        if ($invoice->status === BillingStatus::Paid) {
            return back()->with('flash', [
                'warning' => 'You can not cancel a paid invoice!'
            ]);
        }

        $errorMessage = '';
        $successMessage = 'Invoice cancelled successfully!';

        try {
            $invoice->markAsCancelled();
        }catch (\Exception $e){
            $errorMessage = $e->getMessage();
        }

        if($errorMessage){
            return [
                'status' =>  'error',
                'message' => $errorMessage
            ];
        } else {
            return [
                'status' => 'success',
                'message' => $successMessage
            ];
        }
    }

    public function generateInvoiceFromBills(Request $request)
    {
        $this->authorize('createBilling', team());

        $request->validate([
            'bills' => 'required',
            'confirmed_to_convert_free_bills' => 'boolean'
        ]);

        if (is_string($request->get('bills')) && str_contains($request->get('bills'), ',')) {
            $request->merge([
                'bills' => explode(',', $request->get('bills')),
            ]);
        } else {
            $request->merge([
                'bills' => [$request->get('bills')]
            ]);
        }

        if ($request->confirmed_to_convert_free_bills) {
            $bills = team()->bills()->where('status', BillingStatus::Unpaid)
                ->where('has_offer', true)
                ->whereNull('invoice_id')
                ->whereIn('id', $request->bills)->get();

            if ($bills) {
                $bills->each(function ($bill) {
                    $bill->adjustPreviousOffer();
                });
            }
        } else {
            $bills = team()->bills()->where('status', BillingStatus::Unpaid)
                ->where('has_offer', false)
                ->whereNull('invoice_id')
                ->whereIn('id', $request->bills)->get();
        }

        if($bills->isEmpty()){
            return response()->json([
                'message' => 'No bills found to generate invoice.'
            ], 201);
        }

        if ($bills->sum('amount_to_pay') < 0.50) {
            return response()->json([
                'message' => 'Total amount to pay is less than $0.50. So, no invoice generated.'
            ], 201);
        }

        return response()->json(InvoiceGenerator::bills($bills)
            ->team(team())
            ->source(InvoiceSourceEnum::SinglePurchase)
            ->generate()->toArray());
    }
}
