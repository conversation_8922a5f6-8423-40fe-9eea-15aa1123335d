<?php

namespace App\Http\Controllers\API;

use App\Enums\NotificationIntegrationStatus;
use App\Enums\NotificationIntegrationTypes;
use App\Http\Controllers\Controller;
use App\Models\NotificationIntegration;
use App\Models\Team;
use Exception;
use Guz<PERSON>Http\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TelegramController extends Controller
{
    public function handleTelegramWebhook(Request $request)
    {
        try {
            $response = $request->all();
            if (isset($response['message']) && isset($response['message']['text'])) {
                $text = $response['message']['text'];
                $chatId = $response['message']['chat']['id'];
                $name = $response['message']['chat']['first_name'];
                if (isset($response['message']['chat']['last_name'])) {
                    $name = $response['message']['chat']['first_name'].' '.$response['message']['chat']['last_name'];
                }

                if ($text === '/help') {
                    $message = "/help - Help command, Get a list of available commands\n/start - How to use xCloudHostingBot\n/authenticate - Authenticate to send notification using xCloudHostingBot\n/revoke - Revoke your integration from xCloudHostingBot";
                    $this->sendMessage($chatId, $message);
                }

                if ($text === '/start') {
                    $message = "Hello, {$name}. Welcome to xCloud Hosting Bot! \n\nThis bot is used to receive xCloud Notification through Telegram. Before we start, please make sure you are currently inside Notification Menu in xCloud Panel. \n\nTo start, type this command: /authenticate &lt;token&gt;";
                    $this->sendMessage($chatId, $message);
                }

                if (Str::startsWith($text, '/authenticate') && strlen($text) > 13) {
                    $token = explode(' ', $text);
                    if (strlen($token[1]) === 16) {
                        try {
                            $teamId = hashid_decode($token[1], 16);
                            $team = Team::where('id', $teamId)->first();
                            if (!blank($team)) {
                                $integration = NotificationIntegration::where('team_id', $teamId)
                                    ->where('type', NotificationIntegrationTypes::Telegram->value)
                                    ->first();
                                if (!blank($integration)) {
                                    $message = "You already authenticate this chat with xCloudHostingBot. Please delete authentication for this chat inside Notification menu to attempt another authentication.";
                                    $this->sendMessage($chatId, $message);
                                } else {
                                    NotificationIntegration::create([
                                        'title' => 'Telegram - #'.$chatId,
                                        'is_connected' => false,
                                        'unique_id' => Str::random(22),
                                        'type' => NotificationIntegrationTypes::Telegram,
                                        'status' => NotificationIntegrationStatus::DISCONNECTED,
                                        'token' => $token[1],
                                        'meta' => $response,
                                        'user_id' => $team?->user_id,
                                        'team_id' => $teamId,
                                    ]);

                                    $message = "You are ready to go. Please click authorize on Telegram button inside xCloud Panel integration section.";
                                    $this->sendMessage($chatId, $message);
                                }
                            } else {
                                $message = "Sorry, unable to authenticate at the moment. Your token is invalid. Please try again later.";
                                $this->sendMessage($chatId, $message);
                            }
                        } catch (Exception $e) {
                            Log::error($e);
                            $message = "Sorry, unable to authenticate at the moment. Your token is invalid. Please try again later.";
                            $this->sendMessage($chatId, $message);
                        }
                    } else {
                        $message = "Sorry, unable to authenticate at the moment. Your token is invalid. Please try again later.";
                        $this->sendMessage($chatId, $message);
                    }
                }

                if ($text === '/authenticate') {
                    $message = "You must provide &lt;token&gt; to attempt authentication. \nE.g: /authenticate &lt;token&gt;";
                    $this->sendMessage($chatId, $message);
                }

                if ($text === '/revoke') {
                    $message = "To revoke your chat from xCloudHostingBot, go to Notification menu and click <b>Disconnect</b> button.";
                    $this->sendMessage($chatId, $message);
                }
            }
        } catch (Exception $e) {
            Log::error('Webhook Handling Error: ', ['error' => $e->getMessage()]);
            return response('Internal Server Error', 500);
        }

        return response('OK', 200);
    }

    private function sendMessage($chatId, $message)
    {
        $botToken = config('xcloud-notification.telegram.token');
        $url = "https://api.telegram.org/bot{$botToken}/sendMessage";
        $client = new Client();

        try {
            $response = $client->post($url, [
                'form_params' => [
                    'chat_id' => $chatId,
                    'text' =>  $message,
                    'parse_mode' => 'HTML',
                ],
            ]);

            Log::info('Message sent successfully:', [
                'chat_id' => $chatId,
                'response' => json_decode($response->getBody(), true),
            ]);
        } catch (Exception $e) {
            Log::error('Unexpected error: ', [
                'chat_id' => $chatId,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
