<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Server\InstallServerNodeVersionJob;
use App\Models\NodeVersion;
use App\Models\Server;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class NodeVersionController extends Controller
{
    function index(Server $server): JsonResponse
    {
        $this->authorize('manageNode', $server);

        return response()->json($server->nodeVersions);
    }

    function store(Server $server, Request $request): JsonResponse
    {
        $this->authorize('manageNode', $server);

        $request->validate([
            'node_version' => ['required', 'string', NodeVersion::asRule()],
        ]);

        $server->nodeManager()->install($request->get('node_version'));

        return response()->json($server->nodeVersions);
    }

    function updateDefault(Server $server, Request $request)
    {
        $this->authorize('manageNode', $server);

        $request->validate([
            'node_version' => ['required', 'string', NodeVersion::asRule()],
        ]);

        $server->update(['node_version' => $request->get('node_version')]);

        InstallServerNodeVersionJob::dispatch($server, $nodeVersion = $request->get('node_version'), $setDefault = true);

        if ($request->wantsJson()){
            return response()->json($server->nodeVersions);
        }

        return back()->with('flash', ['message' => 'Default Node.js Update is in progress. It may take a few minutes to complete.']);
    }

    function destroy(Server $server, Request $request): JsonResponse
    {
        $this->authorize('manageNode', $server);

        $request->validate([
            'node_version' => ['required', 'string', NodeVersion::asRule()],
        ]);

        $server->nodeManager()->uninstall($request->get('node_version'));

        return response()->json($server->nodeVersions);
    }
}
