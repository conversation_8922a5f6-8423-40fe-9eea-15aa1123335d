<?php

namespace App\Http\Controllers\API;

use App\Enums\SiteType;
use App\Http\Controllers\Controller;
use App\Models\Server;
use App\Models\Site;
use App\Models\User;
use App\Scripts\ConfigurePhpMyAdminAuth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SitePhpMyAdminLoginController extends Controller
{
    /**
     * Redirect to phpMyAdmin with a secure token for site-specific database access.
     *
     * @param Request $request
     * @param Site $site
     * @return \Illuminate\Http\RedirectResponse
     */
    public function redirect(Request $request, Site $site)
    {
        $this->authorize('database', $site);

        // Check if the server has phpMyAdmin enabled
        $phpMyAdminSite = $site->server->sites()->where('type', SiteType::PHPMYADMIN)->first();

        if (!$phpMyAdminSite) {
            return redirect()->route('site.database', [$site->server, $site])->with('flash', [
                'type' => 'error',
                'message' => 'phpMyAdmin is not enabled on this server. Please enable it first.'
            ]);
        }

        if (!$site->server->isConnected()) {
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Server is not connected'
            ]);
        }

        // Generate a secure token
        $secret = Str::random(30);

        // Create an encrypted token with necessary data
        $token = encrypt(json_encode([
            'token' => $secret,
            'server' => $site->server->id,
            'site' => $site->id,
            'phpMyAdminSite' => $phpMyAdminSite->id,
            'user' => user()->id,
            'site_specific' => true,
        ]));

        // Store the token in cache for a short time (5 minutes)
        Cache::put('SitePhpMyAdmin:' . user()->id . ':' . $site->id . ':', $secret, 300);

        // Ensure the auth script is installed
        $task = $phpMyAdminSite->runInline(new ConfigurePhpMyAdminAuth($phpMyAdminSite));

        if (!$task->successful()) {
            Log::error('Failed to configure phpMyAdmin authentication: ' . $task->output);
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Failed to configure phpMyAdmin authentication'
            ]);
        }

        // Log the success for debugging
        Log::info('Site-specific phpMyAdmin authentication configured successfully for site: ' . $site->id);

        // Redirect to the phpMyAdmin gateway script with the token and site_specific parameter
        // Also add a clear_cookies parameter to ensure we start fresh
        $url = $phpMyAdminSite->getSiteUrlAttribute() . '/pma-gateway.php?xcloud-token=' . $token . '&debug=' . time() . '&site_specific=1&auth_type=site';
        return redirect()->away($url);
    }

    /**
     * Verify the token and return site-specific database credentials.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        try {
            $token = json_decode(decrypt(request('token')), true);
        } catch (\Exception $e) {
            Log::error('Site-specific PhpMyAdmin token decryption failed: ' . $e->getMessage());
            return response()->json(['success' => false]);
        }

        if (empty($token['token']) || empty($token['server']) || empty($token['site']) || empty($token['user']) || empty($token['phpMyAdminSite'])) {
            return response()->json(['success' => false, 'message' => 'Invalid token format']);
        }

        $server = Server::find($token['server']);
        $site = Site::find($token['site']);
        $phpMyAdminSite = Site::find($token['phpMyAdminSite']);
        $user = User::find($token['user']);
        $site_specific = $token['site_specific'] ?? false;

        if (is_null($server) || is_null($site) || is_null($user) || is_null($phpMyAdminSite) || Gate::forUser($user)->denies('database', $site)) {
            return response()->json(['success' => false, 'message' => 'Invalid permissions']);
        }

        $_token = Cache::get('SitePhpMyAdmin:' . $user->id . ':' . $site->id . ':');
        $tries = Cache::get("{$_token}:try", 0);

        if ($_token == $token['token'] && $tries < 2) {
            Cache::put("{$_token}:try", $tries + 1, 30);

            // Return the site-specific database credentials
            $data = [
                'host' => 'localhost',
                'username' => $site->database_user,
                'password' => $site->database_password,
                'database' => $site->database_name,
            ];

            if ($tries == 1) {
                Cache::forget("{$_token}:try");
                Cache::forget('SitePhpMyAdmin:' . $user->id . ':' . $site->id . ':');
            }

            Log::info('Site-specific phpMyAdmin login successful for user: ' . $user->id . ' to site: ' . $site->id);

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);
        }

        Log::warning('Site-specific phpMyAdmin login failed: Invalid or expired token for user: ' . ($user->id ?? 'unknown'));
        return response()->json(['success' => false, 'message' => 'Invalid or expired token']);
    }
}
