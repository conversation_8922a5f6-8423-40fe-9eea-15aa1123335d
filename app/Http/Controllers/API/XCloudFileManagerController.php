<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class XCloudFileManagerController extends Controller
{
    function redirect(Request $request, Site $site)
    {
        $this->authorize('fileManager', $site);
        $enable_file_manager = $site->getMeta('file_manager', false);
        abort_unless($enable_file_manager, 403, 'File manager is not enabled');
        if (!$site->server->isConnected()) {
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Server is not connected'
            ]);
        }
        $secret = Str::random(30);

        $token = encrypt(json_encode([
            'token' => $secret,
            'site' => $site->id,
            'user' => user()->id,
        ]));

        Cache::put('XCloudTFM:' . user()->id . ':' . $site->id . ':', $secret, 60);
        Log::info('XCloudTFM Token: ' . $token);
        return redirect()->away($site->getSiteUrlAttribute() . '/'.$enable_file_manager.'?xc-token=' . $token);
    }

    function verify(Request $request)
    {
        $token = json_decode(decrypt(request('token')), true);
        Log::info('XCloudTFM Verify: ' . json_encode($token));
        if (empty($token['token']) || empty($token['site']) || empty($token['user'])) {
            return ['success' => false];
        }
        $site = Site::find($token['site']);
        $user = User::find($token['user']);

        if (is_null($site) || is_null($user) || Gate::forUser($user)->denies('fileManager', $site)) {
            return ['success' => false];
        }
        $_token = Cache::get('XCloudTFM:' . $user->id . ':' . $site->id . ':');
        if ($_token == $token['token']) {
            Cache::forget('XCloudTFM:' . $user->id . ':' . $site->id . ':');
            return [
                'success' => true,
                'data' => [
                    'user' => $user->name,
                ],
            ];
        }
        return ['success' => false, 'message' => 'Something went wrong'];
    }

    function failed(Request $request, Site $site)
    {
        return redirect()->route('site.index')->with('flash', [
            'type' => 'error',
            'message' => 'Remote Login failed'
        ]);
    }
}
