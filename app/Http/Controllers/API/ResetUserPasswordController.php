<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\Request;
use Illuminate\Contracts\Auth\PasswordBroker;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Support\Facades\Password;
use Laravel\Fortify\Actions\CompletePasswordReset;
use Laravel\Fortify\Contracts\FailedPasswordResetResponse;
use Laravel\Fortify\Contracts\PasswordResetResponse;
use <PERSON>vel\Fortify\Contracts\ResetPasswordViewResponse;
use <PERSON>vel\Fortify\Contracts\ResetsUserPasswords;
use <PERSON><PERSON>\Fortify\Fortify;
use <PERSON><PERSON>\Fortify\Http\Controllers\NewPasswordController;

class ResetUserPasswordController extends NewPasswordController
{
    public function store(Request $request): Responsable
    {
        $request->validate([
            'token' => 'required',
            Fortify::email() => 'required|email',
            'password' => 'required',
        ]);

        $whiteLabel = currentWhiteLabel();

        $credentials = $request->only(Fortify::email(), 'password', 'password_confirmation', 'token');

        if(!empty($whiteLabel)){
            $credentials['white_label_id'] = $whiteLabel->id;
        }

        // Customize user logic here
        $status = $this->broker()->reset(
            $credentials,
            function ($user) use ($request) {
                // Customize user logic here before calling ResetsUserPasswords
                // For example, you could modify the user data, validate additional conditions, etc.

                app(ResetsUserPasswords::class)->reset($user, $request->all());

                app(CompletePasswordReset::class)($this->guard, $user);
            }
        );

        return $status == Password::PASSWORD_RESET
            ? app(PasswordResetResponse::class, ['status' => $status])
            : app(FailedPasswordResetResponse::class, ['status' => $status]);
    }
}
