<?php

namespace App\Http\Controllers;

use App\Enums\CloudProviderEnums;
use App\Enums\WhiteLabel\ProductStatus;
use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Enums\XcloudBilling\CouponDiscountType;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Enums\XcloudBilling\PackageEnum;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Enums\XcloudBilling\PaymentMethodStatus;
use App\Enums\XcloudBilling\PlansEnum;
use App\Http\Requests\CheckoutPackageRequest;
use App\Http\Requests\CheckoutServerCreationRequest;
use App\Jobs\SubscribeUserToFluentCRM;
use App\Jobs\SubscribeUserToMailchimp;
use App\Jobs\UpdateContactOnFluentCRM;
use App\Models\BillingPlan;
use App\Models\CloudProvider;
use App\Models\Coupon;
use App\Models\InvitationCode;
use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Models\PhpVersion;
use App\Models\Package;
use App\Models\CartForm;
use App\Models\Product;
use App\Models\Promoter;
use App\Models\SshKeyPair;
use App\Models\Team;
use App\Models\User;
use App\Models\Utm;
use App\Repository\FirstPromoterRepository;
use App\Repository\ServerBillingRepository;
use App\Repository\ServerRepository;
use App\Repository\StripePaymentRepository;
use App\Repository\XCloudVultrServerTypeRepository;
use App\Rules\DatabasePassword;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Auth;
use Google\ApiCore\ApiException;
use Google\ApiCore\ValidationException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Log;
use Stripe\Checkout\Session;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;
use Illuminate\Support\Arr;

class CartFormController extends Controller
{
    public function checkoutWithProduct()
    {
        // if user logged in
        if (auth()->check()){
            $name = Str::lower(request('name'));
            if (request('product') && null ==$name){
                $product = Product::find(request('product'));
                $name = Str::lower($product?->title);
            }
            return redirect()->route('server.create.provider', [
                'cloudProvider' => CloudProviderEnums::XCLOUD,
                'name' => $name ?? 'newcomer'
            ]);
        }
        //$vultr_packages = collect(config('services.xvultr.supported_machines'));

        if (isWhiteLabel()) {
            $products = Product::query()
                ->where('white_label_id', currentWhiteLabel()?->id)
                ->where('is_active', true)
                ->get();
        } else {
            $products = Product::query()
                ->where('is_active', true)
                ->whereNull('white_label_id')
                ->whereNotIn('service_type', [
                    BillingServices::ManagedHosting,
                    BillingServices::BackupManagedHosting
                ])->get();
        }

        // matching xcloud product slugs with vultr slugs and merge them
        $serverTypeRepository = new XCloudVultrServerTypeRepository();
        $serverDetails = $serverTypeRepository->getXCloudServerTypes($products);

        return Inertia::render('CheckoutProduct', [
            'sizes' => $serverDetails['sizes']->toArray(),
            'regions' => $serverDetails['regions'],
            'provider' => '',
            'tagList' => [],
            'selectedSize' => $serverDetails['selectedSize'],
            'products' => $products,
        ]);
    }

    public function checkoutWithProductForWhiteLabel(Product $product)
    {
        abort_if($product->white_label_id !== currentWhiteLabel()?->id, 404);
        abort_if(!$product->is_active, 404);

        // if user logged in
        if (auth()->check()){
            $name = Str::lower(request('name'));
            if (request('product') && null ==$name){
                $name = str($product->title)->slug()->lower()->toString();
            }
            return redirect()->route('white-label-server.create', [
                'cloudProvider' => null,
                'name' =>  $name ?? 'newcomer',
                'slug' => $product->slug
            ]);
        }

        $descriptions= explode("\n", trim($product->description));

        foreach ($descriptions as $description) {
            if (strpos($description, 'RAM') !== false) {
                $product['memory'] = trim(str_replace('RAM -', '', $description));
            } elseif (strpos($description, 'SSD') !== false) {
                $product['disk'] = trim(str_replace('SSD -', '', $description));
            } elseif (strpos($description, 'vCPU') !== false) {
                $product['cpu'] = trim(str_replace('vCPU -', '', $description));
            } elseif (strpos($description, 'Bandwidth') !== false) {
                $product['bandwidth'] = trim(str_replace('Bandwidth -', '', $description));
            }
        }

        return Inertia::render('CheckoutProductWithWhiteLabel', [
            'selectedProduct' => $product,
            'whiteLabel' => $product->whiteLabel
        ]);
    }

    public function checkoutWithPackage()
    {

        if (team()?->isPlayGround()) {
            return Inertia::render('CheckoutPackagePlayGround', [
                'packages' => [],
                'selectedPackage' => null,
                'service_types' => [],
                'eligibleForSplitPayment' => false,
                'coupon' => null,
            ]);
        }

        $requSecurity = Coupon::where('code', request('access_key'))?->first();

        $requested_on_active_coupon = $requSecurity?->is_active;

        if ($requested_on_active_coupon) {
            // list of packages from Package model where expire_at is null or expire_at is greater than or equal to today
            $packages = Package::query()
                ->where('id', $requSecurity?->valid_only_package_id)
                ->where(fn($q) => $q->whereNull('expire_at')->orWhereDate('expire_at', '>', today()))
                ->where(fn($q) => $q->whereNull('start_from')->orWhereDate('start_from', '<=', today()))
                ->when(auth()->check(), fn($q) => $q->where('price', '>', 0))
                ->when(!auth()->check(), fn($q) => $q->whereNull('requires_billing_plan'))
                ->orderBy('price')
                ->withCount(['cartForms as purchase_count' => function ($query) {
                    $query->where('status', CartFormStatuses::Completed->value);
                }])
                ->get();
        } else {
            // list of packages from Package model where expire_at is null or expire_at is greater than or equal to today
            $packages = Package::query()
                ->where(fn($q) => $q->whereNull('expire_at')->orWhereDate('expire_at', '>', today()))
                ->where(fn($q) => $q->whereNull('start_from')->orWhereDate('start_from', '<=', today()))
                ->where(['show_on_display'=>true])
                ->when(auth()->check(), fn($q) => $q->where('price', '>', 0))
                ->when(!auth()->check(), fn($q) => $q->whereNull('requires_billing_plan'))
                ->orderBy('price')
                ->withCount(['cartForms as purchase_count' => function ($query) {
                    $query->where('status', CartFormStatuses::Completed->value);
                }])
                ->get();
        }

        $selectedPackage = $packages->firstWhere('id',  request('package'));

        if($selectedPackage){
            if(in_array($selectedPackage->name, PackageEnum::eligiblePackagesForSplitPayment())){
                $eligibleForSplitPayment = true;
            }
        }

        $coupon = null;

        if (request('coupon')) {
            $coupon = Coupon::where('code', request('coupon'))->first();
        }

        if ($coupon && !$coupon->isNotExpired()) {
            $coupon = null;
        }

        if ($coupon) {
            $packages = $packages->map(function ($package) use ($coupon) {
                return $package->filterPriceByCoupon($coupon->code);
            });
        }

        $service_types = Arr::where(BillingServices::asValueLabel(), function($value, $key) use($packages){
           return in_array($key, $packages->pluck('service_type')->collect()->unique()->pluck('value')->toArray());
        });

        return Inertia::render('CheckoutPackage', [
            'packages' => $packages,
            'selectedPackage' => $selectedPackage,
            'service_types'=>$service_types,
            'eligibleForSplitPayment' => $eligibleForSplitPayment ?? false,
            'coupon' => $coupon,
        ]);
    }


    /**
     * Display a listing of the resource.
     *
     * @return RedirectResponse
     */
    public function storeWithPackage(CheckoutPackageRequest $request, Package $package)
    {

        if ($package->required_purchase_any_of_products || $package->required_purchase_any_of_packages) {
            if (!auth()->check()) {
                return back()->with('flash', [
                    'message' => 'This package is not available for direct purchase. Please login first.',
                    'type' => 'warning'
                ]);
            }

            if ($package->required_purchase_any_of_products) {
                $products = Product::whereIn('id', $package->required_purchase_any_of_products)->get();
                $teamProducts = team()->products->pluck('id')->toArray();
                $teamProducts = array_intersect($teamProducts, $products->pluck('id')->toArray());
                if (empty($teamProducts)) {
                    return back()->with('flash', [
                        'message' => 'This package is not available for direct purchase. Please purchase required products first.',
                        'type' => 'warning'
                    ]);
                }
            }

            if ($package->required_purchase_any_of_packages) {
                $packages = Package::whereIn('id', $package->required_purchase_any_of_packages)->get();
                $teamPackages = team()->packages->pluck('id')->toArray();
                $teamPackages = array_intersect($teamPackages, $packages->pluck('id')->toArray());
                if (empty($teamPackages)) {
                    return back()->with('flash', [
                        'message' => 'This package is not available for direct purchase. Please purchase required packages first.',
                        'type' => 'warning'
                    ]);
                }
            }
        }

        if(auth()->check()){
            $data = [
                'account_name' => auth()->user()->name,
                'account_email' => auth()->user()->email,
                'password' => auth()->user()->password
            ];
        }else{
            $data = $request->validated();
        }

        // if package expires, return back
        if($package->isExpired()){
            return back()->with('flash', [
                'message' => 'Package has been expired!',
                'type' => 'warning'
            ]);
        }

        $formData = [
          'account_name' => Arr::get($data, 'account_name'),
          'account_email' => Arr::get($data, 'account_email'),
          'password' => Crypt::encrypt(Arr::get($data, 'password')),
          'invitation_code' => Arr::get($data, 'invitation_code'),
        ];

        $cart = CartForm::create([
            'model' => $package->service_type->getServiceModel(),
            'email'=> Arr::get($data, 'account_email'),
            'status'=> CartFormStatuses::Pending,
            'service' => $package->service_type,
            'form' => $formData,
            'package_id' => $package->id,
            'meta->requested_coupon_id' => request('coupon'),
        ]);

        // if user is logged in, use his card and take payment
        if(auth()->check()){
            if (Utm::getUtmIdFromCookie()){
                team()->setUtmSource(Utm::getUtmIdFromCookie(), optional(session())->getId(), $request?->ip());
            }
            // check if team has any card
            if(!team()->activePaymentMethod()->isEmpty()){
                $paymentMethod = team()->activePaymentMethod()->first();

                if(!team()->hasDuesToPay()) {
                    // set billing status to active
                    team()->setBillingStatusActive();
                }

                // update cart team id
                $cart->update([
                    'team_id' => team()->id,
                    'package_id' => $package->id
                ]);

                if (request('coupon')) {
                    $package = $package->filterPriceByCoupon(request('coupon'));
                }

                // take payment and generate invoice if not free package
                if($package->price > 0){
                    // generate invoice
                    $invoice = $this->generateInvoice($package, team(), $paymentMethod);
                    // take payment
                    try {
                        $paymentIntent = $this->takePayment($invoice);

                        if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){

                            $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
                                'requires_3d_secure_authentication' => true
                            ]);

                            $invoice->setStatusRequiresAction();

                            return redirect()->route('stripe.package.checkout.requires3dsecure', [
                                'sessionId' => $paymentMethod->session_id,
                                'packageId' => $package->id,
                                'affiliateId' => '',
                                'paymentIntentId' => $paymentIntent->id,
                                'cartId' => $cart->id,
                                'invoiceId' => $invoice->id,
                            ]);
                        }
                    } catch (CardException|ApiErrorException $e) {
                        $cart->setStatusPaymentFailed();
                        $cart->update([
                            'meta->payment_error' => $e->getMessage(),
                        ]);

                        return back()->with('flash', [
                            'message' => $e->getMessage(),
                            'type' => 'error'
                        ]);
                    }
                    // attach invoice to cart form
                    $cart->invoice()->associate($invoice);
                    $cart->save();
                }

                else { ///TODO: Faisal vai please check this part else were not handling the free package
                    $cart->setStatusCompleted();
                    $user = User::whereEmail(Arr::get($cart->form, 'account_email'))->firstOrFail();
                    // make user login and redirect to all server page
                    Auth::login($user);
                    return redirect()->route('user.team.products')->with('flash', [
                        'message' => 'Free package can\'t be purchased from here!',
                        'type' => 'info'
                    ]);
                }


                if($cart->invoice->isPaid()){
                    // attach package to team
                    team()->attachPackage($package);
                }

                if(!$cart->invoice->isPaid()){
                    $cart->setStatusPaymentFailed();

                    $team = Team::findOrFail($cart->team_id);
                    $team->setBillingStatusInactive();
                }

                $user = User::whereEmail(Arr::get($cart->form, 'account_email'))->firstOrFail();
                Auth::login($user);
                $userResponse = [
                    'message' => $cart->invoice->isPaid()
                        ? 'Package purchased successfully!'
                        : 'There is something wrong with your payment method. Please update your payment method to unlock our system.',
                    'type' => $cart->invoice->isPaid() ? 'success' : 'error'
                ];

                return redirect()->to('/user/detailed-invoices/' . $cart->invoice->id .'?invoiceStatusFilter=All')
                    ->with('flash', $userResponse);
            }
        }

        if(request()->cookie('xc_fp_affiliate')){
            $data['affiliate_id'] = request()->cookie('xc_fp_affiliate');
        }

        if(request('coupon')){
            $data['coupon'] = request('coupon');
        }

        return back()->with('flash', [
            'checkout_url' => $this->packageCheckout($package, $cart, $data)
        ]);
    }

    public function storeWithProduct(CheckoutServerCreationRequest $request, Product $product)
    {
        $serverData = $request->validated();

        $cart = CartForm::create([
            'model' => $product->service_type->getServiceModel(),
            'email'=> $serverData['account_email'],
            'status'=> CartFormStatuses::Pending,
            'service' => $product->service_type,
            'form' => array_merge(
                Arr::only($serverData, ['account_name', 'account_email', 'invitation_code']),
                [
                    'password' => Crypt::encrypt($serverData['password'])
                ]
            ),
            'resource' => Arr::only($serverData, ['size']),
            'product_id' => $product->id
        ]);

        if(request()->cookie('xc_fp_affiliate')){
            $serverData['affiliate_id'] = request()->cookie('xc_fp_affiliate');
        }
        if(Utm::getUtmIdFromCookie()){
            $serverData['meta']['utm_id'] = Utm::getUtmIdFromCookie();
        }

        return back()->with('flash', [
            'checkout_url' => $this->productCheckout($product, $cart, $serverData)
        ]);
    }

    private function productCheckout(Product $product, CartForm $cart, $data)
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));
        $stripe = new StripeClient(config('services.stripe.secret_key'));

        try {
            $cart->setStatusPaymentProcessing();

            $customerData = [
                'name' => Arr::get($data, 'account_name'),
                'email' => Arr::get($data, 'account_email'),
                'description' => 'Customer ' . Arr::get($data, 'account_name') . ' , added when purchased from Cart.'
            ];

            if ($product->white_label_id) {
                $customer = Customer::create($customerData, [
                    'stripe_account' => $product->whiteLabel->connectedAccount->stripe_account_id,
                ]);
            } else {
                $customer = Customer::create($customerData);
            }

            $queryParams = "session_id={CHECKOUT_SESSION_ID}&product_id={$product->id}";


            if(Arr::get($data, 'affiliate_id')){
                $queryParams .= "&affiliate_id=".Arr::get($data, 'affiliate_id');
            }

            $sessionData = [
                'payment_method_types' => ['card'],
                'mode' => 'setup',
                'customer' => $customer->id,
                'success_url' => route('stripe.product.checkout.success', [], true) . "?{$queryParams}",
                'cancel_url' =>  route('stripe.product.checkout.cancelled', [], true) . "?{$queryParams}"
            ];

            if($product->white_label_id){
                $session = Session::create($sessionData, [
                    'stripe_account' => $product->whiteLabel->connectedAccount->stripe_account_id,
                ]);
            }else{
                $session = Session::create($sessionData);
            }

            $cart->update([
                'checkout_session_id' => $session->id,
                'meta->stripe->checkout_session' => [
                    'customer_id' => $customer->id,
                    'checkout_url' => $session->url,
                ]
            ]);

            return $session->url;
        }catch (\Exception $e) {
            $cart->setStatusPaymentFailed();

            $cart->update([
                'meta->stripe->error_log' => [
                    'message' => $e->getMessage()
                ]
            ]);
        }
        return redirect()->route('cart.checkoutWithProduct');
    }

    private function packageCheckout(Package $package, CartForm $cart, $data)
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));
        $stripe = new StripeClient(config('services.stripe.secret_key'));

        try {
            $cart->setStatusPaymentProcessing();

            $customer = Customer::create([
                'name' => Arr::get($data, 'account_name'),
                'email' => Arr::get($data, 'account_email'),
                'description' => 'Customer ' . Arr::get($data, 'account_name') . ' , added when purchased from Cart',
            ]);

            $queryParams = "session_id={CHECKOUT_SESSION_ID}&package_id={$package->id}";

            if(Arr::get($data, 'affiliate_id')){
                $queryParams .= "&affiliate_id=".Arr::get($data, 'affiliate_id');
            }

            if(Arr::get($data, 'coupon')){
                $queryParams .= "&coupon=".Arr::get($data, 'coupon');
            }

            $session = Session::create([
                'payment_method_types' => ['card'],
                'mode' => 'setup',
                'customer' => $customer->id,
                'success_url' => route('stripe.package.checkout.success', [], true) . "?{$queryParams}",
                'cancel_url' =>  route('stripe.package.checkout.cancelled', [], true) . "?{$queryParams}"
            ]);

            $cart->update([
                'checkout_session_id' => $session->id,
                'meta->stripe->checkout_session' => [
                    'customer_id' => $customer->id,
                    'checkout_url' => $session->url,
                ]
            ]);

            return $session->url;
        }catch (\Exception $e) {
            $cart->setStatusPaymentFailed();

            $cart->update([
                'meta->stripe->error_log' => [
                    'message' => $e->getMessage()
                ]
            ]);
        }
        return redirect()->route('cart.checkoutWithPackage');
    }

    public function packageLists()
    {
        return Inertia::render('package/list', [
            'packages' => Package::all(),
            'products' => Product::all()
        ]);
    }

    /**
     * @throws InvalidRequestException
     */
    private function createPaymentMethod($session, $customer, $team, $user)
    {
        $paymentMethod = PaymentMethod::create([
            'team_id' => $team->id,
            'user_id' => $user->id,
            'payment_gateway' => PaymentGateway::Stripe,
            'customer_id' => $customer->id,
            'default_card' => !(count($team->paymentMethods) > 0),
            'status' => PaymentMethodStatus::ACTIVE,
            'session_id' => $session->id,
        ]);

        if ($user?->whiteLabel && BillingPlan::where('name', PlansEnum::WebHosting)->exists()) {
            $billingPlanToAssign = BillingPlan::where('name', PlansEnum::WebHosting)->first();
        } else {
            $billingPlanToAssign = BillingPlan::where('is_default', true)->first();
        }

        // update team billing plan
        $team->update([
            'active_plan_id' => $billingPlanToAssign?->id,
        ]);

        if(!$paymentMethod){
            throw new InvalidRequestException('Failed to add payment method!');
        }

        return $paymentMethod;
    }

    private function saveCardDetails(PaymentMethod $paymentMethod, $session, $customer, $stripe)
    {
        if(empty($paymentMethod->card_no)){
            $customerMeta['customer_id'] = $customer->id;
            $customerMeta['email'] = $customer->email;
            $customerMeta['description'] = $customer->description;
            $customerMeta['discount'] = $customer->discount;

            $paymentMethod->saveMeta('customer', $customerMeta);

            if($paymentMethod->team->whiteLabel){
                $paymentMethods = $stripe->paymentMethods->all([
                    'customer' => $customer->id,
                    'type' => 'card',
                ],[
                    'stripe_account' => $paymentMethod->team->whiteLabel->connectedAccount->stripe_account_id
                ]);
            }else{
                $paymentMethods = $stripe->paymentMethods->all([
                    'customer' => $customer->id,
                    'type' => 'card',
                ]);
            }

            if(isset($paymentMethods->data[0])){
                $paymentMethod->update([
                    'card_no' => '**** ' . $paymentMethods->data[0]->card->last4
                ]);

                $meta['payment_method'] = $paymentMethods->data[0]->id;
                $meta['setup_intent'] = $session->setup_intent;
                $meta['payment_method_types'] = $session->payment_method_types;
                $meta['last4digit'] = $paymentMethods->data[0]->card->last4;
                $meta['brand'] = $paymentMethods->data[0]->card->brand;
                $meta['expiry_month'] = $paymentMethods->data[0]->card->exp_month;
                $meta['expiry_year'] = $paymentMethods->data[0]->card->exp_year;

                $paymentMethod->saveMeta('stripe', $meta);
            }
        }
    }

    /**
     * @throws GuzzleException
     */
    public function checkoutProduct(Request $request)
    {
        $sessionId = $request->get('session_id');
        $productId = $request->get('product_id');
        $affiliateId = $request->get('affiliate_id');

        Stripe::setApiKey(config('services.stripe.secret_key'));

        $stripe = new StripeClient(config('services.stripe.secret_key'));

        try {
            $cart = CartForm::whereCheckoutSessionId($sessionId)->first();
            $product = Product::findOrFail($productId);

            if($product->white_label_id){
                $session = $stripe->checkout->sessions->retrieve(
                    $sessionId,
                    [],
                    ['stripe_account' => $product->whiteLabel->connectedAccount->stripe_account_id]
                );

                $customer = Customer::retrieve($session->customer, [
                    'stripe_account' => $product->whiteLabel->connectedAccount->stripe_account_id
                ]);
            }else{
                $session = $stripe->checkout->sessions->retrieve($sessionId, []);

                $customer = Customer::retrieve($session->customer);
            }

            // create user
            $user = User::create([
                'name' => Arr::get($cart->form, 'account_name'),
                'email' => Arr::get($cart->form, 'account_email'),
                'password' => Hash::make(Arr::get($cart->form, 'password')),
                'is_active' => true,
                'white_label_id' => $product->white_label_id
            ]);
            // create team
            $team = $this->createTeam($user);

            if($affiliateId){
                // verify if affiliate_id is valid
                $promoter = Promoter::where('default_ref_id', $affiliateId)->first();
                if($promoter){
                    $response = (new FirstPromoterRepository())->getPromoter($promoter->promoter_id);
                    if(!empty($response) && Arr::get($response, 'status') === 'active'){
                        // set affiliate expiry date 1 year from today
                        $team->update([
                            'affiliate_promoter_id' => $promoter->id,
                            'affiliate_expiry_at' => now()->addYear()->format('Y-m-d H:i:s')
                        ]);

                        (new FirstPromoterRepository())->signupLead($team, $affiliateId);
                    }
                }

                $team->update([
                    'meta->affiliate->affiliate_code' => $affiliateId,
                ]);
            }

            // create payment method (save the card in xcloud)
            $paymentMethod = $this->createPaymentMethod($session, $customer, $team, $user);

            // save card details in payment method
            $this->saveCardDetails($paymentMethod, $session, $customer, $stripe);

            if(team() && !team()->hasDuesToPay()) {
                // set billing status to active
                team()->setBillingStatusActive();

                // set plan to web hosting if not free package
                if(team()->whiteLabel) {
                    team()->setPlan(PlansEnum::WebHosting);
                } else {
                    team()->setPlan(PlansEnum::Starter);
                }
            }

            // update cart team id
            $cart->update([
                'team_id' => $team->id,
                'product_id' => $product->id
            ]);

            if($paymentMethod){
                // take payment and generate invoice if not free package
                if($product->price > 0){
                    // generate invoice
                    $invoice = $this->generateInvoiceForProduct($product, $team, $paymentMethod, $cart);
                    // take payment
                    $paymentIntent = $this->takePayment($invoice);

                    if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                        Auth::login($user);

                        $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
                            'requires_3d_secure_authentication' => true
                        ]);

                        $invoice->setStatusRequiresAction();

                        return redirect()->route('stripe.product.checkout.requires3dsecure', [
                            'sessionId' => $sessionId,
                            'productId' => $productId,
                            'affiliateId' => $affiliateId,
                            'paymentIntentId' => $paymentIntent->id,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                        ]);
                    }
                }

                if($cart->invoice->isPaid()){
                    // attach product to team
                    $team->attachProduct($product);
                }

                // update the cart
                $cart->setStatusCompleted();
                $cart->update([
                    'meta->stripe->checkout_session->payment_method_types' => $session->payment_method_types,
                    'meta->stripe->checkout_session->customer' => $session->customer,
                    'meta->stripe->checkout_session->customer_email' => $session->customer_email,
                ]);
            }

            // make user login and redirect to all server page
            Auth::login($user);
            return redirect()->route('user.team.products')->with('flash', [
                'message' => 'Product purchased successfully!',
                'type' => 'success'
            ]);
        }catch (\Exception $e){
            $cart = CartForm::whereCheckoutSessionId($sessionId)->firstOrFail();
            $cart->setStatusPaymentFailed();
            $cart->update([
               'meta->payment_error' => $e->getMessage(),
            ]);

            return redirect()->route('dashboard')->with('flash', [
                'message' => $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

    /**
     * @throws GuzzleException
     */
    public function checkoutPackage(Request $request)
    {
        $sessionId = $request->get('session_id');
        $packageId = $request->get('package_id');
        $affiliateId = $request->get('affiliate_id');
        $coupon = $request->get('coupon');

        Stripe::setApiKey(config('services.stripe.secret_key'));

        $stripe = new StripeClient(config('services.stripe.secret_key'));

        try {
            $session = $stripe->checkout->sessions->retrieve(
                $sessionId,
                []
            );

            if(!$session){
                throw new InvalidRequestException('Session not found!');
            }

            $customer = Customer::retrieve($session->customer);

            $cart = CartForm::whereCheckoutSessionId($sessionId)->first();
            $package = Package::findOrFail($packageId);

            if ($coupon) {
                $package = $package->filterPriceByCoupon($coupon);
            }

            if(auth()->check()){
                $user = auth()->user();
                $team = Team::findOrFail($user->current_team_id);
            }else{
                // create user
                $user = User::create([
                    'name' => Arr::get($cart->form, 'account_name'),
                    'email' => Arr::get($cart->form, 'account_email'),
                    'password' => Hash::make(Arr::get($cart->form, 'password')),
                    'is_active' => true,
                ]);
                // create team
                $team = $this->createTeam($user);

                $invitationCode = InvitationCode::whereInvitationCode(Arr::get($cart->form, 'invitation_code'))->first();
                if($invitationCode instanceof InvitationCode){
                    $invitationCode->use();
                    $user->update([
                        'meta->invitation_code' => [
                            'invited_by' => $invitationCode->user->email
                        ]
                    ]);
                }

                # Signing up affiliate
                if($affiliateId){
                    // verify if affiliate_id is valid
                    $promoter = Promoter::where('default_ref_id', $affiliateId)->first();

                    if($promoter){
                        $response = (new FirstPromoterRepository())->getPromoter($promoter->promoter_id);
                        if(!empty($response) && Arr::get($response, 'status') === 'active'){
                            // set affiliate expiry date 1 year from today
                            $team->update([
                                'affiliate_promoter_id' => $promoter->id,
                                'affiliate_expiry_at' => now()->addYear()->format('Y-m-d H:i:s')
                            ]);

                            (new FirstPromoterRepository())->signupLead($team, $affiliateId);
                        }
                    }

                    $team->update([
                        'meta->affiliate->affiliate_code' => $affiliateId,
                    ]);
                }
            }

            // create payment method (save the card in xcloud)
            $paymentMethod = $this->createPaymentMethod($session, $customer, $team, $user);

            // save card details in payment method
            $this->saveCardDetails($paymentMethod, $session, $customer, $stripe);

            if(team() && !team()->hasDuesToPay()) {
                // set billing status to active
                team()->setBillingStatusActive();
            }

            // update cart team id
            $cart->update([
                'team_id' => $team->id,
                'package_id' => $package->id
            ]);

            if($paymentMethod){
                // take payment and generate invoice if not free package
                if($package->price > 0){
                    // generate invoice
                    $invoice = $this->generateInvoice($package, $team, $paymentMethod);
                    // take payment
                    $paymentIntent = $this->takePayment($invoice);

                    if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                        Auth::login($user);

                        $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
                            'requires_3d_secure_authentication' => true
                        ]);

                        $invoice->setStatusRequiresAction();

                        return redirect()->route('stripe.package.checkout.requires3dsecure', [
                            'sessionId' => $sessionId,
                            'packageId' => $packageId,
                            'affiliateId' => $affiliateId,
                            'paymentIntentId' => $paymentIntent->id,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                        ]);
                    }

                    // attach invoice to cart form
                    $cart->invoice()->associate($invoice);
                    $cart->save();
                }

                if($cart->invoice->isPaid()){
                    // attach package to team
                    $team->attachPackage($package);
                }

                // update the cart
                $cart->setStatusCompleted();
                $cart->update([
                    'meta->stripe->checkout_session->payment_method_types' => $session->payment_method_types,
                    'meta->stripe->checkout_session->customer' => $session->customer,
                    'meta->stripe->checkout_session->customer_email' => $session->customer_email,
                ]);
            }
        }catch (\Exception $e){
            $cart = CartForm::whereCheckoutSessionId($sessionId)->firstOrFail();
            $cart->setStatusPaymentFailed();
            $cart->update([
                'meta->payment_error' => $e->getMessage(),
            ]);
        }

        if(!$cart->invoice->isPaid()){
            $cart->setStatusPaymentFailed();

            $team = Team::findOrFail($cart->team_id);
            $team->setBillingStatusInactive();
        }

        $user = User::whereEmail(Arr::get($cart->form, 'account_email'))->firstOrFail();
        Auth::login($user);
        $userResponse = [
            'message' => $cart->invoice->isPaid()
                ? 'Package purchased successfully!'
                : 'There is something wrong with your payment method. Please update your payment method to unlock our system.',
            'type' => $cart->invoice->isPaid() ? 'success' : 'error'
        ];

        // make user login and redirect to all server page
        Auth::login($user);

        return redirect()->to('/user/detailed-invoices/' . $cart->invoice->id .'?invoiceStatusFilter=All')
            ->with('flash', $userResponse);
    }
    protected function createTeam(User $user): bool|Team
    {
        $billingPlanToAssign = BillingPlan::where('is_default', true)->first();

        $team = $user->ownedTeams()->save(Team::forceCreate([
            'user_id' => $user->id,
            'email' => $user->email,
            'name' => explode(' ', $user->name, 2)[0] . "'s Team",
            'personal_team' => true,
            'active_plan_id' => $billingPlanToAssign?->id,
            'white_label_id' => $user->white_label_id,
        ]));

        if(Utm::getUtmIdFromCookie()){
            $team->setUtmSource(utmId: Utm::getUtmIdFromCookie(),sessionId: optional(session())->getId(),ip: request()->ip());
           // $team->saveMeta('utm', ['utmId'=>Utm::getUtmIdFromCookie()]);
            Utm::clearUtmCookie();
        }

        UpdateContactOnFluentCRM::dispatch($team);

        //Set user current team
        if (!$user->current_team_id) {
            $user->update(['current_team_id' => $team->id]);
        }
        return $team;
    }

    private function generateInvoice(Package $package, Team $team, PaymentMethod $paymentMethod)
    {
        if($package->price <= 0){
            return null;
        }

        $coupon = Coupon::find($package?->applied_coupon);

        return InvoiceGenerator::team($team)
            ->source(InvoiceSourceEnum::CartFormPurchase)
            ->discount(max($package?->original_price ? $package?->original_price - $package?->price : 0, 0))
            ->coupon($coupon)
            ->paymentMethod($paymentMethod)
            ->title('Package Purchase: ' . $package->name)
            ->description($package->name . ' : ' .  $package->description)
            ->amount($package->price)
            ->currency(BillingCurrency::USD)
            ->generate();
    }

    private function generateInvoiceForProduct(Product $product, Team $team, PaymentMethod $paymentMethod, CartForm $cartForm)
    {
        if($product->price <= 0){
            return null;
        }

        return InvoiceGenerator::team($team)
            ->source(InvoiceSourceEnum::CartFormPurchase)
            ->paymentMethod($paymentMethod)
            ->title('Product Purchase: ' . $product->title)
            ->description($product->title . ' : ' .  $product->description)
            ->amount($product->price)
            ->currency(BillingCurrency::USD)
            ->cartForm($cartForm)
            ->generate();
    }

    /**
     * @throws ApiErrorException
     * @throws CardException
     * @throws GuzzleException
     */
    private function takePayment(GeneralInvoice $invoice)
    {
        try {
            $paymentIntent = (new StripePaymentRepository())->takePayment($invoice);

            if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
                $invoice->setStatusPaid();

                // send email
                $invoice->team->sendInvoiceEmail($invoice);
            }else{
                if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                    $invoice->setStatusRequiresAction();
                }else{
                    $invoice->setStatusPaymentFailed();
                    $invoice->saveLog('payment_error', $paymentIntent->last_payment_error?->message);
                    Log::warning('Payment failed for invoice: ' . $invoice->id . ' with error: ' . $paymentIntent->last_payment_error?->message);
                }
            }

            return $paymentIntent;

        }catch (CardException|InvalidRequestException|ApiErrorException $e) {
            $invoice->setStatusPaymentFailed();
            $invoice->saveLog('payment_error', $e->getMessage());

            Log::warning('Payment failed for invoice: ' . $invoice->id . ' with error: ' . $e->getMessage());

            $paymentIntent = PaymentIntent::retrieve($invoice->gateway_invoice_or_intent_id);
            $paymentIntent->cancel();
            return $paymentIntent;
        }
    }

    /**
     * @throws ValidationException
     * @throws ApiException
     * @throws \Exception
     * @throws GuzzleException
     */
    private function createServer(CartForm $cart, User $user)
    {
//        TODO: Need to see if we can reuse server creation controller functionality here, i think it has redundant functionality
        $cloudProvider = CloudProvider::where('provider', CloudProviderEnums::XCLOUD_VULTR->value)->first();

        if (!$cloudProvider) {
            $cloudProvider = CloudProvider::createXcloudVultr();
        }

        $serverData = [
            'name' => Arr::get($cart->resource, 'name'),
            'size' => Arr::get($cart->resource, 'size'),
            'region' => Arr::get($cart->resource, 'region'),
            'database_name' => 'xcloud',
            'database_type' => Arr::get($cart->resource, 'database_type'),
            'database_password' => Arr::get($cart->resource, 'database_password') ?? DatabasePassword::randomPassword(),
            // default configurations
            'ssh_authentication_mode' => 'public_key',
            'ssh_port' => '22',
            'ssh_username' => 'root',
            'ubuntu_version' => '22.04',
            'server_image' => 'Ubuntu 22.04 LTS x64',
            'cloud_provider_id' => $cloudProvider->id,
            'sudo_password' => Str::random(32),
            'php_version' => PhpVersion::DEFAULT,
            'ssh_key_pair_id' => SshKeyPair::createServerDefaultKey(Str::slug(Arr::get($cart->resource, 'name'))."@xcloud")->id,
            'team_id' => $cart->team_id,
        ];

        $server = $user->servers()->create($serverData);

        if($server->backups){
            $server->saveMeta('backupCost', (new ServerRepository($server))->getBackupCost() ?? 0);
        }

        // generate bill
        (new ServerBillingRepository($server))->generateBillsFromProduct($cart->product);

        // Generate invoice if billing is active
        if($server->team->billingIsActive()){
            $invoice = $server->generateInvoice(source: InvoiceSourceEnum::SinglePurchase);
            // attach invoice to cart form
            $cart->invoice()->associate($invoice);
            $cart->save();
        }

        if (Arr::get($cart->resource, 'tags')) {
            $server->syncTags(Arr::get($cart->resource, 'tags'));
        }
        return $server;
    }

    public function cancelPackageCheckout(Request $request)
    {
        return redirect()->route('cart.checkoutWithPackage', 'package=' . $request->get('package_id'))->with('flash', [
            'message' => 'Payment cancelled!',
            'type' => 'warning'
        ]);
    }

    public function cancelProductCheckout(Request $request)
    {
        return redirect()->route('cart.checkoutWithProduct', 'product=' . $request->get('product_id'))->with('flash', [
            'message' => 'Payment cancelled!',
            'type' => 'warning'
        ]);
    }
}
