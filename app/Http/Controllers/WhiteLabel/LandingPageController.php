<?php

namespace App\Http\Controllers\WhiteLabel;

use App\Http\Controllers\Controller;
use Arr;
use Inertia\Inertia;

class LandingPageController extends Controller
{
    public function index()
    {
        $whiteLabel = currentWhiteLabel();

//        $enableLandingPage = Arr::get($whiteLabel?->settings ?: [], 'landing.enable_landing_page'); This was throwing error
        //dd(Arr::get($whiteLabel->settings, 'landing.enable_landing_page'));
        //abort_if(!Arr::get($whiteLabel->settings, 'landing.enable_landing_page'), 404, 'Landing page is disabled');

        if (!$whiteLabel){
            return redirect()->route('dashboard');
        }

        return Inertia::render('WhiteLabel/Landing', [
            'whiteLabel' => $whiteLabel,
            'landing_page_navbar_photo_url' => $whiteLabel->landing_page_navbar_photo_url,
            'products' => $whiteLabel->activeProducts,
        ]);
    }

    public function tos()
    {
        $whiteLabel = currentWhiteLabel();

        if (!$whiteLabel){
            return redirect('https://xcloud.host/terms-and-conditions/');
        }

        return Inertia::render('WhiteLabel/TOS', [
            'whiteLabel' => $whiteLabel,
            'landing_page_navbar_photo_url' => $whiteLabel->landing_page_navbar_photo_url,
        ]);
    }

    public function privacyPolicy()
    {
        $whiteLabel = currentWhiteLabel();

        if (!$whiteLabel){
            return redirect('https://xcloud.host/privacy-policy/');
        }

        return Inertia::render('WhiteLabel/PrivacyPolicy', [
            'whiteLabel' => $whiteLabel,
            'landing_page_navbar_photo_url' => $whiteLabel->landing_page_navbar_photo_url,
        ]);
    }
}
