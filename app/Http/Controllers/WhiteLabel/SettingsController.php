<?php

namespace App\Http\Controllers\WhiteLabel;

use App\Enums\WhiteLabel\SubdomainSources;
use App\Http\Controllers\Controller;
use App\Http\Requests\BrandSetupSettingsRequest;
use App\Validator\Domain;
use Arr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class SettingsController extends Controller
{
    public function paymentSettings()
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('managePayments', $whiteLabel);

        $connectedAccount = $whiteLabel->connectedAccount;

        return Inertia::render('WhiteLabel/Settings/PaymentSettings', [
            'whiteLabel' => $whiteLabel,
            'connectedAccount' => [
                'id' => $connectedAccount?->id,
                'account_name' => $connectedAccount?->account_name,
                'stripe_account_id' => $connectedAccount?->stripe_account_id,
                'email' => $connectedAccount?->account_email,
                'country' => $connectedAccount?->country,
                'currency' => $connectedAccount?->currency,
                'publishable_key' => $connectedAccount?->publishable_key,
                'account_activated' => $connectedAccount?->account_activated,
                'account_activated_at' => $connectedAccount?->account_activated_at
                    ? Carbon::parse($connectedAccount?->account_activated_at)->format('jS F, Y')
                    : null
            ],
        ]);
    }

    public function brandSettings()
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageBrand',$whiteLabel);

        return Inertia::render('WhiteLabel/Settings/BrandSettings', [
            'whiteLabel' => $whiteLabel
        ]);
    }

    public function brandSettingsUpdate(BrandSetupSettingsRequest $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageBrand', $whiteLabel);

        $brandSetupData = $request->only(['brand_name', 'email', 'support_email', 'contact_number', 'address', 'copyright_name', 'language']);

        foreach ($brandSetupData as $key => $value) {
            $whiteLabel->saveBranding($key, $value);
        }

        if ($request->filled('photo')) {
            $imageSize = Storage::disk('s3')->size($request->input('photo.key'));
            if ($imageSize > (5 * 1024 * 1024)) {
                return redirect()->back()->with('flash', [
                    'type' => 'error',
                    'message' => 'The logo size must not exceed 5MB.'
                ]);
            }

            $extension = $request->input('photo.extension') ?? 'jpg';
            $path = $whiteLabel->getPhotoPath($extension);
            if (isset($whiteLabel->branding['brand_photo_path']) && Storage::disk('s3')->exists($whiteLabel->branding['brand_photo_path'])) {
                Storage::disk('s3')->delete($whiteLabel->branding['brand_photo_path']);
            }
            Storage::disk('s3')->copy($request->input('photo.key'), $path);
            $whiteLabel->saveBranding('brand_photo_path', $path);
        }

        if ($request->filled('favicon')) {
            $imageSize = Storage::disk('s3')->size($request->input('favicon.key'));
            if ($imageSize > (1 * 1024 * 1024)) {
                return redirect()->back()->with('flash', [
                    'type' => 'error',
                    'message' => 'The favicon size must not exceed 1MB.'
                ]);
            }

            $extension = $request->input('favicon.extension') ?? 'png';
            $path = $whiteLabel->getFaviconPhotoPath($extension);
            if (isset($whiteLabel->branding['favicon_photo_path']) && Storage::disk('s3')->exists($whiteLabel->branding['favicon_photo_path'])) {
                Storage::disk('s3')->delete($whiteLabel->branding['favicon_photo_path']);
            }
            Storage::disk('s3')->copy($request->input('favicon.key'), $path);
            $whiteLabel->saveBranding('favicon_photo_path', $path);
        }

        return redirect()->back()->with('flash', ['message' => 'White Label Brand Setup Updated Successfully.']);
    }

    public function domainSettings()
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageDomainSettings', $whiteLabel);

        return Inertia::render('WhiteLabel/Settings/DomainSettings', [
            'whiteLabel' => $whiteLabel
        ]);
    }

    public function domainSettingsUpdate(Request $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageDomainSettings', $whiteLabel);

        $validated = $request->validate([
            'domain' => ['required', new Domain, Rule::unique('white_labels')->ignore($whiteLabel->id)],
        ]);

        $whiteLabel->update([
            'domain' => $validated['domain'],
        ]);

        return redirect()->back()->with('flash', ['message' => 'White Label Domain Setup Saved Successfully.']);
    }

    public function landingPageSettings()
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageLandingPage', $whiteLabel);

        return Inertia::render('WhiteLabel/Settings/LandingPageSettings', [
            'whiteLabel' => $whiteLabel,
            'landing_page_navbar_photo_url' => $whiteLabel->landing_page_navbar_photo_url
        ]);
    }

    public function smtpSettings()
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageSmtpSettings', $whiteLabel);
        $whiteLabel->makeVisible('smtp_password');
        return Inertia::render('WhiteLabel/Settings/SMTPSettings', [
            'whiteLabel' => $whiteLabel->toArrayParent(),
            'default_whitelabel_domain' => SubdomainSources::getActiveDomain(),
            'default_white_label_from_address' => config('mail.mailers.white-label-smtp.from.address')
        ]);
    }

    public function tosPageSettings()
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageTOSSettings', $whiteLabel);

        return Inertia::render('WhiteLabel/Settings/TermsAndServicesSettings', [
            'whiteLabel' => $whiteLabel
        ]);
    }

    public function privacyPolicySettings()
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('managePrivacyPolicySettings', $whiteLabel);

        return Inertia::render('WhiteLabel/Settings/PrivacyPolicySettings', [
            'whiteLabel' => $whiteLabel
        ]);
    }

    public function landingPageSettingsUpdate(Request $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageLandingPage', $whiteLabel);

        $validated = $request->validate([
            'enable_landing_page' => ['required', 'boolean'],
            'hero_section_heading' => ['nullable'],
            'hero_section_subheading' => ['nullable'],
            'hero_section_button_text' => ['nullable'],
            'hero_section_button_url' => ['nullable'],
            'cta_section_button_text' => ['nullable'],
            'cta_section_button_url' => ['nullable'],
            'cta_section_heading' => ['nullable'],
            'cta_section_subheading' => ['nullable'],
            'facebook_link' => ['nullable'],
            'instagram_link' => ['nullable'],
            'x_link' => ['nullable'],
            'linkedin_link' => ['nullable'],
            'youtube_link' => ['nullable'],
            'photo' => ['nullable']
        ]);

        if ($request->filled('photo')) {
            $imageSize = Storage::disk('s3')->size($request->input('photo.key'));
            if ($imageSize > (5 * 1024 * 1024)) {
                return redirect()->back()->with('flash', [
                    'type' => 'error',
                    'message' => 'The logo size must not exceed 5MB.'
                ]);
            }

            $extension = $request->input('photo.extension') ?? 'jpg';
            $path = $whiteLabel->getPhotoPath($extension);
            if (Arr::get($whiteLabel->settings, 'landing_page.photo') && Storage::disk('s3')->exists(Arr::get($whiteLabel->settings, 'landing_page.photo'))) {
                Storage::disk('s3')->delete(Arr::get($whiteLabel->settings, 'landing_page.photo'));
            }
            Storage::disk('s3')->copy($request->input('photo.key'), $path);
            $validated['photo'] = $path;
        }else if($request->input('photo') === null) {
            // remove the file from s3
            if (Arr::get($whiteLabel->settings, 'landing_page.photo') && Storage::disk('s3')->exists(Arr::get($whiteLabel->settings, 'landing_page.photo'))) {
                Storage::disk('s3')->delete(Arr::get($whiteLabel->settings, 'landing_page.photo'));
            }
        }

        // unchanged navbar logo
        if($whiteLabel->landing_page_navbar_photo_url && !$request->filled('photo')) {
            $extension = pathinfo($whiteLabel->landing_page_navbar_photo_url, PATHINFO_EXTENSION);
            $path = $whiteLabel->getPhotoPath($extension);
            $validated['photo'] = $path;
        }

        if(!$request->get('enable_landing_page')) {
            $whiteLabel->update([
                'settings->landing_page' => null,
                'settings->landing_page->enable_landing_page' => false
            ]);
        }else{
            $whiteLabel->update([
                'settings->landing_page' => $validated,
            ]);
        }

        return redirect()->back()->with('flash', [
            'message' => 'Settings Saved Successfully.',
            'success' => true,
        ]);
    }

    public function tosPageSettingsUpdate(Request $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageTOSSettings', $whiteLabel);

        $validated = $request->validate([
            'tos_type' => ['required', 'in:default,custom'],
            'tos' => ['required_if:tos_type,default'],
            'tos_url' => ['required_if:tos_type,custom'],
        ]);


        $whiteLabel->update([
            'settings->tos_page' => Arr::except($validated, ['tos']),
            'tos' => $request->get('tos_type') === 'default' ? $request->get('tos') : null
        ]);

        return redirect()->back()->with('flash', [
            'message' => 'White Label TOS Page Settings Saved Successfully.',
            'success' => true
        ]);
    }

    public function privacyPolicySettingsUpdate(Request $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('managePrivacyPolicySettings', $whiteLabel);

        $validated = $request->validate([
            'privacy_policy_type' => ['required', 'in:default,custom'],
            'privacy_policy' => ['required_if:privacy_policy_type,default'],
            'privacy_policy_url' => ['required_if:privacy_policy_type,custom'],
        ]);

        $whiteLabel->update([
            'settings->privacy_policy' => Arr::except($validated, ['privacy_policy']),
            'privacy_policy' => $request->get('privacy_policy_type') === 'default' ? $request->get('privacy_policy') : null
        ]);

        return redirect()->back()->with('flash', [
            'message' => 'White Label Privacy Policy Page Settings Saved Successfully.',
            'success' => true
        ]);
    }

    public function smtpSettingsUpdate(Request $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel, ResponseAlias::HTTP_NOT_FOUND);
        $this->authorize('manageSmtpSettings', $whiteLabel);

        $validated = $request->validate([
            'enable_smtp_settings' => ['required', 'boolean'],
            'host' => ['required_if:enable_smtp_settings,true'],
            'port' => ['required_if:enable_smtp_settings,true'],
            'username' => ['required_if:enable_smtp_settings,true'],
            'password' => ['required_if:enable_smtp_settings,true', 'required_if:enable_smtp_settings,true'],
            'encryption' => ['nullable'],
            'from_email' => ['required_if:enable_smtp_settings,true'],
        ]);

        if(!$request->get('enable_smtp_settings')) {
            $whiteLabel->update([
                'settings->smtp' => null,
                'smtp_password' => null,
                'settings->smtp->enable_smtp_settings' => false
            ]);
        }else{
            $whiteLabel->update([
                'smtp_password' => $validated['password'],
                'settings->smtp' => Arr::except($validated, ['password']),
            ]);
        }

        return back()->with('flash', [
            'message' => 'SMTP Settings Saved Successfully.',
            'success' => true
        ]);
    }
}
