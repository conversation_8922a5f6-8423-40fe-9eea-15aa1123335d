<?php

namespace App\Http\Controllers;

use App\Enums\CloudFlareR2Regions;
use App\Enums\CloudProviderEnums;
use App\Enums\HetznerObjectStorageRegions;
use App\Enums\NotificationIntegrationStatus;
use App\Enums\NotificationIntegrationTypes;
use App\Enums\VulnerabilityFilterTypeEnum;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Events\UserLeaveTeam;
use App\Events\UserSwitchTeam;
use App\Http\Requests\DisableAdminerRequest;
use App\Http\Requests\DisableFileManagerRequest;
use App\Http\Requests\NotificationIntegrationRequest;
use App\Http\Requests\PasswordUpdateRequest;
use App\Http\Requests\ProfileUpdateRequest;
use App\Http\Requests\StorageProviderRequest;
use App\Http\Requests\TeamDeleteRequest;
use App\Http\Requests\TeamInvitationRequest;
use App\Http\Requests\TeamRequest;
use App\Http\Requests\ViewStoreRequest;
use App\Http\Resources\BillResource;
use App\Http\Resources\SiteResource;
use App\Http\Resources\TaskResource;
use App\Http\Resources\TeamResource;
use App\Jobs\Site\UpdateBackupSettings;
use App\Mail\TeamUserInvitation;
use App\Models\BackupFile;
use App\Models\Cloudflare;
use App\Models\CloudProvider;
use App\Models\InvitationCode;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Models\Membership;
use App\Models\NotificationIntegration;
use App\Models\PatchstackVulnerability;
use App\Models\Product;
use App\Models\Server;
use App\Models\Site;
use App\Models\StorageProvider;
use App\Models\SubscriptionProduct;
use App\Models\Tag;
use App\Models\Task;
use App\Models\Team;
use App\Models\TeamInvitation;
use App\Models\User;
use App\Models\Bill;
use App\Exports\SitesExport;
use App\Exports\ServersExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\Integrations\ElasticEmailService;
use App\Services\XcloudProduct\CalculateBill;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Jenssegers\Agent\Agent;
use Laravel\Fortify\Features as FortifyFeatures;
use Laravel\Jetstream\Contracts\InvitesTeamMembers;
use Laravel\Jetstream\Jetstream;
use Symfony\Component\HttpFoundation\Response;
use Throwable;
use function Pest\Laravel\get;


class SingleProfileController extends Controller
{

    public function profile()
    {
        $user = auth()->user();
        $invitationCode = InvitationCode::whereUserId($user->id)->first();

        if($invitationCode){
            $invitationCode['expires_at'] = Carbon::createFromDate($invitationCode->validity)->format('d M,Y, H:i');
            $invitationCode['remaining_invitation'] = $invitationCode->total_invitations - $invitationCode->invitation_used;
            $invitedUsers = User::whereJsonContains('meta->invitation_code->invited_by', $user->email)
                ->orderBy('id', 'DESC')
                ->get()
                ->each(function ($invitationUser){
                    $invitationUser['invited_at'] = $invitationUser->created_at->diffForHumans();
                });
        }

        return Inertia::render('Profile/UserProfile', [
            'user' => $user,
            'invitationCode' => $invitationCode,
            'timezone_list' => timezone_list(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'invitedUsers' => $invitedUsers ?? [],
            'userMeta' => Arr::only($user->meta ?: [], ['address', 'customer_name']),
            'team' => team()
        ]);
    }

    public function invitations()
    {
        $invitationCode = InvitationCode::whereUserId(auth()->user()->id)->first();
        if ($invitationCode){
            $invitationCode['expires_at'] = Carbon::createFromDate($invitationCode->validity)->format('d M,Y, H:i');
            $invitationCode['remaining_invitation'] = $invitationCode->total_invitations - $invitationCode->invitation_used;
        }
        $invitedUsers = User::whereJsonContains('meta->invitation_code->invited_by', auth()->user()->email)
            ->orderBy('id', 'DESC')
            ->get()
            ->each(function ($invitationUser){
                $invitationUser['invited_at'] = $invitationUser->created_at->diffForHumans();
            })->paginate(10);

        return Inertia::render('Profile/InvitationList', [
            'invitationCode' => $invitationCode,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'invitedUsers' => $invitedUsers ?? []
        ]);
    }

    public function profileUpdate(ProfileUpdateRequest $request)
    {
        $user = auth()->user();
        if ($request->email !== $user->email && app()->environment('production')) {
            $user->update([
                'email_verified_at' => null
            ]);

            $user->sendEmailVerificationNotification();
        }

        $user->update($request->only([
            'name',
            'email',
            'contact_number',
            'time_zone',
        ]));

        $user->saveMeta('address', $request->get('address'));
        $user->saveMeta('customer_name', $request->get('customer_name'));

        team()->update([
            'billing_emails' => (!$request->get('billing_emails_status')) ? $request->get('billing_emails') : null
        ]);

        if ($request->filled('photo')) {
            $extension = $request->input('photo.extension') ?? 'jpg';
            $path = $user->getPhotoPath($extension);
            if ($user->profile_photo_path && Storage::disk('s3')->exists($user->profile_photo_path)) {
                Storage::disk('s3')->delete($user->profile_photo_path);
            }
            Storage::disk('s3')->copy($request->input('photo.key'), $path);
            $user->update(['profile_photo_path' => $path]);
        }
        return redirect()->back()->with('flash', ['message' => 'Profile Updated Successfully.']);
    }

    public function password()
    {
        return Inertia::render('Profile/Password', [
            'user' => auth()->user(),
            'timezone_list' => timezone_list(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function passwordUpdate(PasswordUpdateRequest $request)
    {
        // update password if new password is not empty
        if ($request->filled('new_password')) {
            auth()->user()->update([
                'password' => bcrypt($request->new_password)
            ]);
        }

        return redirect()->back()->with('flash', ['message' => 'Password Update Successfully.']);
    }

    /**
     * @throws AuthorizationException
     */
    public function teamManagement()
    {
        //  dd('teamManagement',auth()->user()->currentTeam);
        $this->authorize('viewAny', Team::class);
        $currentTeamId = team()->id;

        $teams = Team::select(['id','name','personal_team', 'user_id','team_photo_path','email'])
            ->with([
                'tags',
                'owner:id,name,profile_photo_path',
                'memberships' => fn($q) => $q->inRandomOrder()->with(['user:id,name,profile_photo_path'])->limit(2)
            ])
            ->without(['activePlan'])
            ->withCount('memberships', 'servers', 'sites', 'teamInvitations')
            ->whereHas('memberships', fn($query) => $query->where('user_id', auth()->id()))
            ->orWhere('user_id', auth()->id())
            ->orderByRaw("CASE WHEN id = {$currentTeamId} THEN 0 ELSE 1 END")
            ->orderBy('name', 'ASC')
            ->paginate(9)
            ->through(fn($team) => TeamResource::make($team));
        $teams_has_admin_role = auth()->user()->teams()->wherePivot('role', Team::TEAM_ADMIN)->get()->pluck('id')->toArray();
        $teams_has_site_admin_role = auth()->user()->teams()->wherePivot('role', Team::SITE_ADMIN)->get()->pluck('id')->toArray();
        $invitations = TeamInvitation::with('team')->where('email', auth()->user()->email)->get();
        return Inertia::render('Profile/TeamManagement', [
            'user' => auth()->user(),
            'teams' => $teams,
            'teams_has_admin_role' => $teams_has_admin_role,
            'teams_has_site_admin_role' => $teams_has_site_admin_role,
            'title' => 'Team Management',
            'can_create_team' => auth()->user()->can('create', Team::class),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'invitations' => $invitations
        ]);
    }

    public function customization()
    {
        $this->authorize('viewAny', Team::class);
        $teams_has_admin_role = auth()->user()->teams()->wherePivot('role', Team::TEAM_ADMIN)->get()->pluck('id')->toArray();
        $teams_has_site_admin_role = auth()->user()->teams()->wherePivot('role', Team::SITE_ADMIN)->get()->pluck('id')->toArray();
        $disableFileManager = team()->getSettings('feature_customization->disable_file_manager', false);
        $fileManagerInterval = team()->getSettings('feature_customization->file_manager_interval', 'six_hour');
        $disableAdminer = team()->getSettings('feature_customization->disable_adminer', false);
        $adminerInterval = team()->getSettings('feature_customization->adminer_interval', 'six_hour');

        return Inertia::render('Profile/FeatureCustomization', [
            'user' => auth()->user(),
            'file_manager_intervals' => Team::DISABLE_FILE_MANAGER_INTERVALS,
            'adminer_intervals' => Team::DISABLE_ADMINER_INTERVALS,
            'disable_file_manager' => $disableFileManager,
            'file_manager_interval' => $fileManagerInterval,
            'disable_adminer' => $disableAdminer,
            'adminer_interval' => $adminerInterval,
            'teams_has_admin_role' => $teams_has_admin_role,
            'teams_has_site_admin_role' => $teams_has_site_admin_role,
            'title' => 'Customization',
            'can_create_team' => auth()->user()->can('create', Team::class),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
        ]);
    }

    public function export()
    {
        $this->authorize('isOwner', team());
        $teams_has_admin_role = auth()->user()->teams()->wherePivot('role', Team::TEAM_ADMIN)->get()->pluck('id')->toArray();
        $teams_has_site_admin_role = auth()->user()->teams()->wherePivot('role', Team::SITE_ADMIN)->get()->pluck('id')->toArray();

        return Inertia::render('Profile/Export', [
            'user' => auth()->user(),
            'teams_has_admin_role' => $teams_has_admin_role,
            'teams_has_site_admin_role' => $teams_has_site_admin_role,
            'title' => 'Export',
            'can_create_team' => auth()->user()->can('create', Team::class),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'site_columns' => [
                ['id' => 'id', 'label' => 'Site ID'],
                ['id' => 'name', 'label' => 'Name'],
                ['id' => 'type', 'label' => 'Type'],
                ['id' => 'status', 'label' => 'Status'],
                ['id' => 'ssl_provider', 'label' => 'SSL Provider'],
                ['id' => 'environment', 'label' => 'Environment'],
                ['id' => 'php_version', 'label' => 'PHP Version'],
                ['id' => 'wordpress_version', 'label' => 'WordPress Version'],
                ['id' => 'additional_domains', 'label' => 'Additional Domains'],
                ['id' => 'web_root', 'label' => 'Web Root'],
                ['id' => 'site_user', 'label' => 'Site User'],
                ['id' => 'database_provider', 'label' => 'Database Provider'],
                ['id' => 'database_user', 'label' => 'Database User'],
                ['id' => 'database_name', 'label' => 'Database Name'],
                ['id' => 'admin_user', 'label' => 'Admin User'],
                ['id' => 'server_id', 'label' => 'Server ID'],
                ['id' => 'server_name', 'label' => 'Server Name'],
                ['id' => 'created_at', 'label' => 'Created At'],
            ],
            'server_columns' => [
                ['id' => 'id', 'label' => 'Server ID'],
                ['id' => 'name', 'label' => 'Name'],
                ['id' => 'status', 'label' => 'Status'],
                ['id' => 'public_ip', 'label' => 'Public IP'],
                ['id' => 'ubuntu_version', 'label' => 'Ubuntu Version'],
                ['id' => 'php_version', 'label' => 'PHP Version'],
                ['id' => 'region', 'label' => 'Region'],
                ['id' => 'size', 'label' => 'Size'],
                ['id' => 'stack', 'label' => 'Stack'],
                ['id' => 'cpu_architecture', 'label' => 'CPU Architecture'],
                ['id' => 'ssh_port', 'label' => 'SSH Port'],
                ['id' => 'database_name', 'label' => 'Database Name'],
                ['id' => 'time_zone', 'label' => 'Time Zone'],
                ['id' => 'created_at', 'label' => 'Created At'],
            ],
        ]);
    }

    public function exportData(Request $request)
    {
        $this->authorize('isOwner', team());

        $type = $request->input('type', 'site');
        $format = $request->input('format', 'csv');
        $columns = $request->input('columns', []);

        $fileName = $type . '-export-' . now()->format('Y-m-d-H-i-s') . '.' . $format;

        try {
            if ($type === 'site') {
                return Excel::download(new SitesExport($columns), $fileName);
            } else {
                return Excel::download(new ServersExport($columns), $fileName);
            }
        } catch (Exception $e) {
            Log::error('Export error: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json(['error' => 'Failed to export data: ' . $e->getMessage()], 500);
            }

            return redirect()->back()->with('flash', [
                'message' => 'Failed to export data: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

    public function disableFileManager(DisableFileManagerRequest $request)
    {
        $this->authorize('siteFileManager', team());
        team()->update([
            'settings->feature_customization->disable_file_manager' => $request->boolean('disable_file_manager'),
            'settings->feature_customization->file_manager_interval' => $request->string('file_manager_interval')
        ]);

        return redirect()->back()->with('flash', ['message' => 'File Manager Disabled Settings Saved Successfully.']);
    }

    public function disableAdminer(DisableAdminerRequest $request)
    {
        $this->authorize('siteDatabase', team());
        team()->update([
            'settings->feature_customization->disable_adminer' => $request->boolean('disable_adminer'),
            'settings->feature_customization->adminer_interval' => $request->string('adminer_interval')
        ]);

        return redirect()->back()->with('flash', ['message' => 'Adminer Disabled Settings Saved Successfully.']);
    }

    public function createTeam()
    {
        $this->authorize('create', Team::class);
        $tags = Tag::associatedTags('teams');
        return Inertia::render('Profile/Team/Create', [
            'user' => auth()->user(),
            'tags' => $tags,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function storeTeam(TeamRequest $request)
    {
        $this->authorize('create', Team::class);

        $this->validate($request, [
            'name' => [
                'required', 'string', 'max:255', Rule::unique('teams', 'name')->where(function ($query) {
                    return $query->where('user_id', auth()->id());
                })
            ],
            'email' => 'required|string|email|max:255',
        ]);

//        $billingPlan = BillingPlan::where('name', PlansEnum::Starter)->first();

        $team = auth()->user()->ownedTeams()->create([
            'name' => $request->name,
            'email' => $request->email,
            'personal_team' => false,
//            'active_plan' => $billingPlan->name,
//            'active_plan_id' => $billingPlan->id,
        ]);

        if ($request->hasFile('photo')) {
            $team->updateTeamPhoto($request->file('photo'));
        }

        if ($request->has('tags')) {
            $team->syncTags($request->tags);
        }

        if (!$request->user()->switchTeam($team)) {
            abort(403);
        }

        return redirect()->route('user.team')->with('flash', ['message' => 'Team Created Successfully.']);
    }

    /**
     * Store a newly created resource in storage.
     * @param Team $team
     * @param TeamInvitationRequest $request
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function storeUserToTeam(Team $team, TeamInvitationRequest $request)
    {
        Gate::forUser(auth()->user())->authorize('addTeamMember', $team);
        if($team->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        if ($team->users()->where('email', $request->email)->exists()) {
            return redirect()->back()->with('flash', ['message' => 'User already exists in this team.', 'type' => 'error']);
        }
        app(InvitesTeamMembers::class)->invite(
            $request->user(),
            $team,
            $request->email ?: '',
            $request->role,
            $request->only(['server_access', 'selected_servers', 'site_access', 'selected_sites', 'permissions'])
        );
        return redirect()->route('user.team.show', $team);
    }

    public function addUser(Team $team)
    {
        $this->authorize('addTeamMember', $team);
        if($team->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        $inactive_permissions = ['site:access-magic-login', 'site:custom-command-runner'];

        return Inertia::render('Profile/Team/AddMember', [
            'user' => auth()->user(),
            'user_role_list' => Jetstream::$roles,
            'team' => $team,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'title' => 'Add Member to ' . $team->name,
            'inactive_permissions' => $inactive_permissions
        ]);
    }

    public function editTeamMember(Team $team, User $user)
    {
        $this->authorize('updateTeamMember', $team);
        abort_if(!$team->users->contains($user) || $team->user_id == $user->id || auth()->id() == $user->id, 404);

        $server_access = in_array($team->id, $user->getMeta('all_servers_team', [])) ? 'all' : 'choose';

        $serverList = $user->serversByInvitation()
            ->select('servers.id','name')
            ->where('team_id', $team->id)->get();
        $selected_servers = $serverList->pluck('id')->toArray();
        $site_access = in_array($team->id, $user->getMeta('all_sites_team', [])) ? 'all' : 'choose';
        $siteList = $user->sitesByInvitation()
            ->select('sites.id','name')
            ->whereHas('server',fn ($query)=>$query->where('team_id', $team->id))
            ->get();
        $selected_sites = $siteList->pluck('id')->toArray();
        return Inertia::render('Profile/Team/EditMember', [
            'team_user' => $user,
            'user_role_list' => Jetstream::$roles,
            'team' => $team,
            'selected_servers' => $selected_servers,
            'selected_sites' => $selected_sites,
            'role' => $user->roleForTeam($team)?->key,
            'server_access' => $server_access,
            'site_access' => $site_access,
            'permissions' => Membership::where(['team_id' => $team->id, 'user_id' => $user->id])->first()?->permissions ?? [],
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'title' => 'Edit Member ' . $user->name,
            'serverList' => $serverList,
            'siteList' => $siteList,
        ]);
    }

    public function updateTeamMember(Team $team, User $user, TeamInvitationRequest $request)
    {
        $this->authorize('updateTeamMember', $team);
        abort_if(!$team->users->contains($user) || $team->user_id == $user->id || auth()->id() == $user->id, 404);
        DB::beginTransaction();

        try {
            $user->serversByInvitation()->wherePivotIn('server_id', $team->servers->pluck('id')->toArray())->detach();
            $user->sitesByInvitation()->wherePivotIn('site_id', $team->sites->pluck('id')->toArray())->detach();
            $user->updateRole($team->id, $request->input('role'), $request->input('permissions'));
            if ($request->input('role') == Team::SITE_ADMIN) {
                $user->saveMeta('all_servers_team', array_diff($user->getMeta('all_servers_team', []), [$team->id]));
                if ($request->input('site_access') == 'all') {
                    $user->saveMeta('all_sites_team', array_unique(Arr::prepend($user->getMeta('all_sites_team', []), $team->id)));
                } else {
                    //remove team id from all sites meta
                    $user->saveMeta('all_sites_team', array_diff($user->getMeta('all_sites_team', []), [$team->id]));
                    $user->sitesByInvitation()->syncWithoutDetaching($request->input('selected_sites'));
                }
            } else {
                $user->saveMeta('all_sites_team', array_diff($user->getMeta('all_sites_team', []), [$team->id]));
                if ($request->input('server_access') == 'all') {
                    $user->saveMeta('all_servers_team', array_unique(Arr::prepend($user->getMeta('all_servers_team', []), $team->id)));
                } else {
                    $user->saveMeta('all_servers_team', array_diff($user->getMeta('all_servers_team', []), [$team->id]));
                    $user->serversByInvitation()->syncWithoutDetaching($request->input('selected_servers'));
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return redirect()->back()->with('flash', ['message' => $exception->getMessage(), 'type' => 'error']);
        }

        return redirect()->back()->with('flash', ['message' => 'Team Member Updated Successfully.']);
    }

    /**
     * @throws AuthorizationException
     */
    public function removeTeamMember(Team $team, User $user)
    {
        abort_unless(auth()->user()->can('removeTeamMember', $team) || $user->id == auth()->id(), Response::HTTP_FORBIDDEN);
        abort_if(!$team->users->contains($user) || $team->user_id == $user->id, Response::HTTP_FORBIDDEN);
        DB::beginTransaction();
        try {
            $team->removeUser($user);
            $user->serversByInvitation()->wherePivotIn('server_id', $team->servers->pluck('id')->toArray())->detach();
            $user->sitesByInvitation()->wherePivotIn('site_id', $team->sites->pluck('id')->toArray())->detach();
            $user->saveMeta('all_servers_team', array_diff($user->getMeta('all_servers_team', []), [$team->id]));
            $user->saveMeta('all_sites_team', array_diff($user->getMeta('all_sites_team', []), [$team->id]));
            if ($user->current_team_id == $team->id) {
                //Switch to a user default team
                $switchTeam = Team::where('user_id', auth()->id())->first();
                auth()->user()->switchTeam($switchTeam);
                event(new UserSwitchTeam(auth()->user(), $switchTeam));
            }
            event(new UserLeaveTeam($user, $team));
            DB::commit();
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            DB::rollBack();
            return redirect()->back()->with('flash',
                ['message' => 'Something went wrong. Please try again later.', 'type' => 'error']);
        }
        if ($user->id == auth()->id()) {
            return redirect()->route('user.team')->with('flash', ['message' => 'Team Leave Successfully.']);
        }
        return redirect()->back()->with('flash', ['message' => 'Team Member Leave Successfully.']);
    }

    /**
     * @throws AuthorizationException
     */
    public function removeTeamInvitation(Team $team, TeamInvitation $invitation)
    {
        // $this->authorize('removeTeamMember', $team);
        abort_unless($invitation->team_id == $team->id, Response::HTTP_NOT_FOUND);
        abort_unless(auth()->user()->can('addTeamMember', $team) || auth()->user()->can('removeTeamMember', $team) || auth()->user()->email == $invitation->email, Response::HTTP_FORBIDDEN);
        $team->teamInvitations()->where('id', $invitation->id)->delete();
        return redirect()->back()->with('flash', ['message' => 'Team Invitation Removed Successfully.']);
    }

    public function teamInvitations()
    {
        $this->authorize('viewAny', Team::class);
        $invitations = TeamInvitation::with('team')->where('email', auth()->user()->email)->paginate(6);
        return Inertia::render('Profile/TeamInvitations', [
            'invitations' => $invitations,
            'title' => 'Team Invitations',
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function resendInvitationMail(Team $team, TeamInvitation $invitation)
    {
        $this->authorize('addTeamMemberByInvitation', $team);
        abort_if($team->isPlayGround() && user()->isUser(), 403);
        Mail::to($invitation->email)->send(new TeamUserInvitation($invitation));
        return redirect()->back()->with('flash', ['message' => 'Team Invitation Send Successfully.']);
    }

    /**
     * @throws AuthorizationException
     */
    public function editTeamInvitation(Team $team, TeamInvitation $invitation)
    {
        $this->authorize('updateTeamMember', $team);
        abort_if($team->isPlayGround() && user()->isUser(), 403);
        abort_if($invitation->team_id != $team->id, 404);
        $serverList = $team->servers()
            ->select('servers.id','name')
            ->whereIn('id',$invitation->getPermission('selected_servers'))
            ->whereTeamId($team->id)
            ->get();
        $siteList = $team->sites()
            ->select('sites.id','sites.name')
            ->whereIn('sites.id',$invitation?->sites)->get();
        $inactive_permissions = ['site:access-magic-login'];
        return Inertia::render('Profile/Team/EditInvitation', [
            'invitation' => $invitation,
            'user_role_list' => Jetstream::$roles,
            'team' => $team,
            'selected_servers' => $invitation->getPermission('selected_servers'),
            'selected_sites' => $invitation?->sites,
            'server_access' => $invitation->getPermission('server_access'),
            'site_access' => $invitation->getPermission('site_access'),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'title' => 'Update Member ' . $invitation->email,
            'inactive_permissions' => $inactive_permissions,
            'serverList' => $serverList,
            'siteList' => $siteList,
        ]);
    }

    /**
     * Update Team Invitation
     * @throws AuthorizationException
     */
    public function updateTeamInvitation(Team $team, TeamInvitation $invitation, TeamInvitationRequest $request)
    {
        $this->authorize('updateTeamMember', $team);
        abort_if($team->isPlayGround() && user()->isUser(), 403);
        abort_if($invitation->team_id != $team->id, 404);
        DB::beginTransaction();
        try {
            $role = $request->string('role');
            $invitation->update([
                'role' => $role,
                'permissions' => $request->only(['server_access', 'selected_servers', 'site_access', 'selected_sites', 'permissions'])
            ]);
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return redirect()->back()->with('flash', ['message' => $exception->getMessage(), 'type' => 'error']);
        }
        return redirect()->back()->with('flash', ['message' => 'Team Invitation Updated Successfully.']);
    }


    /**
     * @throws AuthorizationException
     */
    public function editTeam(Team $team)
    {
        $this->authorize('update', $team);
        abort_if($team->isPlayGround() && user()->isUser(), 403);
        $tags = Tag::associatedTags('teams');
        $selected_tags = $team->tags->pluck('name')->toArray();
        return Inertia::render('Profile/Team/Edit', [
            'user' => auth()->user(),
            'team' => $team,
            'tags' => $tags,
            'team_photo_url' => $team->team_photo_url,
            'selected_tags' => $selected_tags,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function updateTeam(Team $team, TeamRequest $request)
    {
        $this->authorize('update', $team);
        abort_if($team->isPlayGround() && user()->isUser(), 403);
        $team->update($request->only(['name', 'email', 'notification_language']));

        if ($request->has('tags')) {
            $team->syncTags($request->tags);
        }
        if ($request->filled('photo')) {
            $extension = $request->input('photo.extension') ?? 'jpg';
            $path = $team->getPhotoPath($extension);

            if ($team->team_photo_path && Storage::disk('s3')->exists($team->team_photo_path)) {
                Storage::disk('s3')->delete($team->team_photo_path);
            }
            Storage::disk('s3')->copy($request->input('photo.key'), $path);
            $team->update(['team_photo_path' => $path]);
        }
        return redirect()->back()->with('flash', ['message' => 'Team Update Successfully.']);
    }

    /**
     * Delete Team
     * @throws AuthorizationException
     */
    public function deleteTeam(Team $team, TeamDeleteRequest $request)
    {
        $this->authorize('delete', $team);
        abort_if($team->isPlayGround() && user()->isUser(), 403);
        try{
            $team->removeElasticEmailAccount();
            //start transaction
            DB::beginTransaction();
            $team->teamInvitations()->delete();
            $team->memberships()->delete();
            $team->delete();
            DB::commit();
            return redirect()->route('user.team')->with('flash', ['message' => 'Team Deleted Successfully.']);
        }catch (\Exception $exception){
            DB::rollBack();
            Log::error("Team Delete Error: ".$exception->getMessage());
            return redirect()->back()->with('flash', ['message' => "Something went wrong. Please try again later.", 'type' => 'error']);
        }
    }


    /**
     * @throws AuthorizationException
     */
    public function teamDetails(Team $team)
    {
        $this->authorize('view', $team);
        abort_if($team->isPlayGround() && user()->isUser(), 403);

        $team->loadCount(['servers', 'sites'])->load(['tags', 'teamInvitations']);
        $members = $team->users()->withCount([
            'sites',
            'servers',
            'serversByInvitation' => fn($q) => $q->whereIn('server_id', $team->servers->pluck('id'))
        ])->get();
        $members = $members->merge($team->teamInvitations)->paginate(10);
        if (1 === $members->currentPage()) {
            $members->prepend($team->owner);
        }
        return Inertia::render('Profile/Team/TeamDetails', [
            'user' => auth()->user(),
            'team' => $team,
            'members' => $members,
            'title' => $team->name,
            'can_add_team_member' => auth()->user()->can('addTeamMember', $team),
            'can_remove_team_member' => auth()->user()->can('removeTeamMember', $team),
            'can_update_team_member' => auth()->user()->can('updateTeamMember', $team),
            'can_update_team' => auth()->user()->can('update', $team),
            'can_delete_team' => auth()->user()->can('delete', $team),
            'can_leave_team'=> auth()->user()->can('leaveTeam', $team),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function roleManagement()
    {
        return Inertia::render('Profile/RoleManagement', [
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function serverProvider()
    {
        $this->authorize('manageProvider', team());
        $providers = CloudProvider::query()
            ->select(['id', 'name', 'provider', 'api_key'])
            ->where(['team_id' => team()->id])
            ->withCount('servers');
        $allProviders = $providers->paginate(10);
        $allProviders->makeVisible('api_key');
        $availableProviders = [];
        foreach (CloudProviderEnums::notUnderXCloud() as $key => $enum){
            $availableProviders[] = [
                'id' =>  $key + 1,
                'name' => $enum->getProviderReadableAttribute(),
                'provider' => $enum->value
            ];
        }

        return Inertia::render('Profile/ServerProvider', [
            'user' => auth()->user(),
            'providers' => $allProviders,
            'availableProviders' => $availableProviders,
            'title' => 'Server Provider',
            'can_delete_server_provider' => user()->can('deleteServerProvider', user()->currentTeam),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'uniqueProviders' => $providers->get()->unique('provider')
        ]);
    }

//    addServerProvider has been removed as it was not being use anywhere: Consulted with Arif Bhai

    public function updateServerProvider(CloudProvider $provider, Request $request): RedirectResponse
    {
        $this->authorize('update', $provider);
        $validated = $request->validate([
            'label' => 'required|string',
        ]);
        $provider->update([
            'name' => $validated['label'],
            'api_key' => $request->get('api_key')
        ]);
        return redirect()->back()->with('flash', ['message' => 'Server Provider Updated Successfully.']);
    }

    public function removeServerProvider(CloudProvider $provider)
    {
        $this->authorize('delete', $provider);
        $provider->delete();
        return redirect()->back()->with('flash', ['message' => 'Server Provider Removed Successfully.']);
    }

    public function sshKey()
    {
        abort_if(user()->cannot('manageSshKey', team()) || user()->cannot('serverAccess', team()), 403, 'You are not authorized to perform this action.');
        $keys = team()->sshKeyParis()
            ->where('type', 'team_key')
            ->when(user()->cannot('manageSshKey', team()),fn($query) => $query->where(['user_id' => auth()->id()]))
            ->with(['sudoUsers', 'sites'])
            ->paginate();
        return Inertia::render('Profile/SSHKey', [
            'user' => user(),
            'keys' => $keys,
            'servers' => team()->servers()->select(['id', 'name', 'team_id'])->get(),
            'can_delete_ssh_key' => user()->can('serverAccess', user()->currentTeam),
            'can_create_ssh_key' => user()->can('serverAccess', user()->currentTeam),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function browserSessions(Request $request)
    {
        return Inertia::render('Profile/BrowserSessions', [
            'user' => auth()->user(),
            'sessions' => $this->sessions($request),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    /**
     * Get the current sessions.
     *
     * @param Request $request
     * @return \Illuminate\Support\Collection
     */
    public function sessions(Request $request)
    {
        if (config('session.driver') !== 'database') {
            return collect();
        }

        return collect(
            DB::connection(config('session.connection'))->table(config('session.table', 'sessions'))
                ->where('user_id', $request->user()->getAuthIdentifier())
                ->orderBy('last_activity', 'desc')
                ->get()
        )->map(function ($session) use ($request) {
            $agent = $this->createAgent($session);

            return (object)[
                'session_id' => $session->id,
                'agent' => [
                    'is_desktop' => $agent->isDesktop(),
                    'platform' => $agent->platform(),
                    'browser' => $agent->browser(),
                ],
                'ip_address' => $session->ip_address,
                'is_current_device' => $session->id === $request->session()->getId(),
                'last_active' => Carbon::createFromTimestamp($session->last_activity)->diffForHumans(),
            ];
        });
    }

    /**
     * Create a new agent instance from the given session.
     *
     * @param mixed $session
     * @return \Jenssegers\Agent\Agent
     */
    protected function createAgent($session)
    {
        return tap(new Agent, function ($agent) use ($session) {
            $agent->setUserAgent($session->user_agent);
        });
    }

    public function logOutSpecificSession(Request $request, $session_id = null)
    {
        if ($session_id == null || config('session.driver') !== 'database') {
            return redirect()->back()->with('flash',
                ['message' => 'Session Driver is not Database.', 'type' => 'error']);
        }

        DB::connection(config('session.connection'))->table(config('session.table', 'sessions'))
            ->where('user_id', $request->user()->getAuthIdentifier())
            ->where('id', '!=', $request->session()->getId())
            ->where('id', '=', $session_id)
            ->delete();
        return redirect()->back()->with('flash', ['message' => 'Session Deleted Successfully.']);
    }

    public function logOutOtherSessions(Request $request)
    {
        //match password with current password
        $request->validate([
            'password' => 'required|string',
        ]);
        if (!Hash::check($request->password, $request->user()->password)) {
            return redirect()->back()->with('flash', ['message' => 'Password does not match.', 'type' => 'error']);
        }
        if (config('session.driver') !== 'database') {
            return redirect()->back()->with('flash',
                ['message' => 'Session Driver is not Database.', 'type' => 'error']);
        }
        DB::connection(config('session.connection'))->table(config('session.table', 'sessions'))
            ->where('user_id', $request->user()->getAuthIdentifier())
            ->where('id', '!=', $request->session()->getId())
            ->delete();
        return redirect()->back()->with('flash', ['message' => 'Other Sessions Deleted Successfully.']);
    }
    public function authentication()
    {
        return Inertia::render('Profile/Authentication', [
            'requiresConfirmation' => FortifyFeatures::optionEnabled(FortifyFeatures::twoFactorAuthentication(),
                'confirm'),
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function allEvents(Request $request)
    {
        // $this->authorize('manageEvents',team());
        if (!in_array(strtolower($request->get('filter')), [Task::STATUS_RUNNING, Task::STATUS_FINISHED, Task::STATUS_FAILED, Task::STATUS_TIMEOUT])) {
            $request['filter'] = 'All Events';
        }

        if ($request->has('startDate') && $request->has('endDate') && (!$this->isValidDate($request->get('startDate')) || !$this->isValidDate($request->get('endDate')))) {
            return redirect()->route('user.all-events')->with('flash',
                ['message' => 'Invalid Date.', 'type' => 'error']);
        }

        $taskEvents = team()->tasks()
            ->with(['server:id,name', 'site:id,name', 'initiatedBy:id,name,email'])
            ->when($request->has('startDate'), function ($query) use ($request) {
                $query->whereBetween('updated_at', [
                    $request->get('startDate'),
                    Carbon::parse($request->get('endDate'))->addDay()
                ]);
            })
            ->when($request->has('member') && !empty($request->get('member')), function ($query) use ($request) {
                $query->where('initiated_by', $request->get('member'));
            })
            ->when($request->has('filter') && $request->get('filter') !== 'All Events', function ($query) use ($request) {
                $query->where('status', strtolower($request->get('filter')));
            })
            ->when(user()->cannot('siteEvents',team()), function ($query){
                $query->whereDoesntHave('site');
            })
            ->when(user()->isUser() && !team()->isOwner(user()), function ($q) {
                $q->when(user()->hasAnyRole(team(), [Team::SERVER_ADMIN, Team::TEAM_ADMIN])
                    && !in_array(user()->current_team_id, user()->getMeta('all_servers_team', [])),
                    function ($query) {
                        $query->whereHas('server', fn($q) => $q->whereIn('id', user()->serversByInvitation()->pluck('server_id')->toArray()));
                    }
                )
                    ->when(user()->hasAnyRole(team(), [Team::SITE_ADMIN]), function ($builder) {
                        $builder->when(in_array(user()->current_team_id, user()->getMeta('all_site_team', [])), function ($query) {
                            $query->whereHas('site');
                        })->when(!in_array(user()->current_team_id, user()->getMeta('all_site_team', [])), function ($query) {
                            $query->whereHas('site', fn($q) => $q->whereIn('id', user()->sitesByInvitation()->pluck('site_id')->toArray()));
                        });
                    })
                    ->when(team()->isPlayGround(), function ($query) {
                        $query->where('initiated_by', auth()->id());
                    });
            })
            ->latest()
            ->paginate(10)
            ->through(fn($task) => TaskResource::make($task));

        $teamMembers = user()->hasAnyRole(team(), [Team::TEAM_ADMIN]) ? User::whereIn('id',Membership::where('team_id', team()->id)->pluck('user_id')->toArray())
            ->orWhere('id',team()->user_id)
            ->pluck('name','id')
            ->toArray() : [];

        return Inertia::render('Profile/AllEvents', [
            'title' => 'All Events',
            'taskEvents' => $taskEvents,
            'user' => auth()->user(),
            'filter' => $request->filter ?? 'All Events',
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'showServer'=> user()->hasAnyRole(team(),[Team::TEAM_ADMIN,Team::SERVER_ADMIN]),
            'teamMembers' => $teamMembers,
            'member' => $request->member ?? 0,
        ]);
    }

    public function isValidDate($date): bool
    {
        $regex = '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/';
        return preg_match($regex, $date);
    }

    public function storageProvider()
    {
        $this->authorize('manageStorageProvider', team());
        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $providers = StorageProvider::query()->withCount(['backupSettings'=> fn($q) => $q->whereHas('site')])
            ->where(['team_id' => team()->id])->paginate(10);

        $cloudFlareRegions = CloudFlareR2Regions::asArray();
        $hetznerRegions = HetznerObjectStorageRegions::asArray();
        return Inertia::render('Profile/StorageProvider', [
            'providers' => $providers,
            'title' => 'Storage Provider',
            'can_delete_server_provider' => user()->can('deleteServerProvider', user()->currentTeam),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'providerItems' => StorageProvider::PROVIDERS,
            'cloud_flare_regions' => $cloudFlareRegions,
            'hetzner_regions' => $hetznerRegions
        ]);
    }


    /**
     * Create a new agent instance from the given session.
     *
     * @param StorageProviderRequest $request
     * @return RedirectResponse
     */
    public function storeStorageProvider(StorageProviderRequest $request): RedirectResponse
    {
        $this->authorize('manageStorageProvider', team());
        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        StorageProvider::create($request->validated()+$request->only(['user_id','team_id']));
        return redirect()->back()->with('flash', ['message' => 'Storage Provider Created Successfully.']);
    }

    /**
     * @throws Throwable
     */
    public function updateStorageProvider(StorageProviderRequest $request, StorageProvider $provider): RedirectResponse
    {
        $this->authorize('manageStorageProvider', team());
        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        $updated_keys = array_diff_assoc($request->validationData(),$provider->toArray());
        $provider->update($request->validated()+$request->only(['user_id','team_id']));
        $script_updated_keys = ['access_key_id','secret_key','region','bucket','endpoint'];
        //check if any of the script updated keys update
        if ($provider->backupSettings()->exists() && count(array_intersect($script_updated_keys,array_keys($updated_keys))) > 0) {
           $jobs = [];
           foreach ($provider->backupSettings as $backupSetting){
                $jobs[] = new UpdateBackupSettings($backupSetting->site,$backupSetting);
           }
            Bus::chain($jobs)->dispatch();
        }
        return redirect()->back()->with('flash', ['message' => 'Storage Provider Updated Successfully.']);
    }

    public function dropStorageProvider(StorageProvider $provider)
    {
        $this->authorize('manageStorageProvider', team());
        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        try{
            DB::beginTransaction();
            if($provider->isGDrive() && $accessToken = $provider->getAccessToken()){
                // Revoke the token using Google's API
                Http::post('https://oauth2.googleapis.com/revoke', [
                    'token' => $accessToken,
                ]);
            }
            BackupFile::where(['storage_provider_id' => $provider->id])
                ->orWhereHas('backupSetting', fn($q) => $q->where(['storage_provider_id' => $provider->id]))
                ->delete();
            $provider->backupSettings()->delete();
            $provider->delete();
            DB::commit();
            return redirect()->back()->with('flash', ['message' => 'Storage Provider Deleted Successfully.']);
        }catch (\Exception $exception){
            DB::rollBack();
            return redirect()->back()->with('flash', ['message' => 'Something went wrong. Please try again later.', 'type' => 'error']);
        }
    }

    public function billingDetails()
    {
        $this->authorize('createBilling', team());

        if (!team()) {
            return redirect()->route('user.team');
        }

        $paymentMethods = team()->paymentMethods()->select([
            'id',
            'payment_gateway',
            'card_no',
            'default_card',
            'meta->stripe->brand as brand',
            'meta->stripe->expiry_month as expiry_month',
            'meta->stripe->expiry_year as expiry_year'
        ])->orderBy('default_card', 'DESC')->get();

        return Inertia::render('Profile/BillingDetails', [
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'paymentMethods' => $paymentMethods
        ]);
    }

    public function billDetails($bill)
    {
        $bill = Bill::where('team_id', team()->id)->findOrFail($bill);
        $defaultCard = team()->activePaymentMethod()->first();

        $paymentMethods = team()->paymentMethods()->select([
            'id',
            'payment_gateway',
            'card_no',
            'default_card',
            'meta->stripe->brand as brand',
            'meta->stripe->expiry_month as expiry_month',
            'meta->stripe->expiry_year as expiry_year'
        ])->orderBy('default_card', 'DESC')->get();

        $this->authorize('createBilling', team());

        return Inertia::render('Profile/Billing/PopupBillPayment', [
            'bill' => BillResource::make($bill),
            'paymentMethods' => $paymentMethods,
            'previousRoute' => url()->previous(),
            'defaultCard' => $defaultCard
        ]);
    }

    public function bills()
    {
        $this->authorize('createBilling', team());
        $bills = team()->bills()->latest()->paginate()->through(fn($bill) => BillResource::make($bill));

        return Inertia::render('Profile/Bills', [
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'plan' => team()->getPlan(),
            'bills' => $bills
        ]);
    }
    public function packages()
    {
        abort_if(isWhiteLabel(),404);
        $packages = team()->packages()->latest()
            ->selectRaw('*,DATE_FORMAT(attached_at, "%d %b %Y %h:%i %p") as attached_at_format')
            ->paginate();
        return Inertia::render('Profile/Packages', [
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'plan' => team()->getPlan(),
            'packages' => $packages
        ]);
    }
    public function products()
    {
        $products = team()->products()->latest()->paginate();

        return Inertia::render('Profile/Products', [
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'plan' => team()->getPlan(),
            'products' => $products
        ]);
    }
    public function billsPayment(Request $request)
    {
        $this->authorize('createBilling', team());

        $paymentMethods = team()->paymentMethods()->select([
            'id',
            'card_no',
            'default_card',
            'payment_gateway',
            'status',
            'meta->stripe->expiry_month as expiry_month',
            'meta->stripe->expiry_year as expiry_year',
            'meta->stripe->brand as brand',
        ])->orderBy('default_card', 'DESC')->get();

        $defaultCard = team()->activePaymentMethod()->first();

        // Make sure we're only counting active services from the current month
        $serviceCounts = team()->bills()
                                ->where('service_is_active', true)
                                ->where('bill_from', '>=', Carbon::now()->startOfMonth())
                                ->groupBy('service')
                                ->selectRaw('service, COUNT(*) as count')
                                ->get();

        $bills = \App\Models\Bill::query()
            ->whereTeamId(team()->id)
            ->when($request->has('billsDateFilter'), function ($query) use ($request) {
                if ($request->get('billsDateFilter') !== 'All Bills') {
                    if($request->get('billsDateFilter') === 'This Month'){
                        $query->whereBetween('created_at', [
                            Carbon::now()->startOfMonth(),
                            Carbon::now()->endOfMonth()
                        ]);
                    }else if($request->get('billsDateFilter') === 'Last Month'){
                        $query->whereBetween('created_at', [
                            Carbon::now()->subMonth()->startOfMonth(),
                            Carbon::now()->subMonth()->endOfMonth()
                        ]);
                    }else if($request->get('billsDateFilter') === 'Last 6 Months'){
                        $query->whereBetween('created_at', [
                            Carbon::now()->subMonths(6)->startOfMonth(),
                            Carbon::now()->endOfMonth()
                        ]);
                    } else if($request->get('billsDateFilter') === 'This Year') {
                        $query->whereBetween('created_at', [
                            Carbon::now()->startOfYear(),
                            Carbon::now()->endOfYear()
                        ]);
                    }
                }
            })
            ->when($request->has('billsPaymentStatusFilter'), function ($query) use ($request) {
                if ($request->get('billsPaymentStatusFilter') === 'Paid') {
                    $query->where('status', BillingStatus::Paid);
                } else if ($request->get('billsPaymentStatusFilter') === 'Unpaid') {
                    $query->where('status', BillingStatus::Unpaid);
                }
            });


        $bills = $bills->when($request->has('billsServiceStatusFilter'), function ($query) use ($request) {
            if ($request->get('billsServiceStatusFilter') === 'Active Billing') {
                $query->whereServiceIsActive(true);
            } else if ($request->get('billsServiceStatusFilter') === 'Inactive Billing') {
                $query->whereServiceIsActive(false);
            }
        });

        $bills = $bills->latest()->paginate()->through(fn($bill) => BillResource::make($bill));

        $renewTime = now()->day(CalculateBill::billingDate);

        return Inertia::render('Profile/Billing/BillsPayment', [
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'paymentMethods' => $paymentMethods,
            'defaultCard' => $defaultCard,
            'billingActive' => team()->billingIsActive(),
            'monthlyBillingAmount' => team()->getMonthlyBillingAmount(),
            'nextMonthBillingAmount' => team()->getNextMonthBillingAmount(),
            'overUsedBillingAmount' => team()->getOverUsedBillingAmount(),
            'currentMonth' => Carbon::now()->format('F'),
            'currentMonthShort' => Carbon::now()->format('M'),
            'currentYear' => Carbon::now()->format('Y'),
            'currentPlan' => team()?->activePlan?->name_as_title,
            'renewTime' => 'renews '. $renewTime->format('d'),
            'serviceCounts' => $serviceCounts,
            'bills' => $bills,
            'payment_amount_to_upgrade' => format_billing(team()->bills()
                                                ->where('has_offer', true)
                                                ->where('service_is_active', true)
                                                ->sum('billing_amount')),
            'billsDateFilter' => $request->billsDateFilter ?? 'All Bills',
            'billsPaymentStatusFilter' => $request->billsPaymentStatusFilter ?? 'Payment Status',
            'billsServiceStatusFilter' => $request->billsServiceStatusFilter ?? 'All Billing',
            'eligible_to_upgrade' => team()?->eligibleToUpgradeNextPlan(),
        ]);
    }

    public function emailSubscriptions(Request $request)
    {
        abort_if(isWhiteLabel(), 404);
        $this->authorize('createBilling', team());

        $emailProviderBills = \App\Models\Bill::query()
            ->whereTeamId(team()->id)
            ->whereService(BillingServices::EmailProvider)
            ->when($request->has('billsDateFilter'), function ($query) use ($request) {
                if ($request->get('billsDateFilter') !== 'All Bills') {
                    if($request->get('billsDateFilter') === 'This Month'){
                        $query->whereBetween('created_at', [
                            Carbon::now()->startOfMonth(),
                            Carbon::now()->endOfMonth()
                        ]);
                    }else if($request->get('billsDateFilter') === 'Last Month'){
                        $query->whereBetween('created_at', [
                            Carbon::now()->subMonth()->startOfMonth(),
                            Carbon::now()->subMonth()->endOfMonth()
                        ]);
                    }else if($request->get('billsDateFilter') === 'Last 6 Months'){
                        $query->whereBetween('created_at', [
                            Carbon::now()->subMonths(6)->startOfMonth(),
                            Carbon::now()->endOfMonth()
                        ]);
                    } else if($request->get('billsDateFilter') === 'This Year') {
                        $query->whereBetween('created_at', [
                            Carbon::now()->startOfYear(),
                            Carbon::now()->endOfYear()
                        ]);
                    }
                }
            })
            ->when($request->has('billsPaymentStatusFilter'), function ($query) use ($request) {
                if ($request->get('billsPaymentStatusFilter') === 'Paid') {
                    $query->where('status', BillingStatus::Paid);
                } else if ($request->get('billsPaymentStatusFilter') === 'Unpaid') {
                    $query->where('status', BillingStatus::Unpaid);
                }
            });


        $emailProviderBills = $emailProviderBills->when($request->has('billsServiceStatusFilter'), function ($query) use ($request) {
            if ($request->get('billsServiceStatusFilter') === 'Active Billing') {
                $query->whereServiceIsActive(true);
            } else if ($request->get('billsServiceStatusFilter') === 'Inactive Billing') {
                $query->whereServiceIsActive(false);
            }
        });

        $emailProviderBills = $emailProviderBills->latest()->paginate()->through(fn($bill) => BillResource::make($bill));

        $subAccountEmail = Arr::get(team()->meta, 'elastic_email.subAccount.emailOnSubaccount');

        $subAccountDetails = (new ElasticEmailService(config('services.elastic_email.api_key')))->getSubAccount(email: $subAccountEmail);

        $totalEmails = Arr::get($subAccountDetails, 'Settings.Email.MonthlyRefillCredits');

        $availableCredits = Arr::get($subAccountDetails, 'EmailCredits');

        return Inertia::render('Profile/EmailProvider/EmailSubscriptions', [
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'hasXcloudEmailSubscription' => team()->hasXcloudEmailProvider(),
            'currentMonth' => Carbon::now()->format('F'),
            'currentYear' => Carbon::now()->format('Y'),
            'currentPlan' => team()?->activePlan?->name_as_title,
            'emailProviderBills' => $emailProviderBills,
            'billsDateFilter' => $request->billsDateFilter ?? 'All Bills',
            'billsPaymentStatusFilter' => $request->billsPaymentStatusFilter ?? 'Payment Status',
            'billsServiceStatusFilter' => $request->billsServiceStatusFilter ?? 'All Billing',
            'totalEmails' => $totalEmails ?? 0,
            'availableCredits' => $availableCredits ?? 0,
            'api_key' => team()->getElasticEmailSubAccountApiKey(),
            'maskedApiKey' => team()->masked_api_key
        ]);
    }

    public function patchstackSubscriptions()
    {
        abort_if(isWhiteLabel(), 404);
        $this->authorize('createBilling', team());

        $siteIds = team()->sites->pluck('id')->toArray();
        $patchstackVulnerabilities = PatchstackVulnerability::with('site')->whereIn('site_id', $siteIds)->get();

        return Inertia::render('Profile/PatchstackSubscriptions', [
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'patchstackVulnerabilities' => $patchstackVulnerabilities
        ]);
    }

    public function whitelabelSubscriptions(Request $request)
    {
        $subscriptionProduct = team()->subscriptions()->first();
        if (!$subscriptionProduct) {
            return redirect()->route('white-label.onboarding.startup')
                ->with('flash', [
                    'message' => 'Please subscribe to white label package first.',
                    'type' => 'error'
                ]);
        }
        $activeSubscription = SubscriptionProduct::formatPackage(subscriptionProduct: $subscriptionProduct);
        $whiteLabelPackages = SubscriptionProduct::whiteLabelPackages(activeProduct: $subscriptionProduct);
        return Inertia::render('Profile/Billing/WhitelabelSubscriptions', [
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'whitelabel' => team()->ownedWhiteLabel,
            'activeSubscription' => $activeSubscription,
            'whiteLabelPackages' => $whiteLabelPackages,
        ]);
    }

    public function detailedInvoices($invoiceId = null)
    {
        $this->authorize('createBilling', team());

        $invoices = GeneralInvoice::query()
            ->whereTeamId(team()->id)
            ->when($invoiceId, fn ($query) =>  $query->where('id', $invoiceId))
            ->when(request()->filled('invoiceDateFilter'),function($query){
                $query->when(request()->get('invoiceDateFilter') === 'This Month', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ]);
                })->when(request()->get('invoiceDateFilter') === 'Last Month', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->subMonth()->startOfMonth(),
                        Carbon::now()->subMonth()->endOfMonth()
                    ]);
                })->when(request()->get('invoiceDateFilter') === 'Last 6 Months', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->subMonths(6)->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ]);
                })->when(request()->get('invoiceDateFilter') === 'This Year', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->startOfYear(),
                        Carbon::now()->endOfYear()
                    ]);
                });
            })
            ->selectRaw('invoices.*, date_format(invoices.created_at, "%d %b %Y") as date');

        if(request()->has('invoiceDateFilter')) {
            $invoices = $invoices->when(request()->has('invoiceStatusFilter'), function ($query) {
                if (request()->get('invoiceStatusFilter') === 'Paid Invoices') {
                    $query->whereStatus(BillingStatus::Paid);
                } else if (request()->get('invoiceStatusFilter') === 'Unpaid Invoices') {
                    $query->whereStatus(BillingStatus::Unpaid);
                } else if (request()->get('invoiceStatusFilter') === 'Failed Invoices') {
                    $query->where(function ($query) {
                        $query->whereStatus(BillingStatus::Failed)
                            ->orWhere('status', BillingStatus::PaymentFailed);
                    });
                }
            });
        }

        $invoices =  $invoices->latest('id')->paginate(10);

        $paymentMethods = team()->paymentMethods()->select([
            'id',
            'card_no',
            'default_card',
            'payment_gateway',
            'status',
            'meta->stripe->expiry_month as expiry_month',
            'meta->stripe->expiry_year as expiry_year',
            'meta->stripe->brand as brand',
        ])->orderBy('default_card', 'DESC')->get();

        $defaultCard = team()->activePaymentMethod()->first();

        return Inertia::render('Profile/DetailedInvoices', [
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'invoices' => $invoices,
            'invoiceDateFilter' => request('invoiceDateFilter','All Invoices'),
            'invoiceStatusFilter' => request('invoiceStatusFilter', 'All Invoices'),
            'title' => 'Detailed Invoices',
            'paymentMethods' => $paymentMethods,
            'isStaging' => !app()->environment('production'),
            'isAdmin' => user()->isAdmin(),
            'isImpersonating' => session()->has('impersonate'),
            'defaultCard' => $defaultCard,
        ]);
    }

    public function manualInvoices($invoiceId = null)
    {
        $this->authorize('manualInvoice', team());
        $invoices = ManualInvoice::query()
            ->whereTeamId(team()->id)
            ->when($invoiceId, fn ($query) =>  $query->where('id', $invoiceId))
            ->when(request()->filled('invoiceDateFilter'),function($query){
                $query->when(request()->get('invoiceDateFilter') === 'This Month', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ]);
                })->when(request()->get('invoiceDateFilter') === 'Last Month', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->subMonth()->startOfMonth(),
                        Carbon::now()->subMonth()->endOfMonth()
                    ]);
                })->when(request()->get('invoiceDateFilter') === 'Last 6 Months', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->subMonths(6)->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ]);
                })->when(request()->get('invoiceDateFilter') === 'This Year', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->startOfYear(),
                        Carbon::now()->endOfYear()
                    ]);
                });
            })
            ->selectRaw('invoices.*, date_format(invoices.created_at, "%d %b %Y") as date');

        if(request()->has('invoiceDateFilter')) {
            $invoices = $invoices->when(request()->has('invoiceStatusFilter'), function ($query) {
                if (request()->get('invoiceStatusFilter') === 'Paid Invoices') {
                    $query->whereStatus(BillingStatus::Paid);
                } else if (request()->get('invoiceStatusFilter') === 'Unpaid Invoices') {
                    $query->whereStatus(BillingStatus::Unpaid);
                } else if (request()->get('invoiceStatusFilter') === 'Failed Invoices') {
                    $query->where(function ($query) {
                        $query->whereStatus(BillingStatus::Failed)
                            ->orWhere('status', BillingStatus::PaymentFailed);
                    });
                }
            });
        }

        $invoices =  $invoices->latest('id')->paginate(10);

        return Inertia::render('Profile/DetailedInvoices', [
            'user' => auth()->user(),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'invoices' => $invoices,
            'invoiceDateFilter' => request('invoiceDateFilter','All Invoices'),
            'invoiceStatusFilter' => request('invoiceStatusFilter', 'All Invoices'),
            'title' => "Manual Invoices",
            'activeItem' => 'Manual Invoices'
        ]);
    }
    public function notifications()
    {
        $this->authorize('manageNotifications', team());
        return Inertia::render('Profile/Notifications', [
            'user' => auth()->user(),
            'notifications' => auth()->user()->notification,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function updateNotifications(Request $request)
    {
        $this->authorize('manageNotifications', team());
        $data = $request->validate([
            'server_telegram' => 'nullable|bool',
            'server_whatsapp' => 'nullable|bool',
            'server_slack' => 'nullable|bool',
            'server_email' => 'nullable|bool',
            'server_xcloud' => 'nullable|bool',
            'server_provisioned' => 'nullable|bool',
            'site_telegram' => 'nullable|bool',
            'site_whatsapp' => 'nullable|bool',
            'site_slack' => 'nullable|bool',
            'site_email' => 'nullable|bool',
            'site_xcloud' => 'nullable|bool',
            'other_telegram' => 'nullable|bool',
            'other_whatsapp' => 'nullable|bool',
            'other_slack' => 'nullable|bool',
            'other_email' => 'nullable|bool',
            'other_xcloud' => 'nullable|bool',
            'do_not_sent_sensitive_info' => 'nullable|bool',
            'vulnerability' => 'nullable|bool',
        ]);
        auth()->user()->update(['notification' => array_filter($data)]);
        return redirect()->back()->with('flash', ['message' => 'Notifications Updated Successfully.']);
    }
    /**
     * Get user and users teams archive servers.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function archiveServers(Request $request)
    {
        $this->authorize('manageArchiveServers', team());
        $servers = team()->servers()->accessFilter()->with(['cloudProvider'])->onlyTrashed()->get();
        return Inertia::render('Profile/ArchiveServers', [
            'servers' => $servers,
            'title' => 'Archive Servers',
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);

    }

    /**
     * Delete user current team archive servers.
     *
     * @param Request $request
     * @return RedirectResponse | Response
     * @throws AuthorizationException
     */
    public function deleteServer(Server $server)
    {
        $this->authorize('forceDelete', $server);
        $server->forceDelete();
        return redirect()->back()->with('flash', ['message' => 'Server Deleted Successfully.', 'type' => 'success']);
    }

    /**
     * Restore user current team archive servers.
     *
     * @param Request $request
     * @return RedirectResponse | Response
     * @throws AuthorizationException
     */
    public function restoreServer(Server $server)
    {
        $this->authorize('restore', $server);
        $server->restore();
        return redirect()->back()->with('flash', ['message' => 'Server Restored Successfully.', 'type' => 'success']);
    }

    public function cloudflareIntegration(Request $request)
    {
        $this->authorize('cloudflareIntegration', team());

        $cloudflareIntegrations = team()->cloudflareIntegrations()
            ->select([
                '*',
                'api_token as masked_api_token',
                'global_api_key as masked_global_api_key',
                'global_ca_key as masked_global_ca_key'
            ])
            ->orderBy('id', 'DESC')
            ->paginate(10);

        return Inertia::render('Profile/CloudflareIntegration', [
            'user' => auth()->user(),
            'cloudflareIntegrations' => $cloudflareIntegrations,
            'filter' => $request->filter ?? 'All Events',
            'title' => 'Cloudflare Integrations',
            'canManageCloudflareIntegration' => user()->can('cloudflareIntegration', user()->currentTeam),
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'maxIntegrationLimitReached' => team()->maxCloudflareIntegrationLimitReached(),
            'maxIntegrationLimit' => team()->getMeta('cloudflare_integration_limit') ?? Cloudflare::MAX_INTEGRATION_LIMIT
        ]);
    }

    public function disable2FAWarning() {
        user()->update(['meta->disable_2fa_warning' => true]);
        return redirect()->back()->with('flash', ['message' => '2FA Warning Disabled Successfully.', 'type' => 'success']);
    }

    public function notificationIntegration()
    {
        abort_if(isWhiteLabel(),404);
        $this->authorize('manageNotifications', team());
        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        $connected_integrations = NotificationIntegration::query()->where([
                'team_id' => team()->id
            ])
            ->where('is_connected', true)
            ->select('type')
            ->get();
        $integrations = NotificationIntegration::query()->where('is_connected', true)->where(['team_id' => team()->id])->paginate(10);
        $telegramToken = hashid_encode(team()->id, 16);

        return Inertia::render('Profile/NotificationIntegration', [
            'user' => auth()->user(),
            'integrations' => $integrations,
            'title' => 'Notifications',
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'providerItems' => NotificationIntegrationTypes::asValueLabel(),
            'connected_integrations' => $connected_integrations,
            'telegramToken' => $telegramToken
        ]);
    }

    public function storeNotificationIntegration(NotificationIntegrationRequest $request)
    {
        abort_if(isWhiteLabel(),404);
        $this->authorize('manageNotifications', team());
        if (team()->isTrailMode()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        $type = $request?->type;
        $phone = $request->phone ?? '';
        if ($type === NotificationIntegrationTypes::Slack->value) {
            $slackRedirectUrl = config('xcloud-notification.slack.redirect_uris').'?client_id='.config('xcloud-notification.slack.client_id'). '&redirect_uri=' . config('app.url') . '/user/slack/redirect' .'&scope=incoming-webhook&user_scope=&state=' . \Str::random(10);
            return response()->json($slackRedirectUrl);
        } elseif($type === NotificationIntegrationTypes::WhatsApp->value) {
            return $this->connectWhatsApp($type, $phone);
        } elseif($type === NotificationIntegrationTypes::Telegram->value) {
            if (NotificationIntegration::query()->where(['team_id' => team()->id])->where('is_connected', false)->exists()) {
                return $this->connectTelegram($type);
            } else {
                return response()->json(['type' => 'error', 'message' => 'You need to authenticate first with xCloudHostingBot.'], 422);
            }
        }
    }

    public function slackRedirect(Request $request)
    {
        try {
            $code = $request->get('code');

            $response = Http::asForm()->post(config('xcloud-notification.slack.access_api'), [
                'client_id' => config('xcloud-notification.slack.client_id'),
                'client_secret' => config('xcloud-notification.slack.client_secret'),
                'code' => $code,
                'redirect_uri' => config('app.url') . '/user/slack/redirect'
            ]);

            $webhook = json_encode($response->json());
            $webhookData = json_decode($webhook);

            $uniqueId = $webhookData?->app_id.$webhookData?->incoming_webhook?->channel_id;
            $checkIntegration = NotificationIntegration::where([
                'team_id' => team()->id,
                'unique_id' => $uniqueId
            ])->exists();

            if ($checkIntegration) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'This channel already connected.',
                    'type' => 'error'
                ]);
            }

            if ($webhookData?->ok) {
                $metaData = $response->json();
                unset($metaData['access_token']);

                NotificationIntegration::create([
                    'title' => $webhookData?->team?->name.' - #'.$webhookData?->incoming_webhook?->channel,
                    'is_connected' => true,
                    'unique_id' => $uniqueId,
                    'type' => NotificationIntegrationTypes::Slack,
                    'status' => NotificationIntegrationStatus::CONNECTED,
                    'token' => $webhookData?->access_token,
                    'meta' => $metaData,
                    'user_id' => user()->id,
                    'team_id' => team()->id,
                ]);

                return redirect()->route('user.notification-integration')->with('flash', ['message' => 'Slack is connected.']);
            } else {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'Slack is not connected',
                    'type' => 'error'
                ]);
            }

        } catch (Exception $e) {
            Log::error($e);
            return redirect()->route('user.notification-integration')->with('flash', [
                'message' => 'Slack is not connected',
                'type' => 'error'
            ]);
        }
    }

    public function disconnectNotificationIntegration(Request $request, NotificationIntegration $notificationIntegration)
    {
        if ($notificationIntegration->type->value == NotificationIntegrationTypes::Slack->value) {
            $slackIntegration = team()->integration(NotificationIntegrationTypes::Slack);
            return $this->slackDisconnect($slackIntegration, $notificationIntegration);
        } elseif ($notificationIntegration->type->value == NotificationIntegrationTypes::WhatsApp->value) {
            $whatsAppIntegration = team()->integration(NotificationIntegrationTypes::WhatsApp);
            return $this->whatsAppDisconnect($whatsAppIntegration, $notificationIntegration);
        } else {
            $telegramIntegration = team()->integration(NotificationIntegrationTypes::Telegram);
            return $this->telegramDisconnect($telegramIntegration, $notificationIntegration);
        }
    }

    public function reconnectNotificationIntegration(Request $request, NotificationIntegration $notificationIntegration)
    {
        try {
            $slackIntegration =team()->integration(NotificationIntegrationTypes::Slack);
            if (!$slackIntegration) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'Slack cannot reconnect.',
                    'type' => 'error'
                ]);
            }

            $reconnect = $slackIntegration->service()->reconnect($notificationIntegration);
            if (!$reconnect) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'Slack cannot reconnect.',
                    'type' => 'error'
                ]);
            }

            return redirect()->route('user.notification-integration')->with('flash', ['message' => 'Slack is reconnected.']);
        } catch (Exception $e) {
            Log::error($e);
            return redirect()->route('user.notification-integration')->with('flash', [
                'message' => 'Slack cannot reconnect.',
                'type' => 'error'
            ]);
        }
    }

    public function whatsAppCallback(Request $request)
    {
        Log::info('WhatsApp Callback: ', $request->all());
        $statuses = $request->input('statuses');

        foreach ($statuses as $status) {
            $wamid = $status['id'];
            $messageStatus = $status['status'];

            Log::info("Message {$wamid} is now {$messageStatus}");
        }

        return response()->json(['status' => 'success']);
    }

    private function slackDisconnect($slackIntegration, $notificationIntegration)
    {
        try {
            if (!$slackIntegration) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'Slack cannot disconnected.',
                    'type' => 'error'
                ]);
            }

            $disconnect = $slackIntegration->service()->disconnect($notificationIntegration);
            if (!$disconnect) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'Slack cannot disconnected.',
                    'type' => 'error'
                ]);
            }

            return redirect()->route('user.notification-integration')->with('flash', ['message' => 'Slack is disconnected.']);
        } catch (Exception $e) {
            Log::error($e);
            return redirect()->route('user.notification-integration')->with('flash', [
                'message' => 'Slack cannot disconnected.',
                'type' => 'error'
            ]);
        }
    }

    private function whatsAppDisconnect($whatsAppIntegration, $notificationIntegration)
    {
        try {
            if (!$whatsAppIntegration) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'WhatsApp cannot disconnected.',
                    'type' => 'error'
                ]);
            }

            $disconnect = $whatsAppIntegration->service()->disconnect($notificationIntegration);
            if (!$disconnect) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'WhatsApp cannot disconnected.',
                    'type' => 'error'
                ]);
            }

            return redirect()->route('user.notification-integration')->with('flash', ['message' => 'WhatsApp is disconnected.']);
        } catch (Exception $e) {
            Log::error($e);
            return redirect()->route('user.notification-integration')->with('flash', [
                'message' => 'WhatsApp cannot disconnected.',
                'type' => 'error'
            ]);
        }
    }

    private function telegramDisconnect($telegramIntegration, $notificationIntegration)
    {
        try {
            if (!$telegramIntegration) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'Telegram cannot disconnected.',
                    'type' => 'error'
                ]);
            }

            $disconnect = $telegramIntegration->service()->disconnect($notificationIntegration);
            if (!$disconnect) {
                return redirect()->route('user.notification-integration')->with('flash', [
                    'message' => 'Telegram cannot disconnected.',
                    'type' => 'error'
                ]);
            }

            return redirect()->route('user.notification-integration')->with('flash', ['message' => 'Telegram is disconnected.']);
        } catch (Exception $e) {
            Log::error($e);
            return redirect()->route('user.notification-integration')->with('flash', [
                'message' => 'Telegram cannot disconnected.',
                'type' => 'error'
            ]);
        }
    }

    private function connectWhatsApp($type, $phone)
    {
        $client = new Client();
        $phoneNumberId = config('xcloud-notification.whatsapp.phone_number_id');
        $token = config('xcloud-notification.whatsapp.access_token');
        $apiVersion = config('xcloud-notification.whatsapp.api_version');

        try {
            $response = $client->post("https://graph.facebook.com/{$apiVersion}/{$phoneNumberId}/messages", [
                'headers' => [
                    'Authorization' => 'Bearer '.$token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'messaging_product' => 'whatsapp',
                    'to' => $phone,
                    'type' => 'template',
                    'template' => [
                        'name' => 'purchase_transaction_alert',
                        'language' => ['code' => 'en_US'],
                        'components' => [
                            [
                                'type' => 'header',
                                'parameters' => [
                                    [
                                        'type' => 'text',
                                        'text' => team()->name
                                    ]
                                ]
                            ],
                            [
                                'type' => 'body',
                                'parameters' => [
                                    [
                                        'type' => 'text',
                                        'text' => 'WhatsApp is connected successfully.'
                                    ]
                                ]
                            ],
                            [
                                'type' => 'button',
                                'sub_type' => 'url',
                                'index' => '0',
                                'parameters' => [
                                    [
                                        'type' => 'text',
                                        'text' => 'user/notification-integration'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                Log::info('WhatsApp notification sent to: '.$phone.'. Response: '.$response->getBody()->getContents());

                $metaData = [
                    "messaging_product" => "whatsapp",
                    "to" => $phone,
                    "type" => "template",
                    "template" => [
                        "name" => "purchase_transaction_alert",
                        "language" => [
                            "code" => "en_US"
                        ]
                    ]
                ];

                return NotificationIntegration::create([
                    'title' => $phone,
                    'is_connected' => true,
                    'unique_id' => Str::random(22),
                    'type' => $type,
                    'status' => NotificationIntegrationStatus::CONNECTED,
                    'token' => null,
                    'meta' => $metaData,
                    'user_id' => user()->id,
                    'team_id' => team()->id,
                ]);
            }
        } catch(Exception $e) {
            Log::info('WhatsApp phone number is invalid. WhatsApp notification cannot sent to: '.$phone);
        }
    }

    public function connectTelegram($type)
    {
        $integration = team()->integration(NotificationIntegrationTypes::Telegram);

        if (!blank($integration) && isset($integration->meta['message'])) {
            $chatId = $integration->meta['message']['chat']['id'];
            try {
                $client = new Client();
                $response = $client->post("https://api.telegram.org/bot" . config('xcloud-notification.telegram.token') . "/sendMessage", [
                    'json' => [
                        'chat_id' => $chatId,
                        'text' => 'Successfully authorized this chat to xCloud Notification Bot. You are now able to receive Telegram notification for this chat.',
                    ],
                ]);

                $integration->update([
                    'is_connected' => true,
                    'status' => NotificationIntegrationStatus::CONNECTED
                ]);

                Log::info('Telegram connection successfully. Response: '.$response->getBody()->getContents());

                return $integration;
            } catch (Exception $e) {
                Log::info('Telegram connection failed.');
                return false;
            }
        }
    }

    public function vulnerableSites()
    {
        $this->authorize('vulnerabilityScan', team());

        if(team()->isFreePlan()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        $filterType = VulnerabilityFilterTypeEnum::fromString(request('filter_type', 'all'));

        $sitesQuery = team()->sites()
            ->accessFilter()
            ->with(['server'])
            ->whereHas('server', fn($query) => $query->whereHas('vulnerabilitySetting'));

        switch ($filterType) {
            case VulnerabilityFilterTypeEnum::ALL:
                $sitesQuery->where(function ($query) {
                    $query->where(function ($q) {
                            $q->whereHas('patchstackVulnerability', fn ($q) => $q->where('is_purchase', true))
                                ->whereHas('patchstackVulnerabilitySite', fn ($q) => $q->where('ignored', false));
                        })
                        ->orWhere(function ($q) {
                            $q->whereDoesntHave('patchstackVulnerability', fn ($q) => $q->where('is_purchase', true))
                                ->whereHas('vulnerabilities', fn ($q) => $q->where('ignored', false));
                        });
                });
                break;

            case VulnerabilityFilterTypeEnum::FREE:
                $sitesQuery->where(function ($q) {
                    $q->whereDoesntHave('patchstackVulnerability', fn ($q) => $q->where('is_purchase', true))
                        ->whereHas('vulnerabilities', fn ($q) => $q->where('ignored', false));
                });
                break;

            case VulnerabilityFilterTypeEnum::PRO:
                $sitesQuery->where(function ($q) {
                    $q->whereHas('patchstackVulnerability', fn ($q) => $q->where('is_purchase', true))
                        ->whereHas('patchstackVulnerabilitySite', fn ($q) => $q->where('ignored', false));
                });
                break;
        }

        // Apply server filter if provided
        if (request()->filled('server')) {
            $sitesQuery->where('server_id', request('server'));
        }

        $sites = $sitesQuery->paginate(10)
            ->through(fn ($site) => SiteResource::globalResource($site));

        $servers = Server::where(['team_id' => team()->id])
            ->whereHas('vulnerabilitySetting')
            ->pluck('name','id')
            ->toArray();

        $product = Product::where('service_type', BillingServices::PatchstackAddon)->first();

        $paymentMethods = team()->paymentMethods()->select([
            'id',
            'card_no',
            'default_card',
            'payment_gateway',
            'status',
            'meta->stripe->expiry_month as expiry_month',
            'meta->stripe->expiry_year as expiry_year',
            'meta->stripe->brand as brand',
        ])->orderBy('default_card', 'DESC')->get();
        $defaultCard = team()->activePaymentMethod()->first();

        return Inertia::render('Profile/Team/VulnerableSites', [
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'sites' => $sites,
            'servers' => $servers,
            'can_create_site' => user()->can('create', Site::class),
            'selected_server' => request('server'),
            'filter_type' => $filterType->value,
            'filter_options' => VulnerabilityFilterTypeEnum::toLabelValueSelectArray(),
            'paymentMethods' => $paymentMethods,
            'defaultCard' => $defaultCard,
            'productPrice' => $product?->price
        ]);
    }

    public function storeViewType(ViewStoreRequest $request)
    {
        $user = $request->user();
        if (!$user) {
            Log::error('Authenticated user not found.');
            return back();
        }

        if (!blank($request->get('dashboard_view_type'))) {
            $user->saveMeta('view_type.dashboard', $request->get('dashboard_view_type'));
        }

        if (!blank($request->get('server_view_type'))) {
            $user->saveMeta('view_type.server', $request->get('server_view_type'));
        }

        if (!blank($request->get('site_view_type'))) {
            $user->saveMeta('view_type.site', $request->get('site_view_type'));
        }

        return back();
    }

    public function language(Request $request)
    {
        $lang = $request->get('lang', 'en');
        user()->update(['language' => $lang]);
        return back();
    }
}
