<?php

namespace App\Http\Controllers;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoicePaymentTypeAPI;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Models\PaymentMethod;
use App\Services\PaymentGateway\API\InvoicePaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Inertia\Inertia;
use Inertia\Response;
use Stripe\Exception\ApiErrorException;
use Stripe\Stripe;

class InvoiceController extends Controller
{
    public function downloadInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->download($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }
    public function previewDownloadInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->stream($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }

    public function downloadManualInvoice(ManualInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->download($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }
    public function previewDownloadManualInvoice(ManualInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->stream($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }

    public function previewAsHtml(GeneralInvoice $invoice)
    {
        if (!user()->isSuperAdmin()) {
            abort(404);
        }

        $invoice->load(['paymentMethod', 'cartForm']);

        $bills = $invoice->bills->groupBy(function ($bill) {
            return $bill->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        });

        $cartForms = $invoice->cartForm->groupBy(function ($cartForm) use ($invoice) {
            return $cartForm->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        })->map(function ($cartFormGroup) use ($invoice) {
            return $cartFormGroup->filter(function ($cartForm) use ($invoice) {
                // Check if the cart form product/package id matches any bill's product/package id
                return !$invoice->bills->contains(function ($bill) use ($cartForm) {
                    return $bill->product_id === $cartForm->product_id || $bill->package_id === $cartForm->package_id;
                });
            });
        })->filter(function ($group) {
            // Ensure we only keep non-empty groups
            return $group->isNotEmpty();
        });;

        $cartFormAmount = 0;

        foreach ($cartForms as $cartForm) {
            foreach ($cartForm as $cart) {
                $cartFormAmount += $cart?->package?->price ?: $cart?->product?->price ?: $cart?->subscriptionProduct?->price;
            }
        }

        return view('Invoice.general', [
            'invoice' => $invoice,
            'billsGroup' => $bills,
            'cartFormsGroup' => $cartForms,
            'isPaid' => $invoice->status->is(BillingStatus::Paid),
            'currentMonth' => Carbon::now()->format('F'),
            'cartFormAmount' => $cartFormAmount,
            'billableAmount' => ($invoice->bills->sum('amount_to_pay') + $cartFormAmount) ?: $invoice->amount,
            'paymentMethod' => $invoice->paymentMethod,
        ]);
    }

    /**
     * @param Request $request
     * @param GeneralInvoice $invoice
     * @return Response
     */
    public function payInvoice(Request $request, GeneralInvoice $invoice)
    {
        $this->authorize('canPay', $invoice);

        // Get the current URL to determine where to return after payment
        $returnUrl = url()->previous();

        $params = $request->query();

        if (isset($params['redirect'])) {
            unset($params['redirect']);
        }

        $paymentMethods = team()->paymentMethods()->select([
            'id',
            'card_no',
            'default_card',
            'payment_gateway',
            'status',
            'meta->stripe->expiry_month as expiry_month',
            'meta->stripe->expiry_year as expiry_year',
            'meta->stripe->brand as brand',
        ])->orderBy('default_card', 'DESC')->get();

        // Check if we're on the detailed invoices page
        return Inertia::render('Invoice/InvoicePaymentPage', [ // Todo: Need to replace with 'Invoice/PayInvoice'
            'invoice' => $invoice,
            'paymentMethods' => $paymentMethods,
            'defaultCard' => $paymentMethods->where('default_card', true)->first(),
            'returnUrl' => $returnUrl,
            'redirect' => $request->get('redirect'),
            'urlParams' => $params,
            'successRedirect' => $request->get('success_redirect'),
            'failureRedirect' => $request->get('failure_redirect'),
        ]);
    }

    public function invoiceDetails(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        $invoice->load([
            'paymentMethod',
            'bills',
            'cartForm'
        ])->loadCount(['bills', 'cartForm']);

        return response()->json([
            'invoice' => $invoice->toArray(),
            'bills' => $invoice->bills()->with('generator')->get()->toArray(),
            'paymentMethod' => $invoice->paymentMethod->toArray(),
            'cartForm' => $invoice->cartForm()->get()->toArray(),
        ]);
    }
}
