<?php

namespace App\Http\Controllers;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Models\PaymentMethod;
use App\Services\PaymentGateway\API\InvoicePaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Inertia\Inertia;
use Stripe\Stripe;

class InvoiceController extends Controller
{
    public function downloadInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->download($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }
    public function previewDownloadInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->stream($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }

    public function downloadManualInvoice(ManualInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->download($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }
    public function previewDownloadManualInvoice(ManualInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->stream($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }

    public function previewAsHtml(GeneralInvoice $invoice)
    {
        if (!user()->isSuperAdmin()) {
            abort(404);
        }

        $invoice->load(['paymentMethod', 'cartForm']);

        $bills = $invoice->bills->groupBy(function ($bill) {
            return $bill->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        });

        $cartForms = $invoice->cartForm->groupBy(function ($cartForm) use ($invoice) {
            return $cartForm->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        })->map(function ($cartFormGroup) use ($invoice) {
            return $cartFormGroup->filter(function ($cartForm) use ($invoice) {
                // Check if the cart form product/package id matches any bill's product/package id
                return !$invoice->bills->contains(function ($bill) use ($cartForm) {
                    return $bill->product_id === $cartForm->product_id || $bill->package_id === $cartForm->package_id;
                });
            });
        })->filter(function ($group) {
            // Ensure we only keep non-empty groups
            return $group->isNotEmpty();
        });;

        $cartFormAmount = 0;

        foreach ($cartForms as $cartForm) {
            foreach ($cartForm as $cart) {
                $cartFormAmount += $cart?->package?->price ?: $cart?->product?->price ?: $cart?->subscriptionProduct?->price;
            }
        }

        return view('Invoice.general', [
            'invoice' => $invoice,
            'billsGroup' => $bills,
            'cartFormsGroup' => $cartForms,
            'isPaid' => $invoice->status->is(BillingStatus::Paid),
            'currentMonth' => Carbon::now()->format('F'),
            'cartFormAmount' => $cartFormAmount,
            'billableAmount' => ($invoice->bills->sum('amount_to_pay') + $cartFormAmount) ?: $invoice->amount,
            'paymentMethod' => $invoice->paymentMethod,
        ]);
    }

    public function payInvoice(Request $request, GeneralInvoice $invoice)
    {
        $this->authorize('canPay', $invoice);

        // Check if this is a direct payment request (has paymentMethodId)
        if ($request->has('paymentMethodId')) {
            // Authorize payment method usage
            $paymentMethodId = $request->get('paymentMethodId') ?: team()->activePaymentMethod()->first()?->id;
            if ($paymentMethodId) {
                $paymentMethod = PaymentMethod::find($paymentMethodId);
                if ($paymentMethod) {
                    $this->authorize('can-use', $paymentMethod);
                }
            }

            // Use the shared service to process payment
            $paymentService = new InvoicePaymentService();
            $result = $paymentService->processPayment($request, $invoice);

            // Handle different response types for non-API
            if ($result['type'] === 'success') {
                return back()->with('flash', [
                    'success' => $result['message']
                ]);
            } elseif ($result['type'] === 'warning' && isset($result['redirect'])) {
                return redirect($result['redirect']);
            } else {
                return back()->with('flash', [
                    'error' => $result['message']
                ]);
            }
        }

        // If no paymentMethodId, show the payment modal
        Stripe::setApiKey(config('services.stripe.secret_key'));

        if ($invoice->status->is(BillingStatus::Paid) || ($invoice->status->isFailed() && $invoice?->gateway_invoice_or_intent_id)) {
            $invoice->fetchPaymentWebhookFromStripe();
        }

        if($invoice->status->is(BillingStatus::Paid)){
            return back()->with('flash', [
                'success' => 'Invoice already paid!'
            ]);
        }

        // Get the current URL to determine where to return after payment
        $returnUrl = url()->previous();

        $params = $request->query();

        if (isset($params['redirect'])) {
            unset($params['redirect']);
        }

        // Check if we're on the detailed invoices page
        return Inertia::render('Invoice/InvoicePaymentPage', [ // Todo: Need to replace with 'Invoice/PayInvoice'
            'invoice' => $invoice,
            'paymentMethods' => team()->paymentMethods,
            'defaultCard' => team()->paymentMethods->where('is_default', true)->first(),
            'returnUrl' => $returnUrl,
            'redirect' => $request->get('redirect'),
            'urlParams' => $params,
        ]);
    }

    public function invoiceDetails(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        $invoice->load([
            'paymentMethod',
            'bills',
            'cartForm'
        ])->loadCount(['bills', 'cartForm']);

        return response()->json([
            'invoice' => $invoice->toArray(),
            'bills' => $invoice->bills()->with('generator')->get()->toArray(),
            'paymentMethod' => $invoice->paymentMethod->toArray(),
            'cartForm' => $invoice->cartForm()->get()->toArray(),
        ]);
    }

//    public function invoiceDetails(GeneralInvoice $invoice)
//    {
//        $this->authorize('view', $invoice);
//
//        $invoice->load(['paymentMethod', 'bills', 'cartForm'])->loadCount(['bills', 'cartForm']);
//
//        $invoiceData = $invoice->only([
//            'id', 'reference_no', 'invoice_number', 'title', 'description',
//            'amount', 'currency', 'status', 'type', 'source', 'team_id',
//            'due_date', 'created_at', 'updated_at', 'status_readable', 'number'
//        ]);
//
//        $paymentMethodData = $invoice->paymentMethod->only([
//            'id', 'payment_gateway', 'status', 'card_no', 'created_at', 'updated_at'
//        ]);
//        $paymentMethodData['meta'] = [
//            'stripe' => [
//                'brand' => $invoice->paymentMethod->meta['stripe']['brand'],
//                'last4digit' => $invoice->paymentMethod->meta['stripe']['last4digit'],
//                'expiry_year' => $invoice->paymentMethod->meta['stripe']['expiry_year'],
//                'expiry_month' => $invoice->paymentMethod->meta['stripe']['expiry_month'],
//            ]
//        ];
//
//        return response()->json([
//            'invoice' => $invoiceData,
//            'bills' => $invoice->bills()->with('generator')->get(['id', 'amount', 'description'])->toArray(),
//            'paymentMethod' => $paymentMethodData,
//            'cartForm' => $invoice->cartForm()->get(['id', 'name', 'quantity', 'price'])->toArray(),
//        ]);
//    }

}
