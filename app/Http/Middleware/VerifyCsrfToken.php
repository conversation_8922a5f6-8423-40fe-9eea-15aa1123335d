<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        'stripe/*',
        'paypal/*',
        'payment-webhook',
        '/payment_webhook',
        'affiliate-webhook-xcloud',
//        'api/stripe/webhook/team-subscription-product'
    ];
}
