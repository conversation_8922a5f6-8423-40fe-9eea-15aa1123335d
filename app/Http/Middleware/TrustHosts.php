<?php

namespace App\Http\Middleware;

use Illuminate\Http\Middleware\TrustHosts as Middleware;

class TrustHosts extends Middleware
{
    /**
     * Get the host patterns that should be trusted.
     *
     * @return array
     */
    public function hosts()
    {
        $rules = [
            $this->allSubdomainsOfApplicationUrl(),
        ];

        // Allow the white label domain and subdomain
        if ($wl = currentWhiteLabel()) {
            $rules[] = '^(.+\.)?'.preg_quote($wl->domain).'$';
            $rules[] = '^(.+\.)?'.preg_quote($wl->sub_domain_source->value).'$';
        }
        return $rules;
    }
}
