<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CheckUserIsActive
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param  \Closure(Request): (Response|RedirectResponse)  $next
     * @return Response|RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (auth()->check() && $request->user() && ! $request->user()->isActive() && !$request->user()->isAdmin()) {

            auth()->guard('web')->logout();

            return redirect()->route('login')->with(['flash' => [
                'message' => 'Account is not active please contact support',
                'type' => 'error',
            ]]);

        }

        return $next($request);
    }
}
