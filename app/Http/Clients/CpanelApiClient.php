<?php

namespace App\Http\Clients;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class CpanelApiClient
{
    private $apiKey;
    private $host;
    private $username;

    public function __construct(string $username, string $apiKey, string $host)
    {
        $this->username = $username;
        $this->apiKey = $apiKey;
        $this->host = $host;
    }

    public function validateApi(): bool
    {
        try {
            $response = $this->getDiskInfo()->collect();
            return $response->get('status') === 1;
        } catch (\Exception $e) {
            return false;
        }


    }

    public function isFileExists(string $path): bool
    {
        $response = Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Fileman/get_file_information', [
            'path' => $path
        ])->collect();
        return $response->get('status') === 1;
    }

    public function checkFtpUserExists(string $user): bool
    {
        $response =  Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Ftp/ftp_exists', [
            'user' => $user
        ])->collect();
        return $response->get('status') === 1;
    }

    public function getAllDomainData(): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/DomainInfo/domains_data');
    }

    public function getHostName(): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Variables/get_session_information');

    }

    public function getDiskInfo(): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Quota/get_local_quota_info');
    }

    public function listDomains():Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/DomainInfo/list_domains');
    }

    public function createBackup(string $destination = 'home-directory', string $email = null): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Backup/fullbackup_to_homedir', [
            'destination' => $destination,
            'email' => $email
        ]);
    }

    public function listFiles(string $directory = '', bool $showHidden = true): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Fileman/list_files', [
            'dir' => $directory,
            'showhidden' => $showHidden ? 1 : 0
        ]);
    }

    public function getFileInformation(string $path): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Fileman/get_file_information', [
            'path' => $path
        ]);
    }

    public function addFtpUser(string $password,string $user='xcloud_cpanel', int $quota = 0, string $homeDirectory = ''): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Ftp/add_ftp', [
            'user' => $user,
            'pass' => $password,
            'quota' => $quota,
            'homedir' => $homeDirectory
        ]);
    }

    public function deleteFtpUser($user): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Ftp/delete_ftp', [
            'user' => $user
        ]);
    }

    public function getBackupFiles(): Response
    {
        return Http::withHeaders([
            'Authorization' => 'cpanel '.$this->username.':'.$this->apiKey
        ])->get($this->host.'/execute/Backup/list_backups');
    }

}
