<?php

namespace App\Http\Requests;

use App\Enums\ViewType;
use Illuminate\Foundation\Http\FormRequest;

class ViewStoreRequest extends FormRequest
{
    public function rules()
    {
        $viewTypes = [ViewType::LIST->value, ViewType::GRID->value];

        return [
            'dashboard_view_type' => ['required_with:dashboard_view_type', 'in:' . implode(',', $viewTypes)],
            'server_view_type'    => ['required_with:server_view_type', 'in:' . implode(',', $viewTypes)],
            'site_view_type'      => ['required_with:site_view_type', 'in:' . implode(',', $viewTypes)],
        ];
    }

    public function authorize()
    {
        return true;
    }
}
