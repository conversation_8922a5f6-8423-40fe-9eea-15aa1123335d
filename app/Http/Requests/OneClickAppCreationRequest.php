<?php

namespace App\Http\Requests;

use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Models\PhpVersion;
use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Rules\DatabasePassword;
use App\Rules\SiteNameRule;
use App\Rules\SiteUserRule;
use App\Services\Database\DatabaseNameGenerator as Generator;
use App\Services\Database\DatabaseProvider;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OneClickAppCreationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if ($this->route('server') instanceof \App\Models\Server) {
            return $this->user()->can('addSite', $this->route('server'));
        }

        $server = \App\Models\Server::find($this->route('server'));

        return $server && $this->user()->can('addSite', $server);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => SiteNameRule::nameRules(
                $this->get('domain_parking_method'),
                $this->filled('name'),
                $this->route('server')->id
            ),
            'title' => ['required', 'string', 'max:255'],
            'app_slug' => ['required', 'string', 'max:255', Rule::in(Arr::pluck(SiteType::getOneClickApps(), 'slug'))],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['nullable', 'string', 'max:255'],
            'domain_parking_method' => ['required', 'string', 'in:go_live,staging_env'],
            'selected_staging_domain' => ['required_if:domain_parking_method,staging_env', 'string'],
            'site_user' => SiteUserRule::rules($this->route('server')->id),

            // Admin user fields
            'admin_user' => ['nullable', 'max:32'],
            'admin_password' => ['nullable', 'max:64'],
            'admin_email' => ['nullable', 'email'],
        ];

        if (in_array($this->get('app_slug'), [SiteType::N8N->value, SiteType::UPTIME_KUMA->value, SiteType::MAUTIC->value])) {
            // Database fields
            $rules['database_provider'] = ['nullable', DatabaseProvider::asRule()];
            $rules['database_name'] = ['nullable', 'max:32', 'lowercase', 'alpha_dash'];
            $rules['database_user'] = ['nullable', 'max:32', 'lowercase', 'alpha_dash'];
            $rules['database_password'] = DatabasePassword::rules();
            $rules['database_host'] = ['required_if:database_provider,custom', 'nullable', 'max:256'];
            $rules['database_port'] = ['required_if:database_provider,custom', 'nullable', 'max:5'];

            // Add port validation only for Node.js apps
            if (in_array($this->input('app_slug'), [SiteType::N8N->value, SiteType::UPTIME_KUMA->value])) {
                $rules['port'] = [
                    'required',
                    'integer',
                    'min:1024',
                    'max:65535',
                    function ($attribute, $value, $fail) {
                        // Check if port is already in use by another site
                        $portExists = Site::where('server_id', $this->route('server')->id)
                            ->where('port', $value)
                            ->exists();

                        if ($portExists) {
                            $fail('The port is already in use by another site on this server.');
                        }
                    }
                ];
            }

            // Add php_version validation for Mautic app
            if ($this->get('app_slug') === SiteType::MAUTIC->value) {
                $rules['php_version'] = ['required', PhpVersion::asRule()];
                $rules['admin_user'] = ['required', 'string', 'max:32'];
                $rules['admin_password'] = ['required', 'string', 'min:8', 'max:64'];
                $rules['admin_email'] = ['required', 'email'];
            }
        } else {
            // This is only case for PHPMyAdmin app, it doesn't need Database fields
            $rules['php_version'] = ['required', PhpVersion::asRule()];
        }

        return $rules;
    }

    public function getSiteData(): array
    {
        $data = $this->validated();

        $siteData = [
            'name' => rtrim($data['name'], '/'),
            'title' => $data['title'],
            'type' => $data['app_slug'],
            'status' => SiteStatus::NEW,
        ];

        // Add php_version if not Node.js app
        if ($data['app_slug'] !== SiteType::N8N->value && $data['app_slug'] !== SiteType::UPTIME_KUMA->value) {
            $siteData['php_version'] = $data['php_version'];
        }

        // Add port if Node.js app
        if (($data['app_slug'] === SiteType::N8N->value || $data['app_slug'] === SiteType::UPTIME_KUMA->value) && isset($data['port'])) {
            $siteData['port'] = $data['port'];
        }

        // Add site user
        if (empty($data['site_user'])) {
            $siteData['site_user'] = Generator::generate($data['name']);
        } else {
            $siteData['site_user'] = $data['site_user'];
        }

        // Add database fields
        $siteData['database_name'] = $data['database_name'] ?? Generator::generate('s_'.$data['name']);
        $siteData['database_user'] = $data['database_user'] ?? Generator::generate('u_'.$data['name']);
        $siteData['database_password'] = $data['database_password'] ?? DatabasePassword::randomPassword();
        $siteData['database_host'] = $data['database_host'] ?? 'localhost';
        $siteData['database_port'] = $data['database_port'] ?? '3306';
        $siteData['database_provider'] = $data['database_provider'] ?? DatabaseProvider::IN_SERVER;

        // Add admin user fields
        if ($data['app_slug'] === SiteType::MAUTIC->value) {
            $siteData['admin_user'] = $data['admin_user'] ?? Str::slug($this->user()->name);
            $siteData['admin_password'] = $data['admin_password'] ?? Str::random(12);
            $siteData['meta->admin_email'] = $data['admin_email'] ?? $this->user()->email;
        }

        if ($this->get('domain_parking_method') === 'staging_env') {
            $siteData['ssl_provider'] = $this->get('domain_parking_method') === 'staging_env' ? SslCertificate::PROVIDER_STAGING : null;
            $siteData['environment'] = $this->get('domain_parking_method') === 'staging_env' ? Site::DEMO : Site::PRODUCTION;
        }

        return $siteData;
    }

    public function messages(): array
    {
        return [
            ...DatabasePassword::messages(),
            ...SiteUserRule::messages(),
        ];
    }
}
