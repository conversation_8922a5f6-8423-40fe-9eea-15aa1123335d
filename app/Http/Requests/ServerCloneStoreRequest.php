<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ServerCloneStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $sourceServer = $this->user()->currentTeam->servers()->find($this->source_server_id);
        $destinationServer = $this->user()->currentTeam->servers()->find($this->destination_server_id);
        return $sourceServer && $destinationServer;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'source_server_id' => 'required|exists:servers,id',
            'destination_server_id' => 'required|exists:servers,id',
        ];
    }
}
