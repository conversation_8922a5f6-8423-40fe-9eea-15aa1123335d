<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RestoreBackupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return user()->can('settings', $this->route('site'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'identifier' => 'required|array',
            'identifier.*' => 'required|array',
            'identifier.*.backup_setting_id' => 'required|exists:backup_settings,id',
            'identifier.*.file_name' => 'required|string|ends_with:sql,gz,zip,manifest',
            'identifier.*.is_sql' => 'required|boolean',
            'identifier.*.id' => 'required|exists:backup_files,id',
            'identifier.*.taken_size' => 'nullable|numeric',
            'identifier.*.date' => 'nullable',
            'restore_type' => 'required|string|in:both,database,file',
            'restore_method' => 'required_if:restore_type,files|string|in:remove,replace',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'identifier.*.file_name.ends_with' => 'File type is not supported',
        ];
    }

    /**
     * Get the backup setting ID from the request.
     *
     * @return int
     */
    public function getBackupSettingId(): int
    {
        return collect($this->validated('identifier'))->pluck('backup_setting_id')->first();
    }

    /**
     * Get the backup file IDs from the request.
     *
     * @return array
     */
    public function getBackupFileIds(): array
    {
        return collect($this->validated('identifier'))->pluck('id')->toArray();
    }
}
