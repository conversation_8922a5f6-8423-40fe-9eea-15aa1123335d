<?php

namespace App\Http\Requests;

use App\Rules\MatchOldPassword;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PasswordUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'current_password' => [
                'nullable',
                'required_with:new_password',
                'string',
                new MatchOldPassword,
            ],
            'new_password' => 'required_with:current_password|string|confirmed|min:8|different:current_password|nullable',
            'new_password_confirmation' => 'required_with:new_password|string|nullable',
        ];
    }
}
