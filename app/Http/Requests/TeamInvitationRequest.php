<?php

namespace App\Http\Requests;

use App\Models\Team;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Laravel\Jetstream\Jetstream;

class TeamInvitationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
      return $this->isMethod(Request::METHOD_POST) ? auth()->user()->can('addTeamMember', $this->team) : auth()->user()->can('updateTeamMember', $this->team);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $permissions = array_key_exists($this->role,Jetstream::$roles) ? Jetstream::$roles[$this->role]?->permissions ?? [] : [];
        $acceptedPermissions = [];
        foreach ($permissions as $permission) {
            $acceptedPermissions = [...array_keys($permission),...$acceptedPermissions];
        }
        return [
            'email' => $this->isMethod(Request::METHOD_POST)  ? ['required', 'email'] : ['nullable', 'email'],
            'role' => ['required', 'string', Rule::in(array_keys(Jetstream::$roles))],
            'server_access' => [Rule::requiredIf(fn () => $this->role !== Team::SITE_ADMIN), 'string', Rule::in(['all', 'choose'])],
            'site_access' => [Rule::requiredIf(fn () => $this->role == Team::SITE_ADMIN), 'string', Rule::in(['all', 'choose'])],
            'permissions' => ['required', 'array'],
            'permissions.*' => ['required', 'string', Rule::in($acceptedPermissions)],
            'selected_servers' => ['nullable', Rule::requiredIf(fn () => $this->server_access === 'choose' && $this->role !== Team::SITE_ADMIN), 'array'],
            'selected_servers.*' => ['nullable',Rule::requiredIf(fn () => $this->server_access === 'choose' && $this->role !== Team::SITE_ADMIN), 'integer', 'exists:servers,id'],
            'selected_sites' => ['nullable',Rule::requiredIf(fn () => $this->site_access === 'choose' && $this->role == Team::SITE_ADMIN), 'array'],
            'selected_sites.*' => ['nullable',Rule::requiredIf(fn () => $this->site_access === 'choose' && $this->role == Team::SITE_ADMIN), 'integer', 'exists:sites,id'],
        ];
    }

    public function messages()
    {
        return [
            'permissions.required' => 'You must select at least one permission.',
        ];
    }

}
