<?php

namespace App\Http\Requests;

use App\Enums\IPAddressType;
use App\Models\Site;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreIpAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return user()->can('settings', $this->route('server')) || user()->can('delete',  $this->route('site'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'ip_address' => [
                'required',
                'ip',
                'not_in:' . $this->route('server')->ipAddress(),
                Rule::unique('ip_addresses')
                    ->where(fn ($q) =>
                        $q->where('ipable_id', $this->route('site')->id)->where('ipable_type', Site::class)
                    ),
            ],
            'type' => ['required', 'string', Rule::in(IPAddressType::asValue())],
        ];
    }
}
