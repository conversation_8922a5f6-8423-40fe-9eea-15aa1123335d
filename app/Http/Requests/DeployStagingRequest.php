<?php

namespace App\Http\Requests;

use App\Rules\DomainComparisonRule;
use App\Rules\SiteNameRule;
use App\Rules\StagingDemoSitesOneLevelDomainRule;
use Illuminate\Foundation\Http\FormRequest;

class DeployStagingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $rules = [];

        if ($this->input('staging_env_type') === 'temporary_domain') {
            $rules['name'] = array_merge(
                SiteNameRule::rules($this->route('server')->id),
                SiteNameRule::reservedDomainsRules('staging_env')
            );
        } else {
            $rules['name'] = array_merge(
                SiteNameRule::rules($this->route('server')->id),
                SiteNameRule::reservedDomainsRules('go_live', true)
            );

            $rules['ssl_provider'] = [
                'nullable', 'in:xcloud,custom,cloudflare', function ($attribute, $value, $fail) {
                    if ($value == 'xcloud' && !$this->route('server')->hasDnsRecord($this->get('name'))) {
                        $fail(__('dns.verification_failed'));
                    }
                }
            ];

            $rules['ssl_certificate'] = ['required_if:ssl_provider,custom'];
            $rules['ssl_private_key'] = ['required_if:ssl_provider,custom'];
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'name.not_regex' => 'The site name you entered is reserved and cannot be used. Please choose a different name.',
            'name.unique' => 'The site name has already been taken. Please choose a different name.',
            'ssl_certificate.required_if' => 'The SSL Certificate is required when using a custom SSL provider.',
            'ssl_private_key.required_if' => 'The SSL Private Key is required when using a custom SSL provider.',
        ];
    }
}
