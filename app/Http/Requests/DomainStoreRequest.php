<?php

namespace App\Http\Requests;

use App\Models\SiteMigration;
use App\Rules\SiteNameRule;
use Faker\Factory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use App\Validator\Domain;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class DomainStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Gate::allows('update', $this->route('server'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $siteMigration = $this->get('site_migration_id') ? SiteMigration::findOrFail($this->get('site_migration_id')) : null;
        $server = $this->route('server');

        $domainName = '';
        if($this->get('domain_parking_method') !== 'staging_env'){
            $domainName = $this->get('domain_name');
        }

        $this->request->add(['domain_name' => $domainName ?? null]); // updating domain_name for request validation

        // Convert the array into a string suitable for the regular expression
        $reservedDomainsPattern = implode('|', array_map('preg_quote', getReservedDomains()));

        return [
            'domain_parking_method' => 'required|string|in:staging_env,migrate_into_new_domain',
            'site_title' => 'required_if:domain_parking_method,migrate_into_new_domain|string|nullable|max:255',
            'domain_name' => [
                'required_if:domain_parking_method,migrate_into_new_domain',
                'nullable',
                'max:255',
                new Domain,
                Rule::unique('sites', 'name')
                    ->where('server_id', $server->id)
                    ->ignore(optional($siteMigration)->site_id),
                $this->get('domain_parking_method') === 'migrate_into_new_domain'
                    ? 'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/'
                    : null
            ],
            'ssl_provider' =>[
                'nullable', 'in:xcloud,custom,cloudflare', function ($attribute, $value, $fail) use ($server, $domainName) {
                    if ($value == 'xcloud' && !$server->hasDnsRecord($domainName)) {
                        $fail(__('dns.verification_failed'));
                    }
                }
            ],
            'ssl_certificate' => 'required_if:ssl_provider,custom|string|nullable',
            'ssl_private_key' => 'required_if:ssl_provider,custom|string|nullable'
        ];
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array<string, string>
     */
    public function messages()
    {
       return [
           'site_title.required_if' => 'Site title is required when parking method is "Migrate into new domain"',
              'domain_name.required_if' => 'Domain name is required when parking method is "Migrate into new domain"',
       ];
    }
}
