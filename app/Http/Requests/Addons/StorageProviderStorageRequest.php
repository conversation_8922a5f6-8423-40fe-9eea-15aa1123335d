<?php

namespace App\Http\Requests\Addons;

use App\Addons\Enum\StorageProvider\RegionEnum;
use App\Enums\XcloudBilling\BillingServices;
use App\Models\AddonStorageProvider;
use App\Models\Product;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorageProviderStorageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return user()->can('create', AddonStorageProvider::class);
    }

    public function rules(): array
    {
        return [
            'bucket_name' => ['required', 'string', 'min:3', 'max:63', 'regex:/^[A-Za-z0-9\-]+$/', 'not_regex:/^-|-$/'], // alphanumeric only
            'region' => ['required', Rule::in(RegionEnum::asValue())],
            'product_id' => [
                'required',
                Rule::exists('products','id')
                    ->where('is_active', true)
                    ->where( 'service_type', BillingServices::xCloudStorageProvider->value)
            ],
        ];
    }



    public function messages(): array
    {
        return [
            'bucket_name.regex' => 'Bucket name must be alphanumeric and can contain hyphens.',
            'bucket_name.not_regex' => 'Bucket name cannot start or end with a hyphen.',
            'bucket_name.min' => 'Bucket name must be at least 3 characters long.',
            'product_id.exists' => 'The selected product is invalid or not active.'
        ];
    }

    public function validated($key = null, $default = null)
    {
        $data = parent::validated($key, $default);
        $product = Product::find($this->input('product_id'));
        $data['type'] = AddonStorageProvider::S3_STORAGE;
        $data['provider'] = AddonStorageProvider::BACKBLAZE;
        $data['product_slug'] = $product?->slug;
        $data['team_id'] = team()->id;
        return $data;
    }
}
