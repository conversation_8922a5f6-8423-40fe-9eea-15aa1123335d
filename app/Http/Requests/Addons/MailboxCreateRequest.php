<?php

namespace App\Http\Requests\Addons;

use App\Addons\Enum\MailboxEnums\MailboxPlans;
use Illuminate\Foundation\Http\FormRequest;

class MailboxCreateRequest extends FormRequest
{
    public function rules()
    {
        return [
            'mailboxes' => 'required|array',
            'mailboxes.*.email' => 'required|email|unique:email_accounts|max:255',
            'mailboxes.*.selected_plan' => 'required|in:' . implode(',', array_keys(MailboxPlans::getReadablePlanName())),
            'mailboxes.*.password' => 'required|string|min:8|max:255|regex:/\d/',
            'mailboxes.*.mailbox_settings' => 'nullable|array',
        ];
    }

    public function authorize()
    {
//        return user()->can('manageBrand', team()->ownedWhiteLabel);
        return true;
    }

    public function messages(): array
    {
        return [
            'mailboxes.required' => 'Mailboxes are required.',
            'mailboxes.*.email.required' => 'Email is required.',
            'mailboxes.*.email.email' => 'Email must be a valid email address.',
            'mailboxes.*.email.unique' => 'Email already exists.',
            'mailboxes.*.selected_plan.required' => 'Mailbox plan is required.',
            'mailboxes.*.selected_plan.in' => 'Mailbox plan is invalid.',
            'mailboxes.*.password.required' => 'Password is required.',
            'mailboxes.*.password.string' => 'Password must be a string.',
            'mailboxes.*.password.min' => 'Password must be at least 8 characters.',
            'mailboxes.*.password.max' => 'Password may not be greater than 255 characters.',
            'mailboxes.*.password.regex' => 'Password must contain at least one number.',
        ];
    }
}
