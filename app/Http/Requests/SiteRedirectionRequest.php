<?php

namespace App\Http\Requests;

use App\Models\Redirection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SiteRedirectionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->user()->can('redirect-manage',$this->route('site'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'redirect_type' => ['required',Rule::in(Redirection::ALLOWED_REDIRECT_TYPES)],
            'from' => ['required','string'],
            'to' => ['required','string','not_in:'.$this->from],
        ];
    }
}
