<?php

namespace App\Http\Requests;

use App\Models\Product;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductDuplicateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'custom_price' => [
                'required',
                'numeric',
                function ($attribute, $value, $fail) {
                    $productId = $this->input('product_id');

                    if ($productId) {
                        $product = Product::find($productId);
                        $minPrice = round($product?->source?->price * 1.1, 2);
                        $customPrice = round(doubleval($value), 2);
                        if ($product && $customPrice < $minPrice) {
                            $fail("The custom price must be at least 10% higher than the original product's price of {$product?->source?->price}. Minimum allowed is {$minPrice}.");
                        }
                    }
                },
            ],
            'sku' => [
                'required',
                'string',
                'max:20',
                Rule::unique('products')->where(function ($query) {
                    return $query->where('white_label_id', team()->ownedWhiteLabel?->id)
                        ->whereNull('deleted_at');
                })
            ]
        ];
    }

    public function authorize(): bool
    {
        return user()->can('manageProducts', team()->ownedWhiteLabel);
    }
}
