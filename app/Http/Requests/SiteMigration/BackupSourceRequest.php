<?php

namespace App\Http\Requests\SiteMigration;

use App\Models\BackupSetting;
use App\Models\Site;
use App\Models\SiteMigration;
use App\Models\StorageProvider;
use App\Validator\Domain;
use App\Validator\StorageBackupFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;

class BackupSourceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return $this->route('siteMigration')->isFilled(SiteMigration::DOMAINS);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'import_type' => 'required|string|in:bucket,site',
            'backup_type' => 'nullable|string|in:local,remote',
            'server_id' => 'nullable|required_if:import_type,site|exists:servers,id',
            'site_id' => [
                'nullable',
                'required_if:import_type,site',
                'exists:sites,id',
                function ($attribute, $value, $fail) {
                    $backupType = $this->input('backup_type');
                    $doesntExist = Site::whereId($value)
                        ->whereHas('backupFiles', function($query) use ($backupType) {
                            $query->where('is_remote', $backupType === 'remote')
                                ->where('backup_files.status', '!=', BackupSetting::FAILED);
                        })
                        ->doesntExist();
                    if ($doesntExist) {
                        $fail("No {$backupType} backup found for this site.");
                    }
                }
            ],
            'bucket_id' => [
                'nullable',
                'required_if:import_type,bucket',
                'exists:storage_providers,id',
                Rule::when($this->input('import_type') == 'bucket', [
                    function ($attribute, $value, $fail) {
                        # Check import_type is bucket and bucket_id exists
                        if ($this->input('import_type') == 'bucket') {
                            $provider = StorageProvider::find($value);
                            if (!($provider && $provider->hasStableConnection())) {
                                $fail('Failed to connect to this bucket');
                            }
                        }

                    }
                ])
            ],
            'domain_name' => [
                'nullable',
                'required_if:import_type,bucket',
                'max:255',
                 Rule::when($this->input('import_type') == 'bucket', [
                     new Domain,
                     new StorageBackupFile()
                 ]),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'site_id.exists' => 'No backup found for this site.',
            'domain_name.required_if' => 'The domain name field is required when importing from a bucket.',
            'domain_name.max' => 'The domain name may not be greater than 255 characters.',
            'domain_name.regex' => 'The domain name format is invalid.',
        ];
    }

    public function isSourceBucket(): bool
    {
       return $this->input('import_type') === 'bucket';
    }


    public function getSanitizeData()
    {
       if ($this->isSourceBucket()){
           return $this->only([
                'import_type',
                'bucket_id',
                'domain_name'
           ]);
       }
         return $this->only([
              'import_type',
              'backup_type',
              'server_id',
              'site_id'
         ]);
    }
}
