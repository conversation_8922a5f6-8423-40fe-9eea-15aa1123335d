<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DisableFileManagerRequest extends FormRequest
{
    public function rules()
    {
        return [
            'disable_file_manager' => 'required|boolean',
            'file_manager_interval' => 'required',
        ];
    }

    public function authorize()
    {
        return user()->can('siteFileManager', team());
    }
}
