<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\StorageProvider;
use App\Enums\BackupType;

class BackupSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return user()->can('settings', $this->route('site')) && !team()->isFreePlan();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $server = $this->route('server'); // the `server` is passed in the route

        return [
            'type' => [
                'required',
                Rule::in(BackupType::asValue()),
            ],
            'is_local' => 'required|boolean',
            'storage_provider_id' => [
                'required_if:is_local,0',
                Rule::exists('storage_providers', 'id')->where('team_id', $server->team_id),
                function ($attribute, $value, $fail) {
                    if (!$this->boolean('is_local')) {
                        $storageProvider = StorageProvider::find($value);
                        if (!$storageProvider || !$storageProvider?->hasStableConnection()) {
                            $fail('Storage provider connection failed, please check your storage provider connection.');
                        }
                        if (
                            $this->input('type') == BackupType::INCREMENTAL->value
                            && !$storageProvider?->supportsIncrementalBackup()
                        ) {
                            $fail('Storage provider does not support incremental backup');
                        }
                    }
                },
            ],
            'database' => 'required_if:files,1|boolean',
            'files' => 'required_if:database,1|boolean',
            'exclude_paths' => 'nullable|string',
            'auto_backup' => [
                'nullable',
                'boolean',
                function ($attribute, $value, $fail) {
                    if (
                        !$value
                        && $this->boolean('auto_incremental_backup')
                        && $this->input('type') == BackupType::INCREMENTAL->value
                    ) {
                        $fail('Automatic full backup must be enabled to enable incremental backup');
                    }
                },
            ],
            'auto_incremental_backup' => 'nullable|boolean',
            'auto_delete' => 'nullable|boolean',
            'auto_backup_frequency' => 'required_if:auto_backup,1|nullable|in:twelve_hours,daily,weekly,monthly',
            'auto_incremental_frequency' => 'required_if:auto_incremental_backup,1|nullable|in:twelve_hours,daily,weekly,monthly',
            'delete_after_days' => 'required_if:auto_delete,1|nullable|integer|min:1',
            'time' => 'nullable',
            'incremental_time' => 'nullable',
        ];
    }
    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'Please select a backup type',
            'type.in' => 'Please select a valid backup type',
            'is_local.required' => 'Invalid request',
            'storage_provider_id.required_if' => 'Please select a storage provider',
            'storage_provider_id.exists' => 'Please select a valid storage provider',
            'database.required_if' => 'Please select a database',
            'files.required_if' => 'Please select files',
            'auto_backup_frequency.required_if' => 'Please select a frequency',
            'auto_incremental_frequency.required_if' => 'Please select a frequency',
            'auto_incremental_frequency.in' => 'Please select a valid frequency',
            'delete_after_days.required_if' => 'Please enter a valid number of days',
            'delete_after_days.integer' => 'Please enter a valid number of days',
            'delete_after_days.min' => 'Please enter a valid number of days',
        ];
    }
}
