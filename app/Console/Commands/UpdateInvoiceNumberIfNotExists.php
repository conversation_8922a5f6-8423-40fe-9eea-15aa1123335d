<?php

namespace App\Console\Commands;

use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class UpdateInvoiceNumberIfNotExists extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'xcloud:update-invoice-number';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update invoice number if not exists in invoice model and manual invoice model';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('[1/1] Updating invoice number for manual invoices');
        DB::table('invoices')->whereNull('invoice_number')->orWhere('invoice_number', '')->cursor()->each(function ($invoice) {
            $this->info('Updating invoice number for invoice#' . $invoice->id);
            $updated = DB::table('invoices')->where('id', $invoice->id)->update([
                'invoice_number' => 'XC-INV-' . Carbon::parse(($invoice?->created_at ?: now()->toDateString()))->format('Ymd') . '-' . strtoupper(Str::random(12))
            ]);
            if (!$updated) {
                $this->error('Failed to update invoice number for invoice#' . $invoice->id);
            }
        });
        $this->info('Done');

        return Command::SUCCESS;
    }
}
