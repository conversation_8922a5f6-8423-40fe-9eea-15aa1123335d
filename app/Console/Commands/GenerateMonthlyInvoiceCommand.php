<?php

namespace App\Console\Commands;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Jobs\TakePaymentFromInvoiceJob;
use App\Models\Team;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateMonthlyInvoiceCommand extends Command
{
    protected $signature = 'xcloud:generate-monthly-invoice {teamId?} {--force}';

    protected $description = 'Generate General Invoice';

    public function handle(): void
    {
        Log::info('#MonthlyInvoiceGeneration: start at day ' . now()->day);

        $force = $this->option('force');

        if (!$force && now()->day > 6) {
            Log::info('#MonthlyInvoiceGeneration: Invoice generation can only be done from 1 to before 5th of the month. Today: ' . now()->day);
            $this->info('Invoice generation can only be done from 1 to before 5th of the month');
            return;
        }
//
//        if (!$force && cache()->has('monthly:invoice:generation:completed:for') && cache()->get('monthly:invoice:generation:completed:for') === now()->format('F')) {
//            Log::info('#MonthlyInvoiceGeneration: Invoice generation already completed for ' . now()->toDateTimeString());
//            $this->info('Invoice generation already completed for ' . now()->toDateTimeString());
//            return;
//        }

//        cache()->put('monthly:invoice:generation:started:for', now()->format('F'), now()->endOfMonth()->subDays());

        Log::info('#MonthlyInvoiceGeneration: Generating invoice for ' . now()->toDateTimeString() . '-' . __METHOD__ . '-' . __LINE__ . '-' . __FILE__);

        $teams = $this->hasArgument('teamId') && $this->argument('teamId')
            ? Team::where('id', $this->argument('teamId'))->lazyById()
            : Team::lazyById();

        foreach ($teams as $team) {
            Log::info('#MonthlyInvoiceGeneration: Generating invoice for team#' . $team->email);
            $this->info('Generating invoice for team#' . $team->email);

            if (!$team->activePlan) {
                Log::info('#MonthlyInvoiceGeneration: Team: ' . $team->email . ' does not have an active plan');
                continue;
            }

            if (!$team->activePlan?->requires_billing) {
                $this->info('Team: ' . $team->email . ' does not require billing');
                Log::info('#MonthlyInvoiceGeneration: Team: ' . $team->email . ' does not require billing');
                if ($team->activePlan?->support_manual_billing) {
                    $this->generateManualInvoice($team);
                } else {
                    Log::info('#MonthlyInvoiceGeneration: Team: '.$team->id.'#' . $team->email . ' no invoice generated please check manually');
                }
                continue;
            }

            $this->info('Team: ' . $team->email . ' requires billing');
            Log::info('#MonthlyInvoiceGeneration: Team: ' . $team->email . ', Plan: ' . $team->activePlan->name->value . ' requires billing');
            $this->generateGeneralInvoice($team);
        }

//        cache()->put('monthly:invoice:generation:completed:for', now()->format('F'), now()->endOfMonth()->subDays());

        Log::info('#MonthlyInvoiceGeneration: Invoice generation completed for ' . now()->toDateTimeString() . '-' . __METHOD__ . '-' . __LINE__ . '-' . __FILE__);
        $this->info('Invoice generation completed for ' . now()->toDateTimeString());
    }

    private function generateManualInvoice($team): void
    {
        $bills = $team->bills()
            ->monthly()
            ->unpaid()
            ->where('has_offer', false)
            ->whereNull('invoice_id')
            ->get();

        // Divide bills into monthly basis and generate invoice for each month
        $monthlyBills = $bills->groupBy(fn($bill) => $bill->bill_from);

        if ($monthlyBills->isEmpty()) {
            $this->info('No unpaid bills to generate manual invoice for team#' . $team->email);
            Log::info('#MonthlyInvoiceGeneration: No unpaid bills to generate manual invoice for team#' . $team->email);
            return;
        }

        foreach ($monthlyBills as $month => $billsForMonth) {
            $this->info('Dispatching job for generating invoice for team#' . $team->email . '- Month: ' . $month);
            Log::info('#MonthlyInvoiceGeneration: Dispatching job for generating invoice for team#' . $team->email . '- Month: ' . $month);

            try {
                $invoice = InvoiceGenerator::team($team)
                    ->bills($billsForMonth)
                    ->source(InvoiceSourceEnum::MonthlyManualBilling)
                    ->title('Invoice for ' . Carbon::parse($month)->format('F'))
                    ->sendNotification()
                    ->currency(BillingCurrency::USD)
                    ->generate();

                $this->info('Job dispatched for taking ' . $invoice->id . '# invoice payment for team#' . $team->email);
                Log::info('#MonthlyInvoiceGeneration: ' . $invoice->id . '# invoice created for team#' . $team->email);
            } catch (\Exception $e) {
                $this->error('Error dispatching job for generating invoice for team#' . $team->email . ' - ' . $e->getMessage());
                Log::error('#MonthlyInvoiceGeneration: Error dispatching job for generating invoice for team#' . $team->email . ' - ' . $e->getMessage());
            }
        }

//        if ($bills->isNotEmpty()) {
//            Log::info('#MonthlyInvoiceGeneration: Dispatching job for generating manual invoice for team#' . $team->email);
//
//            try {
//                $invoice = InvoiceGenerator::team($team)
//                    ->bills($bills)
//                    ->source(InvoiceSourceEnum::MonthlyManualBilling)
//                    ->title('Invoice for : ' . Carbon::now()->format('F'))
//                    ->description('Purchased services - ' . implode(', ', collect($bills->pluck('service')->toArray())->map(fn($service) => $service->toReadableSentence())->toArray()))
//                    ->sendNotification()
//                    ->currency(BillingCurrency::USD)
//                    ->generate();
//
//                Log::info('#MonthlyInvoiceGeneration: ' . $invoice->id . '# Manual invoice created');
//            } catch (\Exception $e) {
//                Log::error('#MonthlyInvoiceGeneration: Error dispatching job for generating invoice for team#' . $team->email . ' - ' . $e->getMessage());
//            }
//        } else {
//            Log::info('#MonthlyInvoiceGeneration: No unpaid bills to generate manual invoice for team#' . $team->email);
//        }
    }

    private function generateGeneralInvoice($team): void
    {
        $bills = $team->bills()
            ->monthly()
            ->unpaid()
            ->where('has_offer', false)
            ->whereNull('invoice_id')
            ->get();

        // Divide bills into monthly basis and generate invoice for each month
        $monthlyBills = $bills->groupBy(fn($bill) => $bill->bill_from);

        if ($monthlyBills->isEmpty()) {
            $this->info('No unpaid bills to generate invoice for team#' . $team->email);
            Log::info('#MonthlyInvoiceGeneration: No unpaid bills to generate invoice for team#' . $team->email);
            return;
        }

        foreach ($monthlyBills as $month => $billsForMonth) {
            $this->info('Dispatching job for generating invoice for team#' . $team->email . '- Month: ' . $month);
            Log::info('#MonthlyInvoiceGeneration: Dispatching job for generating invoice for team#' . $team->email . '- Month: ' . $month);

            try {
                $invoice = InvoiceGenerator::team($team)
                    ->bills($billsForMonth)
                    ->source(InvoiceSourceEnum::MonthlyGeneralBilling)
                    ->title('Invoice for ' . Carbon::parse($month)->format('F'))
                    ->currency(BillingCurrency::USD)
                    ->generate();

                TakePaymentFromInvoiceJob::dispatch($invoice);
                $this->info('Job dispatched for taking ' . $invoice->id . '# invoice payment for team#' . $team->email);
                Log::info('#MonthlyInvoiceGeneration: Job dispatched for taking ' . $invoice->id . '# invoice payment for team#' . $team->email);
            } catch (\Exception $e) {
                $this->error('Error dispatching job for generating invoice for team#' . $team->email . ' - '. $e->getMessage());
                Log::error('#MonthlyInvoiceGeneration: Error dispatching job for generating invoice for team#' . $team->email . ' - ' . $e->getMessage());
            }
        }

//        if ($bills->isNotEmpty()) {
//            Log::info('#MonthlyInvoiceGeneration: Generating invoice for team#' . $team->email);
//            $this->info('Generating invoice for team#' . $team->email);
//
//            try {
//                $invoice = InvoiceGenerator::team($team)
//                    ->bills($bills)
//                    ->source(InvoiceSourceEnum::MonthlyGeneralBilling)
//                    ->title('Invoice for : ' . Carbon::now()->format('F'))
//                    ->description('Purchased services - ' . implode(', ', collect($bills->pluck('service')->toArray())->map(fn($service) => $service->toReadableSentence())->toArray()))
//                    ->currency(BillingCurrency::USD)
//                    ->generate();
//
//                TakePaymentFromInvoiceJob::dispatch($invoice);
//                Log::info('#MonthlyInvoiceGeneration: Job dispatched for taking ' . $invoice->id . '# invoice payment for team#' . $team->email);
//            } catch (\Exception $e) {
//                Log::error('#MonthlyInvoiceGeneration: Error dispatching job for generating invoice for team#' . $team->email . ' - ' . $e->getMessage());
//            }
//        } else {
//            Log::info('#MonthlyInvoiceGeneration: No unpaid bills to generate invoice for team#' . $team->email);
//        }
    }
}
