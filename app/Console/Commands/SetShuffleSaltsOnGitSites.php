<?php

namespace App\Console\Commands;

use App\Models\Site;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SetShuffleSaltsOnGitSites extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "git:set-salts {site_id}";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Set shuffle salts on git sites";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $site = Site::query()
            ->when($this->argument('site_id'), fn ($query) => $query->where('id', $this->argument('site_id')))
            ->whereHas('server', fn ($query) => $query->where('is_connected', true))
            ->whereNotNull('meta->git_info')
            ->get();
        Log::info('GITSite: Found' . $site->count() . ' sites with git info');
        if ($site->count() > 0) {
            Log::info('GITSite: Found' . $site->count() . ' sites with git info');
            $site->each(function (Site $site) {
                $this->info('Setting shuffle salts for site ' . $site->id);
                Log::info('ShuffleSalts: Setting shuffle salts for site ' . $site->id);
                $site->setShuffleSalts();
            });
        } else {
            Log::info('GITSite: No sites with git info found');
        }
        return Command::SUCCESS;

    }
}
