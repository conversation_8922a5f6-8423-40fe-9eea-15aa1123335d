<?php

namespace App\Console\Commands;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\GeneralInvoice;
use App\Models\Team;
use App\Models\Utm;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Output\ConsoleOutput;

class UtmTeamBills extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'utm:team-bills';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to generate team bills for UTM';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Generating team bills for UTM');
        $utms = Utm::all();
        $summary = [];
        $utms->each(function (Utm $utm) use (&$summary) {
            $this->info('Processing UTM: '.$utm->utm_id);
            $amount = 0;
            $teams = Team::where('meta->utm->utmId', $utm->utm_id)
                ->with(['generalInvoices.bills'])
                ->whereHas('generalInvoices', fn($query) => $query->where(['status' => BillingStatus::Paid]))
                ->get();
            $this->info('Total teams for UTM: "'.$utm->utm_id.'" is: '.count($teams));
            foreach ($teams as $team) {
                $team->generalInvoices->each(function (GeneralInvoice $invoice) use (&$amount) {
                    $amount += $invoice->totalInvoiceAmount();
                });
            }
            $summary[$utm->utm_id] = ['amount' => $amount, 'teams' => count($teams)];
            $this->info('Total amount for UTM: "'.$utm->utm_id.'" is: '.$amount);
        });
        $summary = collect($summary)->sortByDesc('amount');
        // Print summary as a table
        $output = new ConsoleOutput();
        $table = new Table($output);
        $table->setHeaders(['UTM ID', 'Campaign', 'Source', 'Term', 'Content', 'Teams', 'Total Amount']);
        foreach ($summary as $utmId => $amount) {
            $utm = Utm::where('utm_id', $utmId)->first();
            $table->addRow([$utmId, Str::limit($utm?->utm_campaign, 40), $utm?->utm_source, Str::limit($utm?->utm_term, 40), Str::limit($utm?->utm_content, 40), $amount['teams'], $amount['amount']]);
        }
        $table->render();


        return Command::SUCCESS;
    }
}
