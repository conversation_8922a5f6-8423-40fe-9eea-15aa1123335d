<?php

namespace App\Console\Commands;

use App\Enums\XcloudBilling\BillRenewalPeriod;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixLeapYearIssue2025Feb extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix-leap-year-issue-2025-feb {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'One-time fix for the leap year issue';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        $bills = DB::table('bills')
            ->where('bill_from', '2025-03-01')
            ->where('next_billing_date', '2025-03-01')
            ->get();

        $this->info('Total bills found: ' . $bills->count());
        $this->info(implode(', ', $bills->pluck('id')->toArray()));

        if ($dryRun) {
            $this->info('Dry run mode: No changes were made.');
            return Command::SUCCESS;
        }

        $bills->each(function ($bill) {
            DB::table('bills')
                ->where('id', $bill->id)
                ->update([
                    'bill_from' => Carbon::parse('2025-02-01 00:00:00'),
                    'next_billing_date' => Carbon::parse('2025-02-28 23:59:59')
                ]);
        });

        $this->info('Bills updated successfully.');
        return Command::SUCCESS;
    }
}
