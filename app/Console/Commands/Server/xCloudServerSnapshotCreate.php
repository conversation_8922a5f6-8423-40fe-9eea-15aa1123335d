<?php

namespace App\Console\Commands\Server;

use App\Jobs\Server\CreateXCloudServerSnapshot;
use App\Models\Server;
use App\Models\Task;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;

class xCloudServerSnapshot<PERSON>reate extends Command
{
    protected $signature = 'server:xcloud-snapshot-create {server_id}';

    protected $description = 'This will create snapshot of xCloud servers on Vultr';

    public function handle()
    {
        $server = Server::findOrFail($this->argument('server_id'));

        $task = Task::create([
            'name' => 'Creating snapshot of xCloud Server on Vultr',
            'status' => 'pending',
            'server_id' => $server->id,
            'initiated_by' => user()->id ?? $server?->user_id,
            'team_id' => $server->team_id,
        ]);

        CreateXCloudServerSnapshot::dispatch($task, $server);

        $this->info("Creating Snapshot for: {$server->name}");

        return CommandAlias::SUCCESS;
    }
}
