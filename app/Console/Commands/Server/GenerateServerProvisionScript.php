<?php

namespace App\Console\Commands\Server;

use App\Jobs\Server\SetupSudoUsers;
use App\Models\Server;
use App\Scripts\ProvisionWebServer;
use Illuminate\Console\Command;

class GenerateServerProvisionScript extends Command
{
    protected $signature = 'x:generate-server-provision-script {server_id}';

    protected $description = 'Generate server provision script';

    public function handle()
    {
        $server = Server::findOrFail($this->argument('server_id'));

        SetupSudoUsers::dispatchSync($server);

        $path = base_path("devops/xcloud-demo-server/setup-{$server->stack->value}.sh");

        file_put_contents($path, (new ProvisionWebServer($server))->script());

        $this->info('Done ' . $path);

        return 0;
    }
}
