<?php

namespace App\Console\Commands\Site;

use App\Models\Site;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;

class SiteProvision extends Command
{
    protected $signature = 'site:provision {site_id} {--force} {--sync}';

    protected $description = 'Command description';

    public function handle()
    {
        $site = Site::findOrFail($this->argument('site_id'));

        if ($this->option('sync')) {
            config(['queue.default' => 'sync']);
        }

        if (!$this->option('force') && !$site->isNew()) {
            $this->error('Only new sites are allowed to provision.');
            return CommandAlias::FAILURE;
        }

        $site->provision();

        $this->info("provisioning {$site->name}");

        return CommandAlias::SUCCESS;
    }
}
