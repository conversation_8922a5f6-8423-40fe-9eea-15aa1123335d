<?php

namespace App\Console\Commands\Site;

use App\Models\Site;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateSiteAutoIncrementCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:site-increment {value}';

    protected $description = 'Update AUTO_INCREMENT value for Site';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $value = $this->argument('value');
        if (!is_numeric($value)) {
            $this->error('Value must be numeric');
            return Command::INVALID;
        }
        if ($value < 1) {
            $this->error('Value must be greater than 0');
            return Command::INVALID;
        }

        // Get the current AUTO_INCREMENT value
        $incrementValue = Site::max('id') + $value;
        //get table name
        $table = (new Site())->getTable();
        DB::statement("ALTER TABLE $table AUTO_INCREMENT = $incrementValue");
        $this->info("AUTO_INCREMENT value for '$table' has been set to $incrementValue");
        return Command::SUCCESS;
    }
}
