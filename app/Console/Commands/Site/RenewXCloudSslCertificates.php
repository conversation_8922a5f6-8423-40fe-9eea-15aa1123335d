<?php

namespace App\Console\Commands\Site;

use App\Jobs\Site\SslSyncStatusJob;
use App\Jobs\SslRenewJob;
use App\Models\SslCertificate;
use App\Repository\IntervalBuilder;
use App\Repository\IntervalScheduler;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;

class RenewXCloudSslCertificates extends Command
{
    protected $signature = 'site:renew-xcloud-ssl {--site_id=} {--force}';

    protected $description = 'Renew xCloud SSL Certificates';

    public function handle()
    {
        $this->info('Renewing SSL Certificates');

        SslCertificate::query()
            ->when($this->option('site_id'), fn($query) => $query->where('site_id', $this->option('site_id')))
            ->where('provider', SslCertificate::PROVIDER_XCLOUD)
            ->when(!$this->option('force'), fn($query) => $query->where('expires_at', '<', now()->addWeek()))
            ->with('site')
            ->lazyById()
            ->each(function (SslCertificate $certificate) {
                $this->info('Renewing SSL Certificate: '.$certificate->site?->name);

                if (!$certificate->site || !$certificate->site?->server) {
                    $this->line('Site/Server not found');
                    return;
                }

                if ($certificate->site->ssl_provider !== SslCertificate::PROVIDER_XCLOUD) {
                    $this->line('Certificate is not xCloud, skipping renewal');
                    return;
                }

                if ($certificate->site->sslCertificate?->id !== $certificate->id) {
                    $certificate->site->log("Certificate #{$certificate->id} is not the primary certificate of site #{$certificate->site->id}, skipping renewal");
                    return;
                }

                $certificateIntervals = IntervalBuilder::create()
                    ->everyHoursAfter(thresholdHours: 3 * 24, notifyEveryHours: 12)   // First 3 days: Run every 12 hours
                    ->everyDaysAfter(thresholdDays: 7, notifyEveryDays: 1)            // Days 4-7: Run daily
                    ->everyDaysAfter(thresholdDays: 30, notifyEveryDays: 3)           // Days 8-30: Run every 3 days
                    ->everyDaysAfter(thresholdDays: 31, notifyEveryDays: 15)          // After 30 days: Run every 15 days
                    ->build();

                $scheduler = new IntervalScheduler($certificate->renewal_attempt_at, $certificate->expires_at, $certificateIntervals);

                if (!$scheduler->shouldTrigger()){
                    $certificate->site->log('Skipping renewal because last renewal was attempted less than '.$scheduler->getNotifyInterval().' hours ago. Last renewal was attempted '.$scheduler->getHoursSinceLastNotification().' hours ago');
                    return;
                }

                Bus::chain([
                    new SslSyncStatusJob($certificate),
                    new SslRenewJob($certificate),
                ])->dispatch();
            });

        return Command::SUCCESS;
    }
}
