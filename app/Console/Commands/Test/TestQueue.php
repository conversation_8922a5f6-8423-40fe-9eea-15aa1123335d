<?php

namespace App\Console\Commands\Test;

use App\Jobs\Test\Chain1;
use App\Jobs\Test\Chain2;
use App\Jobs\Test\Chain3;
use App\Models\Site;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Throwable;

class TestQueue extends Command
{
    protected $signature = 'test:queue';

    protected $description = 'Command description';

    public function handle()
    {
        logger()->info('TestQueue: Start');

        dispatch(function () {
            logger()->info('TestQueue: Inside Queue');
        });

        $this->info('Dispatched job to queue');

        return Command::SUCCESS;
    }
}
