<?php

namespace App\Console\Commands\Test;

use App\Services\DNS\CloudflareDns;
use Illuminate\Console\Command;

class CloudFlareDnsCommand extends Command
{
    protected $signature = 'dns:update {site_id?}';

    public function handle()
    {
        $cf = new CloudflareDns;

        // $site = Site::find($this->argument('site_id'));

        // faisal-test.x-cloud.app
        // dd($cf->updateOrCreateRecord(name: $site->name, content: $site->server->public_ip));
        dd($cf->delete('faisal-test.x-cloud.app'));

        // dd($cf->dnsRecords('hello.x-cloud.app'));

        return Command::SUCCESS;
    }
}
