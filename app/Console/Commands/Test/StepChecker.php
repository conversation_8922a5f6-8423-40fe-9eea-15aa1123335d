<?php

namespace App\Console\Commands\Test;

use App\Jobs\Site\InstallDatabase;
use App\Models\Site;
use Illuminate\Console\Command;

class <PERSON><PERSON>he<PERSON> extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:step {--site=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $site = $this->option('site');

        InstallDatabase::dispatchSync(Site::find($site)->first());

        return 0;
    }
}
