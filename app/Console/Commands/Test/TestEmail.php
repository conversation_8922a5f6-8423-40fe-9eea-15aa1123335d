<?php

namespace App\Console\Commands\Test;

use App\Models\GeneralInvoice;
use App\Models\Site;
use App\Models\Team;
use App\Models\User;
use App\Notifications\SendInvoiceNotification;
use App\Notifications\SendWelcomeEmail;
use App\Notifications\TestSendingNotification;
use App\Notifications\VerifyUserEmail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    protected $signature = 'test-email';
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Sending email...');

        // config(['mail.default' => 'white-label-smtp']);

        // Mail::to('<EMAIL>')->send(new \App\Mail\TestEmail());
        // Team::find(1028)->notify(new TestSendingNotification());
        Mail::to('<EMAIL>')->send(new \App\Mail\TestEmail());
        // Team::find(1028)->notify(new SendInvoiceNotification(GeneralInvoice::first()));
        // Team::find(1023)->notify(new SendInvoiceNotification(GeneralInvoice::first()));
        // User::find(2)->notify(new VerifyUserEmail);
        // User::find(2)->notify(new SendWelcomeEmail);

        // Team::find(1023)->sendSiteProvisionSuccessMail(Site::first());
        $this->info('Email sent!');
        return Command::SUCCESS;
    }
}
