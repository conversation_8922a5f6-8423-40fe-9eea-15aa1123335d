<?php

namespace App\Console\Commands;

use App\Models\BackblazeReport;
use Aws\S3\S3Client;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Aws\S3\Exception\S3Exception;

class SyncBackblazeReports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backblaze:sync-reports
                            {--date= : The date to sync reports for (YYYY-MM-DD format, defaults to yesterday)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Backblaze B2 usage reports to the database';

    /**
     * The S3 client instance.
     *
     * @var S3Client
     */
    protected S3Client $s3Client;

    /**
     * The batch size for the database inserts.
     *
     * @var int
     */
    protected const BATCH_SIZE = 100;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        try {
            // Initialize S3 client
            $this->s3Client = $this->getS3Client();

            // Get the date to process
            $date = $this->getDateToProcess();
            $dateFormatted = $date->format('Y-m-d');

            $this->info("Syncing Backblaze reports for {$dateFormatted}");

            // Get a list of CSV files
            $fileList = $this->getCsvFilesFromBackblaze($dateFormatted);
            if (empty($fileList)) {
                $this->warn("No report files found for {$dateFormatted}");
                return 0;
            }

            $this->processReportFiles($fileList, $dateFormatted);

            $this->info("Sync completed successfully");
            return 0;
        } catch (\Exception $e) {
            $this->error("Error syncing Backblaze reports: {$e->getMessage()}");
            Log::error("Backblaze report sync failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Get the date to process reports for.
     *
     * @return \Carbon\Carbon
     */
    protected function getDateToProcess(): Carbon
    {
        return $this->option('date')
            ? Carbon::createFromFormat('Y-m-d', $this->option('date'))
            : Carbon::yesterday();
    }

    /**
     * Process all report files.
     *
     * @param array $fileList
     * @param string $dateFormatted
     * @return void
     */
    protected function processReportFiles(array $fileList, string $dateFormatted): void
    {
        $totalProcessed = 0;
        $totalFiles = count($fileList);

        $this->output->progressStart($totalFiles);

        foreach ($fileList as $file) {
            try {
                $csvContent = $this->downloadCsvFromBucket($dateFormatted, $file);
                $data = $this->parseCSVContent($csvContent);

                $processed = $this->processReportData($data, $file);
                $totalProcessed += $processed;

                $this->output->progressAdvance();
            } catch (\Exception $e) {
                Log::error("Error processing file: {$file}", [
                    'error' => $e->getMessage()
                ]);
                $this->warn("Error processing file {$file}: {$e->getMessage()}");
            }
        }

        $this->output->progressFinish();
        $this->info("Processed {$totalProcessed} records from {$totalFiles} files");
    }

    /**
     * Process report data and insert into database.
     *
     * @param array $data
     * @param string $filename
     * @return int Number of records processed
     */
    protected function processReportData(array $data, string $filename): int
    {
        $filteredData = collect($data)
            ->filter(function ($row){
                // Skip rows without bucket_id
                if (empty($row['bucket_id'])) {
                    return false;
                }


                // Otherwise, only process records that don't exist yet
                return BackblazeReport::where([
                    'bucket_id' => $row['bucket_id'],
                    'date' => $row['date']
                ])->doesntExist();
            })
            ->map(function ($row) {
                $timestamp = now()->toDateTimeString();
                $row['created_at'] = $timestamp;
                $row['updated_at'] = $timestamp;
                return $row;
            });

        $recordsProcessed = $filteredData->count();

        if ($recordsProcessed > 0) {
            // Process in batches to avoid memory issues
            $filteredData
                ->chunk(self::BATCH_SIZE)
                ->each(function (Collection $chunk) use ($filename) {
                    $this->info("Inserting {$chunk->count()} records from file: {$filename}");
                    BackblazeReport::insert($chunk->toArray());
                });
        }

        return $recordsProcessed;
    }

    /**
     * Get CSV files from Backblaze for the specified date.
     *
     * @param string $date
     * @return array
     */
    protected function getCsvFilesFromBackblaze(string $date): array
    {
        try {
            $objects = $this->s3Client->listObjectsV2([
                'Bucket' => config('backblaze.reportBucketName'),
                'Prefix' => $date,
            ]);

            if (!isset($objects['Contents'])) {
                return [];
            }

            return collect($objects['Contents'])
                ->map(fn($file) => basename($file['Key']))
                ->filter(fn($file) => preg_match('/^usage\.group-\d+\.[a-z]{2}-[a-z]+-\d{3}\.csv$/', $file))
                ->toArray();
        } catch (S3Exception $e) {
            Log::error("Error listing Backblaze files", [
                'error' => $e->getMessage(),
                'date' => $date
            ]);
            throw $e;
        }
    }

    /**
     * Get the S3 client for Backblaze.
     *
     * @return S3Client
     */
    protected function getS3Client(): S3Client
    {
        return Storage::build([
            'driver' => 's3',
            'key' => config('backblaze.reportKEYID'),
            'secret' => config('backblaze.reportApplicationKey'),
            'region' => config('backblaze.reportRegion'),
            'bucket' => config('backblaze.reportBucketName'),
            'endpoint' => config('backblaze.reportBucketEndpoint'),
            'visibility' => 'private',
        ])->getClient();
    }

    /**
     * Download CSV file from Backblaze bucket.
     *
     * @param string $directory
     * @param string $file
     * @return string
     * @throws \Exception
     */
    protected function downloadCsvFromBucket(string $directory, string $file): string
    {
        $key = $directory . '/' . $file;
        $bucket = config('backblaze.reportBucketName');

        try {
            $result = $this->s3Client->getObject([
                'Bucket' => $bucket,
                'Key' => $key,
            ]);

            return (string) $result['Body'];
        } catch (S3Exception $e) {
            Log::error("Error downloading CSV from Backblaze", [
                'bucket' => $bucket,
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            throw new \Exception("Failed to download file {$file}: {$e->getMessage()}");
        }
    }

    /**
     * Parse CSV content into an array of records.
     *
     * @param string $csvContent
     * @return array
     * @throws \Exception
     */
    protected function parseCSVContent(string $csvContent): array
    {
        if (empty($csvContent) || str_starts_with($csvContent, 'Error:')) {
            throw new \Exception("Invalid CSV content: {$csvContent}");
        }

        $rows = [];
        $lines = explode("\n", $csvContent);
        $headers = null;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            $data = str_getcsv($line);

            if (!$headers) {
                $headers = $data;
                continue;
            }

            // Skip if data count doesn't match headers count
            if (count($data) !== count($headers)) {
                continue;
            }

            $rows[] = array_combine($headers, $data);
        }

        return $rows;
    }
}
