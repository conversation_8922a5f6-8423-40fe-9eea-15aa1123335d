<?php

namespace App\Console\Commands;

use App\Models\PatchstackVulnerability;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class GetPatchstackVulnerabilitiesCommand extends Command
{
    protected $signature = 'patchstack:vulnerabilities';

    protected $description = 'Get Patchstack Vulnerabilities';

    public function handle()
    {
        $this->info('Getting Patchstack Vulnerabilities...');

        try {
            // Get all unique site IDs from the model
            $siteIds = PatchstackVulnerability::select('patchstack_site_id')
                ->whereNotNull('patchstack_site_id')
                ->where('patchstack_site_id', '!=', '')
                ->distinct()
                ->pluck('patchstack_site_id');

            if ($siteIds->isEmpty()) {
                $this->error('No site IDs found in the database');
                return Command::FAILURE;
            }

            $this->info('Found ' . $siteIds->count() . ' sites to process');

            foreach ($siteIds as $siteId) {
                $this->info("Processing site ID: {$siteId}");

                $url = "https://api.patchstack.com/monitor/dashboard/vulnerabilities/{$siteId}";

                $response = Http::withHeaders([
                    'accept' => 'application/json',
                    'UserToken' => config('services.patchstack.api_key'),
                    'X-CSRF-TOKEN' => '',
                ])->post($url);

                if (!$response->successful()) {
                    $this->error("Error fetching vulnerabilities for site {$siteId}: " . $response->status());
                    continue;
                }

                $vulnerabilities = json_decode($response->body(), true);

                // Update the vulnerabilities for this site
                PatchstackVulnerability::where('patchstack_site_id', $siteId)
                    ->update([
                        'vulnerabilities' => $vulnerabilities,
                        'updated_at' => now()
                    ]);

                $this->info("Updated vulnerabilities for site {$siteId}");
            }

            $this->info('Patchstack Vulnerabilities Updated Successfully');
            return Command::SUCCESS;
        } catch (Exception $e) {
            $this->error('Error processing vulnerabilities: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
