<?php

namespace App\Console\Commands\Language;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use GuzzleHttp\Client;
use Exception;

class TranslateNotificationCommand extends Command
{
    protected $signature = 'translate:notification {locale}';
    protected $description = 'Translate language files from English to another language for Notifications';

    protected $languages = [
        'bn' => 'bn', // Bangla
        'de' => 'de', // German
        'es' => 'es', // Spanish
        'fr' => 'fr', // French
        'hi' => 'hi', // Hindi
        'it' => 'it', // Italian
        'ja' => 'ja', // Japanese
        'nl' => 'nl', // Dutch
        'no' => 'no', // Norwegian
        'pt' => 'pt', // Portuguese
        'ru' => 'ru', // Russian
        'sc' => 'zh', // Simplified Chinese
        'tc' => 'zh-TW', // Traditional Chinese
        'id' => 'id', // Indonesian
        'th' => 'th', // Thai
    ];

    public function handle()
    {
        $locale = $this->argument('locale');

        if (!array_key_exists($locale, $this->languages)) {
            $this->error("Invalid locale: {$locale}. Please use one of the predefined locales.");
            return;
        }

        $apiKey = config('services.chatgpt.api_key');
        if (!$apiKey) {
            $this->error("API key for ChatGPT is not set. Please define it in your .env file as CHATGPT_API_KEY.");
            return;
        }

        $files = ['emails.php', 'slack.php', 'telegram.php', 'whatsapp.php'];
        $sourcePath = resource_path('lang/en');

        foreach ($files as $file) {
            $sourceFilePath = $sourcePath . '/' . $file;

            if (!File::exists($sourceFilePath)) {
                $this->error("Source file {$file} not found in lang/en directory.");
                continue;
            }

            $content = include $sourceFilePath;
            if (!is_array($content)) {
                $this->error("Invalid PHP array in {$file}. Skipping.");
                continue;
            }

            $targetFilePath = resource_path("lang/{$locale}/{$file}");
            $existingContent = File::exists($targetFilePath) ? include $targetFilePath : [];

            if (!is_array($existingContent)) {
                $this->warn("Target file {$file} contains invalid PHP content. Overwriting with new translations.");
                $existingContent = [];
            }

            $mergedContent = $this->mergeTranslations($this->translateContent($content, $locale, $apiKey), $existingContent, $locale, $apiKey);

            $exportedContent = var_export($mergedContent, true);
            $formattedContent = str_replace(['array (', ')'], ['[', ']'], $exportedContent);
            File::ensureDirectoryExists(dirname($targetFilePath));
            File::put($targetFilePath, "<?php\n\nreturn " . $formattedContent . ";\n");

            $this->info("Translated file saved to {$targetFilePath}");
        }
    }

    private function mergeTranslations(array $content, array $existingContent, string $locale, string $apiKey): array
    {
        foreach ($content as $key => $value) {
            if (is_array($value)) {
                $existingContent[$key] = $this->mergeTranslations(
                    $value,
                    $existingContent[$key] ?? [],
                    $locale,
                    $apiKey
                );
            } else {
                if (!array_key_exists($key, $existingContent)) {
                    $existingContent[$key] = $this->translateText($value, $locale, $apiKey);
                }
            }
        }

        return $existingContent;
    }


    private function translateContent(array $content, string $locale, string $apiKey): array
    {
        $translatedContent = [];

        foreach ($content as $key => $value) {
            if (is_array($value)) {
                $translatedContent[$key] = $this->translateContent($value, $locale, $apiKey);
            } else {
                $translatedContent[$key] = $this->translateText($value, $locale, $apiKey);
            }
        }

        return $translatedContent;
    }

    private function translateText(string $text, string $locale, string $apiKey): string
    {
        $targetLanguage = $this->languages[$locale];

        try {
            $client = new Client();
            $response = $client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => "Bearer {$apiKey}",
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4o',
                    'messages' => [
                        ['role' => 'system', 'content' => 'You are a professional translator with expertise in adapting text for software and app interfaces. Ensure translations are accurate, concise, and suitable for user-facing interfaces.'],
                        [
                            'role' => 'user',
                            'content' => "Translate the following text into {$targetLanguage}, ensuring it remains suitable for use in a software or app interface: {$text}",
                        ],
                    ],
                    'temperature' => 0.2,
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            if (isset($data['choices'][0]['message']['content'])) {
                return trim($data['choices'][0]['message']['content']);
            }

            $this->error("Translation failed for text: {$text}");
            return $text;
        } catch (Exception $e) {
            $this->error("Error translating text: {$text}. Exception: {$e->getMessage()}");
            return $text;
        }
    }
}
