<?php

namespace App\Console\Commands\Security;

use App\Enums\SiteStatus;
use App\Enums\Stack;
use App\Jobs\ApplyLiteSpeedSecurityUpdateJob;
use App\Jobs\ApplySecurityUpdateJob;
use App\Models\Server;
use Illuminate\Console\Command;

class ApplyLieSpeedCacheSecurityPatchCommand extends Command
{
    protected $signature = 'x:apply-litespeed-cache-security-patch {--server=}';

    protected $description = 'Apply security patch to servers';

    public function handle()
    {
        $this->line('Applying litespeed security patch to servers');

        Server::query()
            ->where('is_connected', 1)
            ->where('stack', Stack::OpenLiteSpeed)
            ->when($this->option('server'), function ($query) {
                return $query->where('id', $this->option('server'));
            })
            ->lazyById()
            ->each(function (Server $server) {
                $this->line('Applying litespeed security patch to server: '.$server->id.' : '.$server->name);

                $i = 0;
                $sitesCount = $server->sites()->where('status', SiteStatus::PROVISIONED)->count();

                $server->sites()->where('status', SiteStatus::PROVISIONED)->each(function ($site) use (&$i, $sitesCount) {
                    $this->line('Applying litespeed security patch to site: '.$site->id.' : '.$site->name);
                    // apply security patch to server
                    // split the day into max 10 min based on site count
                    $delaySeconds = (int) $i * (600 / $sitesCount);

                    if ($delaySeconds > 600) {
                        $delaySeconds = rand(0, 600);
                    }

                    ApplyLiteSpeedSecurityUpdateJob::dispatch($site)->delay(now()->addSeconds($delaySeconds));

                    $i++;
                });

                $server->saveMeta('security_patch_applied_at', now()->toDateTimeString());
            });
    }
}
