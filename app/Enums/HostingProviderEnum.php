<?php

namespace App\Enums;

enum HostingProviderEnum: string
{
    // Define enum cases along with their associated string values
    case OTHERS = 'Others';
    case XCLOUD = 'xCloud';
    case SPINUP_WP = 'SpinupWP';
    case FORGE = 'Forge';
    case RUNCLOUD = 'RunCloud';
    case PLOI = 'Ploi';
    case CPANEL = 'cPanel';

// Custom method to get all enum values as an array
    public static function getValues(): array
    {
        return [
            ['name' => self::OTHERS->value, 'img' => 'img/others-hosting.png'],
            ['name' => self::XCLOUD->value, 'img' => 'img/xCloud-blue.png'],
            ['name' => self::SPINUP_WP->value, 'img' => 'img/spinup-wp.png'],
            ['name' => self::FORGE->value, 'img' => 'img/forge.png'],
            ['name' => self::RUNCLOUD->value, 'img' => 'img/run-cloud-dark.png'],
            ['name' => self::PLOI->value, 'img' => 'img/ploi.png'],
            ['name' => self::CPANEL->value, 'img' => 'img/c-panel.png']
        ];
    }
}

