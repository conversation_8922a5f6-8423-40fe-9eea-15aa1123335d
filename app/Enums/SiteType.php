<?php

namespace App\Enums;

use App\Traits\EnumHelper;

enum SiteType: string
{
    use EnumHelper;
    case WORDPRESS = 'wordpress';
    case LARAVEL = 'laravel';
    case CUSTOM_PHP = 'custom-php';

    public function getDisplayName(): string
    {
        return match ($this->value) {
            self::WORDPRESS => 'WordPress',
            self::CUSTOM_PHP => 'Custom PHP',
            default => 'Unknown',
        };
    }


    public static function getTypesWithIcon(): array
    {
        return [
            [
                'name' => 'WordPress',
                'icon' => 'xcloud xc-wordpress',
                'value' => self::WORDPRESS,
                'disabled' => false
            ],
            [
                'name' => 'Custom PHP',
                'icon' => 'xcloud xc-php',
                'value' => self::CUSTOM_PHP,
                'disabled' => false
            ],
            [
                'name' => 'Laravel',
                'icon' => 'xcloud xc-laravel',
                'value' => self::LARAVEL,
                'disabled' => false
            ],
        ];
    }

    public function isWordPress(): bool
    {
        return $this === self::WORDPRESS;
    }
    public function isCustomPhp(): bool
    {
        return $this === self::CUSTOM_PHP;
    }
}
