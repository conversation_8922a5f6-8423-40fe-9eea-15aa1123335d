<?php

namespace App\Enums;

use App\Jobs\Site\InstallMautic;
use App\Jobs\Site\InstallN8n;
use App\Jobs\Site\InstallPhpMyAdmin;
use App\Jobs\Site\InstallUptimeKuma;
use App\Models\Server;
use App\Traits\EnumHelper;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

enum SiteType: string
{
    use EnumHelper;

    case WORDPRESS = 'wordpress';
    case LARAVEL = 'laravel';
    case CUSTOM_PHP = 'custom-php';
    case ONECLICK = 'oneclick';
    case PHPMYADMIN = 'phpmyadmin';
    case N8N = 'n8n';
    case UPTIME_KUMA = 'uptime-kuma';
    case MAUTIC = 'mautic';

    public function getDisplayName(): string
    {
        return match ($this) {
            self::WORDPRESS => 'WordPress',
            self::CUSTOM_PHP => 'Custom PHP',
            self::ONECLICK => 'One Click App',
            self::PHPMYADMIN => 'PHPMyAdmin',
            self::N8N => 'n8n',
            self::UPTIME_KUMA => 'Uptime Kuma',
            self::MAUTIC => 'Mautic',
            default => 'Unknown',
        };
    }


    public static function getTypesWithIcon(): array
    {
        return [
            [
                'name' => 'WordPress',
                'icon' => 'xcloud xc-wordpress',
                'value' => self::WORDPRESS,
                'disabled' => false,
                'isNew' => false,
            ],
            [
                'name' => 'Custom PHP',
                'icon' => 'xcloud xc-php',
                'value' => self::CUSTOM_PHP,
                'disabled' => false,
                'isNew' => false,
            ],
            [
                'name' => 'Laravel',
                'icon' => 'xcloud xc-laravel',
                'value' => self::LARAVEL,
                'disabled' => false,
                'isNew' => true,
            ],
            [
                'name' => 'One Click Apps',
                'icon' => 'xcloud xc-one-click-app',
                'value' => self::ONECLICK,
                'disabled' => false,
                'isNew' => true,
            ],
        ];
    }

    public function minPhpVersion(): ?string
    {
        return collect(self::getOneClickApps())->where('slug', $this->value)->value('min_php_version');
    }

    public function isWordPress(): bool
    {
        return $this === self::WORDPRESS;
    }

    public function isCustomPhp(): bool
    {
        return $this === self::CUSTOM_PHP;
    }

    public function isPhpMyAdmin(): bool
    {
        return $this === self::PHPMYADMIN;
    }

    public function isN8n(): bool
    {
        return $this === self::N8N;
    }

    public function isUptimeKuma(): bool
    {
        return $this === self::UPTIME_KUMA;
    }

    public function isMautic(): bool
    {
        return $this === self::MAUTIC;
    }

    public static function getOneClickApps(Server $server = null): array
    {
        return [
            [
                'id' => 1,
                'name' => 'PHPMyAdmin',
                'slug' => 'phpmyadmin',
                'description' => __('phpmyadmin_description'),
                'icon' => 'img/phpmyadmin.svg',
                'is_active' => true,
                'installation_job' => InstallPhpMyAdmin::class,
                'is_node_app' => false,
                'needs_database' => false,
                'already_installed' => $server && $server?->hasPhpMyAdminSite(),
                'min_php_version' => '7.2',
            ],
            [
                'id' => 3,
                'name' => 'Uptime Kuma',
                'slug' => 'uptime-kuma',
                'description' => __('uptime_kuma_description'),
                'icon' => 'img/uptime-kuma.svg',
                'is_active' => true,
                'installation_job' => InstallUptimeKuma::class,
                'is_node_app' => true,
                'needs_database' => true,
            ],
            [
                'id' => 2,
                'name' => 'n8n',
                'slug' => 'n8n',
                'description' => __('n8n_description'),
                'icon' => 'img/n8n-color.svg',
                'is_active' => false,
                'installation_job' => InstallN8n::class,
                'is_node_app' => true,
                'needs_database' => false,
                'coming_soon' => true,
            ],
            [
                'id' => 4,
                'name' => 'Mautic',
                'slug' => 'mautic',
                'description' => __('mautic_description'),
                'icon' => 'img/mautic.svg',
                'is_active' => false,
                'installation_job' => InstallMautic::class,
                'is_node_app' => false,
                'needs_database' => true,
                'coming_soon' => false,
            ],
        ];
    }

    public static function getAppDetailsBySlug(string $slug): ?array
    {
        return collect(self::getOneClickApps())->firstWhere('slug', $slug);
    }

    static function asOneClickRule(): In
    {
        return Rule::in(collect(SiteType::getOneClickApps())->where('is_active', true)->pluck('slug')->toArray());
    }
}
