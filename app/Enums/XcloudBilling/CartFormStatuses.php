<?php

namespace App\Enums\XcloudBilling;

use App\Traits\EnumHelper;

enum CartFormStatuses : string
{
    use EnumHelper;
    case Filling = 'filling';
    case Pending = 'pending';
    case Processing = 'processing';
    case PaymentProcessing = 'payment_processing';
    case PaymentFailed = 'payment_failed';
    case Paid = 'paid';
    case Completed = 'completed';

    public static function ProcessingStatuses(): array
    {
        return [
            self::Pending,
            self::Processing,
        ];
    }

    public static function UnmodifiableStatuses(): array
    {
        return [
            self::Processing,
            self::PaymentProcessing,
            self::Paid,
            self::Completed,
        ];
    }

    public static function CompletedStatuses(): array
    {
        return [
            self::Completed,
            self::Paid,
        ];
    }
}
