<?php

namespace App\Enums\XcloudBilling;

use App\Notifications\SendRefundInvoiceNotification;
use App\Traits\EnumHelper;
use Illuminate\Support\Facades\File;
use Stripe\PaymentIntent;

enum BillingStatus : string
{
    use EnumHelper;

    case Pending = 'pending';
    case Paid = 'paid';
    case Unpaid = 'unpaid';
    case PartiallyPaid = 'partially_paid';
    case Failed = 'failed';
    case Cancelled = 'cancelled';
    case Refundable = 'refundable';
    case RefundPermissionPending = 'refund_permission_pending';
    case RefundPermitted = 'refund_permitted';
    case Refunded = 'refunded';
    case PartiallyRefunded = 'partially_refunded';
    case PaymentFailed = 'payment_failed';
    case Initiated = 'initiated';

    case Free = 'free';

    case Offer = 'offer';

    case RequireAction = 'require_action';

    public function asStripePI() : string
    {
        return match ($this) {
            self::Paid => PaymentIntent::STATUS_SUCCEEDED,
            self::Unpaid => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            self::PartiallyPaid => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            self::Failed => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            self::Cancelled => PaymentIntent::STATUS_CANCELED,
            self::Refundable => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            self::RefundPermissionPending => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            self::RefundPermitted => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            self::Refunded => PaymentIntent::STATUS_SUCCEEDED,
            self::PartiallyRefunded => PaymentIntent::STATUS_SUCCEEDED,
            self::PaymentFailed => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            self::Initiated => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
            default => PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD,
        };
    }

    public function canBeRefunded(): bool
    {
        return match ($this) {
            self::Paid, self::Refundable, self::RefundPermissionPending, self::RefundPermitted => true,
            default => false,
        };
    }

    public function isSealAble(): bool
    {
        return match ($this) {
            self::Paid, self::Unpaid, self::Cancelled, self::Refunded, self::Failed, self::PaymentFailed => true,
            default => false,
        };
    }
    public function getSealImagePath(): ?string
    {
        if (!$this->isSealAble()) {
            return null;
        }
        $path = match ($this) {
            self::Paid => resource_path('images/paid.png'),
            self::Unpaid => resource_path('images/unpaid.png'),
            self::Cancelled => resource_path('images/cancelled.png'),
            self::Refunded => resource_path('images/refunded.png'),
            self::Failed, self::PaymentFailed => resource_path('images/failed.png'),
            default => null,
        };
        return File::exists($path) ? $path : null;
    }

    public function canBeDeleted(): bool
    {
        return match ($this) {
            self::Paid, self::Refunded, self::Refundable, self::RefundPermissionPending, self::RefundPermitted => false,
            default => true,
        };
    }

    public function isSupportedByBill(): bool
    {
        return match ($this) {
            self::Paid, self::Unpaid, self::Cancelled, self::Refundable, self::Refunded => true,
            default => false,
        };
    }

    public function isUnsuccessful(): bool
    {
        return match ($this) {
            self::Paid, self::Cancelled, self::Refundable, self::RefundPermissionPending, self::RefundPermitted, self::Refunded => false,
            default => true,
        };
    }

    public function isPaid() : bool
    {
        return match ($this) {
            self::Paid => true,
            default => false,
        };
    }

    public function isFailed() : bool
    {
        return match ($this) {
            self::Failed, self::PaymentFailed => true,
            default => false,
        };
    }

    public function isCancelled() : bool
    {
        return match ($this) {
            self::Cancelled => true,
            default => false,
        };
    }

    public function getNotificationClass() : string
    {
        return match ($this) {
            self::Refunded => SendRefundInvoiceNotification::class,
            default => '',
        };
    }

    /**
     * Detailed comment for status
     * @param self $status
     *
     * @return string
     */
    public function getCommentByStatus(): string
    {
        return match ($this) {
            self::Paid => 'payment is successful.',
            self::Unpaid => 'payment is not successful.',
            self::PartiallyPaid => 'payment is partially successful.',
            self::Failed, self::PaymentFailed => 'payment is failed please try again or cancel the invoice.',
            self::Cancelled => 'payment is cancelled.',
            self::Refundable => 'payment is refundable.',
            self::RefundPermissionPending => 'payment is refund permission pending.',
            self::RefundPermitted => 'payment is refund permitted.',
            self::Refunded => 'payment is refunded.',
            self::Initiated => 'payment is initiated.',
            self::Free => 'payment is free.',
            self::Offer => 'payment is offer.',
            default => 'payment is pending.',
        };
    }

    public function asTitle(): string
    {
        return match ($this) {
            self::Paid => 'Paid',
            self::Unpaid => 'Unpaid',
            self::PartiallyPaid => 'Partially Paid',
            self::Failed, self::PaymentFailed => 'Failed',
            self::Cancelled => 'Cancelled',
            self::Refundable => 'Refundable',
            self::RefundPermissionPending => 'Refund Requested',
            self::RefundPermitted => 'Refund Permitted',
            self::Refunded => 'Refunded',
            self::Initiated => 'Initiated',
            self::Free => 'Free',
            self::Offer => 'Offer',
            default => 'Pending',
        };
    }

    public function noIssueWithInvoice(): bool
    {
        return match ($this) {
            self::Paid, self::Cancelled, self::Refundable, self::RefundPermissionPending, self::RefundPermitted, self::Refunded, self::Initiated => true,
            default => false,
        };
    }

    public static function noIssueWithInvoices(): array
    {
        return [
            self::Paid,
            self::Cancelled,
            self::Refundable,
            self::RefundPermissionPending,
            self::RefundPermitted,
            self::Refunded,
        ];
    }

    public function getTeamBillingAlert(): TeamBillingAlertType
    {
        return match ($this) {
            self::Paid => TeamBillingAlertType::Success,
            self::Failed, self::Cancelled, self::PaymentFailed => TeamBillingAlertType::Danger,
            self::Unpaid, self::PartiallyPaid => TeamBillingAlertType::Warning,
            default => TeamBillingAlertType::Info
        };
    }

    public function getInvoiceAmountTitle() : string
    {
        return match ($this) {
            self::Paid => 'Paid Amount',
            self::Unpaid, self::Failed, self::PaymentFailed, self::Cancelled => 'Payable Amount',
            self::PartiallyPaid => 'Partially Paid Amount',
            self::Refundable, self::RefundPermissionPending, self::RefundPermitted => 'Refundable Amount',
            self::Refunded => 'Refunded Amount',
            default => 'To Pay',
        };
    }

    public function isInRefundState() : bool
    {
        return match ($this) {
            self::Refundable, self::RefundPermissionPending, self::RefundPermitted, self::Refunded => true,
            default => false,
        };
    }
}
