<?php

namespace App\Enums\XcloudBilling;

use App\Traits\EnumHelper;

enum ProductSources : string
{
    use EnumHelper;

    case Unknown = 'unknown';
    case Xcloud = 'xcloud';
    case Templately = 'templately';
    case EasyJobs = 'easy_jobs';
    case EssentialAddons = 'essential_addons';
    case EssentialBlocks = 'essential_blocks';
    case NotificationX = 'notification_x';
    case WPDeveloper = 'wpdeveloper';
    case AppSumo = 'app_sumo';
}
