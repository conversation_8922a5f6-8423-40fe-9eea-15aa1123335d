<?php

namespace App\Enums\XcloudBilling;

use App\Services\XcloudProduct\CalculateBill;
use App\Traits\EnumHelper;
use Illuminate\Support\Carbon;

enum BillRenewalPeriod : string
{
    use EnumHelper;

    case Monthly = 'monthly';
    case Yearly = 'yearly';
    case Lifetime = 'lifetime';

    public static function getNextRenewalDate(BillRenewalPeriod $period, $date): string
    {
        $date = Carbon::parse($date);

        return match ($period) {
            self::Monthly => $date->addMonth()->toDateString(),
            self::Yearly => $date->addYear()->toDateString(),
            default => '',
        };
    }

    public static function getNextBillFrom(BillRenewalPeriod $period, $date): string
    {
        $date = Carbon::parse($date);

        return match ($period) {
            self::Monthly => $date->addMonth()->toDateString(),
            self::Yearly => $date->addYear()->toDateString(),
            default => '',
        };
    }

    public static function getNextBillTo(BillRenewalPeriod $period, $date): string
    {
        $startOfTheNextMonth = Carbon::parse($date)->startOfMonth()->addMonth(); // Move to next month start

        // Ensure billing date is the CalculateBill::billingDate of the next month
        $billDate = $startOfTheNextMonth->copy()->day(CalculateBill::billingDate)->endOfDay();

        return match ($period) {
            self::Monthly => $billDate->toDateTimeString(), // Ensures "YYYY-MM-28 23:59:59"
            self::Yearly => $startOfTheNextMonth->addYear()->toDateString(),
            default => '',
        };
    }

    public function twoDigitShortForm() : string
    {
        return match ($this) {
            self::Monthly => 'mo',
            self::Yearly => 'yr',
            self::Lifetime => 'lt',
            default => '',
        };
    }

    public function isRenewable() : bool
    {
        return match ($this) {
            self::Monthly, self::Yearly => true,
            default => false,
        };
    }
}
