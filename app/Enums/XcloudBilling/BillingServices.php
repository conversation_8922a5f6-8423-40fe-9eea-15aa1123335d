<?php

namespace App\Enums\XcloudBilling;

use App\Models\BillingPlan;
use App\Models\EmailProvider;
use App\Models\PatchstackVulnerability;
use App\Models\Server;
use App\Models\Site;
use App\Models\Team;
use App\Traits\EnumHelper;

enum BillingServices : string
{
    use EnumHelper;

    case xCloudManagedHosting = 'xcloud_managed_hosting';
    case xCloudProviderHosting = 'xcloud_provider_hosting';
    case BackupXCloudManagedHosting = 'backup_xcloud_managed_hosting';
    case BackupXCloudProviderHosting = 'backup_xcloud_provider_hosting';
    case SelfManagedHosting = 'self_managed_hosting';
    case Site = 'site';
    case EmailProvider = 'email_provider';
    case WhiteLabelSubscription = 'white_label_subscription';
    case ManagedHosting = 'managed_hosting';
    case BackupManagedHosting = 'backup_managed_hosting';
    case PatchstackAddon = 'patchstack_addon';

    public function isUnderServerBackup() : bool
    {
        return match ($this) {
            self::BackupXCloudManagedHosting, self::BackupXCloudProviderHosting, self::BackupManagedHosting => true,
            default => false,
        };
    }

    public function getServiceModel() : string
    {
        return match ($this) {
            self::xCloudManagedHosting,
            self::xCloudProviderHosting,
            self::BackupXCloudManagedHosting,
            self::BackupXCloudProviderHosting,
            self::SelfManagedHosting,
            self::ManagedHosting,
            self::BackupManagedHosting => Server::class,
            self::Site => Site::class,
            self::EmailProvider => EmailProvider::class,
            self::PatchstackAddon => PatchstackVulnerability::class,
            default => '',
        };
    }

    public function toReadableSentence() : string
    {
        return match ($this) {
            self::xCloudManagedHosting => 'xCloud (Managed)',
            self::xCloudProviderHosting => 'xCloud (Provider)',
            self::ManagedHosting => 'Managed Hosting',
            self::SelfManagedHosting => 'Self Managed Hosting',
            self::BackupXCloudManagedHosting => 'Backup for xCloud (Managed)',
            self::BackupXCloudProviderHosting => 'Backup for xCloud (Provider)',
            self::EmailProvider => 'Email Provider',
            self::PatchstackAddon => 'Patchstack Addon',
            default => self::toReadableString(),
        };
    }

    public function toShortTitle() : string
    {
        return match ($this) {
            self::xCloudManagedHosting => 'xCloud (Managed)',
            self::xCloudProviderHosting => 'xCloud (Provider)',
            self::ManagedHosting => 'Managed',
            self::SelfManagedHosting => 'Self Managed',
            self::BackupXCloudManagedHosting, self::BackupXCloudProviderHosting, self::BackupManagedHosting => 'Backup',
            self::EmailProvider => 'Email Provider',
            default => self::toReadableString(),
        };
    }

    public function toShortIdentifier() : string
    {
        return match ($this) {
            self::xCloudManagedHosting => 'xCloud (Managed)',
            self::xCloudProviderHosting => 'xCloud (Provider)',
            self::ManagedHosting => 'Managed (White Label)',
            self::SelfManagedHosting => '(xCloud Self) Managed',
            self::BackupXCloudManagedHosting => 'Backup (xCloud Managed)',
            self::BackupXCloudProviderHosting => 'Backup (xClod Provider)',
            self::BackupManagedHosting => 'Backup (White Label)',
            self::EmailProvider => 'Email (xCloud)',
            default => self::toReadableString(),
        };
    }

    public function canAvailOffer(Team $team): bool
    {
        $activePlan = $team->activePlan;

        if (!$activePlan) {
            return false;
        }

        $availableOffers = BillingPlan::where('name', $activePlan->name)->first()?->service_offers[$this->value] ?? false;

        if (!$availableOffers) {
            return false;
        }

        $availableOffers = intval($availableOffers);
        $availedOffers = $team->availed_offers;

        if (!$availedOffers) {
            return true;
        }

        $availedOffers = intval($availedOffers[$this->value] ?? 0);

        return $availedOffers < $availableOffers;
    }

    public static function providedByxCloudServices() : array
    {
        return [
            self::xCloudManagedHosting,
            self::xCloudProviderHosting,
            self::BackupXCloudManagedHosting,
            self::BackupXCloudProviderHosting,
        ];
    }

    public static function providedByWhiteLabelHostingServices() : array
    {
        return [
            self::ManagedHosting,
            self::BackupManagedHosting,
        ];
    }
}
