<?php

namespace App\Enums;

enum DatabaseClusterStatus: string
{
    case NEW = 'new';
    case CREATING = 'creating'; //creating database cluster in chosen cloud
    case CREATED = 'created'; //database cluster is created, stored IP, ready to run scripts
    case ERROR = 'error'; // error during creating db
    case MODIFYING = 'modifying';
    case MODIFIED = 'modified';
    case DELETING = 'deleting';
    case DELETED = 'deleted';
    case PROVISIONING = 'provisioning';
    case PROVISIONING_FAILED = 'provisioning_failed';
    case PROVISIONED = 'provisioned';
}
