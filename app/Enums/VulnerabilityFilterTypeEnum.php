<?php

namespace App\Enums;

use App\Traits\EnumHelper;

enum VulnerabilityFilterTypeEnum: string
{
    use EnumHelper;

    case ALL = 'all';
    case FREE = 'free';
    case PRO = 'pro';

    public function getLabel(): string
    {
        return match ($this) {
            self::ALL => 'All',
            self::FREE => 'Free',
            self::PRO => 'Pro',
        };
    }

    public static function fromString(string $value): self
    {
        return match ($value) {
            'free' => self::FREE,
            'pro' => self::PRO,
            default => self::ALL,
        };
    }
}
