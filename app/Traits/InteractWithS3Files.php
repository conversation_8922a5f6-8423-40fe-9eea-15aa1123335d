<?php

namespace App\Traits;

use App\Models\StorageProvider;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use League\Flysystem\FilesystemException;

trait InteractWithS3Files
{

    public function paginateFiles($location, $perPage = 10, $continuationToken = null): array
    {

        // List objects in the S3 bucket (with pagination)
        $result = $this->listObjectsV2(location: $location, perPage: $perPage, continuationToken: $continuationToken);

        // Get the file data
        $s3Files = $result->get('Contents');

        $files = $this->filterFiles(s3Files: $s3Files);
        // Prepare next continuation token for pagination
        $nextContinuationToken = $result->get('NextContinuationToken');
        #previouse token
        $prevContinuationToken = $result->get('ContinuationToken');
        return [
            'files' => $files,
            'nextContinuationToken' => $nextContinuationToken,
            'prevContinuationToken' => $prevContinuationToken,
            'isLastPage' => !isset($nextContinuationToken) ,
        ];
    }

    public function s3Client(): \Aws\S3\S3Client
    {
        return StorageProvider::storage($this->getConfig())->getClient();
    }

    public function listObjectsV2($location,$perPage=null,$continuationToken=null)
    {
        // Get the S3 client instance
        $s3Client = $this->s3Client();
        $params = [
           'Bucket' => $this->getBucket(),
           'Prefix' => $location
        ];
        if ($perPage) {
            $params['MaxKeys'] = $perPage;
        }
        if ($continuationToken) {
            $params['ContinuationToken'] = $continuationToken;
        }

        return  $s3Client
            ->listObjectsV2($params);
    }
    public function filterFiles(array $s3Files): array
    {
        $pattern = '/(^duplicity-(full|inc)\.\d{8}T\d{6}Z(\.to\.\d{8}T\d{6}Z)?\.manifest$)|(\.tar\.gz$)|(\.sql$)/';
        $hasIncremental = collect($s3Files)->search(fn ($file) => Str::endsWith((basename($file['Key'])),'.manifest')) > 0;
        return collect($s3Files)
            ->filter(fn ($file) => preg_match($pattern, basename($file['Key'])))
            ->map(function ($file) use ($hasIncremental) {
                if ($hasIncremental) {
                   $datetime = $file['LastModified'];
                }else{
                    preg_match('/(\d{14})/', basename($file['Key']), $matches);
                    $datetime = $matches[1] ?? null;
                }
                return [
                    'Key' => $file['Key'],
                    'Size' => format_bytes($file['Size']),
                    'LastModified' => $file['LastModified'],
                    'DateTime' =>  Carbon::parse($datetime)->format('Y-m-d h:i A'),
                    'is_sql' => Str::endsWith($file['Key'], '.sql')
                ];
            })
            ->groupBy('DateTime')
            ->filter(fn ($files) => $files->count() === 2)
            ->toArray();
    }

    public function getManifestFile($directory, $file)
    {
        $files = $this
            ->listObjectsV2(location: $directory.DIRECTORY_SEPARATOR.'duplicity-full.')
            ->get('Contents');

        return collect($files)
            ->filter(function ($duplicity_file) use ($file){
                if (Str::endsWith($duplicity_file['Key'], '.manifest')) {
                    return Carbon::parse($duplicity_file['LastModified'])->lte($file['LastModified']);
                }
                return false;
            })
            ->sortByDesc('LastModified')
            ->value('Key');
    }
    public function getIncrementalFullFiles($timestamp, $directory)
    {
        return $this
            ->listObjectsV2(location: $directory.DIRECTORY_SEPARATOR.'duplicity-full.'.$timestamp)
            ->get('Contents');
    }
    public function getIncrementalFiles($timestamp, $directory)
    {
        return $this
            ->listObjectsV2(location: $directory.DIRECTORY_SEPARATOR.'duplicity-inc.'.$timestamp)
            ->get('Contents');
    }
    public function getSpecificFile($directory,$file)
    {
        if ($this->isGDrive()) {
            return $this->getGDriveFile($directory,$file);
        }
        return $this
            ->listObjectsV2(location: $directory.DIRECTORY_SEPARATOR.$file)
            ->get('Contents');
    }
}
