<?php

namespace App\Traits;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\BillType;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Jobs\CheckBillableModelHasBill;
use App\Jobs\UpdateBillsMetaOnModelCreated;
use App\Jobs\UpdateBillsMetaOnModelDeleted;
use App\Jobs\UpdateBillsMetaOnModelUpdated;
use App\Jobs\UpdateGeneratorDetailsOnModelDeleting;
use App\Jobs\UpdateOfferActionsOnModelCreated;
use App\Models\Bill;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Models\Package;
use App\Models\Product;
use App\Models\SubscriptionProduct;
use App\Models\Team;
use App\Services\PaymentGateway\InvoiceServices\Invoiceable;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use App\Services\XcloudProduct\CalculateBill;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * Trait Billable
 * @package App\Traits
 * @mixin Model
 * @property-read Bill[] $bills
 */
trait Billable
{
    use TeamBillingHelper;
    private array $billsData = [];

    public bool $isBillable = true;

    abstract public function getBillingName(BillingServices $service): string;
    abstract public function getBillingShortDescriptionTitle(BillingServices $service): string;
    abstract public function getBillingShortDescription(BillingServices $service): string;
    abstract public function getDefaultBillingService(): BillingServices;
    abstract public function getPricing(BillingServices $service, BillRenewalPeriod $renewalPeriod = null, Product|Package|SubscriptionProduct $billThrough = null, float $defaultPrice = 0): float;
    abstract public function getBillingTitle(BillingServices $service, Bill $relatedOrExistingBill = null): string;
    abstract public function getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator(): array;
    abstract public function getColumnsToStoreDataOnBill(): array;
    abstract public function teamInfo(): Team;
    abstract public function billingStartedFrom() : Carbon;
    abstract public function getAdditionalUsageChargeForCurrentPeriod(Bill $relatedOrExistingBill = null, Product|Package|SubscriptionProduct $billThrough = null) : float;

    /**
     * Returns a detailed comment about additional usage charges for the bill.
     *
     * This method should provide a human-readable explanation of any additional usage charges
     * that will be applied to the bill. The comment can include a {amount} placeholder which
     * will be automatically replaced with the actual charge amount and currency symbol.
     *
     * If the comment doesn't include the {amount} placeholder, the amount will be appended
     * to the end of the comment. If no comment is provided (empty string), a default message
     * will be used instead.
     *
     * @return string The comment explaining additional usage charges
     */
    abstract public function getAdditionalUsageDetailsAsComment() : string;

    /**
     * Returns an array of additional usage logs for the bill.
     *
     * @return array
     */
    abstract public function getAdditionalUsageLog() : array;
    abstract public function resetLastAdditionalUsageChargeAndComment(Bill $addedOnBill) : void;

    public static function bootBillable(): void
    {
        static::created(function ($model) {
            if (empty($model->getColumnsToStoreDataOnBill())) {
                UpdateBillsMetaOnModelCreated::dispatch($model);
            } else {
                UpdateBillsMetaOnModelCreated::dispatch($model, $model->getColumnsToStoreDataOnBill());
            }

            CheckBillableModelHasBill::dispatch($model)->delay(now()->addMinutes(5));
        });

        static::updated(function ($model) {

            if (empty($model->getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator())) {
                if (empty($model->getColumnsToStoreDataOnBill())) {
                    UpdateBillsMetaOnModelUpdated::dispatch($model);
                } else {
                    UpdateBillsMetaOnModelUpdated::dispatch($model, $model->getColumnsToStoreDataOnBill());
                }
            } elseif($model->isDirty($model->getColumnsToSyncDataWithBillWhenTheseDirtyOnGenerator())) {
                if (empty($model->getColumnsToStoreDataOnBill())) {
                    UpdateBillsMetaOnModelUpdated::dispatch($model);
                } else {
                    UpdateBillsMetaOnModelUpdated::dispatch($model, $model->getColumnsToStoreDataOnBill());
                }
            }
        });

        static::deleting(function ($model) {
            UpdateGeneratorDetailsOnModelDeleting::dispatch($model);
        });

        static::deleted(function ($model) {

            $model->bills()->update([
                'service_is_active' => false,
                'service_deactivated_from' => now()
            ]);

            UpdateBillsMetaOnModelDeleted::dispatch($model);
        });
    }

    public function bills(): MorphMany
    {
        return $this->morphMany(Bill::class, 'generator');
    }

    public function hasUnpaidPrepaidBill(): bool
    {
        return $this->bills()->prepaid()->unpaid()->orderBy('id', 'desc')->exists();
    }

    public function hasFailedPrepaidBill(): bool
    {
        return $this->bills()->prepaid()->failed()->orderBy('id', 'desc')->exists();
    }

    public function hasPrepaidBill(): bool
    {
        return $this->bills()->prepaid()->unpaid()->exists();
    }

    private function generate(array $data): Model
    {
        $unusedBill = isset($data['adjust_with_previous']) && $data['adjust_with_previous']
                        ? $this->getMonthlyUnusedBillAmount($data['service'] ?? $this?->getDefaultBillingService() ?? null)
                        : 0;

        $adjustPreviousOffer = $data['adjust_previous_offer'] ?? false;
        $inactivePrevious = $data['inactive_previous'] ?? false;
        $disableAdjustment = $data['disable_adjustment'] ?? false;

        if($data['billing_amount'] <= 0 && $adjustPreviousOffer){
            $data['billing_amount'] = $this->bills()
                                            ->where('service', $data['service'])
                                            ->where('has_offer', true)
                                            ->orderBy('id', 'desc')->first()?->billing_amount ?: 0;
        }

        $calculatedBill = CalculateBill::amount($data['billing_amount'], now(), $data['renewal_period'] ?? null)
                                        ->advanceAmount($unusedBill)
                                        ->payFullBillNowAndAdjustAdvanceAmountOnNextBilling();

        $backtrace = debug_backtrace() ?? null;

        unset($data['adjust_previous_offer']);
        unset($data['disable_adjustment']);

        $data['has_offer'] = $adjustPreviousOffer ? false : $data['has_offer'] ?? false;
        $data['title'] = $data['title'] ?? 'Bill generated';
        $data['description'] = $data['description'] ?? 'Bill generated by ' . class_basename($this);
        $data['billing_amount'] = $calculatedBill->getBillingAmount();
        $data['amount_to_pay'] = $data['has_offer'] ? 0 : $data['amount_to_pay'] ?? $calculatedBill->getAmountToPay();
        $data['adjust_with_previous'] = $data['adjust_with_previous'] ?? false;
        $data['adjustable_amount'] = $data['has_offer'] || $data['adjust_with_previous'] || $disableAdjustment ? 0 : $calculatedBill->getAdjustableAmount();
        $data['cost_per_hour'] = $calculatedBill->getCostPerHour();
        $data['application_fee_to_charge'] = $data['application_fee_to_charge'] ?? $data['actual_application_fee'] ?? 0;
        $data['type'] = $data['type'] ?? $this->teamInfo()?->billing_type ?? BillType::Prepaid;
        $data['generated_by'] = $data['generated_by'] ?? auth()->id() ?? null;
        $data['team_id'] = $data['team_id'] ?? $this->team_id ?? auth()->user()->currentTeam->id;
        $data['status'] = $data['status'] ?? BillingStatus::Unpaid;
        $data['service'] = $data['service'] ?? $this?->getDefaultBillingService() ?? null;
        $data['currency'] = BillingCurrency::USD;
        $data['due_on'] = $calculatedBill->getDueDate();
        $data['renewal_period'] = $data['renewal_period'] ?? null;
        $data['next_billing_date'] = $data['next_billing_date'] ?? $calculatedBill->getNextBillingDate();
        $data['bill_from'] = Carbon::parse($data['next_billing_date'])->firstOfMonth()->format('Y-m-d');
        $data['paid_on'] = $data['paid_on'] ?? null;
        $data['product_id'] = $data['product_id'] ?? null;
        $data['package_id'] = $data['package_id'] ?? null;
        $data['meta->calculation_data'] = $calculatedBill->calculationData;

        // add meta from array
        if (isset($data['meta']) && is_array($data['meta'])) {
            foreach ($data['meta'] as $key => $value) {
                $data['meta->' . $key] = $value;
            }
        }

        if (is_array($backtrace) && count($backtrace) > 0) {

            $latestCall = $backtrace[1];

            if (isset($latestCall['file'])) {
                $data['call_trace'] = $latestCall['file'] . ':' . $latestCall['line'] . ':' . $latestCall['function'] . '()';
            } else {
                $data['call_trace'] = $latestCall['function'] . '()';
            }
        }

        $existingBillToAdjustWithPrevious = null;

        if (isset($data['adjust_with_previous']) && $data['adjust_with_previous'] || $inactivePrevious):

        $existingBillToAdjustWithPrevious = $this->bills()
                                ->where('generator_id' , $this->id)
                                ->where('generator_type' , get_class($this))
                                ->where('service', $data['service'] ?? null)
                                ->whereNotNull('next_billing_date')
                                ->whereNotNull('renewal_period')
                                ->orderBy('id', 'desc')
                                ->first();
        endif;


        if ($data['has_offer']) {
            $data['meta->has_offer'] = $data['has_offer'];
            $data['meta->has_offer_from'] = now()->toDateTimeString();
        }

        if ($adjustPreviousOffer) {
            $data['meta->offer_ended_from'] = now()->toDateTimeString();
            $data['meta->adjust_previous_offer'] = $adjustPreviousOffer;
            $data['meta->adjust_previous_offer_from'] = now()->toDateTimeString();
        }

        $billingAmount = $data['billing_amount'];
        $service = $data['service'];
        $renewalPeriod = $data['renewal_period'];
        $nextBillingDate = $data['next_billing_date'];

        unset($data['billing_amount']);
        unset($data['service']);
        unset($data['renewal_period']);
        unset($data['next_billing_date']);
        unset($data['inactive_previous']);

        $basedOnData = [
            'billing_amount' => $billingAmount,
            'service' => $service,
            'renewal_period' => $renewalPeriod,
            'next_billing_date' => $nextBillingDate
        ];

        if (isset($this->billsData['service_is_active'])) {
            $basedOnData['service_is_active'] = $this->billsData['service_is_active'];
        }

        $newBill = $this->bills()->updateOrCreate($basedOnData, $data);

        if ($existingBillToAdjustWithPrevious || $inactivePrevious):
            $existingBillToAdjustWithPrevious?->update([
                'service_is_active' => false, // Updated for resized bill 8 May 2024
                'next_billing_date' => null,
//                'renewal_period'    => null, // Updated for resized bill 8 May 2024
                'bill_forwards_to'  => $newBill->id
            ]);
        endif;

        $this->billsData = []; // reset bills data

        return $newBill;
    }

    public function getMonthlyUnusedBillAmount(BillingServices $service = null): float
    {
        if ($service || isset($this->billsData['service'])) {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->where('service', $service ?: $this->billsData['service'] ?? $this->getDefaultBillingService())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at'
                            ]);
        } else {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at'
                            ]);
        }

        if (!$bills->exists()) {
            return 0;
        }

        $bills = $bills->get();

        $amountsToPay = 0;
        $adjustableAmounts = 0;

        $usedAmount = 0;

        foreach ($bills as $bill) {

            if (!$bill->renewal_period) continue;

            $calculatedBillTillCreation = CalculateBill::amount($bill->billing_amount, $bill->created_at, $bill->renewal_period);
            $calculatedBillTillNow = CalculateBill::amount($bill->billing_amount, Carbon::now(), $bill->renewal_period);

            $usedAmount += $calculatedBillTillNow->getAmountToPay() - $calculatedBillTillCreation->getAmountToPay();

            $amountsToPay += $bill->amount_to_pay;
            $adjustableAmounts += $bill->adjustable_amount;
        }

        return ($amountsToPay + $adjustableAmounts) - $usedAmount;
    }

    public function getMonthlyActualUsedHours(BillingServices $service = null)
    {
        if ($service || isset($this->billsData['service'])) {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->where('service', $service ?: $this->billsData['service'] ?? $this->getDefaultBillingService())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at',
                            ]);
        } else {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at',
                            ]);
        }

        if (!$bills->exists()) {
            return 0;
        }

        $bills = $bills->get();

        // sum the total get_usage_hours

        $totalUsedHours = 0;

        foreach ($bills as $bill) {
            $totalUsedHours += $bill->get_usage_hours;
        }

        return $totalUsedHours;
    }

    public function getMonthlyCostPerHour(BillingServices $service = null)
    {
        if ($service || isset($this->billsData['service'])) {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->where('service', $service ?: $this->billsData['service'] ?? $this->getDefaultBillingService())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'cost_per_hour',
                                'created_at',
                            ]);
        } else {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'cost_per_hour',
                                'created_at',
                            ]);
        }

        if (!$bills->exists()) {
            return 0;
        }

        $bills = $bills->get();

        // sum the total get_usage_hours

        $totalCostPerHour = 0;

        foreach ($bills as $bill) {
            $totalCostPerHour += $bill->cost_per_hour;
        }

        return $totalCostPerHour;
    }

    public function getMonthlyTotalActiveBilling(BillingServices $service = null)
    {
        if ($service || isset($this->billsData['service'])) {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->where('service_is_active', true)
                            ->where('service', $service ?: $this->billsData['service'] ?? $this->getDefaultBillingService())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at',
                            ]);
        } else {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->where('service_is_active', true)
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at',
                            ]);
        }

        if (!$bills->exists()) {
            return 0;
        }

        $bills = $bills->get();

        // sum the total get_usage_hours

        $totalPaid = 0;

        foreach ($bills as $bill) {
            $totalPaid += $bill->amount_to_pay;
        }

        return $totalPaid;
    }
    public function getMonthlyTotalPaid(BillingServices $service = null)
    {
        if ($service || isset($this->billsData['service'])) {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->where('service', $service ?: $this->billsData['service'] ?? $this->getDefaultBillingService())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at',
                            ]);
        } else {
            $bills = $this->bills()
                            ->where('next_billing_date', now()->day(CalculateBill::billingDate)->endOfDay()->toDateString())
                            ->select([
                                'id',
                                'billing_amount',
                                'amount_to_pay',
                                'renewal_period',
                                'service',
                                'generator_id',
                                'generator_type',
                                'adjustable_amount',
                                'next_billing_date',
                                'created_at',
                            ]);
        }

        if (!$bills->exists()) {
            return 0;
        }

        $bills = $bills->get();

        // sum the total get_usage_hours

        $totalPaid = 0;

        foreach ($bills as $bill) {
            $totalPaid += $bill->amount_to_pay;
        }

        return $totalPaid;
    }

    public function getRefundableAmount(BillingServices $service, BillRenewalPeriod $period) : float
    {
        $bills = $this->bills()
                        ->where('service', $service)
                        ->where('renewal_period', $period)
                        ->orderBy('id', 'desc')
                        ->whereNotNull('next_billing_date')
                        ->select([
                            'id',
                            'billing_amount',
                            'amount_to_pay',
                            'renewal_period',
                            'service',
                            'generator_id',
                            'generator_type',
                            'adjustable_amount',
                            'next_billing_date',
                            'created_at'
                        ]);

        if (!$bills->exists()) {
            return 0;
        }

        $bills = $bills->get();

        $calculatedBillTillCreation = 0;

        foreach ($bills as $bill) {

            $getUsedBill = CalculateBill::amount($bill->billing_amount, $bill->created_at, $bill->renewal_period)->getUsedBill();

            if ($getUsedBill) {
                $calculatedBillTillCreation += $bill->billing_amount - $getUsedBill;
            } else { // @Todo: Shifat bro won't be happy with this, to make him happy need to remove else condition
                $calculatedBillTillCreation += $bill->amount_to_pay; // TODO: Need to check if this is correct
            }

        }

        return max($calculatedBillTillCreation, 0);
    }

    public function hasOffer() : self
    {
        $this->billsData['has_offer'] = true;

        return $this;
    }

    public function billingAmount(float $amount): self
    {
        $this->billsData['billing_amount'] = $amount;

        return $this;
    }

    public function cost(float $amount): self
    {
        if (!isset($this->billsData['billing_amount'])) {
            $this->billsData['billing_amount'] = $amount;
        } else {
            $this->billsData['amount_to_pay'] = $amount;
        }

        return $this;
    }

    public function reason(string $title, string $description): self
    {
        $this->billsData['title'] = $title;
        $this->billsData['description'] = $description;

        return $this;
    }

    public function service(BillingServices $service): self
    {
        $this->billsData['service'] = $service;

        return $this;
    }

    public function useProduct(Product $product = null): self
    {
        if (!$product) {
            return $this;
        }

        $this->billsData['product_id'] = $product?->id;
        $this->billsData['service'] = $product->service_type;
        $this->billsData['renewal_period'] = $product->renewal_type;
        $this->billsData['billing_amount'] = format_billing($product->price);

        return $this;
    }

    public function serviceXcloudHosting(): self
    {
        $this->billsData['service'] = BillingServices::xCloudManagedHosting;

        return $this;
    }

    public function serviceBackup(): self
    {
        $this->billsData['service'] = BillingServices::BackupXCloudManagedHosting;

        return $this;
    }

    public function serviceProvider(): self
    {
        $this->billsData['service'] = BillingServices::SelfManagedHosting;

        return $this;
    }

    public function renewMonthly(): self
    {
        $this->billsData['renewal_period'] = BillRenewalPeriod::Monthly;

        return $this;
    }

    public function renewYearly(): self
    {
        $this->billsData['renewal_period'] = BillRenewalPeriod::Yearly;

        return $this;
    }

    public function renewOn(Carbon $every): self
    {
        $this->billsData['renewal_period'] = $every;

        return $this;
    }

    public function dueToday(): self
    {
        $this->billsData['due_on'] = now()->toDateString();
        $this->billsData['type'] = BillType::Prepaid;

        return $this;
    }

    public function dueOn(Carbon $date): self
    {
        $this->billsData['due_on'] = $date->toDateString();

        return $this;
    }

    public function prepaid(): self
    {
        $this->billsData['type'] = BillType::Prepaid;

        return $this;
    }

    public function title(string $title): self
    {
        $this->billsData['title'] = $title;

        return $this;
    }

    public function exploitUniqueHash(): self
    {
        $this->billsData['unique_hash'] = hash('sha256',uniqid());

        $this->billsData['exploit_unique_hash'] = true;

        return $this;
    }

    public function allowDuplicateOnActiveService(): self
    {
        $this->exploitUniqueHash();

        $this->billsData['service_is_active'] = true;

        return $this;
    }

    public function description(string $description): self
    {
        $this->billsData['description'] = $description;

        return $this;
    }

    public function addMeta(array $meta): self
    {
        $this->billsData['meta'] = $meta;

        return $this;
    }

    public function actualApplicationFee(float $amount): self
    {
        $this->billsData['actual_application_fee'] = $amount;

        return $this;
    }

    public function applicationFeeToCharge(float $amount): self
    {
        $this->billsData['application_fee_to_charge'] = $amount;

        return $this;
    }

    public function adjustableApplicationFee(float $amount): self
    {
        $this->billsData['adjustable_application_fee'] = $amount;

        return $this;
    }

    public function postpaid(): self
    {
        $this->billsData['type'] = BillType::Postpaid;

        return $this;
    }

    public function adjustWithPrevious(): self
    {
        $this->billsData['adjust_with_previous'] = true;

        return $this;
    }

    public function inactivePrevious(): self
    {
        $this->billsData['inactive_previous'] = true;

        return $this;
    }

    public function disableAdjustment(): self
    {
        $this->billsData['disable_adjustment'] = true;

        return $this;
    }

    public function adjustPreviousOffer(): self
    {
        $this->billsData['adjust_previous_offer'] = true;

        return $this;
    }

    private function applyAcquiredProductToBill(Product $product): self
    {
        $this->billsData['product_id'] = $product->id;
        $this->billsData['service'] = $product->service_type;
        $this->billsData['renewal_period'] = $product->renewal_type;
        $this->billsData['billing_amount'] = format_billing($product->price);
        $this->billsData['status'] = BillingStatus::Paid;
        $this->billsData['paid_on'] = now();

        return $this;
    }

    private function package(Package $package): self
    {
        $team = null;

        if (isset($billsData['team_id'])) {
            $team = Team::find($billsData['team_id']);
        }

        if (!$team) {
            $team = $this->teamInfo();
        }

        if(!$this->hasLimitOnPackage($package, $team)) {
            throw new \Exception('Max unit limit reached');
        }

        $this->billsData['package_id'] = $package->id;
        $this->billsData['service'] = $package->service_type;
        $this->billsData['renewal_period'] = $package->renewal_type;
        $this->billsData['billing_amount'] = format_billing($package->price / $package->unit);
//        $this->billsData['amount_to_pay'] = 0; // TODO: Need to check if this is correct, this should be removed
        $this->billsData['status'] = BillingStatus::Paid;
        $this->billsData['paid_on'] = now();

        return $this;
    }

    public function generateBillFromAcquiredProduct(Product $product): Model
    {
        if (!$product->is_active) {
            throw new \Exception('Product is not active');
        }

        $this->team->detachProduct($product);

        return $this->applyAcquiredProductToBill($product)->generateBill();
    }

    /**
     * @throws \Exception
     */
    public function generateBillFromPackage(Package $package): Model
    {
        return $this->package($package)->generateBill();
    }

    public function generateBill(): Model
    {
        return $this->generate($this->billsData);
    }

    /**
     * @throws GuzzleException
     */
    public function generateInvoice(InvoiceSourceEnum $source, $notification = false): Invoiceable | null
    {
        $amount = 0;

        $unpaidBills = $this->bills()->prepaid()->unpaid()->whereNull('invoice_id')->get();

        if (!$unpaidBills->count()) {
            return null;
        }

        foreach($unpaidBills as $bill){
            $amount += $bill->getAmount();
        }

        if($amount <= 0){
            return null;
        }

        $invoice = InvoiceGenerator::team($this->teamInfo())
                        ->bills($unpaidBills)
//                        ->coupon($coupon)
                        ->source($source)
                        ->sendNotification($notification)
                        ->generate();

        $unpaidBills->each(function(Bill $bill) use ($invoice){
            $bill->update([
                'invoice_id' => $invoice->id
            ]);
        });

        return $invoice;
    }
}
