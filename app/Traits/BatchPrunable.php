<?php

namespace App\Traits;

use Illuminate\Database\Events\ModelsPruned;
use LogicException;
use Illuminate\Support\Facades\Log;

trait BatchPrunable
{
    /**
     * Prune old records efficiently using an ID threshold.
     *
     * @param  int  $batchSize  Number of records to delete per batch.
     * @return int
     */
    public static function pruneAll(int $batchSize = 1000): int
    {
        if (!method_exists(static::class, 'prunable')) {
            Log::warning(static::class." does not have a prunable() method.");
            return 0;
        }

        $totalDeleted = 0;
        $iteration = 0;

        do {
            $iteration++;

            // Determine the highest ID that should be deleted in this batch
            $maxId = (new static)->prunable()
                ->orderBy('id', 'asc')
                ->limit($batchSize)
                ->pluck('id')
                ->last(); // Get the highest ID in the batch

            if ($maxId === null) {
                break; // No more records to prune
            }

            // Delete records up to and including the determined max ID
            $deletedCount = static::where('id', '<=', $maxId)->delete();
            $totalDeleted += $deletedCount;

            // Log details
            Log::info(static::class." Prune - Batch #{$iteration}: Deleted {$deletedCount} records (Up to ID: {$maxId})");


            if ($deletedCount > 0) {
                event(new ModelsPruned(static::class, $deletedCount));
            }

        } while ($deletedCount > 0); // Continue until no more records are deleted

        Log::info(static::class." Prune Completed - Total Deleted: {$totalDeleted}");

        return $totalDeleted;
    }

    public function prunable()
    {
        throw new LogicException('Please implement the prunable method on your model.');
    }
}
