<?php

namespace App\Traits;

use Illuminate\Http\RedirectResponse;

trait ChecksTrialMode
{
    /**
     * Check if the team is in trial mode and redirect if necessary.
     *
     * @return RedirectResponse|null
     */
    protected function checkTrialMode(): ?RedirectResponse
    {
        if (team()->isTrailMode()) {
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        return null;
    }
}
