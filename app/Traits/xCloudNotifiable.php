<?php

namespace App\Traits;

use App\Enums\NotificationIntegrationTypes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;

trait xCloudNotifiable
{
    use Notifiable;

    /**
     * Route notifications for the Slack channel.
     *
     * @param Notification $notification
     * @return string|null
     */
    public function routeNotificationForSlack(Notification $notification): ?string
    {
        $slackIntegration = $this->integration(NotificationIntegrationTypes::Slack);
        if (!$slackIntegration) {
            return null;
        }

        return $slackIntegration->service()->getWebhookUrl();
    }

    public function routeNotificationForWhatsApp()
    {
        $whatsAppIntegration = $this->integration(NotificationIntegrationTypes::WhatsApp);
        if (!$whatsAppIntegration) {
            return null;
        }

        return $whatsAppIntegration->meta['to'];
    }

    public function routeNotificationForTelegram()
    {
        $telegramIntegration = $this->integration(NotificationIntegrationTypes::Telegram);
        if (!$telegramIntegration) {
            return null;
        }

        if (!blank($telegramIntegration) && isset($telegramIntegration->meta['message'])) {
            return $telegramIntegration->meta['message']['chat']['id'];
        }

        return null;
    }
}
