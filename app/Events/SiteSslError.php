<?php

namespace App\Events;

use App\Contracts\Alertable;
use App\Models\Alert;
use App\Models\Site;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SiteSslError implements Alertable
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private Site $site;
    private string $ssl_status;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Site $site,$ssl_status)
    {
        $this->site = $site;
        $this->ssl_status = $ssl_status;
    }

    /**
     * Create an alert for the given instance.
     *
     * @return Alert
     */
    public function toAlert()
    {
        $this->site->team()->siteSslError($this->site,$this->ssl_status);
    }
}
