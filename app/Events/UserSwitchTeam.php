<?php

namespace App\Events;

use App\Models\Team;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithBroadcasting;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserSwitchTeam implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels, InteractsWithBroadcasting;

    public User $user;
    public Team $team;

    public function __construct(User $user, Team $team)
    {
        $this->user = $user;
        $this->team = $team;

    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return PrivateChannel
     */
    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel( "App.Models.User.{$this->user->id}");
    }

    public function broadcastWith(): array
    {
        return [
            'message' => 'UserSwitchTeam',
            'team' => $this->team->only([
                'id',
                'name',
                'user_id',
                'email',
                'team_photo_url'
            ]),
        ];
    }
}
