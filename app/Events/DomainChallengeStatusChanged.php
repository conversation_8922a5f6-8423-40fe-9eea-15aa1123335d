<?php

namespace App\Events;

use App\Models\DomainChallenge;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DomainChallengeStatusChanged implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public DomainChallenge $domainChallenge)
    {

    }

    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel('domain_challenge.updating.'.$this->domainChallenge->id);
    }

    public function broadcastWith(): array
    {
        return [
            'id' => $this->domainChallenge->id,
            'domainChallenge' => $this->domainChallenge->only(['id', 'status']),
            'ssl_certificate_status' => $this->domainChallenge->sslCertificate?->status,
        ];
    }
}
