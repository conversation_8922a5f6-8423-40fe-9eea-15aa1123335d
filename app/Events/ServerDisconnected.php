<?php

namespace App\Events;

use App\Contracts\Alertable;
use App\Enums\AlertLevel;
use App\Models\Alert;
use App\Models\Server;
use App\Repository\IntervalBuilder;
use App\Repository\IntervalScheduler;
use Carbon\Carbon;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\SerializesModels;

class ServerDisconnected implements Alertable
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private Server $server;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Server $server)
    {
        $this->server = $server;
    }

    /**
     * Create an alert for the given instance.
     *
     * @return Alert
     */
    public function toAlert()
    {
        if (!$this->server->getMeta('disconnection_start_at')) {
            $this->server->log('Skipping disconnection notification because disconnection_start_at is not set');
            return;
        }

        if (!$this->server->team) {
            $this->server->log('Skipping disconnection notification because server does not belong to a team');
            return;
        }

        $disconnectionIntervals = IntervalBuilder::create()
            ->everyHoursAfter(thresholdHours: 12, notifyEveryHours: 6)    // First 12 hours: Notify every 6 hours
            ->everyHoursAfter(thresholdHours: 24 * 3, notifyEveryHours: 24)  // First 3 days: Notify every 24 hours
            ->everyDaysAfter(thresholdDays: 30, notifyEveryDays: 7)    // Next 30 days: Notify every 7 days
            ->everyDaysAfter(thresholdDays: 31, notifyEveryDays: 30)   // After 31 days: Notify every 30 days
            ->build();

        $scheduler = new IntervalScheduler(
            $this->server->getMeta('disconnection_notification_sent_at'),
            $this->server->getMeta('disconnection_start_at'),
            $disconnectionIntervals
        );

        if (!$scheduler->shouldTrigger()) {
            $this->server->log(
                'Skipping disconnection notification because last notification was sent less than '.$scheduler->getNotifyInterval().' hours ago. Last notification was sent '.$scheduler->getHoursSinceLastNotification().' hours ago'
            );
            return;
        }

        $this->server->log(
            'Sending disconnection notification because '.$scheduler->getHoursSinceLastNotification().' hours have passed since disconnection and current notification interval is '.$scheduler->getNotifyInterval().' hours'
        );

        $this->server->team->sendServerDisconnectedMail($this->server);

        $this->server->update([
            'meta->disconnection_notification_sent_at' => now()->toDateTimeString(),
        ]);
    }
}
