<?php

namespace App\Events;

use App\Contracts\Alertable;
use App\Models\Alert;
use App\Models\Site;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DomainUpdateFailed implements Alertable
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected Site $site;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Site $site)
    {
        $this->site = $site;
    }

    public function toAlert(): void
    {
        $this->site->server->team->sendDomainUpdateFailedMail($this->site);
    }
}
