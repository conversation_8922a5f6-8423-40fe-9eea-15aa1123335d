<?php

namespace App\Events;

use App\Models\DeploymentLog;
use App\Models\Site;
use App\Models\SslCertificate;
use Illuminate\Broadcasting\InteractsWithBroadcasting;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SiteStagingPullPushStatusChanged implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, InteractsWithBroadcasting, SerializesModels;

    public function __construct(public Site $site, public ?DeploymentLog $deploymentLog = null)
    {
    }

    public function broadcastOn(): PrivateChannel
    {
        info('broadcasting');

        return new PrivateChannel('site.staging.pull-push.' . $this->site->id);
    }

    public function broadcastWith(): array
    {
        info('current deployment status: ' . $this->deploymentLog->status->value);
        return [
            'site_id' => $this->site->id,
            'deployment_log_id' => $this->deploymentLog->id,
            'deployment_action' => $this->deploymentLog->action,
            'deployment_status' => $this->deploymentLog->status->value,
            'last_ran_at' => $this->deploymentLog->updated_at->diffForHumans(),
        ];
    }
}
