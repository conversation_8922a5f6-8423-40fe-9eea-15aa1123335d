<?php

namespace App\Events;

use App\Models\Site;
use App\Models\AutoSiteMigration;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithBroadcasting;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SiteCloneStatusChanged implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, InteractsWithBroadcasting, SerializesModels;

    public function __construct(public SiteClone $siteClone)
    {
    }

    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel('site.clone.'.$this->siteClone->site_id);
    }

    public function broadcastWith(): array
    {
        return [
            'id' => $this->siteClone->id,
            'status' => (int) $this->siteClone->getMeta('cloningStatus', 1),
            'percentage' => (int) $this->siteClone->getMeta('cloningStatusPercentage', 0),
            'exception' => $this->siteClone->getMeta('cloningErrorMessage') ?: null,
            'redirect' => $this->siteClone->isFinished() || $this->siteClone->site->isProvisioned()
                ? route('site.redirect', $this->siteClone->site_id)
                : null,
            'additionalData' => $this->siteClone->getAdditionalProgressData(),
        ];
    }
}
