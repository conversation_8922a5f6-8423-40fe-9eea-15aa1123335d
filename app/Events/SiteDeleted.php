<?php

namespace App\Events;

use App\Contracts\Alertable;
use App\Enums\AlertLevel;
use App\Models\Alert;
use App\Models\Site;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Lang;

class SiteDeleted implements Alertable
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private Site $site;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Site $site)
    {
        $this->site = $site;
    }

    /**
     * Create an alert for the given instance.
     *
     * @return Alert
     */
    public function toAlert(): Alert
    {
        return $this->site->server->team->alerts()->create([
            'notifiable_id' => $this->site->server->team->id,
            'notifiable_type' => get_class($this->site->server->team),
            'level' => AlertLevel::SUCCESS,
            'type' => 'SiteDeleted',
            'related_id' => $this->site->id,
            'related_type' => Site::class,
            'meta' => [
                'message' => Lang::get('emails.site_delete_success.line', ['name' => $this->site->name]),
                'url' => $this->site->server->team->url(url('/login')),
            ],
        ]);
    }

    /**
     * Create an alert for the given instance.
     *
     */
    public function toMail()
    {
        $this->site->server->team->sendSiteDeletedMail($this->site);
    }
}
