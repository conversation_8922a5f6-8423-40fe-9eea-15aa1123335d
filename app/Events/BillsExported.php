<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithBroadcasting;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BillsExported implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, InteractsWithBroadcasting, SerializesModels;

    public $temporarySignedUrl;

    public function __construct($temporarySignedUrl)
    {
        $this->temporarySignedUrl = $temporarySignedUrl;
    }

    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel('bills-exported');
    }

    public function broadcastWith(): array
    {
        return [
            'temporarySignedUrl' => $this->temporarySignedUrl,
            'status' => 'finished'
        ];
    }
}
