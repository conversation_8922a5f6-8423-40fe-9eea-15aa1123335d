<?php

namespace App\Events;

use App\Contracts\Alertable;
use App\Enums\AlertLevel;
use App\Models\Alert;
use App\Models\Server;
use App\Models\Task;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\SerializesModels;

class ServerHealServiceRestart implements Alertable
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private Server $server;
    private string $service;
    private Task $task;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Server $server, string $service, Task $task)
    {
        $this->server = $server;
        $this->service = $service;
        $this->task = $task;
    }

    /**
     * Create an alert for the given instance.
     *
     */
    public function toAlert()
    {
        $this->server->team->sendAutoHealServiceMail($this->server, $this->service, $this->task->successful());
    }
}
