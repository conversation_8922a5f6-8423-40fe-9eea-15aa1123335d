<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class BillsToCSVExportSheet implements WithMultipleSheets
{
    protected $models;
    protected $filtersComment;
    protected $paymentStatuses;

    public function __construct(Collection $models, $filtersComment, $paymentStatus)
    {
        $this->models = $models;
        $this->filtersComment = $filtersComment;
        $this->paymentStatuses = $paymentStatus;
    }

    public function sheets(): array
    {
        $sheets = [];
        foreach ($this->paymentStatuses as $status) {
            $sheets[] = new BillsToCSVExport($this->models, $this->filtersComment, $status?->value);
        }
        return $sheets;
    }
}
