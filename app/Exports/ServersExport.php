<?php

namespace App\Exports;

use App\Enums\Stack;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ServersExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $selectedColumns;

    public function __construct(array $selectedColumns)
    {
        $this->selectedColumns = $selectedColumns;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return team()->servers()
            ->accessFilter()
            ->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        $headings = [];

        if (in_array('id', $this->selectedColumns)) {
            $headings[] = 'Server ID';
        }

        if (in_array('name', $this->selectedColumns)) {
            $headings[] = 'Name';
        }

        if (in_array('status', $this->selectedColumns)) {
            $headings[] = 'Status';
        }

        if (in_array('public_ip', $this->selectedColumns)) {
            $headings[] = 'Public IP';
        }

        if (in_array('ubuntu_version', $this->selectedColumns)) {
            $headings[] = 'Ubuntu Version';
        }

        if (in_array('php_version', $this->selectedColumns)) {
            $headings[] = 'PHP Version';
        }

        if (in_array('region', $this->selectedColumns)) {
            $headings[] = 'Region';
        }

        if (in_array('size', $this->selectedColumns)) {
            $headings[] = 'Size';
        }

        if (in_array('stack', $this->selectedColumns)) {
            $headings[] = 'Stack';
        }

        if (in_array('cpu_architecture', $this->selectedColumns)) {
            $headings[] = 'CPU Architecture';
        }

        if (in_array('ssh_port', $this->selectedColumns)) {
            $headings[] = 'SSH Port';
        }

        if (in_array('database_name', $this->selectedColumns)) {
            $headings[] = 'Database Name';
        }

        if (in_array('time_zone', $this->selectedColumns)) {
            $headings[] = 'Time Zone';
        }

        if (in_array('created_at', $this->selectedColumns)) {
            $headings[] = 'Created At';
        }

        return $headings;
    }

    /**
     * @param mixed $row
     *
     * @return array
     */
    public function map($row): array
    {
        $data = [];

        if (in_array('id', $this->selectedColumns)) {
            $data[] = $row->id;
        }

        if (in_array('name', $this->selectedColumns)) {
            $data[] = $row->name;
        }

        if (in_array('status', $this->selectedColumns)) {
            $data[] = $row->status ? $row->status->value : '';
        }

        if (in_array('public_ip', $this->selectedColumns)) {
            $data[] = $row->public_ip;
        }

        if (in_array('ubuntu_version', $this->selectedColumns)) {
            $data[] = $row->ubuntu_version;
        }

        if (in_array('php_version', $this->selectedColumns)) {
            $data[] = $row->php_version;
        }

        if (in_array('region', $this->selectedColumns)) {
            $data[] = $row->region ?? '';
        }

        if (in_array('size', $this->selectedColumns)) {
            $data[] = $row->size ?? '';
        }

        if (in_array('stack', $this->selectedColumns)) {
            if ($row->stack instanceof Stack) {
                $data[] = $row->stack->title();
            } else {
                $data[] = '';
            }
        }

        if (in_array('cpu_architecture', $this->selectedColumns)) {
            $data[] = $row->cpu_architecture ?? '';
        }

        if (in_array('ssh_port', $this->selectedColumns)) {
            $data[] = $row->ssh_port ?? '';
        }

        if (in_array('database_name', $this->selectedColumns)) {
            $data[] = $row->database_name ?? '';
        }

        if (in_array('time_zone', $this->selectedColumns)) {
            $data[] = $row->time_zone ?? '';
        }

        if (in_array('created_at', $this->selectedColumns)) {
            $data[] = $row->created_at ? $row->created_at->format('Y-m-d H:i:s') : '';
        }

        return $data;
    }
}
