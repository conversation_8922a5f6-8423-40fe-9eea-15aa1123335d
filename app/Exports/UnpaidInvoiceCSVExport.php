<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class UnpaidInvoiceCSVExport implements FromCollection, WithHeadings, ShouldAutoSize, WithTitle
{
    protected $models;
    protected $month;

    public function __construct(Collection $models, $month)
    {
        $this->models = $models->filter(function ($model) use ($month) {
            $invoiceMonth = Carbon::parse($model?->created_at)->format('F Y');
            return $invoiceMonth === $month;
        });

        $this->month = $month;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->models->map(function ($model) {
            $teamUrl = config('app.url').'/admin/resources/teams/'.$model?->team?->id;
            return [
                $model->invoice_number ?? $model->reference_no,
                $model->date,
                $model?->team?->name.' #'.$model?->team?->id,
                $teamUrl,
                $model->customer_email,
                $model->amount,
                $model->status_readable,
                $model->due_date ?? 'No Due Date',
            ];
        });
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Invoice No',
            'Date',
            'Team Name',
            'Team URL',
            'Email',
            'Amount',
            'Status',
            'Due Date'
        ];
    }

    public function title(): string
    {
        return $this->month;
    }
}
