<?php

namespace App\Exports;

use App\Models\User;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;

class WhiteLabelUsersExport implements FromCollection, WithHeadings, ShouldAutoSize, WithTitle
{
    protected $users;

    public function __construct(Collection $users)
    {
        $this->users = $users;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users->map(function ($user) {
            try {
                // Get all teams for the user (both owned and member of)
                $teamEmails = [];

                try {
                    // Get owned teams emails
                    $ownedTeams = $user->ownedTeams()->select(['teams.id', 'teams.email'])->get()->pluck('email')->toArray();
                    foreach ($ownedTeams as $email) {
                        if (!empty($email) && !in_array($email, $teamEmails)) {
                            $teamEmails[] = $email;
                        }
                    }
                } catch (\Exception $e) {
                    // Silently handle error and continue
                }

                try {
                    // Get teams user is a member of
                    $otherTeams = $user->teams()->select(['teams.id', 'teams.email'])->get()->pluck('email')->toArray();
                    foreach ($otherTeams as $email) {
                        if (!empty($email) && !in_array($email, $teamEmails)) {
                            $teamEmails[] = $email;
                        }
                    }
                } catch (\Exception $e) {
                    // Silently handle error and continue
                }

                // Get current team email if not already included
                if ($user->currentTeam && !empty($user->currentTeam->email)) {
                    if (!in_array($user->currentTeam->email, $teamEmails)) {
                        $teamEmails[] = $user->currentTeam->email;
                    }
                }

                // Get personal team email if not already included
                if (method_exists($user, 'personalTeam')) {
                    $personalTeamObj = $user->personalTeam();
                    if ($personalTeamObj && !empty($personalTeamObj->email)) {
                        if (!in_array($personalTeamObj->email, $teamEmails)) {
                            $teamEmails[] = $personalTeamObj->email;
                        }
                    }
                }

                // Format team emails as a comma-separated string
                $teamsString = !empty($teamEmails) ? implode(', ', $teamEmails) : 'No team emails';

                return [
                    'ID' => $user->id,
                    'Name' => $user->name,
                    'Email' => $user->email,
                    'White Label' => $user->whiteLabel ? $user->whiteLabel->name : 'N/A',
                    'White Label ID' => $user->white_label_id ?? 'N/A',
                    'Team Emails' => $teamsString,
                    'Teams Count' => count($teamEmails),
                    'Created At' => $user->created_at ? $user->created_at->format('Y-m-d H:i:s') : 'N/A',
                    'Email Verified' => $user->email_verified_at ? 'Yes' : 'No',
                    'Is Active' => $user->is_active ? 'Yes' : 'No',
                ];
            } catch (\Exception $e) {
                // If anything goes wrong, return basic user info
                return [
                    'ID' => $user->id ?? 'Error',
                    'Name' => $user->name ?? 'Error',
                    'Email' => $user->email ?? 'Error',
                    'White Label' => 'Error retrieving data',
                    'White Label ID' => 'Error retrieving data',
                    'Team Emails' => 'Error retrieving data',
                    'Teams Count' => 'Error',
                    'Created At' => 'Error retrieving data',
                    'Email Verified' => 'Error retrieving data',
                    'Is Active' => 'Error retrieving data',
                ];
            }
        });
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Email',
            'White Label',
            'White Label ID',
            'Team Emails',
            'Teams Count',
            'Created At',
            'Email Verified',
            'Is Active',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'White Label Users';
    }
}
