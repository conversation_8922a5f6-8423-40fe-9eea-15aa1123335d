<?php

namespace App\Exports;

use Exception;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SitesExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $selectedColumns;

    public function __construct($selectedColumns)
    {
        $this->selectedColumns = is_array($selectedColumns) ? $selectedColumns : [];
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return team()->sites()
            ->accessFilter()
            ->with('server')
            ->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        $headings = [];

        if (in_array('id', $this->selectedColumns)) {
            $headings[] = 'Site ID';
        }

        if (in_array('name', $this->selectedColumns)) {
            $headings[] = 'Name';
        }

        if (in_array('type', $this->selectedColumns)) {
            $headings[] = 'Type';
        }

        if (in_array('status', $this->selectedColumns)) {
            $headings[] = 'Status';
        }

        if (in_array('ssl_provider', $this->selectedColumns)) {
            $headings[] = 'SSL Provider';
        }

        if (in_array('environment', $this->selectedColumns)) {
            $headings[] = 'Environment';
        }

        if (in_array('php_version', $this->selectedColumns)) {
            $headings[] = 'PHP Version';
        }

        if (in_array('wordpress_version', $this->selectedColumns)) {
            $headings[] = 'WordPress Version';
        }

        if (in_array('additional_domains', $this->selectedColumns)) {
            $headings[] = 'Additional Domains';
        }

        if (in_array('web_root', $this->selectedColumns)) {
            $headings[] = 'Web Root';
        }

        if (in_array('site_user', $this->selectedColumns)) {
            $headings[] = 'Site User';
        }

        if (in_array('database_provider', $this->selectedColumns)) {
            $headings[] = 'Database Provider';
        }

        if (in_array('database_user', $this->selectedColumns)) {
            $headings[] = 'Database User';
        }

        if (in_array('database_name', $this->selectedColumns)) {
            $headings[] = 'Database Name';
        }

        if (in_array('admin_user', $this->selectedColumns)) {
            $headings[] = 'Admin User';
        }

        if (in_array('server_id', $this->selectedColumns)) {
            $headings[] = 'Server ID';
        }

        if (in_array('server_name', $this->selectedColumns)) {
            $headings[] = 'Server Name';
        }

        if (in_array('created_at', $this->selectedColumns)) {
            $headings[] = 'Created At';
        }

        return $headings;
    }

    /**
     * @param mixed $row
     *
     * @return array
     */
    public function map($row): array
    {
        $data = [];

        if (in_array('id', $this->selectedColumns)) {
            $data[] = $row->id ?? '';
        }

        if (in_array('name', $this->selectedColumns)) {
            $data[] = $row->name;
        }

        if (in_array('type', $this->selectedColumns)) {
            $data[] = $row->type ? $row->type->value : '';
        }

        if (in_array('status', $this->selectedColumns)) {
            $data[] = $row->status ? $row->status->value : '';
        }

        if (in_array('ssl_provider', $this->selectedColumns)) {
            $data[] = $row->ssl_provider;
        }

        if (in_array('environment', $this->selectedColumns)) {
            $data[] = $row->environment;
        }

        if (in_array('php_version', $this->selectedColumns)) {
            $data[] = $row->php_version;
        }

        if (in_array('wordpress_version', $this->selectedColumns)) {
            $data[] = $row->wordpress_version;
        }

        if (in_array('additional_domains', $this->selectedColumns)) {
            $domainValues = [];

            if (is_array($row->additional_domains)) {
                foreach ($row->additional_domains as $domain) {
                    if (is_array($domain) && isset($domain['value']) && !empty($domain['value'])) {
                        $domainValues[] = $domain['value'];
                    } elseif (is_object($domain) && isset($domain->value) && !empty($domain->value)) {
                        $domainValues[] = $domain->value;
                    } elseif (is_string($domain) && !empty($domain)) {
                        $domainValues[] = $domain;
                    }
                }
            } elseif (is_string($row->additional_domains) && !empty($row->additional_domains)) {
                try {
                    $domains = json_decode($row->additional_domains, true);
                    if (is_array($domains)) {
                        foreach ($domains as $domain) {
                            if (isset($domain['value']) && !empty($domain['value'])) {
                                $domainValues[] = $domain['value'];
                            }
                        }
                    } else {
                        $domainValues[] = $row->additional_domains;
                    }
                } catch (Exception $e) {
                    $domainValues[] = $row->additional_domains;
                }
            }

            $data[] = !empty($domainValues) ? implode(', ', $domainValues) : '';
        }

        if (in_array('web_root', $this->selectedColumns)) {
            $data[] = $row->web_root ?? '';
        }

        if (in_array('site_user', $this->selectedColumns)) {
            $data[] = $row->site_user ?? '';
        }

        if (in_array('database_provider', $this->selectedColumns)) {
            $data[] = $row->database_provider ?? '';
        }

        if (in_array('database_user', $this->selectedColumns)) {
            $data[] = $row->database_user ?? '';
        }

        if (in_array('database_name', $this->selectedColumns)) {
            $data[] = $row->database_name ?? '';
        }

        if (in_array('admin_user', $this->selectedColumns)) {
            $data[] = $row->admin_user ?? '';
        }

        if (in_array('server_id', $this->selectedColumns)) {
            $data[] = $row->server_id ?? '';
        }

        if (in_array('server_name', $this->selectedColumns)) {
            $data[] = $row->server ? $row->server->name : '';
        }

        if (in_array('created_at', $this->selectedColumns)) {
            $data[] = $row->created_at ? $row->created_at->format('Y-m-d H:i:s') : '';
        }

        return $data;
    }
}
