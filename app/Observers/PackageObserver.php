<?php

namespace App\Observers;

use App\Services\Fluent\FluentCRM;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;

class PackageObserver
{
    public function creating($model)
    {
        Cache::forget('xc_package');

        if (!$model->checkout_url) {
            $model->checkout_url = route('cart.checkoutWithPackage', ['package' => $model->id]);
        }

        if (!$model->fluent_crm_tag_id) {
            if (!config('app.fluent_crm_api_username') && !config('app.fluent_crm_api_password')) {
                return;
            }

            $fluentCRM = new FluentCRM(
                config('app.fluent_crm_api_username'),
                config('app.fluent_crm_api_password')
            );

            $response = collect($fluentCRM->getTags([
                'search' => $model->flag,
                'all_tags' => true
            ])['all_tags']);

            $tag = $response->where('slug', $model->flag)->first();

            if ($tag) {
                $model->fluent_crm_tag_id = $tag['id'] ?? null;
            }
        }
    }

    public function created($model)
    {
        $model->update([
            'checkout_url' => route('cart.checkoutWithPackage', ['package' => $model->id])
        ]);

        if (!$model->fluent_crm_tag_id) {
            if (!config('app.fluent_crm_api_username') && !config('app.fluent_crm_api_password')) {
                return;
            }

            $fluentCRM = new FluentCRM(
                config('app.fluent_crm_api_username'),
                config('app.fluent_crm_api_password')
            );

            $response = $fluentCRM->createTag([
                'title' => $model->name,
                'slug' => $model->flag,
                'description' => $model->description,
            ]);

            if (Arr::has($response, 'slug')) {
                throw new Exception('Fluent CRM response: '. Arr::get($response, 'slug.unique'));
            }

            $model->fluent_crm_tag_id = Arr::get($response, 'item.id');
        }
    }

    public function updating($model)
    {
        Cache::forget('xc_package');
    }
}
