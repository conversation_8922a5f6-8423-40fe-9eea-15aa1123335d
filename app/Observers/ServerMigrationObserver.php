<?php

namespace App\Observers;

use App\Events\ServerMigrationStatusChanged;
use App\Models\ServerMigration;

class ServerMigrationObserver
{
      /**
     * Handle the Server "updated" event.
     *
     * @param  \App\Models\ServerMigration  $serverMigration
     * @return void
     */
    public function updated(ServerMigration $serverMigration)
    {
        // Perform additional actions after the Site model is updated
        if ($serverMigration->isDirty('status')) {
            // The status column has been changed
            $newStatus = $serverMigration->getAttribute('status')->value;
            ServerMigrationStatusChanged::dispatch($serverMigration,$newStatus);

        }
    }
}
