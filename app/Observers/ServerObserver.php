<?php

namespace App\Observers;

use App\Enums\FirewallProtocolEnum;
use App\Enums\XcloudBilling\PlansEnum;
use App\Events\ServerStatusChanged;
use App\Models\BillingPlan;
use App\Models\Server;
use Illuminate\Support\Facades\Log;

class ServerObserver
{
    public function created(Server $server): void
    {
        try {
            $user = auth()->user();
            $currentTeam = $user->currentTeam;

            if ($user && !$currentTeam->hasAllServerAccess() && $user->can('addServer', $user->currentTeam)) {
                $user->serversByInvitation()->attach($server->id);
            }
        } catch (\Exception $e) {
            Log::info("User Attach To ServerAccess:" . $e->getMessage());
        }

        $this->handleBillingOnServerCreate($server);

        $this->generateDefaultFirewallRules($server);
    }

    public function updated(Server $server)
    {
        if ($server->isDirty('status')) {
            $newStatus = $server->status;
            ServerStatusChanged::dispatch($server, $newStatus);
        }
    }

    public function deleted(Server $server)
    {
        $this->handleServerDeletion($server);
    }

    public function restored(Server $server)
    {
        // No specific action needed for restored event
    }

    public function forceDeleted(Server $server)
    {
        //delete usersByInvitation
        $server->usersByInvitation()->detach();
        $server->team->dropOrDeleteNotificationAction($server, 'Removed');
        $this->handleServerDeletion($server);

        $server->firewallRules()->delete();
    }

    protected function handleBillingOnServerCreate(Server $server)
    {
        $activePlan = optional($server->team)->activePlan;

        if ($activePlan && $activePlan->can_be_change_dynamically) {
            $planToActivate = PlansEnum::getPlanByServerCount($server->team->countSelfManagedHostingForPlanBasedBilling(), $server?->team);
            $billingPlanToActivate = BillingPlan::where('is_active', true)->where('name', $planToActivate)->first();

            if (!$billingPlanToActivate?->can_downgrade) { // && $billingPlanToActivate->id < $activePlan->id
                return;
            }

            if (!$billingPlanToActivate) {
                return;
            }

            if ($billingPlanToActivate->requires_billing && !$server->team->hasActivePaymentMethod()) {
                return;
            }

            $server->team->update([
                'active_plan_id' => optional(BillingPlan::where('name', $planToActivate)->first())->id
            ]);
        }
    }

    protected function handleServerDeletion(Server $server)
    {
        ServerStatusChanged::dispatch($server, 'deleted');

        $this->handleBillingOnServerCreate($server);
    }

    private function generateDefaultFirewallRules(Server $server): void
    {
        $server->firewallRules()->create([
            'name' => 'SSH',
            'port' => $server->port() ?: 22,
            'ip_address' => null,
            'protocol' => FirewallProtocolEnum::ALL,
            'traffic' => 'allow',
            'is_active' => true,
            'description' => 'Default FTP',
        ]);

        $server->firewallRules()->create([
            'name' => 'HTTP',
            'port' => 80,
            'ip_address' => null,
            'protocol' => FirewallProtocolEnum::ALL,
            'traffic' => 'allow',
            'is_active' => true,
            'description' => 'Default HTTP',
        ]);

        $server->firewallRules()->create([
            'name' => 'HTTPS',
            'port' => 443,
            'ip_address' => null,
            'protocol' => FirewallProtocolEnum::ALL,
            'traffic' => 'allow',
            'is_active' => true,
            'description' => 'Default HTTPS',
        ]);
    }
}
