<?php

namespace App\Observers;

use App\Models\Refund;

class RefundObserver
{
    public function creating(Refund $refund): void
    {
        if (!$refund->amount) {
            $refund->amount = $refund?->invoice?->amount;
        }

        if (!$refund->title) {
            $refund->title = 'Refund for ' . $refund?->invoice?->title;
        }

        if (!$refund->call_trace) {
            $backtrace = debug_backtrace() ?? null;

            if (is_array($backtrace) && count($backtrace) > 0) {

                $latestCall = $backtrace[1];

                if (isset($latestCall['file'])) {
                    $data['call_trace'] = $latestCall['file'] . ':' . $latestCall['line'] . ':' . $latestCall['function'] . '()';
                } else {
                    $data['call_trace'] = $latestCall['function'] . '()';
                }

                $refund->call_trace = $data['call_trace'];
            }
        }

        if (!$refund->created_by && auth()->check()) {
            $refund->created_by = auth()->id();
        }
    }
}
