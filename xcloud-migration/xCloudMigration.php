<?php

const XCLOUD_SITE_ID = 1;
const XCLOUD_SITE_MIGRATION_ID = 1;
const XCLOUD_EXISTING_SITE_URL = 'http://wp.test';
const XCLOUD_NEW_SITE_URL = 'http://wp-clone.test';
const XCLOUD_REMOTE_BASE_URL = 'http://wp.test/?rest_route=/xcloud-migration/v1/';
const XCLOUD_DOWNLOAD_PATH = '/Users/<USER>/code/wp-clone';
const XCLOUD_AUTH_TOKEN = 'o0nR7epJ4zGGAFGO1Dqi7ahOUcgFRZ8a';
const XCLOUD_ENCRIPTION_KEY = 'hzEGCUTOItdZF6Z83G0BXRSJreS6fhEj';
const XCLOUD_CALLBACK_ENDPOINT = 'http://xcloud.test/';
const XCLOUD_TMP_STROAGE_PATH = '/tmp';

const XCLOUD_DATABASE_HOST = 'localhost';
const XCLOUD_DATABASE_NAME = 'wp_clone';
const XCLOUD_DATABASE_USERNAME = 'root';
const XCLOUD_DATABASE_PASSWORD = '';

const XCLOUD_SKIP_DATABASE_MIGRATION = false;
const XCLOUD_SKIP_DATABASE_IMPORT = false;

require_once __DIR__.'/src/SiteMigrationStatus.php';
require_once __DIR__.'/src/Connector.php';
require_once __DIR__.'/src/ConfigManager.php';
require_once __DIR__.'/src/CleanUpManager.php';
require_once __DIR__.'/src/FileSystemManager.php';
require_once __DIR__.'/src/Encrypter.php';
require_once __DIR__.'/src/FileSystem.php';
require_once __DIR__.'/src/Database.php';

class xCloudMigration
{
    static $progress = [];

    static function start()
    {
        echo "XCLOUD_SITE_ID: ".XCLOUD_SITE_ID.PHP_EOL;
        echo "XCLOUD_SITE_MIGRATION_ID: ".XCLOUD_SITE_MIGRATION_ID.PHP_EOL;
        echo "whoami: ".exec('whoami').PHP_EOL;

        $response = (new Connector)->get('/abspath');

        $abspath = isset($response['abspath']) ? $response['abspath'] : '';

        if (!$abspath) {
            echo "XCLOUD_REMOTE_ABSPATH is not defined";
            xcloud_migration_log($response);
            die;
        }

        $abspath = rtrim($abspath, '/').'/'; // make sure it ends with a slash example /home/<USER>/wp/
        echo "XCLOUD_REMOTE_ABSPATH: {$abspath}".PHP_EOL;

        xCloudMigration::addPrgress('status', 'SCANING_FILESYSTEM');

        define('XCLOUD_REMOTE_ABSPATH', $abspath);

        (new FileSystemManager)->handle();

        if (!XCLOUD_SKIP_DATABASE_MIGRATION) {
            (new Database)->handle($response);
            (new ConfigManager)->handle();
        }

        (new CleanUpManager)->handle();

        xCloudMigration::addPrgress('status', 'DB_MIGRATION_FINISHED');
    }

    static function addPrgress($key, $value)
    {
        self::$progress[$key] = $value;

        xcloud_migration_log(' '.$key.' '.$value.' ' . PHP_EOL);

        self::sendProgress();
    }

    static function sendProgress()
    {
        try {
            file_get_contents(XCLOUD_CALLBACK_ENDPOINT.'?'.http_build_query(self::$progress));
        } catch (Exception $e) {
            echo $e->getMessage()."\n";
        }
    }
}

function xcloud_migration_log(...$message)
{
    if (count($message) == 1) {
        $message = $message[0];
    }

    if (is_array($message)) {
        $message = json_encode($message, JSON_PRETTY_PRINT);
    }

    echo PHP_EOL.(new DateTime())->format("y:m:d h:i:s")." ".$message.PHP_EOL;
}

xCloudMigration::start();
