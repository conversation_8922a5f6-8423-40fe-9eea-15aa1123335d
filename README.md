## Laravel Setup

```bash
<NAME_EMAIL>:xCloudDev/xCloud.git xcloud
cd xcloud
cp .env.example .env
composer install
php artisan key:generate
php artisan ciphersweet:generate-key # Then manually add CIPHERSWEET_KEY to your .env
php artisan migrate:fresh --seed
```

## Dependencies

### Mac

```bash
brew install hudochenkov/sshpass/sshpass
brew install terraform

brew install kreuzwerker/taps/m1-terraform-provider-helper
m1-terraform-provider-helper activate
m1-terraform-provider-helper install hashicorp/template -v v2.2.0
```

### Ubuntu

```bash
sudo apt-get install sshpass

# Install Terraform
sudo apt-get update && sudo apt-get install -y gnupg software-properties-common
wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | sudo tee /usr/share/keyrings/hashicorp-archive-keyring.gpg
gpg --no-default-keyring --keyring /usr/share/keyrings/hashicorp-archive-keyring.gpg --fingerprint
echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
sudo apt update
sudo apt-get install terraform
cd /var/www/staging.x-cloud.app/terraform && terraform init
```

# Terraform Error

For more information, see the [Terraform guidelines](./terraform.md).


### Rotate ciphersweet key

```bash
php artisan ciphersweet:encrypt "App\Models\SudoUser" <key>
php artisan ciphersweet:encrypt "App\Models\Database" <key>
php artisan ciphersweet:encrypt "App\Models\Server" <key>
php artisan ciphersweet:encrypt "App\Models\Site" <key>
php artisan ciphersweet:encrypt "App\Models\SshKeyPair" <key>
php artisan ciphersweet:encrypt "App\Models\SslCertificate" <key>
```

### Provision Script

-- do we need `sendmail` ? it takes significant time to install. Also, this could probably be a separate step.
-- supervisor -- remove
-- openssh-client working?
-- libpng-dev libmagickwand-dev -- remove ?
-- `# Configure FPM Pool Settings` duplication issue

### Production Environment Variable Update

```
vapor env:pull production
```

or

```
php artisan env:decrypt --env=production --key=$LARAVEL_ENV_ENCRYPTION_KEY --force
```

make changes to your `.env.production` file then encrypt it using

```
php artisan env:encrypt --env=production --key=$LARAVEL_ENV_ENCRYPTION_KEY --force
```

then run `git push origin master` to push your updates.
