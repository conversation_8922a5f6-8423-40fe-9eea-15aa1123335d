APP_NAME=xCloud
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://xcloud.test

#currently support DO, Vultr
XCLOUD_CLOUD_PROVIDER=Vultr
XCLOUD_DO_TOKEN=secret
XCLOUD_VULTR_TOKEN=secret
XCLOUD_VULTR_SNAPSHOT_ID=cd6b9a08-3507-44e9-9199-91e065f086f0
XCLOUD_VULTR_DEFAULT_SSH_ID=
XCLOUD_DO_IMAGE_ID=131906687
DIGITALOCEAN_CLIENT_ID=
DIGITALOCEAN_CLIENT_SECRET=

LINODE_CLIENT_ID=
LINODE_CLIENT_SECRET=

XCLOUD_DO_DEFAULT_DATABASE_PASSWORD=
XCLOUD_VULTR_DEFAULT_DATABASE_PASSWORD=

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

CIPHERSWEET_KEY=

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=xcloud
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=database
SESSION_LIFETIME=1440

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=0.0.0.0
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_S3_PREFIX=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

NOVA_LICENSE_KEY=F6h8QvPSRWVztcs68eSgSadCPqz42YL8wi5luM35GFWAIHJWTL

VITE_BROADCAST_DRIVER="${BROADCAST_DRIVER}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

STRIPE_PUBLISH_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
VITE_STRIPE_PUBLISH_KEY="${STRIPE_PUBLISH_KEY}"

MAILCHIMP_API_KEY=
MAILCHIMP_SERVER_PREFIX=
MAILCHIMP_LIST_ID=

CLOUDFLARE_ACCOUNT_EMAIL=<EMAIL>

PLAYGROUND_TEAM_EMAIL=<EMAIL>

ELASTIC_EMAIL_API_KEY=

SLACK_POST_MESSAGE_URL=https://slack.com/api/chat.postMessage
SLACK_BOT_USER_OAUTH_TOKEN=
SLACK_DAILY_STAT_CHANNEL_ID=

TELEGRAM_BOT_TOKEN=
