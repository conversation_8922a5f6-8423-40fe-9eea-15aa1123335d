import "./chunk-L7LLGJ6H.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  getCurrentInstance,
  mergeProps,
  nextTick,
  normalizeClass,
  onMounted,
  openBlock,
  ref,
  renderList,
  renderSlot,
  toDisplayString,
  toRefs,
  watch,
  withKeys,
  withModifiers
} from "./chunk-SK6JZ5Q4.js";
import "./chunk-OL3AADLO.js";

// node_modules/@vueform/multiselect/dist/multiselect.js
function isNullish(val) {
  return [null, void 0].indexOf(val) !== -1;
}
function useData(props, context, dep) {
  const { object, valueProp, mode } = toRefs(props);
  const $this = getCurrentInstance().proxy;
  const iv = dep.iv;
  const update = (val) => {
    iv.value = makeInternal(val);
    const externalVal = makeExternal(val);
    context.emit("change", externalVal, $this);
    context.emit("input", externalVal);
    context.emit("update:modelValue", externalVal);
  };
  const makeExternal = (val) => {
    if (object.value) {
      return val;
    }
    if (isNullish(val)) {
      return val;
    }
    return !Array.isArray(val) ? val[valueProp.value] : val.map((v) => v[valueProp.value]);
  };
  const makeInternal = (val) => {
    if (isNullish(val)) {
      return mode.value === "single" ? {} : [];
    }
    return val;
  };
  return {
    update
  };
}
function useValue(props, context) {
  const { value, modelValue, mode, valueProp } = toRefs(props);
  const iv = ref(mode.value !== "single" ? [] : {});
  const ev = modelValue && modelValue.value !== void 0 ? modelValue : value;
  const plainValue = computed(() => {
    return mode.value === "single" ? iv.value[valueProp.value] : iv.value.map((v) => v[valueProp.value]);
  });
  const textValue = computed(() => {
    return mode.value !== "single" ? iv.value.map((v) => v[valueProp.value]).join(",") : iv.value[valueProp.value];
  });
  return {
    iv,
    internalValue: iv,
    ev,
    externalValue: ev,
    textValue,
    plainValue
  };
}
function useSearch(props, context, dep) {
  const { regex } = toRefs(props);
  const $this = getCurrentInstance().proxy;
  const isOpen = dep.isOpen;
  const open = dep.open;
  const search = ref(null);
  const input = ref(null);
  const clearSearch = () => {
    search.value = "";
  };
  const handleSearchInput = (e) => {
    search.value = e.target.value;
  };
  const handleKeypress = (e) => {
    if (regex && regex.value) {
      let regexp = regex.value;
      if (typeof regexp === "string") {
        regexp = new RegExp(regexp);
      }
      if (!e.key.match(regexp)) {
        e.preventDefault();
      }
    }
  };
  const handlePaste = (e) => {
    if (regex && regex.value) {
      let clipboardData = e.clipboardData || window.clipboardData;
      let pastedData = clipboardData.getData("Text");
      let regexp = regex.value;
      if (typeof regexp === "string") {
        regexp = new RegExp(regexp);
      }
      if (!pastedData.split("").every((c) => !!c.match(regexp))) {
        e.preventDefault();
      }
    }
    context.emit("paste", e, $this);
  };
  watch(search, (val) => {
    if (!isOpen.value && val) {
      open();
    }
    context.emit("search-change", val, $this);
  });
  return {
    search,
    input,
    clearSearch,
    handleSearchInput,
    handleKeypress,
    handlePaste
  };
}
function usePointer$1(props, context, dep) {
  const { groupSelect, mode, groups, disabledProp } = toRefs(props);
  const pointer = ref(null);
  const setPointer = (option) => {
    if (option === void 0 || option !== null && option[disabledProp.value]) {
      return;
    }
    if (groups.value && option && option.group && (mode.value === "single" || !groupSelect.value)) {
      return;
    }
    pointer.value = option;
  };
  const clearPointer = () => {
    setPointer(null);
  };
  return {
    pointer,
    setPointer,
    clearPointer
  };
}
function normalize(str, strict = true) {
  return strict ? String(str).toLowerCase().trim() : String(str).normalize("NFD").replace(/\p{Diacritic}/gu, "").toLowerCase().trim();
}
function isObject(variable) {
  return Object.prototype.toString.call(variable) === "[object Object]";
}
function arraysEqual(array1, array2) {
  const array2Sorted = array2.slice().sort();
  return array1.length === array2.length && array1.slice().sort().every(function(value, index) {
    return value === array2Sorted[index];
  });
}
function useOptions(props, context, dep) {
  const {
    options,
    mode,
    trackBy: trackBy_,
    limit,
    hideSelected,
    createTag,
    createOption: createOption_,
    label,
    appendNewTag,
    appendNewOption: appendNewOption_,
    multipleLabel,
    object,
    loading,
    delay,
    resolveOnLoad,
    minChars,
    filterResults,
    clearOnSearch,
    clearOnSelect,
    valueProp,
    canDeselect,
    max,
    strict,
    closeOnSelect,
    groups: groupped,
    reverse,
    infinite,
    groupOptions,
    groupHideEmpty,
    groupSelect,
    onCreate,
    disabledProp,
    searchStart
  } = toRefs(props);
  const $this = getCurrentInstance().proxy;
  const iv = dep.iv;
  const ev = dep.ev;
  const search = dep.search;
  const clearSearch = dep.clearSearch;
  const update = dep.update;
  const pointer = dep.pointer;
  const clearPointer = dep.clearPointer;
  const focus = dep.focus;
  const deactivate = dep.deactivate;
  const close = dep.close;
  const ap = ref([]);
  const ro = ref([]);
  const resolving = ref(false);
  const searchWatcher = ref(null);
  const offset = ref(infinite.value && limit.value === -1 ? 10 : limit.value);
  const createOption = computed(() => {
    return createTag.value || createOption_.value || false;
  });
  const appendNewOption = computed(() => {
    if (appendNewTag.value !== void 0) {
      return appendNewTag.value;
    } else if (appendNewOption_.value !== void 0) {
      return appendNewOption_.value;
    }
    return true;
  });
  const eo = computed(() => {
    if (groupped.value) {
      let groups = ro.value || [];
      let eo2 = [];
      groups.forEach((group) => {
        optionsToArray(group[groupOptions.value]).forEach((option) => {
          eo2.push(Object.assign({}, option, group[disabledProp.value] ? { [disabledProp.value]: true } : {}));
        });
      });
      return eo2;
    } else {
      let eo2 = optionsToArray(ro.value || []);
      if (ap.value.length) {
        eo2 = eo2.concat(ap.value);
      }
      return eo2;
    }
  });
  const fg = computed(() => {
    if (!groupped.value) {
      return [];
    }
    return filterGroups((ro.value || []).map((group, index) => {
      const arrayOptions = optionsToArray(group[groupOptions.value]);
      return {
        ...group,
        index,
        group: true,
        [groupOptions.value]: filterOptions(arrayOptions, false).map((o) => Object.assign({}, o, group[disabledProp.value] ? { [disabledProp.value]: true } : {})),
        __VISIBLE__: filterOptions(arrayOptions).map((o) => Object.assign({}, o, group[disabledProp.value] ? { [disabledProp.value]: true } : {}))
      };
    }));
  });
  const pfo = computed(() => {
    let options2 = eo.value;
    if (reverse.value) {
      options2 = options2.reverse();
    }
    if (createdOption.value.length) {
      options2 = createdOption.value.concat(options2);
    }
    return filterOptions(options2);
  });
  const fo = computed(() => {
    let options2 = pfo.value;
    if (offset.value > 0) {
      options2 = options2.slice(0, offset.value);
    }
    return options2;
  });
  const hasSelected = computed(() => {
    switch (mode.value) {
      case "single":
        return !isNullish(iv.value[valueProp.value]);
      case "multiple":
      case "tags":
        return !isNullish(iv.value) && iv.value.length > 0;
    }
  });
  const multipleLabelText = computed(() => {
    return multipleLabel !== void 0 && multipleLabel.value !== void 0 ? multipleLabel.value(iv.value, $this) : iv.value && iv.value.length > 1 ? `${iv.value.length} options selected` : `1 option selected`;
  });
  const noOptions = computed(() => {
    return !eo.value.length && !resolving.value && !createdOption.value.length;
  });
  const noResults = computed(() => {
    return eo.value.length > 0 && fo.value.length == 0 && (search.value && groupped.value || !groupped.value);
  });
  const createdOption = computed(() => {
    if (createOption.value === false || !search.value) {
      return [];
    }
    return getOptionByTrackBy(search.value) !== -1 ? [] : [{
      [valueProp.value]: search.value,
      [label.value]: search.value,
      [trackBy.value]: search.value,
      __CREATE__: true
    }];
  });
  const trackBy = computed(() => {
    return trackBy_.value || label.value;
  });
  const nullValue = computed(() => {
    switch (mode.value) {
      case "single":
        return null;
      case "multiple":
      case "tags":
        return [];
    }
  });
  const busy = computed(() => {
    return loading.value || resolving.value;
  });
  const select = (option) => {
    if (typeof option !== "object") {
      option = getOption(option);
    }
    switch (mode.value) {
      case "single":
        update(option);
        break;
      case "multiple":
      case "tags":
        update(iv.value.concat(option));
        break;
    }
    context.emit("select", finalValue(option), option, $this);
  };
  const deselect = (option) => {
    if (typeof option !== "object") {
      option = getOption(option);
    }
    switch (mode.value) {
      case "single":
        clear();
        break;
      case "tags":
      case "multiple":
        update(Array.isArray(option) ? iv.value.filter((v) => option.map((o) => o[valueProp.value]).indexOf(v[valueProp.value]) === -1) : iv.value.filter((v) => v[valueProp.value] != option[valueProp.value]));
        break;
    }
    context.emit("deselect", finalValue(option), option, $this);
  };
  const finalValue = (option) => {
    return object.value ? option : option[valueProp.value];
  };
  const remove = (option) => {
    deselect(option);
  };
  const handleTagRemove = (option, e) => {
    if (e.button !== 0) {
      e.preventDefault();
      return;
    }
    remove(option);
  };
  const clear = () => {
    context.emit("clear", $this);
    update(nullValue.value);
  };
  const isSelected = (option) => {
    if (option.group !== void 0) {
      return mode.value === "single" ? false : areAllSelected(option[groupOptions.value]) && option[groupOptions.value].length;
    }
    switch (mode.value) {
      case "single":
        return !isNullish(iv.value) && iv.value[valueProp.value] == option[valueProp.value];
      case "tags":
      case "multiple":
        return !isNullish(iv.value) && iv.value.map((o) => o[valueProp.value]).indexOf(option[valueProp.value]) !== -1;
    }
  };
  const isDisabled = (option) => {
    return option[disabledProp.value] === true;
  };
  const isMax = () => {
    if (max === void 0 || max.value === -1 || !hasSelected.value && max.value > 0) {
      return false;
    }
    return iv.value.length >= max.value;
  };
  const handleOptionClick = (option) => {
    if (isDisabled(option)) {
      return;
    }
    if (onCreate && onCreate.value && !isSelected(option) && option.__CREATE__) {
      option = { ...option };
      delete option.__CREATE__;
      option = onCreate.value(option, $this);
      if (option instanceof Promise) {
        resolving.value = true;
        option.then((result) => {
          resolving.value = false;
          handleOptionSelect(result);
        });
        return;
      }
    }
    handleOptionSelect(option);
  };
  const handleOptionSelect = (option) => {
    if (option.__CREATE__) {
      option = { ...option };
      delete option.__CREATE__;
    }
    switch (mode.value) {
      case "single":
        if (option && isSelected(option)) {
          if (canDeselect.value) {
            deselect(option);
          }
          return;
        }
        if (option) {
          handleOptionAppend(option);
        }
        if (clearOnSelect.value) {
          clearSearch();
        }
        if (closeOnSelect.value) {
          clearPointer();
          close();
        }
        if (option) {
          select(option);
        }
        break;
      case "multiple":
        if (option && isSelected(option)) {
          deselect(option);
          return;
        }
        if (isMax()) {
          return;
        }
        if (option) {
          handleOptionAppend(option);
          select(option);
        }
        if (clearOnSelect.value) {
          clearSearch();
        }
        if (hideSelected.value) {
          clearPointer();
        }
        if (closeOnSelect.value) {
          close();
        }
        break;
      case "tags":
        if (option && isSelected(option)) {
          deselect(option);
          return;
        }
        if (isMax()) {
          return;
        }
        if (option) {
          handleOptionAppend(option);
        }
        if (clearOnSelect.value) {
          clearSearch();
        }
        if (option) {
          select(option);
        }
        if (hideSelected.value) {
          clearPointer();
        }
        if (closeOnSelect.value) {
          close();
        }
        break;
    }
    if (!closeOnSelect.value) {
      focus();
    }
  };
  const handleGroupClick = (group) => {
    if (isDisabled(group) || mode.value === "single" || !groupSelect.value) {
      return;
    }
    switch (mode.value) {
      case "multiple":
      case "tags":
        if (areAllEnabledSelected(group[groupOptions.value])) {
          deselect(group[groupOptions.value]);
        } else {
          select(
            group[groupOptions.value].filter((o) => iv.value.map((v) => v[valueProp.value]).indexOf(o[valueProp.value]) === -1).filter((o) => !o[disabledProp.value]).filter((o, k) => iv.value.length + 1 + k <= max.value || max.value === -1)
          );
        }
        break;
    }
    if (closeOnSelect.value) {
      deactivate();
    }
  };
  const handleOptionAppend = (option) => {
    if (getOption(option[valueProp.value]) === void 0 && createOption.value) {
      context.emit("tag", option[valueProp.value], $this);
      context.emit("option", option[valueProp.value], $this);
      if (appendNewOption.value) {
        appendOption(option);
      }
      clearSearch();
    }
  };
  const selectAll = () => {
    if (mode.value === "single") {
      return;
    }
    select(fo.value);
  };
  const areAllEnabledSelected = (options2) => {
    return options2.find((o) => !isSelected(o) && !o[disabledProp.value]) === void 0;
  };
  const areAllSelected = (options2) => {
    return options2.find((o) => !isSelected(o)) === void 0;
  };
  const getOption = (val) => {
    return eo.value[eo.value.map((o) => String(o[valueProp.value])).indexOf(String(val))];
  };
  const getOptionByTrackBy = (val, norm = true) => {
    return eo.value.map((o) => parseInt(o[trackBy.value]) == o[trackBy.value] ? parseInt(o[trackBy.value]) : o[trackBy.value]).indexOf(
      parseInt(val) == val ? parseInt(val) : val
    );
  };
  const shouldHideOption = (option) => {
    return ["tags", "multiple"].indexOf(mode.value) !== -1 && hideSelected.value && isSelected(option);
  };
  const appendOption = (option) => {
    ap.value.push(option);
  };
  const filterGroups = (groups) => {
    return groupHideEmpty.value ? groups.filter(
      (g) => search.value ? g.__VISIBLE__.length : g[groupOptions.value].length
    ) : groups.filter((g) => search.value ? g.__VISIBLE__.length : true);
  };
  const filterOptions = (options2, excludeHideSelected = true) => {
    let fo2 = options2;
    if (search.value && filterResults.value) {
      fo2 = fo2.filter((option) => {
        return searchStart.value ? normalize(option[trackBy.value], strict.value).startsWith(normalize(search.value, strict.value)) : normalize(option[trackBy.value], strict.value).indexOf(normalize(search.value, strict.value)) !== -1;
      });
    }
    if (hideSelected.value && excludeHideSelected) {
      fo2 = fo2.filter((option) => !shouldHideOption(option));
    }
    return fo2;
  };
  const optionsToArray = (options2) => {
    let uo = options2;
    if (isObject(uo)) {
      uo = Object.keys(uo).map((key) => {
        let val = uo[key];
        return { [valueProp.value]: key, [trackBy.value]: val, [label.value]: val };
      });
    }
    uo = uo.map((val) => {
      return typeof val === "object" ? val : { [valueProp.value]: val, [trackBy.value]: val, [label.value]: val };
    });
    return uo;
  };
  const initInternalValue = () => {
    if (!isNullish(ev.value)) {
      iv.value = makeInternal(ev.value);
    }
  };
  const resolveOptions = (callback) => {
    resolving.value = true;
    return new Promise((resolve, reject) => {
      options.value(search.value, $this).then((response) => {
        ro.value = response || [];
        if (typeof callback == "function") {
          callback(response);
        }
        resolving.value = false;
      }).catch((e) => {
        console.error(e);
        ro.value = [];
        resolving.value = false;
      }).finally(() => {
        resolve();
      });
    });
  };
  const refreshLabels = () => {
    if (!hasSelected.value) {
      return;
    }
    if (mode.value === "single") {
      let option = getOption(iv.value[valueProp.value]);
      if (option !== void 0) {
        let newLabel = option[label.value];
        iv.value[label.value] = newLabel;
        if (object.value) {
          ev.value[label.value] = newLabel;
        }
      }
    } else {
      iv.value.forEach((val, i) => {
        let option = getOption(iv.value[i][valueProp.value]);
        if (option !== void 0) {
          let newLabel = option[label.value];
          iv.value[i][label.value] = newLabel;
          if (object.value) {
            ev.value[i][label.value] = newLabel;
          }
        }
      });
    }
  };
  const refreshOptions = (callback) => {
    resolveOptions(callback);
  };
  const makeInternal = (val) => {
    if (isNullish(val)) {
      return mode.value === "single" ? {} : [];
    }
    if (object.value) {
      return val;
    }
    return mode.value === "single" ? getOption(val) || {} : val.filter((v) => !!getOption(v)).map((v) => getOption(v));
  };
  const initSearchWatcher = () => {
    searchWatcher.value = watch(search, (query) => {
      if (query.length < minChars.value || !query && minChars.value !== 0) {
        return;
      }
      resolving.value = true;
      if (clearOnSearch.value) {
        ro.value = [];
      }
      setTimeout(() => {
        if (query != search.value) {
          return;
        }
        options.value(search.value, $this).then((response) => {
          if (query == search.value || !search.value) {
            ro.value = response;
            pointer.value = fo.value.filter((o) => o[disabledProp.value] !== true)[0] || null;
            resolving.value = false;
          }
        }).catch((e) => {
          console.error(e);
        });
      }, delay.value);
    }, { flush: "sync" });
  };
  if (mode.value !== "single" && !isNullish(ev.value) && !Array.isArray(ev.value)) {
    throw new Error(`v-model must be an array when using "${mode.value}" mode`);
  }
  if (options && typeof options.value == "function") {
    if (resolveOnLoad.value) {
      resolveOptions(initInternalValue);
    } else if (object.value == true) {
      initInternalValue();
    }
  } else {
    ro.value = options.value;
    initInternalValue();
  }
  if (delay.value > -1) {
    initSearchWatcher();
  }
  watch(delay, (value, old) => {
    if (searchWatcher.value) {
      searchWatcher.value();
    }
    if (value >= 0) {
      initSearchWatcher();
    }
  });
  watch(ev, (newValue) => {
    if (isNullish(newValue)) {
      iv.value = makeInternal(newValue);
      return;
    }
    switch (mode.value) {
      case "single":
        if (object.value ? newValue[valueProp.value] != iv.value[valueProp.value] : newValue != iv.value[valueProp.value]) {
          iv.value = makeInternal(newValue);
        }
        break;
      case "multiple":
      case "tags":
        if (!arraysEqual(object.value ? newValue.map((o) => o[valueProp.value]) : newValue, iv.value.map((o) => o[valueProp.value]))) {
          iv.value = makeInternal(newValue);
        }
        break;
    }
  }, { deep: true });
  watch(options, (n, o) => {
    if (typeof props.options === "function") {
      if (resolveOnLoad.value && (!o || n && n.toString() !== o.toString())) {
        resolveOptions();
      }
    } else {
      ro.value = props.options;
      if (!Object.keys(iv.value).length) {
        initInternalValue();
      }
      refreshLabels();
    }
  });
  watch(label, refreshLabels);
  return {
    pfo,
    fo,
    filteredOptions: fo,
    hasSelected,
    multipleLabelText,
    eo,
    extendedOptions: eo,
    fg,
    filteredGroups: fg,
    noOptions,
    noResults,
    resolving,
    busy,
    offset,
    select,
    deselect,
    remove,
    selectAll,
    clear,
    isSelected,
    isDisabled,
    isMax,
    getOption,
    handleOptionClick,
    handleGroupClick,
    handleTagRemove,
    refreshOptions,
    resolveOptions,
    refreshLabels
  };
}
function usePointer(props, context, dep) {
  const {
    valueProp,
    showOptions,
    searchable,
    groupLabel,
    groups: groupped,
    mode,
    groupSelect,
    disabledProp
  } = toRefs(props);
  const fo = dep.fo;
  const fg = dep.fg;
  const handleOptionClick = dep.handleOptionClick;
  const handleGroupClick = dep.handleGroupClick;
  const search = dep.search;
  const pointer = dep.pointer;
  const setPointer = dep.setPointer;
  const clearPointer = dep.clearPointer;
  const multiselect = dep.multiselect;
  const isOpen = dep.isOpen;
  const options = computed(() => {
    return fo.value.filter((o) => !o[disabledProp.value]);
  });
  const groups = computed(() => {
    return fg.value.filter((o) => !o[disabledProp.value]);
  });
  const canPointGroups = computed(() => {
    return mode.value !== "single" && groupSelect.value;
  });
  const isPointerGroup = computed(() => {
    return pointer.value && pointer.value.group;
  });
  const currentGroup = computed(() => {
    return getParentGroup(pointer.value);
  });
  const prevGroup = computed(() => {
    const group = isPointerGroup.value ? pointer.value : getParentGroup(pointer.value);
    const groupIndex = groups.value.map((g) => g[groupLabel.value]).indexOf(group[groupLabel.value]);
    let prevGroup2 = groups.value[groupIndex - 1];
    if (prevGroup2 === void 0) {
      prevGroup2 = lastGroup.value;
    }
    return prevGroup2;
  });
  const nextGroup = computed(() => {
    let nextIndex = groups.value.map((g) => g.label).indexOf(isPointerGroup.value ? pointer.value[groupLabel.value] : getParentGroup(pointer.value)[groupLabel.value]) + 1;
    if (groups.value.length <= nextIndex) {
      nextIndex = 0;
    }
    return groups.value[nextIndex];
  });
  const lastGroup = computed(() => {
    return [...groups.value].slice(-1)[0];
  });
  const currentGroupFirstEnabledOption = computed(() => {
    return pointer.value.__VISIBLE__.filter((o) => !o[disabledProp.value])[0];
  });
  const currentGroupPrevEnabledOption = computed(() => {
    const options2 = currentGroup.value.__VISIBLE__.filter((o) => !o[disabledProp.value]);
    return options2[options2.map((o) => o[valueProp.value]).indexOf(pointer.value[valueProp.value]) - 1];
  });
  const currentGroupNextEnabledOption = computed(() => {
    const options2 = getParentGroup(pointer.value).__VISIBLE__.filter((o) => !o[disabledProp.value]);
    return options2[options2.map((o) => o[valueProp.value]).indexOf(pointer.value[valueProp.value]) + 1];
  });
  const prevGroupLastEnabledOption = computed(() => {
    return [...prevGroup.value.__VISIBLE__.filter((o) => !o[disabledProp.value])].slice(-1)[0];
  });
  const lastGroupLastEnabledOption = computed(() => {
    return [...lastGroup.value.__VISIBLE__.filter((o) => !o[disabledProp.value])].slice(-1)[0];
  });
  const isPointed = (option) => {
    return !!pointer.value && (!option.group && pointer.value[valueProp.value] == option[valueProp.value] || option.group !== void 0 && pointer.value[groupLabel.value] == option[groupLabel.value]) ? true : void 0;
  };
  const setPointerFirst = () => {
    setPointer(options.value[0] || null);
  };
  const selectPointer = () => {
    if (!pointer.value || pointer.value[disabledProp.value] === true) {
      return;
    }
    if (isPointerGroup.value) {
      handleGroupClick(pointer.value);
    } else {
      handleOptionClick(pointer.value);
    }
  };
  const forwardPointer = () => {
    if (pointer.value === null) {
      setPointer((groupped.value && canPointGroups.value ? groups.value[0] : options.value[0]) || null);
    } else if (groupped.value && canPointGroups.value) {
      let nextPointer = isPointerGroup.value ? currentGroupFirstEnabledOption.value : currentGroupNextEnabledOption.value;
      if (nextPointer === void 0) {
        nextPointer = nextGroup.value;
      }
      setPointer(nextPointer || null);
    } else {
      let next = options.value.map((o) => o[valueProp.value]).indexOf(pointer.value[valueProp.value]) + 1;
      if (options.value.length <= next) {
        next = 0;
      }
      setPointer(options.value[next] || null);
    }
    nextTick(() => {
      adjustWrapperScrollToPointer();
    });
  };
  const backwardPointer = () => {
    if (pointer.value === null) {
      let prevPointer = options.value[options.value.length - 1];
      if (groupped.value && canPointGroups.value) {
        prevPointer = lastGroupLastEnabledOption.value;
        if (prevPointer === void 0) {
          prevPointer = lastGroup.value;
        }
      }
      setPointer(prevPointer || null);
    } else if (groupped.value && canPointGroups.value) {
      let prevPointer = isPointerGroup.value ? prevGroupLastEnabledOption.value : currentGroupPrevEnabledOption.value;
      if (prevPointer === void 0) {
        prevPointer = isPointerGroup.value ? prevGroup.value : currentGroup.value;
      }
      setPointer(prevPointer || null);
    } else {
      let prevIndex = options.value.map((o) => o[valueProp.value]).indexOf(pointer.value[valueProp.value]) - 1;
      if (prevIndex < 0) {
        prevIndex = options.value.length - 1;
      }
      setPointer(options.value[prevIndex] || null);
    }
    nextTick(() => {
      adjustWrapperScrollToPointer();
    });
  };
  const getParentGroup = (option) => {
    return groups.value.find((group) => {
      return group.__VISIBLE__.map((o) => o[valueProp.value]).indexOf(option[valueProp.value]) !== -1;
    });
  };
  const adjustWrapperScrollToPointer = () => {
    let pointedOption = multiselect.value.querySelector(`[data-pointed]`);
    if (!pointedOption) {
      return;
    }
    let wrapper = pointedOption.parentElement.parentElement;
    if (groupped.value) {
      wrapper = isPointerGroup.value ? pointedOption.parentElement.parentElement.parentElement : pointedOption.parentElement.parentElement.parentElement.parentElement;
    }
    if (pointedOption.offsetTop + pointedOption.offsetHeight > wrapper.clientHeight + wrapper.scrollTop) {
      wrapper.scrollTop = pointedOption.offsetTop + pointedOption.offsetHeight - wrapper.clientHeight;
    }
    if (pointedOption.offsetTop < wrapper.scrollTop) {
      wrapper.scrollTop = pointedOption.offsetTop;
    }
  };
  watch(search, (val) => {
    if (searchable.value) {
      if (val.length && showOptions.value) {
        setPointerFirst();
      } else {
        clearPointer();
      }
    }
  });
  watch(isOpen, (val) => {
    if (val) {
      let firstSelected = multiselect.value.querySelectorAll(`[data-selected]`)[0];
      if (!firstSelected) {
        return;
      }
      let wrapper = firstSelected.parentElement.parentElement;
      nextTick(() => {
        if (wrapper.scrollTop > 0) {
          return;
        }
        wrapper.scrollTop = firstSelected.offsetTop;
      });
    }
  });
  return {
    pointer,
    canPointGroups,
    isPointed,
    setPointerFirst,
    selectPointer,
    forwardPointer,
    backwardPointer
  };
}
function useDropdown(props, context, dep) {
  const { disabled } = toRefs(props);
  const $this = getCurrentInstance().proxy;
  const isOpen = ref(false);
  const open = () => {
    if (isOpen.value || disabled.value) {
      return;
    }
    isOpen.value = true;
    context.emit("open", $this);
  };
  const close = () => {
    if (!isOpen.value) {
      return;
    }
    isOpen.value = false;
    context.emit("close", $this);
  };
  return {
    isOpen,
    open,
    close
  };
}
function useMultiselect(props, context, dep) {
  const { searchable, disabled } = toRefs(props);
  const input = dep.input;
  const open = dep.open;
  const close = dep.close;
  const clearSearch = dep.clearSearch;
  const isOpen = dep.isOpen;
  const multiselect = ref(null);
  const tags = ref(null);
  const isActive = ref(false);
  const mouseClicked = ref(false);
  const tabindex = computed(() => {
    return searchable.value || disabled.value ? -1 : 0;
  });
  const blur = () => {
    if (searchable.value) {
      input.value.blur();
    }
    multiselect.value.blur();
  };
  const focus = () => {
    if (searchable.value && !disabled.value) {
      input.value.focus();
    }
  };
  const activate = (shouldOpen = true) => {
    if (disabled.value) {
      return;
    }
    isActive.value = true;
    if (shouldOpen) {
      open();
    }
  };
  const deactivate = () => {
    isActive.value = false;
    setTimeout(() => {
      if (!isActive.value) {
        close();
        clearSearch();
      }
    }, 1);
  };
  const handleFocusIn = () => {
    activate(mouseClicked.value);
  };
  const handleFocusOut = () => {
    deactivate();
  };
  const handleCaretClick = () => {
    deactivate();
    blur();
  };
  const handleMousedown = (e) => {
    mouseClicked.value = true;
    if (isOpen.value && (e.target.isEqualNode(multiselect.value) || e.target.isEqualNode(tags.value))) {
      setTimeout(() => {
        deactivate();
      }, 0);
    } else if (document.activeElement.isEqualNode(multiselect.value) && !isOpen.value) {
      activate();
    }
    setTimeout(() => {
      mouseClicked.value = false;
    }, 0);
  };
  return {
    multiselect,
    tags,
    tabindex,
    isActive,
    mouseClicked,
    blur,
    focus,
    activate,
    deactivate,
    handleFocusIn,
    handleFocusOut,
    handleCaretClick,
    handleMousedown
  };
}
function useKeyboard(props, context, dep) {
  const {
    mode,
    addTagOn,
    openDirection,
    searchable,
    showOptions,
    valueProp,
    groups: groupped,
    addOptionOn: addOptionOn_,
    createTag,
    createOption: createOption_,
    reverse
  } = toRefs(props);
  const $this = getCurrentInstance().proxy;
  const iv = dep.iv;
  const update = dep.update;
  const search = dep.search;
  const setPointer = dep.setPointer;
  const selectPointer = dep.selectPointer;
  const backwardPointer = dep.backwardPointer;
  const forwardPointer = dep.forwardPointer;
  const multiselect = dep.multiselect;
  const tags = dep.tags;
  const isOpen = dep.isOpen;
  const open = dep.open;
  const blur = dep.blur;
  const fo = dep.fo;
  const createOption = computed(() => {
    return createTag.value || createOption_.value || false;
  });
  const addOptionOn = computed(() => {
    if (addTagOn.value !== void 0) {
      return addTagOn.value;
    } else if (addOptionOn_.value !== void 0) {
      return addOptionOn_.value;
    }
    return ["enter"];
  });
  const preparePointer = () => {
    if (mode.value === "tags" && !showOptions.value && createOption.value && searchable.value && !groupped.value) {
      setPointer(fo.value[fo.value.map((o) => o[valueProp.value]).indexOf(search.value)]);
    }
  };
  const handleKeydown = (e) => {
    context.emit("keydown", e, $this);
    let tagList;
    let activeIndex;
    if (["ArrowLeft", "ArrowRight", "Enter"].indexOf(e.key) !== -1 && mode.value === "tags") {
      tagList = [...multiselect.value.querySelectorAll(`[data-tags] > *`)].filter((e2) => e2 !== tags.value);
      activeIndex = tagList.findIndex((e2) => e2 === document.activeElement);
    }
    switch (e.key) {
      case "Backspace":
        if (mode.value === "single") {
          return;
        }
        if (searchable.value && [null, ""].indexOf(search.value) === -1) {
          return;
        }
        if (iv.value.length === 0) {
          return;
        }
        update([...iv.value].slice(0, -1));
        break;
      case "Enter":
        e.preventDefault();
        if (activeIndex !== -1 && activeIndex !== void 0) {
          update([...iv.value].filter((v, k) => k !== activeIndex));
          if (activeIndex === tagList.length - 1) {
            if (tagList.length - 1) {
              tagList[tagList.length - 2].focus();
            } else if (searchable.value) {
              tags.value.querySelector("input").focus();
            } else {
              multiselect.value.focus();
            }
          }
          return;
        }
        if (addOptionOn.value.indexOf("enter") === -1 && createOption.value) {
          return;
        }
        preparePointer();
        selectPointer();
        break;
      case " ":
        if (!createOption.value && !searchable.value) {
          e.preventDefault();
          preparePointer();
          selectPointer();
          return;
        }
        if (!createOption.value) {
          return false;
        }
        if (addOptionOn.value.indexOf("space") === -1 && createOption.value) {
          return;
        }
        e.preventDefault();
        preparePointer();
        selectPointer();
        break;
      case "Tab":
      case ";":
      case ",":
        if (addOptionOn.value.indexOf(e.key.toLowerCase()) === -1 || !createOption.value) {
          return;
        }
        preparePointer();
        selectPointer();
        e.preventDefault();
        break;
      case "Escape":
        blur();
        break;
      case "ArrowUp":
        e.preventDefault();
        if (!showOptions.value) {
          return;
        }
        if (!isOpen.value) {
          open();
        }
        backwardPointer();
        break;
      case "ArrowDown":
        e.preventDefault();
        if (!showOptions.value) {
          return;
        }
        if (!isOpen.value) {
          open();
        }
        forwardPointer();
        break;
      case "ArrowLeft":
        if (searchable.value && tags.value.querySelector("input").selectionStart || e.shiftKey || mode.value !== "tags" || !iv.value || !iv.value.length) {
          return;
        }
        e.preventDefault();
        if (activeIndex === -1) {
          tagList[tagList.length - 1].focus();
        } else if (activeIndex > 0) {
          tagList[activeIndex - 1].focus();
        }
        break;
      case "ArrowRight":
        if (activeIndex === -1 || e.shiftKey || mode.value !== "tags" || !iv.value || !iv.value.length) {
          return;
        }
        e.preventDefault();
        if (tagList.length > activeIndex + 1) {
          tagList[activeIndex + 1].focus();
        } else if (searchable.value) {
          tags.value.querySelector("input").focus();
        } else if (!searchable.value) {
          multiselect.value.focus();
        }
        break;
    }
  };
  const handleKeyup = (e) => {
    context.emit("keyup", e, $this);
  };
  return {
    handleKeydown,
    handleKeyup,
    preparePointer
  };
}
function useClasses(props, context, dependencies) {
  const {
    classes: classes_,
    disabled,
    openDirection,
    showOptions
  } = toRefs(props);
  const isOpen = dependencies.isOpen;
  const isPointed = dependencies.isPointed;
  const isSelected = dependencies.isSelected;
  const isDisabled = dependencies.isDisabled;
  const isActive = dependencies.isActive;
  const canPointGroups = dependencies.canPointGroups;
  const resolving = dependencies.resolving;
  const fo = dependencies.fo;
  const classes = computed(() => ({
    container: "multiselect",
    containerDisabled: "is-disabled",
    containerOpen: "is-open",
    containerOpenTop: "is-open-top",
    containerActive: "is-active",
    singleLabel: "multiselect-single-label",
    singleLabelText: "multiselect-single-label-text",
    multipleLabel: "multiselect-multiple-label",
    search: "multiselect-search",
    tags: "multiselect-tags",
    tag: "multiselect-tag",
    tagDisabled: "is-disabled",
    tagRemove: "multiselect-tag-remove",
    tagRemoveIcon: "multiselect-tag-remove-icon",
    tagsSearchWrapper: "multiselect-tags-search-wrapper",
    tagsSearch: "multiselect-tags-search",
    tagsSearchCopy: "multiselect-tags-search-copy",
    placeholder: "multiselect-placeholder",
    caret: "multiselect-caret",
    caretOpen: "is-open",
    clear: "multiselect-clear",
    clearIcon: "multiselect-clear-icon",
    spinner: "multiselect-spinner",
    inifinite: "multiselect-inifite",
    inifiniteSpinner: "multiselect-inifite-spinner",
    dropdown: "multiselect-dropdown",
    dropdownTop: "is-top",
    dropdownHidden: "is-hidden",
    options: "multiselect-options",
    optionsTop: "is-top",
    group: "multiselect-group",
    groupLabel: "multiselect-group-label",
    groupLabelPointable: "is-pointable",
    groupLabelPointed: "is-pointed",
    groupLabelSelected: "is-selected",
    groupLabelDisabled: "is-disabled",
    groupLabelSelectedPointed: "is-selected is-pointed",
    groupLabelSelectedDisabled: "is-selected is-disabled",
    groupOptions: "multiselect-group-options",
    option: "multiselect-option",
    optionPointed: "is-pointed",
    optionSelected: "is-selected",
    optionDisabled: "is-disabled",
    optionSelectedPointed: "is-selected is-pointed",
    optionSelectedDisabled: "is-selected is-disabled",
    noOptions: "multiselect-no-options",
    noResults: "multiselect-no-results",
    fakeInput: "multiselect-fake-input",
    spacer: "multiselect-spacer",
    ...classes_.value
  }));
  const showDropdown = computed(() => {
    return !!(isOpen.value && showOptions.value && (!resolving.value || resolving.value && fo.value.length));
  });
  const classList = computed(() => {
    const c = classes.value;
    return {
      container: [c.container].concat(disabled.value ? c.containerDisabled : []).concat(showDropdown.value && openDirection.value === "top" ? c.containerOpenTop : []).concat(showDropdown.value && openDirection.value !== "top" ? c.containerOpen : []).concat(isActive.value ? c.containerActive : []),
      spacer: c.spacer,
      singleLabel: c.singleLabel,
      singleLabelText: c.singleLabelText,
      multipleLabel: c.multipleLabel,
      search: c.search,
      tags: c.tags,
      tag: [c.tag].concat(disabled.value ? c.tagDisabled : []),
      tagRemove: c.tagRemove,
      tagRemoveIcon: c.tagRemoveIcon,
      tagsSearchWrapper: c.tagsSearchWrapper,
      tagsSearch: c.tagsSearch,
      tagsSearchCopy: c.tagsSearchCopy,
      placeholder: c.placeholder,
      caret: [c.caret].concat(isOpen.value ? c.caretOpen : []),
      clear: c.clear,
      clearIcon: c.clearIcon,
      spinner: c.spinner,
      inifinite: c.inifinite,
      inifiniteSpinner: c.inifiniteSpinner,
      dropdown: [c.dropdown].concat(openDirection.value === "top" ? c.dropdownTop : []).concat(!isOpen.value || !showOptions.value || !showDropdown.value ? c.dropdownHidden : []),
      options: [c.options].concat(openDirection.value === "top" ? c.optionsTop : []),
      group: c.group,
      groupLabel: (g) => {
        let groupLabel = [c.groupLabel];
        if (isPointed(g)) {
          groupLabel.push(isSelected(g) ? c.groupLabelSelectedPointed : c.groupLabelPointed);
        } else if (isSelected(g) && canPointGroups.value) {
          groupLabel.push(isDisabled(g) ? c.groupLabelSelectedDisabled : c.groupLabelSelected);
        } else if (isDisabled(g)) {
          groupLabel.push(c.groupLabelDisabled);
        }
        if (canPointGroups.value) {
          groupLabel.push(c.groupLabelPointable);
        }
        return groupLabel;
      },
      groupOptions: c.groupOptions,
      option: (o, g) => {
        let option = [c.option];
        if (isPointed(o)) {
          option.push(isSelected(o) ? c.optionSelectedPointed : c.optionPointed);
        } else if (isSelected(o)) {
          option.push(isDisabled(o) ? c.optionSelectedDisabled : c.optionSelected);
        } else if (isDisabled(o) || g && isDisabled(g)) {
          option.push(c.optionDisabled);
        }
        return option;
      },
      noOptions: c.noOptions,
      noResults: c.noResults,
      fakeInput: c.fakeInput
    };
  });
  return {
    classList,
    showDropdown
  };
}
function useScroll$1(props, context, dep) {
  const {
    limit,
    infinite
  } = toRefs(props);
  const isOpen = dep.isOpen;
  const offset = dep.offset;
  const search = dep.search;
  const pfo = dep.pfo;
  const eo = dep.eo;
  const observer = ref(null);
  const infiniteLoader = ref(null);
  const hasMore = computed(() => {
    return offset.value < pfo.value.length;
  });
  const handleIntersectionObserver = (entries) => {
    const { isIntersecting, target } = entries[0];
    if (isIntersecting) {
      const parent = target.offsetParent;
      const scrollTop = parent.scrollTop;
      offset.value += limit.value == -1 ? 10 : limit.value;
      nextTick(() => {
        parent.scrollTop = scrollTop;
      });
    }
  };
  const observe = () => {
    if (isOpen.value && offset.value < pfo.value.length) {
      observer.value.observe(infiniteLoader.value);
    } else if (!isOpen.value && observer.value) {
      observer.value.disconnect();
    }
  };
  watch(isOpen, () => {
    if (!infinite.value) {
      return;
    }
    observe();
  });
  watch(search, () => {
    if (!infinite.value) {
      return;
    }
    offset.value = limit.value;
    observe();
  }, { flush: "post" });
  watch(eo, () => {
    if (!infinite.value) {
      return;
    }
    observe();
  }, { immediate: false, flush: "post" });
  onMounted(() => {
    if (window && window.IntersectionObserver) {
      observer.value = new IntersectionObserver(handleIntersectionObserver);
    }
  });
  return {
    hasMore,
    infiniteLoader
  };
}
function useScroll(props, context, dep) {
  const { placeholder, id, valueProp, label: labelProp, mode, groupLabel } = toRefs(props);
  const pointer = dep.pointer;
  dep.iv;
  dep.hasSelected;
  dep.multipleLabelText;
  dep.isOpen;
  const label = ref(null);
  const ariaOwns = computed(() => {
    let texts = [];
    if (id && id.value) {
      texts.push(id.value);
    }
    texts.push("multiselect-options");
    return texts.join("-");
  });
  const ariaActiveDescendant = computed(() => {
    let texts = [];
    if (id && id.value) {
      texts.push(id.value);
    }
    if (pointer.value) {
      texts.push(pointer.value.group ? "multiselect-group" : "multiselect-option");
      texts.push(pointer.value.group ? pointer.value.index : pointer.value[valueProp.value]);
      return texts.join("-");
    }
  });
  const ariaPlaceholder = computed(() => {
    return placeholder.value;
  });
  const ariaMultiselectable = computed(() => {
    return mode.value !== "single";
  });
  const ariaOptionId = (option) => {
    let texts = [];
    if (id && id.value) {
      texts.push(id.value);
    }
    texts.push("multiselect-option");
    texts.push(option[valueProp.value]);
    return texts.join("-");
  };
  const ariaGroupId = (option) => {
    let texts = [];
    if (id && id.value) {
      texts.push(id.value);
    }
    texts.push("multiselect-group");
    texts.push(option.index);
    return texts.join("-");
  };
  const ariaOptionLabel = (option) => {
    let texts = [];
    texts.push(option[labelProp.value]);
    return texts.join(" ");
  };
  const ariaGroupLabel = (group) => {
    let texts = [];
    texts.push(group[groupLabel.value]);
    return texts.join(" ");
  };
  const ariaTagLabel = (label2) => {
    return `${label2} \u274E`;
  };
  onMounted(() => {
    if (id && id.value && document && document.querySelector) {
      let forTag = document.querySelector(`[for="${id.value}"]`);
      label.value = forTag ? forTag.innerText : null;
    }
  });
  return {
    ariaOwns,
    ariaPlaceholder,
    ariaMultiselectable,
    ariaActiveDescendant,
    ariaOptionId,
    ariaOptionLabel,
    ariaGroupId,
    ariaGroupLabel,
    ariaTagLabel
  };
}
function resolveDeps(props, context, features, deps = {}) {
  features.forEach((composable) => {
    if (composable) {
      deps = {
        ...deps,
        ...composable(props, context, deps)
      };
    }
  });
  return deps;
}
var script = {
  name: "Multiselect",
  emits: [
    "paste",
    "open",
    "close",
    "select",
    "deselect",
    "input",
    "search-change",
    "tag",
    "option",
    "update:modelValue",
    "change",
    "clear",
    "keydown",
    "keyup"
  ],
  props: {
    value: {
      required: false
    },
    modelValue: {
      required: false
    },
    options: {
      type: [Array, Object, Function],
      required: false,
      default: () => []
    },
    id: {
      type: [String, Number],
      required: false
    },
    name: {
      type: [String, Number],
      required: false,
      default: "multiselect"
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    label: {
      type: String,
      required: false,
      default: "label"
    },
    trackBy: {
      type: String,
      required: false,
      default: void 0
    },
    valueProp: {
      type: String,
      required: false,
      default: "value"
    },
    placeholder: {
      type: String,
      required: false,
      default: null
    },
    mode: {
      type: String,
      required: false,
      default: "single"
    },
    searchable: {
      type: Boolean,
      required: false,
      default: false
    },
    limit: {
      type: Number,
      required: false,
      default: -1
    },
    hideSelected: {
      type: Boolean,
      required: false,
      default: true
    },
    createTag: {
      type: Boolean,
      required: false,
      default: void 0
    },
    createOption: {
      type: Boolean,
      required: false,
      default: void 0
    },
    appendNewTag: {
      type: Boolean,
      required: false,
      default: void 0
    },
    appendNewOption: {
      type: Boolean,
      required: false,
      default: void 0
    },
    addTagOn: {
      type: Array,
      required: false,
      default: void 0
    },
    addOptionOn: {
      type: Array,
      required: false,
      default: void 0
    },
    caret: {
      type: Boolean,
      required: false,
      default: true
    },
    loading: {
      type: Boolean,
      required: false,
      default: false
    },
    noOptionsText: {
      type: String,
      required: false,
      default: "The list is empty"
    },
    noResultsText: {
      type: String,
      required: false,
      default: "No results found"
    },
    multipleLabel: {
      type: Function,
      required: false
    },
    object: {
      type: Boolean,
      required: false,
      default: false
    },
    delay: {
      type: Number,
      required: false,
      default: -1
    },
    minChars: {
      type: Number,
      required: false,
      default: 0
    },
    resolveOnLoad: {
      type: Boolean,
      required: false,
      default: true
    },
    filterResults: {
      type: Boolean,
      required: false,
      default: true
    },
    clearOnSearch: {
      type: Boolean,
      required: false,
      default: false
    },
    clearOnSelect: {
      type: Boolean,
      required: false,
      default: true
    },
    canDeselect: {
      type: Boolean,
      required: false,
      default: true
    },
    canClear: {
      type: Boolean,
      required: false,
      default: true
    },
    max: {
      type: Number,
      required: false,
      default: -1
    },
    showOptions: {
      type: Boolean,
      required: false,
      default: true
    },
    required: {
      type: Boolean,
      required: false,
      default: false
    },
    openDirection: {
      type: String,
      required: false,
      default: "bottom"
    },
    nativeSupport: {
      type: Boolean,
      required: false,
      default: false
    },
    classes: {
      type: Object,
      required: false,
      default: () => ({})
    },
    strict: {
      type: Boolean,
      required: false,
      default: true
    },
    closeOnSelect: {
      type: Boolean,
      required: false,
      default: true
    },
    autocomplete: {
      type: String,
      required: false
    },
    groups: {
      type: Boolean,
      required: false,
      default: false
    },
    groupLabel: {
      type: String,
      required: false,
      default: "label"
    },
    groupOptions: {
      type: String,
      required: false,
      default: "options"
    },
    groupHideEmpty: {
      type: Boolean,
      required: false,
      default: false
    },
    groupSelect: {
      type: Boolean,
      required: false,
      default: true
    },
    inputType: {
      type: String,
      required: false,
      default: "text"
    },
    attrs: {
      required: false,
      type: Object,
      default: () => ({})
    },
    onCreate: {
      required: false,
      type: Function
    },
    disabledProp: {
      type: String,
      required: false,
      default: "disabled"
    },
    searchStart: {
      type: Boolean,
      required: false,
      default: false
    },
    reverse: {
      type: Boolean,
      required: false,
      default: false
    },
    regex: {
      type: [Object, String, RegExp],
      required: false,
      default: void 0
    },
    rtl: {
      type: Boolean,
      required: false,
      default: false
    },
    infinite: {
      type: Boolean,
      required: false,
      default: false
    },
    aria: {
      required: false,
      type: Object,
      default: () => ({})
    }
  },
  setup(props, context) {
    return resolveDeps(props, context, [
      useValue,
      usePointer$1,
      useDropdown,
      useSearch,
      useData,
      useMultiselect,
      useOptions,
      useScroll$1,
      usePointer,
      useKeyboard,
      useClasses,
      useScroll
    ]);
  }
};
var _hoisted_1 = ["tabindex", "id", "dir", "aria-owns", "aria-placeholder", "aria-expanded", "aria-activedescendant", "aria-multiselectable", "role"];
var _hoisted_2 = ["type", "modelValue", "value", "autocomplete", "id", "aria-owns", "aria-placeholder", "aria-expanded", "aria-activedescendant", "aria-multiselectable"];
var _hoisted_3 = ["onKeyup", "aria-label"];
var _hoisted_4 = ["onClick"];
var _hoisted_5 = ["type", "modelValue", "value", "id", "autocomplete", "aria-owns", "aria-placeholder", "aria-expanded", "aria-activedescendant", "aria-multiselectable"];
var _hoisted_6 = ["innerHTML"];
var _hoisted_7 = ["innerHTML"];
var _hoisted_8 = ["id"];
var _hoisted_9 = ["id", "aria-label", "aria-selected"];
var _hoisted_10 = ["data-pointed", "onMouseenter", "onClick"];
var _hoisted_11 = ["innerHTML"];
var _hoisted_12 = ["aria-label"];
var _hoisted_13 = ["data-pointed", "data-selected", "onMouseenter", "onClick", "id", "aria-selected", "aria-label"];
var _hoisted_14 = ["innerHTML"];
var _hoisted_15 = ["data-pointed", "data-selected", "onMouseenter", "onClick", "id", "aria-selected", "aria-label"];
var _hoisted_16 = ["innerHTML"];
var _hoisted_17 = ["innerHTML"];
var _hoisted_18 = ["innerHTML"];
var _hoisted_19 = ["value"];
var _hoisted_20 = ["name", "value"];
var _hoisted_21 = ["name", "value"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    ref: "multiselect",
    tabindex: _ctx.tabindex,
    class: _ctx.classList.container,
    id: $props.searchable ? void 0 : $props.id,
    dir: $props.rtl ? "rtl" : void 0,
    onFocusin: _cache[9] || (_cache[9] = (...args) => _ctx.handleFocusIn && _ctx.handleFocusIn(...args)),
    onFocusout: _cache[10] || (_cache[10] = (...args) => _ctx.handleFocusOut && _ctx.handleFocusOut(...args)),
    onKeydown: _cache[11] || (_cache[11] = (...args) => _ctx.handleKeydown && _ctx.handleKeydown(...args)),
    onKeyup: _cache[12] || (_cache[12] = (...args) => _ctx.handleKeyup && _ctx.handleKeyup(...args)),
    onMousedown: _cache[13] || (_cache[13] = (...args) => _ctx.handleMousedown && _ctx.handleMousedown(...args)),
    "aria-owns": !$props.searchable ? _ctx.ariaOwns : void 0,
    "aria-placeholder": !$props.searchable ? _ctx.ariaPlaceholder : void 0,
    "aria-expanded": !$props.searchable ? _ctx.isOpen : void 0,
    "aria-activedescendant": !$props.searchable ? _ctx.ariaActiveDescendant : void 0,
    "aria-multiselectable": !$props.searchable ? _ctx.ariaMultiselectable : void 0,
    role: !$props.searchable ? "listbox" : void 0
  }, !$props.searchable ? $props.aria : {}), [
    createCommentVNode(" Search "),
    $props.mode !== "tags" && $props.searchable && !$props.disabled ? (openBlock(), createElementBlock("input", mergeProps({
      key: 0,
      type: $props.inputType,
      modelValue: _ctx.search,
      value: _ctx.search,
      class: _ctx.classList.search,
      autocomplete: $props.autocomplete,
      id: $props.searchable ? $props.id : void 0,
      onInput: _cache[0] || (_cache[0] = (...args) => _ctx.handleSearchInput && _ctx.handleSearchInput(...args)),
      onKeypress: _cache[1] || (_cache[1] = (...args) => _ctx.handleKeypress && _ctx.handleKeypress(...args)),
      onPaste: _cache[2] || (_cache[2] = withModifiers((...args) => _ctx.handlePaste && _ctx.handlePaste(...args), ["stop"])),
      ref: "input",
      "aria-owns": _ctx.ariaOwns,
      "aria-placeholder": _ctx.ariaPlaceholder,
      "aria-expanded": _ctx.isOpen,
      "aria-activedescendant": _ctx.ariaActiveDescendant,
      "aria-multiselectable": _ctx.ariaMultiselectable,
      role: "listbox"
    }, {
      ...$props.attrs,
      ...$props.aria
    }), null, 16, _hoisted_2)) : createCommentVNode("v-if", true),
    createCommentVNode(" Tags (with search) "),
    $props.mode == "tags" ? (openBlock(), createElementBlock("div", {
      key: 1,
      class: normalizeClass(_ctx.classList.tags),
      "data-tags": ""
    }, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.iv, (option, i, key) => {
        return renderSlot(_ctx.$slots, "tag", {
          option,
          handleTagRemove: _ctx.handleTagRemove,
          disabled: $props.disabled
        }, () => [
          (openBlock(), createElementBlock("span", {
            class: normalizeClass(_ctx.classList.tag),
            tabindex: "-1",
            onKeyup: withKeys(($event) => _ctx.handleTagRemove(option, $event), ["enter"]),
            key,
            "aria-label": _ctx.ariaTagLabel(option[$props.label])
          }, [
            createTextVNode(toDisplayString(option[$props.label]) + " ", 1),
            !$props.disabled ? (openBlock(), createElementBlock("span", {
              key: 0,
              class: normalizeClass(_ctx.classList.tagRemove),
              onClick: ($event) => _ctx.handleTagRemove(option, $event)
            }, [
              createBaseVNode("span", {
                class: normalizeClass(_ctx.classList.tagRemoveIcon)
              }, null, 2)
            ], 10, _hoisted_4)) : createCommentVNode("v-if", true)
          ], 42, _hoisted_3))
        ]);
      }), 256)),
      createBaseVNode("div", {
        class: normalizeClass(_ctx.classList.tagsSearchWrapper),
        ref: "tags"
      }, [
        createCommentVNode(" Used for measuring search width "),
        createBaseVNode("span", {
          class: normalizeClass(_ctx.classList.tagsSearchCopy)
        }, toDisplayString(_ctx.search), 3),
        createCommentVNode(" Actual search input "),
        $props.searchable && !$props.disabled ? (openBlock(), createElementBlock("input", mergeProps({
          key: 0,
          type: $props.inputType,
          modelValue: _ctx.search,
          value: _ctx.search,
          class: _ctx.classList.tagsSearch,
          id: $props.searchable ? $props.id : void 0,
          autocomplete: $props.autocomplete,
          onInput: _cache[3] || (_cache[3] = (...args) => _ctx.handleSearchInput && _ctx.handleSearchInput(...args)),
          onKeypress: _cache[4] || (_cache[4] = (...args) => _ctx.handleKeypress && _ctx.handleKeypress(...args)),
          onPaste: _cache[5] || (_cache[5] = withModifiers((...args) => _ctx.handlePaste && _ctx.handlePaste(...args), ["stop"])),
          ref: "input",
          "aria-owns": _ctx.ariaOwns,
          "aria-placeholder": _ctx.ariaPlaceholder,
          "aria-expanded": _ctx.isOpen,
          "aria-activedescendant": _ctx.ariaActiveDescendant,
          "aria-multiselectable": _ctx.ariaMultiselectable,
          role: "listbox"
        }, {
          ...$props.attrs,
          ...$props.aria
        }), null, 16, _hoisted_5)) : createCommentVNode("v-if", true)
      ], 2)
    ], 2)) : createCommentVNode("v-if", true),
    createCommentVNode(" Single label "),
    $props.mode == "single" && _ctx.hasSelected && !_ctx.search && _ctx.iv ? renderSlot(_ctx.$slots, "singlelabel", {
      key: 2,
      value: _ctx.iv
    }, () => [
      createBaseVNode("div", {
        class: normalizeClass(_ctx.classList.singleLabel),
        "aria-hidden": "true"
      }, [
        createBaseVNode("span", {
          class: normalizeClass(_ctx.classList.singleLabelText),
          innerHTML: _ctx.iv[$props.label]
        }, null, 10, _hoisted_6)
      ], 2)
    ]) : createCommentVNode("v-if", true),
    createCommentVNode(" Multiple label "),
    $props.mode == "multiple" && _ctx.hasSelected && !_ctx.search ? renderSlot(_ctx.$slots, "multiplelabel", {
      key: 3,
      values: _ctx.iv
    }, () => [
      createBaseVNode("div", {
        class: normalizeClass(_ctx.classList.multipleLabel),
        innerHTML: _ctx.multipleLabelText,
        "aria-hidden": "true"
      }, null, 10, _hoisted_7)
    ]) : createCommentVNode("v-if", true),
    createCommentVNode(" Placeholder "),
    $props.placeholder && !_ctx.hasSelected && !_ctx.search ? renderSlot(_ctx.$slots, "placeholder", { key: 4 }, () => [
      createBaseVNode("div", {
        class: normalizeClass(_ctx.classList.placeholder),
        "aria-hidden": "true"
      }, toDisplayString($props.placeholder), 3)
    ]) : createCommentVNode("v-if", true),
    createCommentVNode(" Spinner "),
    $props.loading || _ctx.resolving ? renderSlot(_ctx.$slots, "spinner", { key: 5 }, () => [
      createBaseVNode("span", {
        class: normalizeClass(_ctx.classList.spinner),
        "aria-hidden": "true"
      }, null, 2)
    ]) : createCommentVNode("v-if", true),
    createCommentVNode(" Clear "),
    _ctx.hasSelected && !$props.disabled && $props.canClear && !_ctx.busy ? renderSlot(_ctx.$slots, "clear", {
      key: 6,
      clear: _ctx.clear
    }, () => [
      createBaseVNode("span", {
        tabindex: "0",
        role: "button",
        "aria-label": "\u274E",
        class: normalizeClass(_ctx.classList.clear),
        onClick: _cache[6] || (_cache[6] = (...args) => _ctx.clear && _ctx.clear(...args)),
        onKeyup: _cache[7] || (_cache[7] = withKeys((...args) => _ctx.clear && _ctx.clear(...args), ["enter"]))
      }, [
        createBaseVNode("span", {
          class: normalizeClass(_ctx.classList.clearIcon)
        }, null, 2)
      ], 34)
    ]) : createCommentVNode("v-if", true),
    createCommentVNode(" Caret "),
    $props.caret && $props.showOptions ? renderSlot(_ctx.$slots, "caret", { key: 7 }, () => [
      createBaseVNode("span", {
        class: normalizeClass(_ctx.classList.caret),
        onClick: _cache[8] || (_cache[8] = (...args) => _ctx.handleCaretClick && _ctx.handleCaretClick(...args)),
        "aria-hidden": "true"
      }, null, 2)
    ]) : createCommentVNode("v-if", true),
    createCommentVNode(" Options "),
    createBaseVNode("div", {
      class: normalizeClass(_ctx.classList.dropdown),
      tabindex: "-1"
    }, [
      renderSlot(_ctx.$slots, "beforelist", { options: _ctx.fo }),
      createBaseVNode("ul", {
        class: normalizeClass(_ctx.classList.options),
        id: _ctx.ariaOwns
      }, [
        $props.groups ? (openBlock(true), createElementBlock(Fragment, { key: 0 }, renderList(_ctx.fg, (group, i, key) => {
          return openBlock(), createElementBlock("li", {
            class: normalizeClass(_ctx.classList.group),
            key,
            id: _ctx.ariaGroupId(group),
            "aria-label": _ctx.ariaGroupLabel(group),
            "aria-selected": _ctx.isSelected(group),
            role: "option"
          }, [
            createBaseVNode("div", {
              class: normalizeClass(_ctx.classList.groupLabel(group)),
              "data-pointed": _ctx.isPointed(group),
              onMouseenter: ($event) => _ctx.setPointer(group, i),
              onClick: ($event) => _ctx.handleGroupClick(group)
            }, [
              renderSlot(_ctx.$slots, "grouplabel", {
                group,
                isSelected: _ctx.isSelected,
                isPointed: _ctx.isPointed
              }, () => [
                createBaseVNode("span", {
                  innerHTML: group[$props.groupLabel]
                }, null, 8, _hoisted_11)
              ])
            ], 42, _hoisted_10),
            createBaseVNode("ul", {
              class: normalizeClass(_ctx.classList.groupOptions),
              "aria-label": _ctx.ariaGroupLabel(group),
              role: "group"
            }, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(group.__VISIBLE__, (option, i2, key2) => {
                return openBlock(), createElementBlock("li", {
                  class: normalizeClass(_ctx.classList.option(option, group)),
                  "data-pointed": _ctx.isPointed(option),
                  "data-selected": _ctx.isSelected(option) || void 0,
                  key: key2,
                  onMouseenter: ($event) => _ctx.setPointer(option),
                  onClick: ($event) => _ctx.handleOptionClick(option),
                  id: _ctx.ariaOptionId(option),
                  "aria-selected": _ctx.isSelected(option),
                  "aria-label": _ctx.ariaOptionLabel(option),
                  role: "option"
                }, [
                  renderSlot(_ctx.$slots, "option", {
                    option,
                    isSelected: _ctx.isSelected,
                    isPointed: _ctx.isPointed,
                    search: _ctx.search
                  }, () => [
                    createBaseVNode("span", {
                      innerHTML: option[$props.label]
                    }, null, 8, _hoisted_14)
                  ])
                ], 42, _hoisted_13);
              }), 128))
            ], 10, _hoisted_12)
          ], 10, _hoisted_9);
        }), 128)) : (openBlock(true), createElementBlock(Fragment, { key: 1 }, renderList(_ctx.fo, (option, i, key) => {
          return openBlock(), createElementBlock("li", {
            class: normalizeClass(_ctx.classList.option(option)),
            "data-pointed": _ctx.isPointed(option),
            "data-selected": _ctx.isSelected(option) || void 0,
            key,
            onMouseenter: ($event) => _ctx.setPointer(option),
            onClick: ($event) => _ctx.handleOptionClick(option),
            id: _ctx.ariaOptionId(option),
            "aria-selected": _ctx.isSelected(option),
            "aria-label": _ctx.ariaOptionLabel(option),
            role: "option"
          }, [
            renderSlot(_ctx.$slots, "option", {
              option,
              isSelected: _ctx.isSelected,
              isPointed: _ctx.isPointed,
              search: _ctx.search
            }, () => [
              createBaseVNode("span", {
                innerHTML: option[$props.label]
              }, null, 8, _hoisted_16)
            ])
          ], 42, _hoisted_15);
        }), 128))
      ], 10, _hoisted_8),
      _ctx.noOptions ? renderSlot(_ctx.$slots, "nooptions", { key: 0 }, () => [
        createBaseVNode("div", {
          class: normalizeClass(_ctx.classList.noOptions),
          innerHTML: $props.noOptionsText
        }, null, 10, _hoisted_17)
      ]) : createCommentVNode("v-if", true),
      _ctx.noResults ? renderSlot(_ctx.$slots, "noresults", { key: 1 }, () => [
        createBaseVNode("div", {
          class: normalizeClass(_ctx.classList.noResults),
          innerHTML: $props.noResultsText
        }, null, 10, _hoisted_18)
      ]) : createCommentVNode("v-if", true),
      $props.infinite && _ctx.hasMore ? (openBlock(), createElementBlock("div", {
        key: 2,
        class: normalizeClass(_ctx.classList.inifinite),
        ref: "infiniteLoader"
      }, [
        renderSlot(_ctx.$slots, "infinite", {}, () => [
          createBaseVNode("span", {
            class: normalizeClass(_ctx.classList.inifiniteSpinner)
          }, null, 2)
        ])
      ], 2)) : createCommentVNode("v-if", true),
      renderSlot(_ctx.$slots, "afterlist", { options: _ctx.fo })
    ], 2),
    createCommentVNode(" Hacky input element to show HTML5 required warning "),
    $props.required ? (openBlock(), createElementBlock("input", {
      key: 8,
      class: normalizeClass(_ctx.classList.fakeInput),
      tabindex: "-1",
      value: _ctx.textValue,
      required: ""
    }, null, 10, _hoisted_19)) : createCommentVNode("v-if", true),
    createCommentVNode(" Native input support "),
    $props.nativeSupport ? (openBlock(), createElementBlock(Fragment, { key: 9 }, [
      $props.mode == "single" ? (openBlock(), createElementBlock("input", {
        key: 0,
        type: "hidden",
        name: $props.name,
        value: _ctx.plainValue !== void 0 ? _ctx.plainValue : ""
      }, null, 8, _hoisted_20)) : (openBlock(true), createElementBlock(Fragment, { key: 1 }, renderList(_ctx.plainValue, (v, i) => {
        return openBlock(), createElementBlock("input", {
          type: "hidden",
          name: `${$props.name}[]`,
          value: v,
          key: i
        }, null, 8, _hoisted_21);
      }), 128))
    ], 64)) : createCommentVNode("v-if", true),
    createCommentVNode(" Create height for empty input "),
    createBaseVNode("div", {
      class: normalizeClass(_ctx.classList.spacer)
    }, null, 2)
  ], 16, _hoisted_1);
}
script.render = render;
script.__file = "src/Multiselect.vue";

// dep:@vueform_multiselect
var vueform_multiselect_default = script;
export {
  vueform_multiselect_default as default
};
//# sourceMappingURL=@vueform_multiselect.js.map
