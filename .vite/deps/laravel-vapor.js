import {
  require_axios
} from "./chunk-MKUWNTKJ.js";
import {
  __commonJS
} from "./chunk-OL3AADLO.js";

// node_modules/laravel-vapor/dist/laravel-vapor.js
var require_laravel_vapor = __commonJS({
  "node_modules/laravel-vapor/dist/laravel-vapor.js"(exports, module) {
    function e() {
      return (e = Object.assign || function(e2) {
        for (var t2 = 1; t2 < arguments.length; t2++) {
          var r2 = arguments[t2];
          for (var n in r2)
            Object.prototype.hasOwnProperty.call(r2, n) && (e2[n] = r2[n]);
        }
        return e2;
      }).apply(this, arguments);
    }
    var t = require_axios();
    var r = function() {
      try {
        return process.env.MIX_VAPOR_ASSET_URL ? process.env.MIX_VAPOR_ASSET_URL : "";
      } catch (e2) {
        throw console.error("Unable to automatically resolve the asset URL. Use Vapor.withBaseAssetUrl() to specify it manually."), e2;
      }
    };
    module.exports = new (function() {
      function n() {
      }
      var o = n.prototype;
      return o.asset = function(e2) {
        return r() + "/" + e2;
      }, o.withBaseAssetUrl = function(e2) {
        r = function() {
          return e2 || "";
        };
      }, o.store = function(r2, n2) {
        void 0 === n2 && (n2 = {});
        try {
          return Promise.resolve(t.post(n2.signedStorageUrl ? n2.signedStorageUrl : "/vapor/signed-storage-url", e({ bucket: n2.bucket || "", content_type: n2.contentType || r2.type, expires: n2.expires || "", visibility: n2.visibility || "" }, n2.data), e({ baseURL: n2.baseURL || null, headers: n2.headers || {} }, n2.options))).then(function(e2) {
            var o2 = e2.data.headers;
            return "Host" in o2 && delete o2.Host, void 0 === n2.progress && (n2.progress = function() {
            }), Promise.resolve(t.put(e2.data.url, r2, { cancelToken: n2.cancelToken || "", headers: o2, onUploadProgress: function(e3) {
              n2.progress(e3.loaded / e3.total);
            } })).then(function() {
              return e2.data.extension = r2.name.split(".").pop(), e2.data;
            });
          });
        } catch (e2) {
          return Promise.reject(e2);
        }
      }, n;
    }())();
  }
});

// dep:laravel-vapor
var laravel_vapor_default = require_laravel_vapor();
export {
  laravel_vapor_default as default
};
//# sourceMappingURL=laravel-vapor.js.map
