<svg id="ePtumNLyfYq1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 750 497" shape-rendering="geometricPrecision" text-rendering="geometricPrecision"><defs><linearGradient id="ePtumNLyfYq4-fill" x1="374.7135" y1="67.4778" x2="374.7135" y2="258.5382" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq4-fill-0" offset="0%" stop-color="#fff"/><stop id="ePtumNLyfYq4-fill-1" offset="100%" stop-color="#edf2f8"/></linearGradient><linearGradient id="ePtumNLyfYq6-fill" x1="340.139" y1="186.8442" x2="340.139" y2="195.6542" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq6-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq6-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq7-fill" x1="340.139" y1="203.3792" x2="340.139" y2="212.1862" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq7-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq7-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq8-fill" x1="340.139" y1="219.9182" x2="340.139" y2="228.7252" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq8-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq8-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq9-fill" x1="340.139" y1="236.4492" x2="340.139" y2="245.2572" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq9-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq9-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq10-fill" x1="370.099" y1="186.8442" x2="370.099" y2="195.6542" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq10-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq10-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq11-fill" x1="370.099" y1="203.3792" x2="370.099" y2="212.1862" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq11-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq11-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq12-fill" x1="370.099" y1="219.9182" x2="370.099" y2="228.7252" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq12-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq12-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq13-fill" x1="370.099" y1="236.4492" x2="370.099" y2="245.2572" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq13-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq13-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq14-fill" x1="400.06" y1="186.8442" x2="400.06" y2="195.6542" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq14-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq14-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq15-fill" x1="400.06" y1="203.3792" x2="400.06" y2="212.1862" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq15-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq15-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq16-fill" x1="400.06" y1="219.9182" x2="400.06" y2="228.7252" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq16-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq16-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient><linearGradient id="ePtumNLyfYq17-fill" x1="400.06" y1="236.4492" x2="400.06" y2="245.2572" spreadMethod="pad" gradientUnits="userSpaceOnUse" gradientTransform="translate(0 0)"><stop id="ePtumNLyfYq17-fill-0" offset="0%" stop-color="#4588ff"/><stop id="ePtumNLyfYq17-fill-1" offset="100%" stop-color="#4588ff"/></linearGradient></defs><path d="M540.9,25.1c3.1-3.1,7.3-5,12-5h16.8C572.2,8.6,582.5,0,594.7,0c12.3,0,22.5,8.6,25,20.2h4.5c4.7,0,8.9,1.9,12,5s4.9,7.3,4.9,12c0,5.2-2.3,9.8-6,12.9-2.9,2.5-6.8,4-10.9,4h-71.4c-4.7,0-8.9-1.9-12-5s-4.9-7.3-4.9-12c.1-4.7,2-8.9,5-12v0Z" fill="#fff"/><path d="M127,72.6c-2.9-2.9-6.8-4.7-11.2-4.7h-15.8C97.7,57.1,88,49,76.5,49s-21.1,8.1-23.5,18.9h-4.2c-4.4,0-8.4,1.8-11.2,4.7-2.9,2.9-4.7,6.8-4.7,11.2c0,4.9,2.2,9.2,5.6,12.1c2.8,2.4,6.4,3.8,10.3,3.8h67c4.4,0,8.4-1.8,11.2-4.7c2.9-2.9,4.7-6.8,4.7-11.2s-1.8-8.4-4.7-11.2v0Z" fill="#fff"/><path d="M749.4,322.7c-73.6-12.8-172.5-24.4-288.2-24.4-255.4,0-455.8,56.8-455.8,56.8-14.8-31.8,1.6-123.5,40.4-145s72.4,21.5,103.1,6.1s2-150.8,49-159.5s76.6,78.5,134.1,78.5s48.6-87.2,115.8-90.7s65.4,89.3,105.8,89.3s24-37.2,87.4-38.3s107.5,198.8,108.4,227.2Z" fill="url(#ePtumNLyfYq4-fill)"/><path d="M374.5,496.4c176.5,0,319.5-5.6,319.5-12.5s-143-12.5-319.5-12.5-319.5,5.6-319.5,12.5s143,12.5,319.5,12.5Z" fill="#d0dff3"/><path id="ePtumNLyfYq6" d="M344.5,191.2c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,2,4.4,4.4Z" fill="url(#ePtumNLyfYq6-fill)"/><path id="ePtumNLyfYq7" d="M344.5,207.8c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,1.9,4.4,4.4Z" fill="url(#ePtumNLyfYq7-fill)" fill-opacity="0.6"/><path id="ePtumNLyfYq8" d="M344.5,224.3c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,2,4.4,4.4Z" fill="url(#ePtumNLyfYq8-fill)" fill-opacity="0.4"/><path id="ePtumNLyfYq9" d="M344.5,240.9c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,1.9,4.4,4.4Z" fill="url(#ePtumNLyfYq9-fill)" fill-opacity="0.2"/><path id="ePtumNLyfYq10" d="M374.5,191.2c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,2,4.4,4.4Z" fill="url(#ePtumNLyfYq10-fill)"/><path id="ePtumNLyfYq11" d="M374.5,207.8c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,1.9,4.4,4.4Z" fill="url(#ePtumNLyfYq11-fill)" fill-opacity="0.6"/><path id="ePtumNLyfYq12" d="M374.5,224.3c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,2,4.4,4.4Z" fill="url(#ePtumNLyfYq12-fill)" fill-opacity="0.4"/><path id="ePtumNLyfYq13" d="M374.5,240.9c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4c2.4-.1,4.4,1.9,4.4,4.4Z" fill="url(#ePtumNLyfYq13-fill)" fill-opacity="0.2"/><path id="ePtumNLyfYq14" d="M404.5,191.2c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,2,4.4,4.4Z" fill="url(#ePtumNLyfYq14-fill)"/><path id="ePtumNLyfYq15" d="M404.5,207.8c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,1.9,4.4,4.4Z" fill="url(#ePtumNLyfYq15-fill)" fill-opacity="0.6"/><path id="ePtumNLyfYq16" d="M404.5,224.3c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4s4.4,2,4.4,4.4Z" fill="url(#ePtumNLyfYq16-fill)" fill-opacity="0.4"/><path id="ePtumNLyfYq17" d="M404.5,240.9c0,2.4-2,4.4-4.4,4.4s-4.4-2-4.4-4.4s2-4.4,4.4-4.4c2.4-.1,4.4,1.9,4.4,4.4Z" fill="url(#ePtumNLyfYq17-fill)" fill-opacity="0.2"/><path d="M478.4,126.3c0-22.5-14.8-41.5-35.1-47.9-3-23.3-22.8-41.4-47-41.4-9.7,0-18.7,2.9-26.2,7.9-7.5-5-16.5-7.9-26.2-7.9-24.1,0-44,18.1-46.9,41.4-20.4,6.4-35.1,25.4-35.1,47.9c0,27.7,22.5,50.2,50.2,50.2h117.3v0c27.1-.6,49-22.8,49-50.2v0Z" fill="#147aff"/><path d="M369.7,44.7c-7.4-4.8-16.3-7.7-25.8-7.7-24.1,0-44,18.1-46.9,41.4-20.4,6.4-35.1,25.4-35.1,47.9c0,27.7,22.5,50.2,50.2,50.2h46.3c-28.5-24-32.8-81.1,11.3-131.8v0Z" opacity="0.15" fill="#fff"/><path d="M491.1,265h-243.1v154.6h243.1v-154.6Z" fill="#d0dff3"/><path d="M410.7,477.1L406.5,435h-72.7l-4.2,42.1h81.1Z" fill="#d0dff3"/><path d="M420.8,467h-101.4c-4.2,0-7.6,3.4-7.6,7.7c0,4.2,3.4,7.7,7.6,7.7h101.3c4.2,0,7.7-3.4,7.7-7.7s-3.4-7.7-7.6-7.7v0Z" fill="#fff"/><path d="M496.6,253.1h-253c-2.2,0-4,1.8-4,4v179.2c0,2.2,1.8,4,4,4h253.1c2.2,0,4-1.8,4-4v-179.2c0-2.2-1.8-4-4.1-4ZM484.2,411.3h-228.2v-143.5h228.3v143.5Z" fill="#fff"/><path d="M500.6,415.7v-158.6c0-2.2-1.8-4-4-4h-253.1c-2.2,0-4,1.8-4,4v158.6h261.1ZM255.9,267.8h228.3v143.5h-228.3v-143.5Z" fill="#fff"/><path d="M446.6,381.6h-153.1c-7,0-12.6-5.7-12.6-12.6v-97.9c0-5.8,4.7-10.6,10.6-10.6h157.2c5.8,0,10.6,4.7,10.6,10.6v97.9c0,7-5.7,12.6-12.7,12.6v0Z" fill="#147aff"/><path d="M371.6,260.5h-80c-5.8,0-10.6,4.7-10.6,10.6v97.9c0,7,5.6,12.6,12.6,12.6h53.7l24.3-121.1v0Z" opacity="0.2" fill="#fff"/><path d="M280.9,280.4h178.3v110.6c0,5.8-4.7,10.6-10.6,10.6h-157.1c-5.8,0-10.6-4.7-10.6-10.6v-110.6v0Z" fill="#fff"/><path d="M454.3,280.4v110.6c0,3.1-2.5,5.6-5.6,5.6h-157.2c-3.1,0-5.6-2.5-5.6-5.6v-110.6h168.4v0Z" transform="translate(-10.83582 0)" fill="#f3f9ff"/><path d="M323.7,274.1c1.9,0,3.4-1.5,3.4-3.4s-1.5-3.4-3.4-3.4-3.4,1.5-3.4,3.4c0,1.8,1.6,3.4,3.4,3.4Z" fill="#fff"/><path d="M306.5,270.7c0,1.9,1.5,3.4,3.4,3.4s3.4-1.5,3.4-3.4-1.5-3.4-3.4-3.4-3.4,1.5-3.4,3.4Z" fill="#fff"/><path d="M292.6,270.7c0,1.9,1.5,3.4,3.4,3.4s3.4-1.5,3.4-3.4-1.5-3.4-3.4-3.4c-1.8,0-3.4,1.5-3.4,3.4Z" fill="#fff"/><path d="M384.1,386.9h-25.1c-4.9,0-8.9-4-8.9-8.9s4-8.9,8.9-8.9h25.2c4.9,0,8.9,4,8.9,8.9s-4,8.9-9,8.9Z" transform="matrix(1 0 0 0.913649 0 32.640678)" fill="#32ba7c"/><path d="M376.1,356.7c-1.5,0-2.7-1.2-2.7-2.7s1.2-2.7,2.7-2.7s2.7,1.2,2.7,2.7c-.1,1.5-1.3,2.7-2.7,2.7Z" transform="translate(12.144088 0.1)" fill="#cddbe8"/><path id="ePtumNLyfYq38" d="M388,357.6c-2,0-3.5-1.6-3.5-3.5s1.6-3.5,3.5-3.5s3.5,1.6,3.5,3.5-1.5,3.5-3.5,3.5Z" transform="translate(.244088 0)" opacity="0" fill="#2473ff"/><path d="M376.1,356.7c-1.5,0-2.7-1.2-2.7-2.7s1.2-2.7,2.7-2.7s2.7,1.2,2.7,2.7c-.1,1.5-1.3,2.7-2.7,2.7Z" transform="translate(1.25 0.1)" fill="#cddbe8"/><path id="ePtumNLyfYq40" d="M388,357.6c-2,0-3.5-1.6-3.5-3.5s1.6-3.5,3.5-3.5s3.5,1.6,3.5,3.5-1.5,3.5-3.5,3.5Z" transform="translate(-10.65 0)" opacity="0" fill="#2473ff"/><path d="M376.1,356.7c-1.5,0-2.7-1.2-2.7-2.7s1.2-2.7,2.7-2.7s2.7,1.2,2.7,2.7c-.1,1.5-1.3,2.7-2.7,2.7Z" transform="translate(-11.4 0.1)" fill="#cddbe8"/><path id="ePtumNLyfYq42" d="M388,357.6c-2,0-3.5-1.6-3.5-3.5s1.6-3.5,3.5-3.5s3.5,1.6,3.5,3.5-1.5,3.5-3.5,3.5Z" transform="translate(-23.3 0)" opacity="0" fill="#2473ff"/><path d="M376.1,356.7c-1.5,0-2.7-1.2-2.7-2.7s1.2-2.7,2.7-2.7s2.7,1.2,2.7,2.7c-.1,1.5-1.3,2.7-2.7,2.7Z" transform="translate(-23.154028 0.1)" fill="#cddbe8"/><path id="ePtumNLyfYq44" d="M388,357.6c-2,0-3.5-1.6-3.5-3.5s1.6-3.5,3.5-3.5s3.5,1.6,3.5,3.5-1.5,3.5-3.5,3.5Z" transform="translate(-35.054028 0)" opacity="0" fill="#2473ff"/><path id="ePtumNLyfYq47" d="M391,320.9v-6.8l-4.7-.7c-.4-1.5-1-3-1.8-4.3l2.8-3.7-4.8-4.8-3.8,2.8c-1.3-.8-2.7-1.4-4.3-1.8l-.7-4.6h-6.8l-.7,4.6c-1.5.4-3,1-4.3,1.8l-3.7-2.8-4.8,4.8l2.8,3.7c-.8,1.3-1.4,2.7-1.8,4.3l-4.6.7v6.8l4.6.7c.4,1.5.9,3,1.8,4.3l-2.8,3.7l4.8,4.8l3.7-2.8c1.3.8,2.7,1.4,4.3,1.8l.7,4.6h6.8l.7-4.6c1.5-.4,3-1,4.3-1.8l3.8,2.8l4.8-4.8-2.8-3.8c.8-1.3,1.4-2.8,1.8-4.3l4.7-.6v0Zm-20.5,1.8c-2.9,0-5.2-2.3-5.2-5.2s2.3-5.2,5.2-5.2s5.2,2.3,5.2,5.2-2.3,5.2-5.2,5.2v0Z" fill="#2473ff"/><path d="M499.6,202.4v31.8c0,2.1,1.7,3.7,3.7,3.7h37.9c2.1,0,3.7-1.7,3.7-3.7v-31.8c0-2.1-1.7-3.7-3.7-3.7h-37.9c-2,0-3.7,1.7-3.7,3.7Z" fill="#fff"/><path d="M541.2,235.6c.8,0,1.4-.6,1.4-1.4v-31.8c0-.8-.6-1.4-1.4-1.4h-37.9c-.8,0-1.4.6-1.4,1.4v31.8c0,.8.6,1.4,1.4,1.4h37.9Z" fill="#dcebf7"/><path d="M508.4,221.3c2.3,1,5.1,1.1,7.5.1s4.5-3.3,7-3.9c4.3-1.1,8.9,2.3,12.2,4.7c2.5,1.8,4.9,3.7,7.5,5.5v6.5c0,.8-.6,1.4-1.4,1.4h-37.9c-.8,0-1.4-.6-1.4-1.4v-17.7c1.9,2,4,3.7,6.5,4.8v0Z" fill="#ceddea"/><path d="M571.5,212.3v33.7c0,2.2-1.8,4-3.9,4h-40.1c-2.2,0-3.9-1.8-3.9-4v-33.6c0-2.2,1.8-4,3.9-4h40.1c2.1,0,3.9,1.7,3.9,3.9v0Z" fill="#fff"/><path d="M527.5,247.5c-.8,0-1.5-.7-1.5-1.5v-33.6c0-.8.7-1.5,1.5-1.5h40.1c.8,0,1.5.7,1.5,1.5v33.6c0,.8-.7,1.5-1.5,1.5h-40.1v0Z" fill="#dcebf7"/><path d="M562.2,232.3c-2.5,1.1-5.4,1.1-7.9.1-2.6-1-4.8-3.4-7.4-4.1-4.6-1.2-9.4,2.4-12.9,5-2.7,1.9-5.2,4-7.9,5.9v6.8c0,.8.7,1.5,1.5,1.5h40.1c.8,0,1.5-.7,1.5-1.5v-18.7c-2.2,2-4.4,3.8-7,5v0Z" fill="#ceddea"/><path d="M162.2,189h39.4c5.1,0,9.2,4.1,9.2,9.2v37.9c0,5.1-4.1,9.2-9.2,9.2h-39.4c-5.1,0-9.2-4.1-9.2-9.2v-37.9c0-5.1,4.1-9.2,9.2-9.2v0Z" fill="#fff"/><path d="M165.7,206.9h32.4c1.1,0,1.9.9,1.9,1.9s-.9,1.9-1.9,1.9h-32.4c-1.1,0-1.9-.9-1.9-1.9s.9-1.9,1.9-1.9v0Z" fill="#d9e9f4"/><path d="M163.8,200.4c0-1.1.9-1.9,1.9-1.9h32.4c1.1,0,1.9.9,1.9,1.9s-.9,1.9-1.9,1.9h-32.4c-1,.1-1.9-.8-1.9-1.9v0Z" fill="#d9e9f4"/><path d="M198.1,215.3h-32.4c-1.1,0-1.9.9-1.9,1.9c0,1.1.9,1.9,1.9,1.9h32.4c1.1,0,1.9-.9,1.9-1.9c0-1.1-.8-1.9-1.9-1.9Z" fill="#d9e9f4"/><path d="M165.7,223.7h32.4c1.1,0,1.9.9,1.9,1.9c0,1.1-.9,1.9-1.9,1.9h-32.4c-1.1,0-1.9-.9-1.9-1.9c0-1.1.9-1.9,1.9-1.9v0Z" fill="#d9e9f4"/><path d="M198.1,232.1h-32.4c-1.1,0-1.9.9-1.9,1.9c0,1.1.9,1.9,1.9,1.9h32.4c1.1,0,1.9-.9,1.9-1.9c0-1.1-.8-1.9-1.9-1.9Z" fill="#d9e9f4"/><path id="ePtumNLyfYq60" d="M359.26418,378c0,0,17.455074,0,24.93582,0" transform="matrix(1.043639 0 0 1-16.222017 0)" fill="none" stroke="#81e6b8" stroke-width="10" stroke-linecap="round" stroke-miterlimit="50" stroke-dashoffset="24.94" stroke-dasharray="24.94"/><path id="ePtumNLyfYq61" d="M359.26418,378c0,0,17.455074,0,24.93582,0" transform="matrix(1.043639 0 0 1-16.222017 0)" fill="none" stroke="#81e6b8" stroke-width="10" stroke-linecap="round" stroke-miterlimit="50" stroke-dashoffset="24.94" stroke-dasharray="24.94"/><path id="ePtumNLyfYq62" d="M359.26418,378c0,0,17.455074,0,24.93582,0" transform="matrix(1.043639 0 0 1-16.222017 0)" fill="none" stroke="#81e6b8" stroke-width="10" stroke-linecap="round" stroke-miterlimit="50" stroke-dashoffset="24.94" stroke-dasharray="24.94"/>
<script><![CDATA[
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof __SVGATOR_DEFINE__&&__SVGATOR_DEFINE__.amd?__SVGATOR_DEFINE__(e):((t="undefined"!=typeof globalThis?globalThis:t||self).__SVGATOR_PLAYER__=t.__SVGATOR_PLAYER__||{},t.__SVGATOR_PLAYER__["91c80d77"]=e())}(this,(function(){"use strict";function t(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function e(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function o(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function u(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function l(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function f(t,e,n){return(f=l()?Reflect.construct:function(t,e,n){var r=[null];r.push.apply(r,e);var i=new(Function.bind.apply(t,r));return n&&s(i,n.prototype),i}).apply(null,arguments)}function c(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function h(t,e,n){return(h="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(r){var i=Object.getOwnPropertyDescriptor(r,e);return i.get?i.get.call(n):i.value}})(t,e,n||t)}function v(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}Number.isInteger||(Number.isInteger=function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}),Number.EPSILON||(Number.EPSILON=2220446049250313e-31);var y=g(Math.pow(10,-6));function g(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6;if(Number.isInteger(t))return t;var n=Math.pow(10,e);return Math.round((+t+Number.EPSILON)*n)/n}function p(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y;return Math.abs(t-e)<n}var m=Math.PI/180;function b(t){return t}function w(t,e,n){var r=1-n;return 3*n*r*(t*r+e*n)+n*n*n}function k(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return t<0||t>1||n<0||n>1?null:p(t,e)&&p(n,r)?b:function(i){if(i<=0)return t>0?i*e/t:0===e&&n>0?i*r/n:0;if(i>=1)return n<1?1+(i-1)*(r-1)/(n-1):1===n&&t<1?1+(i-1)*(e-1)/(t-1):1;for(var o,u=0,a=1;u<a;){var s=w(t,n,o=(u+a)/2);if(p(i,s))break;s<i?u=o:a=o}return w(e,r,o)}}function _(){return 1}function A(t){return 1===t?1:0}function x(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(1===t){if(0===e)return A;if(1===e)return _}var n=1/t;return function(t){return t>=1?1:(t+=e*n)-t%n}}var O=Math.sin,S=Math.cos,E=Math.acos,P=Math.asin,j=Math.tan,M=Math.atan2,B=Math.PI/180,I=180/Math.PI,T=Math.sqrt,R=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;r(this,t),this.m=[e,n,i,o,u,a],this.i=null,this.w=null,this.s=null}return o(t,[{key:"determinant",get:function(){var t=this.m;return t[0]*t[3]-t[1]*t[2]}},{key:"isIdentity",get:function(){if(null===this.i){var t=this.m;this.i=1===t[0]&&0===t[1]&&0===t[2]&&1===t[3]&&0===t[4]&&0===t[5]}return this.i}},{key:"point",value:function(t,e){var n=this.m;return{x:n[0]*t+n[2]*e+n[4],y:n[1]*t+n[3]*e+n[5]}}},{key:"translateSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!t&&!e)return this;var n=this.m;return n[4]+=n[0]*t+n[2]*e,n[5]+=n[1]*t+n[3]*e,this.w=this.s=this.i=null,this}},{key:"rotateSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(t%=360){var e=O(t*=B),n=S(t),r=this.m,i=r[0],o=r[1];r[0]=i*n+r[2]*e,r[1]=o*n+r[3]*e,r[2]=r[2]*n-i*e,r[3]=r[3]*n-o*e,this.w=this.s=this.i=null}return this}},{key:"scaleSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(1!==t||1!==e){var n=this.m;n[0]*=t,n[1]*=t,n[2]*=e,n[3]*=e,this.w=this.s=this.i=null}return this}},{key:"skewSelf",value:function(t,e){if(e%=360,(t%=360)||e){var n=this.m,r=n[0],i=n[1],o=n[2],u=n[3];t&&(t=j(t*B),n[2]+=r*t,n[3]+=i*t),e&&(e=j(e*B),n[0]+=o*e,n[1]+=u*e),this.w=this.s=this.i=null}return this}},{key:"resetSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,u=this.m;return u[0]=t,u[1]=e,u[2]=n,u[3]=r,u[4]=i,u[5]=o,this.w=this.s=this.i=null,this}},{key:"recomposeSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;return this.isIdentity||this.resetSelf(),t&&(t.x||t.y)&&this.translateSelf(t.x,t.y),e&&this.rotateSelf(e),n&&(n.x&&this.skewSelf(n.x,0),n.y&&this.skewSelf(0,n.y)),!r||1===r.x&&1===r.y||this.scaleSelf(r.x,r.y),i&&(i.x||i.y)&&this.translateSelf(i.x,i.y),this}},{key:"decompose",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.m,r=n[0]*n[0]+n[1]*n[1],i=[[n[0],n[1]],[n[2],n[3]]],o=T(r);if(0===o)return{origin:{x:g(n[4]),y:g(n[5])},translate:{x:g(t),y:g(e)},scale:{x:0,y:0},skew:{x:0,y:0},rotate:0};i[0][0]/=o,i[0][1]/=o;var u=n[0]*n[3]-n[1]*n[2]<0;u&&(o=-o);var a=i[0][0]*i[1][0]+i[0][1]*i[1][1];i[1][0]-=i[0][0]*a,i[1][1]-=i[0][1]*a;var s=T(i[1][0]*i[1][0]+i[1][1]*i[1][1]);if(0===s)return{origin:{x:g(n[4]),y:g(n[5])},translate:{x:g(t),y:g(e)},scale:{x:g(o),y:0},skew:{x:0,y:0},rotate:0};i[1][0]/=s,i[1][1]/=s,a/=s;var l=0;return i[1][1]<0?(l=E(i[1][1])*I,i[0][1]<0&&(l=360-l)):l=P(i[0][1])*I,u&&(l=-l),a=M(a,T(i[0][0]*i[0][0]+i[0][1]*i[0][1]))*I,u&&(a=-a),{origin:{x:g(n[4]),y:g(n[5])},translate:{x:g(t),y:g(e)},scale:{x:g(o),y:g(s)},skew:{x:g(a),y:0},rotate:g(l)}}},{key:"clone",value:function(){var t=this.m;return new this.constructor(t[0],t[1],t[2],t[3],t[4],t[5])}},{key:"toString",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ";if(null===this.s){var e=this.m.map((function(t){return g(t)}));1===e[0]&&0===e[1]&&0===e[2]&&1===e[3]?this.s="translate("+e[4]+t+e[5]+")":this.s="matrix("+e.join(t)+")"}return this.s}}],[{key:"create",value:function(t){return t?Array.isArray(t)?f(this,v(t)):t instanceof this?t.clone():(new this).recomposeSelf(t.origin,t.rotate,t.skew,t.scale,t.translate):new this}}]),t}();function N(t,e,n){return t>=.5?n:e}function F(t,e,n){return 0===t||e===n?e:t*(n-e)+e}function C(t,e,n){var r=F(t,e,n);return r<=0?0:r}function D(t,e,n){var r=F(t,e,n);return r<=0?0:r>=1?1:r}function L(t,e,n){return 0===t?e:1===t?n:{x:F(t,e.x,n.x),y:F(t,e.y,n.y)}}function V(t,e,n){return 0===t?e:1===t?n:{x:C(t,e.x,n.x),y:C(t,e.y,n.y)}}function q(t,e,n){var r=function(t,e,n){return Math.round(F(t,e,n))}(t,e,n);return r<=0?0:r>=255?255:r}function G(t,e,n){return 0===t?e:1===t?n:{r:q(t,e.r,n.r),g:q(t,e.g,n.g),b:q(t,e.b,n.b),a:F(t,null==e.a?1:e.a,null==n.a?1:n.a)}}function z(t,e,n){var r=e.length;if(r!==n.length)return N(t,e,n);for(var i=new Array(r),o=0;o<r;o++)i[o]=F(t,e[o],n[o]);return i}function Y(t,e){for(var n=[],r=0;r<t;r++)n.push(e);return n}function U(t,e){if(--e<=0)return t;var n=(t=Object.assign([],t)).length;do{for(var r=0;r<n;r++)t.push(t[r])}while(--e>0);return t}var W,$=function(){function t(e){r(this,t),this.list=e,this.length=e.length}return o(t,[{key:"setAttribute",value:function(t,e){for(var n=this.list,r=0;r<this.length;r++)n[r].setAttribute(t,e)}},{key:"removeAttribute",value:function(t){for(var e=this.list,n=0;n<this.length;n++)e[n].removeAttribute(t)}},{key:"style",value:function(t,e){for(var n=this.list,r=0;r<this.length;r++)n[r].style[t]=e}}]),t}(),H=/-./g,Q=function(t,e){return e.toUpperCase()};function X(t){return"function"==typeof t?t:N}function J(t){return t?"function"==typeof t?t:Array.isArray(t)?function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:b;if(!Array.isArray(t))return e;switch(t.length){case 1:return x(t[0])||e;case 2:return x(t[0],t[1])||e;case 4:return k(t[0],t[1],t[2],t[3])||e}return e}(t,null):function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:b;switch(t){case"linear":return b;case"steps":return x(e.steps||1,e.jump||0)||n;case"bezier":case"cubic-bezier":return k(e.x1||0,e.y1||0,e.x2||0,e.y2||0)||n}return n}(t.type,t.value,null):null}function K(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=e.length-1;if(t<=e[0].t)return r?[0,0,e[0].v]:e[0].v;if(t>=e[i].t)return r?[i,1,e[i].v]:e[i].v;var o,u=e[0],a=null;for(o=1;o<=i;o++){if(!(t>e[o].t)){a=e[o];break}u=e[o]}return null==a?r?[i,1,e[i].v]:e[i].v:u.t===a.t?r?[o,1,a.v]:a.v:(t=(t-u.t)/(a.t-u.t),u.e&&(t=u.e(t)),r?[o,t,n(t,u.v,a.v)]:n(t,u.v,a.v))}function Z(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return t&&t.length?"function"!=typeof e?null:("function"!=typeof n&&(n=null),function(r){var i=K(r,t,e);return null!=i&&n&&(i=n(i)),i}):null}function tt(t,e){return t.t-e.t}function et(t,e,r,i,o){var u,a="@"===r[0],s="#"===r[0],l=W[r],f=N;switch(a?(u=r.substr(1),r=u.replace(H,Q)):s&&(r=r.substr(1)),n(l)){case"function":if(f=l(i,o,K,J,r,a,e,t),s)return f;break;case"string":f=Z(i,X(l));break;case"object":if((f=Z(i,X(l.i),l.f))&&"function"==typeof l.u)return l.u(e,f,r,a,t)}return f?function(t,e,n){if(arguments.length>3&&void 0!==arguments[3]&&arguments[3])return t instanceof $?function(r){return t.style(e,n(r))}:function(r){return t.style[e]=n(r)};if(Array.isArray(e)){var r=e.length;return function(i){var o=n(i);if(null==o)for(var u=0;u<r;u++)t[u].removeAttribute(e);else for(var a=0;a<r;a++)t[a].setAttribute(e,o)}}return function(r){var i=n(r);null==i?t.removeAttribute(e):t.setAttribute(e,i)}}(e,r,f,a):null}function nt(t,e,r,i){if(!i||"object"!==n(i))return null;var o=null,u=null;return Array.isArray(i)?u=function(t){if(!t||!t.length)return null;for(var e=0;e<t.length;e++)t[e].e&&(t[e].e=J(t[e].e));return t.sort(tt)}(i):(u=i.keys,o=i.data||null),u?et(t,e,r,u,o):null}function rt(t,e,n){if(!n)return null;var r=[];for(var i in n)if(n.hasOwnProperty(i)){var o=nt(t,e,i,n[i]);o&&r.push(o)}return r.length?r:null}function it(t,e){if(!e.settings.duration||e.settings.duration<0)return null;var n,r,i,o,u,a=function(t,e){if(!e)return null;var n=[];if(Array.isArray(e))for(var r=e.length,i=0;i<r;i++){var o=e[i];if(2===o.length){var u=null;if("string"==typeof o[0])u=t.getElementById(o[0]);else if(Array.isArray(o[0])){u=[];for(var a=0;a<o[0].length;a++)if("string"==typeof o[0][a]){var s=t.getElementById(o[0][a]);s&&u.push(s)}u=u.length?1===u.length?u[0]:new $(u):null}if(u){var l=rt(t,u,o[1]);l&&(n=n.concat(l))}}}else for(var f in e)if(e.hasOwnProperty(f)){var c=t.getElementById(f);if(c){var h=rt(t,c,e[f]);h&&(n=n.concat(h))}}return n.length?n:null}(t,e.elements);return a?(n=a,r=e.settings,i=r.duration,o=n.length,u=null,function(t,e){var a=r.iterations||1/0,s=(r.alternate&&a%2==0)^r.direction>0?i:0,l=t%i,f=1+(t-l)/i;e*=r.direction,r.alternate&&f%2==0&&(e=-e);var c=!1;if(f>a)l=s,c=!0,-1===r.fill&&(l=r.direction>0?0:i);else if(e<0&&(l=i-l),l===u)return!1;u=l;for(var h=0;h<o;h++)n[h](l);return c}):null}function ot(t,e){if(W=e,!t||!t.root||!Array.isArray(t.animations))return null;var n=function(t){for(var e=document.getElementsByTagName("svg"),n=0;n<e.length;n++)if(e[n].id===t.root&&!e[n].svgatorAnimation)return e[n].svgatorAnimation=!0,e[n];return null}(t);if(!n)return null;var r=t.animations.map((function(t){return it(n,t)})).filter((function(t){return!!t}));return r.length?{svg:n,animations:r,animationSettings:t.animationSettings,options:t.options||void 0}:null}function ut(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"undefined"!=typeof BigInt&&BigInt,i="0x"+(t.replace(/[^0-9a-fA-F]+/g,"")||27);return e&&r&&n.isSafeInteger&&!n.isSafeInteger(+i)?n(r(i))%e+e:+i}function at(t,e,n){return!t||!n||e>t.length?t:t.substring(0,e)+at(t.substring(e+1),n,n)}function st(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:27;return!t||t%e?t%e:[0,1].includes(e)?e:st(t/e,e)}function lt(t,e,n){if(t&&t.length){var r=ut(n),i=st(r)+5,o=at(t,st(r,5),i);return o=o.replace(/\x7c$/g,"==").replace(/\x2f$/g,"="),o=function(t,e,n){var r=+("0x"+t.substring(0,4));t=t.substring(4);for(var i=ut(e,r)%r+n%27,o=[],u=0;u<t.length;u+=2)if("|"!==t[u]){var a=+("0x"+t[u]+t[u+1])-i;o.push(a)}else{var s=+("0x"+t.substring(u+1,u+1+4))-i;u+=3,o.push(s)}return String.fromCharCode.apply(String,o)}(o=(o=atob(o)).replace(/[\x41-\x5A]/g,""),e,r),o=JSON.parse(o)}}var ft=[{key:"alternate",def:!1},{key:"fill",def:1},{key:"iterations",def:0},{key:"direction",def:1},{key:"speed",def:1},{key:"fps",def:100}],ct=function(){function t(e,n){var i=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;r(this,t),this._id=0,this._running=!1,this._rollingBack=!1,this._animations=e,this._settings=n,(!o||o<"2022-05-02")&&delete this._settings.speed,ft.forEach((function(t){i._settings[t.key]=i._settings[t.key]||t.def})),this.duration=n.duration,this.offset=n.offset||0,this.rollbackStartOffset=0}return o(t,[{key:"alternate",get:function(){return this._settings.alternate}},{key:"fill",get:function(){return this._settings.fill}},{key:"iterations",get:function(){return this._settings.iterations}},{key:"direction",get:function(){return this._settings.direction}},{key:"speed",get:function(){return this._settings.speed}},{key:"fps",get:function(){return this._settings.fps}},{key:"maxFiniteDuration",get:function(){return this.iterations>0?this.iterations*this.duration:this.duration}},{key:"_apply",value:function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this._animations,r=n.length,i=0,o=0;o<r;o++)e[o]?i++:(e[o]=n[o](t,1),e[o]&&i++);return i}},{key:"_rollback",value:function(t){var e=this,n=1/0,r=null;this.rollbackStartOffset=t,this._rollingBack=!0,this._running=!0;this._id=window.requestAnimationFrame((function i(o){if(e._rollingBack){null==r&&(r=o);var u=Math.round(t-(o-r)*e.speed);if(u>e.duration&&n!==1/0){var a=!!e.alternate&&u/e.duration%2>1,s=u%e.duration;u=(s+=a?e.duration:0)||e.duration}var l=(e.fps?1e3/e.fps:0)*e.speed,f=Math.max(0,u);f<=n-l&&(e.offset=f,n=f,e._apply(f));var c=e.iterations>0&&-1===e.fill&&u>=e.maxFiniteDuration;(u<=0||e.offset<u||c)&&e.stop(),e._id=window.requestAnimationFrame(i)}}))}},{key:"_start",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=-1/0,r=null,i={};this._running=!0;var o=function o(u){null==r&&(r=u);var a=Math.round((u-r)*t.speed+e),s=(t.fps?1e3/t.fps:0)*t.speed;if(a>=n+s&&!t._rollingBack&&(t.offset=a,n=a,t._apply(a,i)===t._animations.length))return void t.pause(!0);t._id=window.requestAnimationFrame(o)};this._id=window.requestAnimationFrame(o)}},{key:"_pause",value:function(){this._id&&window.cancelAnimationFrame(this._id),this._running=!1}},{key:"play",value:function(){if(!this._running)return this._rollingBack?this._rollback(this.offset):this._start(this.offset)}},{key:"stop",value:function(){this._pause(),this.offset=0,this.rollbackStartOffset=0,this._rollingBack=!1,this._apply(0)}},{key:"reachedToEnd",value:function(){return this.iterations>0&&this.offset>=this.iterations*this.duration}},{key:"restart",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.stop(t),this.play(t)}},{key:"pause",value:function(){this._pause()}},{key:"reverse",value:function(){this.direction=-this.direction}}],[{key:"build",value:function(t,e){delete t.animationSettings,t.options=lt(t.options,t.root,"91c80d77"),t.animations.map((function(e){e.settings=lt(e.s,t.root,"91c80d77"),delete e.s,t.animationSettings||(t.animationSettings=e.settings)}));var n=t.version;if(!(t=ot(t,e)))return null;var r=t.options||{},i=new this(t.animations,t.animationSettings,n);return{el:t.svg,options:r,player:i}}},{key:"push",value:function(t){return this.build(t)}},{key:"init",value:function(){var t=this,e=window.__SVGATOR_PLAYER__&&window.__SVGATOR_PLAYER__["91c80d77"];Array.isArray(e)&&e.splice(0).forEach((function(e){return t.build(e)}))}}]),t}();!function(){for(var t=0,e=["ms","moz","webkit","o"],n=0;n<e.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[e[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e[n]+"CancelAnimationFrame"]||window[e[n]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(e){var n=Date.now(),r=Math.max(0,16-(n-t)),i=window.setTimeout((function(){e(n+r)}),r);return t=n+r,i},window.cancelAnimationFrame=window.clearTimeout)}();var ht=function(){function t(e,n,i){r(this,t);var o=function(t){var e,n,r,i=t&&1===(null===(e=t.ownerDocument)||void 0===e||null===(n=e.childNodes)||void 0===n?void 0:n.length)&&window.parent!==window,o={el:t,window:window};if(!i)return o;try{r=window.parent.document}catch(t){return o}return o.window=window.parent,o.el=Array.from(r.querySelectorAll("iframe,object")).filter((function(t){return t.contentWindow===window}))[0]||o.el,o}(e);n=Math.max(1,n||1),n=Math.min(n,100),this.el=o.el,this._handlers=[],this.onThresholdChange=i&&i.call?i:function(){},this.thresholdPercent=n||1,this.currentVisibility=null,this.visibilityCalculator=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n,r=!1,i=null,o=function(){for(var o=0,u=n.innerHeight,a=0,s=n.innerWidth,l=t.parentNode;l instanceof Element;){var f=n.getComputedStyle(l);if("visible"!==f.overflowY||"visible"!==f.overflowX){var c=l.getBoundingClientRect();"visible"!==f.overflowY&&(o=Math.max(o,c.top),u=Math.min(u,c.bottom)),"visible"!==f.overflowX&&(a=Math.max(a,c.left),s=Math.min(s,c.right))}if(l===l.parentNode)break;l=l.parentNode}r=!1;var h=t.getBoundingClientRect(),v=Math.min(h.height,Math.max(0,o-h.top)),d=Math.min(h.height,Math.max(0,h.bottom-u)),y=Math.min(h.width,Math.max(0,a-h.left)),g=Math.min(h.width,Math.max(0,h.right-s)),p=(h.height-v-d)/h.height,m=(h.width-y-g)/h.width,b=Math.round(p*m*100);null!==i&&i===b||(i=b,e(b))};return function(t){r&&clearTimeout(r),r=setTimeout((function(){return o()}),100)}}(this.el,this.onVisibilityUpdate.bind(this),o.window),this.bindScrollWatchers(),this.visibilityCalculator()}return o(t,[{key:"bindScrollWatchers",value:function(){for(var t=this.el.parentNode;t&&(this._handlers.push({element:t,event:"scroll",handler:this.visibilityCalculator}),t.addEventListener("scroll",this.visibilityCalculator),t!==t.parentNode&&t!==document);)t=t.parentNode}},{key:"onVisibilityUpdate",value:function(t){var e=this.currentVisibility>=this.thresholdPercent,n=t>=this.thresholdPercent;if(null===this.currentVisibility||e!==n)return this.currentVisibility=t,void this.onThresholdChange(n);this.currentVisibility=t}},{key:"destruct",value:function(){this._handlers.forEach((function(t){t.element.removeEventListener(t.event,t.handler)}))}}]),t}();function vt(t){return g(t)+""}function dt(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t&&t.length?t.map(vt).join(e):""}function yt(t){return vt(t.x)+","+vt(t.y)}function gt(t){if(!t)return"transparent";if(null==t.a||t.a>=1){var e=function(t){return 1===(t=parseInt(t).toString(16)).length?"0"+t:t},n=function(t){return t.charAt(0)===t.charAt(1)},r=e(t.r),i=e(t.g),o=e(t.b);return n(r)&&n(i)&&n(o)&&(r=r.charAt(0),i=i.charAt(0),o=o.charAt(0)),"#"+r+i+o}return"rgba("+t.r+","+t.g+","+t.b+","+t.a+")"}function pt(t){return t?"url(#"+t+")":"none"}var mt={f:null,i:V,u:function(t,e){return function(n){var r=e(n);t.setAttribute("rx",vt(r.x)),t.setAttribute("ry",vt(r.y))}}},bt={f:null,i:function(t,e,n){return 0===t?e:1===t?n:{width:C(t,e.width,n.width),height:C(t,e.height,n.height)}},u:function(t,e){return function(n){var r=e(n);t.setAttribute("width",vt(r.width)),t.setAttribute("height",vt(r.height))}}};Object.freeze({M:2,L:2,Z:0,H:1,V:1,C:6,Q:4,T:2,S:4,A:7});var wt={},kt=null;function _t(t){var e=function(){if(kt)return kt;if("object"!==("undefined"==typeof document?"undefined":n(document))||!document.createElementNS)return{};var t=document.createElementNS("http://www.w3.org/2000/svg","svg");return t&&t.style?(t.style.position="absolute",t.style.opacity="0.01",t.style.zIndex="-9999",t.style.left="-9999px",t.style.width="1px",t.style.height="1px",kt={svg:t}):{}}().svg;if(!e)return function(t){return null};var r=document.createElementNS(e.namespaceURI,"path");r.setAttributeNS(null,"d",t),r.setAttributeNS(null,"fill","none"),r.setAttributeNS(null,"stroke","none"),e.appendChild(r);var i=r.getTotalLength();return function(t){var e=r.getPointAtLength(i*t);return{x:e.x,y:e.y}}}function At(t){return wt[t]?wt[t]:wt[t]=_t(t)}function xt(t,e,n,r){if(!t||!r)return!1;var i=["M",t.x,t.y];if(e&&n&&(i.push("C"),i.push(e.x),i.push(e.y),i.push(n.x),i.push(n.y)),e?!n:n){var o=e||n;i.push("Q"),i.push(o.x),i.push(o.y)}return e||n||i.push("L"),i.push(r.x),i.push(r.y),i.join(" ")}function Ot(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=xt(t,e,n,r),u=At(o);try{return u(i)}catch(t){return null}}function St(t,e,n){return t+(e-t)*n}function Et(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i={x:St(t.x,e.x,n),y:St(t.y,e.y,n)};return r&&(i.a=Pt(t,e)),i}function Pt(t,e){return Math.atan2(e.y-t.y,e.x-t.x)}function jt(t,e,n,r){var i=1-r;return i*i*t+2*i*r*e+r*r*n}function Mt(t,e,n,r){return 2*(1-r)*(e-t)+2*r*(n-e)}function Bt(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=Ot(t,e,null,n,r);return o||(o={x:jt(t.x,e.x,n.x,r),y:jt(t.y,e.y,n.y,r)}),i&&(o.a=It(t,e,n,r)),o}function It(t,e,n,r){return Math.atan2(Mt(t.y,e.y,n.y,r),Mt(t.x,e.x,n.x,r))}function Tt(t,e,n,r,i){var o=i*i;return i*o*(r-t+3*(e-n))+3*o*(t+n-2*e)+3*i*(e-t)+t}function Rt(t,e,n,r,i){var o=1-i;return 3*(o*o*(e-t)+2*o*i*(n-e)+i*i*(r-n))}function Nt(t,e,n,r,i){var o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],u=Ot(t,e,n,r,i);return u||(u={x:Tt(t.x,e.x,n.x,r.x,i),y:Tt(t.y,e.y,n.y,r.y,i)}),o&&(u.a=Ft(t,e,n,r,i)),u}function Ft(t,e,n,r,i){return Math.atan2(Rt(t.y,e.y,n.y,r.y,i),Rt(t.x,e.x,n.x,r.x,i))}function Ct(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Lt(e)){if(Vt(n))return Bt(e,n.start,n,t,r)}else if(Lt(n)){if(qt(e))return Bt(e,e.end,n,t,r)}else{if(qt(e))return Vt(n)?Nt(e,e.end,n.start,n,t,r):Bt(e,e.end,n,t,r);if(Vt(n))return Bt(e,n.start,n,t,r)}return Et(e,n,t,r)}function Dt(t,e,n){var r=Ct(t,e,n,!0);return r.a=function(t){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?t+Math.PI:t}(r.a)/m,r}function Lt(t){return!t.type||"corner"===t.type}function Vt(t){return null!=t.start&&!Lt(t)}function qt(t){return null!=t.end&&!Lt(t)}var Gt=new R;var zt={f:function(t){return t?t.join(" "):""},i:function(t,e,r){if(0===t)return e;if(1===t)return r;var i=e.length;if(i!==r.length)return N(t,e,r);for(var o,u=new Array(i),a=0;a<i;a++){if((o=n(e[a]))!==n(r[a]))return N(t,e,r);if("number"===o)u[a]=F(t,e[a],r[a]);else{if(e[a]!==r[a])return N(t,e,r);u[a]=e[a]}}return u}},Yt={f:null,i:z,u:function(t,e){return function(n){var r=e(n);t.setAttribute("x1",vt(r[0])),t.setAttribute("y1",vt(r[1])),t.setAttribute("x2",vt(r[2])),t.setAttribute("y2",vt(r[3]))}}},Ut={f:vt,i:F},Wt={f:vt,i:D},$t={f:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t&&t.length>0&&(t=t.map((function(t){return g(t,4)}))),dt(t,e)},i:function(t,e,n){var r,i,o,u=e.length,a=n.length;if(u!==a)if(0===u)e=Y(u=a,0);else if(0===a)a=u,n=Y(u,0);else{var s=(o=(r=u)*(i=a)/function(t,e){for(var n;e;)n=e,e=t%e,t=n;return t||1}(r,i))<0?-o:o;e=U(e,Math.floor(s/u)),n=U(n,Math.floor(s/a)),u=a=s}for(var l=[],f=0;f<u;f++)l.push(g(C(t,e[f],n[f])));return l}};function Ht(t,e,n){return t.map((function(t){return function(t,e,n){var r=t.v;if(!r||"g"!==r.t||r.s||!r.v||!r.r)return t;var i=n.getElementById(r.r),o=i&&i.querySelectorAll("stop")||[];return r.s=r.v.map((function(t,e){var n=o[e]&&o[e].getAttribute("offset");return{c:t,o:n=g(parseInt(n)/100)}})),delete r.v,t}(t,0,n)}))}var Qt={gt:"gradientTransform",c:{x:"cx",y:"cy"},rd:"r",f:{x:"x1",y:"y1"},to:{x:"x2",y:"y2"}};function Xt(t,e,r,i,o,u,a,s){return Ht(t,0,s),e=function(t,e,n){for(var r,i,o,u=t.length-1,a={},s=0;s<=u;s++)(r=t[s]).e&&(r.e=e(r.e)),r.v&&"g"===(i=r.v).t&&i.r&&(o=n.getElementById(i.r))&&(a[i.r]={e:o,s:o.querySelectorAll("stop")});return a}(t,i,s),function(i){var o=r(i,t,Jt);if(!o)return"none";if("c"===o.t)return gt(o.v);if("g"===o.t){if(!e[o.r])return pt(o.r);var u=e[o.r];return function(t,e){for(var n=t.s,r=n.length;r<e.length;r++){var i=n[n.length-1].cloneNode();i.id=te(i.id),t.e.appendChild(i),n=t.s=t.e.querySelectorAll("stop")}for(var o=0,u=n.length,a=e.length-1;o<u;o++)n[o].setAttribute("stop-color",gt(e[Math.min(o,a)].c)),n[o].setAttribute("offset",e[Math.min(o,a)].o)}(u,o.s),Object.keys(Qt).forEach((function(t){if(void 0!==o[t])if("object"!==n(Qt[t])){var e,r="gt"===t?(e=o[t],Array.isArray(e)?"matrix("+e.join(" ")+")":""):o[t],i=Qt[t];u.e.setAttribute(i,r)}else Object.keys(Qt[t]).forEach((function(e){if(void 0!==o[t][e]){var n=o[t][e],r=Qt[t][e];u.e.setAttribute(r,n)}}))})),pt(o.r)}return"none"}}function Jt(t,n,r){if(0===t)return n;if(1===t)return r;if(n&&r){var i=n.t;if(i===r.t)switch(n.t){case"c":return{t:i,v:G(t,n.v,r.v)};case"g":if(n.r===r.r){var o={t:i,s:Kt(t,n.s,r.s),r:n.r};return n.gt&&r.gt&&(o.gt=z(t,n.gt,r.gt)),n.c?(o.c=L(t,n.c,r.c),o.rd=C(t,n.rd,r.rd)):n.f&&(o.f=L(t,n.f,r.f),o.to=L(t,n.to,r.to)),o}}if("c"===n.t&&"g"===r.t||"c"===r.t&&"g"===n.t){var u="c"===n.t?n:r,a="g"===n.t?e({},n):e({},r),s=a.s.map((function(t){return{c:u.v,o:t.o}}));return a.s="c"===n.t?Kt(t,s,a.s):Kt(t,a.s,s),a}}return N(t,n,r)}function Kt(t,e,n){if(e.length===n.length)return e.map((function(e,r){return Zt(t,e,n[r])}));for(var r=Math.max(e.length,n.length),i=[],o=0;o<r;o++){var u=Zt(t,e[Math.min(o,e.length-1)],n[Math.min(o,n.length-1)]);i.push(u)}return i}function Zt(t,e,n){return{o:D(t,e.o,n.o||0),c:G(t,e.c,n.c||{})}}function te(t){return t.replace(/-fill-([0-9]+)$/,(function(t,e){return"-fill-"+(+e+1)}))}var ee={blur:V,brightness:C,contrast:C,"drop-shadow":function(t,e,n){return 0===t?e:1===t?n:{blur:V(t,e.blur,n.blur),offset:L(t,e.offset,n.offset),color:G(t,e.color,n.color)}},grayscale:C,"hue-rotate":F,invert:C,opacity:C,saturate:C,sepia:C};function ne(t,e,n){if(0===t)return e;if(1===t)return n;var r=e.length;if(r!==n.length)return N(t,e,n);for(var i,o=[],u=0;u<r;u++){if(e[u].type!==n[u].type)return e;if(!(i=ee[e[u].type]))return N(t,e,n);o.push({type:e.type,value:i(t,e[u].value,n[u].value)})}return o}var re={blur:function(t){return t?function(e){t.setAttribute("stdDeviation",yt(e))}:null},brightness:function(t,e,n){return(t=oe(n,e))?function(e){e=vt(e),t.map((function(t){return t.setAttribute("slope",e)}))}:null},contrast:function(t,e,n){return(t=oe(n,e))?function(e){var n=vt((1-e)/2);e=vt(e),t.map((function(t){t.setAttribute("slope",e),t.setAttribute("intercept",n)}))}:null},"drop-shadow":function(t,e,n){var r=n.getElementById(e+"-blur");if(!r)return null;var i=n.getElementById(e+"-offset");if(!i)return null;var o=n.getElementById(e+"-flood");return o?function(t){r.setAttribute("stdDeviation",yt(t.blur)),i.setAttribute("dx",vt(t.offset.x)),i.setAttribute("dy",vt(t.offset.y)),o.setAttribute("flood-color",gt(t.color))}:null},grayscale:function(t){return t?function(e){t.setAttribute("values",dt(function(t){return[.2126+.7874*(t=1-t),.7152-.7152*t,.0722-.0722*t,0,0,.2126-.2126*t,.7152+.2848*t,.0722-.0722*t,0,0,.2126-.2126*t,.7152-.7152*t,.0722+.9278*t,0,0,0,0,0,1,0]}(e)))}:null},"hue-rotate":function(t){return t?function(e){return t.setAttribute("values",vt(e))}:null},invert:function(t,e,n){return(t=oe(n,e))?function(e){e=vt(e)+" "+vt(1-e),t.map((function(t){return t.setAttribute("tableValues",e)}))}:null},opacity:function(t,e,n){return(t=n.getElementById(e+"-A"))?function(e){return t.setAttribute("tableValues","0 "+vt(e))}:null},saturate:function(t){return t?function(e){return t.setAttribute("values",vt(e))}:null},sepia:function(t){return t?function(e){return t.setAttribute("values",dt(function(t){return[.393+.607*(t=1-t),.769-.769*t,.189-.189*t,0,0,.349-.349*t,.686+.314*t,.168-.168*t,0,0,.272-.272*t,.534-.534*t,.131+.869*t,0,0,0,0,0,1,0]}(e)))}:null}};var ie=["R","G","B"];function oe(t,e){var n=ie.map((function(n){return t.getElementById(e+"-"+n)||null}));return-1!==n.indexOf(null)?null:n}var ue={fill:Xt,"fill-opacity":Wt,stroke:Xt,"stroke-opacity":Wt,"stroke-width":Ut,"stroke-dashoffset":{f:vt,i:F},"stroke-dasharray":$t,opacity:Wt,transform:function(t,e,r,i){if(!(t=function(t,e){if(!t||"object"!==n(t))return null;var r=!1;for(var i in t)t.hasOwnProperty(i)&&(t[i]&&t[i].length?(t[i].forEach((function(t){t.e&&(t.e=e(t.e))})),r=!0):delete t[i]);return r?t:null}(t,i)))return null;var o=function(n,i,o){var u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return t[n]?r(i,t[n],o):e&&e[n]?e[n]:u};return e&&e.a&&t.o?function(e){var n=r(e,t.o,Dt);return Gt.recomposeSelf(n,o("r",e,F,0)+n.a,o("k",e,L),o("s",e,L),o("t",e,L)).toString()}:function(t){return Gt.recomposeSelf(o("o",t,Ct,null),o("r",t,F,0),o("k",t,L),o("s",t,L),o("t",t,L)).toString()}},"#filter":function(t,e,n,r,i,o,u,a){if(!e.items||!t||!t.length)return null;var s=function(t,e){var n=(t=t.map((function(t){return t&&re[t[0]]?(e.getElementById(t[1]),re[t[0]](e.getElementById(t[1]),t[1],e)):null}))).length;return function(e){for(var r=0;r<n;r++)t[r]&&t[r](e[r].value)}}(e.items,a);return s?(t=function(t,e){return t.map((function(t){return t.e=e(t.e),t}))}(t,r),function(e){s(n(e,t,ne))}):null},"#line":Yt,points:{f:dt,i:z},d:zt,r:Ut,"#size":bt,"#radius":mt,_:function(t,e){if(Array.isArray(t))for(var n=0;n<t.length;n++)this[t[n]]=e;else this[t]=e}},ae={currentTime:"offset",duration:"duration",hasEnded:function(){return this.reachedToEnd()},isAlternate:"alternate",isPlaying:"_running",isRollingBack:"_rollingBack",state:function(t,e){return e.isPlaying?e.isRollingBack?"rollback":"playing":e.hasEnded?"ended":"paused"},totalTime:"maxFiniteDuration",iterations:"iterations",direction:"direction",fill:"fill",isReversed:function(t,e){return-1===e.direction},isBackwards:function(t,e){return-1===e.fill},isInfinite:function(t,e){return 0===e.iterations},speed:"speed",fps:"fps"},se={destruct:"destruct",pause:"pause",play:function(t,e){return le(t,e.hasEnded?"restart":"play",e)},restart:"restart",reverse:function(t,e){return le(t,"reverse",e,[!0])},seek:"seek",seekBy:"seekBy",seekTo:"seekTo",stop:"stop",toggle:"toggle",togglePlay:"toggle",set:"set"};function le(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];return function(){var i,o=Array.prototype.slice.call(arguments);return o.unshift.apply(o,v(r)),(i=t[e]).call.apply(i,[t].concat(v(o))),n}}var fe=function t(e){r(this,t);var n={},i=["on","off"],o={get:function(t,e,r){return ae[e]?"function"==typeof ae[e]?ae[e].call(t,t,r):t[ae[e]]:se[e]?"function"==typeof se[e]?se[e].call(t,t,r):le(t,se[e],r):-1!==i.indexOf(e)?n[e]:"ready"===e?function(t){return t&&t.call(r,r),r}:void 0},set:function(t,e,r){return-1!==i.indexOf(e)&&(n[e]=r)},ownKeys:function(t){return Object.keys(ae)},has:function(t,e){return void 0!==ae[e]}};if("function"==typeof Proxy)return new Proxy(e,o);var u=Object.keys(ae).concat(Object.keys(se)).concat(i),a={};return u.forEach((function(t){var n={enumerable:!1,configurable:!1,get:function(){return o.get(e,t,a)}};-1!==i.indexOf(t)&&(n.set=function(n){return o.set(e,t,n)}),Object.defineProperty(a,t,n)})),a};function ce(t){t||(t=this);var e={};this.on=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return"function"==typeof n&&(t.split(/[, ]+/g).forEach((function(t){return e[t]=e[t]||[],r?e[t].unshift(n):e[t].push(n)})),!0)},this.off=function(t,n){for(var r in e)if(e.hasOwnProperty(r)&&r.substr(0,t.length)===t)if(n)for(var i=0;i<e[r].length;i++)e[r][i]===n&&(e[r][i]=null);else e[r]=null},this.trigger=function(){var n,r=Array.prototype.slice.call(arguments),i=r[0],o=r.slice(1);t:for(var u in e)if(e.hasOwnProperty(u)&&e[u]&&(u===i||u.substr(0,i.length+1)===i+"."))for(var a=0;a<(e[u]||[]).length;a++)if(e[u][a]&&!1===(n=e[u][a].apply(t,o)))break t;return n}}var he=!0,ve=["iterations","speed","fps","direction","fill","alternate"],de=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&s(t,e)}(u,t);var e,n,i=(e=u,n=l(),function(){var t,r=a(e);if(n){var i=a(this).constructor;t=Reflect.construct(r,arguments,i)}else t=r.apply(this,arguments);return c(this,t)});function u(t,e){var n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return r(this,u),(n=i.call(this,t,e,o))._handlers=[],n}return o(u,[{key:"_adjustOffset",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.alternate?2*this.duration:this.duration;if(t){if(!this._rollingBack&&0===this.offset)return void(this.offset=e);this._rollingBack&&(this.offset,this.maxFiniteDuration)}!this._rollingBack||this.rollbackStartOffset<=this.duration?0!==this.iterations&&(this.offset=Math.min(this.offset,this.maxFiniteDuration)):(this.offset=this.rollbackStartOffset-(this.rollbackStartOffset-this.offset)%e,this.rollbackStartOffset=0)}},{key:"reverse",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this._running)return this._adjustOffset(t),this._rollingBack=!this._rollingBack,t&&this.play(!1),void this.trigger("reverse",this.offset);this.pause(!1,!1),this._adjustOffset(),this._rollingBack=!this._rollingBack,this.play(!1),this.trigger("reverse",this.offset)}},{key:"play",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:he,e=h(a(u.prototype),"play",this).call(this);return t===he&&this.trigger("play",this.offset),e}},{key:"pause",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:he,n=h(a(u.prototype),"pause",this).call(this);return e===he&&this.trigger(t?"end":"pause",this.offset),n}},{key:"restart",value:function(){var t=h(a(u.prototype),"restart",this).call(this,!1);return this.trigger("restart",this.offset),t}},{key:"stop",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:he,e=h(a(u.prototype),"stop",this).call(this);return t===he&&this.trigger("stop",this.offset),e}},{key:"_apply",value:function(t){var e=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:he,r=h(a(u.prototype),"_apply",this).call(this,t);if(n===he){var i=function(){return e.trigger("keyframe",t)};window.requestAnimationFrame(i)}return r}},{key:"seekTo",value:function(t){var e,n,r,i=this._running;i&&this.pause(!1,!1),this.offset=this.iterations>0?(e=t,n=0,r=this.maxFiniteDuration,e<n?n:e>r?r:e):Math.max(t,0),this._apply(this.offset),i&&this.play(!1)}},{key:"seek",value:function(t){return this.seekTo(Math.round(t/100*this.maxFiniteDuration))}},{key:"seekBy",value:function(t){return this.seekTo(this.offset+t)}},{key:"toggle",value:function(){return this._running?this.pause():this.reachedToEnd()?this.restart():this.play()}},{key:"set",value:function(t,e){if(ve.includes(t)){var n=this._running;n&&this.pause(!1,!1),this._settings[t]=e,n?this.play(!1):this._apply(this.offset,{},!1)}}},{key:"destruct",value:function(){var t=this;this.stop(),this._handlers.forEach((function(t){t.element?t.element.removeEventListener(t.event,t.handler):t.callback&&t.callback.call&&t.callback.call()}));var e=function(){},n=Object.getOwnPropertyNames(Object.getPrototypeOf(this));n.push.apply(n,v(Object.getOwnPropertyNames(this))),n.forEach((function(n){"function"==typeof t[n]?t[n]=e:delete t[n]}))}}],[{key:"build",value:function(t){var e=h(a(u),"build",this).call(this,t,ue);if(!e)return null;var n=e.el,r=e.options,i=e.player,o=new fe(i),s=new ce(o);o.on=s.on,o.off=s.off,i.trigger=s.trigger;var l=n.svgatorPlayer&&n.svgatorPlayer.ready&&n.svgatorPlayer.ready.call&&n.svgatorPlayer.ready.call();n.svgatorPlayer=o,function(t,e,n){if("click"===n.start){var r=function(){switch(n.click){case"freeze":return!t._running&&t.reachedToEnd()?t.restart():t.toggle();case"restart":return t.offset>0?t.restart():t.play();case"reverse":return t._running?t.reverse():t.reachedToEnd()?1===t.fill?t.reverse(!0):t.restart():t.play();case"none":default:if(t._running)return;return t.reachedToEnd()?t.restart():t.play()}};return t._handlers.push({element:e,event:"click",handler:r}),void e.addEventListener("click",r)}if("hover"===n.start){var i=function(){return t.reachedToEnd()?t.restart():t._rollingBack?t.reverse():t.play()};t._handlers.push({element:e,event:"mouseenter",handler:i}),e.addEventListener("mouseenter",i);var o=function(){switch(n.hover){case"freeze":return t.pause();case"reset":return t.stop();case"reverse":if(t.reverse(),t._running)return;return t.play();case"none":default:return}};return t._handlers.push({element:e,event:"mouseleave",handler:o}),void e.addEventListener("mouseleave",o)}if("scroll"===n.start){var u=new ht(e,n.scroll||25,(function(e){e?t.reachedToEnd()?t.restart():t.play():t.pause()}));return void t._handlers.push({callback:function(){return u.destruct()}})}if("programmatic"===n.start)return;t.play()}(i,n,r),function(t,e,n){var r;"function"==typeof Event?r=new Event("ready"):(r=document.createEvent("Event")).initEvent("ready",!0,!0);if(t.dispatchEvent(r),!n||!n.length)return;n.forEach((function(t){return e.ready(t)}))}(n,n.svgatorPlayer,l)}}]),u}(ct);return de.init(),de}));
(function(s,i,o,w,d,a,b){(a=Array.from(d.querySelectorAll('svg#' + i.root)).filter(n=> !n.svgatorPlayer)[0]||{}).svgatorPlayer={ready:(function(a){b=[];return function(c){return c?(b.push(c),a.svgatorPlayer):b}})(a)};w[o]=w[o]||{};w[o][s]=w[o][s]||[];w[o][s].push(i);})('91c80d77',{"root":"ePtumNLyfYq1","version":"2022-05-04","animations":[{"elements":{"ePtumNLyfYq6":{"opacity":[{"t":600,"v":1},{"t":1100,"v":0},{"t":1600,"v":1}]},"ePtumNLyfYq7":{"opacity":[{"t":400,"v":1},{"t":900,"v":0},{"t":1400,"v":1}]},"ePtumNLyfYq8":{"opacity":[{"t":200,"v":1},{"t":700,"v":0},{"t":1200,"v":1}]},"ePtumNLyfYq9":{"opacity":[{"t":0,"v":1},{"t":500,"v":0},{"t":1000,"v":1}]},"ePtumNLyfYq10":{"opacity":[{"t":1400,"v":1},{"t":1900,"v":0},{"t":2400,"v":1}]},"ePtumNLyfYq11":{"opacity":[{"t":1200,"v":1},{"t":1700,"v":0},{"t":2200,"v":1}]},"ePtumNLyfYq12":{"opacity":[{"t":1000,"v":1},{"t":1500,"v":0},{"t":2000,"v":1}]},"ePtumNLyfYq13":{"opacity":[{"t":800,"v":1},{"t":1300,"v":0},{"t":1800,"v":1}]},"ePtumNLyfYq14":{"opacity":[{"t":2000,"v":1},{"t":2500,"v":0},{"t":3000,"v":1}]},"ePtumNLyfYq15":{"opacity":[{"t":1800,"v":1},{"t":2300,"v":0},{"t":2800,"v":1}]},"ePtumNLyfYq16":{"opacity":[{"t":1600,"v":1},{"t":2100,"v":0},{"t":2600,"v":1}]},"ePtumNLyfYq17":{"opacity":[{"t":1400,"v":1},{"t":1900,"v":0},{"t":2400,"v":1}]},"ePtumNLyfYq38":{"opacity":[{"t":1800,"v":0,"e":[0.42,0,0.58,1]},{"t":2100,"v":1,"e":[0.42,0,0.58,1]},{"t":2400,"v":0}]},"ePtumNLyfYq40":{"opacity":[{"t":1200,"v":0,"e":[0.42,0,0.58,1]},{"t":1500,"v":1,"e":[0.42,0,0.58,1]},{"t":1800,"v":0}]},"ePtumNLyfYq42":{"opacity":[{"t":600,"v":0,"e":[0.42,0,0.58,1]},{"t":900,"v":1,"e":[0.42,0,0.58,1]},{"t":1200,"v":0}]},"ePtumNLyfYq44":{"opacity":[{"t":0,"v":0,"e":[0.42,0,0.58,1]},{"t":300,"v":1,"e":[0.42,0,0.58,1]},{"t":600,"v":0}]},"ePtumNLyfYq47":{"transform":{"data":{"o":{"x":370.400009,"y":317.5,"type":"corner"},"t":{"x":-370.400009,"y":-317.5}},"keys":{"r":[{"t":0,"v":0},{"t":3000,"v":360}]}}},"ePtumNLyfYq60":{"stroke-dashoffset":[{"t":0,"v":24.94,"e":[0.86,0,0.07,1]},{"t":500,"v":0,"e":[0.86,0,0.07,1]},{"t":1000,"v":-24.94}]},"ePtumNLyfYq61":{"stroke-dashoffset":[{"t":1000,"v":24.94,"e":[0.445,0.05,0.55,0.95]},{"t":1500,"v":0,"e":[0.445,0.05,0.55,0.95]},{"t":2000,"v":-24.94}]},"ePtumNLyfYq62":{"stroke-dashoffset":[{"t":2000,"v":24.94,"e":[0.445,0.05,0.55,0.95]},{"t":2500,"v":0,"e":[0.445,0.05,0.55,0.95]},{"t":3000,"v":-24.94}]}},"s":"MDTA1ZDhkMzQ3NDjg3ODRTNzM4QNjdiODE4MDMY0NGM0NTQyNDMI0MjNlMzQ3NYjdiODRDNzc3FNTg2N2I4MTgGwMzRXNGM0MzENlMzQ3Yjg2NRzc4NDczODY3JYjgxODA4NTMJ0NGNFNDIzZTNM0Sjc4N2I3ZLTdlMzQ0Y0Q0BMzNlMzQ3M0ME3ZTg2Nzc4NDTgwNzM4Njc3MNzQ0Y0c3ODczGN2U4NTc3M2UVzNDg1ODI3NzYc3NzZGMzQ0YOzQzM2UzNDc4BODI4NTM0NGMY0MzQySTQyOGHY/"}],"options":"MDTAxMDgyMjk3YLTdiNjg3OTdiOMjk0MTI5NzMG3NjY4NmIyOUTY4NA|"},'__SVGATOR_PLAYER__',window,document)
]]></script>
</svg>
