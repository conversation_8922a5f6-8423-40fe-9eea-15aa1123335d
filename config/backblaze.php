<?php
return [
    'adminGroupID' => env('BACKBLAZE_ADMIN_GROUP_ID'),
    'accountId' => env('BACKBLAZE_ACCOUNT_ID'),
    'keyID' => env('BACKBLAZE_KEY_ID'),
    'applicationKey' => env('BACKBLAZE_APPLICATION_KEY'),

    'reportKEYID' => env('BACKBLAZE_REPORT_KEY_ID'),
    'reportApplicationKey' => env('BACKBLAZE_REPORT_APPLICATION_KEY'),
    'reportBucketEndpoint' => env('BACKBLAZE_REPORT_BUCKET_ENDPOINT'),
    'reportBucketID' => env('BACKBLAZE_REPORT_BUCKET_ID'),
    'reportBucketName' => env('BACKBLAZE_REPORT_BUCKET_NAME'),
    'reportEndpoint' => env('BACKBLAZE_REPORT_ENDPOINT'),
    'reportRegion' => env('BACKBLAZE_REPORT_REGION'),
];
