<?php

use App\Enums\WhiteLabel\SubdomainSources;
use App\Models\BillingPlan;
use App\Models\FeaturePermission;
use App\Models\Option;
use App\Models\Team;
use App\Models\User;
use App\Models\WhiteLabel;
use Carbon\Carbon;
use Illuminate\Support\Str;
use League\ISO3166\ISO3166;

const XCLOUD = 'xcloud';

/**
 * Get a hashid.
 *
 * @param  int  $value
 * @return string
 */
function hashid_encode(int $value, $minLength = 36): string
{
    $hashids = new Hashids\Hashids(config('app.key'), $minLength);

    return $hashids->encode($value);
}

/**
 * Decode a hashid.
 *
 * @param  string  $value
 * @return int
 */
function hashid_decode(string $value, $minLength = 36): int
{
    $hashids = new Hashids\Hashids(config('app.key'), $minLength);

    return $hashids->decode($value)[0] ?: 0;
}

function shortPlainUUID($length = 8) : string
{
    if ($length > 32) {
        throw new \InvalidArgumentException('Length of UUID cannot be greater than 32');
    }

    $startFrom = [0, 8, 16];

    if ($length > 8) {
        $startFrom = [0];
    }

    return substr(str_replace('-', '', Str::uuid()->toString()), $startFrom[array_rand($startFrom)], $length);
}


function callback_url($url)
{
    if (!app()->isProduction()) {
        app('url')->forceRootUrl(config('app.url'));
        if (str(config('app.url'))->contains(['ngrok', 'expose'])){
            app('url')->forceScheme('https');
        }
    }

    // \App\Http\Middleware\TrustProxies::$proxies, is already trusitng localhost.
    // so not necessary to manually force setup root I guess

    return url($url);
}


function file_get_json($url)
{
    return json_decode(file_get_contents($url), true);
}

function timezone_list(): array
{
    $zones_array = array();
    $timestamp = time();
    foreach (timezone_identifiers_list() as $key => $zone) {
        date_default_timezone_set($zone);
        $zones_array[$key]['zone'] = $zone;
        $zones_array[$key]['diff_from_GMT'] = 'UTC/GMT '.date('P', $timestamp);
    }
    return $zones_array;
}

/** @return Team */
function team()
{
    $team = user()?->currentTeam;
    if (!$team && user()) {
        if (user()->ownedTeams()->count() === 0) {
            $team = getTeam(user());
        } else {
            $team = user()->ownedTeams()->first();
        }
        user()->switchTeam($team);
    }
    return $team;
}

function getTeam(User $user) {
    $billingPlanToAssign = BillingPlan::where('is_default', true)->first();
    $team = $user->ownedTeams()->save(Team::forceCreate([
        'user_id' => $user->id,
        'email' => $user->email,
        'name' => explode(' ', $user->name, 2)[0] . "'s Team",
        'personal_team' => true,
//            'active_plan' => $billingPlanToAssign?->name,
        'active_plan_id' => $billingPlanToAssign?->id
    ]));

    //Set user current team
    if (!$user->current_team_id) {
        $user->update(['current_team_id' => $team->id]);
    }
    return $team;
}


/** @return User */
function user()
{
    return auth()->user();
}

function title($text = '')
{
    return ucwords(str_replace(['_', '-'], ' ', $text));
}

function _title($text = '')
{
    // Split the string by underscores
    $parts = explode('_', $text);

    $newParts = [];

    foreach ($parts as $part) {
        if (preg_match('/[A-Z]/', $part)) {
            $newParts[] = $part;
        } else {
            $newParts[] = ucfirst($part);
        }
    }

    // Join the parts back together with a space
    return implode(' ', $newParts);
}


//function _title($text = '')
//{
//    // Split the string by underscores
//    $parts = explode('_', $text);
//
//    // If there are multiple parts, capitalize the first letter of the second part
//    if (count($parts) > 1) {
//        $parts[1] = ucfirst($parts[1]);
//    }
//
//    // Join the parts back together with a space
//    return implode(' ', $parts);
//}


function get_domain($url)
{
    $parsed_url = parse_url($url);

    if (!($parsed_url['host'] ?? null)) {
        return without_http($url);
    }

    return $parsed_url['host'] ?? null;
}

function without_http($url)
{
    return preg_replace('#^https?://#', '', $url);
}

function insert_http($url, $scheme = 'http')
{
    if (!preg_match("~^(?:f|ht)tps?://~i", $url)) {
        $url = $scheme . "://" . $url;
    }
    return $url;
}

function get_region($name)
{
    static $json;
    static $regions;

    if (is_null($json)){
        $json = file_get_contents(base_path('resources/json/regionWithCountryCode.json'));
        $regions = json_decode($json, true);
    }

    // Match the name without the last three characters
    $name_without_suffix = substr($name ?: '', 0, -2);

    $region = $regions[$name] ?? $regions[$name_without_suffix] ?? null;

    if ($region) {
        $region['region'] = $name;

        return $region;
    }

    return [
        'region' => $name,
        'region-code' => 'unknown',
        'region-name' => 'unknown',
        'country' => 'unknown',
        'country_code' => 'unknown'
    ];
}

function getCountriesWithFlags()
{
    $json = file_get_contents(base_path('resources/json/countriesWithFlagAndCountryCode.json'));
    return json_decode($json, true);
}

function get_location($name): string
{
    return get_region($name)['region-name'] ?? get_region($name)['city'] ?? $name;
}

function format_bytes($bytes, $precision = 2)
{
    if (!$bytes) {
        return;
    }

    $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB');

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    // Uncomment one of the following alternatives
    $bytes /= pow(1024, $pow);
    // $bytes /= (1 << (10 * $pow));

    return round($bytes, $precision).' '.$units[$pow];
}

function mb_to_gb($mb): float|int
{
    return $mb / 1024;
}


function format_billing(float $price, $decimals = 2) : float
{
    return format_number($price, $decimals);
}

function format_number(float $price, $decimals = 2) : float
{
    return number_format($price, $decimals, '.', '');
}

function formatTitle($string) : string
{
    if (containsCamelCases($string)) {
        return addSpacesToCamelCase($string);
    }

    if (allAreUpperCases($string)) {
        return $string;
    }

    if (allAreUpperCasesButContainsUnderscore($string)) {
        return str_replace('_', ' ', $string);
    }

    return title($string);
}

function containsCamelCases($string): bool
{
    return preg_match('/[a-z]\K[A-Z]/', $string) === 1;
}

function addSpacesToCamelCase($inputString) : string
{
    if (ctype_upper($inputString)) return $inputString;

    return ucwords(Str::snake($inputString, ' '));
}

function allAreUpperCases($inputString) : bool
{
    return ctype_upper($inputString);
}

function allAreUpperCasesButContainsUnderscore($inputString) : bool
{
    if (str_contains($inputString, '_')) {
        return ctype_upper((implode('', explode('_', $inputString))));
    }
    return false;
}

function extractSubdomainAndSiteNameFromDomain($url): array
{
    $parsedUrl = parse_url($url);

    $protocol = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] : '';
    $host = isset($parsedUrl['host']) ? $parsedUrl['host'] : '';

    // Split host by dot
    $parts = explode('.', $host);

    // Get TLD
    $tld = array_pop($parts);

    // Get the subdomain part
    $subdomainParts = array_slice($parts, 0, -1); // Remove the last part which is TLD
    $subdomainPart = implode('.', $subdomainParts);

    // Construct site name
    $siteName = implode('.', array_slice($parts, -1));

    return [
        'protocol' => $protocol,
        'subdomainPart' => $subdomainPart,
        'siteName' => $siteName . '.' . $tld,
        'host' => $host,
        'tld' => $tld,
    ];
}

function removeHttp($url) {
    $parsedUrl = parse_url($url);

    // Remove the scheme (http:// or https://)
    unset($parsedUrl['scheme']);

    return build_url($parsedUrl);
}

function removeSubdomain($domain) {
    $parsedUrl = parse_url($domain);

    if (isset($parsedUrl['host'])) {
        $hostParts = explode('.', $parsedUrl['host']);
        // Check if there are more than one part in the host, indicating the presence of a subdomain
        if (count($hostParts) > 2) {
            // Remove the first part, which is the subdomain
            array_shift($hostParts);
            $parsedUrl['host'] = implode('.', $hostParts);
        }
    }

    return build_url($parsedUrl);
}

function build_url($parsedUrl) {
    $scheme = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] . '://' : '';
    $user = isset($parsedUrl['user']) ? $parsedUrl['user'] : '';
    $pass = isset($parsedUrl['pass']) ? ':' . $parsedUrl['pass'] : '';
    $pass = ($user || $pass) ? "$pass@" : '';
    $host = isset($parsedUrl['host']) ? $parsedUrl['host'] : '';
    $port = isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '';
    $path = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
    $query = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
    $fragment = isset($parsedUrl['fragment']) ? '#' . $parsedUrl['fragment'] : '';

    return "$scheme$user$pass$host$port$path$query$fragment";
}

function canGenerateSslCertificateOnCloudflare($domain): bool
{
    $parts = explode('.', $domain);

    // Count the number of parts
    $numParts = count($parts);

    // Example Usage
//    $domain1 = 'ventmachine.com'; // valid
//    $domain2 = 'faisal.ventmachine.com'; // valid
//    $domain3 = 'faisal.dev.ventmachine.com'; // invalid
//    $domain3 = 'faisal.dev.coder.ventmachine.com'; // invalid

    // Cloudflare supports primary domain and one-level subdomains
    // A valid domain for SSL should have 2 parts (primary domain) or 3 parts (single-level subdomain)
    return $numParts === 2 || $numParts === 3;
}
function randomLowerString(int $length = 10,?string $append=null): string
{
    $string = substr(str_shuffle(str_repeat($x='abcdefghijklmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    if ($append) {
        $string .= $append;
    }
    return $string;
}


function getCountryOrContinentFullName($code)
{
    if (Str::length($code) > 2) {
        throw new \InvalidArgumentException(
            'Supported country or continent code is of length 2. '. $code .' - '. Str::length($code) . ' length given'
        );
    }

    $continentMapping = [
        'AF' => 'Africa',
        'AN' => 'Antarctica',
        'AS' => 'Asia',
        'EU' => 'Europe',
        'NA' => 'North America',
        'OC' => 'Oceania',
        'SA' => 'South America',
        'US' => 'Americas',
        'AP' => 'Asia Pacific'
    ];

    $code = strtoupper($code);

    // ISO 3166 library for country code lookup
    $iso = new ISO3166();

    if (isset($continentMapping[$code])) {
        return $continentMapping[$code];
    }

    try {
        $country = $iso->alpha2($code);

        return $country['name'];

    } catch (\OutOfBoundsException $e) {

        return 'Country or continent code not found';
    }
}

function add_backslash_before_last_dot($string) {
    $lastDotPosition = strrpos($string, '.');
    if ($lastDotPosition !== false) {
        $string = substr($string, 0, $lastDotPosition) . '\\' . substr($string, $lastDotPosition);
    }
    return $string;
}

function _backtrace($layer = 0) : string
{
    $layer = $layer + 1;

    $backtrace = debug_backtrace() ?? '';

    if (is_array($backtrace) && count($backtrace) >= $layer) {

        $latestCall = $backtrace[$layer];

        if (isset($latestCall['file'])) {
            $data['call_trace'] = $latestCall['file'] . ':' . $latestCall['line'] . ':' . $latestCall['function'] . '()';
        } else {
            $data['call_trace'] = $latestCall['function'] . '()';
        }
    }

    return $data['call_trace'] ?? '';
}


function set_option(string $key, $value = null): Option
{
    return Option::set($key, $value);
}

function get_option($key)
{
    return Option::get($key);
}

function remove_option($key)
{
    return Option::remove($key);
}

use Illuminate\Http\Request;

function addQueryArg($url, $key, $value) {
    $request = Request::create($url);
    return $request->fullUrlWithQuery([$key => $value]);
}

function subdomainExists($url): array
{
    // Parse the domain from the URL
    $parsedUrl = parse_url($url, PHP_URL_HOST);

    $appDomain = 'xcloud.test'; // TODO: Remove this line(@Faisal)

    // $appDomain = env('APP_URL');

    // Check if it's a subdomain
    if (str_ends_with($parsedUrl, $appDomain)) {
        // Get the full domain (subdomain.domain)
        $subdomain = str_replace('.' . $appDomain, '', $parsedUrl);

        // If the subdomain is empty, it's the main domain
        if (empty($subdomain) || $subdomain === $appDomain) {
            return ['isSubdomain' => false, 'domain' => $appDomain];
        }

        return ['isSubdomain' => true, 'subdomain' => $subdomain, 'domain' => $parsedUrl];
    }

    return ['isSubdomain' => false, 'domain' => $appDomain];
}

function remove_http($url)
{
    $disallowed = ['http://', 'https://'];
    foreach ($disallowed as $d) {
        if (strpos($url, $d) === 0) {
            return str_replace($d, '', $url);
        }
    }
    return $url;
}

function is_url($url)
{
    return filter_var($url, FILTER_VALIDATE_URL);
}

function get_request_host()
{
    $host = request()->getHost();

    if (isset($_SERVER['HTTP_X_ORIGINAL_HOST'])) {
        $host = $_SERVER['HTTP_X_ORIGINAL_HOST'];
    }

    return $host;
}
function request_subdomain($host = null): ?string
{
    if (is_null($host)) {
        $host = get_request_host();
    }

    $request_domain = clean_domain($host);

    // remove the app domain from the request domain
    // $subdomain = str_replace(clean_domain(config('app.url')), '', $request_domain); // no need for this
    $subdomain = str_replace(clean_domain(SubdomainSources::getActiveDomain()), '', $request_domain);

    $subdomain = is_url($subdomain) ? $subdomain : explode('.', $subdomain);

    return is_array($subdomain) ? $subdomain[0] ?? null : $subdomain;
}

function clean_domain($domain): string
{
    return remove_www(remove_http($domain));
}


function getReservedDomains(): array
{
    if (auth()->check() && auth()->user()->isAdmin()) {
        return [
            str_repeat('a', 63) . '.com', // 63 characters
        ]; // Admins can use any domain, adding a dummy domain to prevent any issues
    }

    return ['wp1.site', 'wp1.sh', 'xcloud.name', 'xcloud.host', 'x-cloud.app'];
}

function hasReservedDomain($domain): bool
{
    return collect(getReservedDomains())->contains(fn($reserved) => Str::endsWith($domain, $reserved));
}

function remove_www($url)
{
    return preg_replace('#^(http(s)?://)?w{3}\.#', '$1', $url);
}


function inject_www(string $url)
{
    return inject_subdomain($url, 'www');
}

function has_subdomain($url): bool
{
    return count(explode('.', parse_url($url)['host'])) > 2;
}

/**
 * @param $url
 * @param $subdomain
 * @return array|mixed|string|string[]
 * @desc Smartly handle www
 */
function inject_subdomain($url, $subdomain): mixed
{
    if (empty($subdomain)) {
        return $url;
    }

    strstr($url, 'www') ?
        $url_parts = explode('://www', $url) :
        $url_parts = explode('://', $url);

    $explode_domain_parts = explode('.', $url);

    $url_with_subdomain = isset($explode_domain_parts[0]) &&
    ($explode_domain_parts[0] !== $subdomain) ?
        (explode('.', $url)[0] == 'www' ?
            str_replace('www', $subdomain, $url) :
            $subdomain . '.' . $url) : $url;

    $dot = isset($url_parts[1]) && str_contains($url_parts[1], '.') && empty(explode('.', $url_parts[1])[0]) ? null : '.';

    return isset($url_parts[0]) && isset($url_parts[1]) ? $url_parts[0] . '://' . $subdomain . $dot . $url_parts[1] : $url_with_subdomain;
}

function remove_subdomain($host_with_subdomain): string
{
    $array = explode(".", $host_with_subdomain);

    return (array_key_exists(count($array) - 2, $array) ? $array[count($array) - 2] : "") . "." . $array[count($array) - 1];
}

/**
 * @return WhiteLabel|null
 */
function currentWhiteLabel(): ?WhiteLabel
{
    return app()->has('currentWhiteLabel') ? app('currentWhiteLabel') : null;
}

if (!function_exists('isWhiteLabel')) {
    function isWhiteLabel(): bool
    {
        return currentWhiteLabel() !== null;
    }
}


if (!function_exists('expandPath')) {
    /**
     * Convert a path with ~ to an absolute path.
     *
     * @param string $path
     * @return string
     */
    function expandPath(string $path): string
    {
        // Check if the path starts with '~' (home directory shortcut)
        if (str_starts_with($path, '~')) {
            // Replace '~' with the user's home directory path
            $path = str_replace('~', '/root' ?: '', $path);
        }
        // Return the absolute path
        return $path;
    }
}



function isFeatureAllowed($feature_key)
{
    $whiteLabel = currentWhiteLabel();

    if (!$whiteLabel) {
        return true; // Feature is available for xCloud by default
    }

    // Check if the feature is enabled in the database
    return FeaturePermission::where('feature_key', $feature_key)
        ->value('is_enabled_for_whitelabel') ?? false;
}

function extractJsonFromString(string $string, ?string $key = null, ?string $default = null): null|array|string
{
    try {
        if (preg_match('/\{.*\}/', $string, $matches)) {
            $json = json_decode($matches[0], true);
            return $key ? ($json[$key] ?? $default) : $json;
        }
        return null;
    }catch (\Exception $e){
        return null;
    }
}
