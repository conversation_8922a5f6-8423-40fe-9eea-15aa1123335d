# FROM gitpod/workspace-full
FROM gitpod/workspace-mysql

USER gitpod

RUN \
# Update
sudo apt-get update -y && \
# Install Unzip
sudo apt-get install unzip -y && \
# need wget
sudo apt-get install wget -y && \
# vim
sudo apt-get install vim -y

################################
# Install Terraform
################################

# Download terraform for linux
RUN wget https://releases.hashicorp.com/terraform/1.2.6/terraform_1.2.6_linux_amd64.zip

# Unzip
RUN unzip terraform_1.2.6_linux_amd64.zip

# Move to local bin
RUN sudo mv terraform /usr/local/bin/
# Check that it's installed
RUN terraform --version 

RUN sudo install-packages php-xdebug