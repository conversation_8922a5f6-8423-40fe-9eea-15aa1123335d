<?php

namespace Database\Seeders;

use App\Enums\EmailProvider;
use App\Enums\ProductProvider;
use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Models\Package;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $vultr_packages = collect(config('services.xvultr.supported_machines'));
        $products = [];
        foreach ($vultr_packages as  $type => $packages){
            foreach ($packages as $package){
                $products[] = [...Arr::except($package,['tag','tooltip']),
                               'type'=>$type,
                               'service_type'=>BillingServices::xCloudManagedHosting->value,
                               'is_active'=>true,
                               'show_on_display'=>true,
                               'renewal_type'=>'monthly',
                               'currency'=>BillingCurrency::USD->value,
                ];
            }
        }
        Product::insert($products);

        $whiteLabelProducts = [];

        foreach ($vultr_packages as  $type => $packages){
            foreach ($packages as $package){
                $whiteLabelProducts[] = [...Arr::except($package,['tag','tooltip']),
                   'title' => 'WH - '. $package['title'],
                   'type' => $type,
                   'service_type' => BillingServices::ManagedHosting->value,
                   'source_model' => \App\Models\Server::class,
                   'is_active' => true,
                   'show_on_display' => false,
                   'slug' => $package['slug'],
                   'sku' => str_replace('vc', 'wh', $package['slug']),
                   'available_for_white_label' => true,
                   'provider' => ProductProvider::Vultr,
                   'renewal_type' => BillRenewalPeriod::Monthly->value,
                   'currency' => BillingCurrency::USD->value,
                ];
            }
        }

        Product::insert($whiteLabelProducts);

        $packages = collect(config('services.white_label_subscription'));

        foreach ($packages as $package) {
            Package::create($package);
        }

        $vultr_provider_packages = collect(config('services.xvultr_provider.supported_machines'));
        $products = [];
        foreach ($vultr_provider_packages as  $type => $packages){
            foreach ($packages as $package){
                $products[] = [...Arr::except($package,['tag','tooltip']),
                               'type'=>$type,
                               'service_type'=>BillingServices::xCloudProviderHosting->value,
                               'is_active'=>true,
                               'show_on_display'=>true,
                               'renewal_type'=>'monthly',
                               'currency'=>BillingCurrency::USD->value,
                ];
            }
        }
        Product::insert($products);

        // Email product seeder
        $emailProducts = [];

        foreach(xCloudEmailProviderPlanEnum::asValueLabel() as $key => $value){
            $emailProducts[] = [
                'title' => $key === xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS->value ? 'xcloud 100 emails(Free)' : Arr::get(xCloudEmailProviderPlanEnum::getReadablePlanName(), $key),
                'type' => 'custom',
                'slug' => $key,
                'sku' => xCloudEmailProviderPlanEnum::getSku()[$key],
                'source_model' => \App\Models\EmailProvider::class,
                'provider' => ProductProvider::ElasticEmail,
                'description' => $key === xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS->value ? 'xcloud 100 emails(Free)' : Arr::get(xCloudEmailProviderPlanEnum::getReadablePlanName(), $key),
                'is_active' => true,
                'renewal_type' => BillRenewalPeriod::Monthly->value,
                'service_type' => BillingServices::EmailProvider->value,
                'price' => xCloudEmailProviderPlanEnum::getPlanPrice($key),
                'max_purchase_limit' => 1
            ];
        }
        Product::insert($emailProducts);

        Product::all()->each(function ($product) {
            $backupService = null;
            if ($product->service_type === BillingServices::xCloudManagedHosting) {
                $backupService = BillingServices::BackupXCloudManagedHosting;
            } elseif ($product->service_type === BillingServices::xCloudProviderHosting) {
                $backupService = BillingServices::BackupXCloudProviderHosting;
            } elseif ($product->service_type === BillingServices::ManagedHosting) {
                $backupService = BillingServices::BackupManagedHosting;
            }

            if ($backupService) {
                $clonedProduct = $product->replicate(["id", "created_at", "updated_at"]);

                $clonedProduct->title =
                    $product->title . " - " . $backupService->toReadableString();
                $clonedProduct->service_type = $backupService;
                $clonedProduct->price = $product->price * 0.2;

                $clonedProduct->save();
            }
        });
    }
}
