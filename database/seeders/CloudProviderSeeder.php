<?php

namespace Database\Seeders;

use App\Enums\CloudProviderEnums;
use App\Enums\CloudProviderEnums as EnumsCloudProvider;
use App\Models\CloudProvider;
use Illuminate\Database\Seeder;

class CloudProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        CloudProvider::factory()->create([
            'provider' => EnumsCloudProvider::XCLOUD->value,
            'user_id' => 1,
            'name' => 'default xcloud'
        ]);

        CloudProvider::factory()->create([
            'provider' => EnumsCloudProvider::XCLOUD_VULTR->value,
            'user_id' => 1,
            'name' => 'default xcloud vultr'
        ]);
        CloudProvider::factory()->create([
            'provider' => CloudProviderEnums::VULTR->value,
            'team_id' => 1,
            'name' => 'default vultr',
            'user_id' => 1
        ]);

        CloudProvider::factory()->create([
            'provider' => CloudProviderEnums::DIGITALOCEAN->value,
            'team_id' => 1,
            'name' => 'default digitalocean',
            'user_id' => 1
        ]);
        CloudProvider::factory()->create([
            'provider' => CloudProviderEnums::GCP->value,
            'team_id' => 1,
            'name' => 'default gcp',
            'user_id' => 1
        ]);
        CloudProvider::factory()->create([
            'provider' => CloudProviderEnums::HETZNER->value,
            'team_id' => 1,
            'name' => 'default hetzner',
            'user_id' => 1
        ]);
    }
}
