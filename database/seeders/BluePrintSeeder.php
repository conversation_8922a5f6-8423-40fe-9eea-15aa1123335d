<?php

namespace Database\Seeders;

use App\Models\BluePrint;
use App\Models\Team;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class BluePrintSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run($team=null): void
    {
        try {
            $blueprints = $this->file_get_json();
            if($team){
                $this->insertBluePrint($team,$blueprints);
            }else{
                $this->insertBluePrints($blueprints);
            }
        } catch (\Exception $e) {
           Log::error($e->getMessage());
        }
    }

    private function insertBluePrints($blueprints): void
    {
        Team::whereDoesntHave('bluePrints')->chunk(100, function ($teams) use ($blueprints){
            foreach ($teams as $team) {
                if (!$team->getMeta('has_default_blueprints')) {
                    echo "Seeding blueprints for team: {$team->name}\n";
                    $this->insertBluePrint($team, $blueprints);
                    $team->saveMeta('has_default_blueprints', true);
                }else{
                    echo "Team: {$team->name} already has blueprints\n";
                }
            }
        });

    }

    private function insertBluePrint($team, $blueprints): void
    {
        $blueprints = array_map(function ($blueprint) use ($team) {
            return array_merge($blueprint, [
                'team_id' => $team->id,
                'theme' => json_encode($blueprint['theme']),
                'plugins' => json_encode($blueprint['plugins']),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }, $blueprints);
        BluePrint::insert($blueprints);
    }

    /**
     * @throws \Exception
     */
    private function file_get_json()
    {
        if (!file_exists(resource_path('json/blueprints.json'))) {
            throw new \Exception('Blueprints file not found');
        }
        $blueprintJson = resource_path('json/blueprints.json');
        return file_get_json($blueprintJson);
    }
}
