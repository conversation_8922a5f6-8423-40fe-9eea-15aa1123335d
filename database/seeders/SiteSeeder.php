<?php

namespace Database\Seeders;

use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use Illuminate\Database\Seeder;

class SiteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $site = file_get_json(resource_path('json/site-1.json'));
        $ssl = file_get_json(resource_path('json/ssl-1.json'));
        $sslStaging = file_get_json(resource_path('json/ssl-staging-lets-encrypt.json'));

        // $staging = file_get_json(resource_path('json/site-staging.json'));

        $tags = [
            'production',
            'staging',
            'test',
            'development',
            'local',
        ];

        $new_site = new Site();
        $new_site->forceFill($site)->save();
        $new_site->syncTags($tags);

        // $new_staging = new Site();
        // $new_staging->forceFill($staging)->save();
        // $new_staging->syncTags($tags);

        Site::factory()->count(1)->for(
            Server::find(2) ?: Server::factory()
        )->create();

        Site::factory()->count(1)->for(
            Server::find(2) ?: Server::factory()
        )->create([
            'status' => 'new'
        ]);

        (new SslCertificate())->forceFill($ssl)->save();
        (new SslCertificate())->forceFill($sslStaging)->save();

        optional(Site::find(4))->syncTags($tags);
    }
}
