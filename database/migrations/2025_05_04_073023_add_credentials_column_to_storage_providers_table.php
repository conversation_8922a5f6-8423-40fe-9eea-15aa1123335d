<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('storage_providers', function (Blueprint $table) {
            if (!Schema::hasColumn('storage_providers', 'credentials')) {
                $table->text('credentials')->nullable()->after('secret_key');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('storage_providers', function (Blueprint $table) {
            if (Schema::hasColumn('storage_providers', 'credentials')) {
                $table->dropColumn('credentials');
            }
        });
    }
};
