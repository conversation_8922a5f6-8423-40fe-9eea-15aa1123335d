<?php

use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('addon_storage_providers', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Team::class)->constrained()->cascadeOnDelete();
            $table->string('type')->index();   #for example, s3, ftp, etc. now only s3 is supported
            $table->string('provider')->index();  #for example, backblaze, aws, etc. now only backblaze is supported
            $table->string('bucket_name');  #for example, b2-backup for user label
            $table->string('bucket_id')->nullable(); #for example, s3 bucket id
            $table->string('provider_bucket')->nullable(); # provider end bucket name because backblaze bucket name should be unique
            $table->string('region')->nullable();
            $table->boolean('is_enabled')->default(false);
            $table->string('product_slug')->nullable();
            $table->foreignIdFor(\App\Models\Product::class);
            $table->foreignIdFor(\App\Models\BackblazeMember::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('addon_storage_providers');
    }
};
