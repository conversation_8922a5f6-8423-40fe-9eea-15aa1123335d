<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->unsignedBigInteger('fluent_crm_tag_id')->nullable()->index()->after('expire_at');
            $table->json('meta')->nullable()->after('show_on_display');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->dropIndex('packages_fluent_crm_tag_id_index');
            $table->dropColumn('fluent_crm_tag_id');
            $table->dropColumn('meta');
        });
    }
};
