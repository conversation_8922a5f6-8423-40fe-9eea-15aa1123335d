<?php

use App\Enums\XcloudBilling\StripeProductSubscriptionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscription_product_team', function (Blueprint $table) {
            $table->id();
            $table->string('status')->default(StripeProductSubscriptionStatus::Active->value);
            $table->foreignId('subscription_product_id')->constrained()->onDelete('cascade');
            $table->foreignId('team_id')->constrained()->onDelete('cascade');
            $table->string('stripe_subscription_id')->nullable()->index();
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('ends_at')->index()->nullable();
            $table->timestamps();

            $table->index(['subscription_product_id', 'team_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscription_product_team');
    }
};
