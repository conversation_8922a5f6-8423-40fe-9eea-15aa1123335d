<?php

use App\Models\Addons\MailboxDomain;
use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_aliases', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(MailboxDomain::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Team::class)->constrained()->cascadeOnDelete();

            $table->string('name', 100);
            $table->string('alias_email', 100)->index();
            $table->json('destinations');
            $table->string('alias_code')->index();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_aliases');
    }
};
