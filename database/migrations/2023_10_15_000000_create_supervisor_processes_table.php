<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supervisor_processes', function (Blueprint $table) {
            $table->id();
            $table->string('command');
            $table->string('directory')->nullable();
            $table->string('user');
            $table->integer('numprocs');
            $table->integer('startsecs')->nullable();
            $table->integer('stopsecs')->nullable();
            $table->string('stopsignal')->nullable();
            $table->string('status')->default('new');
            $table->text('log')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('site_id')->nullable();
            $table->unsignedBigInteger('server_id');
            $table->timestamps();

            $table->index('server_id');
            $table->index('site_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('supervisor_processes');
    }
};
