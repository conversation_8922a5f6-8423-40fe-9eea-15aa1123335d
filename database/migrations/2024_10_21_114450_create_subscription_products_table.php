<?php

use App\Enums\XcloudBilling\BillingCurrency;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscription_products', function (Blueprint $table) {
            $table->id();
            $table->string('name')->index();
            $table->string('short_description')->nullable();
            $table->string('currency')->index()->default(BillingCurrency::USD->value)->index();
            $table->float('actual_price')->nullable()->unsigned()->index();
            $table->float('price')->unsigned()->index();
            $table->boolean('is_active')->default(true)->index();
            $table->boolean('show_on_display')->default(true)->index();
            $table->boolean('sync_with_stripe')->default(true)->index();
            $table->string('stripe_product_id', 100)->nullable()->index();
            $table->string('stripe_price_id', 100)->nullable()->index();
            $table->string('service_type')->index();
            $table->string('service_model', 100)->nullable()->index();
            $table->integer('unit')->unsigned()->default(1)->index();
            $table->string('renewal_period')->index();
            $table->text('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscription_products');
    }
};
