<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('storage_providers', function (Blueprint $table) {
            // Add foreign key to addon_storage_providers table
            $table->foreignId('addon_storage_provider_id')
                ->after('team_id')
                ->index()
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('storage_providers', function (Blueprint $table) {

            // Then drop the column
            $table->dropColumn('addon_storage_provider_id');
        });
    }
};
