<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('cloudflare_integrations', function (Blueprint $table) {
            $table->text('global_api_key')->change();
            $table->text('global_ca_key')->change();
        });
    }

    public function down(): void
    {
        Schema::table('cloudflare_integrations', function (Blueprint $table) {
            $table->string('global_api_key')->change();
            $table->string('global_ca_key')->change();
        });
    }
};
