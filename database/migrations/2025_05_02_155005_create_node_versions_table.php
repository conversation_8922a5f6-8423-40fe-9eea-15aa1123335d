<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('node_versions', function (Blueprint $table) {
            $table->id();
            $table->string('node_version', 15);
            $table->string('status', 30)->default('new');
            $table->foreignId('server_id')->constrained()->cascadeOnDelete();
            $table->longText('log')->nullable();
            $table->timestamp('node_version_updated_at')->nullable();
            $table->timestamps();

            $table->index('node_version');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('node_versions');
    }
};
