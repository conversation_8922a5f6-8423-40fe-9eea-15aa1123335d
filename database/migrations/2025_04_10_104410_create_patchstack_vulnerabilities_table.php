<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('patchstack_vulnerabilities', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Site::class)->constrained()->cascadeOnDelete();
            $table->string('patchstack_site_id')->nullable();
            $table->string('site_api_key')->nullable();
            $table->json('vulnerabilities')->nullable();
            $table->boolean('is_purchase')->default(false);
            $table->string('status')->comment("Connected, Not Connected")->default(\App\Enums\PatchstackVulnerabilityStatus::NOT_CONNECTED->value);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('patchstack_vulnerabilities');
    }
};
