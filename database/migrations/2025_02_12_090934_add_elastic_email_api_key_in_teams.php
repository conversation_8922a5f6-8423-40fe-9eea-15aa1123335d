<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->string('elastic_email_api_key')->nullable()->after('elastic_email_password');
        });

        \App\Models\Team::lazyById()->each(function (\App\Models\Team $team) {
            if (!$team->getElasticEmailSubAccountApiKey()) {
                return;
            }
            $team->update([
                'elastic_email_api_key' => $team->getElasticEmailSubAccountApiKey(),
            ]);
        });
    }

    public function down(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('elastic_email_api_key');
        });
    }
};
