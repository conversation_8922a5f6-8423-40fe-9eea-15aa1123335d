<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('backblaze_reports', function (Blueprint $table) {
            $table->id();
            $table->date('date')->index();
            $table->string('group_id')->nullable();
            $table->string('reporting_location')->nullable();
            $table->string('account_id');
            $table->string('account_email');
            $table->string('bucket_id')->index();
            $table->string('bucket_name')->nullable();
            $table->decimal('uploaded_gb', 12, 2)->default(0);
            $table->decimal('deleted_gb', 12, 2)->default(0);
            $table->decimal('downloaded_gb', 12, 2)->default(0);
            $table->bigInteger('downloaded_bytes')->default(0);
            $table->bigInteger('downloaded_favored_bytes')->default(0);
            $table->decimal('stored_gb', 12, 2)->default(0);
            $table->bigInteger('storage_byte_hours')->default(0);
            $table->integer('api_txn_class_a')->default(0);
            $table->integer('api_txn_class_b')->default(0);
            $table->integer('api_txn_class_c')->default(0);
            $table->timestamps();
            $table->unique(['bucket_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('backblaze_reports');
    }
};
