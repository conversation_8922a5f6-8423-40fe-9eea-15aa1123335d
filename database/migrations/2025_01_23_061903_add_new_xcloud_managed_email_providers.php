<?php

use App\Enums\ProductProvider;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Models\Product;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $newEmailProviders = [
            xCloudEmailProviderPlanEnum::XCLOUD_10000_EMAILS,
            xCloudEmailProviderPlanEnum::XCLOUD_20000_EMAILS,
            xCloudEmailProviderPlanEnum::XCLOUD_50000_EMAILS
        ];

        $emailProducts = [];
        foreach($newEmailProviders as $emailProvider){
            $emailProducts[] = [
                'title' => Arr::get(xCloudEmailProviderPlanEnum::getReadablePlanName(), $emailProvider->value),
                'type' => 'custom',
                'slug' => $emailProvider->value,
                'sku' => xCloudEmailProviderPlanEnum::getSku()[$emailProvider->value],
                'source_model' => \App\Models\EmailProvider::class,
                'provider' => ProductProvider::ElasticEmail,
                'description' => Arr::get(xCloudEmailProviderPlanEnum::getReadablePlanName(), $emailProvider->value),
                'is_active' => true,
                'renewal_type' => BillRenewalPeriod::Monthly->value,
                'service_type' => BillingServices::EmailProvider->value,
                'price' => xCloudEmailProviderPlanEnum::getPlanPrice($emailProvider->value),
                'max_purchase_limit' => 1
            ];
        }

        Product::insert($emailProducts);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
