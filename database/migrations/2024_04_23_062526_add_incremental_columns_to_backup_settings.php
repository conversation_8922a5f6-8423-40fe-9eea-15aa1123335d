<?php

use App\Enums\BackupType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('backup_settings', function (Blueprint $table) {
            $table->string('type')->index()->default(BackupType::FULL->value)->after('site_id');
            $table->boolean('auto_incremental_backup')->default(false)->after('auto_backup');
            $table->string('auto_incremental_frequency')->nullable()->after('auto_backup_frequency');
            $table->string('incremental_time')->nullable()->after('time');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('backup_settings', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('auto_incremental_backup');
            $table->dropColumn('auto_incremental_frequency');
            $table->dropColumn('incremental_time');
        });
    }
};
