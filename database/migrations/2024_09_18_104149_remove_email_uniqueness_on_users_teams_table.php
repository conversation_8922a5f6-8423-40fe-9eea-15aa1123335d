<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // check if the users_email_unique constraint exists before dropping it
            if (DB::select(DB::raw("SHOW INDEX FROM users WHERE Key_name = 'users_email_unique'"))) {
                $table->dropUnique('users_email_unique');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
//        Schema::table('users', function (Blueprint $table) {
//            if (!DB::select(DB::raw("SHOW INDEX FROM users WHERE Key_name = 'users_email_unique'"))) {
//                $table->unique('email');
//            }
//        });
    }
};
