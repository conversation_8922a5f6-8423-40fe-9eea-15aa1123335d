<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('ssl_certificates', function (Blueprint $table) {
            $table->timestamp('notification_sent_at')->nullable()->after('expires_at');
        });
    }

    public function down(): void
    {
        Schema::table('ssl_certificates', function (Blueprint $table) {
            $table->dropColumn('notification_sent_at');
        });
    }
};
