<?php

use App\Models\Addons\MailboxDomain;
use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_accounts', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(MailboxDomain::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Team::class)->constrained()->cascadeOnDelete();

            $table->string('email', 100)->unique()->index();
            $table->string('password');
            $table->string('email_account_code')->nullable()->index();
            $table->string('plan')->index();
            $table->string('status')->default('active')->index();
            $table->string('mailbox_size')->nullable();
            $table->json('forwarding_emails')->nullable();
            $table->json('email_settings')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_accounts');
    }
};
