<?php

namespace Database\Factories;

use App\Models\Team;
use App\Models\TeamInvitation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class TeamInvitationFactory extends Factory
{
    protected $model = TeamInvitation::class;

    public function definition(): array
    {
        return [
            'email' => $this->faker->unique()->safeEmail(),
            'role' => 'user',
            'permissions' => [],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'team_id' => Team::factory(),
        ];
    }
}
