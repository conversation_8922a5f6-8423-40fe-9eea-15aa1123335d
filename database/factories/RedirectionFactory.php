<?php

namespace Database\Factories;

use App\Models\Redirection;
use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class RedirectionFactory extends Factory
{
    protected $model = Redirection::class;

    public function definition(): array
    {
        return [
            'redirect_type' => 301,
            'from' => $this->faker->word(),
            'to' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'site_id' => Site::factory(),
        ];
    }
}
