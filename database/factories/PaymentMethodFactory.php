<?php

namespace Database\Factories;

use App\Enums\XcloudBilling\PaymentGateway;
use App\Enums\XcloudBilling\PaymentMethodStatus;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentMethod>
 */
class PaymentMethodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'payment_gateway' => PaymentGateway::Stripe,
            'status' => PaymentMethodStatus::ACTIVE,
            'session_id' => \Str::random(10),
            'team_id' => Team::factory(),
            'user_id' => User::factory(),
            'default_card' => true
        ];
    }
}
