<?php

namespace Database\Factories;

use App\Enums\DatabaseType;
use App\Enums\ServerStatus;
use App\Models\Server;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class ServerFactory extends Factory
{
    protected $model = Server::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->name(),

            'status' => ServerStatus::PROVISIONED,
            'is_connected' => true,
            'is_provisioned' => true,
            'public_ip' => '0.0.0.0',
            'private_ip' => '0.0.0.0',

            'ssh_port' => '22',
            'ssh_username' => 'root',
            'sudo_password' => $this->faker->password(),

            'database_type' => DatabaseType::MYSQL_8,
            'database_name' => 'xcloud',
            'database_password' => $this->faker->password(),

            'next_site_prefix_id' => 1,

            // 'meta' => [],
            // 'log' => '',

            // 'checked_at' => Carbon::now(),
            // 'provisioning_job_dispatched_at' => Carbon::now(),

            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'user_id' => User::factory(),

            'team_id' => Team::factory(),
        ];
    }

    public function forUser($user)
    {
        return $this->has($user)->has($user->currentTeam);
    }
}
