<?php

namespace Database\Factories;

use App\Enums\SiteStatus;
use App\Models\Server;
use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class SiteFactory extends Factory
{
    protected $model = Site::class;

    public function definition(): array
    {
        $domin = $this->faker->domainName();

        return [
            'name' => $domin,
            'title' => $domin,
            'type' => 'wordpress',
            'status' => SiteStatus::PROVISIONED,

            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            // 'database_password' => 'something',
            // 'admin_password' => $this->faker->password,
            // 'database_name' => 'xcloud',
            // 'database_user' => 'xcloud',
            'site_user' => 'xcloud',
            // 'admin_user' => 'admin',
            'php_version' => Arr::random([
                '7.4',
                '8.0',
                '8.1',
            ]),
            // 'meta' => [],

            'server_id' => function () {
                return Server::factory()->create()->id;
            },
        ];
    }
}
