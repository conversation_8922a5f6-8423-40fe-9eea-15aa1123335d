<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ServerMigration>
 */
class ServerMigrationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'public_ip' => '*******',
            'ssh_username' => 'root',
            'ssh_port' => '22',
            'ssh_authentication_mode' => 'public_key',
            'ssh_password' => null,
            'ssh_key_pair_id' => 2,
            'form' => json_encode(['next_step' => 'sites']),
            'status' => 'filling',
            'type' => null,
            'user_id' => 1,
            'team_id' => 1,
            'server_id' => 1,
            'auth_token' => null,
            'encryption_key' => null,
        ];
    }
}
