# Getting Started
- [Install Terraform](https://learn.hashicorp.com/tutorials/terraform/install-cli)
- Clone this repo & `cd` into the folder. 
- Create ssh key to use while creating the instance.
` ssh-keygen -t rsa -C "<EMAIL>" -f ./ssh-key`
- Create API token for Digital ocean and access key, secret key (Administrative access) for AWS from there console.
- Initialize the terraform by `terraform init`
- Create the resources by `terraform apply -var="do_token=dop_v1_xxxxx" -var="access_key=xxxxxx" -var="secret_key=xxxxx"` (Dynamically pass the token or keys)
- You will see the created server ip as output. 
- Can ssh into the server by `ssh terraform@<ip> -i ssh-key`
- If you want to destro the created resources run `terraform destroy -var="do_token=dop_v1_xxxxx" -var="access_key=xxxxxx" -var="secret_key=xxxxx"`

# TODO 
- [x] Dynamic SSH key
- [ ] Dynamic AMI, Image
- [ ] Dynamic Instance type/size
- [ ] AWS Dynamic AMI ID based on region
- [ ] Run this remotely from Terraform Cloud (so we don't have to install terraform in server)


