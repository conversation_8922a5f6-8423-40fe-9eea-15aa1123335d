## Fetch All Digital Ocean Droplets
#data "digitalocean_sizes" "droplet_sizes_list" {
#}

# ssh key related
resource "digitalocean_ssh_key" "ssh_credentials" {
    count = var.public_ssh_key == "" ? 0 : 1
    name       = var.key_username
    public_key = var.public_ssh_key
}

# create a volume for block storage
resource "digitalocean_volume" "manage_volume" {
    region                  = var.region
    name                    = var.name
    size                    = var.size
    initial_filesystem_type = var.do_vol_filesys_type
    description             = "Managed By XCloud"
    tags                    = var.tags
}

# attach existing volume to existing droplet
resource "digitalocean_volume_attachment" "attach_volume" {
    droplet_id = var.do_droplet_id
    volume_id  = var.do_volume_id
}

# Create Droplet
resource "digitalocean_droplet" "manage_droplet" {
    image     = var.image
    name      = var.name
    region    = var.region
    size      = var.size
    resize_disk = true
    backups   = var.backups
    volume_ids = var.do_volume_id   # attach volumes to droplet if volume already exists, it's a list
# user_data = data.template_file.user_data.rendered
    ssh_keys = [try(length(var.ssh_key_fingerprint), 0) > 0 ? var.ssh_key_fingerprint : digitalocean_ssh_key.ssh_credentials[0].fingerprint]
    tags = try(split(",", var.tags), [])
}

# create database cluster (will create a default user, password and database)
resource "digitalocean_database_cluster" "manage_db_cluster" {
    name       = var.name
    engine     = var.engine
    version    = var.db_version
    size       = var.size
    region     = var.region
    node_count = var.db_node
    tags       = try(split(",", var.tags), [])
}

# create more database
resource "digitalocean_database_db" "manage_database" {
    cluster_id = var.db_cluster_id
    name       = var.name
}

resource "digitalocean_database_user" "manage_db_user" {
    cluster_id = var.db_cluster_id
    name       = var.name
}


data "digitalocean_account" "get_account_info" {
}
