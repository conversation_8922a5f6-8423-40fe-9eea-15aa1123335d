output "get_droplet_details" {
    value = {
        id = digitalocean_droplet.manage_droplet.id
        urn = digitalocean_droplet.manage_droplet.urn
        name = digitalocean_droplet.manage_droplet.name
        region = digitalocean_droplet.manage_droplet.region
        image = digitalocean_droplet.manage_droplet.image
        ipv6 = digitalocean_droplet.manage_droplet.ipv6
        ipv6_address = digitalocean_droplet.manage_droplet.ipv6_address
        ipv4_address = digitalocean_droplet.manage_droplet.ipv4_address
        ipv4_address_private = digitalocean_droplet.manage_droplet.ipv4_address_private
        locked = digitalocean_droplet.manage_droplet.locked
        price_hourly = digitalocean_droplet.manage_droplet.price_hourly
        price_monthly = digitalocean_droplet.manage_droplet.price_monthly
        size = digitalocean_droplet.manage_droplet.size
        disk = digitalocean_droplet.manage_droplet.disk
        vcpus = digitalocean_droplet.manage_droplet.vcpus
        memory = digitalocean_droplet.manage_droplet.memory
        status = digitalocean_droplet.manage_droplet.status
        tags = digitalocean_droplet.manage_droplet.tags
        disk = digitalocean_droplet.manage_droplet.disk
        volume_ids = digitalocean_droplet.manage_droplet.volume_ids
        public_key_fingerprint = try(length(var.ssh_key_fingerprint), 0) > 0 ? var.ssh_key_fingerprint : digitalocean_ssh_key.ssh_credentials[0].fingerprint
    }
}

output "get_account_info" {
    value = {
        droplet_limit = data.digitalocean_account.get_account_info.droplet_limit
        floating_ip_limit = data.digitalocean_account.get_account_info.floating_ip_limit
        email = data.digitalocean_account.get_account_info.email
        uuid = data.digitalocean_account.get_account_info.uuid
        email_verified = data.digitalocean_account.get_account_info.email_verified
        status = data.digitalocean_account.get_account_info.status
        status_message = data.digitalocean_account.get_account_info.status_message

    }
}

output "get_volume_info" {
    value = {
        id = digitalocean_volume.manage_volume.id
        urn = digitalocean_volume.manage_volume.urn
        name = digitalocean_volume.manage_volume.name
        description = digitalocean_volume.manage_volume.description
        tags = digitalocean_volume.manage_volume.tags
        region = digitalocean_volume.manage_volume.region
        droplet_ids = digitalocean_volume.manage_volume.droplet_ids
        snapshot_id = digitalocean_volume.manage_volume.snapshot_id
        filesystem_type = digitalocean_volume.manage_volume.filesystem_type
        filesystem_label = digitalocean_volume.manage_volume.filesystem_label
        initial_filesystem_type = digitalocean_volume.manage_volume.initial_filesystem_type
        initial_filesystem_label = digitalocean_volume.manage_volume.initial_filesystem_label
    }
}

output "droplet_volume_attachment_id" {
    value = digitalocean_volume_attachment.attach_volume.id
}

output "get_db_cluster_info" {
    value = {
        id = digitalocean_database_cluster.manage_db_cluster.id
        urn = digitalocean_database_cluster.manage_db_cluster.urn
        name = digitalocean_database_cluster.manage_db_cluster.name
        engine = digitalocean_database_cluster.manage_db_cluster.engine
        version = digitalocean_database_cluster.manage_db_cluster.version
        region = digitalocean_database_cluster.manage_db_cluster.region
        size = digitalocean_database_cluster.manage_db_cluster.size
        tags = digitalocean_database_cluster.manage_db_cluster.tags
        private_network_uuid = digitalocean_database_cluster.manage_db_cluster.private_network_uuid
        maintenance_window = digitalocean_database_cluster.manage_db_cluster.maintenance_window
        host = digitalocean_database_cluster.manage_db_cluster.host
        port = digitalocean_database_cluster.manage_db_cluster.port
        private_host = digitalocean_database_cluster.manage_db_cluster.private_host
        uri = digitalocean_database_cluster.manage_db_cluster.uri
        private_uri = digitalocean_database_cluster.manage_db_cluster.private_uri
        database = digitalocean_database_cluster.manage_db_cluster.database
        user = digitalocean_database_cluster.manage_db_cluster.user
        password = digitalocean_database_cluster.manage_db_cluster.password
    }
}

output "get_db_user_info" {
    value = {
        id = digitalocean_database_user.manage_db_user.id
        name = digitalocean_database_user.manage_db_user.name
        password = digitalocean_database_user.manage_db_user.password
    }
}
